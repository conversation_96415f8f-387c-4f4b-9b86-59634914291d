<script src="/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>
<script src="../SweetAlert2/dist/sweetalert2.all.min.js"></script>

<?php  //PDO
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

 // ดึงข้อมูล
$sql_sum = "SELECT
                T1.*,
                T2.sco_cl_data AS data,
                T3.`bsdp_name` AS BSDP
            FROM
                `wm_tb_Complaints` AS T1 " .
                "LEFT JOIN wm_tb_source_complaints AS T2 ON T1.cp_cl_source = T2.sco_cl_aid " .
                "LEFT JOIN `police_name_bsdetective` AS T3 ON T1.cp_cl_investigative_officer = T3.`aid_bsdp` " .
            "WHERE
                ((`cp_cl_source`=8 OR `cp_cl_source`=9 OR `cp_cl_source`=10 OR `cp_cl_source`=11 OR `cp_cl_source`=13 OR `cp_cl_source`=14) AND `cp_cl_status`=1) AND `station`='$station'";


    $stmt_sum = $pdo->prepare($sql_sum);
    $stmt_sum->execute();
    $total = $stmt_sum->rowCount();

// เลขคดี
$sql_no = "SELECT * FROM wm_tb_Complaints WHERE station='$station' ORDER BY cp_cl_aid DESC LIMIT 1";
    $stmt_no = $pdo->prepare($sql_no);
    $stmt_no->execute();
    $row_no = $stmt_no->fetch(PDO::FETCH_ASSOC);

if ($row_no !== false) {
    $running_no = $row_no['cp_cl_case_id'];
} else {
  // Handle the case when no information is found
  $running_no = null; // or any default value you prefer
}

    
//echo '<pre>';
//print_r($res_sum);
//echo '</pre>';

?>

<link rel="stylesheet" href="../bootstrap/css/bootstrap-grid.css">
<style>
    .swal2-popup {
    width: 500px !important;
    height: auto !important;
    font-size: 14px !important;
}
@media print {
   a {
      display: none !important;
   }
}
</style>
<link rel="stylesheet" type="text/css" href="print.css" media="print">
<div class="align-self-start">
<div class=" h3 text-center  alert alert-success mb-4 mt-4 " role="alert" >บัญชีเรื่องร้องเรียน <?= $name_station ?></div>
<div align="left">
    
	<!--&nbsp;&nbsp;<a href="/Bansuan/index.php?rnd=<?= rand(); ?>&page=complaints" class="btn btn-warning btn-lg mb-4" >ย้อนกลับ</a> -->
    &nbsp;&nbsp;<a href="Add_complaints.php" class="btn btn-success btn-lg mb-4 <?= $add_btn ?>" style="flex:initial">เพิ่มข้อมูล</a>
    &nbsp;<a href="finish_complaints.php" class="btn btn-primary btn-lg mb-4" style="flex:initial">รายการที่ดำเนินการ สิ้นสุดแล้ว</a>
    &nbsp;<a href="/Activity/Show_Activity6.php" class="btn btn-primary btn-lg mb-4" style="flex:initial">ตารางสรุปยอดรายเดือน</a>
    &nbsp;&nbsp;<a style="background-color: yellow ; padding: 20px">กำลังดำเนินการ : <b style="color: crimson; font-size: 26px"><?= $total ?></b> เรื่อง</a> &nbsp;&nbsp;
    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a>รหัสรับแจ้งเหตุ (ล่าสุด) </a>&nbsp;<b style="color: crimson; font-size: 20px ; background-color: yellow ;padding: 10px"><?= $running_no ?></b>
</div>
<div class="table-responsive">
<table width="100%" height="94" border="1" cellpadding="1" cellspacing="1" class="table-bordered table-hover mb-4 mt-4">
  <tbody>
    <tr>
	  <td height="44" bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">ลำดับ</td>		
	  <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">รหัสรับแจ้งเหตุ</td>   
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">เหตุ</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">ที่มา</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">เลขหนังสือต้นเรื่อง</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">วันเวลา รับแจ้งเหตุ</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">วันที่เกิดเหตุ</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">เวลา</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">ชื่อผู้ถูกร้องเรียน</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">สถานที่เกิดเหตุ</td>      
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">ร้อยเวรสืบสวน</td>
	  <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">เจ้าหน้าที่สืบสวน</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">ไฟล์เอกสาร</td>
      <td colspan="3" bgcolor="#995D5D"></td>
      <td colspan="8" bgcolor="#995D5D"></td>
    </tr>
	  
<?php

// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no = 1;
while($row_sum = $stmt_sum->fetch(PDO::FETCH_ASSOC))	
      //เพิ่มเข้ามาใหม่ 12/09/2022 จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
{
     $pcid = $row_sum['cp_cl_case_id'];  
    
    //ฟังก์ชั่น วันที่ ดึงจาก condb
    if($row_sum["cp_cl_date_complaints"]!=''){
        $strDate = DateThai( $row_sum["cp_cl_date_complaints"] );
    }else{
        $strDate = '';
    }
    if($row_sum["cp_cl_date_incident"]!=''){
        $strDate2 = DateThai( $row_sum["cp_cl_date_incident"] );
    }else{
        $strDate2 = '';
    }
    
    
    //ตรวจสอบไฟล์ สส.1 ก่อนว่า มีไฟล์ในตารางไหม หากมี ให้ส่งเป็นลิงค์
	$pdf = $row_sum["cp_cl_complaints_file"];
	if($pdf !== '')
	{
		$links = '<a href="/' . $row_sum["cp_cl_complaints_file"] .'" target="_blank">Download</a>';
	}
	else {
		$links = 'ไม่มีไฟล์';
	}
    
    
?>	

    <tr>
      <td> <?= $no ?> </td>
      <td><?= $row_sum["cp_cl_case_id"]?></td>
      <td><?= $row_sum["cp_cl_case"]?></td>
      <td><?= $row_sum["data"]?></td>
      <td><?= $row_sum["cp_cl_number"]?></td>
      <td nowrap><?= $strDate ?> / <?= $row_sum["cp_cl_time_complaints"]?></td>
      <td nowrap><?= $strDate2 ?></td>
      <td><?= $row_sum["cp_cl_time_start_incident"]?> - <?= $row_sum["cp_cl_time_end_incident"]?></td>
      <td><?= $row_sum["cp_cl_suspect1"]?></td>
   	  <td><?= $row_sum["cp_cl_place"]?></td>
	  <td><?= $row_sum["cp_cl_investigative_sergeant"]?></td>
	  <td><?= $row_sum["BSDP"]?></td>
      <td><?= $links ?></td>

        <td><a href="show_detail_complaints2.php?id=<?= $row_sum["cp_cl_aid"] ?>&pcid=<?= $pcid ?>" class="btn btn-success mb-4" >รายละเอียด</a></td>
        <td><a href="Edit_complaints.php?id=<?= $row_sum["cp_cl_aid"] ?>" class="btn btn-warning mb-4 <?= $edit_btn ?>" >แก้ไข</a></td>
        <td><button href="#" class="btn btn-danger mb-4" onClick="deleteItem(<?= $row_sum["cp_cl_aid"] ?>)"<?=$del_btn?>>ลบ</button></td>

	  </tr>
  </tbody>
<?php
	$no++;
}//while
?>
</table>
</div>
</div>
<script>
function deleteItem(id) {
    Swal.fire({
        title: 'คุณต้องการลบข้อมูลนี้ใช่ไหม?',
        text: "หลังจากลบแล้ว จะไม่สามารถกู้คืนได้",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'ใช่, ยืนยันลบ',
        cancelButtonText: 'ยกเลิก'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = '/Bansuan/Del_complaints.php?id=' + id;
        }
    });
}
</script>
