<?php
// Database connection parameters
include '../Condb.php';
include '../users.inc.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// User's latitude and longitude
$userLat = $_POST['lat'];
$userLong = $_POST['long'];
$radius = $_POST[ 'km' ];
/*$userLat = 17.***************;
$userLong = 99.**************;*/

// Radius in kilometers
//$radius = 2;

// Query nearby places
$query = "SELECT  AD.ad_cl_aid, AD.ad_cl_idcard, AD.ad_cl_lat, AD.ad_cl_lon, AD.ad_cl_num AS num, AD.ad_cl_moo AS moo, PV.pv_name_th AS PV_name, AP.ap_name_th AS AP_name, TB.tb_name_th AS TB_name, PS.ps_cl_name AS PS_name, PS.ps_cl_surname AS surname, PS.ps_cl_image AS ps_img, PS.ps_cl_prefix AS prefix,
          (6371 * ACOS(COS(RADIANS(:lat)) * COS(RADIANS(AD.ad_cl_lat)) * COS(RADIANS(AD.ad_cl_lon) - RADIANS(:long)) + SIN(RADIANS(:lat)) * SIN(RADIANS(AD.ad_cl_lat)))) AS distance
          FROM wm_tb_address AD
          LEFT JOIN wm_tb_personal as PS ON PS.ps_cl_idcard = AD.ad_cl_idcard
          LEFT JOIN provinces as PV ON PV.pv_code = AD.ad_cl_province
          LEFT JOIN amphures as AP ON AP.ap_code = AD.ad_cl_amphur
          LEFT JOIN districts as TB ON TB.tb_id = AD.ad_cl_tumbon
          HAVING distance <= :radius
          ORDER BY distance";

$stmt = $pdo->prepare($query);
$stmt->bindParam(':lat', $userLat);
$stmt->bindParam(':long', $userLong);
$stmt->bindParam(':radius', $radius);
$stmt->execute();
$places = $stmt->fetchAll(PDO::FETCH_ASSOC);

//print_r($places);
//
//exit();
?>

<!DOCTYPE html>
<html>
<head>
    <title>Nearby Places</title>
    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCIN1hO0EaE0HOhnhxk5i0tC1COym6fWSw&callback=initMap" async defer></script>
	<!--<script src='https://maps.googleapis.com/maps/api/distancematrix/json?origins=Washington%2C%20DC&destinations=New%20York%20City%2C%20NY&units=imperial&key=YOUR_API_KEY'
	</script>-->
    <script>
        var map;
		var userLocation = {lat: <?php echo $userLat; ?>, lng: <?php echo $userLong; ?>};
		var radiusCircle;

        function initMap() {
        map = new google.maps.Map(document.getElementById('map'), {
            center: userLocation,
            zoom: 12
        });

        radiusCircle = new google.maps.Circle({
            strokeColor: '#FF0000',
            strokeOpacity: 0.8,
            strokeWeight: 2,
            fillColor: '#FEA5A5',
            fillOpacity: 0.35,
            map: map,
            center: userLocation,
            radius: <?php echo $radius * 1000; ?> // Convert radius to meters
        });

        <?php foreach ($places as $place) {
		$ps_n = $place['prefix'] . $place['PS_name'] . ' ' . $place['surname'] ;
		$adr = $place['num'] . ' หมู่ที่ ' . $place['moo'] . ' ต.' . $place['TB_name'] . ' อ.' . $place['AP_name'] . ' จ.' . $place['PV_name'];
        $content = '<div><strong>Id card :</strong> ' . $place['ad_cl_idcard'] . '</div>' .
					'<div>ชื่อ : ' . $ps_n . '</div>' .
					'<div>ที่อยู่ : ' . $adr . '</div>' .
					'<hr>' .
				    '<div align="center"><img src="' . $place['ps_img'] . '" alt="Image" style="max-width: 200px;"></div>';

			echo "var marker = new google.maps.Marker({
					position: {lat: {$place['ad_cl_lat']}, lng: {$place['ad_cl_lon']}},
					map: map,
					title: '{$place['ad_cl_idcard']}',
					label: '{$place['ad_cl_idcard']}'
				});

               var infoWindow = new google.maps.InfoWindow();

                // Create a function to associate the marker and infoWindow content correctly
                (function(marker, content) {
                    google.maps.event.addListener(marker, 'click', function() {
                        console.log('Marker clicked: ' + marker.title);
                        infoWindow.setContent(content);
                        infoWindow.open(map, marker);
                    });
                })(marker, '$content');";
            } ?>
    }
</script>
</head>
<body>
    <h1 align="center">Crime Person Near Me</h1>
	<h3 align="center">(รัศมี  <?= $_POST['km'] ?>  กิโลเมตร)</h3>
    <div id="map" style="height: 600px;"></div>
</body>
</html>