<?php
include '../Condb.php';
include '../users.inc.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

$pcid = $_GET['pcid'];

$checklist = '';
$people_name = '';
$cl_aid = 0;
if($pcid != '') {
	$query1 = "SELECT * FROM wm_tb_personal WHERE ps_cl_idcard ='$pcid' ";
	$result1 = mysqli_query($conn, $query1);
	$row1 = mysqli_fetch_array($result1);	
	
	$people_name = $row1['ps_cl_prefix'] . ' ' . $row1['ps_cl_name'] . ' ' . $row1['ps_cl_surname'];
	$people_image = $row1["ps_cl_image"];
	if($people_image != '') {
		$people_image = "<img src='{$people_image}' height='80'> ";
	}
    
    $sql_cl = "SELECT * FROM wm_tb_warrant_checklist30 WHERE cl_idcard='$pcid'";
    $result_cl = mysqli_query($conn, $sql_cl);
	$row_cl = mysqli_fetch_array($result_cl);
    
   // print_r($row_cl);
    
    if($row_cl) {
        $cl_aid = $row_cl['cl_aid'];
        $checklist = $row_cl['cl_checklist']; //explode(',', $row_ck['ck_checkbox']);
        //$checklist = explode(',', $row_cl['cl_checklist']);
    }
}
//print_r($checklist);
?>

<!DOCTYPE html>
<html>
<head>
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<base target="_top">
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="stylesheet" href="../policeinnopolis/bootstrap/css/bootstrap.rtl.min.css">
<link rel="stylesheet" href="../bootstrap-3.4.1-dist/css/bootstrap.min.css">
<script src="../bootstrap-3.4.1-dist/js/bootstrap.min.js"></script>
<style>
body {
  margin: 0;
  min-width: 250px;
}

/* Include the padding and border in an element's total width and height */
* {
  box-sizing: border-box;
}

/* Remove margins and padding from the list */
ul {
  margin: 0;
  padding: 0;
}

/* Style the list items */
ul li {
  cursor: pointer;
  position: relative;
  padding: 12px 8px 12px 40px;
  list-style-type: none;
  background: #eee;
  font-size: 18px;
  transition: 0.2s;
  
  /* make the list items unselectable */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Set all odd list items to a different color (zebra-stripes) */
ul li:nth-child(odd) {
  background: #f9f9f9;
}

/* Darker background-color on hover */
ul li:hover {
  background: #ddd;
}

/* When clicked on, add a background color and strike out text */
ul li.checked {
  background: #888;
  color: #fff;
  text-decoration: line-through;
}

/* Add a "checked" mark when clicked on */
ul li.checked::before {
  content: '';
  position: absolute;
  border-color: #fff;
  border-style: solid;
  border-width: 0 2px 2px 0;
  top: 10px;
  left: 16px;
  transform: rotate(45deg);
  height: 15px;
  width: 7px;
}

/* Style the close button */
.close {
  position: absolute;
  right: 0;
  top: 0;
  padding: 12px 16px 12px 16px;
}

.close:hover {
  background-color: #f44336;
  color: white;
}

/* Style the header */
.header {
  background-color: #f44336;
  padding: 5px 5px;
  color: white;
  text-align: center;
}

/* Clear floats after the header */
.header:after {
  content: "";
  display: table;
  clear: both;
}

/* Style the input */
input {
  margin: 0;
  border: none;
  border-radius: 0;
  width: 75%;
  padding: 10px;
  float: left;
  font-size: 16px;
   color: #000;
}

/* Style the "Add" button */
.addBtn {
  padding: 10px;
  width: 25%;
  background: #d9d9d9;
  color: #555;
  float: left;
  text-align: center;
  font-size: 16px;
  cursor: pointer;
  transition: 0.3s;
  border-radius: 0;
}

.addBtn:hover {
  background-color: #bbb;
}
</style>
<script src="https://code.jquery.com/jquery-3.6.0.js"></script>
<!-- สำหรับ วันที่ แบบ พ.ศ. -->
<link rel="stylesheet" href="http://code.jquery.com/ui/1.9.1/themes/base/jquery-ui.css" />
<script src="http://code.jquery.com/jquery-1.8.2.js"></script>
<script src="/policeinnopolis/js/jquery-ui.js"></script>
</head>
<body>
<div class="container">
<div id="myDIV" class="header">
        <div class=" h4 text-center mb-4 mt-4 " role="alert">
           <h2 style="margin:5px">Check List ตรวจสอบข้อมูลหมายจับ 32 รายการ <b style="font-size: 25px ; color: yellow"><br><br><?= $people_image ?><br><?=$people_name?> <?= $pcid ?> </b></h2><br>
        </div>
        <input type="hidden" id="myInput" placeholder="Title...">
        <!--<span onclick="newElement()" class="addBtn" >Add</span>-->
</div><br>
    &nbsp;&nbsp;&nbsp;<a href="/policeinnopolis/WatchmanData/Show_All.php?pcid=<?= $pcid ?>&page=warrant" class="btn btn-warning" style="font-size: 25px" >ย้อนกลับ</a><br><br>

<form action="save_checklist30_1.php?pcid=<?=$pcid?>" method="POST" enctype="multipart/form-data" >

        <ul id="myUL">
          <li id="ck_1" _value="1">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;1. ประวัติหมายจับ</li>
            <li id="ck_2" _value="2">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2. ทะเบียนราษฎร์</li>
            <li id="ck_3" _value="3">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;3. การตรวจสอบผลคดี</li>
            <li id="ck_4" _value="4">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;4. สถานภาพครอบครัว</li>
            <li id="ck_5" _value="5">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;5. สถานที่ทำงาน (ประกันสังคม)</li>
            <li id="ck_6" _value="6">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;6. การตรวจสอบข้อมูลทะเบียนบริษัท</li>
            <li id="ck_7" _value="7">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;7. การเลือกตั้ง</li>
            <li id="ck_8" _value="8">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;8. ข้อมูลเสียภาษีอากร</li>
            <li id="ck_9" _value="9">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;9. ประกันชีวิต</li>
            <li id="ck_10" _value="10">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;10. การรักษาพยาบาล</li>
            <li id="ck_11" _value="11">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;11. หนังสือเดินทาง</li>
            <li id="ck_12" _value="12">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;12. การครอบครองรถ</li>
            <li id="ck_13" _value="13">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;13. ประกันภัยรถ</li>
            <li id="ck_14" _value="14">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;14. ไฟแนนซ์ (ส่งใบเสร็จ)</li>
            <li id="ck_15" _value="15">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;15. ครอบครองอสังหาริมทรัพย์</li>
            <li id="ck_16" _value="16">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;16. ข้อมูลการเงิน (ธนาคาร)</li>
            <li id="ck_17" _value="17">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;17. ข้อมูลโทรศัพท์</li>
            <li id="ck_18" _value="18">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;18. ข้อมูลโทรศัพท์พื้นฐาน</li>
            <li id="ck_19" _value="19">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;19. ข้อมูลเดินทาง</li>
            <li id="ck_20" _value="20">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;20. ประวัติอาชญากรรม</li>
            <li id="ck_21" _value="21">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;21. ข้อมูลไฟฟ้า,ประปา</li>
            <li id="ck_22" _value="22">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;22. กล้องยาเสพติด</li>
            <li id="ck_23" _value="23">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;23. ข้อมูล Social network</li>
            <li id="ck_24" _value="24">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;24. ใบขับขี่ ใบอนุญาตขับรถ</li>
            <li id="ck_25" _value="25">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;25. ข้อมูลใบสั่ง</li>
            <li id="ck_26" _value="26">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;26. ข้อมูลรถอุบัติเหตุ</li>
            <li id="ck_27" _value="27">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;27. อาวุธปืน</li>
            <li id="ck_28" _value="28">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;28. บัตรเครดิต</li>
            <li id="ck_29" _value="29">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;29. ประวัติการซ่อมรถ</li>
            <li id="ck_30" _value="30">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;30. การตรวจสอบบริการ เช่น พิซซ่า</li>
            <li id="ck_31" _value="31">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;31. ข้อมูลการรับวัคซีน</li>
            <li id="ck_32" _value="32">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;32. ข้อมูลการรับส่งพัสดุ</li>
        </ul>
<p>
          <input type="hidden" id="cl_checklist" name="cl_checklist" value="" />
        </p>
  </table>
<table width="86%" border="0" cellspacing="1" cellpadding="1">
        <label hidden ="hidden">ลำดับ</label>
        <input type = "text" name = "cl_aid" class="form-control"  hidden ="hidden" value="<?= $cl_aid ?>" >
            
        <label  hidden ="hidden">เลขบัตร</label>
<!--        <input type = "hidden" name = "cl_idcard" class="form-control" value= "<?=$row1['ps_cl_idcard']?>"  hidden ="hidden">-->
        <input type="hidden" name="cl_idcard" value="<?= $pcid ?>" />  
        <br>
        <tbody>
        <tr>
          <td width="62%" align="center">&nbsp;<label style="font-size: 18px">เจ้าหน้าที่สืบสวน/บันทึก <span style="color: #F90004">* จำเป็น</span></label>
            <input type = "text" name = "cl_detectives" class="form-control" placeholder="เจ้าหน้าที่สืบสวน/บันทึก"  >
              
            </td>&nbsp;&nbsp;&nbsp;&nbsp;
              <td width="38%" align="center">&nbsp;&nbsp;&nbsp;&nbsp;<label style="font-size: 18px">วันที่ตรวจสอบ <span style="color: #F90004">* จำเป็น</span></label>
          <script>
                  $( function() {
                        $.datepicker.setDefaults( $.datepicker.regional[ "th" ] );

                          var currentTime = new Date();
                          var year = currentTime.getFullYear();

                          // date
                          $("#datepicker").datepicker({
                            changeMonth: true,
                            changeYear: true,
                            yearSuffix: year+543,
                            yearRange: '-100:+0',
                            dateFormat: 'yy-mm-dd'
                          });
                       } );
                  </script>
          &nbsp;&nbsp;&nbsp;&nbsp;<input type="text" name="cl_date" id="datepicker" autocomplete="off" class="form-control" border="1"></td>
            <script>
            function set_cal(ele)//function สร้างตัวเลือกปฎิทิน
            {
              $( ele ).datepicker({
                  onSelect:(date_text)=>
                  {
                    let arr=date_text.split("/");
                    let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])+543).toString();
                    $(ele).val(new_date);
                    $(ele).css("color","");
                  },
                  beforeShow:()=>{

                    if($(ele).val()!="")
                    {
                      let arr=$(ele).val().split("/");
                      let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])-543).toString();
                      $(ele).val(new_date);

                    }

                    $(ele).css("color","white");
                  },
                  onClose:()=>{

                      $(ele).css("color","");

                      if($(ele).val()!="")
                      {
                          let arr=$(ele).val().split("-");
                          if(parseInt(arr[2])<2500)
                          {
                              let new_date=arr[0]+"-"+arr[1]+"-"+(parseInt(arr[2])).toString();
                              $(ele).val(new_date);
                          }
                      }
                  },
                  dateFormat:"yy-mm-dd", //กำหนดรูปแบบวันที่เป็น วัน/เดือน/ปี
                  changeMonth:true,//กำหนดให้เลือกเดือนได้
                  changeYear:true,//กำหนดให้เลือกปีได้
                  showOtherMonths:true,//กำหนดให้แสดงวันของเดือนก่อนหน้าได้
              });
            }
          $(document).ready(function(){
            //เรียก function set_cal เมื่อเปิดหน้าเว็บ โดยส่ง object element ที่มี id เป็น datepicker เป็นพารามิเตอร์
            set_cal( $("#datepicker") );
          })
          </script>
        <!-- สำหรับ วันที่ แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ -->
            <br>
        </tr>
        <tr>
              <td align="center">&nbsp;</td>
        </tr>
        <tr>
              <td align="center">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<button onClick="doSave()" style="font-size: 18px" class="btn btn-success">Update</button> &nbsp;&nbsp;<a href="/policeinnopolis/WatchmanData/Show_All.php?pcid=<?= $pcid ?>&page=warrant" class="btn btn-warning"  style="font-size: 20px; color: red" >ยกเลิก</a>&nbsp;</td>  
       </tr>
          </tbody>
    </table>
    <br>
<p>&nbsp;</p>
<p>
  <script>
// Create a "close" button and append it to each list item
var myNodelist = document.getElementsByTagName("LI");
var i;
for (i = 0; i < myNodelist.length; i++) {
  var span = document.createElement("SPAN");
  var txt = document.createTextNode("\u00D7");
  span.className = "close";
  span.appendChild(txt);
  myNodelist[i].appendChild(span);
}

// Click on a close button to hide the current list item
//var close = document.getElementsByClassName("close");
//var i;
//for (i = 0; i < close.length; i++) {
//  close[i].onclick = function() {
//    var div = this.parentElement;
//    div.style.display = "none";
//  }
//}

// Add a "checked" symbol when clicking on a list item
var list = document.querySelector('ul');
list.addEventListener('click', function(ev) {
  if (ev.target.tagName === 'LI') {
    ev.target.classList.toggle('checked');
  }
}, false);

// Create a new list item when clicking on the "Add" button
function newElement() {
  var li = document.createElement("li");
  var inputValue = document.getElementById("myInput").value;
  var t = document.createTextNode(inputValue);
  li.appendChild(t);
  if (inputValue === '') {
    alert("You must write something!");
  } else {
    document.getElementById("myUL").appendChild(li);
  }
  document.getElementById("myInput").value = "";

  var span = document.createElement("SPAN");
  var txt = document.createTextNode("\u00D7");
  span.className = "close";
  span.appendChild(txt);
  li.appendChild(span);

  for (i = 0; i < close.length; i++) {
    close[i].onclick = function() {
      var div = this.parentElement;
      div.style.display = "none";
    }
  }
}
    
function doSave()
{
    var checks = $('#myUL').find("li.checked");
    console.log(checks);
    var value = "";
    for(var i=0; i<checks.length; i++) {
        console.log(checks[i], $(checks[i]).attr("_value"));
        
        value += ($(checks[i]).attr("_value") || 0) + ",";
    }
    $('#cl_checklist').val(value);
    //
    document.forms[0].submit();
}
    
function auto_checklist()
{
     var checklist = "<?= $checklist ?>";
     checklist = checklist.split(",");
     for(var i=0; i<checklist.length; i++) {
        var value = checklist[i];
        
       $('#ck_' +  value).addClass("checked"); // <<
    }
}
       
auto_checklist(); // <<<
  </script>
<!--  <script src="/js/datetimepicker2.5.1/jquery.ui.datepicker-th.js" type="text/javascript"></script>-->
</p>
</body>
</html>
