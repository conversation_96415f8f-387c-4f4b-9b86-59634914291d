<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';
include '../Head_image.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

//get month from select or current month
$month = isset($_GET['month']) ? $_GET['month'] : '10';
//$year = isset($_GET['year']) ? $_GET['year'] : (date("m") < 10 ? date("Y")-1 : date("Y"));
//$station = isset($_GET['station']) ? $_GET['station'] : 6707;
//$station = 6707;   // สภ.บ้านสวน

// Get the year from the query string
$year = isset($_GET['year']) ? $_GET['year'] : date('Y');
// Calculate the start and end dates
$start_date = ($year - 1) . '-10-01';
$end_date = $year . '-09-30';

$limite_date = strtotime("$year-09-30");
$check_date = strtotime("$year-10-01");
$select_date = strtotime("$year-$month-01");

$m1 = '';
$m2 = '';
$m3 = '';
$m4 = '';
$m5 = '';
$m6 = '';
$m7 = '';
$m8 = '';
$m9 = '';
$m10 = '';
$m11 = '';
$m12 = '';
switch($month)
{
    case "01": $m1 = "selected"; break;
    case "02": $m2 = "selected"; break;
    case "03": $m3 = "selected"; break;
    case "04": $m4 = "selected"; break;
    case "05": $m5 = "selected"; break;
    case "06": $m6 = "selected"; break;
    case "07": $m7 = "selected"; break;
    case "08": $m8 = "selected"; break;
    case "09": $m9 = "selected"; break;
    case "10": $m10 = "selected"; break;
    case "11": $m11 = "selected"; break;
    case "12": $m12 = "selected"; break;
}
$current_month = $global_thai_month[0+$month];
$y2 = $year+543;

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<base target="_top">
<title>แบบ สส.15</title>
<link rel="stylesheet" href="/bootstrap/css/bootstrap-grid.css">
<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
</head>
    
<body>
<!--<p><img src="../Image/WatchmanDB3.jpg" width="100%" height="" alt=""/></p>-->
<div class=" h3 text-center  alert alert-success mb-4 mt-4 " role="alert" >แบบ สส.15 ข้อมูลการบำบัดยาเสพติด ประจำปีงบประมาณ พ.ศ.&nbsp;<?= $y2 ?> &nbsp; <?= $name_station ?> </div>

<div>
    <table width="90%" border="0" cellspacing="1" cellpadding="1">
      <tbody>
        <tr>
          <td width="14%"><div align="left">
            &nbsp;&nbsp;<a href="/WM/index.php?rnd=<?= rand(); ?>&page=report" class="btn btn-warning btn-lg mb-4" >ย้อนกลับ</a></td>
          <td width="16%"><label>เลือกปีงบประมาณ </label>
            <select id="changeyear" name="changeyear" class="form-select col-8 d-lg-inline" style="background-color: #00D2FE; font-size: 16px" onChange="dates_change()" >
              <option value="" >เลือก</option>
              <?php
                    $cur_y = date("Y") + 2;
                    for($y=2020; $y<$cur_y; $y++) {
                        $sel = ($y == $year) ? "selected" : '';
                        $start_month = ($y == date("Y")) ? "10" : "01"; // October for current year, January otherwise
                        echo "<option value='$y' $sel> ". ($y + 543) ." </option>";
                    }
                ?>
            </select></td>
          <td width="39%">&nbsp;</td>
        </tr>
      </tbody>
    </table>
</div>
<br>
    
<div class="container" align="left" style="margin-left:auto">
<table width="99%" border="1" cellspacing="1" cellpadding="1" class="table-striped table-hover table-bordered">
  <tbody>
<?php
      
$sql = "SELECT
            CONCAT(YEAR(`pf_cl_dates`)+543) AS `year`,
            MONTH(`pf_cl_dates`) AS month,
            SUM(`pf_cl_drug`IN('6','7')) AS count,
            SUM(`pf_cl_drug`='6') AS take,
            SUM(`pf_cl_drug`='7') AS take2
        FROM
            `wm_tb_performance`
        WHERE 
            station='$station' AND `pf_cl_type_sub`='พ.ร.บ.ยาเสพติด' AND pf_cl_dates >= :start_date AND pf_cl_dates <= :end_date
        GROUP BY 
            MONTH(`pf_cl_dates`)
        ORDER BY
            pf_cl_dates ASC ";
      
$params = array(
    'start_date' => $start_date,
    'end_date' => $end_date
);

// Execute the query
try {
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    echo "Query failed: " . $e->getMessage();
    exit();
}

// Check if there are any rows returned by the query
if (count($result) == 0) {
    echo "No data for the selected time period.";
} else {
    // Create an array of all the months between the start date and end date
    $months = array();
    $start = new DateTime($start_date);
    $end = new DateTime($end_date);
    $interval = DateInterval::createFromDateString('1 month');
    $period = new DatePeriod($start, $interval, $end);

    foreach ($period as $dt) {
        $months[] = array(
            'year' => $dt->format('Y') + 543,
            'month' => $dt->format('m'),
            'count' => '0',
            'f' => '0'
        );
    }

    // Left join the result set with the months array
    $data = array_replace($months, $result);
}

?>

    <tr>
      <td rowspan="2" style="font-size: 22px; color: blue; background: #42FF00">เดือน ปี</td>
      <td colspan="2" style="font-size: 22px; color: blue; text-align: center; background: #42FF00">รูปแบบการบำบัดยาเสพติด</td>
      <td rowspan="2" style="font-size: 22px; color: blue; text-align: center; background: #42FF00">รวม</td>
      <td rowspan="2" style="font-size: 22px; color: blue; background: #42FF00">หมายเหตุ</td>
    </tr>
    <tr>
      <td style="font-size: 22px; color: blue; text-align: center; background: #42FF00">สมัครใจบำบัด</td>
      <td style="font-size: 22px; color: blue; text-align: center; background: #42FF00">บังคับบำบัด</td>
      </tr>
      
<?php foreach ($result as $row): ?>
      
<?php

switch($row['month'])
{
    case "01": $row['month'] = "มกราคม"; break;
    case "02": $row['month'] = "กุมภาพันธ์"; break;
    case "03": $row['month'] = "มีนาคม"; break;
    case "04": $row['month'] = "เมษายน"; break;
    case "05": $row['month'] = "พฤษภาคม"; break;
    case "06": $row['month'] = "มิถุนายน"; break;
    case "07": $row['month'] = "กรกฎาคม"; break;
    case "08": $row['month'] = "สิงหาคม"; break;
    case "09": $row['month'] = "กันยายน"; break;
    case "10": $row['month'] = "ตุลาคม"; break;
    case "11": $row['month'] = "พฤศจิกายน"; break;
    case "12": $row['month'] = "ธันวาคม"; break;
}
      
if(!empty($row['take'])) {
    $take = $row['take'];
}else{
    $take = '';
}

if(!empty($row['take2'])) {
    $take2 = $row['take2'];
}else{
    $take2 = '';
}
      
if(!empty($row['count'])) {
    $count = $row['count'];
}else{
    $count = '';
}
      
?>
      
    <tr style="font-size: 18px">
      <td style="text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<?php echo $row['month']; ?>&nbsp;&nbsp;<?php echo $row['year']; ?>&nbsp;</td>
      <td style="text-align: center">&nbsp;<?php echo (($take > 0) ? $take  : "") ?>&nbsp;</td>
      <td style="text-align: center">&nbsp;<?php echo (($take2 > 0) ? $take2  : "") ?>&nbsp;</td>
      <td style="text-align: center">&nbsp;<?php echo (($count > 0) ? $count  : "") ?>&nbsp;</td>
      <td>&nbsp;&nbsp;</td>
    </tr>
  </tbody>
<?php endforeach; ?>
</table>
</div>
    <br>

&nbsp;&nbsp;<button class="btn btn-primary btn-lg mb-4" onClick="do_print()"> พิมพ์รายงาน </button> 
	<!--<button class="btn btn-primary btn-lg mb-4" onClick="if ($station === 6707) { do_print(); }">พิมพ์รายงาน</button>--> 
</body>
<script>
function dates_change()  // คำสั่งเปลี่ยนเดือน ปี
{
   // var ms = $("#changemonth option:selected").val();
    var ys = $("#changeyear option:selected").val();
    window.location = "/Activity/Show_Activity1_SS15.php?&year=" + ys + "&rnd=" + Math.random();
}    
</script>
</html>
<!-- script สำหรับส่งไปพิมพ์รายงาน  -->
<script>
    
    function do_print()
    {
        var ys = $("#changeyear option:selected").val();
        var newW = window.open("/Activity/Show_Activity1_SS15_print.php?&year=" + ys + "&rnd=" + Math.random(), "_print", "menubar=no,titlebar=no,toolbar=no,status=no");
        newW.print();
        //window.print();
    }
    
</script>