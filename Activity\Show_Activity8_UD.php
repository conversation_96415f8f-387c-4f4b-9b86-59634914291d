<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';
include '../Head_image.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);


$id = isset($_GET['hv_cl_aid']) ? $_GET['hv_cl_aid'] : '';

//get month from select or current month
$month = isset($_GET['month']) ? $_GET['month'] : date("m");
$year = isset($_GET['year']) ? $_GET['year'] : date("Y");

$m1 = '';
$m2 = '';
$m3 = '';
$m4 = '';
$m5 = '';
$m6 = '';
$m7 = '';
$m8 = '';
$m9 = '';
$m10 = '';
$m11 = '';
$m12 = '';
switch($month)
{
    case "01": $m1 = "selected"; break;
    case "02": $m2 = "selected"; break;
    case "03": $m3 = "selected"; break;
    case "04": $m4 = "selected"; break;
    case "05": $m5 = "selected"; break;
    case "06": $m6 = "selected"; break;
    case "07": $m7 = "selected"; break;
    case "08": $m8 = "selected"; break;
    case "09": $m9 = "selected"; break;
    case "10": $m10 = "selected"; break;
    case "11": $m11 = "selected"; break;
    case "12": $m12 = "selected"; break;
}
$current_month = $global_thai_month[0+$month];
$y2 = $year+543;

?>

<meta charset="utf-8">
<title>รายงานบุคคลพ้นโทษ/พักโทษ <?php echo $name_provincial ?></title>
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
        <base target="_top">
		<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
		<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
  		<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
 		<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js"></script>

<style type="text/css">
body{
    font-size:12px; 
}
.textAlignVer{
    display:block;
    filter: flipv fliph;
    -webkit-transform: rotate(-90deg); 
    -moz-transform: rotate(-90deg); 
    transform: rotate(-90deg); 
    position:relative;
    width:20px;
    white-space:nowrap;
    font-size:12px;
    margin-bottom:10px;
}
	td {
		height: 50px;
	}
</style>
<!--<p><img src="../Image/Head2.jpg" width="100%" height="" alt=""/></p>-->
<div class="container-fluid" align="left">
<div class=" h3 text-center  alert alert-success mb-4 mt-4 " role="alert" >กิจกรรม 8 การตรวจเยี่ยมบุคคลพ้นโทษ/พักโทษ <?php echo $name_provincial ?></div>

<div align="left">
<div>
    <table width="80%" border="0" cellspacing="1" cellpadding="1">
      <tbody>
        <tr>
          <td width="12%">&nbsp;<a href="/WM/Show_UD_report.php?rnd=<?= rand(); ?>" class="btn btn-warning btn-lg mb-4" >ย้อนกลับ</a></td>
          <!--<td width="11%">&nbsp;<a href="Add_Activity1.php" class="btn btn-success btn-lg mb-4" >เพิ่มข้อมูล</a></td>-->
          <td width="16%"><label>เลือกปี </label>
            <select id="mi_cl_year" name="mi_cl_year" class="form-select col-8 d-lg-inline" style="background-color: khaki; font-size: 16px" onChange="dates_change()">
              <option value="">เลือก</option>
              <?php
                $cur_y = date("Y") + 2;
                for($y=2020; $y<$cur_y; $y++) {
                    $sel = ($y == $year) ? "selected" : '';
                    echo "<option value='$y' $sel> ". ($y + 543) ." </option>";
                }
            ?>
          </select></td>
            <td width="2%">&nbsp;</td>
          <td width="25%"><label>เดือน</label>
            <select id="mi_cl_month" name="mi_cl_month" class="form-select col-3 d-lg-inline" style="background-color: khaki; font-size: 16px" onChange="dates_change()">
              <option value="">เลือก</option>
              <option <?= $m1 ?> value="01">มกราคม</option>
              <option <?= $m2 ?> value="02">กุมภาพันธ์</option>
              <option <?= $m3 ?> value="03">มีนาคม</option>
              <option <?= $m4 ?> value="04">เมษายน</option>
              <option <?= $m5 ?> value="05">พฤษภาคม</option>
              <option <?= $m6 ?> value="06">มิถุนายน</option>
              <option <?= $m7 ?> value="07">กรกฎาคม</option>
              <option <?= $m8 ?> value="08">สิงหาคม</option>
              <option <?= $m9 ?> value="09">กันยายน</option>
              <option <?= $m10 ?> value="10">ตุลาคม</option>
              <option <?= $m11 ?> value="11">พฤศจิกายน</option>
              <option <?= $m12 ?> value="12">ธันวาคม</option>
          </select></td>
          <td width="40%">&nbsp;</td>
          <td width="5%">&nbsp;</td>
        </tr>
      </tbody>
    </table>
</div>

<h3 align="center">ข้อมูลการติดตามพฤติการณ์และความเคลื่อนไหวของบุคคลพ้นโทษ พักโทษ  <?php echo $name_provincial ?></h3>
<h3 align="center">ประจำเดือน&nbsp; <?= $current_month ?> &nbsp;<?= $y2 ?> &nbsp;</h3>
<div class="table-responsive">
<table height="221" border="1" cellpadding="1" cellspacing="1" class="table-bordered table-hover mb-4 mt-4">
  <tbody>
    <tr>
      <td rowspan="3" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">&nbsp;สถานี&nbsp;</td>
      <td rowspan="3" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">จำนวนครั้ง<br>(รวมทั้งหมด)</td>
      <td height="46" colspan="5" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">เยี่ยมพบ</td>
      <td colspan="9" bgcolor="#FD5F2F" style="color: #00000; text-align: center; border-color: navy">เยี่ยมไม่พบ</td>
      <td rowspan="3" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">มีผลการปฏิบัติ</td>
      <td rowspan="3" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">ไม่มีผลการปฏิบัติ</td>
      <!--<td colspan="3" rowspan="3" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy"></td>-->
    </tr>
    <tr>
      <td rowspan="2" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">&nbsp;จำนวน (คน)&nbsp;</td>
      <td rowspan="2" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">&nbsp;ผลใน Police4.0&nbsp;</td>
      <td rowspan="2" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">ร้อยละความสำเร็จ</td>
      <td rowspan="2" bgcolor="#48CF1A" style="color: #00000; text-align: center; border-color: navy">เก็บ DNA</td>
      <td rowspan="2" bgcolor="#48CF1A" style="color: #00000; text-align: center; border-color: navy">ยังไม่เก็บ DNA</td>
      <td height="58" colspan="3" bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">ทราบที่อยู่</td>
      <td colspan="3" bgcolor="#FDBD62" style="color: #00000; text-align: center; border-color: navy">ไม่ทราบที่อยู่</td>
      <td colspan="3" bgcolor="#FA9779" style="color: #00000; text-align: center; border-color: navy">กระทำผิดอีก/อยู่เรือนจำ</td>
      </tr>
    <tr>
	  <td height="58" bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">&nbsp;จำนวน&nbsp;</td>
	  <td bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">&nbsp;แจ้งท้องที่ใหม่&nbsp;</td>
	  <td bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">&nbsp;ยังไม่แจ้ง&nbsp;</td>
	  <td bgcolor="#FDBD62" style="color: #00000; text-align: center; border-color: navy">&nbsp;จำนวน&nbsp;</td>
	  <td bgcolor="#FDBD62" style="color: #00000; text-align: center; border-color: navy">&nbsp;แจ้ง รจ.และทว.&nbsp;</td>
	  <td bgcolor="#FDBD62" style="color: #00000; text-align: center; border-color: navy">&nbsp;ยังไม่แจ้ง&nbsp;</td>
	  <td bgcolor="#FA9779" style="color: #00000; text-align: center; border-color: navy">&nbsp;จำนวน&nbsp;</td>
	  <td bgcolor="#FA9779" style="color: #00000; text-align: center; border-color: navy">&nbsp;แจ้ง ทว.&nbsp;</td>
	  <td bgcolor="#FA9779" style="color: #00000; text-align: center; border-color: navy">&nbsp;ยังไม่แจ้ง&nbsp;</td>
      </tr>
	  
<?php     
                      
$sql = "SELECT " .
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T1_1, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T1, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T2, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T3, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T4, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T5, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T6, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T7, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T8, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T9, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T10, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T11, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T12, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T13, " .
	
	// ด่านแม่คำมัน
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6801) AS T1_1_dan, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6801) AS T1_dan, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6801) AS T2_dan, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6801) AS T3_dan, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6801) AS T4_dan, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6801) AS T5_dan, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6801) AS T6_dan, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6801) AS T7_dan, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6801) AS T8_dan, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6801) AS T9_dan, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6801) AS T10_dan, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6801) AS T11_dan, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6801) AS T12_dan, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6801) AS T13_dan, " .
	// ตรอน
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6802) AS T1_1_tro, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6802) AS T1_tro, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6802) AS T2_tro, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6802) AS T3_tro, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6802) AS T4_tro, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6802) AS T5_tro, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6802) AS T6_tro, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6802) AS T7_tro, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6802) AS T8_tro, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6802) AS T9_tro, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6802) AS T10_tro, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6802) AS T11_tro, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6802) AS T12_tro, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6802) AS T13_tro, " .
	// ทองแสนขัน
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6803) AS T1_1_tho, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6803) AS T1_tho, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6803) AS T2_tho, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6803) AS T3_tho, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6803) AS T4_tho, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6803) AS T5_tho, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6803) AS T6_tho, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6803) AS T7_tho, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6803) AS T8_tho, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6803) AS T9_tho, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6803) AS T10_tho, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6803) AS T11_tho, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6803) AS T12_tho, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6803) AS T13_tho, " .
	// ท่าปลา
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6804) AS T1_1_thp, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6804) AS T1_thp, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6804) AS T2_thp, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6804) AS T3_thp, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6804) AS T4_thp, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6804) AS T5_thp, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6804) AS T6_thp, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6804) AS T7_thp, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6804) AS T8_thp, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6804) AS T9_thp, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6804) AS T10_thp, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6804) AS T11_thp, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6804) AS T12_thp, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6804) AS T13_thp, " .
	// น้ำปาด
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6805) AS T1_1_npa, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6805) AS T1_npa, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6805) AS T2_npa, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6805) AS T3_npa, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6805) AS T4_npa, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6805) AS T5_npa, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6805) AS T6_npa, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6805) AS T7_npa, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6805) AS T8_npa, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6805) AS T9_npa, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6805) AS T10_npa, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6805) AS T11_npa, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6805) AS T12_npa, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6805) AS T13_npa, " .
	
	// บ้านโคก
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6806) AS T1_1_bko, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6806) AS T1_bko, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6806) AS T2_bko, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6806) AS T3_bko, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6806) AS T4_bko, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6806) AS T5_bko, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6806) AS T6_bko, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6806) AS T7_bko, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6806) AS T8_bko, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6806) AS T9_bko, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6806) AS T10_bko, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6806) AS T11_bko, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6806) AS T12_bko, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6806) AS T13_bko, " .
	// พิชัย
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6807) AS T1_1_pic, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6807) AS T1_pic, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6807) AS T2_pic, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6807) AS T3_pic, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6807) AS T4_pic, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6807) AS T5_pic, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6807) AS T6_pic, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6807) AS T7_pic, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6807) AS T8_pic, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6807) AS T9_pic, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6807) AS T10_pic, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6807) AS T11_pic, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6807) AS T12_pic, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6807) AS T13_pic, " .
	// ฟากท่า
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6808) AS T1_1_fag, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6808) AS T1_fag, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6808) AS T2_fag, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6808) AS T3_fag, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6808) AS T4_fag, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6808) AS T5_fag, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6808) AS T6_fag, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6808) AS T7_fag, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6808) AS T8_fag, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6808) AS T9_fag, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6808) AS T10_fag, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6808) AS T11_fag, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6808) AS T12_fag, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6808) AS T13_fag, " .
	// เมืองอุตรดิตถ์
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6809) AS T1_1_mud, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6809) AS T1_mud, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6809) AS T2_mud, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6809) AS T3_mud, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6809) AS T4_mud, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6809) AS T5_mud, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6809) AS T6_mud, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6809) AS T7_mud, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6809) AS T8_mud, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6809) AS T9_mud, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6809) AS T10_mud, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6809) AS T11_mud, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6809) AS T12_mud, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6809) AS T13_mud, " .
	// ลับแล
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6810) AS T1_1_lap, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6810) AS T1_lap, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6810) AS T2_lap, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6810) AS T3_lap, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6810) AS T4_lap, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6810) AS T5_lap, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6810) AS T6_lap, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6810) AS T7_lap, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6810) AS T8_lap, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6810) AS T9_lap, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6810) AS T10_lap, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6810) AS T11_lap, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6810) AS T12_lap, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6810) AS T13_lap, " .
	// วังกะพี้
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6811) AS T1_1_wkp, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6811) AS T1_wkp, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6811) AS T2_wkp, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6811) AS T3_wkp, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6811) AS T4_wkp, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6811) AS T5_wkp, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6811) AS T6_wkp, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6811) AS T7_wkp, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6811) AS T8_wkp, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6811) AS T9_wkp, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6811) AS T10_wkp, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6811) AS T11_wkp, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6811) AS T12_wkp, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6811) AS T13_wkp, " .
	// เด่นเหล็ก
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6812) AS T1_1_den, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6812) AS T1_den, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6812) AS T2_den, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6812) AS T3_den, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6812) AS T4_den, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6812) AS T5_den, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6812) AS T6_den, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6812) AS T7_den, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6812) AS T8_den, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6812) AS T9_den, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6812) AS T10_den, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6812) AS T11_den, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6812) AS T12_den, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6812) AS T13_den, " .
	// นาอิน
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6813) AS T1_1_nin, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6813) AS T1_nin, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6813) AS T2_nin, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6813) AS T3_nin, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6813) AS T4_nin, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6813) AS T5_nin, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6813) AS T6_nin, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6813) AS T7_nin, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6813) AS T8_nin, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6813) AS T9_nin, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6813) AS T10_nin, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6813) AS T11_nin, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6813) AS T12_nin, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6813) AS T13_nin, " .
	// น้ำหมัน
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6814) AS T1_1_nmu, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6814) AS T1_nmu, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6814) AS T2_nmu, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6814) AS T3_nmu, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6814) AS T4_nmu, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6814) AS T5_nmu, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6814) AS T6_nmu, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6814) AS T7_nmu, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6814) AS T8_nmu, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6814) AS T9_nmu, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6814) AS T10_nmu, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6814) AS T11_nmu, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6814) AS T12_nmu, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6814) AS T13_nmu, " .
	// พญาแมน
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6815) AS T1_1_pym, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6815) AS T1_pym, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6815) AS T2_pym, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6815) AS T3_pym, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6815) AS T4_pym, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6815) AS T5_pym, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6815) AS T6_pym, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6815) AS T7_pym, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6815) AS T8_pym, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6815) AS T9_pym, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6815) AS T10_pym, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6815) AS T11_pym, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6815) AS T12_pym, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6815) AS T13_pym " .
	
        " ";

$stmt = $pdo->prepare($sql);
$stmt->bindParam(':month', $month);
$stmt->bindParam(':year', $year);
$stmt->bindParam(':provincial', $provincial); // Add station parameter
try {
    $stmt->execute();
} catch (PDOException $e) {
    echo '$sql Failed: '. $e->getMessage();
}

          
// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no = 1;
while($row = $stmt->fetch(PDO::FETCH_ASSOC))	//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
{
//ฟังก์ชั่น วันที่ ดึงจาก condb
//$strDate = DateThai( $row_mi["date_record"] );
    
if($row["T1"] > 0) {   
    $persen = ($row["T2"]*100)/$row["T1"];
}
else {
    $persen=0;
    }
    $sum1 = $row["T1"] + $row["T5"] + $row["T8"] + $row["T11"];
    $sum2 = $row["T1_1"] - $sum1;

// ด่านแม่คำมัน
if($row["T1_dan"] > 0) {   
    $persen_dan = ($row["T2_dan"]*100)/$row["T1_dan"];
}
else {
    $persen_dan=0;
    }
    $sum1_dan = $row["T1_dan"] + $row["T5_dan"] + $row["T8_dan"] + $row["T11_dan"];
    $sum2_dan = $row["T1_1_dan"] - $sum1_dan;
	
// ตรอน
if($row["T1_tro"] > 0) {   
    $persen_tro = ($row["T2_tro"]*100)/$row["T1_tro"];
}
else {
    $persen_tro=0;
    }
    $sum1_tro = $row["T1_tro"] + $row["T5_tro"] + $row["T8_tro"] + $row["T11_tro"];
    $sum2_tro = $row["T1_1_tro"] - $sum1_tro;
	
// ทองแสนขัน
if($row["T1_tho"] > 0) {   
    $persen_tho = ($row["T2_tho"]*100)/$row["T1_tho"];
}
else {
    $persen_tho=0;
    }
    $sum1_tho = $row["T1_tho"] + $row["T5_tho"] + $row["T8_tho"] + $row["T11_tho"];
    $sum2_tho = $row["T1_1_tho"] - $sum1_tho;
	
// ท่าปลา
if($row["T1_thp"] > 0) {   
    $persen_thp = ($row["T2_thp"]*100)/$row["T1_thp"];
}
else {
    $persen_thp=0;
    }
    $sum1_thp = $row["T1_thp"] + $row["T5_thp"] + $row["T8_thp"] + $row["T11_thp"];
    $sum2_thp = $row["T1_1_thp"] - $sum1_thp;
	
// น้ำปาด
if($row["T1_npa"] > 0) {   
    $persen_npa = ($row["T2_npa"]*100)/$row["T1_npa"];
}
else {
    $persen_npa=0;
    }
    $sum1_npa = $row["T1_npa"] + $row["T5_npa"] + $row["T8_npa"] + $row["T11_npa"];
    $sum2_npa = $row["T1_1_npa"] - $sum1_npa;
	
// บ้านโคก
if($row["T1_bko"] > 0) {   
    $persen_bko = ($row["T2_bko"]*100)/$row["T1_bko"];
}
else {
    $persen_bko=0;
    }
    $sum1_bko = $row["T1_bko"] + $row["T5_bko"] + $row["T8_bko"] + $row["T11_bko"];
    $sum2_bko = $row["T1_1_bko"] - $sum1_bko;
	
// พิชัย
if($row["T1_pic"] > 0) {   
    $persen_pic = ($row["T2_pic"]*100)/$row["T1_pic"];
}
else {
    $persen_pic=0;
    }
    $sum1_pic = $row["T1_pic"] + $row["T5_pic"] + $row["T8_pic"] + $row["T11_pic"];
    $sum2_pic = $row["T1_1_pic"] - $sum1_pic;
	
// ฟากท่า
if($row["T1_fag"] > 0) {   
    $persen_fag = ($row["T2_fag"]*100)/$row["T1_fag"];
}
else {
    $persen_fag=0;
    }
    $sum1_fag = $row["T1_fag"] + $row["T5_fag"] + $row["T8_fag"] + $row["T11_fag"];
    $sum2_fag = $row["T1_1_fag"] - $sum1_fag;
	
// เมืองอุตรดิตถ์
if($row["T1_mud"] > 0) {   
    $persen_mud = ($row["T2_mud"]*100)/$row["T1_mud"];
}
else {
    $persen_mud=0;
    }
    $sum1_mud = $row["T1_mud"] + $row["T5_mud"] + $row["T8_mud"] + $row["T11_mud"];
    $sum2_mud = $row["T1_1_mud"] - $sum1_mud;
	
// ลับแล
if($row["T1_lap"] > 0) {   
    $persen_lap = ($row["T2_lap"]*100)/$row["T1_lap"];
}
else {
    $persen_lap=0;
    }
    $sum1_lap = $row["T1_lap"] + $row["T5_lap"] + $row["T8_lap"] + $row["T11_lap"];
    $sum2_lap = $row["T1_1_lap"] - $sum1_lap;
	
// วังกะพี้
if($row["T1_wkp"] > 0) {   
    $persen_wkp = ($row["T2_wkp"]*100)/$row["T1_wkp"];
}
else {
    $persen_wkp=0;
    }
    $sum1_wkp = $row["T1_wkp"] + $row["T5_wkp"] + $row["T8_wkp"] + $row["T11_wkp"];
    $sum2_wkp = $row["T1_1_wkp"] - $sum1_wkp;
	
// เด่นเหล็ก
if($row["T1_den"] > 0) {   
    $persen_den = ($row["T2_den"]*100)/$row["T1_den"];
}
else {
    $persen_den=0;
    }
    $sum1_den = $row["T1_den"] + $row["T5_den"] + $row["T8_den"] + $row["T11_den"];
    $sum2_den = $row["T1_1_den"] - $sum1_den;
	
// นาอิน
if($row["T1_nin"] > 0) {   
    $persen_nin = ($row["T2_nin"]*100)/$row["T1_nin"];
}
else {
    $persen_nin=0;
    }
    $sum1_nin = $row["T1_nin"] + $row["T5_nin"] + $row["T8_nin"] + $row["T11_nin"];
    $sum2_nin = $row["T1_1_nin"] - $sum1_nin;
	
// น้ำหมัน
if($row["T1_nmu"] > 0) {   
    $persen_nmu = ($row["T2_nmu"]*100)/$row["T1_nmu"];
}
else {
    $persen_nmu=0;
    }
    $sum1_nmu = $row["T1_nmu"] + $row["T5_nmu"] + $row["T8_nmu"] + $row["T11_nmu"];
    $sum2_nmu = $row["T1_1_nmu"] - $sum1_nmu;
	
// พญาแมน
if($row["T1_pym"] > 0) {   
    $persen_pym = ($row["T2_pym"]*100)/$row["T1_pym"];
}
else {
    $persen_pym=0;
    }
    $sum1_pym = $row["T1_pym"] + $row["T5_pym"] + $row["T8_pym"] + $row["T11_pym"];
    $sum2_pym = $row["T1_1_pym"] - $sum1_pym;
	
?>	

    <tr>
      <td nowrap style="text-align: left; font-size: 22px; font-weight: bold">&nbsp;<?php echo $name_provincial ?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T1_1"] > 0) ? $row["T1_1"]  : "") ?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T1"] > 0) ? $row["T1"]  : "") ?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T2"] > 0) ? $row["T2"]  : "") ?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold"><?php echo $persen ?>&nbsp;%</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T3"] > 0) ? $row["T3"]  : "") ?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T4"] > 0) ? $row["T4"]  : "") ?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T5"] > 0) ? $row["T5"]  : "")?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T6"] > 0) ? $row["T6"]  : "")?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T7"] > 0) ? $row["T7"]  : "")?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T8"] > 0) ? $row["T8"]  : "")?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T9"] > 0) ? $row["T9"]  : "")?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T10"] > 0) ? $row["T10"]  : "")?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T11"] > 0) ? $row["T11"]  : "")?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T12"] > 0) ? $row["T12"]  : "")?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T13"] > 0) ? $row["T13"]  : "")?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($sum1 > 0) ? $sum1  : "") ?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($sum2 > 0) ? $sum2  : "") ?>&nbsp;</td>
    </tr>
	<!-- ด่านแม่คำมัน -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.ด่านแม่คำมัน&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_dan"] > 0) ? $row["T1_1_dan"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_dan"] > 0) ? $row["T1_dan"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_dan"] > 0) ? $row["T2_dan"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?php echo $persen_dan ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_dan"] > 0) ? $row["T3_dan"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_dan"] > 0) ? $row["T4_dan"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_dan"] > 0) ? $row["T5_dan"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_dan"] > 0) ? $row["T6_dan"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_dan"] > 0) ? $row["T7_dan"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_dan"] > 0) ? $row["T8_dan"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_dan"] > 0) ? $row["T9_dan"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_dan"] > 0) ? $row["T10_dan"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_dan"] > 0) ? $row["T11_dan"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_dan"] > 0) ? $row["T12_dan"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_dan"] > 0) ? $row["T13_dan"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_dan > 0) ? $sum1_dan : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_dan > 0) ? $sum2_dan : "") ?>&nbsp;</td>
    </tr>
	<!-- ตรอน -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.ตรอน&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_tro"] > 0) ? $row["T1_1_tro"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_tro"] > 0) ? $row["T1_tro"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_tro"] > 0) ? $row["T2_tro"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?= $persen_tro ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_tro"] > 0) ? $row["T3_tro"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_tro"] > 0) ? $row["T4_tro"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_tro"] > 0) ? $row["T5_tro"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_tro"] > 0) ? $row["T6_tro"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_tro"] > 0) ? $row["T7_tro"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_tro"] > 0) ? $row["T8_tro"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_tro"] > 0) ? $row["T9_tro"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_tro"] > 0) ? $row["T10_tro"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_tro"] > 0) ? $row["T11_tro"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_tro"] > 0) ? $row["T12_tro"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_tro"] > 0) ? $row["T13_tro"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_tro > 0) ? $sum1_tro : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_tro > 0) ? $sum2_tro : "")?>&nbsp;</td>
    </tr> 
	<!-- ทองแสนขัน -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.ทองแสนขัน&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_tho"] > 0) ? $row["T1_1_tho"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_tho"] > 0) ? $row["T1_tho"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_tho"] > 0) ? $row["T2_tho"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?= $persen_tho ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_tho"] > 0) ? $row["T3_tho"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_tho"] > 0) ? $row["T4_tho"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_tho"] > 0) ? $row["T5_tho"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_tho"] > 0) ? $row["T6_tho"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_tho"] > 0) ? $row["T7_tho"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_tho"] > 0) ? $row["T8_tho"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_tho"] > 0) ? $row["T9_tho"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_tho"] > 0) ? $row["T10_tho"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_tho"] > 0) ? $row["T11_tho"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_tho"] > 0) ? $row["T12_tho"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_tho"] > 0) ? $row["T13_tho"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_tho > 0) ? $sum1_tho : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_tho > 0) ? $sum2_tho : "")?>&nbsp;</td>
    </tr> 
	<!-- ท่าปลา -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.ท่าปลา&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_thp"] > 0) ? $row["T1_1_thp"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_thp"] > 0) ? $row["T1_thp"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_thp"] > 0) ? $row["T2_thp"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?= $persen_thp ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_thp"] > 0) ? $row["T3_thp"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_thp"] > 0) ? $row["T4_thp"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_thp"] > 0) ? $row["T5_thp"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_thp"] > 0) ? $row["T6_thp"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_thp"] > 0) ? $row["T7_thp"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_thp"] > 0) ? $row["T8_thp"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_thp"] > 0) ? $row["T9_thp"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_thp"] > 0) ? $row["T10_thp"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_thp"] > 0) ? $row["T11_thp"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_thp"] > 0) ? $row["T12_thp"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_thp"] > 0) ? $row["T13_thp"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_thp > 0) ? $sum1_thp : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_thp > 0) ? $sum2_thp : "")?>&nbsp;</td>
    </tr> 
	<!-- น้ำปาด -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.น้ำปาด&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_npa"] > 0) ? $row["T1_1_npa"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_npa"] > 0) ? $row["T1_npa"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_npa"] > 0) ? $row["T2_npa"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?= $persen_npa ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_npa"] > 0) ? $row["T3_npa"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_npa"] > 0) ? $row["T4_npa"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_npa"] > 0) ? $row["T5_npa"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_npa"] > 0) ? $row["T6_npa"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_npa"] > 0) ? $row["T7_npa"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_npa"] > 0) ? $row["T8_npa"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_npa"] > 0) ? $row["T9_npa"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_npa"] > 0) ? $row["T10_npa"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_npa"] > 0) ? $row["T11_npa"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_npa"] > 0) ? $row["T12_npa"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_npa"] > 0) ? $row["T13_npa"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_npa > 0) ? $sum1_npa : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_npa > 0) ? $sum2_npa : "")?>&nbsp;</td>
    </tr> 
	<!-- บ้านโคก -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.บ้านโคก&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_bko"] > 0) ? $row["T1_1_bko"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_bko"] > 0) ? $row["T1_bko"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_bko"] > 0) ? $row["T2_bko"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?= $persen_bko ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_bko"] > 0) ? $row["T3_bko"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_bko"] > 0) ? $row["T4_bko"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_bko"] > 0) ? $row["T5_bko"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_bko"] > 0) ? $row["T6_bko"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_bko"] > 0) ? $row["T7_bko"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_bko"] > 0) ? $row["T8_bko"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_bko"] > 0) ? $row["T9_bko"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_bko"] > 0) ? $row["T10_bko"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_bko"] > 0) ? $row["T11_bko"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_bko"] > 0) ? $row["T12_bko"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_bko"] > 0) ? $row["T13_bko"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_bko > 0) ? $sum1_bko : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_bko > 0) ? $sum2_bko : "")?>&nbsp;</td>
    </tr> 
	<!-- พิชัย -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.พิชัย&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_pic"] > 0) ? $row["T1_1_pic"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_pic"] > 0) ? $row["T1_pic"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_pic"] > 0) ? $row["T2_pic"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?= $persen_pic ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_pic"] > 0) ? $row["T3_pic"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_pic"] > 0) ? $row["T4_pic"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_pic"] > 0) ? $row["T5_pic"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_pic"] > 0) ? $row["T6_pic"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_pic"] > 0) ? $row["T7_pic"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_pic"] > 0) ? $row["T8_pic"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_pic"] > 0) ? $row["T9_pic"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_pic"] > 0) ? $row["T10_pic"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_pic"] > 0) ? $row["T11_pic"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_pic"] > 0) ? $row["T12_pic"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_pic"] > 0) ? $row["T13_pic"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_pic > 0) ? $sum1_pic : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_pic > 0) ? $sum2_pic : "")?>&nbsp;</td>
    </tr>
	<!-- ฟากท่า -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.ฟากท่า&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_fag"] > 0) ? $row["T1_1_fag"] : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_fag"] > 0) ? $row["T1_fag"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_fag"] > 0) ? $row["T2_fag"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?= $persen_fag ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_fag"] > 0) ? $row["T3_fag"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_fag"] > 0) ? $row["T4_fag"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_fag"] > 0) ? $row["T5_fag"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_fag"] > 0) ? $row["T6_fag"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_fag"] > 0) ? $row["T7_fag"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_fag"] > 0) ? $row["T8_fag"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_fag"] > 0) ? $row["T9_fag"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_fag"] > 0) ? $row["T10_fag"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_fag"] > 0) ? $row["T11_fag"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_fag"] > 0) ? $row["T12_fag"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_fag"] > 0) ? $row["T13_fag"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_fag > 0) ? $sum1_fag : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_fag > 0) ? $sum2_fag : "") ?>&nbsp;</td>
    </tr> 
	<!-- เมืองอุตรดิตถ์ -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.เมืองอุตรดิตถ์&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_mud"] > 0) ? $row["T1_1_mud"] : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_mud"] > 0) ? $row["T1_mud"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_mud"] > 0) ? $row["T2_mud"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?php echo $persen_mud ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_mud"] > 0) ? $row["T3_mud"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_mud"] > 0) ? $row["T4_mud"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_mud"] > 0) ? $row["T5_mud"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_mud"] > 0) ? $row["T6_mud"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_mud"] > 0) ? $row["T7_mud"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_mud"] > 0) ? $row["T8_mud"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_mud"] > 0) ? $row["T9_mud"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_mud"] > 0) ? $row["T10_mud"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_mud"] > 0) ? $row["T11_mud"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_mud"] > 0) ? $row["T12_mud"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_mud"] > 0) ? $row["T13_mud"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_mud > 0) ? $sum1_mud : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_mud > 0) ? $sum2_mud : "") ?>&nbsp;</td>
    </tr> 
	<!-- ลับแล -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.ลับแล&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_lap"] > 0) ? $row["T1_1_lap"] : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_lap"] > 0) ? $row["T1_lap"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_lap"] > 0) ? $row["T2_lap"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?php echo $persen_lap ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_lap"] > 0) ? $row["T3_lap"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_lap"] > 0) ? $row["T4_lap"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_lap"] > 0) ? $row["T5_lap"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_lap"] > 0) ? $row["T6_lap"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_lap"] > 0) ? $row["T7_lap"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_lap"] > 0) ? $row["T8_lap"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_lap"] > 0) ? $row["T9_lap"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_lap"] > 0) ? $row["T10_lap"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_lap"] > 0) ? $row["T11_lap"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_lap"] > 0) ? $row["T12_lap"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_lap"] > 0) ? $row["T13_lap"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_lap > 0) ? $sum1_lap : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_lap > 0) ? $sum2_lap : "") ?>&nbsp;</td>
    </tr> 
	<!-- วังกะพี้ -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.วังกะพี้&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_wkp"] > 0) ? $row["T1_1_wkp"] : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_wkp"] > 0) ? $row["T1_wkp"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_wkp"] > 0) ? $row["T2_wkp"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?php echo $persen_wkp ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_wkp"] > 0) ? $row["T3_wkp"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_wkp"] > 0) ? $row["T4_wkp"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_wkp"] > 0) ? $row["T5_wkp"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_wkp"] > 0) ? $row["T6_wkp"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_wkp"] > 0) ? $row["T7_wkp"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_wkp"] > 0) ? $row["T8_wkp"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_wkp"] > 0) ? $row["T9_wkp"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_wkp"] > 0) ? $row["T10_wkp"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_wkp"] > 0) ? $row["T11_wkp"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_wkp"] > 0) ? $row["T12_wkp"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_wkp"] > 0) ? $row["T13_wkp"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_wkp > 0) ? $sum1_wkp : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_wkp > 0) ? $sum2_wkp : "") ?>&nbsp;</td>
    </tr> 
	  <!-- เด่นเหล็ก -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.เด่นเหล็ก&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_den"] > 0) ? $row["T1_1_den"] : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_den"] > 0) ? $row["T1_den"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_den"] > 0) ? $row["T2_den"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?php echo $persen_den ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_den"] > 0) ? $row["T3_den"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_den"] > 0) ? $row["T4_den"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_den"] > 0) ? $row["T5_den"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_den"] > 0) ? $row["T6_den"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_den"] > 0) ? $row["T7_den"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_den"] > 0) ? $row["T8_den"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_den"] > 0) ? $row["T9_den"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_den"] > 0) ? $row["T10_den"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_den"] > 0) ? $row["T11_den"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_den"] > 0) ? $row["T12_den"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_den"] > 0) ? $row["T13_den"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_den > 0) ? $sum1_den : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_den > 0) ? $sum2_den : "") ?>&nbsp;</td>
    </tr> 
	  <!-- นาอิน -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.นาอิน&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_nin"] > 0) ? $row["T1_1_nin"] : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_nin"] > 0) ? $row["T1_nin"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_nin"] > 0) ? $row["T2_nin"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?php echo $persen_nin ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_nin"] > 0) ? $row["T3_nin"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_nin"] > 0) ? $row["T4_nin"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_nin"] > 0) ? $row["T5_nin"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_nin"] > 0) ? $row["T6_nin"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_nin"] > 0) ? $row["T7_nin"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_nin"] > 0) ? $row["T8_nin"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_nin"] > 0) ? $row["T9_nin"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_nin"] > 0) ? $row["T10_nin"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_nin"] > 0) ? $row["T11_nin"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_nin"] > 0) ? $row["T12_nin"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_nin"] > 0) ? $row["T13_nin"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_nin > 0) ? $sum1_nin : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_nin > 0) ? $sum2_nin : "") ?>&nbsp;</td>
    </tr> 
	  <!-- น้ำหมัน -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.น้ำหมัน&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_nmu"] > 0) ? $row["T1_1_nmu"] : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_nmu"] > 0) ? $row["T1_nmu"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_nmu"] > 0) ? $row["T2_nmu"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?php echo $persen_nmu ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_nmu"] > 0) ? $row["T3_nmu"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_nmu"] > 0) ? $row["T4_nmu"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_nmu"] > 0) ? $row["T5_nmu"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_nmu"] > 0) ? $row["T6_nmu"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_nmu"] > 0) ? $row["T7_nmu"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_nmu"] > 0) ? $row["T8_nmu"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_nmu"] > 0) ? $row["T9_nmu"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_nmu"] > 0) ? $row["T10_nmu"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_nmu"] > 0) ? $row["T11_nmu"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_nmu"] > 0) ? $row["T12_nmu"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_nmu"] > 0) ? $row["T13_nmu"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_nmu > 0) ? $sum1_nmu : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_nmu > 0) ? $sum2_nmu : "") ?>&nbsp;</td>
    </tr> 
	  <!-- พญาแมน -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.พญาแมน&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_pym"] > 0) ? $row["T1_1_pym"] : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_pym"] > 0) ? $row["T1_pym"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_pym"] > 0) ? $row["T2_pym"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?php echo $persen_pym ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_pym"] > 0) ? $row["T3_pym"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_pym"] > 0) ? $row["T4_pym"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_pym"] > 0) ? $row["T5_pym"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_pym"] > 0) ? $row["T6_pym"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_pym"] > 0) ? $row["T7_pym"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_pym"] > 0) ? $row["T8_pym"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_pym"] > 0) ? $row["T9_pym"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_pym"] > 0) ? $row["T10_pym"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_pym"] > 0) ? $row["T11_pym"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_pym"] > 0) ? $row["T12_pym"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_pym"] > 0) ? $row["T13_pym"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_pym > 0) ? $sum1_pym : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_pym > 0) ? $sum2_pym : "") ?>&nbsp;</td>
    </tr> 
	
  </tbody>
<?php
        
	$no++;
}//while
?>
</table>
</div>
&nbsp;&nbsp;<button class="btn btn-primary btn-lg mb-4" onClick="do_print()"> พิมพ์รายงาน </button>
<script>
function Del(url)
{
	if(confirm("คุณจะลบข้อมูล หรือ ไม่ ?"))
	{
		window.location = url;		
	}
}
function dates_change()  // คำสั่งเปลี่ยนเดือน ปี
{
    var ms = $("#mi_cl_month option:selected").val();
    var ys = $("#mi_cl_year option:selected").val();
    window.location = "/Activity/Show_Activity8_UD.php?&month=" + ms + "&year=" + ys + "&rnd=" + Math.random();
}
</script>

<!-- script สำหรับส่งไปพิมพ์รายงาน  -->
<script>
    
    function do_print()
    {
        var ms = $("#mi_cl_month option:selected").val();
        var ys = $("#mi_cl_year option:selected").val();
        var newW = window.open("/Activity/Show_Activity8_UD_print.php?&month=" + ms + "&year=" + ys + "&rnd=" + Math.random(), "_print", "menubar=no,titlebar=no,toolbar=no,status=no,width=100,resizable=yes");
        newW.print();
        //window.print();
    }
    
</script>
