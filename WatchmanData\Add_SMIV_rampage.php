<?php
include '../Condb.php';
include '../users.inc.php';
include '../right_user.php';


// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// เมื่อมีการใช้ ตัวแปร Session จะต้องเปิดก่อน
//session_start();  // เปิดตัวแปร session
/*if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
*/

$pcid = $_GET['pcid']; // url parameter url ? [v1=222] & [v2=xxx]

$people_name = '';
if($pcid ) 
{
	$query1 = "SELECT ps_cl_prefix, ps_cl_name, ps_cl_surname, ps_cl_image FROM wm_tb_personal WHERE ps_cl_idcard ='$pcid' ";
	$result1 = mysqli_query($conn, $query1);
	$row1 = mysqli_fetch_array($result1);		
	$people_name = $row1['ps_cl_prefix'] . $row1['ps_cl_name'] . ' ' . $row1['ps_cl_surname'];
	$people_image = $row1["ps_cl_image"];
	if($people_image != '') {
		$people_image = "<img src='{$people_image}' height='80'> ";
	}
}

?>


<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link rel="icon" type="image/x-icon" href="/favicon.ico">
<title>เพิ่มข้อมูลการคลุ้มคลั่งอาละวาด</title>
<script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/policeinnopolis/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 18px;
    }
input::placeholder {
  color: red;
}
.form-control::placeholder {
  color: sandybrown;
}
</style>
<!-- สำหรับ วันที่ แบบ พ.ศ. -->
<!--<link rel="stylesheet" href="http://code.jquery.com/ui/1.9.1/themes/base/jquery-ui.css" />
<script src="http://code.jquery.com/jquery-1.8.2.js"></script>
<script src="/policeinnopolis/js/jquery-ui.js"></script>-->
	
<!-- สำหรับวันที่ เดือนไทย ปี พ.ศ. และ เลือนเลือกปี พ.ศ.ได้  -->
<link rel="stylesheet" href="../datepicker_th/jquery.datetimepicker.css" type="text/css">
<script src="../datepicker_th/jquery-1.8.3.min.js"></script>  
<script src="../datepicker_th/jquery.datetimepicker.full.js"></script>
</head>

<body>
	<div class="container">
	<div class="row">
		<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> เพิ่มข้อมูลการคลุ้มคลั่งอาละวาด ของ <?= $people_image ?> <?= $people_name ?> <?= $pcid ?> </div>
	<form action="Save_SMIV_rampage.php" method="POST" enctype="multipart/form-data" class="body">
        
        <label hidden ="hidden">ลำดับ</label>
		<input type = "text" name = "rm_cl_aid" class="form-control"  hidden ="hidden" >
        
		<label>เลขบัตรประชาชน</label>
		<input type = "text" name="rm_cl_idcard" class="form-control" value="<?= $pcid ?>"  readonly="readonly" >
        
        <!--<label>วันที่คลุ้มคลั่งอาละวาด <span style="color: #F90004">* จำเป็น</span> </label>
        <p><input type="text" name="rm_cl_date" id="datepicker" class="form-control" autocomplete="off" ></p>-->
        <label>วันที่คลุ้มคลั่งอาละวาด <span style="color: #F90004">* รูปแบบ ปี ค.ศ.-เดือน-วัน ** </span></label>
			<input type="text" name="rm_cl_date" id="datepicker" value="" placeholder="ใช้รูปแบบ 2023-09-25 เท่านั้น หรือเลือกจากปฎิทิน" class="form-control" style="width:350px;" autocomplete="off">
			<p id="result" style="color: red"></p>
		<script>
		  $(function() {
			$.datetimepicker.setLocale('th');
			$("#datepicker").datetimepicker({
			  timepicker: false,
			  format: 'Y-m-d',
			  lang: 'th',
			  onSelectDate: function(dp, $input) {
				var yearT = new Date(dp).getFullYear() - 0;
				var yearTH = yearT;
				var fulldate = $input.val();
				var fulldateTH = fulldate.replace(yearT, yearTH);
				$input.val(fulldateTH);

				// Get the result element and update the displayed year
				var result = document.getElementById('result');
				result.textContent = 'พ.ศ. ' + (yearTH+543);
			  },
			});

			$("#datepicker").on('change', function(e) {
			  var dateValue = $(this).val();
			  if (dateValue != "") {
				var arr_date = dateValue.split("-");
				dateValue = dateValue.replace(arr_date[0], yearT);
				$(this).val(dateValue);
			  }
			});
		  });
		</script>
        <!-- สำหรับ วันที่ แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ -->
        
		<label>สถานที่ก่อเหตุ</label>
		<input type = "text" name = "rm_cl_where" class="form-control" placeholder="ระบุสถานที่ก่อเหตุ"  >
        <label>ลักษณะการก่อเหตุ</label>
		<input type = "text" name = "rm_cl_maniac" class="form-control" placeholder="ระบุลักษณะการก่อเหตุ"  >
		<label>การดำเนินการ</label>
		<input type = "text" name = "rm_cl_action" class="form-control" placeholder="ระบุการดำเนินการ"  >
		<label>หมายเหตุ</label>
		<input type = "text" name = "rm_cl_remark" class="form-control" placeholder="หมายเหตุ"  >
	 	<br>
			<p>
			<input type="submit" value="บันทึกข้อมูล" class="btn btn-success" >
			<td> <a href="Show_All_SMIV.php?pcid=<?= $pcid ?>" class="btn btn-warning" >ยกเลิก</a> </td>
			</p>
		<br>
		<br>
	</form>
	</div>
	</div>
	</div>
</body>
</html>