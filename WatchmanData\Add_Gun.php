<?php
include '../Condb.php';
include '../users.inc.php';
include '../right_user.php';


// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// เมื่อมีการใช้ ตัวแปร Session จะต้องเปิดก่อน
//session_start();  // เปิดตัวแปร session
/*if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
*/

// สำหรับเลือก จังหวัด อำเภอ ตำบล
include("../config.inc.php");
include("../classes/class.database.inc.php");
// สำหรับเลือก จังหวัด อำเภอ ตำบล
$conn2 = get_connection(); // connect to mySQLDB
// สำหรับเลือกสถานีตำรวจทั่วประเทศ
$region = isset($_GET['region']) ? $_GET['region'] : 0; // sukhothai
$provincial = isset($_GET['provincial']) ? $_GET['provincial'] : 0; // sukhothai
//$station = isset($_GET['station']) ? $_GET['station'] : 0; // sukhothai

?>


<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>เพิ่มข้อมูลผู้ครอบครองอาวุธปืน</title>
<link rel="stylesheet" href="/policeinnopolis/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 18px;
    }
input::placeholder {
  color: red;
}
.form-control::placeholder {
  color: sandybrown;
}
</style>
    <!--  เลือกเพศอัตโนมัต -->
<script language="javascript" src="/policeinnopolis/jQuery/jquery-3.5.1.min.js"></script>    
<script>
    function doPrefixChange()
    {
        var selected = $("#ps_cl_prefix option:selected").val();
        if(selected == "นาย") {
            $("#ps_cl_sex1").prop("checked", true);
        }
        else if(selected == "นาง"){
            $("#ps_cl_sex2").prop("checked", true);
        }
        else if(selected == "น.ส."){
            $("#ps_cl_sex2").prop("checked", true);
        }
        else if(selected == "ด.ช."){
            $("#ps_cl_sex1").prop("checked", true);
        }
        else if(selected == "ด.ญ."){
            $("#ps_cl_sex2").prop("checked", true);
        }
        else {
            $("#ps_cl_sex1").prop("checked", false);
            $("#ps_cl_sex2").prop("checked", false);
            $("#ps_cl_sex3").prop("checked", false);
        }
    }
</script>
<!-- สำหรับ วันที่ แบบ พ.ศ. -->
<link rel="stylesheet" href="http://code.jquery.com/ui/1.9.1/themes/base/jquery-ui.css" />
<script src="http://code.jquery.com/jquery-1.8.2.js"></script>
<script src="/policeinnopolis/js/jquery-ui.js"></script>
</head>

<body>
	
	<div class="container">
	<div class="row">
	<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> เพิ่มข้อมูลผู้ครอบครองอาวุธปืน </div>
	<form action="Save_Gun.php" method="POST" enctype="multipart/form-data" class="body">
		<span style="color: #1203F8">
		<label>เลขบัตรประชาชน <span style="color: #F90004">* จำเป็น</span> </label>
		</span>
		<input type = "text" name="gu_cl_idcard" class="form-control" placeholder="กรอกเลขบัตรประชาชน 13 หลัก" autocomplete="off" Required >

		<label>คำนำหน้า</label>
		<select id="ps_cl_prefix" name="ps_cl_prefix" class="form-select form-select-sm"  onChange="doPrefixChange()">
			<option value="" selected> </option>
			<option value="นาย">นาย</option>
			<option value="นาง">นาง</option>
			<option value="น.ส.">น.ส.</option>
			<option value="ด.ช.">ด.ช.</option>
			<option value="ด.ญ.">ด.ญ.</option>
		</select>

        <label> กรณีบุคคลนั้น มียศ (โปรดระบุ)</label>
		<input type = "text" name = "gu_cl_rank" class="form-control" placeholder="หากมียศ ให้ระบุ" >
        
		<label>เพศ</label><br>
		  <input name="ps_cl_sex" id="ps_cl_sex1" type="radio" value="ชาย" checked="checked" />
		  ชาย </label>
		<label><input name="ps_cl_sex" id="ps_cl_sex2" type="radio" value="หญิง" /> 
		หญิง </label>																										
		<label><input name="ps_cl_sex" id="ps_cl_sex3" type="radio" value="LGBTQ" /> 
		  LGBTQ 
		</label><br>
        
		<label>ชื่อ <span style="color: #F90004">* จำเป็น</span> </label>
		<input type = "text" name = "ps_cl_name" class="form-control" placeholder="ระบุ ชื่อจริง"   Required  >
		<label>นามสกุล <span style="color: #F90004">* จำเป็น</span> </label>
		<input type = "text" name = "ps_cl_surname" class="form-control" placeholder="ระบุ นามสกุลจริง"  Required >
		<label>ชื่อเล่น</label>
		<input type = "text" name = "ps_cl_nickname" class="form-control" placeholder="ระบุ ชื่อเล่น" >
		
        <label>วันเดือนปีเกิด</label> 
        <p><input type="text" name="ps_cl_birthday2" id="datepicker" class="form-control" autocomplete="off" ></p>
        <script>
            function set_cal(ele)//function สร้างตัวเลือกปฎิทิน
            {
              $( ele ).datepicker({
                  onSelect:(date_text)=>
                  {
                    let arr=date_text.split("/");
                    let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])+543).toString();
                    $(ele).val(new_date);
                    $(ele).css("color","");
                  },
                  beforeShow:()=>{

                    if($(ele).val()!="")
                    {
                      let arr=$(ele).val().split("/");
                      let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])-543).toString();
                      $(ele).val(new_date);

                    }

                    $(ele).css("color","white");
                  },
                  onClose:()=>{

                      $(ele).css("color","");

                      if($(ele).val()!="")
                      {
                          let arr=$(ele).val().split("-");
                          if(parseInt(arr[2])<2500)
                          {
                              let new_date=arr[0]+"-"+arr[1]+"-"+(parseInt(arr[2])).toString();
                              $(ele).val(new_date);
                          }
                      }
                  },
                  dateFormat:"yy-mm-dd", //กำหนดรูปแบบวันที่เป็น วัน/เดือน/ปี
                  changeMonth:true,//กำหนดให้เลือกเดือนได้
                  changeYear:true,//กำหนดให้เลือกปีได้
                  showOtherMonths:true,//กำหนดให้แสดงวันของเดือนก่อนหน้าได้
              });
            }
          $(document).ready(function(){
            //เรียก function set_cal เมื่อเปิดหน้าเว็บ โดยส่ง object element ที่มี id เป็น datepicker เป็นพารามิเตอร์
            set_cal( $("#datepicker") );
          })
          </script>
        <!-- สำหรับ วันที่ แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ -->
        
		<label>บิดา</label>
		<input type = "text" name = "ps_cl_father" class="form-control" placeholder="ชื่อบิดา" >
		<label>เลขบัตรประชาชน บิดา</label>
		<input type = "text" name = "ps_cl_father_pid" class="form-control" placeholder="เลขบัตรประชาชน บิดา" >
		<label>มารดา</label>
		<input type = "text" name = "ps_cl_mother" class="form-control" placeholder="ชื่อมารดา" >
		<label>เลขบัตรประชาชน มารดา</label>
		<input type = "text" name = "ps_cl_mother_pid" class="form-control" placeholder="เลขบัตรประชาชน มารดา" >

        <label>สถานภาพสมรส</label>
		<select id="ps_cl_marital_status" name="ps_cl_marital_status" class="form-select form-select-sm" >
			<option value="" selected> </option>
			<option value="โสด">โสด</option>
			<option value="สมรส">สมรส</option>
			<option value="หย่า">หย่า</option>
			<option value="หม้าย">หม้าย</option>
			<option value="อื่น ๆ">อื่น ๆ</option>
		</select>
        <hr>
        <b>ข้อมูลเกี่ยวกับอาวุธปืน</b> <br>
        
        <label>เครื่องหมายทะเบียน</label>
		<input type = "text" name = "gu_cl_reg_no" class="form-control" placeholder="เครื่องหมายทะเบียน" >
        
        <label>หมายเลขประจำปืน</label>
		<input type = "text" name = "gu_cl_no" class="form-control" placeholder="หมายเลขประจำปืน" >
        
        <label>ประเภทปืน</label>
			<select id="gu_cl_type" name="gu_cl_type" class="form-select form-select-sm"  >
				<option value="" selected> </option>  <!-- ดึงข้อมูลมาจากตารางใหม่ -->
				<?php
					$res_gt = mysqli_query($conn, "SELECT * FROM `wm_tb_gun_type` ORDER BY `gt_cl_aid` ASC");
                
					while($row_gt = mysqli_fetch_array($res_gt))
					{
						echo "<option value='{$row_gt['gt_cl_aid']}'>{$row_gt['gt_cl_type']}</option>";
					}
				?>
			</select>

        <label>ชนิดปืน</label>
			<select id="gu_cl_kind" name="gu_cl_kind" class="form-select form-select-sm"  >
				<option value="" selected> </option>  <!-- ดึงข้อมูลมาจากตารางใหม่ -->
				<?php
					$res_v = mysqli_query($conn, "SELECT * FROM `wm_tb_gun_kind` ORDER BY `gk_cl_aid` ASC");
                
					while($row_v = mysqli_fetch_array($res_v))
					{
						echo "<option value='{$row_v['gk_cl_aid']}'>{$row_v['gk_cl_kind']}</option>";
					}
				?>
			</select>
        
        <label>ขนาด</label>
		<input type = "text" name = "gu_cl_bullet_size" class="form-control" placeholder="ขนาด" >
        
        <label>ยี่ห้อ</label>
		<input type = "text" name = "gu_cl_brand" class="form-control" placeholder="ยี่ห้อ" >
        
        <label>เลขที่ใบอนุญาต</label>
		<input type = "text" name = "gu_cl_license_no" class="form-control" placeholder="เลขที่ใบอนุญาต" >
        
        <label>อำเภอที่ออกใบอนุญาต</label>
		<input type = "text" name = "gu_cl_amphur" class="form-control" placeholder="อำเภอที่ออก" >
        
        <label>วันที่ออกใบอนุญาต</label>
        <p><input type="text" name="gu_cl_reg_date" id="datepicker2" class="form-control"  placeholder="วันที่ออกใบอนุญาต" autocomplete="off" ></p>
        <script>
            function set_cal(ele)//function สร้างตัวเลือกปฎิทิน
            {
              $( ele ).datepicker({
                  onSelect:(date_text)=>
                  {
                    let arr=date_text.split("/");
                    let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])+543).toString();
                    $(ele).val(new_date);
                    $(ele).css("color","");
                  },
                  beforeShow:()=>{

                    if($(ele).val()!="")
                    {
                      let arr=$(ele).val().split("/");
                      let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])-543).toString();
                      $(ele).val(new_date);

                    }

                    $(ele).css("color","white");
                  },
                  onClose:()=>{

                      $(ele).css("color","");

                      if($(ele).val()!="")
                      {
                          let arr=$(ele).val().split("-");
                          if(parseInt(arr[2])<2500)
                          {
                              let new_date=arr[0]+"-"+arr[1]+"-"+(parseInt(arr[2])).toString();
                              $(ele).val(new_date);
                          }
                      }
                  },
                  dateFormat:"yy-mm-dd", //กำหนดรูปแบบวันที่เป็น วัน/เดือน/ปี
                  changeMonth:true,//กำหนดให้เลือกเดือนได้
                  changeYear:true,//กำหนดให้เลือกปีได้
                  showOtherMonths:true,//กำหนดให้แสดงวันของเดือนก่อนหน้าได้
              });
            }
          $(document).ready(function(){
            //เรียก function set_cal เมื่อเปิดหน้าเว็บ โดยส่ง object element ที่มี id เป็น datepicker เป็นพารามิเตอร์
            set_cal( $("#datepicker2") );
          })
          </script>
        <!-- สำหรับ วันที่ แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ -->
        
		<label>สถานีตำรวจ <span style="color: #F90004">* จำเป็น</span></label><br>
                <!-- เลือกสถานีตำรวจ -->
        <label> บช. </label><br>
        <select name="region" id="region" onChange="do_region_change()" class="form-control" Required >
                <option value="">&lt;&lt; เลือก บช. &gt;&gt;</option>
                <?php
			  	$res_re = $conn2->query("SELECT * FROM `wm_tb_police_region` 
                                            ORDER BY `name_region` ASC");
			     $selected = '';
			     while($row_re = $conn2->fetch_row($res_re)) 
                     {
                      $code_region = $row_re['code_region'];
                      $name_region = $row_re['name_region'];
                      // set default provionce to 64 >> sukhothai
                          if($code_region == $region) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$code_region' $selected> $name_region </option>\n";
                     }
			  
			  ?>
              </select>
        
        <label> บก. </label><br>
        <select name="provincial" id="provincial" onChange="do_provincial_change()" class="form-control" >
        <option value="0">&lt;&lt; เลือก บก. &gt;&gt;</option>
                <?php
                if($region > 0)
                {
                    $res_prov = $conn2->query("SELECT * FROM `wm_tb_police_provincial` 
                                                WHERE `region_code`='$region' 
                                                ORDER BY `provincial_code` ASC");
                    $selected = '';
                  while($res_prov = $conn2->fetch_row($res_prov)) 
                  {
                      $provincial_code = $res_prov['provincial_code']; // 
                      $provincial = $res_prov['provincial'];
                      // set default provionce to 64 >> sukhothai
                          if($provincial_code == $provincial) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$provincial_code' $selected> $provincial </option>\n";
                  }
                }	
                ?>
              </select>

         <label> สน./สภ. </label>
        <br>
        <select name="station" id="station" class="form-control" >
                <option value="0">&lt;&lt; เลือก สน./สภ. &gt;&gt;</option>
                <?php
	           if($provincial > 0)
                {
                    $res_st = $conn2->query("SELECT * FROM `wm_tb_police_station2` 
                                                WHERE provincial_code='$provincial_code'
                                                ORDER BY `station_code` ASC");
                    $selected = '';
                  while($row_st = $conn2->fetch_row($res_st)) 
                  {
                      $station_code = $row_st['station_code']; // 
                      $station_name = $row_st['station_name'];
                      // set default provionce to 64 >> sukhothai
                          if($station_code == $station) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$station_code' $selected> $station_name </option>\n";
                  }
                }	
                ?>                                                                     
              </select>
		<br>

		<div class="mb-3">
			<label for="formFileMultiple" class="form-label">รูปภาพ</label>
			 <input class="form-control" type="file" id="ps_cl_image" name="ps_cl_image" multiple>
		</div>
		<p>
			<input type="submit" value="บันทึกข้อมูล" class="btn btn-success" >
			<td> <a href="/WatchmanData/Show_Gun.php?rnd=<?= rand(); ?>&page=ข้อมูลท้องถิ่น" class="btn btn-warning" >ยกเลิก</a> </td>
		</p>
		<br>
		<br>
	</form>
	</div>
	</div>
	</div>
<script>
// ฟังก์ชั่น เปลี่ยนภาค เป็น จังหวัด
function do_region_change()
{
	var sel_region = document.getElementById("region");		
	var code_region = sel_region.options[sel_region.selectedIndex].value;

	$.get("../ajax/get_provincial.php?region=" + code_region, 
		  function(data, status){
				//alert("Data: " + data + "\nStatus: " + status);
				// jQuery style						
				$('#provincial').find('option').remove().end();
				var data = $.trim(data);
				if(data == '') {
					alert("ไม่พบข้อมูล บก. !");
					return;
				}

					//.append('<option value="whatever">text</option>').val('whatever');
				var datas = data.split(';');
				for(var i=0; i<datas.length; i++) {
					if(datas[i] == '') continue;
					datas[i] = datas[i].split(':');
					var code_region = datas[i][0];
					var name_region = datas[i][1];
					$('#provincial').append('<option value="'+ code_region+'">' + name_region + '</option>');
				}
                // Trigger the change event on the amphur dropdown so the tambon dropdown will be populated     
                $('#provincial').trigger('change');
		});
}
    //เลือก สถานี จากจังหวัด
function do_provincial_change()
{
	var sel_provincial = document.getElementById("region");		
	var provincial_code = sel_provincial.options[sel_provincial.selectedIndex].value;
    
    var sel_provincial2 = document.getElementById("provincial");		
	var station_code = sel_provincial2.options[sel_provincial2.selectedIndex].value;

	$.get("../ajax/get_station.php?region=" + provincial_code + "&provincial=" + station_code, 
			  function(data, status){
					//alert("Data: " + data + "\nStatus: " + status);
					// jQuery style						
					$('#station').find('option').remove().end();
					var data = $.trim(data);
					if(data == '') {
						alert("ไม่พบข้อมูลสถานี !");
						return;
					}

						//.append('<option value="whatever">text</option>').val('whatever');
					var datas = data.split(';');
					for(var i=0; i<datas.length; i++) {
						if(datas[i] == '') continue;
						datas[i] = datas[i].split(':');
						var station_code = datas[i][0];
						var station_name = datas[i][1];
						$('#station').append('<option value="'+ station_code+'">' + station_name + '</option>');
					}
			});
}
</script>
<!--<script src="/js/datetimepicker2.5.1/jquery.ui.datepicker-th.js" type="text/javascript"></script> --> 
</body>
</html>