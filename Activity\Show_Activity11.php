<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';
include '../Head_image.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

//get month from select or current month
$month = isset($_GET['month']) ? $_GET['month'] : date("m");
$year = isset($_GET['year']) ? $_GET['year'] : date("Y");

$m1 = '';
$m2 = '';
$m3 = '';
$m4 = '';
$m5 = '';
$m6 = '';
$m7 = '';
$m8 = '';
$m9 = '';
$m10 = '';
$m11 = '';
$m12 = '';
switch($month)
{
    case "01": $m1 = "selected"; break;
    case "02": $m2 = "selected"; break;
    case "03": $m3 = "selected"; break;
    case "04": $m4 = "selected"; break;
    case "05": $m5 = "selected"; break;
    case "06": $m6 = "selected"; break;
    case "07": $m7 = "selected"; break;
    case "08": $m8 = "selected"; break;
    case "09": $m9 = "selected"; break;
    case "10": $m10 = "selected"; break;
    case "11": $m11 = "selected"; break;
    case "12": $m12 = "selected"; break;
}
$current_month = $global_thai_month[0+$month];
$cur_y = date("Y");
$y=2021;
$y<$cur_y; $y++;
$y2 = $y+543;
?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<base target="_top">
<title>รายงานการขับเคลื่อนงานสืบสวน</title>
<link rel="stylesheet" href="/bootstrap/css/bootstrap-grid.css">
<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<style type="text/css">
body{
    font-size:16px; 
}
.textAlignVer{
    display:block;
    filter: flipv fliph;
    -webkit-transform: rotate(-90deg); 
    -moz-transform: rotate(-90deg); 
    transform: rotate(-90deg); 
    position:relative;
    width:20px;
    white-space:nowrap;
    font-size:16px;
    margin-bottom:10px;
}
    .box !im{
        border: black;
        border-top-color: black !important;
        border-bottom-color: black !important;
        border-left-color: black !important;
        border-right-color: black !important;
        
    }
</style>
    
</head>
	
<body>
<div class="container-fluid" align="left">
<div class=" h3 text-center  alert alert-success mb-4 mt-4 " role="alert" >กิจกรรม 11 การตรวจสอบข้อมูลบุคคลต่างด้าวในพื้นที่</div>
    <a href="/WM/index.php?rnd=<?= rand(); ?>&page=report" class="btn btn-warning btn-lg mb-4" >ย้อนกลับ</a></td>
<h3 align="center">ข้อมูลบุคคลต่างด้าวในพื้นที่</h3>
    
<table width="90%" border="2" cellspacing="1" cellpadding="1" class="table table-bordered">
  <tbody>
         <tr class="box" height=28 style='height:21.0pt'>
           <td rowspan="2" bordercolor="#0C0B0B" class="box" style='border:solid #000000;
          height:21.0pt; background-color:yellow'>ลำดับ</td>
           <td rowspan="2" bordercolor="#000000" class="box" style='border:solid #000000; background-color:yellow '>หน่วยงาน</td>
           <td rowspan="2" bordercolor="#000000" class="box" style='border:solid #000000; background-color:yellow '><p>แหล่งข้อมูล</p></td>
           <td height="28" colspan="12" bordercolor="#000000" class="box" style='border:solid #000000; background-color:yellow '>สัญชาติ</td>
           <td rowspan="2" bordercolor="#0C0B0B" class="box" style='border:solid #000000;
          height:21.0pt; background-color:yellow '>หมายเหตุ</td>
         </tr>
         <tr class="box" height=28 style='height:21.0pt'>
           <td  bordercolor="#000000" class="box" style='border:solid #000000; background-color:yellow '><p>เมียนมา</p></td>
          <td class="box" width=104 style='border:solid #000000;
          border-top:none;width:78pt; background-color:yellow ' bordercolor="#0C0B0B">กัมพูชา</td>
          <td align=left class="box" style='border:solid #000000; background-color:yellow '>ลาว</td>
          <td align=left class="box" style='border:solid #000000; background-color:yellow '>อาเซียนอื่น ๆ</td>
          <td class="box" style='border:solid #000000; background-color:yellow '><p>เอเชียอื่น ๆ</td>
          <td class="box" style='border:solid #000000; background-color:yellow '>ยุโรป</td>
          <td class="box" style='border:solid #000000; background-color:yellow '>อเมริกาเหนือ</td>
          <td class="box" style='border:solid #000000; background-color:yellow '>อเมริกาใต้</td>
          <td class="box" style='border:solid #000000; background-color:yellow '>แอฟริกา</td>
          <td class="box" style='border:solid #000000; background-color:yellow '>ออสเตรเลีย</td>
          <td class="box" style='border:solid #000000; background-color:yellow '>อื่น ๆ</td>
          <td class="box" style='border:solid #000000; background-color:yellow '>รวม</td>
         </tr>
<?php     
                      
$sql2 = "SELECT " .
            "(SELECT COUNT(*) FROM `wm_tb_foreigner` WHERE `fo_cl_nationality`='เมียนมา' AND `source`='1' AND station='$station' ) AS T1, ".   //เมียนมา ตม
            "(SELECT COUNT(*) FROM `wm_tb_foreigner` WHERE `fo_cl_nationality`='เมียนมา' AND `source`='2' AND station='$station' ) AS T2, " .  //เมียนมา สำรวจเอง
            "(SELECT COUNT(*) FROM `wm_tb_foreigner` WHERE `fo_cl_nationality`='กัมพูชา' AND `source`='1' AND station='$station' ) AS T3, " .  //กัมพูชา ตม.
            "(SELECT COUNT(*) FROM `wm_tb_foreigner` WHERE `fo_cl_nationality`='กัมพูชา' AND `source`='2' AND station='$station' ) AS T4, " .  //กัมพูชา สำรวจเอง
            "(SELECT COUNT(*) FROM `wm_tb_foreigner` WHERE `fo_cl_nationality`='ลาว' AND `source`='1' AND station='$station' ) AS T5, " .  //ลาว ตม.
            "(SELECT COUNT(*) FROM `wm_tb_foreigner` WHERE `fo_cl_nationality`='ลาว' AND `source`='2' AND station='$station' ) AS T6, " .  //ลาว สำรวจเอง
            "(SELECT COUNT(*) FROM `wm_tb_foreigner` WHERE `fo_cl_nationality`='อาเซียนอื่น ๆ' AND `source`='1' AND station='$station' ) AS T7, " .  //อาเซียนอื่น ๆ
            "(SELECT COUNT(*) FROM `wm_tb_foreigner` WHERE `fo_cl_nationality`='อาเซียนอื่น ๆ' AND `source`='2' AND station='$station' ) AS T8 ".   //อาเซียนอื่น ๆ สำรวจเอง
            "";
                      
      $stmt = $pdo->prepare($sql2);
try{
    $stmt->execute();
}catch (PDOException $e) {
    echo '$sql2 Failed: '. $e->getMessage();
}

// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no2 = 1;
while($row2 = $stmt->fetch(PDO::FETCH_ASSOC))	//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
{
//ฟังก์ชั่น วันที่ ดึงจาก condb
//$strDate = DateThai( $row_mi["date_record"] );
    
$total = $row2["T1"] + $row2["T2"] ;
$total2 = $row2["T5"] + $row2["T6"] ;
$total3 = $row2["T1"] + $row2["T5"] ;
$total4 = $row2["T2"] + $row2["T6"] ;
    
$total_all = $total+$total2;

?>	

    <tr>
      <td height="53" style="font-size: 24px">1</td>
      <td style="font-size: 24px" nowrap><?= $name_station ?> &nbsp;</td>
      <td style="font-size: 24px">ข้อมูลจาก ตม.</td>
      <td style="font-size: 24px"><?php echo (($row2["T1"] > 0) ? $row2["T1"]  : "")?></td>
      <td style="font-size: 24px"><?php echo (($row2["T3"] > 0) ? $row2["T3"]  : "")?></td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row2["T5"] > 0) ? $row2["T5"]  : "")?></td>
      <td style="font-size: 24px">&nbsp;</td>
      <td style="font-size: 24px">&nbsp;</td>
      <td style="font-size: 24px">&nbsp;</td>
      <td style="font-size: 24px">&nbsp;</td>
      <td style="font-size: 24px">&nbsp;</td>
      <td style="font-size: 24px">&nbsp;</td>
      <td style="font-size: 24px">&nbsp;</td>
      <td style="font-size: 24px">&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?= $total3?> </td>
      <td style="font-size: 24px">&nbsp;</td>
    </tr>
    <tr>
      <td height="53" style="font-size: 24px">2</td>
      <td style="font-size: 24px" nowrap>&nbsp;<?= $name_station ?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;สำรวจเอง</td>
      <td style="font-size: 24px"><?php echo (($row2["T2"] > 0) ? $row2["T2"]  : "")?></td>
      <td style="font-size: 24px"><?php echo (($row2["T4"] > 0) ? $row2["T4"]  : "")?></td>
      <td style="font-size: 24px"><?php echo (($row2["T6"] > 0) ? $row2["T6"]  : "")?></td>
      <td style="font-size: 24px">&nbsp;</td>
      <td style="font-size: 24px">&nbsp;</td>
      <td style="font-size: 24px">&nbsp;</td>
      <td style="font-size: 24px">&nbsp;</td>
      <td style="font-size: 24px">&nbsp;</td>
      <td style="font-size: 24px">&nbsp;</td>
      <td style="font-size: 24px">&nbsp;</td>
      <td style="font-size: 24px">&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?= $total4 ?></td>
      <td style="font-size: 24px">&nbsp;</td>
    </tr>
    <tr>
	  <td height="53" colspan="3" style="font-size: 24px; background-color:yellow">รวม</td> 
      <td style="font-size: 24px; background-color:yellow">&nbsp;<?= $total ?>&nbsp;</td> <!--รวม -->
      <td style="font-size: 24px; background-color:yellow">&nbsp;&nbsp;</td>
      <td style="font-size: 24px; background-color:yellow">&nbsp;<?= $total2 ?>&nbsp;</td>
      <td style="font-size: 24px; background-color:yellow">&nbsp;&nbsp;</td>
      <td style="font-size: 24px; background-color:yellow">&nbsp;&nbsp;</td>
      <td style="font-size: 24px; background-color:yellow">&nbsp;&nbsp;</td>
      <td style="font-size: 24px; background-color:yellow">&nbsp;&nbsp;</td>
      <td style="font-size: 24px; background-color:yellow">&nbsp;&nbsp;</td>
      <td style="font-size: 24px; background-color:yellow">&nbsp;</td>
      <td style="font-size: 24px; background-color:yellow">&nbsp;</td>
      <td style="font-size: 24px; background-color:yellow">&nbsp;</td>
      <td style="font-size: 24px; background-color:yellow">&nbsp;<?= $total_all ?></td>
      <td style="font-size: 24px; background-color:yellow">&nbsp;</td>
    </tr>
  </tbody>
<?php
	$no2++;
}
?>
</table>
<p>&nbsp;</p>
<p>&nbsp;</p>  
    