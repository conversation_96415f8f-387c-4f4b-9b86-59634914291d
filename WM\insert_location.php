<?php
include '../Condb.php'; //PDO
include '../users.inc.php';
include '../Alert.php';
include '../right_user.php';

header("Location: /WatchmanData/main.php");
exit();

$timestamp = '';
$timezone = new DateTimeZone('Asia/Bangkok');
$datetime = new DateTime($timestamp);
$datetime->setTimezone($timezone);
$timestamp = $datetime->format('Y-m-d H:i:s');

/*echo '<pre>';
print_r($_POST);
echo '</pre>';

echo '<hr';

print_r($_FILES);
exit();*/

// Call this function at the top of each page to log the page view
$ip_address = $_SERVER['REMOTE_ADDR'];
$action = 'Save CCTV location'; // You can set the action to a suitable value
$data = str_replace("'", "", compact_array($_POST));
log_activity($acc, $action, $data, $ip_address);

$cctv_id = $_POST['cctv_id'];
$location_name = $_POST['location_name'];
$latitude = $_POST['latitude'];
$longitude = $_POST['longitude'];
$cctv_province = $_POST['cctv_province'];
$cctv_amphur = $_POST['cctv_amphur'];
$cctv_tumbon = $_POST['cctv_tumbon'];
$cctv_moo = $_POST['cctv_moo'];
$cctv_adr = $_POST['cctv_adr'];
$cctv_status = $_POST['cctv_status'];
$cctv_detail = $_POST['cctv_detail'];
$cctv_admin = $_POST['cctv_admin'];
$cctv_contact = $_POST['cctv_contact'];
$region = $region_user;
$provincial = $provincial_user;
$station = $station_user;
$cctv_owner = $_POST['cctv_owner'];
$recorder = $member_name;
$date_record = $timestamp;

// ล้างข้อมูล Lat Lon ที่ติดเครื่องหมายคอมม่ามา
$sanitizedLat = sanitizeCoordinate($latitude);
$sanitizedLon = sanitizeCoordinate($longitude);

// save Image
$file1 = $_FILES['cctv_img']['tmp_name'];
if (is_uploaded_file($file1)) {
    // เปลี่ยนชื่อไฟล์ก่อนบันทึก แก้ปัญหา ชื่อซ้ำกัน
    $cctv_img = "/policeinnopolis/uploaded/Image1/cctv_" . basename($_FILES['cctv_img']['name']);
    // file + ext
    $ext = strrchr($cctv_img, ".");
    $cctv_img = "../policeinnopolis/uploaded/Image1/CCTV_" . time() . $ext; // file+time+ext        
    move_uploaded_file($file1, $cctv_img);
} else {
    $cctv_img = '';
}

try {
    // ตรวจสอบค่าที่จำเป็นก่อนบันทึก
    if (empty($location_name) || empty($latitude) || empty($longitude)) {
        throw new Exception('กรุณากรอกข้อมูลให้ครบถ้วน');
    }

    // Insert data into the database
    $sql = "INSERT INTO cctv_locations 
            (location_name, latitude, longitude, cctv_province, cctv_amphur, cctv_tumbon, cctv_moo, cctv_adr, cctv_status, cctv_detail, cctv_admin, cctv_contact, region, provincial, station, cctv_owner, cctv_img, recorder, date_record)
            VALUES 
            (:location_name, :latitude, :longitude, :cctv_province, :cctv_amphur, :cctv_tumbon, :cctv_moo, :cctv_adr, :cctv_status, :cctv_detail, :cctv_admin, :cctv_contact, :region, :provincial, :station, :cctv_owner, :cctv_img, :recorder, :date_record)";
    $params = [
        'location_name' => $location_name,
        'latitude' => $sanitizedLat,
        'longitude' => $sanitizedLon,
        'cctv_province' => $cctv_province,
        'cctv_amphur' => $cctv_amphur,
        'cctv_tumbon' => $cctv_tumbon,
        'cctv_moo' => $cctv_moo,
        'cctv_adr' => $cctv_adr,
		'cctv_status' => $cctv_status,
        'cctv_detail' => $cctv_detail,
        'cctv_admin' => $cctv_admin,
        'cctv_contact' => $cctv_contact,
        'region' => $region,
        'provincial' => $provincial,
        'station' => $station,
        'cctv_owner' => $cctv_owner,
        'cctv_img' => $cctv_img,
        'recorder' => $recorder,
        'date_record' => $date_record
    ];

    $stmt = $pdo->prepare($sql);
    $result = $stmt->execute($params);

    /*if ($result) {
        echo 'บันทึกข้อมูลสำเร็จ';
    } else {
        throw new Exception('บันทึกข้อมูลไม่สำเร็จ');
    }*/
} catch (Exception $e) {
    echo 'Error: ' . $e->getMessage();
}


if ($result) {
    $_SESSION['success'] = "Data has been inserted succesfully";
    showSweetAlert("Saved successfully", "บันทึกข้อมูลเรียบร้อย");
    unset($_SESSION['success']); // Clear the session variable
    header("refresh:2; url=/WM/GoogleMapsDisplay.php");
} else {
    $_SESSION['error'] = "Error";
    showSweetAlert('Error', "บันทึกข้อมูลไม่สำเร็จ", 'error');
    unset($_SESSION['error']); // Clear the session variable
    header("refresh:2; url=/WM/GoogleMapsDisplay.php");
}

$pdo = null;

?>
