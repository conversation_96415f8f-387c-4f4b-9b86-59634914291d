<?php
include '../Condb.php';
include '../users.inc.php';
include '../Alert.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);
$ip_address = $_SERVER['REMOTE_ADDR'];
$action = 'Save CCTV'; // You can set the action to a suitable value
//$data = json_encode($_POST); // You can store the $_POST data as a JSON string
$data = str_replace("'", "", compact_array($_POST));
log_activity($acc, $action, $data, $ip_address); // ใช้ function ในไฟล์ Condb.php

/*echo '<pre>';
print_r($_POST);
echo '</pre>';

echo '<hr>';
exit();*/

$cc_cl_aid = $_POST[ 'cc_cl_aid' ];
$cc_cl_type = $_POST[ 'cc_cl_type' ];
$cc_cl_owner = $_POST[ 'cc_cl_owner' ];
$cc_cl_install = $_POST[ 'cc_cl_install' ];
$cc_cl_place = $_POST[ 'cc_cl_place' ];
$cc_cl_province = $_POST[ 'cc_cl_province' ];
$cc_cl_amphur = $_POST[ 'cc_cl_amphur' ];
$cc_cl_tumbon = $_POST[ 'cc_cl_tumbon' ];
$cc_cl_moo = $_POST[ 'cc_cl_moo' ];
$cc_cl_adr = $_POST[ 'cc_cl_adr' ];
$cc_cl_admin = $_POST[ 'cc_cl_admin' ];
$cc_cl_phone = $_POST[ 'cc_cl_phone' ];
$cc_cl_record = $_POST[ 'cc_cl_record' ];
$cc_cl_status = $_POST[ 'cc_cl_status' ];
$cc_cl_date = $_POST[ 'cc_cl_date' ];
$cc_cl_lat = $_POST[ 'cc_cl_lat' ];
$cc_cl_lon = $_POST[ 'cc_cl_lon' ];
$region = $_POST[ 'region' ];
$provincial = $_POST[ 'provincial' ];
$station = $_POST[ 'station' ];
$cc_cl_remark = $_POST[ 'cc_cl_remark' ];

// save Image ภาพกล้อง
$file1 = $_FILES[ 'cc_cl_cctv_picture' ][ 'tmp_name' ];
if ( is_uploaded_file( $file1 ) ) {
    // เปลี่ยนชื่อไฟล์ก่อนบันทึก แก้ปัญหา ชื่อซ้ำกัน
    $cc_cl_cctv_picture = "/policeinnopolis/uploaded/Image1/" . basename( $_FILES[ 'cc_cl_cctv_picture' ][ 'name' ] );	    
    // file + ext    
    $ext = strrchr($cc_cl_cctv_picture, ".");    
    $cc_cl_cctv_picture = "../policeinnopolis/uploaded/Image1/CCTV_" . time() . "_" . $station . $ext; // file+time+ext        
    move_uploaded_file( $file1, $cc_cl_cctv_picture );
} else {
  $cc_cl_cctv_picture = '';
}

// save Image ภาพมุมกล้อง
$file2 = $_FILES[ 'cc_cl_cctv_view' ][ 'tmp_name' ];
if ( is_uploaded_file( $file2 ) ) {
    // เปลี่ยนชื่อไฟล์ก่อนบันทึก แก้ปัญหา ชื่อซ้ำกัน
    $cc_cl_cctv_view = "/policeinnopolis/uploaded/Image1/" . basename( $_FILES[ 'cc_cl_cctv_view' ][ 'name' ] );	    
    // file + ext    
    $ext = strrchr($cc_cl_cctv_view, ".");    
    $cc_cl_cctv_view = "../policeinnopolis/uploaded/Image1/CCTV_" . time() . "_" . $station . $ext; // file+time+ext        
    move_uploaded_file( $file2, $cc_cl_cctv_view );
} else {
  $cc_cl_cctv_view = '';
}

// ล้างข้อมูล Lat Lon ที่ติดเครื่องหมายคอมม่ามา
$sanitizedLat = sanitizeCoordinate($cc_cl_lat);
$sanitizedLon = sanitizeCoordinate($cc_cl_lon);


$sql ="SELECT * FROM `wm_tb_cctv` WHERE cc_cl_aid='$cc_cl_aid' ";
$result = mysqli_query($conn,$sql);
$row = mysqli_fetch_array($result);
mysqli_report(MYSQLI_REPORT_ERROR | MYSQLI_REPORT_STRICT);
if($row) {
        $cc_cl_aid = $row['cc_cl_aid'];
        $sql = "UPDATE wm_tb_cctv SET " .
                    "cc_cl_type = '$cc_cl_type'," .
                    "cc_cl_owner = '$cc_cl_owner', ".
                    "cc_cl_install = '$cc_cl_install', ".
                    "cc_cl_place = '$cc_cl_place', ".
                    "cc_cl_province = '$cc_cl_province', ".
                    "cc_cl_amphur = '$cc_cl_amphur', ".
                    "cc_cl_tumbon = '$cc_cl_tumbon', ".
                    "cc_cl_moo = '$cc_cl_moo', ".
                    "cc_cl_adr = '$cc_cl_adr', ".
                    "cc_cl_admin = '$cc_cl_admin', ".
                    "cc_cl_phone = '$cc_cl_phone', ".
                    "cc_cl_record = '$cc_cl_record', ".
                    "cc_cl_status = '$cc_cl_status', ".
                    "cc_cl_date = '$cc_cl_date', ".
                    "cc_cl_lat = '$sanitizedLat', ".
                    "cc_cl_lon = '$sanitizedLon', ".
                    "region = '$region', ".
                    "provincial = '$provincial', ".
                    "station = '$station', ".
                    "cc_cl_remark = '$cc_cl_remark' ";

                    if($cc_cl_cctv_picture !== '')
                        {
                            $sql =  $sql . ", cc_cl_cctv_picture = '$cc_cl_cctv_picture' ";
                        }

                    elseif($cc_cl_cctv_view !== '')
                        {
                            $sql =  $sql . ", cc_cl_cctv_view = '$cc_cl_cctv_view' ";
                        }
                    $sql = $sql . " WHERE cc_cl_aid = '$cc_cl_aid' ";
        }else{
            $sql = "INSERT INTO wm_tb_cctv (cc_cl_type, cc_cl_owner, cc_cl_install, cc_cl_place, cc_cl_province, cc_cl_amphur, cc_cl_tumbon, cc_cl_moo, cc_cl_adr, cc_cl_admin, cc_cl_phone, cc_cl_record, cc_cl_status, cc_cl_date, cc_cl_lat, cc_cl_lon, region, provincial, station, cc_cl_remark, cc_cl_cctv_picture, cc_cl_cctv_view) VALUES('$cc_cl_type', '$cc_cl_owner', '$cc_cl_install', '$cc_cl_place', '$cc_cl_province', '$cc_cl_amphur', '$cc_cl_tumbon', '$cc_cl_moo', '$cc_cl_adr', '$cc_cl_admin', '$cc_cl_phone', '$cc_cl_record', '$cc_cl_status', '$cc_cl_date', '$sanitizedLat', '$sanitizedLon', '$region', '$provincial', '$station', '$cc_cl_remark', '$cc_cl_cctv_picture', '$cc_cl_cctv_view') ";
        }

$result=mysqli_query($conn, $sql);

if ($result) {
    $_SESSION['success'] = "Data has been inserted succesfully";
    showSweetAlert("Saved successfully", "บันทึกข้อมูลเรียบร้อย");
    unset($_SESSION['success']); // Clear the session variable
    header("refresh:2; url=/WatchmanData/Show_CCTV.php?pcid={$cc_cl_aid}");
} else {
    $_SESSION['error'] = "Error";
    showSweetAlert('Error', "บันทึกข้อมูล ไม่สำเร็จ", 'error');
    unset($_SESSION['error']); // Clear the session variable
    header("refresh:2; url=/WatchmanData/Show_CCTV.php?pcid={$cc_cl_aid}");
}

mysqli_close($conn);

?>

<!--<script>
error_reporting(E_ALL);
ini_set('display_errors', 1);
</script>-->