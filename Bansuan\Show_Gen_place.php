<script src="/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>
<script src="../SweetAlert2/dist/sweetalert2.all.min.js"></script>

<?php //PDO
include '../Condb.php';
include '../users.inc.php';
include '../right_user.php';
include '../Head_image.php';
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// crime type
$pg_type = isset($_GET['pg_type']) ? $_GET['pg_type'] : '0';
// ค้นข้อมูล
$search = isset($_GET['search']) ? $_GET['search'] : '';
// เรียงข้อมูล
$sort_by_no = isset($_GET['n']) ? $_GET['n'] : -1;

if($search != '') {                 // ถ้าคำค้นเป็นว่าง    
    $words = explode(',', $search); // 3 ข้อความ
    $name = $words[0];              // ชื่อ ตรงกับคำค้น เป็นตำแหน่งที่ 1
}
else {
    $name = "";
}

$options = "";
if($search != '') {                 // ถ้าคำค้นเป็นว่าง
    $options = "";
   
    if($name != "") {
        $option1 = "PD.gc_cl_name LIKE '%{$name}%' ";
        $options .= "OR " . $option1;
    }
    
    //
    if(substr($options,0,2) == "OR") {
        $options = trim(substr($options, 3));    
    }    
}

//echo $pg_type;
// ------------------
// list by crim type
// ------------------
if($pg_type == "0")
{
    $sql_pg = "SELECT PD.*, PT.pt_cl_type AS Type, PG.gp_cl_place AS Place FROM wm_tb_place_data AS PD " .
              "LEFT JOIN wm_tb_place_gen AS PG ON PD.gc_cl_gen_place_type = PG.gp_cl_aid " .
              "LEFT JOIN wm_tb_place_type AS PT ON PD.gc_cl_place = PT.pt_cl_aid  " .
              "WHERE PD.station='$station' AND PD.gc_cl_place='1' ";

    //print_r($sql_pg);
    
    if($options != "") {
        $sql_pg .= " AND " .$options;
    }
}

// ------------------
// no crime type + no search word
// ------------------
else {    
    $sql_pg = "SELECT PD.*, PT.pt_cl_type AS Type, PG.gp_cl_place AS Place FROM wm_tb_place_data AS PD " .
              "LEFT JOIN wm_tb_place_gen AS PG ON PD.gc_cl_gen_place_type = PG.gp_cl_aid " .
              "LEFT JOIN wm_tb_place_type AS PT ON PD.gc_cl_place = PT.pt_cl_aid  " .
              "WHERE PD.station='$station' AND PD.gc_cl_place='1' AND PD.gc_cl_gen_place_type='{$pg_type}' ";
    
       
    if($options != "") {
        $sql_pg .= " AND " .$options;
    }
        
}

//	
if($sort_by_no == 0){
    $sql_pg .= " ORDER BY PD.gc_cl_aid ASC ";
}
elseif($sort_by_no == 1) {
    $sql_pg .= " ORDER BY PD.gc_cl_aid DESC ";
}

$stmt_pg = $pdo->prepare($sql_pg);
$stmt_pg->execute();
$result_pg = $stmt_pg->fetchAll(PDO::FETCH_ASSOC);
$total = $stmt_pg->rowCount();


//$result_pg = mysqli_query($conn, $sql_pg);
//$total = mysqli_num_rows($result_pg);


//echo "พบข้อมูล <b>" . $total . "</b>";
//echo $result_pg ;
?>

<!DOCTYPE html>
<html>
    <head>
        <title>ระบบ WatchmanDB</title>
		<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
        <base target="_top">
		<link rel="stylesheet" href="../bootstrap/css/bootstrap.rtl.min.css">
		<link rel="stylesheet" href="../bootstrap-3.4.1-dist/css/bootstrap.min.css">
  		<script src="../jQuery/jquery-3.6.1.min.js"></script>
 		<script src="../bootstrap-3.4.1-dist/js/bootstrap.min.js"></script>
        <style>
            .swal2-popup {
            width: 500px !important;
            height: auto !important;
            font-size: 14px !important;
        }
        </style>
        <style>
            body {
              font-family: 'Courier New', Courier, monospace;
              font-size: 16px;
            }
        </style>
    </head>
    <body class="container-fluid">

<div>
	<?php
	include('../users_info.php');
	?>	
</div>
<div class="container-fluid" align="left">
		<div class=" h3 text-center  alert alert-success mb-4 mt-4 " role="alert" >ข้อมูลสถานที่ทั่วไป <?= $name_station ?></div>
		<div align="left"> &nbsp;<a href="Add_Gen_place.php" class="btn btn-success btn-lg mb-4 <?= $add_btn ?>" >เพิ่มข้อมูล</a>&nbsp;&nbsp;<a href="/Bansuan/?rnd=<?= rand(); ?>&page=information" class="btn btn-primary  btn-lg mb-4" >กลับ</a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
		<a style="background-color: yellow ; padding: 25px">จำนวนข้อมูล : <b style="color: crimson; font-size: 26px"><?= $total ?></b></a>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="Show_Gen_place_tb.php" class="btn btn-primary  btn-lg mb-4" >บัญชีข้อมูลสถานที่ทั่วไป <?= $name_station ?></a>&nbsp;
    </div>
    

    <div class="col-3" >
    <label style="font-size: 18px">สถานที่ทั่วไป ตามแบบ ศขส. 57 ประเภท</label>
        <select id="gp_cl_place" name="gp_cl_place" class="form-select form-select-sm" style="font-size: 18px ;background-color: #01FDFD; border-color: blueviolet" onChange="on_type_change()">
            <option value="0">เลือก</option>
            <?php
                $res_ty = $pdo->prepare("SELECT * FROM wm_tb_place_gen order by gp_cl_aid ASC");
				try{
                        $res_ty->execute();
                    }catch(PDOException $e){
                        echo 'Query $res_ty Failed: '. $e->getMessage();
                    }
                
                    while($row_ty = $res_ty->fetch(PDO::FETCH_ASSOC))
                    {
                        if($pg_type == $row_ty['gp_cl_aid']) {
                            $selected = "selected";
                        }
                        else {
                            $selected = "";
                        }
                        $gp_cl_aid = htmlspecialchars($row_ty['gp_cl_aid'], ENT_QUOTES, 'UTF-8');
                        $gp_cl_place = htmlspecialchars($row_ty['gp_cl_place'], ENT_QUOTES, 'UTF-8');
                        
                        echo "<option value='$gp_cl_aid' {$selected}> $gp_cl_place </option>";
                    }
            ?>
        </select>

    </div>
	<!--	 ปุ่ม search  -->
<table width="90%" border="0" cellspacing="5" cellpadding="1" style="flex: auto">
  <tbody>
    <tr align="center" >
      <td width="25%">
        <span class="form-group col-md-3; align-items-baseline" >
        <input name="searchName" id="searchName" type="text" class="form-control" placeholder="สืบค้นจาก ชื่อสถานที่" align="right" value="<?= $name ?>" style="background-color: #E8F6F6">
      </span> </td>
      <td width="13%"><span class="form-group col-md-3"><span class="input-group-btn">
        <a><button name="submit" type="submit" id="submit" value="search" class="btn btn-warning my-4"  align="right" onClick="do_search()"><span class="glyphicon glyphicon-search">ค้นหา</span></button></a>
      </span></span>
    </tr>
  </tbody>
</table>
    
<script>
  const input = document.getElementById("searchName");
  input.addEventListener("keyup", function(event) {
    if (event.keyCode === 13) {
      event.preventDefault();
      do_search();
    }
  });
</script> 

<script>
    // ฟังก์ชั่น ค้นหาข้อมูล
function do_search()
{
    var select_id = $('#gp_cl_place option:selected').val();
    var name = $('#searchName').val();
    //
    var url = "Show_Gen_place.php?pg_type=" + select_id + "&search="+ name + "&rnd=" + Math.random();
    
    window.location = url;
}
</script>
			
<a style="color: green">(คลิกที่ "ลำดับ" เพื่อเรียงข้อมูลจากล่างขึ้นบน)</a>
<table class="table table-striped table-hover mb-4 mt-4" >
  <tbody>
    <table class="table table-striped table-hover" > 
	<tr>	
		<th onClick="sort_by_no(<?= $sort_by_no ?>)" style="cursor:  pointer;"> ลำดับ </th>
		<th> ประเภท </th>
		<th> สถานที่ </th>
		<th> ชื่อสถานที่ </th>
		<th> ที่อยู่ </th>
		<th> โทรศัพท์ </th>
		<th> ชื่อเจ้าของ </th>    
		<th> เลขบัตรเจ้าของ </th>
		<th> ชื่อผู้จัดการ/ผู้ดูแล </th>
		<th> เลขบัตรผู้จัดการ/ผู้ดูแล </th>
        <th> ลักษณะของสถานที่ </th>
        <th> กิจกรรมของสถานที่ </th> 
        <th> ข้อมูลสำคัญที่ควรรู้ </th>
        <th> ผู้บันทึก </th>
        <th> วันที่บันทึก </th>
        <th> หน่วยงาน </th>
        <th> แผนที่ </th>
		<th> รูปภาพ</th>
		<th></th>
        <th></th>
	</tr>
	  

<?php
			
// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no = 1;
//while($row = mysqli_fetch_array($result_pg))	//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
foreach($result_pg AS $row)
    //echo $row;
//	$pcid = $row['gc_cl_aid']; //เลขบัตรนักการเมือง

{
    //ฟังก์ชั่น วันที่ ดึงจาก condb
    $strDate = DateThai( $row["gc_cl_date_record"] );
    
    $AID = $row['gc_cl_aid'];
		$lat = 0.0 + $row['gc_cl_place_lat'];
		$lon = 0.0 + $row['gc_cl_place_lon'];
		if($lat && $lon) {
			$map = "<a href='javascript:goMap({$row['gc_cl_place_lat']},{$row['gc_cl_place_lon']})'> Go </a>";
		}
		else {
			$map = "<em><small>ไม่ระบุ </small></em>";
		}
    
?>
	  
    <tr>	
		<td> <?= $no ?> </td>
		<td> <?=$row["Type"]?> </td>  
		<td> <?=$row["Place"]?> </td>
		<td> <?=$row["gc_cl_name"]?> </td>
		<td> <?=$row["gc_cl_place_address"]?> </td>
		<td> <?=$row["gc_cl_place_phone"]?> </td>
        <td> <?=$row["gc_cl_owner_name"]?></td>  
		<td> <?=$row["gc_cl_owner_idcard"]?> </td>
        <td> <?=$row["gc_cl_mgr_name"]?> </td> 
        <td> <?=$row["gc_cl_mgr_idcard"]?> </td>
        <td> <?=$row["gc_cl_general_charac"]?> </td>
        <td> <?=$row["gc_cl_activity"]?> </td>
        <td> <?=$row["gc_cl_important_info"]?> </td>
        <td> <?=$row["gc_cl_recorder"]?> </td>
        <td nowrap> <?= $strDate ?> </td>
        <td> <?=$row["station"]?> </td>
        <td style="font-size: 20px; color: red"> <?=$map?> </td>
		<td> <img src="<?= $row["gc_cl_place_image"] ?>" height="50"> </td>

        <td>&nbsp;<a href="Edit_Gen_place.php?id=<?=$row["gc_cl_aid"] ?>" class="btn btn-warning mb-4 <?= $edit_btn ?>" >แก้ไข</a>&nbsp;</td>
        
        <td>&nbsp;<button href="#" class="btn btn-danger mb-4" onClick="deleteItem(<?= $row["gc_cl_aid"] ?>)"<?=$del_btn?>>ลบ</button>&nbsp;</td>
        
<?php
	$no++;
}//while
    if($total == 0) {
        ?>
        <tr class="container-fluid">
      <td>&nbsp;</td>
      <td colspan="9">ไม่พบข้อมูล</td>
      <td>&nbsp;</td>
    </tr>
<?php      
    }
?>
  </tbody>
</table>
</body>
</html>

<script>    
function go_detail(pcid)	
{
	window.location = "/WatchmanData/Show_All.php?pcid=" +pcid + "&rnd=" ;
}
    
    
function sort_by_order(sort_type)
{
    var select_id = $('#gp_cl_place option:selected').val();
    
	if(sort_type == 1) {
		sort_type = 0;
	}
	else {
		sort_type = 1;
	}
    
	window.location ="Show_Gen_place.php?pg_type=" + select_id + "&n=" + sort_type + "&search=<?= $search ?>" + "&rnd=" + Math.random();
}
    
    // ฟังก์ชั่น เลือกประเภทอาชญากรรม (ยังทำไม่เสร็จ)
function on_type_change()
{
    var select_id = $('#gp_cl_place option:selected').val();    
    window.location ="Show_Gen_place.php?pg_type=" + select_id + "&rnd=" + Math.random();
}
    
    //map
function goMap(lat,long) 
{
	window.open("https://www.google.com/maps/search/?api=1&query=" + lat + "," + long, "_blank", '', true);
}
</script>
	
<script>
function deleteItem(id) {
    Swal.fire({
        title: 'คุณต้องการลบข้อมูลนี้ใช่ไหม?',
        text: "หลังจากลบแล้ว จะไม่สามารถกู้คืนได้",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'ใช่, ยืนยันลบ',
        cancelButtonText: 'ยกเลิก'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = '/Bansuan/Del_Gen_place.php?id=' + id;
        }
    });
}
</script>

