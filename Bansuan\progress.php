<script src="/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>
<script src="../SweetAlert2/dist/sweetalert2.all.min.js"></script>

<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

$pcid = $_GET['pcid']; // เลขบัตรผู้พ้นโทษ ถูกส่งมาจากการ click

$people_name = '';
if($pcid != '') {
	$query1 = "SELECT * FROM wm_tb_personal WHERE ps_cl_idcard = :pcid ";
        $stmt = $pdo->prepare($query1);
        $stmt->bindParam(':pcid', $pcid);
        $stmt->execute();
        $row1 = $stmt->fetch(PDO::FETCH_ASSOC);

}

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
<style>
    .swal2-popup {
    width: 500px !important;
    height: auto !important;
    font-size: 14px !important;
}
</style>
<title>ระบบ WatchmanDB</title>
</head>

<body>
	<div class="container-fluid">
		<div class=" h3 text-center  alert alert-success mb-2 mt-2 " role="alert" >ข้อมูลการติดตาม หมายจับ <?= $name_station ?></div>
		<div> &nbsp;&nbsp;<a href="/Bansuan/?rnd=<?= rand(); ?>&page=wanted" class="btn btn-warning mb-4" >ย้อนกลับ</a> &nbsp;&nbsp;<a href="add_progress.php?pcid=<?= $pcid ?>" class="btn btn-success mb-4 <?= $add_btn ?>" >เพิ่มข้อมูล </a></div>
	<table class="table table-striped table-hover table-bordered">
		<tr bgcolor="#0333FA">
            <th bgcolor="#0333FA"><a style="font: bold; color: aliceblue"> ลำดับ</a></th>
			<th bgcolor="#0333FA"><a style="font: bold; color: aliceblue"> เลขบัตร</a> </th>   
			<th bgcolor="#0333FA"><a style="font: bold; color: aliceblue"> วันที่ติดตามผล</a></th>
			<th bgcolor="#0333FA"><a style="font: bold; color: aliceblue"> ความคืบหน้า</a></th>
			<th bgcolor="#0333FA"><a style="font: bold; color: aliceblue"> การดำเนินการ</a></th>
			<th bgcolor="#0333FA"><a style="font: bold; color: aliceblue"> ผู้สืบสวน</a></th>
			<th bgcolor="#0333FA"><a style="font: bold; color: aliceblue"> หมายเหตุ</a></th>
			<th bgcolor="#0333FA"><a style="font: bold; color: aliceblue"> รายงานสืบสวน</a></th>
            <th bgcolor="#0333FA"><a style="font: bold; color: aliceblue"> แผนที่</a></th>
			<th colspan="2" bgcolor="#0333FA">  </th>
        </tr>  

     
<?php
$sql = "SELECT * FROM wm_tb_wanted_progress ";

if($pcid != ''){
	$sql .= " WHERE pg_cl_idcard = :pcid ";
}
$sql .= " ORDER BY pg_cl_date ASC";
                                                                                                                                       
$stmt = $pdo->prepare($sql);
$stmt->bindParam(':pcid', $pcid);
$stmt->execute();                                                                                                       
$no = 1;

while($row_pg = $stmt->fetch(PDO::FETCH_ASSOC)) {
    //ฟังก์ชั่น วันที่ ดึงจาก condb
    $strDate = DateThai( $row_pg["pg_cl_date"] );
    
    //ตรวจสอบไฟล์ก่อนว่า มีไฟล์ในตารางไหม หากมี ให้ส่งเป็นลิงค์
	$doc = $row_pg["pg_cl_file"];
	if($doc !== '')
	{
		$links = '<a href="/' . $row_pg["pg_cl_file"] .'" target="_blank">Download</a>';
	}
	else {
		$links = 'ไม่มีไฟล์';
	}
	
?>

		<tr>
			<td> <?= $no ?> </td>
			<td> <?= $pcid ?> </td>  
			<td nowrap> <?= $strDate ?> </td>
			<td> <?=$row_pg["pg_cl_progress"]?> </td>
			<td> <?=$row_pg["pg_cl_action"]?> </td>
			<td> <?=$row_pg["pg_cl_investigator"]?> </td>
			<td> <?=$row_pg["pg_cl_remark"]?> </td>
			<td> <?= $links ?> </td>
            <td> <a href="map/map_ck_base.php?id=<?= $row_pg["pg_cl_aid"]?>&pcid=<?=$pcid ?>" class="btn btn-primary mb-4"  target="_blank">แผนที่เบส</a> </td>
            
			<td>&nbsp;<a href="Edit_progress.php?id=<?= $row_pg["pg_cl_aid"]?>&pcid=<?=$pcid ?>" class="btn btn-warning mb-4 <?= $edit_btn ?>" >แก้ไข</a>&nbsp;</td>
            
            <td>&nbsp;<a href="#" class="btn btn-danger mb-4 <?= $del_btn ?>" onClick="deleteItem(<?= $row_pg["pg_cl_aid"] ?>, '<?= $pcid ?>')">ลบ</a>&nbsp;</td>

		</tr>
<?php
	$no++;
	}

if($no == 1) {
	echo "<tr><td colspan='10' style='color: red'> ไม่พบข้อมูลการติดตามหมายจับ ของ <b style='color: blue'>$people_name</b> </td></tr>";
}
	?>
	
</table>
<p>&nbsp;</p>
</div>

<script>
function deleteItem(id, pcid) {
    Swal.fire({
        title: 'คุณต้องการลบข้อมูลนี้ใช่ไหม?',
        text: "หลังจากลบแล้ว จะไม่สามารถกู้คืนได้",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'ใช่, ยืนยันลบ',
        cancelButtonText: 'ยกเลิก'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = '/Bansuan/Del_progress.php?id=' + id + '&pcid=' + pcid;
        }
    });
}
</script>
</body>
</html>