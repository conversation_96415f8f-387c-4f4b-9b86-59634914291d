<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';


// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);


$id = $_GET['id'];

//print_r($id);

$sql = "SELECT * FROM wm_tb_place_data WHERE gc_cl_aid = :id ";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':id', $id);
    $stmt->execute();
    $row = $stmt->fetch(PDO::FETCH_ASSOC);

// สำหรับเลือก จังหวัด อำเภอ ตำบล
include("../config.inc.php");
include("../classes/class.database.inc.php");
$conn2 = get_connection(); // connect to mySQLDB

// สำหรับเลือกสถานีตำรวจทั่วประเทศ
$region = isset($_GET['region']) ? $_GET['region'] : 0; // sukhothai
$provincial = isset($_GET['provincial']) ? $_GET['provincial'] : 0; // sukhothai
//$station = isset($_GET['station']) ? $_GET['station'] : 0; // sukhothai

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Edit General Place</title>
<script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/policeinnopolis/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 18px;
    }
input::placeholder {
  color: red;
}
.form-control::placeholder {
  color: sandybrown;
}
</style>
<!-- สำหรับวันที่ เดือนไทย ปี พ.ศ. และ เลือนเลือกปี พ.ศ.ได้  -->
<link rel="stylesheet" href="../datepicker_th/jquery.datetimepicker.css" type="text/css">
<script src="../datepicker_th/jquery-1.8.3.min.js"></script>  
<script src="../datepicker_th/jquery.datetimepicker.full.js"></script>
	
<script src="/policeinnopolis/js/jquery-ui.js"></script>
</head>

<body>
	
	<div class="container">
	<div class="row">
	<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> แก้ไขข้อมูลสถานที่ทั่วไป </div>
	<form action="../WM/Save_Gen_place_Edit.php" method="POST" enctype="multipart/form-data" class="body">
        
        <label hidden ="hidden">ลำดับ</label>
		<input type = "text" name = "gc_cl_aid" class="form-control" value= "<?= $row['gc_cl_aid']?>" hidden ="hidden" >
        
        <label>ประเภทสถานที่ <span style="color: #F90004">* จำเป็น</span></label>
                <select id="gc_cl_place" name="gc_cl_place" class="form-select form-select-sm" aria-label=".form-select-sm example" Required>
                    <option value="" selected> </option>  <!-- ดึงข้อมูลมาจากตารางใหม่ -->		
					<?php
						$gc_cl_place_old = $row['gc_cl_place']; // สมมุติว่าดึงค่าจากฐานข้อมูลก่อนหน้านี้

						$res1 = $pdo->prepare("SELECT * FROM `wm_tb_place_type` WHERE `pt_cl_aid`=1");
						$res1->execute();

						while($row1 = $res1->fetch(PDO::FETCH_ASSOC)) {
							$pt_cl_aid = htmlspecialchars($row1['pt_cl_aid'], ENT_QUOTES, 'UTF-8');
							$pt_cl_type = htmlspecialchars($row1['pt_cl_type'], ENT_QUOTES, 'UTF-8');

							$selected = ($pt_cl_aid == $gc_cl_place_old) ? 'selected' : '';
							echo "<option value='$pt_cl_aid' $selected>$pt_cl_type</option>";
						}
						?>
                </select>
        
        <label>สถานที่ทั่วไป 57 ประเภท <span style="color: #F90004">* จำเป็น</span></label>
                <select id="gc_cl_gen_place_type" name="gc_cl_gen_place_type" class="form-select form-select-sm" aria-label=".form-select-sm example" Required>
                    <option value="" selected> </option>  <!-- ดึงข้อมูลมาจากตารางใหม่ -->
					<?php
						$gc_cl_gen_place_type_old = $row['gc_cl_gen_place_type']; // สมมุติว่าดึงค่าจากฐานข้อมูลก่อนหน้านี้

						$res2 = $pdo->prepare("SELECT * FROM wm_tb_place_gen order by gp_cl_aid ASC");
                        $res2->execute();

						while($row2 = $res2->fetch(PDO::FETCH_ASSOC))
                        {
                            $gp_cl_aid = htmlspecialchars($row2['gp_cl_aid'], ENT_QUOTES, 'UTF-8');
                            $gp_cl_place = htmlspecialchars($row2['gp_cl_place'], ENT_QUOTES, 'UTF-8');
                            
							$selected = ($gp_cl_aid == $gc_cl_gen_place_type_old) ? 'selected' : '';
							echo "<option value='$gp_cl_aid' $selected>$gp_cl_place</option>";
                        }
						?>
					
                </select>
        
        <label>ชื่อสถานที่</label>
        <input type = "text" name = "gc_cl_name" class="form-control" value= "<?=$row['gc_cl_name']?>" placeholder="ระบุชื่อสถานที่"  >
        
        <label>ที่อยู่</label>
        <input type = "text" name = "gc_cl_place_address" class="form-control" value= "<?=$row['gc_cl_place_address']?>" placeholder="ระบุที่อยู่" >
        
        <label>โทรศัพท์</label>
        <input type = "text" name = "gc_cl_place_phone" class="form-control" value= "<?=$row['gc_cl_place_phone']?>" placeholder="ระบุหมายเลขโทรศัพท์" >
        
        <label>ชื่อเจ้าของ</label>
        <input type = "text" name = "gc_cl_owner_name" class="form-control" value= "<?=$row['gc_cl_owner_name']?>" placeholder="ระบุชื่อเจ้าของ" >
		
		<label>วันเดือนปีเกิดเจ้าของ <span style="color: #F90004">* รูปแบบ ปี ค.ศ.-เดือน-วัน ** หากไม่ทราบ ให้ข้าม</span></label>
			<input type="text" name="gc_cl_owner_bd1" id="datepicker1"  value= "<?=$row['gc_cl_owner_bd1']?>" placeholder="ใช้รูปแบบ 2023-09-25 เท่านั้น หรือเลือกจากปฎิทิน" class="form-control" style="width:350px;" autocomplete="off">
			<p id="result" style="color: red"></p>
		<script>
		  $(function() {
			$.datetimepicker.setLocale('th');
			$("#datepicker1").datetimepicker({
			  timepicker: false,
			  format: 'Y-m-d',
			  lang: 'th',
			  onSelectDate: function(dp, $input) {
				var yearT = new Date(dp).getFullYear() - 0;
				var yearTH = yearT;
				var fulldate = $input.val();
				var fulldateTH = fulldate.replace(yearT, yearTH);
				$input.val(fulldateTH);

				// Get the result element and update the displayed year
				var result = document.getElementById('result');
				result.textContent = 'พ.ศ. ' + (yearTH+543);
			  },
			});

			$("#datepicker1").on('change', function(e) {
			  var dateValue = $(this).val();
			  if (dateValue != "") {
				var arr_date = dateValue.split("-");
				dateValue = dateValue.replace(arr_date[0], yearT);
				$(this).val(dateValue);
			  }
			});
		  });
		</script>
		
		<label>อายุเจ้าของ</label>
			<input type = "text" name = "gc_cl_owner_bd2" value= "<?=$row['gc_cl_owner_bd2']?>" class="form-control" placeholder="อายุ หากมีวันเกิดแล้ว ไม่ต้องกรอกช่องนี้" >
		<br>
        
        <label>เลขบัตรเจ้าของ</label>
        <input type = "text" name = "gc_cl_owner_idcard" class="form-control" value= "<?=$row['gc_cl_owner_idcard']?>"  placeholder="เลขบัตรประชาชน เจ้าของ" >
		
		<label>ที่อยู่ตามบัตรเจ้าของ</label>
        <input type = "text" name = "gc_cl_owner_adr" class="form-control" value= "<?=$row['gc_cl_owner_adr']?>" placeholder="ที่อยู่ตามบัตรเจ้าของ" >
        
        <label>ชื่อผู้จัดการ/ผู้ดูแล</label>
        <input type = "text" name = "gc_cl_mgr_name" class="form-control" value= "<?=$row['gc_cl_mgr_name']?>"  placeholder="ระบุชื่อผู้จัดการ/ผู้ดูแล" >
		
		<label>วันเดือนปีเกิดผู้จัดการ/ผู้ดูแล <span style="color: #F90004">* รูปแบบ ปี ค.ศ.-เดือน-วัน ** หากไม่ทราบ ให้ข้าม</span></label>
			<input type="text" name="gc_cl_mgr_bd1" id="datepicker2"  value= "<?=$row['gc_cl_mgr_bd1']?>" placeholder="ใช้รูปแบบ 2023-09-25 เท่านั้น หรือเลือกจากปฎิทิน" class="form-control" style="width:350px;" autocomplete="off">
			<p id="result" style="color: red"></p>
		<script>
		  $(function() {
			$.datetimepicker.setLocale('th');
			$("#datepicker2").datetimepicker({
			  timepicker: false,
			  format: 'Y-m-d',
			  lang: 'th',
			  onSelectDate: function(dp, $input) {
				var yearT = new Date(dp).getFullYear() - 0;
				var yearTH = yearT;
				var fulldate = $input.val();
				var fulldateTH = fulldate.replace(yearT, yearTH);
				$input.val(fulldateTH);

				// Get the result element and update the displayed year
				var result = document.getElementById('result');
				result.textContent = 'พ.ศ. ' + (yearTH+543);
			  },
			});

			$("#datepicker2").on('change', function(e) {
			  var dateValue = $(this).val();
			  if (dateValue != "") {
				var arr_date = dateValue.split("-");
				dateValue = dateValue.replace(arr_date[0], yearT);
				$(this).val(dateValue);
			  }
			});
		  });
		</script>
		
		<label>อายุผู้จัดการ/ผู้ดูแล</label>
			<input type = "text" name = "gc_cl_mgr_bd2" value= "<?=$row['gc_cl_mgr_bd2']?>" class="form-control" placeholder="อายุ หากมีวันเกิดแล้ว ไม่ต้องกรอกช่องนี้" >
		<br>
        
        <label>เลขบัตรผู้จัดการ/ผู้ดูแล</label>
        <input type = "text" name = "gc_cl_mgr_idcard" class="form-control" value= "<?=$row['gc_cl_mgr_idcard']?>"  placeholder="เลขบัตรประชาชน ผู้จัดการ/ผู้ดูแล" >
		
		<label>ที่อยู่ตามบัตรผู้จัดการ/ผู้ดูแล</label>
        <input type = "text" name = "gc_cl_mgr_adr" class="form-control" value= "<?=$row['gc_cl_mgr_adr']?>"placeholder="ที่อยู่ตามบัตรผู้จัดการ/ผู้ดูแล" >
		
		<label>เบอร์โทรที่อยู่ตามบัตรผู้จัดการ/ผู้ดูแล</label>
        <input type = "text" name = "gc_cl_mgr_adr_phone" class="form-control" value= "<?=$row['gc_cl_mgr_adr_phone']?>"placeholder="เบอร์โทรที่อยู่ตามบัตรผู้จัดการ/ผู้ดูแล" >
		
		<label>ที่พักอาศัยปัจจุบันผู้จัดการ/ผู้ดูแล</label>
        <input type = "text" name = "gc_cl_mgr_adr_current" class="form-control" value= "<?=$row['gc_cl_mgr_adr_current']?>"placeholder="ที่พักอาศัยปัจจุบันผู้จัดการ/ผู้ดูแล" >
		
		<label>เบอร์โทรที่พักอาศัยปัจจุบันผู้จัดการ/ผู้ดูแล</label>
        <input type = "text" name = "gc_cl_mgr_adr_current_phone" class="form-control"value= "<?=$row['gc_cl_mgr_adr_current_phone']?>" placeholder="เบอร์โทรที่พักอาศัยปัจจุบันผู้จัดการ/ผู้ดูแล" >
        
        <label>ลักษณะทั่วไปของสถานที่</label>
        <input type = "text" name = "gc_cl_general_charac" class="form-control" value= "<?=$row['gc_cl_general_charac']?>" placeholder="ลักษณะทั้วไปของสถานที่" >
        
        <label>กิจกรรมของสถานที่</label>
        <input type = "text" name = "gc_cl_activity" class="form-control" value= "<?=$row['gc_cl_activity']?>" placeholder="กิจกรรมของสถานที่" >
        
        <label>ข้อมูลสำคัญที่ควรรู้</label>
        <input type = "text" name = "gc_cl_important_info" class="form-control" value= "<?=$row['gc_cl_important_info']?>"  placeholder="ข้อมูลสำคัญที่ควรรู้" >

        <label>ผู้บันทึกข้อมูล</label>
		<?php
			if ($station == 6707) {
				// User's station is 6707, display the dropdown select
				echo '<select id="gc_cl_recorder" name="gc_cl_recorder" class="form-select form-select-sm" placeholder="ระบุชื่อผู้บันทึก" required>';
				echo '<option value="" selected> </option>';
				$bsdp = $pdo->prepare("SELECT * FROM `police_name_bsdetective` ORDER BY `police_name_bsdetective`.`aid_bsdp` DESC");
				$bsdp->execute();
				while ($row_bsdp = $bsdp->fetch(PDO::FETCH_ASSOC)) {
					$aid_bsdp = htmlspecialchars($row_bsdp['aid_bsdp'], ENT_QUOTES, 'UTF-8');
					$bsdp_name = htmlspecialchars($row_bsdp['bsdp_name'], ENT_QUOTES, 'UTF-8');

					echo "<option value='$bsdp_name'>$bsdp_name</option>";
				}
				echo '</select>';
			} else {
				// User's station is not 6707, display a text input
				$originalValue = htmlspecialchars($row['gc_cl_recorder'], ENT_QUOTES, 'UTF-8');
				echo '<input type="text" id="gc_cl_recorder" name="gc_cl_recorder" value="' . $originalValue . '" class="form-control" placeholder="ระบุ ยศชื่อ-สกุล ผู้บันทึก" required>';
			}
		?>
        
        <label>วันที่บันทึก<span style="color: #F90004">* จำเป็น</span></label>
        <p><input type="text" name="gc_cl_date_record" id="datepicker" value= "<?=$row['gc_cl_date_record']?>" class="form-control" autocomplete="off" Required ></p>
        <script>
            function set_cal(ele)//function สร้างตัวเลือกปฎิทิน
            {
              $( ele ).datepicker({
                  onSelect:(date_text)=>
                  {
                    let arr=date_text.split("/");
                    let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])+543).toString();
                    $(ele).val(new_date);
                    $(ele).css("color","");
                  },
                  beforeShow:()=>{

                    if($(ele).val()!="")
                    {
                      let arr=$(ele).val().split("/");
                      let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])-543).toString();
                      $(ele).val(new_date);

                    }

                    $(ele).css("color","white");
                  },
                  onClose:()=>{

                      $(ele).css("color","");

                      if($(ele).val()!="")
                      {
                          let arr=$(ele).val().split("-");
                          if(parseInt(arr[2])<2500)
                          {
                              let new_date=arr[0]+"-"+arr[1]+"-"+(parseInt(arr[2])).toString();
                              $(ele).val(new_date);
                          }
                      }
                  },
                  dateFormat:"yy-mm-dd", //กำหนดรูปแบบวันที่เป็น วัน/เดือน/ปี
                  changeMonth:true,//กำหนดให้เลือกเดือนได้
                  changeYear:true,//กำหนดให้เลือกปีได้
                  showOtherMonths:true,//กำหนดให้แสดงวันของเดือนก่อนหน้าได้
              });
            }
          $(document).ready(function(){
            //เรียก function set_cal เมื่อเปิดหน้าเว็บ โดยส่ง object element ที่มี id เป็น datepicker เป็นพารามิเตอร์
            set_cal( $("#datepicker") );
          })
          </script>
        <!-- สำหรับ วันที่ แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ -->
        
        <label>สถานีตำรวจ (รับผิดชอบคดี หรือจับกุม/ควบคุมตัว) <span style="color: #F90004">* จำเป็น</span></label><br>
                <!-- เลือกสถานีตำรวจ -->
        <label> บช. </label><br>
        <select name="region" id="region" onChange="do_region_change()" class="form-control" Required >
                <option value="">&lt;&lt; เลือก บช. &gt;&gt;</option>
                <?php
                $region = $row['region']; // <<ดึงข้อมูลจาก ตารางมา row ซึ่งเป็นคนละตัวกับ row_p
                //------------------------------------
			  	$res_re = $conn2->query("SELECT * FROM `wm_tb_police_region` 
                                            ORDER BY `name_region` ASC");
			     $selected = '';
			     while($row_re = $conn2->fetch_row($res_re)) 
                     {
                      $code_region = $row_re['code_region'];
                      $name_region = $row_re['name_region'];
                      // set default provionce to 64 >> sukhothai
                          if($code_region == $region) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$code_region' $selected> $name_region </option>\n";
                     }
			  ?>
              </select>
        
        
        <label> บก. </label><br>        
        <select name="provincial" id="provincial" onChange="do_provincial_change()" value="<?php echo $provincial; ?>" class="form-control" >
        <option value="0">&lt;&lt; เลือก บก. &gt;&gt;</option>
                <?php
                if($region > 0)
                {
                    $provincial = $row['provincial']; // <<
                    //-------------------------------------
                    $res_prov = $conn2->query("SELECT * FROM `wm_tb_police_provincial` 
                                                WHERE `region_code`='$region' 
                                                ORDER BY `provincial_code` ASC");
                    $selected = '';
                  while($res_prov = $conn2->fetch_row($res_prov)) 
                  {
                      $provincial_code = $res_prov['provincial_code']; // 
                      $provincial = $res_prov['provincial'];
                      // set default provionce to 64 >> sukhothai
                          if($provincial_code == $provincial) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$provincial_code' $selected> $provincial </option>\n";
                  }
                }	
                ?>
              </select>
        
         <label> สน./สภ. </label>
        <br>
                
        <select name="station" id="station" class="form-control" >
                <option value="0">&lt;&lt; เลือก สน./สภ. &gt;&gt;</option>
                <?php
	           if($provincial > 0)
                {
                   $station = $row['station'];
                   //------------------------------------------
                    $res_st = $conn2->query("SELECT * FROM `wm_tb_police_station2` 
                                                WHERE provincial_code='$provincial_code'
                                                ORDER BY `station_code` ASC");
                    $selected = '';
                  while($row_st = $conn2->fetch_row($res_st)) 
                  {
                      $station_code = $row_st['station_code'];
                      $station_name = $row_st['station_name'];
                          if($station_code == $station) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      echo "<option value='$station_code' $selected> $station_name </option>\n";
                  }
                }	
            
                ?>                                                                     
              </select>
		<br>
        
        <label>ละติจูด<span style="color: #F90004">* จำเป็น</span></label>
        <input type = "text" name = "gc_cl_place_lat" class="form-control" value= "<?=$row['gc_cl_place_lat']?>"  placeholder="ละติจูด หากไม่มีให้ระบุเป็น 0" Required>
        
        <label>ลองจิจูด<span style="color: #F90004">* จำเป็น</span></label>
        <input type = "text" name = "gc_cl_place_lon" class="form-control" value= "<?=$row['gc_cl_place_lon']?>" placeholder="ลองจิจูด หากไม่มีให้ระบุเป็น 0" Required>

        <div class="mb-3">
            <label for="formFileMultiple" class="form-label">รูปภาพสถานที่</label>
             <input class="form-control" type="file" id="gc_cl_place_image" name="gc_cl_place_image" value= "<?=$row['gc_cl_place_image']?>"  multiple>
        </div>
        <p>
            <input type="submit" value="บันทึกข้อมูล" class="btn btn-success" >
            <td> <a href="../WM/Show_Gen_place.php" class="btn btn-warning" >ยกเลิก</a> </td>
        </p>
            <br>
            <br>
        </form>
        </div>
        </div>
        </div>
<!--<script src="/js/datetimepicker2.5.1/jquery.ui.datepicker-th.js" type="text/javascript"></script> -->
<script>
// ฟังก์ชั่น เปลี่ยนภาค เป็น จังหวัด
function do_region_change(current_value)
{
    current_value = (typeof current_value == 'undefined') ? 0 : current_value;
	var sel_region = document.getElementById("region");		
	var code_region = sel_region.options[sel_region.selectedIndex].value;

	$.get("../ajax/get_provincial.php?region=" + code_region, 
		  function(data, status){
				//alert("Data: " + data + "\nStatus: " + status);
				// jQuery style						
				$('#provincial').find('option').remove().end();
				var data = $.trim(data);
				if(data == '') {
					//alert("ไม่พบข้อมูล บก. !");
					return;
				}

					//.append('<option value="whatever">text</option>').val('whatever');
				var datas = data.split(';');
				for(var i=0; i<datas.length; i++) {
					if(datas[i] == '') continue;
					datas[i] = datas[i].split(':');
					var code_region = datas[i][0];
					var name_region = datas[i][1];
					//$('#provincial').append('<option value="'+ code_region+'">' + name_region + '</option>');
                    if(current_value == code_region) {
                        $('#provincial').append('<option value="'+ code_region+'" selected>' + name_region + '</option>'); 
                    }
                    else {
                        $('#provincial').append('<option value="'+ code_region+'">' + name_region + '</option>');
                    }
				}
                // Trigger the change event on the amphur dropdown so the tambon dropdown will be populated     
                $('#provincial').trigger('change');
		});
}
    //เลือก สถานี จากจังหวัด
function do_provincial_change(current_value)
{
    current_value = (typeof current_value == 'undefined') ? 0 : current_value;
    
	var sel_provincial = document.getElementById("region");		
	var provincial_code = sel_provincial.options[sel_provincial.selectedIndex].value;
    
    var sel_provincial2 = document.getElementById("provincial");		
	var station_code = sel_provincial2.options[sel_provincial2.selectedIndex].value;

	$.get("../ajax/get_station.php?region=" + provincial_code + "&provincial=" + station_code, 
			  function(data, status){
					//alert("Data: " + data + "\nStatus: " + status);
					// jQuery style						
					$('#station').find('option').remove().end();
					var data = $.trim(data);
					if(data == '') {
						//alert("ไม่พบข้อมูลสถานี !");
						return;
					}

						//.append('<option value="whatever">text</option>').val('whatever');
					var datas = data.split(';');
					for(var i=0; i<datas.length; i++) {
						if(datas[i] == '') continue;
						datas[i] = datas[i].split(':');
						var station_code = datas[i][0];
						var station_name = datas[i][1];
                        if(current_value == station_code) {
                            $('#station').append('<option value="'+ station_code+'" selected>' + station_name + '</option>'); 
                        }
                        else {
                            $('#station').append('<option value="'+ station_code+'">' + station_name + '</option>');   
                        }						
					}
			});
}
</script>   
<script>
function auto_select(sid, value)
{
    $('#' + sid).val(value);
}

$(document).ready(function() {
<?php
    echo "auto_select('gc_cl_place', '{$row['gc_cl_place']}');\n";
    echo "auto_select('gc_cl_gen_place_type', '{$row['gc_cl_gen_place_type']}');\n";
    echo "auto_select('gc_cl_recorder', '{$row['gc_cl_recorder']}');\n";
    //echo "auto_select('gc_cl_police_station', '{$row['gc_cl_police_station']}');\n";

?>
    setTimeout("do_region_change('<?= $row['provincial'] ?>')", 1000);
    setTimeout("do_provincial_change('<?= $row['station'] ?>')", 2000);
    });
</script>
</body>
</html>