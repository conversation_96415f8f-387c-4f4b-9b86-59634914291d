<?php
include '../Condb.php';
include '../users.inc.php';
include '../right_user.php';
include '../Head_image.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// เมื่อมีการใช้ ตัวแปร Session จะต้องเปิดก่อน
//session_start();  // เปิดตัวแปร session
/*if (session_status() == PHP_SESSION_NONE) {
    session_start();
}*/

$sql_all = "SELECT " .
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_crimes_place_type='1' AND station=:station ) AS T1, ".   //สถานที่ราชการ
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_crimes_place_type='2' AND station=:station ) AS T2, " .  //ทูตานุทูต
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_crimes_place_type='3' AND station=:station ) AS T3, " .  //กงสุล
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_crimes_place_type='4' AND station=:station ) AS T4, " .  //หัวหน้าองค์กรต่างประเทศ
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_crimes_place_type='5' AND station=:station ) AS T5, " .  //มหาวิทยาลัย
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_crimes_place_type='6' AND station=:station ) AS T6, " .  //วิทยาลัย
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_crimes_place_type='7' AND station=:station ) AS T7, " .  //วิทยาเขต
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_crimes_place_type='8' AND station=:station ) AS T8, ".   //สถาบัน
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_crimes_place_type='9' AND station=:station ) AS T9, " .  //โรงเรียน
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_crimes_place_type='10' AND station=:station ) AS T10, " .  //สถานฝึกอบรม
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_crimes_place_type='11' AND station=:station ) AS T11, " .  //สถานพยาบาลรัฐบาล
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_crimes_place_type='12' AND station=:station ) AS T12, " .  //สถานพยาบาลเอกชน
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_crimes_place_type='13' AND station=:station ) AS T13, " .  //ที่ทำการรัฐวิสาหกิจ
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_crimes_place_type='14' AND station=:station ) AS T14, " .  //สถานที่ท่องเที่ยว
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_crimes_place_type='15' AND station=:station ) AS T15, " .  //
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_crimes_place_type='16' AND station=:station ) AS T16 " .
            "";
    // Prepare the statement
    $stmt = $pdo->prepare($sql_all);

    // Bind the station value to the parameter
    $stmt->bindParam(':station', $station);

try {
    // Execute the query
    $stmt->execute();

    // Fetch the result as an associative array
    $row_all = $stmt->fetch(PDO::FETCH_ASSOC);

    // You can access the results using the column aliases (T1, T2, T31, T32)
/*    $T1 = $row_all['T1'];
    $T32 = $row_all['T32'];*/

} catch (PDOException $e) {
    // Handle any errors that may occur during the database operation
    echo "Error: " . $e->getMessage();
}

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
        <base target="_top">
        <title>ระบบ WatchmanDB</title>
		<link rel="stylesheet" href="../bootstrap/css/bootstrap.rtl.min.css">
		<link rel="stylesheet" href="../bootstrap-3.4.1-dist/css/bootstrap.min.css">
  		<script src="../jQuery/jquery-3.6.1.min.js"></script>
 		<script src="../bootstrap-3.4.1-dist/js/bootstrap.min.js"></script>
        <style>
            body {
              font-family: 'Courier New', Courier, monospace;
              font-size: 16px;
            }
        </style>
</head>

<body>

<div>
  <?php
    include('../users_info.php');
    ?>	
</div>

<div class=" h3 text-center  alert alert-danger mb-2 mt-2 " role="alert" >บัญชีข้อมูลสถานที่เกี่ยวข้องกับอาชญากรรม (แบบ ศขส.) 16 ประเภท <?= $name_station ?></div>
    <div style="flex: auto">
        &nbsp;&nbsp;&nbsp;<a href="/Bansuan/Show_Crimes_place.php?rnd=<?= rand(); ?>" class="btn btn-primary btn-lg mb-4" >ย้อนกลับ</a>&nbsp;
    </div>
    
<div class="container">
<table width="95%" border="1" cellspacing="1" cellpadding="1" class="table-striped table-hover table-bordered">
  <tbody>
    <tr>
      <td height="40" width="54%" bgcolor="#1D04F3" style="text-align: center"><strong>รายการ</strong></td>
      <td height="40" width="19%" bgcolor="#1D04F3" style="text-align: center" ><strong>จำนวน</strong></td>
      <td height="40" width="27%" bgcolor="#1D04F3" style="text-align: center"><strong>หมายเหตุ</strong></td>
      
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;1. สถานที่ผลิต, จำหน่าย หรือมั่วสุมยาเสพติด</td>
      <td style="text-align: center"><b>
          <?php          
          $T1Value = $row_all['T1'];
			if ($T1Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T1Value . '</b>';
			} else {
				echo $T1Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;2. สถานที่ผลิตอาวุธปืน, เครื่องกระสุนปืน และสิ่งเทียมอาวุธปืน</td>
      <td style="text-align: center"><b>
          <?php          
          $T2Value = $row_all['T2'];
			if ($T2Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T2Value . '</b>';
			} else {
				echo $T2Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;3. สถานที่ลักลอบเล่นการพนัน</td>
      <td style="text-align: center"><b>
          <?php          
          $T3Value = $row_all['T3'];
			if ($T3Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T3Value . '</b>';
			} else {
				echo $T3Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;4. สถานที่ได้รับอนุญาตให้มีการเล่นการพนัน</td>
      <td style="text-align: center"><b>
          <?php          
          $T4Value = $row_all['T4'];
			if ($T4Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T4Value . '</b>';
			} else {
				echo $T4Value;
			}
          ?></b></td>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;5. โต๊ะสนุกเกอร์, บิลเลียด</td>
      <td style="text-align: center"><b>
          <?php          
          $T5Value = $row_all['T5'];
			if ($T5Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T5Value . '</b>';
			} else {
				echo $T5Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;6. สถานที่ทำการค้าประเวณี</td>
      <td style="text-align: center"><b>
          <?php          
          $T6Value = $row_all['T6'];
			if ($T6Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T6Value . '</b>';
			} else {
				echo $T6Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;7. สถานบริการ</td>
      <td style="text-align: center"><b>
          <?php          
          $T7Value = $row_all['T7'];
			if ($T7Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T7Value . '</b>';
			} else {
				echo $T7Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;8. สถานที่ผลิต, เก็บ, จำหน่าย, เครื่องอุปโภคโดยผิดกฎหมาย (สุรา, อาหาร, ปุ๋ย, ของใช้ไม่ได้มาตรฐาน, โรงงานเถื่อน)</td>
      <td style="text-align: center"><b>
          <?php          
          $T8Value = $row_all['T8'];
			if ($T8Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T8Value . '</b>';
			} else {
				echo $T8Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;9. สถานที่ลักลอบจำหน่ายสินค้าหนีภาษี</td>
      <td style="text-align: center"><b>
          <?php          
          $T9Value = $row_all['T9'];
			if ($T9Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T9Value . '</b>';
			} else {
				echo $T9Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;10. สถานที่ลักลอบปลอมแปลงเงินตรา</td>
      <td style="text-align: center"><b>
          <?php          
          $T10Value = $row_all['T10'];
			if ($T10Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T10Value . '</b>';
			} else {
				echo $T10Value;
			}
          ?></b></td>
      <td height="40" style="font-size: 18px">&nbsp;</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;11. สถานที่ลักลอบปลอมแปลงเอกสารของทางราชการ เช่น พาสปอร์ต, ใบขับขี่ อื่น ฯลฯ</td>
      <td style="text-align: center"><b>
          <?php          
          $T11Value = $row_all['T11'];
			if ($T11Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T11Value . '</b>';
			} else {
				echo $T11Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;12. สถานที่ผลิตสินค้าละเมิดลิขสิทธิ์, เครื่องหมายการค้า, สิทธิบัตร</td>
      <td style="text-align: center"><b>
          <?php          
          $T12Value = $row_all['T12'];
			if ($T12Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T12Value . '</b>';
			} else {
				echo $T12Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;13. แหล่งผลิตหรือจำหน่ายเอกสาร วัตถุ, สิ่งพิมพ์ลามกอนาจาร</td>
      <td style="text-align: center"><b>
          <?php          
          $T13Value = $row_all['T13'];
			if ($T13Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T13Value . '</b>';
			} else {
				echo $T13Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;14. สถานที่เป็นแหล่งต้องสงสัย, ไม่น่าไว้วางใจ, หรือมีแนวโน้มที่จะกระทำผิดกฎหมาย</td>
      <td style="text-align: center"><b>
          <?php          
          $T14Value = $row_all['T14'];
			if ($T14Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T14Value . '</b>';
			} else {
				echo $T14Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;15. สถานที่เก็บวัสดุ หรือสารที่มีพิษ เป็นอันตราย หรืออาจก่อให้เกิดภยันตราย</td>
      <td style="text-align: center"><b>
          <?php          
          $T15Value = $row_all['T15'];
			if ($T15Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T15Value . '</b>';
			} else {
				echo $T15Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;16. อื่น ๆ</td>
      <td style="text-align: center"><b>
          <?php          
          $T16Value = $row_all['T16'];
			if ($T16Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T16Value . '</b>';
			} else {
				echo $T16Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
  </tbody>
</table>
    <br>
    <div style="flex: auto" align="right">
        &nbsp;<a href="/Bansuan/Show_Crimes_place.php?rnd=<?= rand(); ?>" class="btn btn-primary btn-lg mb-4" >กลับ</a>&nbsp;
    </div>
    <hr>
</div>
</body>
</html>