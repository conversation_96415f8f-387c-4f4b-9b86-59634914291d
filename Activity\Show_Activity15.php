<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';
include '../Head_image.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// เฉพาะ ยูเซอร์ บ้านเสวน = 1 เท่านั้นที่จะดูหน้านี้ได้
if($station != 6707)
{
	header("Location: /WatchmanData/main.php");
}


$id = isset($_GET['shoot_aid']) ? $_GET['shoot_aid'] : '';

////get month from select or current month
$month = isset($_GET['month']) ? $_GET['month'] : date("m");
$year = isset($_GET['year']) ? $_GET['year'] : date("Y");

$m1 = '';
$m2 = '';
$m3 = '';
$m4 = '';
$m5 = '';
$m6 = '';
$m7 = '';
$m8 = '';
$m9 = '';
$m10 = '';
$m11 = '';
$m12 = '';
switch($month)
{
    case "01": $m1 = "selected"; break;
    case "02": $m2 = "selected"; break;
    case "03": $m3 = "selected"; break;
    case "04": $m4 = "selected"; break;
    case "05": $m5 = "selected"; break;
    case "06": $m6 = "selected"; break;
    case "07": $m7 = "selected"; break;
    case "08": $m8 = "selected"; break;
    case "09": $m9 = "selected"; break;
    case "10": $m10 = "selected"; break;
    case "11": $m11 = "selected"; break;
    case "12": $m12 = "selected"; break;
}
$current_month = $global_thai_month[0+$month];
$y2 = $year+543;

?>

<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
        <base target="_top">
		<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
		<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
  		<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
 		<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js"></script>

<style type="text/css">
body{
    font-size:12px; 
}
.textAlignVer{
    display:block;
    filter: flipv fliph;
    -webkit-transform: rotate(-90deg); 
    -moz-transform: rotate(-90deg); 
    transform: rotate(-90deg); 
    position:relative;
    width:20px;
    white-space:nowrap;
    font-size:12px;
    margin-bottom:10px;
}
</style>
<!--<p><img src="../Image/Head2.jpg" width="100%" height="" alt=""/></p>-->
<div class="container-fluid" align="left">
<div class=" h3 text-center  alert alert-success mb-4 mt-4 " role="alert" >กิจกรรม 15 การยิงเก็บหัวกระสุนและปลอกกระสุนปืนในพื้นที่</div>

<div align="left">
<div>
    <table width="84%" border="0" cellspacing="1" cellpadding="1">
      <tbody>
        <tr>
          <td width="10%">&nbsp;<a href="/WM/index.php?rnd=<?= rand(); ?>&page=report" class="btn btn-warning btn-lg mb-4" >ย้อนกลับ</a></td>
          <td width="11%">&nbsp;<a href="#Add_Activity1.php" class="btn btn-success btn-lg mb-4 <?= $add_btn ?>" >เพิ่มข้อมูล</a></td>
          <td width="6%"><label>เลือกปี </label>
            <select id="mi_cl_year" name="mi_cl_year" class="form-select col-8 d-lg-inline" style="background-color: khaki; font-size: 16px" onChange="dates_change()">
              <option value="">เลือก</option>
              <?php
                $cur_y = date("Y") + 2;
                for($y=2020; $y<$cur_y; $y++) {
                    $sel = ($y == $year) ? "selected" : '';
                    echo "<option value='$y' $sel> ". ($y + 543) ." </option>";
                }
            ?>
          </select></td>
            <td width="1%">&nbsp;</td>
          <td width="10%"><label>เดือน</label>
            <select id="mi_cl_month" name="mi_cl_month" class="form-select col-3 d-lg-inline" style="background-color: khaki; font-size: 16px" onChange="dates_change()">
              <option value="">เลือก</option>
              <option <?= $m1 ?> value="01">มกราคม</option>
              <option <?= $m2 ?> value="02">กุมภาพันธ์</option>
              <option <?= $m3 ?> value="03">มีนาคม</option>
              <option <?= $m4 ?> value="04">เมษายน</option>
              <option <?= $m5 ?> value="05">พฤษภาคม</option>
              <option <?= $m6 ?> value="06">มิถุนายน</option>
              <option <?= $m7 ?> value="07">กรกฎาคม</option>
              <option <?= $m8 ?> value="08">สิงหาคม</option>
              <option <?= $m9 ?> value="09">กันยายน</option>
              <option <?= $m10 ?> value="10">ตุลาคม</option>
              <option <?= $m11 ?> value="11">พฤศจิกายน</option>
              <option <?= $m12 ?> value="12">ธันวาคม</option>
          </select></td>
          <td width="7%">&nbsp;</td>
            &nbsp;&nbsp;&nbsp;&nbsp;<td width="55%">&nbsp;<a href="history_shooting.php" class="btn btn-primary btn-lg mb-4" >ทะเบียนคุมอาวุธปืนสำหรับการยิงเก็บประวัติ</a>&nbsp;</td>
        </tr>
      </tbody>
    </table>
</div>
<br>
<h3 align="center">ข้อมูลการยิงเก็บหัวกระสุนและปลอกกระสุนปืนในพื้นที่ (แยกรายเดือน)</h3>
<h3 align="center">ประจำเดือน&nbsp; <?= $current_month ?> &nbsp;<?= $y2 ?> &nbsp;</h3>
<div class="table-responsive">
<table height="224" border="1" cellpadding="1" cellspacing="1" class="table-bordered table-hover mb-4 mt-4">
  <tbody>
    <tr>
      <td rowspan="2" bgcolor="#C8F96D" style="color: #00000; text-align: center; border-color: navy">ลำดับ</td>
      <td rowspan="2" bgcolor="#C8F96D" style="color: #00000; text-align: center; border-color: navy">สถานี</td>
      <td height="46" colspan="6" bgcolor="#C8F96D" style="color: #00000; text-align: center; border-color: navy">จำนวนอาวุธปืนที่ได้รับอนุญาต (ประสานนายอำเภอ)</td>
      <td colspan="6" bgcolor="#F2C0AD" style="color: #00000; text-align: center; border-color: navy">ยิงเก็บปลอก/หัวกระสุน</td>
      <td rowspan="2" bgcolor="yellow" style="color: #00000; text-align: center; border-color: navy">เหลือยังไม่ได้เก็บ</td>
      <td rowspan="2" bgcolor="#C8F96D" style="color: #00000; text-align: center; border-color: navy">วันที่</td>
      <td colspan="3" rowspan="2" bgcolor="#C8F96D" style="color: #00000; text-align: center; border-color: navy"></td>
    </tr>
    <tr>
	  <td height="58" bgcolor="#C8F96D" style="color: #00000; text-align: center; border-color: navy" >ก่อน 1 ต.ค.2565</td>
	  <td bgcolor="#C8F96D" style="color: #00000; text-align: center; border-color: navy">1-31 ต.ค.2565</td>
      <td bgcolor="#C8F96D" style="color: #00000; text-align: center; border-color: navy">1-30 พ.ย.2565</td>
      <td bgcolor="#C8F96D" style="color: #00000; text-align: center; border-color: navy">1-31 ธ.ค.2565</td>
      <td bgcolor="#C8F96D" style="color: #00000; text-align: center; border-color: navy">รวม 1 ต.ค.2565 - ปัจจุบัน</td>
      <td bgcolor="#C8F96D" style="color: #00000; text-align: center; border-color: navy">รวมทั้งหมด</td>
      <td bgcolor="#F2C0AD" style="color: #00000; text-align: center; border-color: navy">ก่อน 1 ต.ค.2565</td>
      <td bgcolor="#F2C0AD" style="color: #00000; text-align: center; border-color: navy">1-31 ต.ค.2565</td>
      <td bgcolor="#F2C0AD" style="color: #00000; text-align: center; border-color: navy">1-30 พ.ย.2565</td>
      <td bgcolor="#F2C0AD" style="color: #00000; text-align: center; border-color: navy">1-31 ธ.ค.2565</td>
      <td bgcolor="#F2C0AD" style="color: #00000; text-align: center; border-color: navy">รวม 1 ต.ค.2565 - ปัจจุบัน</td>
      <td bgcolor="#F2C0AD" style="color: #00000; text-align: center; border-color: navy">รวมทั้งหมด</td>
      </tr>
	  
<?php                                    

$m1 = "$year-$month-01";    // m1 = เดือนปัจจุบันที่เลือก
    $dtt = date("t", strtotime($m1));
    $m2 = "$year-$month-$dtt"; // << end date of current month ได้ค่าวันสุดท้ายของเดือนปัจจุบันที่เลือกมา
    // NOW()
    $now = $m2;   // ตัวแปร now จะเป็นค่าของเดือนปัจจบัน
    
$sql_m = "SELECT
            T1.*,
            T2.wm_station_name AS pol_st
        FROM
            wm_tb_gun_shooting_keep_shell AS T1 ".
            "LEFT JOIN wm_tb_police_station AS T2 ON T1.shoot_pol_st = T2.wm_PID
        WHERE
            MONTH(shoot_date)=:month AND YEAR(shoot_date)=:year ";

    $stmt_m = $pdo->prepare($sql_m);
    $stmt_m->bindParam(':month', $month);
    $stmt_m->bindParam(':year', $year);
try{
    $stmt_m->execute();
}catch (PDOException $e) {
    echo '$sql_m Failed: '. $e->getMessage();
}

// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no_m = 1;
while($row_m = $stmt_m->fetch(PDO::FETCH_ASSOC))	//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
{
    
    //ฟังก์ชั่น วันที่ ดึงจาก condb
 $strDate2 = DateThai( $row_m["shoot_date"] );

    //คำนวนยอด
$total_co_m = $row_m["befor_oct2022"]+$row_m["oct_2022"]+$row_m["nov_2022"]+$row_m["dec_2022"];
$total_sh_m = $row_m["sh_befor_oct2022"]+$row_m["sh_oct_2022"]+$row_m["sh_nov_2022"]+$row_m["sh_dec_2022"];
$balance_m = $total_co_m - $total_sh_m;
    // sum
?>	

    <tr>
      <td><?= $no_m ?></td>
      <td nowrap>&nbsp;
        <?= $row_m["pol_st"]?>
        &nbsp;</td>
      <td>&nbsp;
        <?= $row_m["befor_oct2022"] ?>
        &nbsp;</td>
      <td>&nbsp;
        <?= $row_m["oct_2022"] ?>
        &nbsp;</td>
      <td>&nbsp;
        <?= $row_m["nov_2022"]?>
        &nbsp;</td>
      <td>&nbsp;
        <?= $row_m["dec_2022"] ?>
        &nbsp;</td>
      <td bgcolor="#C8F96D">&nbsp;
        <?= $total_co_m ?>
        &nbsp;</td>
      <td bgcolor="#C8F96D">&nbsp;
        <?= $total_co_m ?>
        &nbsp;</td>
      <td>&nbsp;
        <?= $row_m["sh_befor_oct2022"]?>
        &nbsp;</td>
      <td>&nbsp;
        <?= $row_m["sh_oct_2022"]?>
        &nbsp;</td>

      <td>&nbsp;
        <?= $row_m["sh_nov_2022"] ?>
        &nbsp;</td>
      <td>&nbsp;
        <?= $row_m["sh_dec_2022"] ?>
        &nbsp;</td>
      <td bgcolor="#F2C0AD">&nbsp;
        <?= $total_sh_m ?>
        &nbsp;</td>
      <td bgcolor="#F2C0AD">&nbsp;
        <?= $total_sh_m ?>
        &nbsp;</td>
      <td bgcolor="yellow">&nbsp;
        <?= $balance_m ?>
        &nbsp;</td>
      <td nowrap="nowrap">&nbsp;
        <?= $strDate2 ?>
        &nbsp;</td>
      <td><a href="#Edit_mission.php?id=<?= $row_m["aid_crimes_record"] ?>" class="btn btn-warning mb-4" >แก้ไข</a></td>
      <td>    
    </tr>
<?php
	$no_m++;
}
?>

  </tbody>
</table>
</div>
<!-------------------------------------------------------------------->
<!-------------------------------------------------------------------->
<!-------------------------------------------------------------------->
<br>
<h3 align="center">ข้อมูลการยิงเก็บหัวกระสุนและปลอกกระสุนปืนในพื้นที่ ทั้งหมด</h3>
<div class="table-responsive">
<table height="224" border="1" cellpadding="1" cellspacing="1" class="table-bordered table-hover mb-4 mt-4">
  <tbody>
    <tr>
      <td rowspan="2" bgcolor="#C8F96D" style="color: #00000; text-align: center; border-color: navy">ลำดับ</td>
      <td rowspan="2" bgcolor="#C8F96D" style="color: #00000; text-align: center; border-color: navy">สถานี</td>
      <td height="46" colspan="7" bgcolor="#C8F96D" style="color: #00000; text-align: center; border-color: navy">จำนวนอาวุธปืนที่ได้รับอนุญาต (ประสานนายอำเภอ)</td>
      <td colspan="7" bgcolor="#F2C0AD" style="color: #00000; text-align: center; border-color: navy">ยิงเก็บปลอก/หัวกระสุน</td>
      <td rowspan="2" bgcolor="yellow" style="color: #00000; text-align: center; border-color: navy">เหลือยังไม่ได้เก็บ</td>
      <td rowspan="2" bgcolor="#C8F96D" style="color: #00000; text-align: center; border-color: navy">วันที่</td>
      <td colspan="3" rowspan="2" bgcolor="#C8F96D" style="color: #00000; text-align: center; border-color: navy"></td>
    </tr>
    <tr>
	  <td height="58" bgcolor="#C8F96D" style="color: #00000; text-align: center; border-color: navy" >ก่อน 1 ต.ค.2565</td>
	  <td bgcolor="#C8F96D" style="color: #00000; text-align: center; border-color: navy">1 ต.ค.-30 พ.ย.2565</td>
      <td bgcolor="#C8F96D" style="color: #00000; text-align: center; border-color: navy">1-31 ธ.ค.2565</td>
      <td bgcolor="#C8F96D" style="color: #00000; text-align: center; border-color: navy">1-31 ม.ค.2566</td>
      <td bgcolor="#C8F96D" style="color: #00000; text-align: center; border-color: navy">1-28 ก.พ.2566</td>
      <td bgcolor="#C8F96D" style="color: #00000; text-align: center; border-color: navy">รวม 1 ต.ค.2565 - ปัจจุบัน</td>
      <td bgcolor="#C8F96D" style="color: #00000; text-align: center; border-color: navy">รวมทั้งหมด</td>
      <td bgcolor="#F2C0AD" style="color: #00000; text-align: center; border-color: navy">ก่อน 1 ต.ค.2565</td>
      <td bgcolor="#F2C0AD" style="color: #00000; text-align: center; border-color: navy">1 ต.ค.-30 พ.ย.2565</td>
      <td bgcolor="#F2C0AD" style="color: #00000; text-align: center; border-color: navy">1-31 ธ.ค.2565</td>
      <td bgcolor="#F2C0AD" style="color: #00000; text-align: center; border-color: navy">1-31 ม.ค.2566</td>
      <td bgcolor="#F2C0AD" style="color: #00000; text-align: center; border-color: navy">1-28 ก.พ.2566</td>
      <td bgcolor="#F2C0AD" style="color: #00000; text-align: center; border-color: navy">รวม 1 ต.ค.2565 - ปัจจุบัน</td>
      <td bgcolor="#F2C0AD" style="color: #00000; text-align: center; border-color: navy">รวมทั้งหมด</td>
      </tr>
	  
<?php
$sql = "SELECT *, T2.wm_station_name AS pol_st ".
        "FROM wm_tb_gun_shooting_keep_shell AS T1 ".
        "LEFT JOIN wm_tb_police_station AS T2 ON T1.shoot_pol_st = T2.wm_PID " .
        "WHERE shoot_date ORDER BY shoot_aid ASC ";  //ตั้งตัวแปร sql เลือกฐานข้อมูลจากตารางที่ชื่อ wm_tb_personal
if($id != ''){
	$sql = $sql . " WHERE shoot_aid=:id ";
}
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':id', $id);
try{
    $stmt->execute();
}catch (PDOException $e) {
    echo '$sql Failed: '. $e->getMessage();
}
      
// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no = 1;
while($row = $stmt->fetch(PDO::FETCH_ASSOC))	//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
{
    
    //ฟังก์ชั่น วันที่ ดึงจาก condb
 $strDate = DateThai( $row["shoot_date"] );

$total_co = $row["befor_oct2022"]+$row["oct_2022"]+$row["nov_2022"]+$row["dec_2022"]+$row["jan_2023"]+$row["feb_2023"];


$total_sh = $row["sh_befor_oct2022"]+$row["sh_oct_2022"]+$row["sh_nov_2022"]+$row["sh_dec_2022"]+$row["sh_jan_2023"]+$row["sh_feb_2023"];


if($total_co == ''){
    $balance = '0';
}else{
    $balance = $total_co-$total_sh;
}
    
$total_co2 = $row["befor_oct2022"]+$row["oct_2022"]+$row["nov_2022"]+$row["dec_2022"]+$balance;

?>	

    <tr>
      <td><?= $no ?></td>
      <td nowrap>&nbsp;<?= $row["pol_st"]?>&nbsp;</td>
      <td>&nbsp;<?= $row["befor_oct2022"] ?>&nbsp;</td>
      <td>&nbsp;<?= $row["nov_2022"] ?>&nbsp;</td>
      <td>&nbsp;<?= $row["dec_2022"] ?>&nbsp;</td>
      <td>&nbsp;<?= $row["jan_2023"] ?>&nbsp;</td>
      <td>&nbsp;<?= $row["feb_2023"] ?>&nbsp;</td>
      <td bgcolor="#C8F96D">&nbsp;<?= $total_co ?>&nbsp;</td>
      <td bgcolor="#C8F96D">&nbsp;<?= $total_co ?>&nbsp;</td>
      <td>&nbsp;<?= $row["sh_befor_oct2022"]?>&nbsp;</td>
      <td>&nbsp;<?= $row["sh_nov_2022"] ?>&nbsp;</td>
      <td>&nbsp;<?= $row["sh_dec_2022"] ?>&nbsp;</td>
      <td>&nbsp;<?= $row["sh_jan_2023"] ?>&nbsp;</td>
      <td>&nbsp;<?= $row["sh_feb_2023"] ?>&nbsp;</td>
      <td bgcolor="#F2C0AD">&nbsp;
        <?= $total_sh ?>
        &nbsp;</td>
      <td bgcolor="#F2C0AD">&nbsp;
        <?= $total_sh ?>
        &nbsp;</td>
      <td bgcolor="yellow">&nbsp;
        <?= $balance ?>
        &nbsp;</td>
      <td nowrap="nowrap">&nbsp;
        <?= $strDate ?>
        &nbsp;</td>
      <td><a href="#Edit_mission.php?id=<?= $row["aid_crimes_record"] ?>" class="btn btn-warning mb-4" >แก้ไข</a></td>
      <td>    
    </tr>
<?php
	$no++;
}
?>
      
<?php
      $sql_s = "SELECT SUM(befor_oct2022) AS BF FROM wm_tb_gun_shooting_keep_shell";
        $res_s = $pdo->query($sql_s);
        $row_s = $res_s->fetch(PDO::FETCH_ASSOC);
      
      $sql_s2 = "SELECT SUM(oct_2022) AS oct_2022 FROM wm_tb_gun_shooting_keep_shell";
        $res_s2 = $pdo->query($sql_s2);
        $row_s2 = $res_s2->fetch(PDO::FETCH_ASSOC);
      
      $sql_s3 = "SELECT SUM(nov_2022) AS nov_2022 FROM wm_tb_gun_shooting_keep_shell ";
        $res_s3 = $pdo->query($sql_s3);
        $row_s3 = $res_s3->fetch(PDO::FETCH_ASSOC);
      
      $sql_s4 = "SELECT SUM(dec_2022) AS dec_2022 FROM wm_tb_gun_shooting_keep_shell ";
        $res_s4 = $pdo->query($sql_s4);
        $row_s4 = $res_s4->fetch(PDO::FETCH_ASSOC);

      $sql_s5 = "SELECT SUM(jan_2023) AS jan_2023 FROM wm_tb_gun_shooting_keep_shell ";
        $res_s5 = $pdo->query($sql_s5);
        $row_s5 = $res_s5->fetch(PDO::FETCH_ASSOC);

      $sql_s6 = "SELECT SUM(feb_2023) AS feb_2023 FROM wm_tb_gun_shooting_keep_shell ";
        $res_s6 = $pdo->query($sql_s6);
        $row_s6 = $res_s6->fetch(PDO::FETCH_ASSOC);

            
      $sql_sho = "SELECT SUM(sh_befor_oct2022) AS sh_befor_oct2022 FROM wm_tb_gun_shooting_keep_shell ";
        $res_sho = $pdo->query($sql_sho);
        $row_sho = $res_sho->fetch(PDO::FETCH_ASSOC);

      $sql_sho2 = "SELECT SUM(sh_oct_2022) AS sh_oct_2022 FROM wm_tb_gun_shooting_keep_shell ";
        $res_sho2 = $pdo->query($sql_sho2);
        $row_sho2 = $res_sho2->fetch(PDO::FETCH_ASSOC);

      $sql_sho3 = "SELECT SUM(sh_nov_2022) AS sh_nov_2022 FROM wm_tb_gun_shooting_keep_shell ";
        $res_sho3 = $pdo->query($sql_sho3);
        $row_sho3 = $res_sho3->fetch(PDO::FETCH_ASSOC);
      
      $sql_sho4 = "SELECT SUM(sh_dec_2022) AS sh_dec_2022 FROM wm_tb_gun_shooting_keep_shell ";
        $res_sho4 = $pdo->query($sql_sho4);
        $row_sho4 = $res_sho4->fetch(PDO::FETCH_ASSOC);

      $sql_sho5 = "SELECT SUM(sh_jan_2023) AS sh_jan_2023 FROM wm_tb_gun_shooting_keep_shell ";
        $res_sho5 = $pdo->query($sql_sho5);
        $row_sho5 = $res_sho5->fetch(PDO::FETCH_ASSOC);
      
      $sql_sho6 = "SELECT SUM(sh_feb_2023) AS sh_feb_2023 FROM wm_tb_gun_shooting_keep_shell ";
        $res_sho6 = $pdo->query($sql_sho6);
        $row_sho6 = $res_sho6->fetch(PDO::FETCH_ASSOC);

      //ยอดรวม 1 ต.ค.2565 - ปัจจุบัน
      $total_cos = $row_s['BF']+$row_s2['oct_2022']+$row_s3['nov_2022']+$row_s4['dec_2022']+$row_s5['jan_2023']+$row_s6['feb_2023'];

      //ยอดรวม 1 ต.ค.2565 - ปัจจุบัน
      $total_cosho = $row_sho['sh_befor_oct2022']+$row_sho2['sh_oct_2022']+$row_sho3['sh_nov_2022']+$row_sho4['sh_dec_2022']+$row_sho5['sh_jan_2023']+$row_sho6['sh_feb_2023'];
      $bl = $total_cos - $total_cosho;
?>
<!--ตารางรวม -->
    <tr>
	  <td height="52" colspan="2" style="font-size: 24px; background-color: yellow">รวม&nbsp;&nbsp;</td>
	  <td height="52" style="font-size: 24px; background-color: yellow">&nbsp;<?= $row_s['BF'] ?>&nbsp;</td>
	  <td height="52" style="font-size: 24px; background-color: yellow">&nbsp;&nbsp;
	    <?= $row_s3['nov_2022'] ?></td>
	  <td style="font-size: 24px; background-color: yellow">&nbsp;
	    <?= $row_s4['dec_2022'] ?>
	    &nbsp;</td>
	  <td style="font-size: 24px; background-color: yellow"><?= $row_s5['jan_2023'] ?></td>
	  <td style="font-size: 24px; background-color: yellow"><?= $row_s6['feb_2023'] ?></td>
	  <td height="52" style="font-size: 24px; background-color: yellow">&nbsp;<?= $total_cos ?>&nbsp;</td>
	  <td height="52" style="font-size: 24px; background-color: yellow">&nbsp;<?= $total_cos ?>&nbsp;</td>
	  <td height="52" style="font-size: 24px; background-color: yellow">&nbsp;<?= $row_sho['sh_befor_oct2022'] ?>&nbsp;</td>
	  <td height="52" style="font-size: 24px; background-color: yellow">&nbsp;&nbsp;
	    <?= $row_sho3['sh_nov_2022'] ?></td>
	  <td height="52" style="font-size: 24px; background-color: yellow">&nbsp;&nbsp;
	    <?= $row_sho4["sh_dec_2022"] ?></td>
	  <td style="font-size: 24px; background-color: yellow">&nbsp;&nbsp;
	    <?= $row_sho5["sh_jan_2023"] ?></td>
	  <td style="font-size: 24px; background-color: yellow">&nbsp;&nbsp;
	    <?= $row_sho6["sh_feb_2023"] ?></td>
	  <td height="52" style="font-size: 24px; background-color: yellow">&nbsp;<?= $total_cosho ?>&nbsp;</td>
	  <td height="52" style="font-size: 24px; background-color: yellow">&nbsp;<?= $total_cosho ?>&nbsp;</td>  
      <td bgcolor="yellow" style="font-size: 24px" >&nbsp;<?= $bl ?></td>
      <td colspan="2" nowrap="nowrap" bgcolor="yellow">&nbsp;&nbsp;</td>
	  
	  <td></td>
    </tr>
  </tbody>
</table>
</div>
    
    
<script>
function Del(url)
{
	if(confirm("คุณจะลบข้อมูล หรือ ไม่ ?"))
	{
		window.location = url;		
	}
}
function dates_change()  // คำสั่งเปลี่ยนเดือน ปี
{
    var ms = $("#mi_cl_month option:selected").val();
    var ys = $("#mi_cl_year option:selected").val();
    window.location = "/Activity/Show_Activity15.php?&month=" + ms + "&year=" + ys + "&rnd=" + Math.random();
}
</script>

<br>
<hr>
<br>

    

