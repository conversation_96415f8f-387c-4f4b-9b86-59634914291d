<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../Alert.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);
$ip_address = $_SERVER['REMOTE_ADDR'];
$action = 'Save online invest'; // You can set the action to a suitable value
$data = str_replace("'", "", compact_array($_POST));
log_activity($acc, $action, $data, $ip_address); // ใช้ function ในไฟล์ Condb.php


$oi_cl_aid = $_POST['oi_cl_aid'];
$oi_cl_case_no = $_POST['oi_cl_case_no'];
$oi_cl_date = $_POST['oi_cl_date'];
$oi_cl_invest = $_POST['oi_cl_invest'];
$oi_cl_invest_res = $_POST['oi_cl_invest_res'];
$oi_cl_detective = $_POST['oi_cl_detective'];

// save file รายงานสืบสวน
$file1 = $_FILES['oi_cl_file']['tmp_name'];
if (is_uploaded_file($file1)) {
    $oi_cl_file = "uploaded/Doc/" . $_FILES['oi_cl_file']['name'];
    move_uploaded_file($file1, "../" . $oi_cl_file);
} else {
    $oi_cl_file = '';
}

$sql = "SELECT * FROM wm_tb_online_invest WHERE oi_cl_aid = :oi_cl_aid ";
$stmt = $pdo->prepare($sql);
$stmt->bindParam(':oi_cl_aid', $oi_cl_aid);
$stmt->execute();
$row = $stmt->fetch(PDO::FETCH_ASSOC);

if ($row) {
    // Update existing record
    $sql = "UPDATE wm_tb_online_invest SET oi_cl_case_no=:oi_cl_case_no, oi_cl_date=:oi_cl_date, oi_cl_invest=:oi_cl_invest, oi_cl_invest_res=:oi_cl_invest_res, oi_cl_detective=:oi_cl_detective, oi_cl_file=:oi_cl_file WHERE oi_cl_aid=:oi_cl_aid";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':oi_cl_case_no', $oi_cl_case_no);
    $stmt->bindParam(':oi_cl_date', $oi_cl_date);
    $stmt->bindParam(':oi_cl_invest', $oi_cl_invest);
    $stmt->bindParam(':oi_cl_invest_res', $oi_cl_invest_res);
    $stmt->bindParam(':oi_cl_detective', $oi_cl_detective);
    $stmt->bindParam(':oi_cl_file', $oi_cl_file);
    $stmt->bindParam(':oi_cl_aid', $oi_cl_aid);
    $stmt->execute();
} else {
    // Insert new record
    $sql = "INSERT INTO wm_tb_online_invest (oi_cl_aid, oi_cl_case_no, oi_cl_date, oi_cl_invest, oi_cl_invest_res, oi_cl_detective, oi_cl_file) VALUES (:oi_cl_aid, :oi_cl_case_no, :oi_cl_date, :oi_cl_invest, :oi_cl_invest_res, :oi_cl_detective, :oi_cl_file)";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':oi_cl_aid', $oi_cl_aid);
    $stmt->bindParam(':oi_cl_case_no', $oi_cl_case_no);
    $stmt->bindParam(':oi_cl_date', $oi_cl_date);
    $stmt->bindParam(':oi_cl_invest', $oi_cl_invest);
    $stmt->bindParam(':oi_cl_invest_res', $oi_cl_invest_res);
    $stmt->bindParam(':oi_cl_detective', $oi_cl_detective);
    $stmt->bindParam(':oi_cl_file', $oi_cl_file);
    $stmt->execute();
}
		
if ($stmt->rowCount() > 0) {
        $_SESSION['success'] = "Data has been inserted succesfully";
        showSweetAlert("Saved successfully", "บันทึกข้อมูลเรียบร้อย");
        unset($_SESSION['success']); // Clear the session variable
        header("refresh:2; url=/Bansuan/show_detail_online.php?pcid={$oi_cl_case_no}");
    } else {
        $_SESSION['error'] = "Error";
        showSweetAlert('Error', "บันทึกข้อมูล ไม่สำเร็จ", 'error');
        unset($_SESSION['error']); // Clear the session variable
        header("refresh:2; url=/Bansuan/show_detail_online.php?pcid={$oi_cl_case_no}");
    }

$pdo = null;

?>