<?php
include '../Condb.php';

//echo '<pre>';
//print_r($_POST);
//echo '</pre>';
//
//echo '<hr>';

$wc_cl_aid=$_POST['wc_cl_aid'];
$wc_cl_date=$_POST['wc_cl_date'];
$wc_cl_idcard=$_POST['wc_cl_idcard'];
$wc_cl_name=$_POST['wc_cl_name'];
$wc_cl_football_match=$_POST['wc_cl_football_match'];
$wc_cl_dealer_case=$_POST['wc_cl_dealer_case'];
$wc_cl_dealer_person=$_POST['wc_cl_dealer_person'];
$wc_cl_players_case=$_POST['wc_cl_players_case'];
$wc_cl_players_person=$_POST['wc_cl_players_person'];
$wc_cl_walker_case=$_POST['wc_cl_walker_case'];
$wc_cl_walker_person=$_POST['wc_cl_walker_person'];
$wc_cl_dealer_net_case=$_POST['wc_cl_dealer_net_case'];
$wc_cl_dealer_net_person=$_POST['wc_cl_dealer_net_person'];
$wc_cl_players_net_case=$_POST['wc_cl_players_net_case'];
$wc_cl_players_net_person=$_POST['wc_cl_players_net_person'];
$wc_cl_poiball=$_POST['wc_cl_poiball'];
$wc_cl_bath=$_POST['wc_cl_bath'];
$wc_cl_cash=$_POST['wc_cl_cash'];
$wc_cl_other=$_POST['wc_cl_other'];
$wc_cl_remark=$_POST['wc_cl_remark'];
$wc_cl_record=$_POST['wc_cl_record'];

// convert Thai dates to eng
//if($wc_cl_date != '') {
//	if(strpos($wc_cl_date, '/') > 0) {
//    	$dates = explode('/', $wc_cl_date);	// d/m/y
//	}
//	elseif(strpos($wc_cl_date, '-') > 0) {
//		$date = explode('-', $wc_cl_date);	// y-m-d
//		$dates = array($date[2], $date[1], $date[0]);	// d/m/Y
//	}
//	// thai dates
//	if(substr(''.$dates[2],0,2) ==='25') {
//		$wc_cl_date = ($dates[2]-543) . '-' . $dates[1] . '-' . $dates[0];
//	}
//	// eng dates
//	else {
//    	$wc_cl_date = $dates[2] . '-' . $dates[1] . '-' . $dates[0];
//	}
//}

 $sql = "UPDATE wm_tb_gambling_football SET " .
		"wc_cl_date = '$wc_cl_date'," .
        "wc_cl_idcard = '$wc_cl_idcard'," .
        "wc_cl_name = '$wc_cl_name'," .
        "wc_cl_football_match = '$wc_cl_football_match'," .
        "wc_cl_dealer_case = '$wc_cl_dealer_case'," .
        "wc_cl_dealer_person = '$wc_cl_dealer_person'," .
        "wc_cl_players_case = '$wc_cl_players_case'," .
        "wc_cl_players_person = '$wc_cl_players_person'," .
        "wc_cl_walker_case = '$wc_cl_walker_case'," .
        "wc_cl_walker_person = '$wc_cl_walker_person'," .
        "wc_cl_dealer_net_case = '$wc_cl_dealer_net_case'," .
        "wc_cl_dealer_net_person = '$wc_cl_dealer_net_person'," .
        "wc_cl_players_net_case = '$wc_cl_players_net_case'," .
        "wc_cl_players_net_person = '$wc_cl_players_net_person'," .
        "wc_cl_poiball = '$wc_cl_poiball'," .
        "wc_cl_bath = '$wc_cl_bath'," .
        "wc_cl_cash = '$wc_cl_cash'," .
        "wc_cl_other = '$wc_cl_other'," .
        "wc_cl_remark = '$wc_cl_remark'," .
        "wc_cl_record = '$wc_cl_record' " .
        "WHERE wc_cl_aid = '$wc_cl_aid' ";

$result=mysqli_query($conn,$sql);
if($result){
	echo "<script>alert('บันทึกข้อมูลเรียบร้อย');</script>";	
}else{
	echo "<script>alert(\"บันทึกข้อมูลไม่สำเร็จ\\n" . print_r(mysqli_error($conn), true) . "\");</script>";
}
mysqli_close($conn);

echo "<script>window.location='Show_worldcup2022.php';</script>";

?>