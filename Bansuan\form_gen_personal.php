<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../Alert.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);


if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

//$pcid = *************;
$pcid = $_GET['pcid'];

// Store $pcid in a session variable
$_SESSION['pcid'] = $pcid;

// ข้อมูลของบุคคลนั้น
$sql = "SELECT 
            p.ps_cl_prefix, p.ps_cl_name, p.ps_cl_surname, p.ps_cl_nickname, p.ps_cl_birthday2, p.ps_cl_image, p.ps_cl_type, p.ps_cl_recorder, p.ps_cl_date_rec,
            rec.bsdp_name AS recorder, rec.bsdp_position AS position,
            per.gd_cl_name AS person_type,
            ad.ad_cl_num AS num, ad.ad_cl_moo AS moo,
            T2.pv_name_th AS province, T3.ap_name_th AS amphur, T4.tb_name_th AS tumbon,
            ph.ph_cl_phone AS phone,
            wk.wk_cl_career AS career, wk.wk_cl_job AS job, wk.wk_cl_address AS wk_adr, wk.wk_cl_phone AS wk_phone, wk.wk_cl_status AS wk_status, wk.wk_cl_date_out AS d_out,
            
            f.ps_cl_prefix AS f_prefix, f.ps_cl_name AS f_name, f.ps_cl_surname AS f_surname, f.ps_cl_nickname AS f_nickname, f.ps_cl_birthday2 AS f_bd, f.ps_cl_death AS f_death,
            ad_f.ad_cl_num AS num_f, ad_f.ad_cl_moo AS moo_f,
            T2_f.pv_name_th AS province_f, T3_f.ap_name_th AS amphur_f, T4_f.tb_name_th AS tumbon_f,
            d.meaning AS fa_death,
            ph_f.ph_cl_phone AS phone_f,
            wk_f.wk_cl_career AS career_f, wk_f.wk_cl_job AS job_f,
            
            m.ps_cl_prefix AS m_prefix, m.ps_cl_name AS m_name, m.ps_cl_surname AS m_surname, m.ps_cl_nickname AS m_nickname, m.ps_cl_birthday2 AS m_bd, m.ps_cl_death AS m_death,
            ad_m.ad_cl_num AS num_m, ad_m.ad_cl_moo AS moo_m,
            T2_m.pv_name_th AS province_m, T3_m.ap_name_th AS amphur_m, T4_m.tb_name_th AS tumbon_m,
            ph_m.ph_cl_phone AS phone_m,
            d2.meaning AS ma_death,
            wk_m.wk_cl_career AS career_m, wk_m.wk_cl_job AS job_m
            
        FROM wm_tb_personal p
        LEFT JOIN wm_tb_address ad ON p.ps_cl_idcard = ad.ad_cl_idcard
        LEFT JOIN provinces AS T2 ON ad.ad_cl_province = T2.pv_code
        LEFT JOIN amphures AS T3 ON ad.ad_cl_amphur = T3.ap_code
        LEFT JOIN districts AS T4 ON ad.ad_cl_tumbon = T4.tb_id
        LEFT JOIN wm_tb_phone ph ON p.ps_cl_idcard = ph.ph_cl_idcard
        LEFT JOIN wm_tb_gen_detail per ON per.gd_cl_aid = p.ps_cl_gen_detail
        LEFT JOIN police_name_bsdetective rec ON rec.aid_bsdp = p.ps_cl_recorder
        LEFT JOIN wm_tb_work wk ON p.ps_cl_idcard = wk.wk_cl_idcard
        
        LEFT JOIN wm_tb_personal f ON p.ps_cl_father_pid = f.ps_cl_idcard
        LEFT JOIN death_status d ON d.value = f.ps_cl_death
        LEFT JOIN wm_tb_address ad_f ON f.ps_cl_idcard = ad_f.ad_cl_idcard
        LEFT JOIN provinces AS T2_f ON ad_f.ad_cl_province = T2_f.pv_code
        LEFT JOIN amphures AS T3_f ON ad_f.ad_cl_amphur = T3_f.ap_code
        LEFT JOIN districts AS T4_f ON ad_f.ad_cl_tumbon = T4_f.tb_id
        LEFT JOIN wm_tb_phone ph_f ON f.ps_cl_idcard = ph_f.ph_cl_idcard
        LEFT JOIN wm_tb_work wk_f ON f.ps_cl_idcard = wk_f.wk_cl_idcard
        
        LEFT JOIN wm_tb_personal m ON p.ps_cl_mother_pid = m.ps_cl_idcard
        LEFT JOIN death_status d2 ON d2.value = m.ps_cl_death
        LEFT JOIN wm_tb_address ad_m ON m.ps_cl_idcard = ad_m.ad_cl_idcard
        LEFT JOIN provinces T2_m ON ad_m.ad_cl_province = T2_m.pv_code
        LEFT JOIN amphures T3_m ON ad_m.ad_cl_amphur = T3_m.ap_code
        LEFT JOIN districts T4_m ON ad_m.ad_cl_tumbon = T4_m.tb_id
        LEFT JOIN wm_tb_phone ph_m ON m.ps_cl_idcard = ph_m.ph_cl_idcard
        LEFT JOIN wm_tb_work wk_m ON m.ps_cl_idcard = wk_m.wk_cl_idcard
        
        ";

if($pcid != ''){
	$sql = $sql . " WHERE p.ps_cl_idcard = :pcid " ;
}
 
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':pcid', $pcid);
try{
    $stmt->execute();
}catch (PDOException $e) {
    echo '$sql Failed: '. $e->getMessage();
}
    $row = $stmt->fetch(PDO::FETCH_ASSOC);

// ชื่อของบุคคนั้น
if (!empty($row['p.ps_cl_idcard'])) {
    $people_name = $row['ps_cl_prefix'] .  $row['ps_cl_name'] . ' ' . $row['ps_cl_surname'] . ' (' . $row['ps_cl_nickname'] . ')';
} else {
    $people_name = $row['ps_cl_prefix'] .  $row['ps_cl_name'] . ' ' . $row['ps_cl_surname'];
}

// ข้อมูลชื่อ บิดา
if (!empty($row['f.ps_cl_idcard'])) {
    $f_name = $row['f_prefix'] .  $row['f_name'] . ' ' . $row['f_surname'] . ' (' . $row['f_nickname'] . ')';
} else {
    $f_name = $row['f_prefix'] .  $row['f_name'] . ' ' . $row['f_surname'];
}

// ข้อมูลชื่อ มารดา
if (!empty($row['m.ps_cl_idcard'])) {
    $m_name = $row['m_prefix'] .  $row['m_name'] . ' ' . $row['m_surname'] . ' (' . $row['m_nickname'] . ')';
} else {
    $m_name = $row['m_prefix'] .  $row['m_name'] . ' ' . $row['m_surname'];
}

// รูปภาพบุคคลนั้น
$people_image = $row["ps_cl_image"];
if($people_image != '') {
	$people_image = "<img src='{$people_image}' width='250px' style='border: 2px solid blue;'> ";
}

//อายุบุคคลนั้น
if($row["ps_cl_birthday2"] > 0){
        $age = get_personal_age_2( $row["ps_cl_birthday2"] );
        $age_display = $age . " ปี";
    }else{
        $age_display = '-';
    }

// อายุพ่อ
if($row['f_bd'] > 0){
    $age_f = get_personal_age_2($row['f_bd']);
    $age_display_f = $age_f . " ปี";
}else{
    $age_display_f = '-';
}

// อายุแม่
if($row['m_bd'] > 0){
    $age_m = get_personal_age_2($row['m_bd']);
    $age_display_m = $age_m . " ปี";
}else{
    $age_display_m = '-';
}

// วันบันทึกข้อมูล
if($row["ps_cl_date_rec"] > 0){
        $record_date = DateThaiFull( $row["ps_cl_date_rec"] );
    }else{
        $record_date = '';
    }

//ที่ทำงานของบุคคลนั้น
if (!empty($row['wk_status'])){
    $job = $row['job']. ' (' .$row['wk_status'] . ' ' . $row['d_out'] . ')';
}else{
    $job =$row['job'];
}

//ที่อยู่พ่อ
if(!empty($row['num_f'])){
    $adr_f = $row['num_f']. ' หมู่ที่ ' . $row['moo_f'] . ' ต.'. $row['tumbon_f'] . ' อ.' . $row['amphur_f'] . ' จ.' .  $row['province_f'];
}else{
    $adr_f = '';
}

//ที่อยู่แม่
if(!empty($row['num_m'])){
    $adr_m = $row['num_m']. ' หมู่ที่ ' . $row['moo_m'] . ' ต.'. $row['tumbon_m'] . ' อ.' . $row['amphur_m'] . ' จ.' .  $row['province_m'];
}else{
    $adr_m = '';
}

$ip_address = $_SERVER['REMOTE_ADDR'];
//Log the user activity
$action = "View Form Gen Person";
$data = $pcid;
log_activity($acc, $action, $data, $ip_address);


?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<title>Form General Personal</title>
<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
<style>
    table {
        border-collapse: collapse;
    }
    td {
        padding: 10px;
        width: auto;
    }
    .dotted-bottom {
        border-bottom: 1px dotted black;
    }
    .fbody{
        font-size: 18px;
        color: black;
    }
    .td{
        color: blue;
    }
</style>
</head>

<body>
    <form>
        <table width="99%" border="0" cellspacing="1" cellpadding="1">
  <tbody class="fbody">
    <tr nowrap>
      <td width="1%">&nbsp;</td>
      <td width="1%">&nbsp;</td>
      <td width="13%">&nbsp;</td>
      <td width="12" colspan="7">&nbsp;</td>
      <td width="1%">&nbsp;</td>
      <td width="1%">&nbsp;</td>
      <td width="1%">&nbsp;</td>
      <td width="1%">&nbsp;</td>
      <td width="1%">&nbsp;</td>
      <td width="1%">&nbsp;</td>
      <td width="1%">&nbsp;</td>
      <td width="1%">&nbsp;</td>
      <td width="1%">&nbsp;</td>
      <td width="1%">&nbsp;</td>
      <td width="5%">&nbsp;</td>
      <td width="7%">&nbsp;</td>
      <td width="18%">&nbsp;</td>
      <td width="1%">&nbsp;</td>
      <td width="2%">&nbsp;</td>
      <td width="5%">&nbsp;</td>
      <td width="3%">&nbsp;</td>
      <td width="3%">&nbsp;</td>
      <td width="4%">&nbsp;</td>
      <td>ลำดับที่</td>
      <td class="dotted-bottom" style="color: blue">&nbsp;</td>
      </tr>
    <tr nowrap>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td colspan="7">&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>ผนวก ค.</td>
      <td>&nbsp;</td>
      </tr>
    <tr nowrap>
      <td height="69" colspan="31" style="text-align: center; font-size: 24px">แบบประวัติบุคคลทั่วไป<br></td>
    </tr>
    <tr nowrap>
      <td height="47" colspan="31" style="text-align: center; font-size: 24px">&nbsp;</td>
      </tr>
    <tr nowrap>
      <td>&nbsp;</td>
      <td colspan="3" nowrap>ประเภทบุคคล :</td>
      <td colspan="24" nowrap class="dotted-bottom" style="color: blue">&nbsp;<?= $row['person_type']?></td>
      <td>&nbsp;</td>
      <td class="td" colspan="2" rowspan="7">&nbsp;<?= $people_image ?></td>
      </tr>
    <tr nowrap>
      <td height="54">&nbsp;</td>
      <td>&nbsp;</td>
      <td colspan="2">1. ชื่อ ชื่อสกุล : </td>
      <td colspan="24" class="dotted-bottom" style="color: blue">&nbsp;<?= $people_name ?>&nbsp;</td>
      <td>&nbsp;</td>
      </tr>
    <tr nowrap>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>อายุ</td>
      <td colspan="24" class="dotted-bottom" style="color: blue">&nbsp;<?= $age_display ?></td>
      <td>&nbsp;</td>
      </tr>
    <tr nowrap>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td colspan="2">2. อาชีพ</td>
      <td colspan="24" class="dotted-bottom" style="color: blue">&nbsp;<?= $row['career']?></td>
      <td>&nbsp;</td>
      </tr>
    <tr nowrap>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td colspan="11" nowrap>3. ชื่อสถานที่ทำงาน - ที่ตั้ง - หมายเลขโทรศัพท์</td>
      <td nowrap>&nbsp;</td>
      <td nowrap>&nbsp;</td>
      <td nowrap>&nbsp;</td>
      <td nowrap>&nbsp;</td>
      <td nowrap>&nbsp;</td>
      <td nowrap>&nbsp;</td>
      <td nowrap>&nbsp;</td>
      <td nowrap>&nbsp;</td>
      <td nowrap>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      </tr>
    <tr nowrap>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>ชื่อ </td>
      <td colspan="24" class="dotted-bottom" style="color: blue">&nbsp;<?= $job ?></td>
      <td>&nbsp;</td>
      </tr>
    <tr nowrap>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>ที่ทำงาน </td>
      <td colspan="24" class="dotted-bottom" style="color: blue">&nbsp;<?= $row['wk_adr'] ?></td>
      <td>&nbsp;</td>
      </tr>
    <tr nowrap>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>โทร. </td>
      <td colspan="24" class="dotted-bottom" style="color: blue">&nbsp;<?= $row['wk_phone']?></td>
      <td>&nbsp;</td>
      </tr>
      
    <tr nowrap>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td colspan="29">4. ที่อยู่</td>
      </tr>
    <tr nowrap>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>4.1 ปัจจุบัน</td>
      <td colspan="27" class="dotted-bottom" style="color: blue">&nbsp;<?= $row['num']?> หมู่ที่ <?= $row['moo']?> ต.<?= $row['tumbon']?> อ.<?= $row['amphur']?> จ.<?= $row['province']?></td>
      </tr>
    <tr nowrap>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>โทร.</td>
      <td colspan="27" class="dotted-bottom" style="color: blue">&nbsp;<?= $row['phone']?></td>
      </tr>
    <tr nowrap>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td nowrap>4.2 ภูมิลำเนาเดิม</td>
      <td colspan="27" class="dotted-bottom" style="color: blue">&nbsp;</td>
      </tr>
    <tr nowrap>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>โทร.</td>
      <td colspan="27" class="dotted-bottom" style="color: blue">&nbsp;</td>
      </tr>
    <tr nowrap>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td colspan="2">5. ชื่อบิดา</td>
      <td colspan="21" class="dotted-bottom" style="color: blue">&nbsp;<?= $f_name ?></td>
      <td>อายุ</td>
      <td colspan="2" class="dotted-bottom" style="color: blue">&nbsp;<?= $age_display_f ?></td>
      <td colspan="2">มีชีวิต/เสียชีวิต</td>
      <td class="dotted-bottom" style="color: blue">&nbsp;<?= $row['fa_death']?></td>
      <!--<td class="dotted-bottom" style="<?php if($row['fa_death'] == 2) echo 'color: red;'; ?>">&nbsp;<?= $row['fa_death']?></td>-->
    </tr>
    <tr nowrap>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>ที่อยู่</td>
      <td colspan="23" class="dotted-bottom" style="color: blue">&nbsp;<?= $adr_f ?></td>
      <td>โทร.</td>
      <td colspan="3" class="dotted-bottom" style="color: blue">&nbsp;<?= $row['phone_f'] ?></td>
      </tr>
    <tr nowrap>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>อาชีพ</td>
      <td colspan="18" class="dotted-bottom" style="color: blue">&nbsp;<?= $row['career_f'] ?></td>
      <td nowrap>ที่ทำงาน</td>
      <td colspan="8" class="dotted-bottom" style="color: blue">&nbsp;<?= $row['job_f'] ?></td>
      </tr>
    <tr nowrap>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td colspan="2">6. ชื่อมารดา</td>
      <td colspan="21" class="dotted-bottom" style="color: blue">&nbsp;<?= $m_name ?></td>
      <td>อายุ</td>
      <td colspan="2" class="dotted-bottom" style="color: blue">&nbsp;<?= $age_display_m ?></td>
      <td colspan="2">มีชีวิต/เสียชีวิต</td>
      <td class="dotted-bottom" style="color: blue">&nbsp;<?= $row['ma_death'] ?></td>
    </tr>
    <tr nowrap>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>ที่อยู่</td>
      <td colspan="23" class="dotted-bottom" style="color: blue">&nbsp;<?= $adr_m ?>&nbsp;</td>
      <td>โทร.</td>
      <td colspan="3" class="dotted-bottom" style="color: blue">&nbsp;<?= $row['phone_m'] ?></td>
      </tr>
    <tr nowrap>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>อาชีพ</td>
      <td colspan="18" class="dotted-bottom" style="color: blue">&nbsp;<?= $row['career_m'] ?></td>
      <td nowrap>ที่ทำงาน</td>
      <td colspan="8" class="dotted-bottom" style="color: blue">&nbsp;<?= $row['job_m'] ?></td>
      </tr>
      
<?php
////  ตารางสามี-ภรรยา
$sql_sp = "SELECT
            PS.*,
            PS.ps_cl_prefix AS Prefix_sp,
            PS.ps_cl_name AS Name_sp,
            PS.ps_cl_surname AS Surname_sp,
            PS.ps_cl_nickname AS Nickname_sp,
            PS.ps_cl_birthday2 AS Birthday_sp,
            PS.ps_cl_death AS sp_death,
            d3.meaning AS spouse_death,
            ad2.ad_cl_num AS num2, ad2.ad_cl_moo AS moo2,
            pv2.pv_name_th AS province2, ap2.ap_name_th AS amphur2, tb2.tb_name_th AS tumbon2,
            ph2.ph_cl_phone AS phone_sp,
            T2.*
        FROM 
            wm_tb_personal AS PS
            LEFT JOIN wm_tb_spouse AS T2 ON T2.sp_cl_idcard_spouse = PS.ps_cl_idcard
            LEFT JOIN wm_tb_address ad2 ON PS.ps_cl_idcard = ad2.ad_cl_idcard
            LEFT JOIN provinces AS pv2 ON ad2.ad_cl_province = pv2.pv_code
            LEFT JOIN amphures AS ap2 ON ad2.ad_cl_amphur = ap2.ap_code
            LEFT JOIN districts AS tb2 ON ad2.ad_cl_tumbon = tb2.tb_id
            LEFT JOIN wm_tb_phone ph2 ON PS.ps_cl_idcard = ph2.ph_cl_idcard
            LEFT JOIN death_status d3 ON d3.value = PS.ps_cl_death
            ";
if($pcid != ''){
	$sql_sp = $sql_sp . " WHERE T2.sp_cl_idcard = :pcid " ;
}
    $sql_sp = $sql_sp . "GROUP BY PS.ps_cl_idcard ";
        
    $stmt = $pdo->prepare($sql_sp);
    $stmt->bindParam(':pcid', $pcid);
try{
    $stmt->execute();
}catch (PDOException $e) {
    echo '$sql_sp Failed: '. $e->getMessage();
}

$no_sp = 7.1;
while($rowSp = $stmt->fetch(PDO::FETCH_ASSOC)){

if(!empty($rowSp["Birthday_sp"])){
//    if($rowSp["Birthday"] > 0){
        $age_display_sp = '';
        $age = get_personal_age_2( $rowSp["Birthday_sp"] );
        $age_display_sp = $age . " ปี";
    }else{
        $age_display_sp = ' - ';
    }

// ชื่อ ของสามีภรรยา
if (!empty($rowSp['Name_sp'])) {
    $sp_name = $rowSp['Prefix_sp'] .  $rowSp['Name_sp'] . ' ' . $rowSp['Surname_sp']. '  ' .$rowSp['Nickname_sp'] ;
} else {
    $sp_name = "";
}

// ที่อยู่ ของสามีภรรยา
if (!empty($rowSp['num2'])) {
    $sp_adr = $rowSp['num2'] . ' หมู่ที่ ' . $rowSp['moo2'] . ' ต.' . $rowSp['tumbon2'] . ' อ.' . $rowSp['amphur2'] . ' จ.' . $rowSp['province2'];
} else{
    $sp_adr = "";
}

// การมีชีวิต ของสามีภรรยา
if(!empty($rowSp['sp_death'])) {
    $death = $rowSp['spouse_death'];
}else{
    $death = '';
}

// โทรศัพท์ สามีภรรยา
if(!empty($rowSp['phone_sp'])) {
    $phone = $rowSp['phone_sp'];
}else{
    $phone = "";
}
  
?>
      
    <tr nowrap>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td colspan="2" nowrap><?=$no_sp?>. ชื่อสามี/ภรรยา</td>
      <td colspan="21" class="dotted-bottom" style="color: blue">&nbsp;<?= $sp_name ?></td>
      <td>อายุ</td>
      <td colspan="2" class="dotted-bottom" style="color: blue">&nbsp;<?= $age_display_sp ?></td>
      <td colspan="2">มีชีวิต/เสียชีวิต</td>
      <td class="dotted-bottom" style="color: blue">&nbsp;<?= $death ?></td>
    </tr>
    <tr nowrap>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>ที่อยู่</td>
      <td colspan="23" class="dotted-bottom" style="color: blue">&nbsp;<?= $sp_adr ?></td>
      <td>โทร.</td>
      <td colspan="3" class="dotted-bottom" style="color: blue">&nbsp;<?= $phone ?></td>
      </tr>
    <tr nowrap>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>อาชีพ</td>
      <td colspan="18" class="dotted-bottom" style="color: blue">&nbsp;</td>
      <td>ที่ทำงาน</td>
      <td colspan="8" class="dotted-bottom" style="color: blue">&nbsp;</td>
      </tr>
<?php
    $no_sp += 0.1;
}
?>
      
    <tr nowrap>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
    <td colspan="29">8. ชื่อบุตร</td>
        
<?php
//////// ข้อมูลลูก
$sql_cd2 = "SELECT PS3.*,
            PS3.ps_cl_prefix AS Prefix_cd, PS3.ps_cl_name AS Name_cd, PS3.ps_cl_surname AS Surname_cd, PS3.ps_cl_nickname AS Nickname_cd, PS3.ps_cl_birthday2 AS Birthday_cd, PS3.ps_cl_father AS Father_cd, PS3.ps_cl_father_pid AS Father_id_cd, PS3.ps_cl_mother AS Mother_cd, PS3.ps_cl_mother_pid AS Mother_id_cd,
            ph_cd.ph_cl_phone AS phone_cd,
            wk_cd.wk_cl_career AS career_cd, wk_cd.wk_cl_job AS job_cd,
            CD.* 
        FROM
            wm_tb_personal AS PS3
            LEFT JOIN wm_tb_child AS CD ON CD.cd_cl_idcard_child = PS3.ps_cl_idcard
            LEFT JOIN wm_tb_phone ph_cd ON PS3.ps_cl_idcard = ph_cd.ph_cl_idcard
            LEFT JOIN wm_tb_work wk_cd ON PS3.ps_cl_idcard = wk_cd.wk_cl_idcard
            ";
if($pcid != ''){
$sql_cd2 = $sql_cd2 . " WHERE CD.cd_cl_idcard = :pcid " . // ผู้ปกครอง
                "OR PS3.ps_cl_father_pid = :pcid " . // พ่อ
                "OR PS3.ps_cl_mother_pid = :pcid " . // แม่*/
                "ORDER BY PS3.ps_cl_birthday2 ASC
                ";
            //echo $sql_cd2;
            }

    $stmt = $pdo->prepare($sql_cd2);
    $stmt->bindParam(':pcid', $pcid);
try{
    $stmt->execute();
}catch (PDOException $e) {
    echo '$sql_cd2 Failed: '. $e->getMessage();
}

$no = 1;
while($rowCi = $stmt->fetch(PDO::FETCH_ASSOC)) {//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป

    // คำนวณอายุอัตโนมัติ 
    if ($rowCi["Birthday_cd"] > 0) {
        $age_cd = get_personal_age_2($rowCi["Birthday_cd"]);
        $age_display_cd = $age_cd . " ปี"; // add "years" to the age for display
    } else {
        $age_display_cd = '-';
    }

    if (!empty($rowCi['Nickname_cd'])) {
        $Nick = ' (' . $rowCi['Nickname_cd'] . ')';
    } else {
        $Nick = '-';
    }

    //ที่อยู่ลูก
    if(!empty($rowCi['num_cd'])){
        $adr_cd = $rowCi['num_cd']. ' หมู่ที่ ' . $rowCi['moo_cd'] . ' ต.'. $rowCi['tumbon_cd'] . ' อ.' . $rowCi['amphur_cd'] . ' จ.' .  $rowCi['province_cd'];
    }else{
        $adr_cd = '';
    }
//}     
    
?>   
        
      </tr>
    <tr nowrap>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td><?= $no ?>.</td>
      <td colspan="21" class="dotted-bottom" style="color: blue">&nbsp;<?=$rowCi["Prefix_cd"]?>&nbsp;<?=$rowCi["Name_cd"]?>&nbsp;&nbsp;<?=$rowCi["Surname_cd"]?></td>
      <td>อายุ</td>
      <td colspan="2" class="dotted-bottom" style="color: blue">&nbsp;<?= $age_display_cd ?></td>
      <td>อาชีพ</td>
      <td colspan="2" class="dotted-bottom" style="color: blue">&nbsp;<?= $rowCi['career_cd']?></td>
      </tr>
    <tr nowrap>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>ที่อยู่</td>
      <td colspan="27" class="dotted-bottom" style="color: blue">&nbsp;<?= $adr_cd ?></td>
    </tr>
    <tr nowrap>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>ที่ทำงาน</td>
      <td colspan="27" class="dotted-bottom" style="color: blue">&nbsp;<?= $rowCi['job_cd']?></td>
      </tr>
    <tr nowrap>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>โทร.</td>
      <td colspan="27" class="dotted-bottom" style="color: blue">&nbsp;<?= $rowCi['phone_cd'] ?></td>
      </tr>
<?php
    $no++;
}
//mysqli_close($conn);
          
?>
    <tr nowrap>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td colspan="29">9. ยานพาหนะที่ใช้ (ยี่ห้อ รุ่น สี หมายเลขทะเบียน)</td>
      </tr>
<?php

$sql_vc = "SELECT * FROM wm_tb_vehicle ";  //ตั้งตัวแปร sql เลือกฐานข้อมูลจากตารางที่ชื่อ wm_tb_personal
if($pcid != ''){
	$sql_vc = $sql_vc . " WHERE vc_cl_idcard = :pcid ";
}
    $stmt = $pdo->prepare($sql_vc);
    $stmt->bindParam(':pcid', $pcid);
try{
    $stmt->execute();
}catch (PDOException $e) {
    echo '$sql_vc Failed: '. $e->getMessage();
}

// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no_vc = 9.1;
while($rowVc = $stmt->fetch(PDO::FETCH_ASSOC))	//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
{
    if(!empty($rowVc['vc_cl_idcard'])){
        $vehicle = $rowVc["vc_cl_type"] . ' ยี่ห้อ ' . $rowVc["vc_cl_brand"] . ' รุ่น ' . $rowVc["vc_cl_model"] . ' สี ' . $rowVc["vc_cl_color"] . ' ทะเบียน ' . $rowVc["vc_cl_license"];
    }else{
        $vehicle = '';
    }
    
?>

    <tr nowrap>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td><? echo number_format($no_vc, 1); ?> </td>
      <td colspan="27" class="dotted-bottom" style="color: blue">&nbsp;<?= $vehicle ?></td>
      </tr>

<?php
		$no_vc += 0.1;
	}
?>
      
    <tr nowrap>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td colspan="29">10. บุคคลที่อยู่ร่วมกันในครอบครัว</td>
      </tr>
      
<?php
$sql_fm = "SELECT
            PS.*,
            PS.ps_cl_idcard AS idcard_fm,
            PS.ps_cl_prefix AS Prefix_fm, 
            PS.ps_cl_name AS Name_fm, 
            PS.ps_cl_surname AS Surname_fm, 
            PS.ps_cl_nickname AS Nickname_fm, 
            PS.ps_cl_birthday2 AS Birthday_fm, 
            FM.* 
        FROM
            wm_tb_personal AS PS " .
            "LEFT JOIN wm_tb_family AS FM ON FM.fm_cl_idcard_family = PS.ps_cl_idcard ";                        

if($pcid != ''){
	$sql_fm = $sql_fm . " WHERE FM.fm_cl_idcard = :pcid " ;
        /*. // ผู้ปกครอง
//                  " OR PS.ps_cl_idcard='$pcid' " .
                  " OR PS.ps_cl_father_pid='$pcid' " .
                  " OR PS.ps_cl_mother_pid='$pcid' " ; */
}
      
    $stmt = $pdo->prepare($sql_fm);
    $stmt->bindParam(':pcid', $pcid);
try{
    $stmt->execute();
}catch (PDOException $e) {
    echo '$sql_fm Failed: '. $e->getMessage();
}

// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no_fm = 1;
while($rowFm = $stmt->fetch(PDO::FETCH_ASSOC))	{//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
           
// คำนวณอายุอัตโนมัติ 
    //$age = get_personal_age($row["7ps_cl_birthday"]); // thai
    if($rowFm["Birthday_fm"] > 0){
        $age_fm = get_personal_age_2( $rowFm["Birthday_fm"] ); // eng 
        $age_display_fm = $age_fm . " ปี";
    }else{
        $age_display_fm = '-';
    }
    
if (!empty($rowFm["Nickname_fm"])) {
    $Nick_fm = ' (' . $rowFm["Nickname_fm"] . ')';
} else {
    $Nick_fm = '-';
}
    
// รูปแบบเลขบัตรประชาชน
$id_number = $rowFm['idcard_fm'];
$formatted_id = $id_number[0] . "-" . substr($id_number, 1, 4) . "-" . substr($id_number, 5, 5) . "-" . substr($id_number, 10, 2) . "-" . $id_number[12];
//echo $formatted_id;
   
?>
    <tr nowrap>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;<?= $no_fm ?>.</td>
      <td colspan="2">ชื่อ ชื่อสกุล</td>
      <td colspan="20" class="dotted-bottom" style="color: blue">&nbsp;<?=$rowFm["Prefix_fm"]?>&nbsp;<?=$rowFm["Name_fm"]?>&nbsp;&nbsp;<?=$rowFm["Surname_fm"]?>&nbsp;&nbsp;<?= $Nick_fm ?></td>
      <td>อายุ</td>
      <td class="td">&nbsp;</td>
      <td colspan="4" class="dotted-bottom" style="color: blue">&nbsp;<?= $age_display_fm ?></td>
      </tr>
    <tr nowrap>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td colspan="2">หมายเลขประจำตัวประชาชน</td>
      <td colspan="20" class="dotted-bottom" style="color: blue">&nbsp;<?= $formatted_id ?></td>
      <td>อาชีพ</td>
      <td class="td">&nbsp;</td>
      <td colspan="4" class="dotted-bottom" style="color: blue">&nbsp;</td>
      </tr>
    <tr nowrap>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>สถานที่ทำงาน</td>
      <td>&nbsp;</td>
      <td colspan="23" class="dotted-bottom" style="color: blue">&nbsp;</td>
      <td>โทร.</td>
      <td colspan="2" class="dotted-bottom" style="color: blue">&nbsp;</td>
      </tr>
    <tr nowrap>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>ที่อยู่ตามภูมิลำเนาเดิม</td>
      <td>&nbsp;</td>
      <td colspan="26" class="dotted-bottom" style="color: blue">&nbsp;</td>
      </tr>
<?php      
$no_fm++;
	}
?>
    
    <tr nowrap>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td colspan="2" nowrap>11. คนรับใช้ - ลูกจ้าง</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td class="td">&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr nowrap>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>1. ชื่อ - ชื่อสกุล</td>
      <td>&nbsp;</td>
      <td colspan="20" class="dotted-bottom" style="color: blue">&nbsp;</td>
      <td>อายุ</td>
      <td class="td">&nbsp;</td>
      <td colspan="4" class="dotted-bottom" style="color: blue">&nbsp;</td>
      </tr>
    <tr nowrap>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td colspan="2" nowrap>หมายเลขประจำตัวประชาชน</td>
      <td colspan="26" class="dotted-bottom" style="color: blue">&nbsp;</td>
      </tr>
    <tr nowrap>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>ที่อยู่ตามภูมิลำเนาเดิม</td>
      <td>&nbsp;</td>
      <td colspan="26" class="dotted-bottom" style="color: blue">&nbsp;</td>
      </tr>
    <tr nowrap>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>2. ชื่อ - ชื่อสกุล</td>
      <td>&nbsp;</td>
      <td colspan="20" class="dotted-bottom" style="color: blue">&nbsp;</td>
      <td>อายุ</td>
      <td class="td">&nbsp;</td>
      <td colspan="4" class="dotted-bottom" style="color: blue">&nbsp;</td>
      </tr>
    <tr nowrap>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td colspan="2">หมายเลขประจำตัวประชาชน</td>
      <td colspan="26" class="dotted-bottom" style="color: blue">&nbsp;</td>
      </tr>
    <tr nowrap>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>ที่อยู่ตามภูมิลำเนาเดิม</td>
      <td>&nbsp;</td>
      <td colspan="26" class="dotted-bottom" style="color: blue">&nbsp;</td>
      </tr>
    <tr nowrap>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td colspan="2">12. อื่น ๆ ที่น่าสนใจ</td>
    <tr nowrap>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td colspan="29" class="dotted-bottom" style="color: blue">&nbsp;</td>
    </tr>
    <tr>
      <td>&nbsp;</td>
      <td colspan="26" class="dotted-bottom" style="color: blue"></td>
    </tr>
    <tr nowrap>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td colspan="29" class="dotted-bottom" style="color: blue">&nbsp;</td>
    </tr>
    <tr>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td colspan="7">&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td colspan="7" >&nbsp;</td>
      <td colspan="2">ผู้จัดทำ</td>
      <td colspan="13" class="dotted-bottom" style="color: blue">&nbsp;<?= $row['recorder']?>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td colspan="7">&nbsp;</td>
      <td colspan="2">ตำแหน่ง</td>
      <td colspan="13" class="dotted-bottom" style="color: blue">&nbsp;<?= $row['position']?>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td colspan="7">&nbsp;</td>
      <td colspan="2" nowrap>วันเดือนปีที่จัดทำ</td>
      <td colspan="13" class="dotted-bottom" style="color: blue">&nbsp;<?= $record_date ?>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td colspan="7">&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td colspan="7">&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td colspan="7">&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td colspan="7">&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;
        <!--<button href="https://invest.watchman1.com/WatchmanData/Show_All.php?pcid=<?=$pcid?>" class="btn btn-primary mb-4" >ย้อนกลับ</button>--></td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td colspan="7">&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td colspan="7">&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td colspan="7">&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td colspan="7">&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td colspan="7">&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
      </tr>
  </tbody>
</table>
</form>
</body>
</html>
