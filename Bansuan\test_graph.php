<?php
include '../Condb.php';
include '../users.inc.php';

// Query the database for the data you want to compare
$sql = "SELECT category, tumbon, COUNT(*) AS count FROM test_graph_table GROUP BY category";
$result = $conn->query($sql);

// Initialize an array to store the data
$data = array();

// Loop through the results and add each row to the data array
while ($row = $result->fetch_assoc()) {
    $data[$row['category']] = $row['count'];
    $data[$row['tumbon']] = $row['count'];
}

// Set the width and height of the graph
$width = 800;
$height = 500;

// Create a new image with the specified width and height
$image = imagecreatetruecolor($width, $height);

// Set the background color of the image to white
$white = imagecolorallocate($image, 255, 255, 255);
imagefill($image, 0, 0, $white);

// Set the colors for the bars and the text
$bar_color = imagecolorallocate($image, 0, 0, 255);
$text_color = imagecolorallocate($image, 0, 0, 0);

// Calculate the maximum value in the data array
$max_value = max($data);

// Calculate the width of each bar
$bar_width = $width / count($data);

// Loop through the data and draw a bar for each value
foreach ($data as $category => $count) {
    // Calculate the height of the bar
    $bar_height = ($count / $max_value) * $height;

    // Calculate the position of the bar
    $x1 = $bar_width * array_search($category, array_keys($data));
    $y1 = $height - $bar_height;
    $x2 = $x1 + $bar_width - 20;
    $y2 = $height - 25;

    // Draw the bar
    imagefilledrectangle($image, $x1, $y1, $x2, $y2, $bar_color);

    // Draw the text for the category
    $text_x = $x1 + ($bar_width / 2) - 25;
    $text_y = $height - 20;
    imagestring($image, 5, $text_x, $text_y, $category, $text_color);

    // Draw the text for the count
    $text_x = $x1 + ($bar_width / 2) - 20;
    $text_y = $y1 - 0;
    imagestring($image, 5, $text_x, $text_y, $count, $text_color);
}

// Output the image
header("Content-type: image/png");
imagepng($image);

// Clean up
imagedestroy($image);
$conn->close();

?>