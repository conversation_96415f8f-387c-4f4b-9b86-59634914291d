<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';
include '../Head_image.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

//get month from select or current month
$month = isset($_GET['month']) ? $_GET['month'] : date("m");
$year = isset($_GET['year']) ? $_GET['year'] : date("Y");
//$station = isset($_GET['station']) ? $_GET['station'] : 6707;
//$station = 6707;   // สภ.บ้านสวน

//--------------------
// last modify 2023-01-20
$limite_date = strtotime("$year-09-30"); // วันสุดท้ายของช่วง เป็น 30 ก.ย.ของปีนั้น ๆ
$current_date = time(); // now()   กำหนดวันปัจจุบัน
$check_date = strtotime("$year-10-01");   // ตัวแปร $check_date เป็นวันตั้งแต่ 1 ต.ค.ของปีนั้น ๆ เป็นต้นไป
$select_date = strtotime("$year-$month-01"); // เป็นตัวแปร สำหรับเลือก ของเดือนนั้น ๆ

//ถ้าเวลายังไม่สิ้นสุด 30 กย. ของปี
if($current_date < $check_date)   // ถ้าวันปัจจุบัน มีค่าน้อยกว่า วันที่ 1 ต.ค.
{
    $limite_date = strtotime(($year-1) . "-09-30");  // ตัวแปร $limite_date จะมีค่าเท่ากับ 30 ก.ย.ของปี ที่ลบค่าไป 1
    $check_date = strtotime(($year-1) . "-10-01");   // ตัวแปร $check_date จะมีค่าเท่ากับ 1 ต.ค. ของปี ที่ลบค่าไป 1
}

$m1 = '';
$m2 = '';
$m3 = '';
$m4 = '';
$m5 = '';
$m6 = '';
$m7 = '';
$m8 = '';
$m9 = '';
$m10 = '';
$m11 = '';
$m12 = '';
switch($month)
{
    case "01": $m1 = "selected"; break;
    case "02": $m2 = "selected"; break;
    case "03": $m3 = "selected"; break;
    case "04": $m4 = "selected"; break;
    case "05": $m5 = "selected"; break;
    case "06": $m6 = "selected"; break;
    case "07": $m7 = "selected"; break;
    case "08": $m8 = "selected"; break;
    case "09": $m9 = "selected"; break;
    case "10": $m10 = "selected"; break;
    case "11": $m11 = "selected"; break;
    case "12": $m12 = "selected"; break;
}
$current_month = $global_thai_month[0+$month];
$y2 = $year+543;

// แปลงเวลา เป็นปีไทย
function date_2_Thai( $date )
{
    global $global_thaimonth;
    $strYear = date("Y", $date) + 543;
    $strMonth = date("n",$date) - 1;
    $strDay = date("j", $date);
    $strMonthThai = $global_thaimonth[$strMonth];
    return "$strDay $strMonthThai $strYear";
}

//สร้างฟังก์ชั่น ย้อนหลัง arrest_summary_count มาใช้งาน เป็นยอดรวมการจับกุม ย้อนหลัง 1 เดือน
// dateM - datetime var
function arrest_summary_count($date)
{
    global $pdo, $station;

    $m1 = date("Y-m-1", $date);
    $m2 = date("Y-m-t", $date);

    $sql_s = "SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial` = :provincial AND `wr_cl_status` IN (2,4,5,6)";

    $stmt = $pdo->prepare($sql_s);
    $stmt->bindParam(':m1', $m1);
    $stmt->bindParam(':m2', $m2);
    $stmt->bindParam(':provincial', $provincial);
    $stmt->execute();

    $row_s = $stmt->fetch(PDO::FETCH_NUM);
    return $row_s[0];
}

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<base target="_top">
<title>รายงานหมายจับ ภ.จว.สุโขทัย</title>
<link rel="stylesheet" href="/bootstrap/css/bootstrap-grid.css">
<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<style type="text/css">
body{
    font-size:12px; 
}
.textAlignVer{
    display:block;
    filter: flipv fliph;
    -webkit-transform: rotate(-90deg); 
    -moz-transform: rotate(-90deg); 
    transform: rotate(-90deg); 
    position:relative;
    width:20px;
    white-space:nowrap;
    font-size:16px;
    margin-bottom:10px;
}
.box !im{
    border: black;
    border-top-color: black !important;
    border-bottom-color: black !important;
    border-left-color: black !important;
    border-right-color: black !important;

}
</style>
    
</head>

<body>
    
<div class="container-fluid" align="left">
<div class=" h3 text-center  alert alert-success mb-4 mt-4 " role="alert" >กิจกรรม 7 หมายจับค้างเก่า <?= $name_provincial ?></div>
<div>
    <table width="80%" border="0" cellspacing="1" cellpadding="1">
      <tbody>
        <tr>
          <td width="21%">&nbsp;<a href="/WM/index.php?rnd=<?= rand(); ?>&page=report" class="btn btn-warning btn-lg mb-4" >ย้อนกลับ</a><!--&nbsp;&nbsp;<a href="Add_mission.php" class="btn btn-success btn-lg mb-4" >เพิ่มข้อมูล</a>--></td>
          <td width="17%">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<b style="font-size: 25px">เลือกช่วงข้อมูล</b></td>    
          <td width="10%"><label>เลือกปี </label>
            <select id="mi_cl_year" name="mi_cl_year" class="form-select col-8 d-lg-inline" style="background-color: khaki; font-size: 16px" onChange="dates_change()">
              <option value="">เลือก</option>
              <?php
                $cur_y = date("Y") + 2;
                for($y=2020; $y<$cur_y; $y++) {
                    $sel = ($y == $year) ? "selected" : '';
                    echo "<option value='$y' $sel> ". ($y + 543) ." </option>";
                }
            ?>
          </select></td>
          <td width="3%"></td>
          <td width="16%"><label>เดือน</label>
            <select id="mi_cl_month" name="mi_cl_month" class="form-select col-3 d-lg-inline" style="background-color: khaki; font-size: 16px" onChange="dates_change()">
              <option value="">เลือก</option>
              <option <?= $m1 ?> value="01">มกราคม</option>
              <option <?= $m2 ?> value="02">กุมภาพันธ์</option>
              <option <?= $m3 ?> value="03">มีนาคม</option>
              <option <?= $m4 ?> value="04">เมษายน</option>
              <option <?= $m5 ?> value="05">พฤษภาคม</option>
              <option <?= $m6 ?> value="06">มิถุนายน</option>
              <option <?= $m7 ?> value="07">กรกฎาคม</option>
              <option <?= $m8 ?> value="08">สิงหาคม</option>
              <option <?= $m9 ?> value="09">กันยายน</option>
              <option <?= $m10 ?> value="10">ตุลาคม</option>
              <option <?= $m11 ?> value="11">พฤศจิกายน</option>
              <option <?= $m12 ?> value="12">ธันวาคม</option>
          </select></td>
          <td width="29%">&nbsp;</td>
          <td width="4%">&nbsp;</td>
        </tr>
      </tbody>
    </table><br>
</div>
<h3 align="center">ข้อมูลหมายจับค้างเก่า  <?= $name_provincial ?> </h3>
<h3 align="center">ประจำเดือน&nbsp; <?= $current_month ?> &nbsp;<?= $y2 ?> &nbsp;</h3><br>
<table width="90%" border="1" cellspacing="1" cellpadding="1" class="table table-bordered">
  <tbody>
         <tr  height=28 style='height:21.0pt'>
           <td rowspan="3" bordercolor="#0C0B0B"  style='border:solid #000000;
          height:21.0pt; background-color:aqua '>&nbsp;หน่วย</td>
           <td height="28" colspan="4" bordercolor="#000000"  style='border:solid #000000; background-color:#DDDDDD '>ยอดหมายจับ (ยกมา)</td>
           <td height=28 colspan=12 bordercolor="#0C0B0B"  style='border:solid #000000;
          height:21.0pt; background-color:aqua '><span style="height:21.0pt; background-color:aqua ">หมายจับทั่วไปของ  <?= $name_provincial ?>  ตั้งแต่ 11 ต.ค.45 - <?= date_2_Thai($limite_date) ?></span></td>
          <td colspan=14  style='border:solid #000000; background-color:#ADFDB6'>หมายจับทั่วไปของ
           <?= $name_provincial ?>  ตั้งแต่ <?= date_2_Thai($check_date) ?>  - ปัจจุบัน</td>
          <td colspan=3  style='border:solid #000000; background-color: yellow; text-align: center'>รวม (ในเดือนนั้น)</td>
         </tr>
         <tr  height=28 style='height:21.0pt'>
           <td rowspan=2  bordercolor="#000000"  style='border:solid #000000; background-color:#DDDDDD '><p>ยอดหมายจับ (01)</p></td>
           <td rowspan=2  bordercolor="#000000"  style='border:solid #000000; background-color:#DDDDDD '><p>มีคุณภาพ  (02)</p></td>
           <td rowspan=2  bordercolor="#000000"  style='border:solid #000000; background-color:#DDDDDD '>อายัด (03)</td>
          <td rowspan=2  bordercolor="#000000"  style='border:solid #000000; background-color:#51ADFC '><p>ไม่มีคุณภาพ (04)</p></td>
          <td rowspan=2  width=104 style='border:solid #000000; width:78pt; background-color:aqua ' bordercolor="#0C0B0B">หมายคงเหลือทั้งหมด (1)</td>
          <td colspan="2" align=left  style='border:solid #000000; background-color:aqua '>คุณภาพของหมาย</td>
          <td colspan=6  style='border:solid #000000; background-color:#FA757F '><p>การจำหน่ายหมายจับ
            (เพิ่มเติม)
             (<?= $current_month ?>&nbsp;<?= $y2 ?>)</td>
          <td colspan=2  style='border:solid #000000;
          border-left:none; background-color:aqua '>จำนวนหมายคงเหลือ</td>
          <td rowspan=2  width=93 style='border:solid #000000;
          border-top:none;width:70pt; background-color:aqua '>ร้อยละผลการจับกุมจากหมายจับทั้งหมด (12)</td>
          <td rowspan=2  width=87 style='border:solid #000000;
          border-top:none;width:65pt; background-color:#ADFDB6'>หมายจับคงเหลือทั้งหมดจากการประชุมที่ผ่านมา (13)</td>
          <td rowspan=2  width=81 style='border:solid #000000;
          border-top:none;width:61pt; background-color:#ADFDB6'>จำนวนหมายจับใหม่ (เพิ่มเติม) (14)</td>
          <td rowspan=2  width=65 style='border:solid #000000;
          border-top:none;width:49pt; background-color:#ADFDB6'>รวมหมายจับทั้งหมด (15)</td>
          <td colspan="2" align=left class=xl961464 style='border:solid #000000; background-color:#ADFDB6'>คุณภาพของหมาย</td>
          <td colspan=6  style='border:solid #000000; background-color:#FA757F'>การจำหน่ายหมายจับ
            (เพิ่มเติม)<br>
            (<?= $current_month ?>&nbsp;<?= $y2 ?>)</td>
          <td colspan=2  style='border:solid #000000;
          border-left:none; background-color:#ADFDB6'>จำนวนหมายคงเหลือ</td>
          <td rowspan=2  width=93 style='border:solid #000000;
          border-top:none;width:70pt; background-color:#ADFDB6'>ร้อยละผลการจับกุมจากหมายจับทั้งหมด (26)</td>
          <td rowspan=2  width=70 style='border:solid #000000;width:53pt; background-color: #51ADFC'>รวมหมายจับทั้งหมด
          1 ต.ค.45 - ปัจจุบัน (27)</td>
          <td rowspan=2  width=70 style='border:solid #000000;width:53pt; background-color: #FA757F'>รวมจำหน่ายหมายทั้งหมด (28)</td>
          <td rowspan=2  width=70 style='border:solid #000000;width:53pt; background-color: yellow'>ร้อยละผลการจับกุมจากหมายจับทั้งหมด</td>
         </tr>
         <tr  height=133>
          <td style='border:solid #000000; background-color:#7FFA75 ' align="center" valign="bottom"><span class="textAlignVer">มีคุณภาพ (2)</td>
             <td  style='border:solid #000000; background-color:aqua ' align="center" valign="bottom"><span class="textAlignVer">ไม่มีคุณภาพ (3)</td>
          <td  style='border:solid #000000; background-color:aqua '>จับกุม (4)</td>
          <td  style='border:solid #000000; background-color:aqua '>ขาดอายุความ (5)</td>
          <td  style='border:solid #000000; background-color:aqua '>ตาย (6)</td>
          <td  style='border:solid #000000; background-color:aqua '>อายัด (7)</td>
          <td  style='border:solid #000000; background-color:aqua '>อื่น ๆ (8)</td>
          <td  style='border:solid #000000; background-color:#FA757F '>รวม (9)</td>
          <td  style='border:solid #000000; background-color:#51ADFC ' align="center" valign="bottom"><span class="textAlignVer">มีคุณภาพ (10)</td>
          <td  style='border:solid #000000; background-color:aqua ' align="center" valign="bottom"><span class="textAlignVer">ไม่มีคุณภาพ (11)</td>
          <td  style='border:solid #000000; background-color:#7FFA75' align="center" valign="bottom"><span class="textAlignVer">มีคุณภาพ (16)</td>
          <td  style='border:solid #000000; background-color:#ADFDB6' align="center" valign="bottom"><span class="textAlignVer">ไม่มีคุณภาพ (17)</td>
          <td  style='border:solid #000000; background-color:#ADFDB6'>จับกุม (18)</td>
          <td  style='border:solid #000000; background-color:#ADFDB6'>ขาดอายุความ (19)</td>
          <td  style='border:solid #000000; background-color:#ADFDB6'>ตาย (20)</td>
          <td  style='border:solid #000000; background-color:#ADFDB6'>อายัด (21)</td>
          <td  style='border:solid #000000; background-color:#ADFDB6'>อื่น ๆ (22)</td>
          <td  style='border:solid #000000; background-color:#FA757F'>รวม (23)</td>
          <td  style='border:solid #000000; background-color:#51ADFC' align="center" valign="bottom"><span class="textAlignVer">มีคุณภาพ (24)</td>
          <td  style='border:solid #000000; background-color:#ADFDB6' align="center" valign="bottom"><span class="textAlignVer">ไม่มีคุณภาพ (25)</td>
         </tr>
<?php                                    
    
//$date1 = date("Y", $limite_date) . "-$month-01";        //"$year-$month-01";
$day1 = date("t", $limite_date);
$date2 = "$year-$month-$day1"; // <<<
                      
// ยอดหมายจับเก่า ที่ออกระหว่าง ปี 45 - 30 ก.ย.2565 ทั้งหมด
$date_prev = date("Y-m-d", $limite_date);
$date_next = date("Y-m-01", $check_date); // ตั้งแต่ 1 ตค เปนต้นไป
    $m1 = date("Y-m-01", $select_date);       // m1 = เดือนปัจจุบันที่เลือก
    $m2 = date("Y-m-t", $select_date);        // << end date of current month ได้ค่าวันสุดท้ายของเดือนปัจจุบันที่เลือกมา
                      
//ฟังก์ชั่น เลือกเดือน ย้อนหลัง 1 เดือน ได้ตัวแปร $sum2 ไปใช้งาน
$sum = arrest_summary_count( $select_date );
$prev_y = $year;
$prev_m = $month - 1;
if($prev_m < 0) {
    $prev_m = 12;
    $prev_y--;
}
$mp = strtotime("$prev_y-$prev_m-01");
$sum2 = arrest_summary_count( $mp );  // ยอดย้อนหลังไป 1 เดือน
     
					  
$sql = "SELECT " .
	//////// ยอดรวม ของ ภ.จว.สุโขทัย  ///////////
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `wr_cl_status` IN (1,3)) AS All_01, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
    ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `wr_cl_status` IN (1,3)) AS Old_All_1, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
    ///// ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7 , " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`=6 ) AS Old_other_8, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`IN(2,4,5,6)) AS S_9, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     /////  หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `provincial`=:provincial AND `wr_cl_status` IN (1,3)) AS New_BF_13 , ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND (`wr_cl_status` IN (1,3,2))) AS New_M_14 , ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `provincial`=:provincial AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16 , ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `provincial`=:provincial AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17 , ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18 , ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`=4) AS New_Exp_19 , ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`=5) AS New_Dead_20 , " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`=3) AS New_Hold_21 , " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`=6) AS New_other_22 , " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`IN(2,4,5,6)) AS S_23, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
	
	////////// สภ.กงไกรลาศ ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6701 AND `wr_cl_status` IN (1,3)) AS All_01_kkl, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6701 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_kkl, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6701 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_kkl, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6701 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_kkl, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6701 AND `wr_cl_status` IN (1,3)) AS Old_All_1_kkl, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6701 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_kkl, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6701 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_kkl, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     ///// ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6701 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_kkl, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6701 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_kkl, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6701 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_kkl, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6701 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_kkl, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6701 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_kkl, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6701 AND `wr_cl_status`=6 ) AS Old_other_8_kkl, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6701 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_kkl, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6701 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_kkl, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     /////  หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6701 AND `wr_cl_status` IN (1,3)) AS New_BF_13_kkl, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6701 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_kkl, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6701 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_kkl, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6701 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_kkl, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6701 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_kkl, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6701 AND `wr_cl_status`=4) AS New_Exp_19_kkl, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6701 AND `wr_cl_status`=5) AS New_Dead_20_kkl, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6701 AND `wr_cl_status`=3) AS New_Hold_21_kkl, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6701 AND `wr_cl_status`=6) AS New_other_22_kkl, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6701 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_kkl, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6701 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_kkl, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
	
	////////// สภ.คีรีมาศ ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6702 AND `wr_cl_status` IN (1,3)) AS All_01_krm, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6702 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_krm, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6702 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_krm, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6702 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_krm, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6702 AND `wr_cl_status` IN (1,3)) AS Old_All_1_krm, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6702 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_krm, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6702 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_krm, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6702 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_krm, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6702 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_krm, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6702 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_krm, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6702 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_krm, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6702 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_krm, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6702 AND `wr_cl_status`=6 ) AS Old_other_8_krm, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6702 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_krm, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6702 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_krm, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     /////  หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6702 AND `wr_cl_status` IN (1,3)) AS New_BF_13_krm, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6702 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_krm, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6702 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_krm, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6702 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_krm, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6702 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_krm, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6702 AND `wr_cl_status`=4) AS New_Exp_19_krm, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6702 AND `wr_cl_status`=5) AS New_Dead_20_krm, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6702 AND `wr_cl_status`=3) AS New_Hold_21_krm, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6702 AND `wr_cl_status`=6) AS New_other_22_krm, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6702 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_krm, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6702 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_krm, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
	
	////////// สภ.ทุ่งเสลี่ยม ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6703 AND `wr_cl_status` IN (1,3)) AS All_01_tsl, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6703 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_tsl, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6703 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_tsl, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6703 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_tsl, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6703 AND `wr_cl_status` IN (1,3)) AS Old_All_1_tsl, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6703 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_tsl, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6703 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_tsl, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6703 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_tsl, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6703 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_tsl, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6703 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_tsl, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6703 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_tsl, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6703 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_tsl, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6703 AND `wr_cl_status`=6 ) AS Old_other_8_tsl, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6703 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_tsl, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6703 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_tsl, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
    //////หมายจับ ใหม่  //////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6703 AND `wr_cl_status` IN (1,3)) AS New_BF_13_tsl, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6703 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_tsl, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6703 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_tsl, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6703 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_tsl, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
    ////// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก  //////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6703 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_tsl, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6703 AND `wr_cl_status`=4) AS New_Exp_19_tsl, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6703 AND `wr_cl_status`=5) AS New_Dead_20_tsl, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6703 AND `wr_cl_status`=3) AS New_Hold_21_tsl, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6703 AND `wr_cl_status`=6) AS New_other_22_tsl, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6703 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_tsl, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6703 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_tsl, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  6703
	
	////////// สภ.บ้านแก่ง ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6704 AND `wr_cl_status` IN (1,3)) AS All_01_bng, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6704 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_bng, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6704 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_bng, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6704 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_bng, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6704 AND `wr_cl_status` IN (1,3)) AS Old_All_1_bng, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6704 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_bng, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6704 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_bng, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6704 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_bng, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6704 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_bng, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6704 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_bng, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6704 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_bng, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6704 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_bng, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6704 AND `wr_cl_status`=6 ) AS Old_other_8_bng, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6704 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_bng, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6704 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_bng, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     /////  หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6704 AND `wr_cl_status` IN (1,3)) AS New_BF_13_bng, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6704 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_bng, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6704 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_bng, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6704 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_bng, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6704 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_bng, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6704 AND `wr_cl_status`=4) AS New_Exp_19_bng, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6704 AND `wr_cl_status`=5) AS New_Dead_20_bng, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6704 AND `wr_cl_status`=3) AS New_Hold_21_bng, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6704 AND `wr_cl_status`=6) AS New_other_22_bng, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6704 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_bng, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6704 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_bng, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  6704 
	
	////////// สภ.บ้านด่านลานหอย ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6705 AND `wr_cl_status` IN (1,3)) AS All_01_bdh, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6705 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_bdh, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6705 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_bdh, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6705 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_bdh, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
    /////////////////////////////// หมายจับเก่า  ///////////////////////////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6705 AND `wr_cl_status` IN (1,3)) AS Old_All_1_bdh, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6705 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_bdh, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6705 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_bdh, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6705 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_bdh, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6705 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_bdh, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6705 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_bdh, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6705 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_bdh, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6705 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_bdh, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6705 AND `wr_cl_status`=6 ) AS Old_other_8_bdh, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6705 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_bdh, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6705 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_bdh, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     /////  หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6705 AND `wr_cl_status` IN (1,3)) AS New_BF_13_bdh, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6705 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_bdh, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6705 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_bdh, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6705 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_bdh, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6705 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_bdh, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6705 AND `wr_cl_status`=4) AS New_Exp_19_bdh, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6705 AND `wr_cl_status`=5) AS New_Dead_20_bdh, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6705 AND `wr_cl_status`=3) AS New_Hold_21_bdh, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6705 AND `wr_cl_status`=6) AS New_other_22_bdh, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6705 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_bdh, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6705 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_bdh, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  6705
	
	////////// สภ.บ้านไร่ ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6706 AND `wr_cl_status` IN (1,3)) AS All_01_bnr, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6706 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_bnr, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6706 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_bnr, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6706 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_bnr, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6706 AND `wr_cl_status` IN (1,3)) AS Old_All_1_bnr, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6706 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_bnr, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6706 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_bnr, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6706 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_bnr, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6706 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_bnr, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6706 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_bnr, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6706 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_bnr, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6706 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_bnr, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6706 AND `wr_cl_status`=6 ) AS Old_other_8_bnr, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6706 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_bnr, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6706 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_bnr, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     ///// หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6706 AND `wr_cl_status` IN (1,3)) AS New_BF_13_bnr, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6706 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_bnr, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6706 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_bnr, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6706 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_bnr, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6706 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_bnr, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6706 AND `wr_cl_status`=4) AS New_Exp_19_bnr, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6706 AND `wr_cl_status`=5) AS New_Dead_20_bnr, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6706 AND `wr_cl_status`=3) AS New_Hold_21_bnr, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6706 AND `wr_cl_status`=6) AS New_other_22_bnr, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6706 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_bnr, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6706 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_bnr, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  6706
	
	////////// สภ.บ้านสวน ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6707 AND `wr_cl_status` IN (1,3)) AS All_01_bs, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6707 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_bs, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6707 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_bs, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6707 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_bs, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6707 AND `wr_cl_status` IN (1,3)) AS Old_All_1_bs, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6707 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_bs, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6707 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_bs, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6707 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_bs, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6707 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_bs, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6707 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_bs, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6707 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_bs, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6707 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_bs, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6707 AND `wr_cl_status`=6 ) AS Old_other_8_bs, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6707 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_bs, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6707 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_bs, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     ///// หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6707 AND `wr_cl_status` IN (1,3)) AS New_BF_13_bs, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6707 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_bs, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6707 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_bs, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6707 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_bs, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6707 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_bs, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6707 AND `wr_cl_status`=4) AS New_Exp_19_bs, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6707 AND `wr_cl_status`=5) AS New_Dead_20_bs, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6707 AND `wr_cl_status`=3) AS New_Hold_21_bs, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6707 AND `wr_cl_status`=6) AS New_other_22_bs, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6707 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_bs, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6707 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_bs, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  6707
	
	////////// สภ.เมืองเก่า ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6708 AND `wr_cl_status` IN (1,3)) AS All_01_mgk, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6708 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_mgk, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6708 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_mgk, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6708 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_mgk, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6708 AND `wr_cl_status` IN (1,3)) AS Old_All_1_mgk, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6708 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_mgk, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6708 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_mgk, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6708 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_mgk, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6708 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_mgk, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6708 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_mgk, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6708 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_mgk, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6708 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_mgk, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6708 AND `wr_cl_status`=6 ) AS Old_other_8_mgk, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6708 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_mgk, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6708 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_mgk, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     ///// หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6708 AND `wr_cl_status` IN (1,3)) AS New_BF_13_mgk, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6708 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_mgk, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6708 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_mgk, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6708 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_mgk, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6708 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_mgk, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6708 AND `wr_cl_status`=4) AS New_Exp_19_mgk, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6708 AND `wr_cl_status`=5) AS New_Dead_20_mgk, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6708 AND `wr_cl_status`=3) AS New_Hold_21_mgk, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6708 AND `wr_cl_status`=6) AS New_other_22_mgk, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6708 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_mgk, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6708 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_mgk, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  6708
	
	////////// สภ.เมืองสุโขทัย ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6709 AND `wr_cl_status` IN (1,3)) AS All_01_mst, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6709 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_mst, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6709 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_mst, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6709 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_mst, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6709 AND `wr_cl_status` IN (1,3)) AS Old_All_1_mst, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6709 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_mst, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6709 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_mst, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6709 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_mst, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6709 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_mst, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6709 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_mst, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6709 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_mst, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6709 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_mst, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6709 AND `wr_cl_status`=6 ) AS Old_other_8_mst, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6709 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_mst, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6709 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_mst, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     ///// หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6709 AND `wr_cl_status` IN (1,3)) AS New_BF_13_mst, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6709 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_mst, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6709 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_mst, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6709 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_mst, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6709 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_mst, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6709 AND `wr_cl_status`=4) AS New_Exp_19_mst, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6709 AND `wr_cl_status`=5) AS New_Dead_20_mst, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6709 AND `wr_cl_status`=3) AS New_Hold_21_mst, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6709 AND `wr_cl_status`=6) AS New_other_22_mst, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6709 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_mst, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6709 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_mst, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  6709
	
	////////// สภ.ศรีนคร ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6710 AND `wr_cl_status` IN (1,3)) AS All_01_snk, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6710 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_snk, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6710 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_snk, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6710 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_snk, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6710 AND `wr_cl_status` IN (1,3)) AS Old_All_1_snk, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6710 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_snk, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6710 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_snk, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6710 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_snk, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6710 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_snk, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6710 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_snk, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6710 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_snk, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6710 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_snk, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6710 AND `wr_cl_status`=6 ) AS Old_other_8_snk, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6710 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_snk, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6710 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_snk, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     ///// หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6710 AND `wr_cl_status` IN (1,3)) AS New_BF_13_snk, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6710 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_snk, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6710 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_snk, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6710 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_snk, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6710 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_snk, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6710 AND `wr_cl_status`=4) AS New_Exp_19_snk, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6710 AND `wr_cl_status`=5) AS New_Dead_20_snk, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6710 AND `wr_cl_status`=3) AS New_Hold_21_snk, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6710 AND `wr_cl_status`=6) AS New_other_22_snk, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6710 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_snk, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6710 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_snk, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  6710
	
	////////// สภ.ศรีสัชนาลัย ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6711 AND `wr_cl_status` IN (1,3)) AS All_01_snl, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6711 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_snl, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6711 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_snl, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6711 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_snl, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6711 AND `wr_cl_status` IN (1,3)) AS Old_All_1_snl, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6711 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_snl, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6711 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_snl, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6711 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_snl, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6711 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_snl, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6711 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_snl, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6711 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_snl, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6711 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_snl, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6711 AND `wr_cl_status`=6 ) AS Old_other_8_snl, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6711 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_snl, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6711 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_snl, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     ///// หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6711 AND `wr_cl_status` IN (1,3)) AS New_BF_13_snl, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6711 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_snl, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6711 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_snl, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6711 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_snl, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6711 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_snl, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6711 AND `wr_cl_status`=4) AS New_Exp_19_snl, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6711 AND `wr_cl_status`=5) AS New_Dead_20_snl, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6711 AND `wr_cl_status`=3) AS New_Hold_21_snl, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6711 AND `wr_cl_status`=6) AS New_other_22_snl, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6711 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_snl, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6711 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_snl, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  6711
	
	////////// สภ.ศรีสำโรง ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6712 AND `wr_cl_status` IN (1,3)) AS All_01_ssr, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6712 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_ssr, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6712 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_ssr, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6712 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_ssr, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6712 AND `wr_cl_status` IN (1,3)) AS Old_All_1_ssr, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6712 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_ssr, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6712 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_ssr, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6712 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_ssr, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6712 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_ssr, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6712 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_ssr, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6712 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_ssr, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6712 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_ssr, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6712 AND `wr_cl_status`=6 ) AS Old_other_8_ssr, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6712 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_ssr, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6712 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_ssr, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     ///// หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6712 AND `wr_cl_status` IN (1,3)) AS New_BF_13_ssr, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6712 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_ssr, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6712 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_ssr, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6712 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_ssr, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6712 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_ssr, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6712 AND `wr_cl_status`=4) AS New_Exp_19_ssr, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6712 AND `wr_cl_status`=5) AS New_Dead_20_ssr, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6712 AND `wr_cl_status`=3) AS New_Hold_21_ssr, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6712 AND `wr_cl_status`=6) AS New_other_22_ssr, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6712 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_ssr, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6712 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_ssr, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  6712
	
	////////// สภ.สวรรคโลก ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6713 AND `wr_cl_status` IN (1,3)) AS All_01_swl, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6713 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_swl, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6713 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_swl, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6713 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_swl, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6713 AND `wr_cl_status` IN (1,3)) AS Old_All_1_swl, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6713 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_swl, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6713 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_swl, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6713 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_swl, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6713 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_swl, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6713 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_swl, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6713 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_swl, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6713 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_swl, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6713 AND `wr_cl_status`=6 ) AS Old_other_8_swl, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6713 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_swl, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6713 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_swl, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     ///// หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6713 AND `wr_cl_status` IN (1,3)) AS New_BF_13_swl, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6713 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_swl, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6713 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_swl, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6713 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_swl, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6713 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_swl, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6713 AND `wr_cl_status`=4) AS New_Exp_19_swl, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6713 AND `wr_cl_status`=5) AS New_Dead_20_swl, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6713 AND `wr_cl_status`=3) AS New_Hold_21_swl, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6713 AND `wr_cl_status`=6) AS New_other_22_swl, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6713 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_swl, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6713 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_swl, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  6713
	
	////////// สภ.ท่าฉนวน ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6714 AND `wr_cl_status` IN (1,3)) AS All_01_tcn, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6714 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_tcn, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6714 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_tcn, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6714 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_tcn, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6714 AND `wr_cl_status` IN (1,3)) AS Old_All_1_tcn, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6714 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_tcn, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6714 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_tcn, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6714 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_tcn, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6714 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_tcn, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6714 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_tcn, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6714 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_tcn, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6714 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_tcn, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6714 AND `wr_cl_status`=6 ) AS Old_other_8_tcn, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6714 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_tcn, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6714 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_tcn, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     ///// หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6714 AND `wr_cl_status` IN (1,3)) AS New_BF_13_tcn, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6714 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_tcn, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6714 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_tcn, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6714 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_tcn, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6714 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_tcn, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6714 AND `wr_cl_status`=4) AS New_Exp_19_tcn, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6714 AND `wr_cl_status`=5) AS New_Dead_20_tcn, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6714 AND `wr_cl_status`=3) AS New_Hold_21_tcn, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6714 AND `wr_cl_status`=6) AS New_other_22_tcn, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6714 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_tcn, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6714 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_tcn, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  6714
	
	////////// สภ.เมืองบางขลัง ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6715 AND `wr_cl_status` IN (1,3)) AS All_01_mbk, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6715 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_mbk, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6715 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_mbk, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6715 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_mbk, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6715 AND `wr_cl_status` IN (1,3)) AS Old_All_1_mbk, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6715 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_mbk, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6715 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_mbk, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6715 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_mbk, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6715 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_mbk, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6715 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_mbk, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6715 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_mbk, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6715 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_mbk, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6715 AND `wr_cl_status`=6 ) AS Old_other_8_mbk, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6715 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_mbk, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6715 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_mbk, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     ///// หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6715 AND `wr_cl_status` IN (1,3)) AS New_BF_13_mbk, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6715 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_mbk, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6715 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_mbk, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6715 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_mbk, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6715 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_mbk, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6715 AND `wr_cl_status`=4) AS New_Exp_19_mbk, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6715 AND `wr_cl_status`=5) AS New_Dead_20_mbk, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6715 AND `wr_cl_status`=3) AS New_Hold_21_mbk, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6715 AND `wr_cl_status`=6) AS New_other_22_mbk, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6715 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_mbk, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6715 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_mbk ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  6715
	
    "";
                      
try {
    $stmt = $pdo->prepare($sql);
    // Bind parameter values
    $stmt->bindParam(':m1', $m1);
    $stmt->bindParam(':m2', $m2);
    $stmt->bindParam(':provincial', $provincial);
    $stmt->bindParam(':date_next', $date_next);
    $stmt->bindParam(':date_prev', $date_prev);
    
    // Execute the prepared statement
    $stmt->execute();
    
	
    // Fetch the results if needed
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Process the results as required
    foreach ($results as $row) {
                      
	$numerator = 10;
    $denominator = 0;

    if ($denominator != 0) {
        $result = $numerator / $denominator;
		} else {
		$results = null; // Or any other appropriate value or action.
    }
//echo $sql;
///// ยอดของ ภ.จว.สุโขทัย ////
$New_All_15 = $row['New_BF_13']+$row['New_M_14'];     //  
$S_27 = $row['Old_All_1']+$New_All_15;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27

// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5 = $row['Old_Exp_1']+$row['Old_Exp_2'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear = $row['Old_C_4'] + $Old_Exp_5 + $row['Old_Dead_6']+ $row['Old_other_8'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear = $row['Old_C_4'] + $row['Old_Exp_1'] + $row['Old_Dead_6']+ $row['Old_other_8'];
$Old_Q_clear_10 = $row['Old_Q_2'] - $Old_Q_clear;
$Old_NQ_clear_11 = $row['Old_NQ_3'] - $row['Old_Exp_2'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13'] >$sum2){
    $New_BF_13 = $row['New_BF_13']-$sum2;
}else{
    $New_BF_13 = $row['New_BF_13'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16'] > $sum2){
    $New_Q_16 = $row['New_Q_16']-$sum2;
}else{
    $New_Q_16 = $row['New_Q_16'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24 = $New_Q_16-$row['S_23'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24 > $sum2){
        $S_24_1 = $S_24-$sum2;
}else{
        $S_24_1 = $S_24;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23 = $row['New_C_18'] + $row['New_Exp_19'] + $row['New_Dead_20']+ $row['New_other_22'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24 = $New_Q_16 - $New_clear_23;
$S_28 = $row['S_9']+$New_clear_23; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen = 0;
$NewPersen = 0;
$AllPersen = 0;
if ($row['All_01'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen = ($row['S_9'] / $row['All_01']) * 100;
	$OldPersen = round($OldPersen,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen = ($row['S_23'] / $row['All_01']) * 100;
	$NewPersen = round($NewPersen,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27 != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen = ($S_28 / $S_27) * 100;
	$AllPersen = round($AllPersen,2);  // จุดทศนิยม 2 ตำแหน่ง  
}

	
/////  สภ.กงไกรลาศ  ////
		
$New_All_15_kkl = $row['New_BF_13_kkl']+$row['New_M_14_kkl'];     //  
$S_27_kkl = $row['Old_All_1_kkl']+$New_All_15_kkl;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_kkl = $row['Old_Exp_1_kkl']+$row['Old_Exp_2_kkl'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_kkl = $row['Old_C_4_kkl'] + $Old_Exp_5_kkl + $row['Old_Dead_6_kkl']+ $row['Old_other_8_kkl'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_kkl = $row['Old_C_4_kkl'] + $row['Old_Exp_1_kkl'] + $row['Old_Dead_6_kkl']+ $row['Old_other_8_kkl'];
$Old_Q_clear_10_kkl = $row['Old_Q_2_kkl'] - $Old_Q_clear_kkl;
$Old_NQ_clear_11_kkl = $row['Old_NQ_3_kkl'] - $row['Old_Exp_2_kkl'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_kkl'] >$sum2){
    $New_BF_13_kkl = $row['New_BF_13_kkl']-$sum2;
}else{
    $New_BF_13_kkl = $row['New_BF_13_kkl'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_kkl'] > $sum2){
    $New_Q_16_kkl = $row['New_Q_16_kkl']-$sum2;
}else{
    $New_Q_16_kkl = $row['New_Q_16_kkl'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_kkl = $New_Q_16_kkl - $row['S_23_kkl'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_kkl > $sum2){
        $S_24_1_kkl = $S_24_kkl - $sum2;
}else{
        $S_24_1_kkl = $S_24_kkl;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_kkl = $row['New_C_18_kkl'] + $row['New_Exp_19_kkl'] + $row['New_Dead_20_kkl']+ $row['New_other_22_kkl'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_kkl = $New_Q_16_kkl - $New_clear_23_kkl;
$S_28_kkl = $row['S_9_kkl']+$New_clear_23_kkl; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_kkl = 0;
$NewPersen_kkl = 0;
$AllPersen_kkl = 0;
if ($row['All_01_kkl'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_kkl = ($row['S_9_kkl'] / $row['All_01_kkl']) * 100;
	$OldPersen_kkl = round($OldPersen_kkl,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_kkl = ($row['S_23_kkl'] / $row['All_01_kkl']) * 100;
	$NewPersen_kkl = round($NewPersen_kkl,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_kkl != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_kkl = ($S_28_kkl / $S_27_kkl) * 100;
	$AllPersen_kkl = round($AllPersen_kkl,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.คีรีมาศ  ////
		
$New_All_15_krm = $row['New_BF_13_krm']+$row['New_M_14_krm'];     //  
$S_27_krm = $row['Old_All_1_krm']+$New_All_15_krm;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_krm = $row['Old_Exp_1_krm']+$row['Old_Exp_2_krm'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_krm = $row['Old_C_4_krm'] + $Old_Exp_5_krm + $row['Old_Dead_6_krm']+ $row['Old_other_8_krm'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_krm = $row['Old_C_4_krm'] + $row['Old_Exp_1_krm'] + $row['Old_Dead_6_krm']+ $row['Old_other_8_krm'];
$Old_Q_clear_10_krm = $row['Old_Q_2_krm'] - $Old_Q_clear_krm;
$Old_NQ_clear_11_krm = $row['Old_NQ_3_krm'] - $row['Old_Exp_2_krm'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_krm'] >$sum2){
    $New_BF_13_krm = $row['New_BF_13_krm']-$sum2;
}else{
    $New_BF_13_krm = $row['New_BF_13_krm'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_krm'] > $sum2){
    $New_Q_16_krm = $row['New_Q_16_krm']-$sum2;
}else{
    $New_Q_16_krm = $row['New_Q_16_krm'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_krm = $New_Q_16_krm - $row['S_23_krm'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_krm > $sum2){
        $S_24_1_krm = $S_24_krm - $sum2;
}else{
        $S_24_1_krm = $S_24_krm;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_krm = $row['New_C_18_krm'] + $row['New_Exp_19_krm'] + $row['New_Dead_20_krm']+ $row['New_other_22_krm'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_krm = $New_Q_16_krm - $New_clear_23_krm;
$S_28_krm = $row['S_9_krm']+$New_clear_23_krm; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_krm = 0;
$NewPersen_krm = 0;
$AllPersen_krm = 0;
if ($row['All_01_krm'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_krm = ($row['S_9_krm'] / $row['All_01_krm']) * 100;
	$OldPersen_krm = round($OldPersen_krm,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_krm = ($row['S_23_krm'] / $row['All_01_krm']) * 100;
	$NewPersen_krm = round($NewPersen_krm,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_krm != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_krm = ($S_28_krm / $S_27_krm) * 100;
	$AllPersen_krm = round($AllPersen_krm,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.ทุ่งเสลี่ยม  ////
		
$New_All_15_tsl = $row['New_BF_13_tsl']+$row['New_M_14_tsl'];     //  
$S_27_tsl = $row['Old_All_1_tsl']+$New_All_15_tsl;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_tsl = $row['Old_Exp_1_tsl']+$row['Old_Exp_2_tsl'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_tsl = $row['Old_C_4_tsl'] + $Old_Exp_5_tsl + $row['Old_Dead_6_tsl']+ $row['Old_other_8_tsl'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_tsl = $row['Old_C_4_tsl'] + $row['Old_Exp_1_tsl'] + $row['Old_Dead_6_tsl']+ $row['Old_other_8_tsl'];
$Old_Q_clear_10_tsl = $row['Old_Q_2_tsl'] - $Old_Q_clear_tsl;
$Old_NQ_clear_11_tsl = $row['Old_NQ_3_tsl'] - $row['Old_Exp_2_tsl'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_tsl'] >$sum2){
    $New_BF_13_tsl = $row['New_BF_13_tsl']-$sum2;
}else{
    $New_BF_13_tsl = $row['New_BF_13_tsl'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_tsl'] > $sum2){
    $New_Q_16_tsl = $row['New_Q_16_tsl']-$sum2;
}else{
    $New_Q_16_tsl = $row['New_Q_16_tsl'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_tsl = $New_Q_16_tsl - $row['S_23_tsl'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_tsl > $sum2){
        $S_24_1_tsl = $S_24_tsl - $sum2;
}else{
        $S_24_1_tsl = $S_24_tsl;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_tsl = $row['New_C_18_tsl'] + $row['New_Exp_19_tsl'] + $row['New_Dead_20_tsl']+ $row['New_other_22_tsl'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_tsl = $New_Q_16_tsl - $New_clear_23_tsl;
$S_28_tsl = $row['S_9_tsl']+$New_clear_23_tsl; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_tsl = 0;
$NewPersen_tsl = 0;
$AllPersen_tsl = 0;
if ($row['All_01_tsl'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_tsl = ($row['S_9_tsl'] / $row['All_01_tsl']) * 100;
	$OldPersen_tsl = round($OldPersen_tsl,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_tsl = ($row['S_23_tsl'] / $row['All_01_tsl']) * 100;
	$NewPersen_tsl = round($NewPersen_tsl,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_tsl != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_tsl = ($S_28_tsl / $S_27_tsl) * 100;
	$AllPersen_tsl = round($AllPersen_tsl,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.บ้านแก่ง  ////
		
$New_All_15_bng = $row['New_BF_13_bng']+$row['New_M_14_bng'];     //  
$S_27_bng = $row['Old_All_1_bng']+$New_All_15_bng;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_bng = $row['Old_Exp_1_bng']+$row['Old_Exp_2_bng'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_bng = $row['Old_C_4_bng'] + $Old_Exp_5_bng + $row['Old_Dead_6_bng']+ $row['Old_other_8_bng'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_bng = $row['Old_C_4_bng'] + $row['Old_Exp_1_bng'] + $row['Old_Dead_6_bng']+ $row['Old_other_8_bng'];
$Old_Q_clear_10_bng = $row['Old_Q_2_bng'] - $Old_Q_clear_bng;
$Old_NQ_clear_11_bng = $row['Old_NQ_3_bng'] - $row['Old_Exp_2_bng'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_bng'] >$sum2){
    $New_BF_13_bng = $row['New_BF_13_bng']-$sum2;
}else{
    $New_BF_13_bng = $row['New_BF_13_bng'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_bng'] > $sum2){
    $New_Q_16_bng = $row['New_Q_16_bng']-$sum2;
}else{
    $New_Q_16_bng = $row['New_Q_16_bng'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_bng = $New_Q_16_bng - $row['S_23_bng'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_bng > $sum2){
        $S_24_1_bng = $S_24_bng - $sum2;
}else{
        $S_24_1_bng = $S_24_bng;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_bng = $row['New_C_18_bng'] + $row['New_Exp_19_bng'] + $row['New_Dead_20_bng']+ $row['New_other_22_bng'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_bng = $New_Q_16_bng - $New_clear_23_bng;
$S_28_bng = $row['S_9_bng']+$New_clear_23_bng; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_bng = 0;
$NewPersen_bng = 0;
$AllPersen_bng = 0;
if ($row['All_01_bng'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_bng = ($row['S_9_bng'] / $row['All_01_bng']) * 100;
	$OldPersen_bng = round($OldPersen_bng,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_bng = ($row['S_23_bng'] / $row['All_01_bng']) * 100;
	$NewPersen_bng = round($NewPersen_bng,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_bng != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_bng = ($S_28_bng / $S_27_bng) * 100;
	$AllPersen_bng = round($AllPersen_bng,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.บ้านด่านลานหอย  ////
		
$New_All_15_bdh = $row['New_BF_13_bdh']+$row['New_M_14_bdh'];     //  
$S_27_bdh = $row['Old_All_1_bdh']+$New_All_15_bdh;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_bdh = $row['Old_Exp_1_bdh']+$row['Old_Exp_2_bdh'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_bdh = $row['Old_C_4_bdh'] + $Old_Exp_5_bdh + $row['Old_Dead_6_bdh']+ $row['Old_other_8_bdh'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_bdh = $row['Old_C_4_bdh'] + $row['Old_Exp_1_bdh'] + $row['Old_Dead_6_bdh']+ $row['Old_other_8_bdh'];
$Old_Q_clear_10_bdh = $row['Old_Q_2_bdh'] - $Old_Q_clear_bdh;
$Old_NQ_clear_11_bdh = $row['Old_NQ_3_bdh'] - $row['Old_Exp_2_bdh'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_bdh'] >$sum2){
    $New_BF_13_bdh = $row['New_BF_13_bdh']-$sum2;
}else{
    $New_BF_13_bdh = $row['New_BF_13_bdh'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_bdh'] > $sum2){
    $New_Q_16_bdh = $row['New_Q_16_bdh']-$sum2;
}else{
    $New_Q_16_bdh = $row['New_Q_16_bdh'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_bdh = $New_Q_16_bdh - $row['S_23_bdh'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_bdh > $sum2){
        $S_24_1_bdh = $S_24_bdh - $sum2;
}else{
        $S_24_1_bdh = $S_24_bdh;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_bdh = $row['New_C_18_bdh'] + $row['New_Exp_19_bdh'] + $row['New_Dead_20_bdh']+ $row['New_other_22_bdh'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_bdh = $New_Q_16_bdh - $New_clear_23_bdh;
$S_28_bdh = $row['S_9_bdh']+$New_clear_23_bdh; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_bdh = 0;
$NewPersen_bdh = 0;
$AllPersen_bdh = 0;
if ($row['All_01_bdh'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_bdh = ($row['S_9_bdh'] / $row['All_01_bdh']) * 100;
	$OldPersen_bdh = round($OldPersen_bdh,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_bdh = ($row['S_23_bdh'] / $row['All_01_bdh']) * 100;
	$NewPersen_bdh = round($NewPersen_bdh,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_bdh != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_bdh = ($S_28_bdh / $S_27_bdh) * 100;
	$AllPersen_bdh = round($AllPersen_bdh,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.บ้านไร่  ////
		
$New_All_15_bnr = $row['New_BF_13_bnr']+$row['New_M_14_bnr'];     //  
$S_27_bnr = $row['Old_All_1_bnr']+$New_All_15_bnr;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_bnr = $row['Old_Exp_1_bnr']+$row['Old_Exp_2_bnr'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_bnr = $row['Old_C_4_bnr'] + $Old_Exp_5_bnr + $row['Old_Dead_6_bnr']+ $row['Old_other_8_bnr'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_bnr = $row['Old_C_4_bnr'] + $row['Old_Exp_1_bnr'] + $row['Old_Dead_6_bnr']+ $row['Old_other_8_bnr'];
$Old_Q_clear_10_bnr = $row['Old_Q_2_bnr'] - $Old_Q_clear_bnr;
$Old_NQ_clear_11_bnr = $row['Old_NQ_3_bnr'] - $row['Old_Exp_2_bnr'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_bnr'] >$sum2){
    $New_BF_13_bnr = $row['New_BF_13_bnr']-$sum2;
}else{
    $New_BF_13_bnr = $row['New_BF_13_bnr'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_bnr'] > $sum2){
    $New_Q_16_bnr = $row['New_Q_16_bnr']-$sum2;
}else{
    $New_Q_16_bnr = $row['New_Q_16_bnr'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_bnr = $New_Q_16_bnr - $row['S_23_bnr'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_bnr > $sum2){
        $S_24_1_bnr = $S_24_bnr - $sum2;
}else{
        $S_24_1_bnr = $S_24_bnr;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_bnr = $row['New_C_18_bnr'] + $row['New_Exp_19_bnr'] + $row['New_Dead_20_bnr']+ $row['New_other_22_bnr'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_bnr = $New_Q_16_bnr - $New_clear_23_bnr;
$S_28_bnr = $row['S_9_bnr']+$New_clear_23_bnr; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_bnr = 0;
$NewPersen_bnr = 0;
$AllPersen_bnr = 0;
if ($row['All_01_bnr'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_bnr = ($row['S_9_bnr'] / $row['All_01_bnr']) * 100;
	$OldPersen_bnr = round($OldPersen_bnr,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_bnr = ($row['S_23_bnr'] / $row['All_01_bnr']) * 100;
	$NewPersen_bnr = round($NewPersen_bnr,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_bnr != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_bnr = ($S_28_bnr / $S_27_bnr) * 100;
	$AllPersen_bnr = round($AllPersen_bnr,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.บ้านสวน  ////
		
$New_All_15_bs = $row['New_BF_13_bs']+$row['New_M_14_bs'];     //  
$S_27_bs = $row['Old_All_1_bs']+$New_All_15_bs;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_bs = $row['Old_Exp_1_bs']+$row['Old_Exp_2_bs'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_bs = $row['Old_C_4_bs'] + $Old_Exp_5_bs + $row['Old_Dead_6_bs']+ $row['Old_other_8_bs'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_bs = $row['Old_C_4_bs'] + $row['Old_Exp_1_bs'] + $row['Old_Dead_6_bs']+ $row['Old_other_8_bs'];
$Old_Q_clear_10_bs = $row['Old_Q_2_bs'] - $Old_Q_clear_bs;
$Old_NQ_clear_11_bs = $row['Old_NQ_3_bs'] - $row['Old_Exp_2_bs'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_bs'] >$sum2){
    $New_BF_13_bs = $row['New_BF_13_bs']-$sum2;
}else{
    $New_BF_13_bs = $row['New_BF_13_bs'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_bs'] > $sum2){
    $New_Q_16_bs = $row['New_Q_16_bs']-$sum2;
}else{
    $New_Q_16_bs = $row['New_Q_16_bs'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_bs = $New_Q_16_bs - $row['S_23_bs'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_bs > $sum2){
        $S_24_1_bs = $S_24_bs - $sum2;
}else{
        $S_24_1_bs = $S_24_bs;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_bs = $row['New_C_18_bs'] + $row['New_Exp_19_bs'] + $row['New_Dead_20_bs']+ $row['New_other_22_bs'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_bs = $New_Q_16_bs - $New_clear_23_bs;
$S_28_bs = $row['S_9_bs']+$New_clear_23_bs; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_bs = 0;
$NewPersen_bs = 0;
$AllPersen_bs = 0;
if ($row['All_01_bs'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_bs = ($row['S_9_bs'] / $row['All_01_bs']) * 100;
	$OldPersen_bs = round($OldPersen_bs,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_bs = ($row['S_23_bs'] / $row['All_01_bs']) * 100;
	$NewPersen_bs = round($NewPersen_bs,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_bs != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_bs = ($S_28_bs / $S_27_bs) * 100;
	$AllPersen_bs = round($AllPersen_bs,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.เมืองเก่า  ////
		
$New_All_15_mgk = $row['New_BF_13_mgk']+$row['New_M_14_mgk'];     //  
$S_27_mgk = $row['Old_All_1_mgk']+$New_All_15_mgk;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_mgk = $row['Old_Exp_1_mgk']+$row['Old_Exp_2_mgk'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_mgk = $row['Old_C_4_mgk'] + $Old_Exp_5_mgk + $row['Old_Dead_6_mgk']+ $row['Old_other_8_mgk'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_mgk = $row['Old_C_4_mgk'] + $row['Old_Exp_1_mgk'] + $row['Old_Dead_6_mgk']+ $row['Old_other_8_mgk'];
$Old_Q_clear_10_mgk = $row['Old_Q_2_mgk'] - $Old_Q_clear_mgk;
$Old_NQ_clear_11_mgk = $row['Old_NQ_3_mgk'] - $row['Old_Exp_2_mgk'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_mgk'] >$sum2){
    $New_BF_13_mgk = $row['New_BF_13_mgk']-$sum2;
}else{
    $New_BF_13_mgk = $row['New_BF_13_mgk'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_mgk'] > $sum2){
    $New_Q_16_mgk = $row['New_Q_16_mgk']-$sum2;
}else{
    $New_Q_16_mgk = $row['New_Q_16_mgk'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_mgk = $New_Q_16_mgk - $row['S_23_mgk'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_mgk > $sum2){
        $S_24_1_mgk = $S_24_mgk - $sum2;
}else{
        $S_24_1_mgk = $S_24_mgk;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_mgk = $row['New_C_18_mgk'] + $row['New_Exp_19_mgk'] + $row['New_Dead_20_mgk']+ $row['New_other_22_mgk'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_mgk = $New_Q_16_mgk - $New_clear_23_mgk;
$S_28_mgk = $row['S_9_mgk']+$New_clear_23_mgk; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_mgk = 0;
$NewPersen_mgk = 0;
$AllPersen_mgk = 0;
if ($row['All_01_mgk'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_mgk = ($row['S_9_mgk'] / $row['All_01_mgk']) * 100;
	$OldPersen_mgk = round($OldPersen_mgk,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_mgk = ($row['S_23_mgk'] / $row['All_01_mgk']) * 100;
	$NewPersen_mgk = round($NewPersen_mgk,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_mgk != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_mgk = ($S_28_mgk / $S_27_mgk) * 100;
	$AllPersen_mgk = round($AllPersen_mgk,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.เมืองสุโขทัย  ////
		
$New_All_15_mst = $row['New_BF_13_mst']+$row['New_M_14_mst'];     //  
$S_27_mst = $row['Old_All_1_mst']+$New_All_15_mst;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_mst = $row['Old_Exp_1_mst']+$row['Old_Exp_2_mst'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_mst = $row['Old_C_4_mst'] + $Old_Exp_5_mst + $row['Old_Dead_6_mst']+ $row['Old_other_8_mst'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_mst = $row['Old_C_4_mst'] + $row['Old_Exp_1_mst'] + $row['Old_Dead_6_mst']+ $row['Old_other_8_mst'];
$Old_Q_clear_10_mst = $row['Old_Q_2_mst'] - $Old_Q_clear_mst;
$Old_NQ_clear_11_mst = $row['Old_NQ_3_mst'] - $row['Old_Exp_2_mst'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_mst'] >$sum2){
    $New_BF_13_mst = $row['New_BF_13_mst']-$sum2;
}else{
    $New_BF_13_mst = $row['New_BF_13_mst'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_mst'] > $sum2){
    $New_Q_16_mst = $row['New_Q_16_mst']-$sum2;
}else{
    $New_Q_16_mst = $row['New_Q_16_mst'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_mst = $New_Q_16_mst - $row['S_23_mst'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_mst > $sum2){
        $S_24_1_mst = $S_24_mst - $sum2;
}else{
        $S_24_1_mst = $S_24_mst;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_mst = $row['New_C_18_mst'] + $row['New_Exp_19_mst'] + $row['New_Dead_20_mst']+ $row['New_other_22_mst'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_mst = $New_Q_16_mst - $New_clear_23_mst;
$S_28_mst = $row['S_9_mst']+$New_clear_23_mst; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_mst = 0;
$NewPersen_mst = 0;
$AllPersen_mst = 0;
if ($row['All_01_mst'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_mst = ($row['S_9_mst'] / $row['All_01_mst']) * 100;
	$OldPersen_mst = round($OldPersen_mst,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_mst = ($row['S_23_mst'] / $row['All_01_mst']) * 100;
	$NewPersen_mst = round($NewPersen_mst,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_mst != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_mst = ($S_28_mst / $S_27_mst) * 100;
	$AllPersen_mst = round($AllPersen_mst,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.ศรีนคร  ////
		
$New_All_15_snk = $row['New_BF_13_snk']+$row['New_M_14_snk'];     //  
$S_27_snk = $row['Old_All_1_snk']+$New_All_15_snk;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_snk = $row['Old_Exp_1_snk']+$row['Old_Exp_2_snk'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_snk = $row['Old_C_4_snk'] + $Old_Exp_5_snk + $row['Old_Dead_6_snk']+ $row['Old_other_8_snk'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_snk = $row['Old_C_4_snk'] + $row['Old_Exp_1_snk'] + $row['Old_Dead_6_snk']+ $row['Old_other_8_snk'];
$Old_Q_clear_10_snk = $row['Old_Q_2_snk'] - $Old_Q_clear_snk;
$Old_NQ_clear_11_snk = $row['Old_NQ_3_snk'] - $row['Old_Exp_2_snk'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_snk'] >$sum2){
    $New_BF_13_snk = $row['New_BF_13_snk']-$sum2;
}else{
    $New_BF_13_snk = $row['New_BF_13_snk'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_snk'] > $sum2){
    $New_Q_16_snk = $row['New_Q_16_snk']-$sum2;
}else{
    $New_Q_16_snk = $row['New_Q_16_snk'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_snk = $New_Q_16_snk - $row['S_23_snk'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_snk > $sum2){
        $S_24_1_snk = $S_24_snk - $sum2;
}else{
        $S_24_1_snk = $S_24_snk;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_snk = $row['New_C_18_snk'] + $row['New_Exp_19_snk'] + $row['New_Dead_20_snk']+ $row['New_other_22_snk'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_snk = $New_Q_16_snk - $New_clear_23_snk;
$S_28_snk = $row['S_9_snk']+$New_clear_23_snk; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_snk = 0;
$NewPersen_snk = 0;
$AllPersen_snk = 0;
if ($row['All_01_snk'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_snk = ($row['S_9_snk'] / $row['All_01_snk']) * 100;
	$OldPersen_snk = round($OldPersen_snk,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_snk = ($row['S_23_snk'] / $row['All_01_snk']) * 100;
	$NewPersen_snk = round($NewPersen_snk,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_snk != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_snk = ($S_28_snk / $S_27_snk) * 100;
	$AllPersen_snk = round($AllPersen_snk,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.ศรีสัชนาลัย  ////
		
$New_All_15_snl = $row['New_BF_13_snl']+$row['New_M_14_snl'];     //  
$S_27_snl = $row['Old_All_1_snl']+$New_All_15_snl;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_snl = $row['Old_Exp_1_snl']+$row['Old_Exp_2_snl'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_snl = $row['Old_C_4_snl'] + $Old_Exp_5_snl + $row['Old_Dead_6_snl']+ $row['Old_other_8_snl'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_snl = $row['Old_C_4_snl'] + $row['Old_Exp_1_snl'] + $row['Old_Dead_6_snl']+ $row['Old_other_8_snl'];
$Old_Q_clear_10_snl = $row['Old_Q_2_snl'] - $Old_Q_clear_snl;
$Old_NQ_clear_11_snl = $row['Old_NQ_3_snl'] - $row['Old_Exp_2_snl'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_snl'] >$sum2){
    $New_BF_13_snl = $row['New_BF_13_snl']-$sum2;
}else{
    $New_BF_13_snl = $row['New_BF_13_snl'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_snl'] > $sum2){
    $New_Q_16_snl = $row['New_Q_16_snl']-$sum2;
}else{
    $New_Q_16_snl = $row['New_Q_16_snl'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_snl = $New_Q_16_snl - $row['S_23_snl'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_snl > $sum2){
        $S_24_1_snl = $S_24_snl - $sum2;
}else{
        $S_24_1_snl = $S_24_snl;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_snl = $row['New_C_18_snl'] + $row['New_Exp_19_snl'] + $row['New_Dead_20_snl']+ $row['New_other_22_snl'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_snl = $New_Q_16_snl - $New_clear_23_snl;
$S_28_snl = $row['S_9_snl']+$New_clear_23_snl; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_snl = 0;
$NewPersen_snl = 0;
$AllPersen_snl = 0;
if ($row['All_01_snl'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_snl = ($row['S_9_snl'] / $row['All_01_snl']) * 100;
	$OldPersen_snl = round($OldPersen_snl,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_snl = ($row['S_23_snl'] / $row['All_01_snl']) * 100;
	$NewPersen_snl = round($NewPersen_snl,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_snl != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_snl = ($S_28_snl / $S_27_snl) * 100;
	$AllPersen_snl = round($AllPersen_snl,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.ศรีสำโรง  ////
		
$New_All_15_ssr = $row['New_BF_13_ssr']+$row['New_M_14_ssr'];     //  
$S_27_ssr = $row['Old_All_1_ssr']+$New_All_15_ssr;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_ssr = $row['Old_Exp_1_ssr']+$row['Old_Exp_2_ssr'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_ssr = $row['Old_C_4_ssr'] + $Old_Exp_5_ssr + $row['Old_Dead_6_ssr']+ $row['Old_other_8_ssr'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_ssr = $row['Old_C_4_ssr'] + $row['Old_Exp_1_ssr'] + $row['Old_Dead_6_ssr']+ $row['Old_other_8_ssr'];
$Old_Q_clear_10_ssr = $row['Old_Q_2_ssr'] - $Old_Q_clear_ssr;
$Old_NQ_clear_11_ssr = $row['Old_NQ_3_ssr'] - $row['Old_Exp_2_ssr'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_ssr'] >$sum2){
    $New_BF_13_ssr = $row['New_BF_13_ssr']-$sum2;
}else{
    $New_BF_13_ssr = $row['New_BF_13_ssr'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_ssr'] > $sum2){
    $New_Q_16_ssr = $row['New_Q_16_ssr']-$sum2;
}else{
    $New_Q_16_ssr = $row['New_Q_16_ssr'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_ssr = $New_Q_16_ssr - $row['S_23_ssr'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_ssr > $sum2){
        $S_24_1_ssr = $S_24_ssr - $sum2;
}else{
        $S_24_1_ssr = $S_24_ssr;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_ssr = $row['New_C_18_ssr'] + $row['New_Exp_19_ssr'] + $row['New_Dead_20_ssr']+ $row['New_other_22_ssr'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_ssr = $New_Q_16_ssr - $New_clear_23_ssr;
$S_28_ssr = $row['S_9_ssr']+$New_clear_23_ssr; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_ssr = 0;
$NewPersen_ssr = 0;
$AllPersen_ssr = 0;
if ($row['All_01_ssr'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_ssr = ($row['S_9_ssr'] / $row['All_01_ssr']) * 100;
	$OldPersen_ssr = round($OldPersen_ssr,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_ssr = ($row['S_23_ssr'] / $row['All_01_ssr']) * 100;
	$NewPersen_ssr = round($NewPersen_ssr,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_ssr != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_ssr = ($S_28_ssr / $S_27_ssr) * 100;
	$AllPersen_ssr = round($AllPersen_ssr,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.สวรรคโลก  ////
		
$New_All_15_swl = $row['New_BF_13_swl']+$row['New_M_14_swl'];     //  
$S_27_swl = $row['Old_All_1_swl']+$New_All_15_swl;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_swl = $row['Old_Exp_1_swl']+$row['Old_Exp_2_swl'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_swl = $row['Old_C_4_swl'] + $Old_Exp_5_swl + $row['Old_Dead_6_swl']+ $row['Old_other_8_swl'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_swl = $row['Old_C_4_swl'] + $row['Old_Exp_1_swl'] + $row['Old_Dead_6_swl']+ $row['Old_other_8_swl'];
$Old_Q_clear_10_swl = $row['Old_Q_2_swl'] - $Old_Q_clear_swl;
$Old_NQ_clear_11_swl = $row['Old_NQ_3_swl'] - $row['Old_Exp_2_swl'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_swl'] >$sum2){
    $New_BF_13_swl = $row['New_BF_13_swl']-$sum2;
}else{
    $New_BF_13_swl = $row['New_BF_13_swl'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_swl'] > $sum2){
    $New_Q_16_swl = $row['New_Q_16_swl']-$sum2;
}else{
    $New_Q_16_swl = $row['New_Q_16_swl'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_swl = $New_Q_16_swl - $row['S_23_swl'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_swl > $sum2){
        $S_24_1_swl = $S_24_swl - $sum2;
}else{
        $S_24_1_swl = $S_24_swl;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_swl = $row['New_C_18_swl'] + $row['New_Exp_19_swl'] + $row['New_Dead_20_swl']+ $row['New_other_22_swl'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_swl = $New_Q_16_swl - $New_clear_23_swl;
$S_28_swl = $row['S_9_swl']+$New_clear_23_swl; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_swl = 0;
$NewPersen_swl = 0;
$AllPersen_swl = 0;
if ($row['All_01_swl'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_swl = ($row['S_9_swl'] / $row['All_01_swl']) * 100;
	$OldPersen_swl = round($OldPersen_swl,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_swl = ($row['S_23_swl'] / $row['All_01_swl']) * 100;
	$NewPersen_swl = round($NewPersen_swl,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_swl != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_swl = ($S_28_swl / $S_27_swl) * 100;
	$AllPersen_swl = round($AllPersen_swl,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.ท่าฉนวน  ////
		
$New_All_15_tcn = $row['New_BF_13_tcn']+$row['New_M_14_tcn'];     //  
$S_27_tcn = $row['Old_All_1_tcn']+$New_All_15_tcn;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_tcn = $row['Old_Exp_1_tcn']+$row['Old_Exp_2_tcn'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_tcn = $row['Old_C_4_tcn'] + $Old_Exp_5_tcn + $row['Old_Dead_6_tcn']+ $row['Old_other_8_tcn'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_tcn = $row['Old_C_4_tcn'] + $row['Old_Exp_1_tcn'] + $row['Old_Dead_6_tcn']+ $row['Old_other_8_tcn'];
$Old_Q_clear_10_tcn = $row['Old_Q_2_tcn'] - $Old_Q_clear_tcn;
$Old_NQ_clear_11_tcn = $row['Old_NQ_3_tcn'] - $row['Old_Exp_2_tcn'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_tcn'] >$sum2){
    $New_BF_13_tcn = $row['New_BF_13_tcn']-$sum2;
}else{
    $New_BF_13_tcn = $row['New_BF_13_tcn'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_tcn'] > $sum2){
    $New_Q_16_tcn = $row['New_Q_16_tcn']-$sum2;
}else{
    $New_Q_16_tcn = $row['New_Q_16_tcn'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_tcn = $New_Q_16_tcn - $row['S_23_tcn'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_tcn > $sum2){
        $S_24_1_tcn = $S_24_tcn - $sum2;
}else{
        $S_24_1_tcn = $S_24_tcn;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_tcn = $row['New_C_18_tcn'] + $row['New_Exp_19_tcn'] + $row['New_Dead_20_tcn']+ $row['New_other_22_tcn'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_tcn = $New_Q_16_tcn - $New_clear_23_tcn;
$S_28_tcn = $row['S_9_tcn']+$New_clear_23_tcn; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_tcn = 0;
$NewPersen_tcn = 0;
$AllPersen_tcn = 0;
if ($row['All_01_tcn'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_tcn = ($row['S_9_tcn'] / $row['All_01_tcn']) * 100;
	$OldPersen_tcn = round($OldPersen_tcn,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_tcn = ($row['S_23_tcn'] / $row['All_01_tcn']) * 100;
	$NewPersen_tcn = round($NewPersen_tcn,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_tcn != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_tcn = ($S_28_tcn / $S_27_tcn) * 100;
	$AllPersen_tcn = round($AllPersen_tcn,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.เมืองบางขลัง  ////
		
$New_All_15_mbk = $row['New_BF_13_mbk']+$row['New_M_14_mbk'];     //  
$S_27_mbk = $row['Old_All_1_mbk']+$New_All_15_mbk;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_mbk = $row['Old_Exp_1_mbk']+$row['Old_Exp_2_mbk'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_mbk = $row['Old_C_4_mbk'] + $Old_Exp_5_mbk + $row['Old_Dead_6_mbk']+ $row['Old_other_8_mbk'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_mbk = $row['Old_C_4_mbk'] + $row['Old_Exp_1_mbk'] + $row['Old_Dead_6_mbk']+ $row['Old_other_8_mbk'];
$Old_Q_clear_10_mbk = $row['Old_Q_2_mbk'] - $Old_Q_clear_mbk;
$Old_NQ_clear_11_mbk = $row['Old_NQ_3_mbk'] - $row['Old_Exp_2_mbk'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_mbk'] >$sum2){
    $New_BF_13_mbk = $row['New_BF_13_mbk']-$sum2;
}else{
    $New_BF_13_mbk = $row['New_BF_13_mbk'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_mbk'] > $sum2){
    $New_Q_16_mbk = $row['New_Q_16_mbk']-$sum2;
}else{
    $New_Q_16_mbk = $row['New_Q_16_mbk'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_mbk = $New_Q_16_mbk - $row['S_23_mbk'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_mbk > $sum2){
        $S_24_1_mbk = $S_24_mbk - $sum2;
}else{
        $S_24_1_mbk = $S_24_mbk;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_mbk = $row['New_C_18_mbk'] + $row['New_Exp_19_mbk'] + $row['New_Dead_20_mbk']+ $row['New_other_22_mbk'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_mbk = $New_Q_16_mbk - $New_clear_23_mbk;
$S_28_mbk = $row['S_9_mbk']+$New_clear_23_mbk; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_mbk = 0;
$NewPersen_mbk = 0;
$AllPersen_mbk = 0;
if ($row['All_01_mbk'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_mbk = ($row['S_9_mbk'] / $row['All_01_mbk']) * 100;
	$OldPersen_mbk = round($OldPersen_mbk,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_mbk = ($row['S_23_mbk'] / $row['All_01_mbk']) * 100;
	$NewPersen_mbk = round($NewPersen_mbk,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_mbk != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_mbk = ($S_28_mbk / $S_27_mbk) * 100;
	$AllPersen_mbk = round($AllPersen_mbk,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
?>
<!-- ภ.จว.สุโขทัย -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua ' nowrap><span style="height:22.8pt; background-color:aqua "><?= $name_provincial ?></span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4'] > 0) ? $row['Old_C_4'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5 > 0) ? $Old_Exp_5 : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6'] > 0) ? $row['Old_Dead_6'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7'] > 0) ? $row['Old_Hold_7'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8'] > 0) ? $row['Old_other_8'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear > 0) ? $Old_clear : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10 > 0) ? ($Old_Q_clear_10)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3'] > 0) ? ($row['Old_NQ_3'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13 > 0) ? ($New_BF_13)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14'] > 0) ? $row['New_M_14']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15 > 0) ? ($New_All_15)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16 > 0) ? ($New_Q_16)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17'] > 0) ? $row['New_NQ_17'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18'] > 0) ? $row['New_C_18'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19'] > 0) ? $row['New_Exp_19'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20'] > 0) ? $row['New_Dead_20'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21'] > 0) ? $row['New_Hold_21'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22'] > 0) ? $row['New_other_22'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23 > 0) ? $New_clear_23 : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24 > 0) ? ($New_Q_bl_24)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25'] > 0) ? ($row['S_25'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27 ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28 ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.กงไกรลาศ -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua ' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.กงไกรลาศ</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_kkl'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_kkl'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_kkl'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_kkl'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_kkl'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_kkl'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_kkl'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_kkl'] > 0) ? $row['Old_C_4_kkl'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_kkl > 0) ? $Old_Exp_5_kkl : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_kkl'] > 0) ? $row['Old_Dead_6_kkl'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_kkl'] > 0) ? $row['Old_Hold_7_kkl'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_kkl'] > 0) ? $row['Old_other_8_kkl'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_kkl > 0) ? $Old_clear_kkl : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_kkl > 0) ? ($Old_Q_clear_10_kkl)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_kkl'] > 0) ? ($row['Old_NQ_3_kkl'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_kkl ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_kkl > 0) ? ($New_BF_13_kkl)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_kkl'] > 0) ? $row['New_M_14_kkl']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_kkl > 0) ? ($New_All_15_kkl)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_kkl > 0) ? ($New_Q_16_kkl)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_kkl'] > 0) ? $row['New_NQ_17_kkl'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_kkl'] > 0) ? $row['New_C_18_kkl'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_kkl'] > 0) ? $row['New_Exp_19_kkl'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_kkl'] > 0) ? $row['New_Dead_20_kkl'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_kkl'] > 0) ? $row['New_Hold_21_kkl'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_kkl'] > 0) ? $row['New_other_22_kkl'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_kkl > 0) ? $New_clear_23_kkl : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_kkl > 0) ? ($New_Q_bl_24_kkl)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_kkl'] > 0) ? ($row['S_25_kkl'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_kkl ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_kkl ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_kkl ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_kkl > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_kkl < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_kkl ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.คีรีมาศ -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua ' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.คีรีมาศ</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_krm'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_krm'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_krm'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_krm'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_krm'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_krm'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_krm'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_krm'] > 0) ? $row['Old_C_4_krm'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_krm > 0) ? $Old_Exp_5_krm : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_krm'] > 0) ? $row['Old_Dead_6_krm'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_krm'] > 0) ? $row['Old_Hold_7_krm'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_krm'] > 0) ? $row['Old_other_8_krm'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_krm > 0) ? $Old_clear_krm : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_krm > 0) ? ($Old_Q_clear_10_krm)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_krm'] > 0) ? ($row['Old_NQ_3_krm'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_krm ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_krm > 0) ? ($New_BF_13_krm)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_krm'] > 0) ? $row['New_M_14_krm']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_krm > 0) ? ($New_All_15_krm)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_krm > 0) ? ($New_Q_16_krm)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_krm'] > 0) ? $row['New_NQ_17_krm'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_krm'] > 0) ? $row['New_C_18_krm'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_krm'] > 0) ? $row['New_Exp_19_krm'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_krm'] > 0) ? $row['New_Dead_20_krm'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_krm'] > 0) ? $row['New_Hold_21_krm'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_krm'] > 0) ? $row['New_other_22_krm'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_krm > 0) ? $New_clear_23_krm : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_krm > 0) ? ($New_Q_bl_24_krm)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_krm'] > 0) ? ($row['S_25_krm'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_krm ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_krm ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_krm ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_krm > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_krm < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_krm ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.่ทุ่งเสลี่ยม -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua ' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.ทุ่งเสลี่ยม</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_tsl'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_tsl'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_tsl'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_tsl'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_tsl'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_tsl'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_tsl'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_tsl'] > 0) ? $row['Old_C_4_tsl'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_tsl > 0) ? $Old_Exp_5_tsl : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_tsl'] > 0) ? $row['Old_Dead_6_tsl'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_tsl'] > 0) ? $row['Old_Hold_7_tsl'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_tsl'] > 0) ? $row['Old_other_8_tsl'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_tsl > 0) ? $Old_clear_tsl : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_tsl > 0) ? ($Old_Q_clear_10_tsl)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_tsl'] > 0) ? ($row['Old_NQ_3_tsl'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_tsl ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_tsl > 0) ? ($New_BF_13_tsl)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_tsl'] > 0) ? $row['New_M_14_tsl']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_tsl > 0) ? ($New_All_15_tsl)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_tsl > 0) ? ($New_Q_16_tsl)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_tsl'] > 0) ? $row['New_NQ_17_tsl'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_tsl'] > 0) ? $row['New_C_18_tsl'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_tsl'] > 0) ? $row['New_Exp_19_tsl'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_tsl'] > 0) ? $row['New_Dead_20_tsl'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_tsl'] > 0) ? $row['New_Hold_21_tsl'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_tsl'] > 0) ? $row['New_other_22_tsl'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_tsl > 0) ? $New_clear_23_tsl : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_tsl > 0) ? ($New_Q_bl_24_tsl)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_tsl'] > 0) ? ($row['S_25_tsl'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_tsl ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_tsl ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_tsl ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_tsl > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_tsl < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_tsl ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.่บ้านแก่ง -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua ' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.บ้านแก่ง</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_bng'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_bng'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_bng'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_bng'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_bng'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_bng'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_bng'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_bng'] > 0) ? $row['Old_C_4_bng'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_bng > 0) ? $Old_Exp_5_bng : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_bng'] > 0) ? $row['Old_Dead_6_bng'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_bng'] > 0) ? $row['Old_Hold_7_bng'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_bng'] > 0) ? $row['Old_other_8_bng'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_bng > 0) ? $Old_clear_bng : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_bng > 0) ? ($Old_Q_clear_10_bng)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_bng'] > 0) ? ($row['Old_NQ_3_bng'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_bng ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_bng > 0) ? ($New_BF_13_bng)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_bng'] > 0) ? $row['New_M_14_bng']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_bng > 0) ? ($New_All_15_bng)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_bng > 0) ? ($New_Q_16_bng)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_bng'] > 0) ? $row['New_NQ_17_bng'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_bng'] > 0) ? $row['New_C_18_bng'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_bng'] > 0) ? $row['New_Exp_19_bng'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_bng'] > 0) ? $row['New_Dead_20_bng'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_bng'] > 0) ? $row['New_Hold_21_bng'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_bng'] > 0) ? $row['New_other_22_bng'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_bng > 0) ? $New_clear_23_bng : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_bng > 0) ? ($New_Q_bl_24_bng)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_bng'] > 0) ? ($row['S_25_bng'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_bng ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_bng ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_bng ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_bng > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_bng < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_bng ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.บ้านด่านลานหอย -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua ' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.บ้านด่านลานหอย</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_bdh'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_bdh'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_bdh'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_bdh'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_bdh'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_bdh'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_bdh'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_bdh'] > 0) ? $row['Old_C_4_bdh'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_bdh > 0) ? $Old_Exp_5_bdh : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_bdh'] > 0) ? $row['Old_Dead_6_bdh'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_bdh'] > 0) ? $row['Old_Hold_7_bdh'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_bdh'] > 0) ? $row['Old_other_8_bdh'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_bdh > 0) ? $Old_clear_bdh : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_bdh > 0) ? ($Old_Q_clear_10_bdh)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_bdh'] > 0) ? ($row['Old_NQ_3_bdh'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_bdh ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_bdh > 0) ? ($New_BF_13_bdh)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_bdh'] > 0) ? $row['New_M_14_bdh']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_bdh > 0) ? ($New_All_15_bdh)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_bdh > 0) ? ($New_Q_16_bdh)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_bdh'] > 0) ? $row['New_NQ_17_bdh'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_bdh'] > 0) ? $row['New_C_18_bdh'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_bdh'] > 0) ? $row['New_Exp_19_bdh'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_bdh'] > 0) ? $row['New_Dead_20_bdh'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_bdh'] > 0) ? $row['New_Hold_21_bdh'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_bdh'] > 0) ? $row['New_other_22_bdh'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_bdh > 0) ? $New_clear_23_bdh : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_bdh > 0) ? ($New_Q_bl_24_bdh)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_bdh'] > 0) ? ($row['S_25_bdh'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_bdh ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_bdh ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_bdh ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_bdh > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_bdh < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_bdh ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.บ้านไร่ -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua ' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.บ้านไร่</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_bnr'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_bnr'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_bnr'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_bnr'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_bnr'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_bnr'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_bnr'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_bnr'] > 0) ? $row['Old_C_4_bnr'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_bnr > 0) ? $Old_Exp_5_bnr : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_bnr'] > 0) ? $row['Old_Dead_6_bnr'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_bnr'] > 0) ? $row['Old_Hold_7_bnr'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_bnr'] > 0) ? $row['Old_other_8_bnr'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_bnr > 0) ? $Old_clear_bnr : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_bnr > 0) ? ($Old_Q_clear_10_bnr)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_bnr'] > 0) ? ($row['Old_NQ_3_bnr'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_bnr ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_bnr > 0) ? ($New_BF_13_bnr)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_bnr'] > 0) ? $row['New_M_14_bnr']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_bnr > 0) ? ($New_All_15_bnr)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_bnr > 0) ? ($New_Q_16_bnr)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_bnr'] > 0) ? $row['New_NQ_17_bnr'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_bnr'] > 0) ? $row['New_C_18_bnr'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_bnr'] > 0) ? $row['New_Exp_19_bnr'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_bnr'] > 0) ? $row['New_Dead_20_bnr'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_bnr'] > 0) ? $row['New_Hold_21_bnr'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_bnr'] > 0) ? $row['New_other_22_bnr'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_bnr > 0) ? $New_clear_23_bnr : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_bnr > 0) ? ($New_Q_bl_24_bnr)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_bnr'] > 0) ? ($row['S_25_bnr'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_bnr ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_bnr ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_bnr ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_bnr > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_bnr < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_bnr ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.บ้านสวน -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua ' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.บ้านสวน</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_bs'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_bs'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_bs'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_bs'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_bs'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_bs'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_bs'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_bs'] > 0) ? $row['Old_C_4_bs'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_bs > 0) ? $Old_Exp_5_bs : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_bs'] > 0) ? $row['Old_Dead_6_bs'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_bs'] > 0) ? $row['Old_Hold_7_bs'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_bs'] > 0) ? $row['Old_other_8_bs'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_bs > 0) ? $Old_clear_bs : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_bs > 0) ? ($Old_Q_clear_10_bs)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_bs'] > 0) ? ($row['Old_NQ_3_bs'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_bs ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_bs > 0) ? ($New_BF_13_bs)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_bs'] > 0) ? $row['New_M_14_bs']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_bs > 0) ? ($New_All_15_bs)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_bs > 0) ? ($New_Q_16_bs)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_bs'] > 0) ? $row['New_NQ_17_bs'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_bs'] > 0) ? $row['New_C_18_bs'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_bs'] > 0) ? $row['New_Exp_19_bs'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_bs'] > 0) ? $row['New_Dead_20_bs'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_bs'] > 0) ? $row['New_Hold_21_bs'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_bs'] > 0) ? $row['New_other_22_bs'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_bs > 0) ? $New_clear_23_bs : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_bs > 0) ? ($New_Q_bl_24_bs)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_bs'] > 0) ? ($row['S_25_bs'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_bs ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_bs ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_bs ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_bs > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_bs < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_bs ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.เมืองเก่า -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua ' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.เมืองเก่า</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_mgk'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_mgk'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_mgk'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_mgk'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_mgk'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_mgk'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_mgk'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_mgk'] > 0) ? $row['Old_C_4_mgk'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_mgk > 0) ? $Old_Exp_5_mgk : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_mgk'] > 0) ? $row['Old_Dead_6_mgk'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_mgk'] > 0) ? $row['Old_Hold_7_mgk'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_mgk'] > 0) ? $row['Old_other_8_mgk'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_mgk > 0) ? $Old_clear_mgk : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_mgk > 0) ? ($Old_Q_clear_10_mgk)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_mgk'] > 0) ? ($row['Old_NQ_3_mgk'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_mgk ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_mgk > 0) ? ($New_BF_13_mgk)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_mgk'] > 0) ? $row['New_M_14_mgk']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_mgk > 0) ? ($New_All_15_mgk)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_mgk > 0) ? ($New_Q_16_mgk)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_mgk'] > 0) ? $row['New_NQ_17_mgk'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_mgk'] > 0) ? $row['New_C_18_mgk'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_mgk'] > 0) ? $row['New_Exp_19_mgk'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_mgk'] > 0) ? $row['New_Dead_20_mgk'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_mgk'] > 0) ? $row['New_Hold_21_mgk'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_mgk'] > 0) ? $row['New_other_22_mgk'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_mgk > 0) ? $New_clear_23_mgk : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_mgk > 0) ? ($New_Q_bl_24_mgk)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_mgk'] > 0) ? ($row['S_25_mgk'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_mgk ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_mgk ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_mgk ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_mgk > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_mgk < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_mgk ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.เมืองสุโขทัย -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua ' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.เมืองสุโขทัย</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_mst'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_mst'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_mst'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_mst'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_mst'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_mst'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_mst'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_mst'] > 0) ? $row['Old_C_4_mst'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_mst > 0) ? $Old_Exp_5_mst : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_mst'] > 0) ? $row['Old_Dead_6_mst'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_mst'] > 0) ? $row['Old_Hold_7_mst'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_mst'] > 0) ? $row['Old_other_8_mst'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_mst > 0) ? $Old_clear_mst : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_mst > 0) ? ($Old_Q_clear_10_mst)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_mst'] > 0) ? ($row['Old_NQ_3_mst'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_mst ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_mst > 0) ? ($New_BF_13_mst)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_mst'] > 0) ? $row['New_M_14_mst']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_mst > 0) ? ($New_All_15_mst)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_mst > 0) ? ($New_Q_16_mst)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_mst'] > 0) ? $row['New_NQ_17_mst'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_mst'] > 0) ? $row['New_C_18_mst'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_mst'] > 0) ? $row['New_Exp_19_mst'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_mst'] > 0) ? $row['New_Dead_20_mst'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_mst'] > 0) ? $row['New_Hold_21_mst'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_mst'] > 0) ? $row['New_other_22_mst'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_mst > 0) ? $New_clear_23_mst : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_mst > 0) ? ($New_Q_bl_24_mst)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_mst'] > 0) ? ($row['S_25_mst'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_mst ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_mst ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_mst ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_mst > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_mst < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_mst ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.ศรีนคร -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua ' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.ศรีนคร</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_snk'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_snk'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_snk'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_snk'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_snk'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_snk'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_snk'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_snk'] > 0) ? $row['Old_C_4_snk'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_snk > 0) ? $Old_Exp_5_snk : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_snk'] > 0) ? $row['Old_Dead_6_snk'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_snk'] > 0) ? $row['Old_Hold_7_snk'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_snk'] > 0) ? $row['Old_other_8_snk'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_snk > 0) ? $Old_clear_snk : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_snk > 0) ? ($Old_Q_clear_10_snk)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_snk'] > 0) ? ($row['Old_NQ_3_snk'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_snk ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_snk > 0) ? ($New_BF_13_snk)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_snk'] > 0) ? $row['New_M_14_snk']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_snk > 0) ? ($New_All_15_snk)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_snk > 0) ? ($New_Q_16_snk)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_snk'] > 0) ? $row['New_NQ_17_snk'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_snk'] > 0) ? $row['New_C_18_snk'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_snk'] > 0) ? $row['New_Exp_19_snk'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_snk'] > 0) ? $row['New_Dead_20_snk'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_snk'] > 0) ? $row['New_Hold_21_snk'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_snk'] > 0) ? $row['New_other_22_snk'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_snk > 0) ? $New_clear_23_snk : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_snk > 0) ? ($New_Q_bl_24_snk)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_snk'] > 0) ? ($row['S_25_snk'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_snk ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_snk ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_snk ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_snk > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_snk < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_snk ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.ศรีสัชนาลัย -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua ' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.ศรีสัชนาลัย</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_snl'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_snl'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_snl'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_snl'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_snl'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_snl'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_snl'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_snl'] > 0) ? $row['Old_C_4_snl'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_snl > 0) ? $Old_Exp_5_snl : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_snl'] > 0) ? $row['Old_Dead_6_snl'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_snl'] > 0) ? $row['Old_Hold_7_snl'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_snl'] > 0) ? $row['Old_other_8_snl'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_snl > 0) ? $Old_clear_snl : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_snl > 0) ? ($Old_Q_clear_10_snl)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_snl'] > 0) ? ($row['Old_NQ_3_snl'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_snl ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_snl > 0) ? ($New_BF_13_snl)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_snl'] > 0) ? $row['New_M_14_snl']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_snl > 0) ? ($New_All_15_snl)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_snl > 0) ? ($New_Q_16_snl)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_snl'] > 0) ? $row['New_NQ_17_snl'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_snl'] > 0) ? $row['New_C_18_snl'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_snl'] > 0) ? $row['New_Exp_19_snl'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_snl'] > 0) ? $row['New_Dead_20_snl'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_snl'] > 0) ? $row['New_Hold_21_snl'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_snl'] > 0) ? $row['New_other_22_snl'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_snl > 0) ? $New_clear_23_snl : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_snl > 0) ? ($New_Q_bl_24_snl)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_snl'] > 0) ? ($row['S_25_snl'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_snl ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_snl ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_snl ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_snl > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_snl < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_snl ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.ศรีสำโรง -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua ' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.ศรีสำโรง</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_ssr'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_ssr'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_ssr'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_ssr'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_ssr'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_ssr'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_ssr'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_ssr'] > 0) ? $row['Old_C_4_ssr'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_ssr > 0) ? $Old_Exp_5_ssr : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_ssr'] > 0) ? $row['Old_Dead_6_ssr'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_ssr'] > 0) ? $row['Old_Hold_7_ssr'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_ssr'] > 0) ? $row['Old_other_8_ssr'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_ssr > 0) ? $Old_clear_ssr : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_ssr > 0) ? ($Old_Q_clear_10_ssr)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_ssr'] > 0) ? ($row['Old_NQ_3_ssr'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_ssr ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_ssr > 0) ? ($New_BF_13_ssr)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_ssr'] > 0) ? $row['New_M_14_ssr']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_ssr > 0) ? ($New_All_15_ssr)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_ssr > 0) ? ($New_Q_16_ssr)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_ssr'] > 0) ? $row['New_NQ_17_ssr'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_ssr'] > 0) ? $row['New_C_18_ssr'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_ssr'] > 0) ? $row['New_Exp_19_ssr'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_ssr'] > 0) ? $row['New_Dead_20_ssr'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_ssr'] > 0) ? $row['New_Hold_21_ssr'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_ssr'] > 0) ? $row['New_other_22_ssr'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_ssr > 0) ? $New_clear_23_ssr : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_ssr > 0) ? ($New_Q_bl_24_ssr)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_ssr'] > 0) ? ($row['S_25_ssr'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_ssr ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_ssr ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_ssr ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_ssr > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_ssr < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_ssr ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.สวรรคโลก -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua ' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.สวรรคโลก</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_swl'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_swl'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_swl'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_swl'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_swl'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_swl'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_swl'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_swl'] > 0) ? $row['Old_C_4_swl'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_swl > 0) ? $Old_Exp_5_swl : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_swl'] > 0) ? $row['Old_Dead_6_swl'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_swl'] > 0) ? $row['Old_Hold_7_swl'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_swl'] > 0) ? $row['Old_other_8_swl'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_swl > 0) ? $Old_clear_swl : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_swl > 0) ? ($Old_Q_clear_10_swl)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_swl'] > 0) ? ($row['Old_NQ_3_swl'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_swl ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_swl > 0) ? ($New_BF_13_swl)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_swl'] > 0) ? $row['New_M_14_swl']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_swl > 0) ? ($New_All_15_swl)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_swl > 0) ? ($New_Q_16_swl)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_swl'] > 0) ? $row['New_NQ_17_swl'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_swl'] > 0) ? $row['New_C_18_swl'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_swl'] > 0) ? $row['New_Exp_19_swl'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_swl'] > 0) ? $row['New_Dead_20_swl'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_swl'] > 0) ? $row['New_Hold_21_swl'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_swl'] > 0) ? $row['New_other_22_swl'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_swl > 0) ? $New_clear_23_swl : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_swl > 0) ? ($New_Q_bl_24_swl)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_swl'] > 0) ? ($row['S_25_swl'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_swl ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_swl ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_swl ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_swl > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_swl < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_swl ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.ท่าฉนวน -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua ' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.ท่าฉนวน</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_tcn'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_tcn'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_tcn'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_tcn'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_tcn'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_tcn'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_tcn'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_tcn'] > 0) ? $row['Old_C_4_tcn'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_tcn > 0) ? $Old_Exp_5_tcn : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_tcn'] > 0) ? $row['Old_Dead_6_tcn'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_tcn'] > 0) ? $row['Old_Hold_7_tcn'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_tcn'] > 0) ? $row['Old_other_8_tcn'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_tcn > 0) ? $Old_clear_tcn : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_tcn > 0) ? ($Old_Q_clear_10_tcn)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_tcn'] > 0) ? ($row['Old_NQ_3_tcn'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_tcn ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_tcn > 0) ? ($New_BF_13_tcn)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_tcn'] > 0) ? $row['New_M_14_tcn']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_tcn > 0) ? ($New_All_15_tcn)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_tcn > 0) ? ($New_Q_16_tcn)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_tcn'] > 0) ? $row['New_NQ_17_tcn'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_tcn'] > 0) ? $row['New_C_18_tcn'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_tcn'] > 0) ? $row['New_Exp_19_tcn'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_tcn'] > 0) ? $row['New_Dead_20_tcn'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_tcn'] > 0) ? $row['New_Hold_21_tcn'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_tcn'] > 0) ? $row['New_other_22_tcn'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_tcn > 0) ? $New_clear_23_tcn : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_tcn > 0) ? ($New_Q_bl_24_tcn)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_tcn'] > 0) ? ($row['S_25_tcn'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_tcn ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_tcn ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_tcn ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_tcn > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_tcn < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_tcn ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.เมืองบางขลัง -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua ' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.เมืองบางขลัง</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_mbk'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_mbk'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_mbk'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_mbk'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_mbk'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_mbk'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_mbk'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_mbk'] > 0) ? $row['Old_C_4_mbk'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_mbk > 0) ? $Old_Exp_5_mbk : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_mbk'] > 0) ? $row['Old_Dead_6_mbk'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_mbk'] > 0) ? $row['Old_Hold_7_mbk'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_mbk'] > 0) ? $row['Old_other_8_mbk'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_mbk > 0) ? $Old_clear_mbk : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_mbk > 0) ? ($Old_Q_clear_10_mbk)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_mbk'] > 0) ? ($row['Old_NQ_3_mbk'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_mbk ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_mbk > 0) ? ($New_BF_13_mbk)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_mbk'] > 0) ? $row['New_M_14_mbk']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_mbk > 0) ? ($New_All_15_mbk)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_mbk > 0) ? ($New_Q_16_mbk)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_mbk'] > 0) ? $row['New_NQ_17_mbk'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_mbk'] > 0) ? $row['New_C_18_mbk'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_mbk'] > 0) ? $row['New_Exp_19_mbk'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_mbk'] > 0) ? $row['New_Dead_20_mbk'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_mbk'] > 0) ? $row['New_Hold_21_mbk'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_mbk'] > 0) ? $row['New_other_22_mbk'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_mbk > 0) ? $New_clear_23_mbk : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_mbk > 0) ? ($New_Q_bl_24_mbk)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_mbk'] > 0) ? ($row['S_25_mbk'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_mbk ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_mbk ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_mbk ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_mbk > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_mbk < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_mbk ?>
</div>
	 </td> <!--  -->
 </tr>
	  
	  
<?php
    }
} catch (PDOException $e) {
    // Handle errors
    echo 'Query failed: ' . $e->getMessage();
    exit;
}
?>
  </tbody>
</table>

</div>    
&nbsp;&nbsp;<button class="btn btn-primary btn-lg mb-4" onClick="do_print()"> พิมพ์รายงาน </button>
<p style="font-size: 18px">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;เป้าหมาย ตร. ให้จับกุม หมายเก่า 3 % &nbsp;&nbsp;&nbsp;&nbsp; รวมหมายเก่า+หมายใหม่ 6.5 %</p>
    <hr>
<!--<p>&nbsp;&nbsp;<a href="#catch_old_w.php" class="btn btn-secondary btn-lg mb-4" >จับหมายเก่า</a>&nbsp;&nbsp;&nbsp;&nbsp;<a href="#catch_new_w.php" class="btn btn-secondary btn-lg mb-4" >จับหมายใหม่</a>&nbsp;&nbsp;</p>-->
<p>&nbsp;</p>
<script>
function dates_change()  // คำสั่งเปลี่ยนเดือน ปี
{
    var ms = $("#mi_cl_month option:selected").val();
    var ys = $("#mi_cl_year option:selected").val();
    window.location = "/Activity/Show_Activity7.php?&month=" + ms + "&year=" + ys + "&rnd=" + Math.random();
}
  </script>
</body>

</html>
<!-- script สำหรับส่งไปพิมพ์รายงาน  -->
<script>
    
    function do_print()
    {
        var ms = $("#mi_cl_month option:selected").val();
        var ys = $("#mi_cl_year option:selected").val();
        var newW = window.open("/Activity/Show_Activity7_print.php?&month=" + ms + "&year=" + ys + "&rnd=" + Math.random(), "_print", "menubar=no,titlebar=no,toolbar=no,status=no");
        newW.print();
        //window.print();
    }
    
</script>
