<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../Alert.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);
$ip_address = $_SERVER['REMOTE_ADDR'];
$action = 'Save Charge'; // You can set the action to a suitable value
$data = str_replace("'", "", compact_array($_POST));
log_activity($acc, $action, $data, $ip_address); // ใช้ function ในไฟล์ Condb.php


/*echo '<pre>';
print_r($_POST);
echo '</pre>';

echo '<hr';

print_r($_FILES);
exit();*/

// Check if the form is submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
  $chargeItem = $_POST['charge_direct']; // Get the selected charge item from the form

  // Insert the charge into the database
  $stmt = $pdo->prepare("INSERT INTO DirectoryDetention22_charge (charge_direct) VALUES (:charge_direct)");
  $stmt->bindValue(':charge_direct', $chargeItem); // Use the selected charge item
  $stmt->execute();

  // Redirect to the page after successful addition
  header("Location: https://invest.watchman1.com/Bansuan/Add_Directory.php"); // Change "index.php" to your desired page
  exit();
}

?>