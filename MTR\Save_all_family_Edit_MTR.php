<?php
include '../Condb.php';
include '../users.inc.php';

// EDIT > AID >> to update
$fm_cl_aid = $_POST[ 'fm_cl_aid' ];
$fm_cl_idcard = $_POST[ 'fm_cl_idcard' ];
$fm_cl_status = $_POST[ 'fm_cl_status' ];
$fm_cl_idcard_family = $_POST[ 'fm_cl_idcard_family' ];
$fm_cl_prefix = $_POST[ 'fm_cl_prefix' ];
$fm_cl_sex = $_POST[ 'fm_cl_sex' ];
$fm_cl_name = $_POST[ 'fm_cl_name' ];
$fm_cl_surname = $_POST[ 'fm_cl_surname' ];
$fm_cl_nickname = $_POST[ 'fm_cl_nickname' ];
$fm_cl_birthday2 = $_POST[ 'fm_cl_birthday2' ];
$fm_cl_father = $_POST[ 'fm_cl_father' ];
$fm_cl_father_pid = $_POST[ 'fm_cl_father_pid' ];
$fm_cl_mother = $_POST[ 'fm_cl_mother' ];
$fm_cl_mother_pid = $_POST[ 'fm_cl_mother_pid' ];
$fm_cl_spouse = $_POST[ 'fm_cl_spouse' ];

//$fm_cl_birthday2 = thai_date_2_eng($fm_cl_birthday);

// save Image
$file1 = $_FILES[ 'fm_cl_image' ][ 'tmp_name' ];
if ( is_uploaded_file( $file1 ) ) {
  $fm_cl_image = "/policeinnopolis/uploaded/Image1/" . basename( $_FILES[ 'fm_cl_image' ][ 'name' ] );
	
  move_uploaded_file( $file1, "../" . $fm_cl_image );  
} else {
  $fm_cl_image = '';
}


// $sql2 >> insert ไปลงตาราง >> personal
// check exists data in personal เช็คว่ามีข้อมูลในตาราง Personal หรือยัง ถ้ามีแล้ว ไม่ต้องทำอะไร
$sql4 = "SELECT * FROM wm_tb_personal WHERE ps_cl_idcard='$fm_cl_idcard_family' ";
//echo $sql4;
$res4 = mysqli_query($conn, $sql4);
// มีข้อมูลแล้ว
if($row4 = mysqli_fetch_array($res4)) {
   
}
// เปนข้อมูลใหม่ >>
else {
    $sql4 = "UPDATE wm_tb_personal SET " .
            "ps_cl_prefix = '$fm_cl_prefix', " .
            "ps_cl_sex = '$fm_cl_sex', " .
            "ps_cl_name = '$fm_cl_name', " .
            "ps_cl_surname = '$fm_cl_surname', " .
            "ps_cl_nickname = '$fm_cl_nickname', " .
            "ps_cl_birthday2 = '$fm_cl_birthday2', " .
            "ps_cl_father = '$fm_cl_father', " .
            "ps_cl_father_pid = '$fm_cl_father_pid', " .
            "ps_cl_mother = '$fm_cl_mother', " .
            "ps_cl_mother_pid = '$fm_cl_mother_pid', " .
            "ps_cl_image = '$fm_cl_image' " .
            "WHERE ps_cl_idcard = '$fm_cl_idcard_family' ";
    mysqli_query($conn, $sql4);
    
   // echo( $sql3 );
//    echo mysqli_error($conn);
}
//


$sql = "UPDATE wm_tb_family SET " .
		"fm_cl_idcard = '$fm_cl_idcard', ".
		"fm_cl_status = '$fm_cl_status', ".
		"fm_cl_idcard_family = '$fm_cl_idcard_family', " .
		"fm_cl_prefix = '$fm_cl_prefix', " .
		"fm_cl_sex = '$fm_cl_sex', " .
		"fm_cl_name = '$fm_cl_name', " .
		"fm_cl_surname = '$fm_cl_surname', " .
		"fm_cl_nickname = '$fm_cl_nickname', " .
        "fm_cl_birthday2 = '$fm_cl_birthday2', " .
		"fm_cl_father = '$fm_cl_father', " .
		"fm_cl_father_pid = '$fm_cl_father_pid', " .
		"fm_cl_mother = '$fm_cl_mother', " .
		"fm_cl_mother_pid = '$fm_cl_mother_pid', " .
		"fm_cl_spouse = '$fm_cl_spouse' ";

	if($fm_cl_image !== '')
	{
		$sql =  $sql . ", fm_cl_image = '$fm_cl_image' ";
	}

	$sql = $sql . " WHERE fm_cl_aid = '$fm_cl_aid' ";
	
	

$result=mysqli_query($conn,$sql);
if($result){
	echo "<script>alert('บันทึกข้อมูลเรียบร้อย');</script>";	
}else{
	echo "<script>alert(\"บันทึกข้อมูลไม่สำเร็จ\\n" . print_r(mysqli_error($conn), true) . "\");</script>";
}

// เก็บข้อมูล Action สำหรับ EDIT
$inputs = str_replace("'", "", compact_array($_POST));
$prev = str_replace("'", "", implode(',', $row_0));

$acc = $user['account'];
$sql3 = "INSERT wm_tb_user_action (at_cl_user,at_cl_date,at_cl_action,at_cl_input,at_cl_prev) VALUES('$acc',CURRENT_TIMESTAMP,'Edit family Mueang {$fm_cl_idcard}', '$inputs', '{$prev}')";
mysqli_query($conn, $sql3);
//


mysqli_close($conn);
echo "<script>window.location='Show_All_SMIV_MTR.php?pcid=" . $fm_cl_idcard . "&page=family_MTR';</script>";
?>

