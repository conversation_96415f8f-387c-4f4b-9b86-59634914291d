<?php
include '../Condb.php'; //PDO
include '../users.inc.php';
include '../right_user.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// สำหรับเลือก จังหวัด อำเภอ ตำบล
include("../config.inc.php");
include("../classes/class.database.inc.php");

// สำหรับเลือก จังหวัด อำเภอ ตำบล
$conn2 = get_connection(); // connect to mySQLDB

// สำหรับเลือกสถานีตำรวจทั่วประเทศ
$region = isset($_GET['region']) ? $_GET['region'] : 0; // sukhothai
$provincial = isset($_GET['provincial']) ? $_GET['provincial'] : 0; // sukhothai
//$station = isset($_GET['station']) ? $_GET['station'] : 0; // sukhothai

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>เพิ่มข้อมูลการตรวจสอบอาวุธปืนของกลาง</title>
<script src="/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 18px;
    }
input::placeholder {
  color: red;
}
.form-control::placeholder {
  color: sandybrown;
}
</style>
<script src="/jquery-ui-1.12.1/jquery-ui.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/jquery-ui-1.12.1/jquery-ui.css">
<link rel="stylesheet" href="/js/datetimepicker2.5.1/jquery.datetimepicker.min.css"> 
    
<!-- สำหรับวันที่ แบบใหม่  -->
<link rel="stylesheet" href="../datepicker_th/jquery.datetimepicker.css" type="text/css">
<script src="../datepicker_th/jquery-1.8.3.min.js"></script>  
<script src="../datepicker_th/jquery.datetimepicker.full.js"></script>
</head>

<body>
	
	<div class="container">
	<div class="row">
	<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> เพิ่มข้อมูลการตรวจสอบอาวุธปืนของกลาง </div>
	<form action="Save_confidential_gun.php" method="POST" enctype="multipart/form-data" class="body">
	<span style="color: #1203F8">
        
        <label hidden ="hidden">ลำดับ</label>
		            <input type = "text" name = "aid_conf_gun" class="form-control" hidden ="hidden" >
        
        <br>
        <label>สถานีตำรวจ <span style="color: #F90004">* จำเป็น</span></label><br>
                <!-- เลือกสถานีตำรวจ -->
        <label> บช. </label><br>
        <select name="region" id="region" onChange="do_region_change()" class="form-control" Required >
                <option value="">&lt;&lt; เลือก บช. &gt;&gt;</option>
                <?php
			  	$res_re = $conn2->query("SELECT * FROM `wm_tb_police_region` 
                                            ORDER BY `name_region` ASC");
			     $selected = '';
			     while($row_re = $conn2->fetch_row($res_re)) 
                     {
                      $code_region = $row_re['code_region'];
                      $name_region = $row_re['name_region'];
                      // set default provionce to 64 >> sukhothai
                          if($code_region == $region) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$code_region' $selected> $name_region </option>\n";
                     }
			  
			  ?>
              </select>
        
        <label> บก. </label><br>
        <select name="provincial" id="provincial" onChange="do_provincial_change()" class="form-control" >
        <option value="0">&lt;&lt; เลือก บก. &gt;&gt;</option>
                <?php
                if($region > 0)
                {
                    $res_prov = $conn2->query("SELECT * FROM `wm_tb_police_provincial` 
                                                WHERE `region_code`='$region' 
                                                ORDER BY `provincial_code` ASC");
                    $selected = '';
                  while($res_prov = $conn2->fetch_row($res_prov)) 
                  {
                      $provincial_code = $res_prov['provincial_code']; // 
                      $provincial = $res_prov['provincial'];
                      // set default provionce to 64 >> sukhothai
                          if($provincial_code == $provincial) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$provincial_code' $selected> $provincial </option>\n";
                  }
                }	
                ?>
              </select>

         <label> สน./สภ. </label>
        <br>
        <select name="station" id="station" class="form-control" >
                <option value="0">&lt;&lt; เลือก สน./สภ. &gt;&gt;</option>
                <?php
	           if($provincial > 0)
                {
                    $res_st = $conn2->query("SELECT * FROM `wm_tb_police_station2` 
                                                WHERE provincial_code='$provincial_code'
                                                ORDER BY `station_code` ASC");
                    $selected = '';
                  while($row_st = $conn2->fetch_row($res_st)) 
                  {
                      $station_code = $row_st['station_code']; // 
                      $station_name = $row_st['station_name'];
                      // set default provionce to 64 >> sukhothai
                          if($station_code == $station) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$station_code' $selected> $station_name </option>\n";
                  }
                }	
                ?>                                                                     
              </select>
		<br>
        
                    <label class="body">เลขคดี</label>
                    <input type = "text" name = "case_no" class="form-control" placeholder="เลขคดี" >

                    <label class="body">เลขยึด</label>
                    <input type = "text" name = "inspect_no" class="form-control" placeholder="เลขยึด" >

                    <label class="body"> รายการของกลาง </label>
                    <textarea class="form-control" id="gun_treasure_detail" name="gun_treasure_detail" rows="5" placeholder="รายการของกลาง" ></textarea><br>

					<label>ชื่อพนักงานสอบสวน</label>
					<?php
					if ($station == 6707) {
						echo '<select id="inquiry_official" name="inquiry_official" class="form-select form-select-sm" placeholder="ระบุชื่อพนักงานสอบสวน">';
						echo '<option value="" selected> </option>';
						 $bs_inq = $pdo->prepare("SELECT * FROM `police_name_bs_inqinquiry` ORDER BY `police_name_bs_inqinquiry`.`aid_bs_inq` ASC");
							try{
								$bs_inq->execute();
							}catch(PDOException $e){
								echo 'Query $bs_inq Failed: '. $e->getMessage();
							}

							while($row_inq = $bs_inq->fetch(PDO::FETCH_ASSOC))
							{
								$aid_bs_inq = htmlspecialchars($row_inq['aid_bs_inq'], ENT_QUOTES, 'UTF-8');
								$bs_inq_name = htmlspecialchars($row_inq['bs_inq_name'], ENT_QUOTES, 'UTF-8');
							echo "<option value='$bs_inq_name'>$bs_inq_name</option>";
							}
						echo '</select>';
						} else {
							// User's station is not 6707, display a text input
							echo '<input type="text" id="inquiry_official" name="inquiry_official" class="form-control" placeholder="ระบุชื่อพนักงานสอบสวน" >';
						}
					?>
					<br>
						
                    <label class="body">ประเภทปืน</label> <!-- สำหรับ นับยอดใส่ ช่อง (1) = (2)+(13) Count(*) From ...WHERE confidential_gun -->
                    <select id="confidential_gun" name="confidential_gun" class="form-select form-select-sm"  placeholder="ประเภทปืน">
                        <option value="" selected> </option>
                        <option value="1" >ปืนของกลางที่มีอัตลักษณ์ (ปืนที่ผลิตโดยโรงงาน มีแบรนด์ มีคุณภาพการผลิต)</option> <!-- สำหรับเข้าช่อง (2) AND confidential_gun='1'-->
                        <option value="2" >ปืนของกลางที่ไม่มีมีอัตลักษณ์ (ปืนที่ไม่ได้ผลิตโดยโรงงาน ไม่มีแบรนด์ ไม่มีคุณภาพการผลิต หรือปืนไทยประดิษฐ์ เป็นต้น)</option> <!-- สำหรับเข้าช่อง (13) AND confidential_gun='2' -->
                    </select><br>

            <b class="body">ปืนของกลางที่มีอัตลักษณ์ <br>(ปืนที่ผลิตโดยโรงงาน มีแบรนด์ มีคุณภาพการผลิต)</b><br>
        
                    <label class="body">ผลคดีและการดำเนินการ</label><br>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<label class="body"><input name="unique_guns" id="unique_guns1" type="radio" value="1" /> ศาลสั่งริบ คดีถึงที่สุด </label><br>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<label class="body"><input name="unique_guns" id="unique_guns2" type="radio" value="2" /> ศาลสั่งคืน คดีถึงที่สุด </label><br>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<label class="body"><input name="unique_guns" id="unique_guns3" type="radio" value="3" /> คดียังไม่ถึงที่สุด </label><br><br>
        
                    <label class="body">กรณีไม่ปรากฎว่าผู้ใดเป็นผู้กระทำความผิดหรือคดีที่รู้ตัวแต่ยังจับตัวไม่ได้</label><br>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<label class="body"><input name="unique_guns_unknow" id="unique_guns_unknow1" type="checkbox" value="1" /> ขาดอายุความคดีอาญา </label><br><!-- สำหรับเข้าช่อง (6) out_limit='4'-->
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<label class="body"><input name="unique_guns_unknow" id="unique_guns_unknow2" type="checkbox" value="2" /> ยังอยู่ในอายุความคดีอาญา </label><br><br><!-- สำหรับเข้าช่อง (7) -->

                    <label class="body" style="color: black">กรณีศาลสั่งริบ คดีถึงที่สุดแล้ว</label><br>
                    <label class="body">ส่งกองสรรพวุธ</label><br>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<label class="body"><input name="sanpawut" id="sanpawut1" type="checkbox" value="1" /> ส่งกองสรรพวุธแล้ว (มีทะเบียน) </label><br><!-- สำหรับเข้าช่อง (6) out_limit='4'-->
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<label class="body"><input name="sanpawut" id="sanpawut2" type="checkbox" value="2" /> ส่งกองสรรพวุธแล้ว (ไม่มีทะเบียน) </label><br><!-- สำหรับเข้าช่อง (6) out_limit='4'-->
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<label class="body"><input name="sanpawut" id="sanpawut3" type="checkbox" value="3" /> รอส่งกองสรรพวุธ </label><br><br><!-- สำหรับเข้าช่อง (6) out_limit='4'-->
                
                    <label class="body">ประสานนายอำเภอเพิกถอนทะเบียน</label><br>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<label class="body"><input name="DistrictChief_co" id="DistrictChief_co1" type="checkbox" value="1" /> ประสานแล้ว </label><br>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<label class="body"><input name="DistrictChief_co" id="DistrictChief_co2" type="checkbox" value="2" /> ยังไม่ได้ประสาน </label><br><br>
        
                    <label class="body">นายอำเภอเพิกถอนทะเบียน</label><br>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<label class="body"><input name="DistrictChief_revoke" id="DistrictChief_revoke1" type="checkbox" value="1" /> เพิกถอนทะเบียนแล้ว </label><br>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<label class="body"><input name="DistrictChief_revoke" id="DistrictChief_revoke2" type="checkbox" value="2" /> ยังไม่ได้เพิกถอน </label><br><br>

        <!-- สำหรับเข้าช่อง (12) = (11)/(10) * 100-->
        
                  <!--  ===============================  -->
        
            <b class="body">ปืนของกลางที่ไม่มีมีอัตลักษณ์ <br>(ปืนที่ไม่ได้ผลิตโดยโรงงาน ไม่มีแบรนด์ ไม่มีคุณภาพการผลิต หรือปืนไทยประดิษฐ์ เป็นต้น)</b><br>
        
                    <label class="body">ผลคดีและการดำเนินการ</label><br>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<label class="body"><input name="no_identify" id="no_identify1" type="radio" value="1" /> ศาลสั่งริบ คดีถึงที่สุด </label><br>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<label class="body"><input name="no_identify" id="no_identify3" type="radio" value="3" /> ศาลสั่งคืน คดีถึงที่สุด </label><br>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<label class="body"><input name="no_identify" id="no_identify2" type="radio" value="2" /> คดียังไม่ถึงที่สุด </label><br><br>
                    
                    <label class="body">กรณีไม่ปรากฎว่าผู้ใดเป็นผู้กระทำความผิดหรือคดีที่รู้ตัวแต่ยังจับตัวไม่ได้</label><br>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<label class="body"><input name="no_identify_unknow" id="no_identify_unknow1" type="checkbox" value="1" /> ขาดอายุความคดีอาญา </label><br><!-- สำหรับเข้าช่อง (6) out_limit='4'-->
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<label class="body"><input name="no_identify_unknow" id="no_identify_unknow2" type="checkbox" value="2" /> ยังอยู่ในอายุความคดีอาญา </label><br><br>

                     <label class="body">กรณีศาลสั่งริบ คดีถึงที่สุดแล้ว</label><br>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<label class="body"><input name="destroy_unknow" id="destroy_unknow1" type="checkbox" value="1" /> ทำลายแล้ว </label><br><!-- สำหรับเข้าช่อง (18) destroy_unknow -->
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<label class="body"><input name="destroy_unknow" id="destroy_unknow2" type="checkbox" value="2" /> รอทำลาย </label><br><br>


        <!-- (19) = (18)/[ (14)+(16) ] * 100 -->


                    <b class="body">สถานะการดำเนินการ</b><br>
                    <label class="body">ดำเนินการอยู่ในขั้นตอนใด</label>
                    <select id="status" name="status" class="form-select form-select-sm"  placeholder="การดำเนินการ">
                        <option value="" selected> </option>
                        <option value="1">ยังอยู่ในคลัง รอคำสั่งศาล</option>
                        <option value="2">ยังอยู่ในคลัง อยู่ระหว่างประสานเจ้าของมารับคืน</option>
                        <option value="3">คืนเจ้าของแล้ว</option>
                        <option value="4">ยังอยู่ในคลัง รอทำลาย</option>
                        <option value="5">ทำลายแล้ว</option>
                        <option value="6">ส่งสรรพาวุธแล้ว</option>
                    </select><br>
        
                    <b class="body">ระบุผู้บันทึก</b><br>
                    <label>เจ้าหน้าที่บันทึก</label>
					<?php if ($station == 6707) {
					echo '<select id="record_name" name="record_name" class="form-select form-select-sm"  placeholder="เจ้าหน้าที่บันทึก">';
					echo '<option value="" selected> </option>
                        <option value="ส.ต.ต.ณพดล โคกสันเที๊ยะ">ส.ต.ต.ณพดล โคกสันเที๊ยะ</option>
                        <option value="ส.ต.ต.พิเชษฐ์ วิชิตนาค">ส.ต.ต.พิเชษฐ์ วิชิตนาค</option>';
					echo '</select>';
					} else {
				// User's station is not 6707, display a text input
						echo '<input type="text" id="record_name" name="record_name" class="form-control" placeholder="เจ้าหน้าที่บันทึก" >';
						}
					?>
                        
                    <br>
        
                     <label>วันที่ <span style="color: #F90004">* จำเป็น</span></label>
                       <p><input type="text" name="record_date" id="datepicker" value="" placeholder="เพื่อข้อมูลถูกต้อง กรุณาเลือกจากปฎิทินด้านล่าง" class="form-control" style="width:250px;" autocomplete="off" required></p>
                        <script type="text/javascript"> 
                            $(function(){

                                $.datetimepicker.setLocale('th'); // ต้องกำหนดเสมอถ้าใช้ภาษาไทย และ เป็นปี พ.ศ.
                                // กรณีใช้แบบ input
                                $("#datepicker").datetimepicker({
                                    timepicker:false,
                                    format:'Y-m-d',  // กำหนดรูปแบบวันที่ ที่ใช้ เป็น 00-00-0000			
                                    lang:'th',  // ต้องกำหนดเสมอถ้าใช้ภาษาไทย และ เป็นปี พ.ศ.
                                    onSelectDate:function(dp,$input){
                                        var yearT=new Date(dp).getFullYear()-0;  
                                        var yearTH=yearT;  // ถ้าจะให้แสดงเป็น พ.ศ. ให้เป็น yearT+543
                                        var fulldate=$input.val();
                                        var fulldateTH=fulldate.replace(yearT,yearTH);
                                        $input.val(fulldateTH);
                                    },
                                });       
                                // กรณีใช้กับ input ต้องกำหนดส่วนนี้ด้วยเสมอ เพื่อปรับปีให้เป็น ค.ศ. ก่อนแสดงปฏิทิน
                                $("#datepicker").on(function(e){
                                    var dateValue=$(this).val();
                                    if(dateValue!=""){
                                            var arr_date=dateValue.split("-"); // ถ้าใช้ตัวแบ่งรูปแบบอื่น ให้เปลี่ยนเป็นตามรูปแบบนั้น
                                            // ในที่นี้อยู่ในรูปแบบ 00-00-0000 เป็น d-m-Y  แบ่งด่วย - ดังนั้น ตัวแปรที่เป็นปี จะอยู่ใน array
                                            //  ตัวที่สอง arr_date[2] โดยเริ่มนับจาก 0 
                                            dateValue=dateValue.replace(arr_date[0],yearT);
                                            $(this).val(dateValue);													
                                    }		
                                });


                            });
                            </script>
                        <!-- สำหรับ วันที่ แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ -->

                    <div class="mb-3">
                        <label for="formFileMultiple" class="form-label">เอกสาร (แนะนำ PDF ขนาดไม่เกิน 5 MB)</label>
                        <input class="form-control" type="file" id="conf_gun_file" name="conf_gun_file" multiple>
                    </div>

                    <p>
                    <br>
                        <input type="submit" value="บันทึกข้อมูล" class="btn btn-success" >
                        <td> <a href="/WatchmanData/Show_gun_treasure_all.php?rnd=<?= rand(); ?>" class="btn btn-warning" >ยกเลิก</a> </td>
                    </p>
                    <br>
                    <br>
	</form>
	</div>
	</div>
	</div>
<script>
// ฟังก์ชั่น เปลี่ยนภาค (บช.) เป็น ภ.จว. (บก.)
function do_region_change()
{
	var sel_region = document.getElementById("region");		
	var code_region = sel_region.options[sel_region.selectedIndex].value;

	$.get("../ajax/get_provincial.php?region=" + code_region, 
		  function(data, status){
				//alert("Data: " + data + "\nStatus: " + status);
				// jQuery style						
				$('#provincial').find('option').remove().end();
				var data = $.trim(data);
				if(data == '') {
					alert("ไม่พบข้อมูล บก. !");
					return;
				}

					//.append('<option value="whatever">text</option>').val('whatever');
				var datas = data.split(';');
				for(var i=0; i<datas.length; i++) {
					if(datas[i] == '') continue;
					datas[i] = datas[i].split(':');
					var code_region = datas[i][0];
					var name_region = datas[i][1];
					$('#provincial').append('<option value="'+ code_region+'">' + name_region + '</option>');
				}
                // Trigger the change event on the amphur dropdown so the tambon dropdown will be populated     
                $('#provincial').trigger('change');
		});
}
    //เลือก สถานี จากจังหวัด
function do_provincial_change()
{
	var sel_provincial = document.getElementById("region");		
	var provincial_code = sel_provincial.options[sel_provincial.selectedIndex].value;
    
    var sel_provincial2 = document.getElementById("provincial");		
	var station_code = sel_provincial2.options[sel_provincial2.selectedIndex].value;

	$.get("../ajax/get_station.php?region=" + provincial_code + "&provincial=" + station_code, 
			  function(data, status){
					//alert("Data: " + data + "\nStatus: " + status);
					// jQuery style						
					$('#station').find('option').remove().end();
					var data = $.trim(data);
					if(data == '') {
						alert("ไม่พบข้อมูลสถานี !");
						return;
					}

						//.append('<option value="whatever">text</option>').val('whatever');
					var datas = data.split(';');
					for(var i=0; i<datas.length; i++) {
						if(datas[i] == '') continue;
						datas[i] = datas[i].split(':');
						var station_code = datas[i][0];
						var station_name = datas[i][1];
						$('#station').append('<option value="'+ station_code+'">' + station_name + '</option>');
					}
			});
}    
</script>
        
</body>
</html>
