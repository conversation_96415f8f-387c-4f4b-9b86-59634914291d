<!doctype html>
<html>
<head>
<meta charset="utf-8">
<title>Code สำหรับ เผื่อจะใช้งาน</title>
</head>

<body>
<?php
// แปลงปีในเซฟลงฐานข้อมูลให้ถูกต้อง
// convert Thai dates to eng
if($on_cl_date != '') {
	if(strpos($on_cl_date, '/') > 0) {
    	$dates = explode('/', $on_cl_date);	// d/m/y
	}
	elseif(strpos($on_cl_date, '-') > 0) {
		$date = explode('-', $on_cl_date);	// y-m-d
		$dates = array($date[2], $date[1], $date[0]);	// d/m/Y
	}
	// thai dates
	if(substr(''.$dates[2],0,2) ==='25') {
		$on_cl_date = ($dates[2]-543) . '-' . $dates[1] . '-' . $dates[0];
	}
	// eng dates
	else {
    	$on_cl_date = $dates[2] . '-' . $dates[1] . '-' . $dates[0];
	}
}
    
?>
<?php 

//echo '<pre>';
//print_r($result);
//echo '</pre>';

//echo '<hr>';
//
//echo '<pre>';
//var_dump($row);
//echo '</pre>';
//
//exit();    
    ?>
    

    <!-- Date picker ปี ค.ศ.-->
    <p><input type="text" name="ps_cl_birthday2" id="datepicker" class="form-control" autocomplete="off" ></p>
              <script>
              $( function() {
                    $.datepicker.setDefaults( $.datepicker.regional[ "th" ] );

                      var currentTime = new Date();
                      var year = currentTime.getFullYear();

                      // date
                      $("#datepicker").datepicker({
                        changeMonth: true,
                        changeYear: true,
                        yearSuffix: year+543,
                        yearRange: '-100:+0',
                        dateFormat: 'yy-mm-dd'
                      });
                   } );
              </script>

<?php
    // save Image ภาพกล้อง ใหม่แทน
$file1 = $_FILES[ 'cc_cl_cctv_picture' ][ 'tmp_name' ];

if ( is_uploaded_file( $file1 ) ) {
    //ลบรูปเก่าในเซิร์ฟเวอร์ก่อน
    $sql_pic = "SELECT * FROM wm_tb_cctv WHERE cc_cl_aid='cc_cl_aid' ";
    $res_pic = mysqli_query($conn,$sql_pic);
    if($row_pic = mysqli_fetch_array($res_pic))
    {
        if(($row_pic['cc_cl_cctv_picture'] != '') && file_exists($row_pic['cc_cl_cctv_picture'])) {
            unlink( $row_pic['cc_cl_cctv_picture'] );
        }    
    }
    //เปลี่ยนชื่อไฟล์ใหม่
    $cc_cl_cctv_picture = "/policeinnopolis/uploaded/Image1/" . basename( $_FILES[ 'cc_cl_cctv_picture' ][ 'name' ] );	    
    // file + ext    
    $ext = strrchr($cc_cl_cctv_picture, ".");    
    $cc_cl_cctv_picture = "../policeinnopolis/uploaded/Image1/File_" . time() . $ext; // file+time+ext        
    move_uploaded_file( $file1, $cc_cl_cctv_picture );  
} 
else {
  $cc_cl_cctv_picture = '';
}
?>
    คำสั่งลบ รายการซ้ำ ให้เหลือ 1 รายการ
    
DELETE FROM `wm_tb_warrant` 
WHERE (`wr_cl_no`,`wr_cl_idcard`,`wr_cl_date`,`wr_cl_aid`) NOT IN 
(SELECT MIN(`wr_cl_no`), MIN(`wr_cl_idcard`), MIN(`wr_cl_date`),MIN(`wr_cl_aid`) 
 FROM `wm_tb_warrant` 
 GROUP BY `wr_cl_no`,`wr_cl_idcard`,`wr_cl_date`);
    
    
    เครื่องหมายถูก หน้าข้อความ
&nbsp;<img src="../images/check_mark.png" width="30" style="flex: auto">&nbsp;
    
    /////
   Update SQL จาก NULL เป็น 0
    
UPDATE wm_tb_performance
SET pf_cl_drug_sell = COALESCE(pf_cl_drug_sell, 0)
    
    /////
    Update SQL จาก 0 เป็น NULL<br>
    
UPDATE wm_tb_performance
SET pf_cl_drug_sell = NULLIF(pf_cl_drug_sell, 0)
--------------------------------------------------------------------------
    
// Date Picker แสดงวันที่ และเวลา
<label>วันเวลา <span style="color: #F90004">* จำเป็น</span> </label>
        <p><input type="text" name="mov_date" id="datepicker" placeholder="ระบุวันที่" class="form-control" autocomplete="off" required></p>
        <script type="text/javascript"> 
            $(function(){

                $.datetimepicker.setLocale('th'); // ต้องกำหนดเสมอถ้าใช้ภาษาไทย และ เป็นปี พ.ศ.
                // กรณีใช้แบบ input
                $("#datepicker").datetimepicker({
                    timepicker:true,
                    format:'Y-m-d H:i',  // กำหนดรูปแบบวันที่ ที่ใช้ เป็น 00-00-0000			
                    lang:'th',  // ต้องกำหนดเสมอถ้าใช้ภาษาไทย และ เป็นปี พ.ศ.
                    onSelectDate:function(dp,$input){
                        var yearT=new Date(dp).getFullYear()-0;  
                        var yearTH=yearT;  // ถ้าจะให้แสดงเป็น พ.ศ. ให้เป็น yearT+543
                        var fulldate=$input.val();
                        var fulldateTH=fulldate.replace(yearT,yearTH);
                        $input.val(fulldateTH);
                    },
                });       
                // กรณีใช้กับ input ต้องกำหนดส่วนนี้ด้วยเสมอ เพื่อปรับปีให้เป็น ค.ศ. ก่อนแสดงปฏิทิน
                $("#datepicker").on(function(e){
                    var dateValue=$(this).val();
                    if(dateValue!=""){
                            var arr_date=dateValue.split("-"); // ถ้าใช้ตัวแบ่งรูปแบบอื่น ให้เปลี่ยนเป็นตามรูปแบบนั้น
                            // ในที่นี้อยู่ในรูปแบบ 00-00-0000 เป็น d-m-Y  แบ่งด่วย - ดังนั้น ตัวแปรที่เป็นปี จะอยู่ใน array
                            //  ตัวที่สอง arr_date[2] โดยเริ่มนับจาก 0 
                            dateValue=dateValue.replace(arr_date[0],yearT);
                            $(this).val(dateValue);													
                    }		
                });
            });
            </script>
        <!-- สำหรับ วันที่ แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ -->
        <br>
------------------------------------------------------------------------------------
// Debug code
if (!$result) {
    echo "Query failed: " . mysqli_error($conn);
} else {
    $num_rows = mysqli_num_rows($result);
    echo "Query returned $num_rows rows<br>";
    while ($row = mysqli_fetch_assoc($result)) {
        var_dump($row);
    }
}
   
    เช็ควันเกิดก่อน ว่ามีค่าไหม ถ้าไม่มีให้เป็นค่าว่าง
if (isset($_POST['ps_cl_birthday2'])) {
   // If the "date_of_death" field is set, you can use its value
   $ps_cl_birthday2 = $_POST['ps_cl_birthday2'];
} else {
   // If the "date_of_death" field is not set, you can set its value to NULL or an empty string
   $ps_cl_birthday2 = NULL; // or $date_of_death = "";
}

if (isset($_POST['ps_cl_birthday2']) && !empty($_POST['ps_cl_birthday2'])) {
   // If the ps_cl_birthday2 field is set and not empty, you can use its value
   $ps_cl_birthday2 = $_POST['ps_cl_birthday2'];
   
   // Validate the date format
   $datetime = DateTime::createFromFormat('Y-m-d', $ps_cl_birthday2);
   if (!$datetime) {
      // If the date format is not correct, you can set the value to NULL or an empty string
      echo "Invalid date format for ps_cl_birthday2 field";
      exit;
   }
} else {
   // If the ps_cl_birthday2 field is not set or empty, you can set its value to NULL or an empty string
   $ps_cl_birthday2 = NULL; // or $ps_cl_birthday2 = "";
}
    
/*if (isset($_POST['date_of_death'])) {
   // If the "date_of_death" field is set, you can use its value
   $date_of_death = $_POST['date_of_death'];
} else {
   // If the "date_of_death" field is not set, you can set its value to NULL or an empty string
   $date_of_death = NULL; // or $date_of_death = "";
}*/
    
----------------------------------------------------------------------
    ปรับภาพให้ใหญ่ เมื่อไม่มีภาพ ให้แสดงบุคคลดำ
    
        <td> <?php if(!empty($row["ps_cl_image"])): ?>
                  <img class="enlarge-image" src="<?= $rowCi["ps_cl_image"] ?>" height="50">
            <?php else: ?>
                  <img class="enlarge-image" src="../upload/Image1/icon-บุคคล ดำ.jpg" height="50">
            <?php endif; ?>
        </td>
    
-----------------------------------------------------------------------
    Code ตรวจสอบ error
    
    mysqli_report(MYSQLI_REPORT_ERROR | MYSQLI_REPORT_STRICT);
    
-----------------------------------------------------------------------
    การตรวจสอบข้อผิดพลาดด้วยคำสั่ง try catch
<?php
    try{
      // ส่วนของคำสั่ง ที่ต้องการตรวจสอบข้อผิดพลาด
    }catch(Exception $e){
        // ให้แสดงผลเป็นข้อความ
        echo $e->getMessage();
        // หรือแสดง บรรทัดที่เกิดข้อผิดพลาด
        echo $e->getLine();
    }
?>
------------------------------------------------------------------------
<?php
    // วางไว้ใต้ $result
    $err =  mysqli_error($conn);
    print_r($err);
?>
    
-------------------------------------------------------------------------
SweetAert2
    
<!-- ให้ alert ข้างล่าง -->

<?php
if(isset($_SESSION['Success'])){  ?>
    <script>
        Swal.fire({
          icon: 'success',
          title: 'บันทึกข้อมูลสำเร็จ',
          text: 'Saved Successfully',
            // ตรวจสอบว่า มีการกดปุ่ม ok ให้ไปที่หน้าที่ต้องการ
          confirmButtonText: 'OK'
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = 'https://invest.watchman1.com/Bansuan/index.php?rnd=<?= rand(); ?>&page=criminalfund';
            }
        });
    </script>

<?php
    unset($_SESSION['Success']);
}
?>

<?php
if(isset($_SESSION['warning'])){  ?>
    <script>
        Swal.fire({
          title: 'ยืนยันที่จะลบใช่ไหม?',
          text: "ข้อมูลนี้ จะถูกลบถาวร!",
          icon: 'warning',
          showCancelButton: false,
          confirmButtonColor: '#FB0505',
          cancelButtonColor: '#d33',
          confirmButtonText: 'Yes, delete it!'
        }).then((result) => {
          if (result.isConfirmed) {
            Swal.fire(
              'Deleted!',
              'Your file has been deleted.',
              'success'
            )
              window.location.href = 'https://invest.watchman1.com/Bansuan/index.php?rnd=<?= rand(); ?>&page=criminalfund';
          }
        })
    </script>

<?php
    unset($_SESSION['warning']);
}
?>
    
<!-- ถ้าบันทึกข้อมูลไม่สำเร็จ -->
<?php
if(isset($_SESSION['error'])){  ?>   <!--//เช็คว่า ตัวแปร session เป็นค่าว่างหรือไม่ ถ้ามีข้อมูลมา ก็จะให้ทำการแจ้งเตือน -->
<!-- เขียนเป็นภาษา htmlอิสระ-->
    <script>
        Swal.fire({
          icon: 'error',
          title: 'บันทึกข้อมูลไม่สำเร็จ',
          text: '<?php echo $_SESSION['error'] ?>',
        })
    </script>

<?php
    unset($_SESSION['error']);
    
}
?>
    
----------------------------------------------------------------------------
</body>
</html>