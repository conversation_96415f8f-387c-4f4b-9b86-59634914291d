<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';


// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

//get month from select or current month
$month = isset($_GET['month']) ? $_GET['month'] : date("m");
$year = isset($_GET['year']) ? $_GET['year'] : date("Y");

try{
// เลขคดี
$sql_no = "SELECT * FROM wm_tb_investigating_case WHERE `station`='$station' AND Year(`ivc_cl_date`)='$year' ORDER BY `ivc_cl_aid` DESC LIMIT 1";
    $stmt = $pdo->prepare($sql_no);
    $stmt->execute();
    $row_no = $stmt->fetch(PDO::FETCH_ASSOC);

    $running_no = $row_no['ivc_cl_no'];
    
} catch (PDOException $e) {
    echo 'Query sql_no Failed : '. $e->getMessage();
}

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>เพิ่มข้อมูลคดีที่อยู่ระหว่างการสืบสวน/คดีอุกฉกรรจ์</title>
<script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/policeinnopolis/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 18px;
    }
input::placeholder {
  color: red;
}
.form-control::placeholder {
  color: sandybrown;
}
</style>
<!-- สำหรับ วันที่ แบบ พ.ศ. -->
<link rel="stylesheet" href="http://code.jquery.com/ui/1.9.1/themes/base/jquery-ui.css" />
<script src="http://code.jquery.com/jquery-1.8.2.js"></script>
<script src="/policeinnopolis/js/jquery-ui.js"></script>
<body>
	
	<div class="container">
	<div class="row">
	<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> เพิ่มข้อมูลคดีที่อยู่ระหว่างการสืบสวน/คดีอุกฉกรรจ์ ของ ฝ่ายสืบสวน </div>
	<form action="../WM/Save_investigating_case.php" method="POST" enctype="multipart/form-data" class="body">
	<span style="color: #1203F8">
        
        <div align="right"> <a>เลขรับแจ้งล่าสุด</a>&nbsp;<b style="color: crimson; font-size: 20px ; background-color: yellow ;padding: 10px"><?= $running_no ?></b></div>
        
        <label hidden="hidden">ลำดับ</label>
		<input name="ivc_cl_aid" type = "text" class="form-control" hidden="hidden"  >
        
        <label>เลขรับแจ้ง</label>
		<input type = "text" name = "ivc_cl_no" class="form-control" placeholder="เลขรับแจ้ง (เพิ่มจากล่าสุด)" required  >
        
        <label>ประเภทคดี</label>
		<select id="ivc_cl_type" name="ivc_cl_type" class="form-select form-select-sm" placeholder="ประเภทคดี">
			<option value="" selected> </option>
            <option value="คดีอาญาทั่วไป">คดีอาญาทั่วไป</option>
            <option value="คดีอุกฉกรรจ์">คดีอุกฉกรรจ์</option>
		</select>
        <br>
        
        <label>ลักษณะคดี</label>
		<select id="ivc_cl_know" name="ivc_cl_know" class="form-select form-select-sm" placeholder="ประเภทคดี">
			<option value="" selected> </option>
            <option value="1">รู้ตัวผู้กระทำผิด</option>
            <option value="2">ไม่รู้ตัวผู้กระทำผิด</option>
		</select>
        <br>
        
        <label>เหตุ/ข้อหา</label>
		<input type = "text" name = "ivc_cl_case" class="form-control" placeholder="ระบุเหตุ/ข้อหาที่เกิดขึ้น"   >
        
        <label>เลขคดีอาญา (ถ้ามี)</label>
		<input type = "text" name = "ivc_cl_crimes_no" class="form-control" placeholder="ระบุเลขคดีอาญา (ถ้ามี)"   >
        
        <label>ผู้เสียหาย</label>
		<input type = "text" name = "ivc_cl_name_sufferer" class="form-control" placeholder="ระบุชื่อผู้เสียหาย"   >
        
        <label>เลขบัตรผู้ต้องหา</label>
		<input type = "text" name = "ivc_cl_idcard" class="form-control" placeholder="ระบุเลขบัตรผู้ต้องหา"   >
        
        <label>ชื่อ-สกุล ผู้ต้องหา</label>
		<input type = "text" name = "ivc_cl_name" class="form-control" placeholder="ระบุชื่อ-สกุล ผู้ต้องหา"   >
        
        <br>
        <div class="input-group">
          <div class="input-group" >
            <span class="input-group-text">รายละเอียดโดยย่อ</span>
          </div>
            <textarea class="form-control" id="ivc_cl_detail" name="ivc_cl_detail"  rows="5" placeholder="รายละเอียดโดยย่อ" ></textarea>
        </div>
        
        <label>มูลค่าเสียหาย</label>
		<input type = "text" name = "ivc_cl_damages" class="form-control" placeholder="ระบุมูลค่าความเสียหาย"   >
        
        <br>
                
        <label>พนักงานสอบสวน</label>
		<?php
		if ($station == 6707) {
			echo '<select id="ivc_cl_inquiry_official" name="ivc_cl_inquiry_official" class="form-select form-select-sm" placeholder="พนักงานสอบสวน">';
			echo '<option value="" selected> </option>';
            
                $bs_inq = $pdo->prepare("SELECT * FROM `police_name_bs_inqinquiry` ORDER BY `police_name_bs_inqinquiry`.`aid_bs_inq` ASC");
                $bs_inq->execute();
            
                while($row_inq = $bs_inq->fetch(PDO::FETCH_ASSOC))
                {
                    $aid_bs_inq = htmlspecialchars($row_inq['aid_bs_inq'], ENT_QUOTES, 'UTF-8');
                    $bs_inq_name = htmlspecialchars($row_inq['bs_inq_name'], ENT_QUOTES, 'UTF-8');
			echo "<option value='$bs_inq_name'>$bs_inq_name</option>";
				}
			echo '</select>';
        } else {
				// User's station is not 6707, display a text input
				echo '<input type="text" id="ivc_cl_inquiry_official" name="ivc_cl_inquiry_official" class="form-control" placeholder="พนักงานสอบสวน" >';
			}
		?>
        <br>
		
        <label>วันที่รับแจ้ง/เกิดเหตุ <span style="color: #F90004">* จำเป็น</span> </label>
        <p><input type="text" name="ivc_cl_date" id="datepicker" placeholder="เพื่อข้อมูลถูกต้อง กรุณาเลือกจากปฎิทินด้านล่าง" class="form-control" autocomplete="off" required></p>
        <script>
            function set_cal(ele)//function สร้างตัวเลือกปฎิทิน
            {
              $( ele ).datepicker({
                  onSelect:(date_text)=>
                  {
                    let arr=date_text.split("/");
                    let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])+543).toString();
                    $(ele).val(new_date);
                    $(ele).css("color","");
                  },
                  beforeShow:()=>{

                    if($(ele).val()!="")
                    {
                      let arr=$(ele).val().split("/");
                      let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])-543).toString();
                      $(ele).val(new_date);

                    }

                    $(ele).css("color","white");
                  },
                  onClose:()=>{

                      $(ele).css("color","");

                      if($(ele).val()!="")
                      {
                          let arr=$(ele).val().split("-");
                          if(parseInt(arr[2])<2500)
                          {
                              let new_date=arr[0]+"-"+arr[1]+"-"+(parseInt(arr[2])).toString();
                              $(ele).val(new_date);
                          }
                      }
                  },
                  dateFormat:"yy-mm-dd", //กำหนดรูปแบบวันที่เป็น วัน/เดือน/ปี
                  changeMonth:true,//กำหนดให้เลือกเดือนได้
                  changeYear:true,//กำหนดให้เลือกปีได้
                  showOtherMonths:true,//กำหนดให้แสดงวันของเดือนก่อนหน้าได้
              });
            }
          $(document).ready(function(){
            //เรียก function set_cal เมื่อเปิดหน้าเว็บ โดยส่ง object element ที่มี id เป็น datepicker เป็นพารามิเตอร์
            set_cal( $("#datepicker") );
          })
          </script>
        <!-- สำหรับ วันที่ แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ -->
        
        <label>สถานะคดี</label>
            <select id="ivc_cl_status_case" name="ivc_cl_status_case" class="form-select form-select-sm" placeholder="สถานะคดี">
                    <option value="" selected> </option>  <!-- ดึงข้อมูลมาจากตารางใหม่ -->
                    <?php
                        $res_ics = $pdo->prepare("SELECT * FROM wm_tb_investigating_case_staus order by ics_cl_aid ASC");
                        $res_ics->execute();
                
                        while($row_ics = $res_ics->fetch(PDO::FETCH_ASSOC))
                        {
                            $ics_cl_aid = htmlspecialchars($row_ics['ics_cl_aid'], ENT_QUOTES, 'UTF-8');
                            $ics_cl_status = htmlspecialchars($row_ics['ics_cl_status'], ENT_QUOTES, 'UTF-8');
                            
                            echo "<option value='$ics_cl_aid'>$ics_cl_status</option>";
                        }
                    ?>
                </select>
        <br>
        
        <label>สถานะการจับกุม</label>
		<select id="ivc_cl_catch" name="ivc_cl_catch" class="form-select form-select-sm" placeholder="สถานะการจับกุม">
			<option value="" selected> </option>
            <option value="จับได้">จับได้</option>
            <option value="ยังจับไม่ได้">ยังจับไม่ได้</option>
			</select>
        <br>
        
        <label>จำนวนจำหน่ายคดี</label>
		<select id="ivc_cl_case_out" name="ivc_cl_case_out" class="form-select form-select-sm" placeholder="จำนวนจำหน่ายคดี">
			<option value="" selected> </option>
            <option value="1">1</option>
            <option value="2">2</option>
			</select>
        <br>
        
        <label>จำนวนจำหน่ายหมายจับ</label>
		<select id="ivc_cl_wanted" name="ivc_cl_wanted" class="form-select form-select-sm" placeholder="จำนวนจำหน่ายหมายจับ">
			<option value="" selected> </option>
            <option value="1">1</option>
            <option value="2">2</option>
			</select>
        <br>
        
		<label>ผู้สืบสวน</label>
		<?php
		if ($station == 6707) {
			echo '<select id="ivc_cl_detective" name="ivc_cl_detective" class="form-select form-select-sm" placeholder="ระบุเจ้าหน้าที่ผู้สืบสวน">';
			echo '<option value="" selected>เลือก</option>';
			$bsdp = $pdo->prepare("SELECT * FROM `police_name_bsdetective` order by `aid_bsdp` ASC");
                    $bsdp->execute();
            
                    while($row_bsdp = $bsdp->fetch(PDO::FETCH_ASSOC))
                    {
                        $aid_bsdp = htmlspecialchars($row_bsdp['aid_bsdp'], ENT_QUOTES, 'UTF-8');
                        $bsdp_name = htmlspecialchars($row_bsdp['bsdp_name'], ENT_QUOTES, 'UTF-8');
			echo "<option value='$bsdp_name'>$bsdp_name</option>";
		} 
			echo '</select>';
			} else {
				// User's station is not 6707, display a text input
				echo '<input type="text" id="ivc_cl_detective" name="ivc_cl_detective" class="form-control" placeholder="ระบุเจ้าหน้าที่ผู้สืบสวน" >';
			}
		?>
        <br>
        
        <label>หมายเหตุ</label>
		<input type = "text" name = "ivc_cl_remark" class="form-control" placeholder="หมายเหตุ"   >
        
        <div class="mb-3">
			<label for="formFileMultiple" class="form-label">รายงานสืบสวน (ขนาดไม่เกิน 5 MB)</label>
			<input class="form-control" type="file" id="ivc_cl_file" name="ivc_cl_file" multiple>
		</div>
		
		<br>
		<p>
			<input type="submit" value="บันทึกข้อมูล" class="btn btn-success" >
			<td> <a href="../WM/index.php?rnd=<?= rand(); ?>&page=investigating" class="btn btn-warning" >ยกเลิก</a> </td>
		</p>
		<br>
		<br>
	</form>
	</div>
	</div>
	</div>
<script>
// ฟังก์ชั่น เปลี่ยนภาค (บช.) เป็น ภ.จว. (บก.)
function do_region_change()
{
	var sel_region = document.getElementById("region");		
	var code_region = sel_region.options[sel_region.selectedIndex].value;

	$.get("../ajax/get_provincial.php?region=" + code_region, 
		  function(data, status){
				//alert("Data: " + data + "\nStatus: " + status);
				// jQuery style						
				$('#provincial').find('option').remove().end();
				var data = $.trim(data);
				if(data == '') {
					alert("ไม่พบข้อมูล บก. !");
					return;
				}

					//.append('<option value="whatever">text</option>').val('whatever');
				var datas = data.split(';');
				for(var i=0; i<datas.length; i++) {
					if(datas[i] == '') continue;
					datas[i] = datas[i].split(':');
					var code_region = datas[i][0];
					var name_region = datas[i][1];
					$('#provincial').append('<option value="'+ code_region+'">' + name_region + '</option>');
				}
                // Trigger the change event on the amphur dropdown so the tambon dropdown will be populated     
                $('#provincial').trigger('change');
		});
}
    //เลือก สถานี จากจังหวัด
function do_provincial_change()
{
	var sel_provincial = document.getElementById("region");		
	var provincial_code = sel_provincial.options[sel_provincial.selectedIndex].value;
    
    var sel_provincial2 = document.getElementById("provincial");		
	var station_code = sel_provincial2.options[sel_provincial2.selectedIndex].value;

	$.get("../ajax/get_station.php?region=" + provincial_code + "&provincial=" + station_code, 
			  function(data, status){
					//alert("Data: " + data + "\nStatus: " + status);
					// jQuery style						
					$('#station').find('option').remove().end();
					var data = $.trim(data);
					if(data == '') {
						alert("ไม่พบข้อมูลสถานี !");
						return;
					}

						//.append('<option value="whatever">text</option>').val('whatever');
					var datas = data.split(';');
					for(var i=0; i<datas.length; i++) {
						if(datas[i] == '') continue;
						datas[i] = datas[i].split(':');
						var station_code = datas[i][0];
						var station_name = datas[i][1];
						$('#station').append('<option value="'+ station_code+'">' + station_name + '</option>');
					}
			});
}
</script>
<!--<script src="/js/datetimepicker2.5.1/jquery.ui.datepicker-th.js" type="text/javascript"></script>  -->
</body>
</html>