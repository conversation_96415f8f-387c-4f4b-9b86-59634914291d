<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';
include '../Head_image.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

//get month from select or current month
$month = isset($_GET['month']) ? $_GET['month'] : date("m");
$year = isset($_GET['year']) ? $_GET['year'] : date("Y");
//$station = isset($_GET['station']) ? $_GET['station'] : 6707;
//$station = 6707;   // สภ.บ้านสวน

//--------------------
// last modify 2023-01-20
$limite_date = strtotime("$year-09-30"); // วันสุดท้ายของช่วง เป็น 30 ก.ย.ของปีนั้น ๆ
$current_date = time(); // now()   กำหนดวันปัจจุบัน
$check_date = strtotime("$year-10-01");   // ตัวแปร $check_date เป็นวันตั้งแต่ 1 ต.ค.ของปีนั้น ๆ เป็นต้นไป
$select_date = strtotime("$year-$month-01"); // เป็นตัวแปร สำหรับเลือก ของเดือนนั้น ๆ

//ถ้าเวลายังไม่สิ้นสุด 30 กย. ของปี
if($current_date < $check_date)   // ถ้าวันปัจจุบัน มีค่าน้อยกว่า วันที่ 1 ต.ค.
{
    $limite_date = strtotime(($year-1) . "-09-30");  // ตัวแปร $limite_date จะมีค่าเท่ากับ 30 ก.ย.ของปี ที่ลบค่าไป 1
    $check_date = strtotime(($year-1) . "-10-01");   // ตัวแปร $check_date จะมีค่าเท่ากับ 1 ต.ค. ของปี ที่ลบค่าไป 1
}

$m1 = '';
$m2 = '';
$m3 = '';
$m4 = '';
$m5 = '';
$m6 = '';
$m7 = '';
$m8 = '';
$m9 = '';
$m10 = '';
$m11 = '';
$m12 = '';
switch($month)
{
    case "01": $m1 = "selected"; break;
    case "02": $m2 = "selected"; break;
    case "03": $m3 = "selected"; break;
    case "04": $m4 = "selected"; break;
    case "05": $m5 = "selected"; break;
    case "06": $m6 = "selected"; break;
    case "07": $m7 = "selected"; break;
    case "08": $m8 = "selected"; break;
    case "09": $m9 = "selected"; break;
    case "10": $m10 = "selected"; break;
    case "11": $m11 = "selected"; break;
    case "12": $m12 = "selected"; break;
}
$current_month = $global_thai_month[0+$month];
$y2 = $year+543;

// แปลงเวลา เป็นปีไทย
function date_2_Thai( $date )
{
    global $global_thaimonth;
    $strYear = date("Y", $date) + 543;
    $strMonth = date("n",$date) - 1;
    $strDay = date("j", $date);
    $strMonthThai = $global_thaimonth[$strMonth];
    return "$strDay $strMonthThai $strYear";
}

//สร้างฟังก์ชั่น ย้อนหลัง arrest_summary_count มาใช้งาน เป็นยอดรวมการจับกุม ย้อนหลัง 1 เดือน
// dateM - datetime var
function arrest_summary_count($date)
{
    global $conn, $station;
    
    $m1 = date("Y-m-1", $date);
    $m2 = date("Y-m-t", $date);
    
$sql_s = "SELECT COUNT(*) FROM wm_tb_warrant WHERE station='$station' (`wr_cl_date` BETWEEN :m1 AND :m2) AND (`T1.wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=:station AND `wr_cl_status` IN ('2', '5','6','7') ";
    $stmt_s = $pdo->prepare($sql_s);
    $stmt_s->bindParam(':m1', $m1);
    $stmt_s->bindParam(':m2', $m2);
    $stmt_s->bindParam(':station', $station);
try{
    $stmt_s->execute();
}catch (PDOException $e) {
    echo '$sql_s Failed: '. $e->getMessage();
}
    $row_s = $stmt_s->fetch(PDO::FETCH_NUM); 
    return $row_s[0]; 
}

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<base target="_top">
<title>แบบ สส.12-1</title>
<link rel="stylesheet" href="/bootstrap/css/bootstrap-grid.css">
<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
</head>
    
<body>
<!--<p><img src="../Image/WatchmanDB3.jpg" width="100%" height="" alt=""/></p>-->
<div class=" h3 text-center  alert alert-success mb-4 mt-4 " role="alert" >แบบ สส.12-1 สถิติการจับกุมผู้ต้องหาคดียาเสพติด ภาพรวม รายปี &nbsp; <?= $name_station ?> </div>

<div class="container-fluid" align="left">
    <table width="90%" border="0" cellspacing="1" cellpadding="1">
      <tbody>
        <tr>
          <td width="14%"><div align="left">
            &nbsp;&nbsp;<a href="/WM/index.php?rnd=<?= rand(); ?>&page=report" class="btn btn-warning btn-lg mb-4" >ย้อนกลับ</a></td>
          <td width="16%"><label>เลือกปี </label>
            <select id="changeyear" name="changeyear" class="form-select col-8 d-lg-inline" style="background-color: #00D2FE; font-size: 16px" onChange="dates_change()" >
              <option value="" >เลือก</option>
              <?php
                $cur_y = date("Y") + 2;
                for($y=2020; $y<$cur_y; $y++) {
                    $sel = ($y == $year) ? "selected" : '';
                    echo "<option value='$y' $sel> ". ($y + 543) ." </option>";
                }
            ?>
            </select></td>
          <td width="39%">&nbsp;</td>
        </tr>
      </tbody>
    </table>
</div>
<br>
    
<div class="container" align="left" style="margin-left:auto">
<table width="99%" border="1" cellspacing="1" cellpadding="1" class="table-striped table-hover table-bordered">
  <tbody>
<?php
      
$sql = "SELECT
            CONCAT(YEAR(T1.`pf_cl_dates`)+543) AS `year`,
            MONTH(T1.`pf_cl_dates`) AS month,
            COUNT(T1.`pf_cl_dates`) AS count,
            SUM(T1.`pf_cl_drug`IN('4','5','6')) AS take,
            Sum(T1.`pf_cl_drug`='3') AS have, 
            Sum(T1.`pf_cl_drug`='2') AS sell, 
            Sum(T1.`pf_cl_drug`='1') AS make
        FROM
            `wm_tb_performance` AS T1 
        WHERE 
            T1.station='$station' AND T1.`pf_cl_type_sub`='พ.ร.บ.ยาเสพติด' AND YEAR(T1.`pf_cl_dates`)=:year 
        GROUP BY 
            `year`";

    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':year', $year);
try{
    $stmt->execute();
}catch (PDOException $e) {
    echo '$sql Failed: '. $e->getMessage();
}
    $result = $stmt->fetchAll(PDO::FETCH_ASSOC);

?>

    <tr>
      <td rowspan="2" style="font-size: 22px; color: blue; background: #42FF00">ปี</td>
      <td colspan="4" style="font-size: 22px; color: blue; text-align: center; background: #42FF00">ข้อกล่าวหา</td>
      <td rowspan="2" style="font-size: 22px; color: blue; background: #42FF00">รวม</td>
      <td rowspan="2" style="font-size: 22px; color: blue; background: #42FF00">หมายเหตุ</td>
    </tr>
    <tr>
      <td style="font-size: 22px; color: blue; background: #42FF00">เสพ</td>
      <td style="font-size: 22px; color: blue; background: #42FF00">ครอบครอง</td>
      <td style="font-size: 22px; color: blue; background: #42FF00">จำหน่าย</td>
      <td style="font-size: 22px; color: blue; background: #42FF00">ผลิต</td>
      </tr>
      
<?php foreach ($result as $row): ?>
        
    <tr style="font-size: 18px">
      <td style="text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<?php echo $row['year']; ?>&nbsp;</td>
      <td>&nbsp;<?php echo (($row['take'] > 0) ? $row['take']  : "") ?>&nbsp;</td>
      <td>&nbsp;<?php echo (($row['have'] > 0) ? $row['have']  : "") ?>&nbsp;</td>
      <td>&nbsp;<?php echo (($row['sell'] > 0) ? $row['sell']  : "") ?>&nbsp;</td>
      <td>&nbsp;<?php echo (($row['make'] > 0) ? $row['make']  : "") ?>&nbsp;</td>
      <td>&nbsp;<?php echo (($row['count'] > 0) ? $row['count']  : "") ?>&nbsp;</td>
      <td>&nbsp;&nbsp;</td>
    </tr>
  </tbody>
<?php endforeach; ?>
</table>
</div>
    <br>

&nbsp;&nbsp;<button class="btn btn-primary btn-lg mb-4" onClick="do_print()"> พิมพ์รายงาน </button> 
	<!--<button class="btn btn-primary btn-lg mb-4" onClick="if ($station === 6707) { do_print(); }">พิมพ์รายงาน</button>-->
</body>
<script>
function dates_change()  // คำสั่งเปลี่ยนเดือน ปี
{
   // var ms = $("#changemonth option:selected").val();
    var ys = $("#changeyear option:selected").val();
    window.location = "/Activity/Show_Activity1_SS12-1.php?&year=" + ys + "&rnd=" + Math.random();
}    
</script>
</html>
<!-- script สำหรับส่งไปพิมพ์รายงาน  -->
<script>
    
    function do_print()
    {
        var ys = $("#changeyear option:selected").val();
        var newW = window.open("/Activity/Show_Activity1_SS12-1_print.php?&year=" + ys + "&rnd=" + Math.random() , "_print", "menubar=no,titlebar=no,toolbar=no,status=no");
        newW.print();
        //window.print();
    }
    
</script>