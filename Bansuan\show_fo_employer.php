<?php

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

?>

<div class="container--x">
<table class="table table-striped" > 
	<tr>	
        <th> เลขหนังสือเดินทาง </th>
		<th> เลขใบอนุญาตทำงาน </th>
		<th> ตำแหน่งงาน </th>
		<th> ได้รับอนุญาตให้ทำงานถึงวันที่ </th>
		<th> เลขที่นายจ้าง </th>
        <th> ชื่อนายจ้าง </th>
        <th> ประเภทนายจ้าง </th>
        <th> ที่อยู่นายจ้าง </th>
        <th> เบอร์นายจ้าง </th>
	</tr>
<?php
$sql = "SELECT * FROM wm_tb_foreigner ";  //ตั้งตัวแปร sql เลือกฐานข้อมูลจากตารางที่ชื่อ wm_tb_personal
if($pcid != ''){
	$sql = $sql . " WHERE fo_cl_passport='$pcid' " ; // ผู้ปกครอง

		
}
$resultPn = mysqli_query($conn,$sql);
// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
//$no = 1;
while($rowPn=mysqli_fetch_array($resultPn))	{//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
    
    //$age = get_personal_age_2( $row["fo_cl_age"] ); // eng    
?>
	<tr>	
        <td> <?=$rowPn["fo_cl_passport"]?> </td>
		<td> <?=$rowPn["fo_cl_workpermit_no"]?> </td>
		<td> <?=$rowPn["fo_cl_position"]?> </td>
		<td> <?=$rowPn["fo_cl_workpermit_exp"]?> </td>
		<td> <?=$rowPn["fo_cl_employer_no"]?> </td>
		<td> <?=$rowPn["fo_cl_employer_name"]?> </td>
        <td> <?=$rowPn["fo_cl_employer_type"]?> </td>
        <td> <?=$rowPn["fo_cl_employer_address"]?> </td>
        <td> <?=$rowPn["fo_cl_employer_phone"]?> </td>
	</tr>
	<?php
	//	$no++;
	}
	mysqli_close($conn);	//ปิดการเชื่อมต่อฐานข้อมูล

//if($no == 1) {
	//echo "<tr><td colspan='10' style='color: red'> ไม่พบข้อมูลของ <b style='color: blue'>$people_name</b> </td></tr>";
//}
	?>

		</table>
</div>
<script>
function Del(url)
{
	if(confirm("คุณจะลบข้อมูล หรือ ไม่ ?"))
	{
		window.location = url;		
	}
}
</script>