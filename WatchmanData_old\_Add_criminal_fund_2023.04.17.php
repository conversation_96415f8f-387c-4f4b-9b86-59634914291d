<?php
include '../Condb.php';
include '../users.inc.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// เมื่อมีการใช้ ตัวแปร Session จะต้องเปิดก่อน
//session_start();  // เปิดตัวแปร session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

?>


<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>เพิ่มข้อมูลเงินกองทุนสืบสวน</title>
<script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/policeinnopolis/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 18px;
    }
input::placeholder {
  color: red;
}
.form-control::placeholder {
  color: sandybrown;
}
</style>
<!-- สำหรับ วันที่ แบบ พ.ศ. -->
<link rel="stylesheet" href="http://code.jquery.com/ui/1.9.1/themes/base/jquery-ui.css" />
<script src="http://code.jquery.com/jquery-1.8.2.js"></script>
<script src="/policeinnopolis/js/jquery-ui.js"></script>
<script src="../SweetAlert2/dist/sweetalert2.all.min.js"></script>
</head>
    
<body>
	
	<div class="container">
	<div class="row">
	<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> เพิ่มข้อมูลเงินกองทุนสืบสวน สภ.บ้านสวน </div>
	<form action="Save_criminal_fund.php" method="POST" enctype="multipart/form-data" class="body">
	<span style="color: #1203F8">
        
        <label hidden="hidden">ลำดับ</label>
		<input name="mi_cl_aid" type = "text" class="form-control" hidden="hidden"  >
		        
        <label>วันที่ <span style="color: #F90004">* จำเป็น</span> </label>
        <p><input type="text" name="fun_cl_date" id="datepicker" class="form-control" autocomplete="off" required ></p>
        <script>
            function set_cal(ele)//function สร้างตัวเลือกปฎิทิน
            {
              $( ele ).datepicker({
                  onSelect:(date_text)=>
                  {
                    let arr=date_text.split("/");
                    let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])+543).toString();
                    $(ele).val(new_date);
                    $(ele).css("color","");
                  },
                  beforeShow:()=>{

                    if($(ele).val()!="")
                    {
                      let arr=$(ele).val().split("/");
                      let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])-543).toString();
                      $(ele).val(new_date);

                    }

                    $(ele).css("color","white");
                  },
                  onClose:()=>{

                      $(ele).css("color","");

                      if($(ele).val()!="")
                      {
                          let arr=$(ele).val().split("-");
                          if(parseInt(arr[2])<2500)
                          {
                              let new_date=arr[0]+"-"+arr[1]+"-"+(parseInt(arr[2])).toString();
                              $(ele).val(new_date);
                          }
                      }
                  },
                  dateFormat:"yy-mm-dd", //กำหนดรูปแบบวันที่เป็น วัน/เดือน/ปี
                  changeMonth:true,//กำหนดให้เลือกเดือนได้
                  changeYear:true,//กำหนดให้เลือกปีได้
                  showOtherMonths:true,//กำหนดให้แสดงวันของเดือนก่อนหน้าได้
              });
            }
          $(document).ready(function(){
            //เรียก function set_cal เมื่อเปิดหน้าเว็บ โดยส่ง object element ที่มี id เป็น datepicker เป็นพารามิเตอร์
            set_cal( $("#datepicker") );
          })
          </script>
        <!-- สำหรับ วันที่ แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ -->
        
		<label>รายการ</label>
		<input type = "text" name = "fun_cl_detail" class="form-control" placeholder="ระบุรายการ"   >

        <br>
		
		<label>ผู้บันทึก</label>
		<select id="fun_cl_record" name="fun_cl_record" class="form-select form-select-sm" placeholder="ระบุเจ้าหน้าที่สืบสวน/บันทึก">
			<option value="" selected> </option>
            <?php
                    $bsdp = mysqli_query($conn, "SELECT * FROM `police_name_bsdetective` order by `aid_bsdp` DESC");
                    while($row_bsdp = mysqli_fetch_array($bsdp))
                    {
                        echo "<option value='{$row_bsdp['aid_bsdp']}'>{$row_bsdp['bsdp_name']}</option>";
                    }
                ?>
		</select>
        <br>
        <div class="mb-3">
			<label for="formFileMultiple" class="form-label">ไฟล์แนบ</label>
			<input class="form-control" type="file" id="fun_cl_file" name="fun_cl_file" multiple>
		</div>
		
		<br>
		<p>
			<input type="submit" value="บันทึกข้อมูล" class="btn btn-success" >
			<td> <a href="index.php?rnd=<?= rand(); ?>&page=criminalfund" class="btn btn-warning" >ยกเลิก</a> </td>
		</p>
		<br>
		<br>
	</form>
	</div>
	</div>
	</div>
<!--<script src="/js/datetimepicker2.5.1/jquery.ui.datepicker-th.js" type="text/javascript"></script>  -->
</body>
</html>
<!-- ให้ alert ข้างล่าง -->

<?php
if(isset($_SESSION['Success'])){  ?>
    <script>
        Swal.fire({
          icon: 'success',
          title: 'บันทึกข้อมูลสำเร็จ',
          text: 'Saved Successfully',
            // ตรวจสอบว่า มีการกดปุ่ม ok ให้ไปที่หน้าที่ต้องการ
          confirmButtonText: 'OK'
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = 'https://invest.watchman1.com/Bansuan/index.php?rnd=<?= rand(); ?>&page=criminalfund';
            }
        });
    </script>

<?php
    unset($_SESSION['Success']);
}
?>

<?php
if(isset($_SESSION['warning'])){  ?>
    <script>
        Swal.fire({
          title: 'ยืนยันที่จะลบใช่ไหม?',
          text: "ข้อมูลนี้ จะถูกลบถาวร!",
          icon: 'warning',
          showCancelButton: false,
          confirmButtonColor: '#FB0505',
          cancelButtonColor: '#d33',
          confirmButtonText: 'Yes, delete it!'
        }).then((result) => {
          if (result.isConfirmed) {
            Swal.fire(
              'Deleted!',
              'Your file has been deleted.',
              'success'
            )
              window.location.href = 'https://invest.watchman1.com/Bansuan/index.php?rnd=<?= rand(); ?>&page=criminalfund';
          }
        })
    </script>

<?php
    unset($_SESSION['warning']);
}
?>
    
<!-- ถ้าบันทึกข้อมูลไม่สำเร็จ -->
<?php
if(isset($_SESSION['error'])){  ?>   <!--//เช็คว่า ตัวแปร session เป็นค่าว่างหรือไม่ ถ้ามีข้อมูลมา ก็จะให้ทำการแจ้งเตือน -->
<!-- เขียนเป็นภาษา htmlอิสระ-->
    <script>
        Swal.fire({
          icon: 'error',
          title: 'บันทึกข้อมูลไม่สำเร็จ',
          text: '<?php echo $_SESSION['error'] ?>',
        })
    </script>

<?php
    unset($_SESSION['error']);
    
}
?>