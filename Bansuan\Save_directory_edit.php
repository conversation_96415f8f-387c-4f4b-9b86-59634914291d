<?php  //PDO
// Include the database connection file
include "../Condb.php";
include '../users.inc.php';
include '../Alert.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);


/*echo '<pre>';
print_r($_POST);
echo '</pre>';

echo '<hr';

echo '<pre>';
var_dump($_POST);
echo '</pre>';

exit();*/

// Retrieve the form data
$id = $_POST['id'];
$idcard = $_POST['idcard'];
$charge = $_POST['charge'];
$pol_arrest = $_POST['pol_arrest'];
$position_arrest = $_POST['position_arrest'];
$phone_arrest = $_POST['phone_arrest'];
$region = $_POST['region'];
$provincial = $_POST['provincial'];
$station = $_POST['station'];
$gov_name = $_POST['gov_name'];
$gov_contact = $_POST['gov_contact'];
$gov_date = $_POST['gov_date'];
$attorney_name = $_POST['attorney_name'];
$att_contact = $_POST['att_contact'];
$att_date = $_POST['att_date'];
$remark = $_POST['remark'];

// ไฟล์ PDF ฝ่ายปกครอง
$file1 = $_FILES['gov_pdf']['tmp_name'];
$gov_pdf = ''; // Initialize the variable

if (is_uploaded_file($file1)) {
  $gov_pdf = "uploaded/PDF22/Gov_" . $_FILES['gov_pdf']['name'];
  move_uploaded_file($file1, "../" . $gov_pdf);
} else {
  // Check if the file exists in the database
  $stmt1 = $pdo->prepare("SELECT gov_pdf FROM DirectoryDetention22 WHERE idcard = :idcard");
  $stmt1->bindParam(':idcard', $idcard);
  $stmt1->execute();

  $old_gov_pdf = $stmt1->fetchColumn();

  if (!empty($old_gov_pdf)) {
    $gov_pdf = $old_gov_pdf;
  }
}

// ไฟล์ PDF อัยการ
$file2 = $_FILES['att_pdf']['tmp_name'];
$att_pdf = ''; // Initialize the variable

if (is_uploaded_file($file2)) {
  $att_pdf = "uploaded/PDF22/Att_" . $_FILES['att_pdf']['name'];
  move_uploaded_file($file2, "../" . $att_pdf);
} else {
  // Check if the file exists in the database
  $stmt2 = $pdo->prepare("SELECT att_pdf FROM DirectoryDetention22 WHERE idcard = :idcard");
  $stmt2->bindParam(':idcard', $idcard);
  $stmt2->execute();

  $old_att_pdf = $stmt2->fetchColumn();

  if (!empty($old_att_pdf)) {
    $att_pdf = $old_att_pdf;
  }
}

// Set the timezone to Thailand
date_default_timezone_set('Asia/Bangkok');

// Get the current date and time
$currentDateTime = date("Y-m-d_H-i-s");

// Save Image$image1_front // ภาพด้านหน้า
$image1_front = $_FILES['image1']['tmp_name'];

// Retrieve the existing image path
$stmt1 = $pdo->prepare("SELECT image1 FROM DirectoryDetention22 WHERE idcard = :idcard");
$stmt1->bindParam(':idcard', $idcard);
$stmt1->execute();
$old_image1 = $stmt1->fetchColumn();

if (is_uploaded_file($image1_front)) {
    // User has uploaded a new image
    // Delete the old image if it exists
    if (!empty($old_image1) && file_exists($old_image1)) {
        unlink($old_image1);
    }
    $ext = strrchr($_FILES['image1']['name'], ".");
    $image1 = "../uploaded/Image22/Img1_" . $idcard . "_" . $currentDateTime . $ext;

    // Prepare the statement
    $stmt1 = $pdo->prepare("UPDATE DirectoryDetention22 SET image1 = :image1 WHERE idcard = :idcard");
    $stmt1->bindParam(':image1', $image1);
    $stmt1->bindParam(':idcard', $idcard);
    $stmt1->execute(); // Execute the SQL statement

    // Move the uploaded file
    move_uploaded_file($image1_front, $image1);
} else {
    // User has not uploaded a new image, keep the existing one
    $image1 = $old_image1;
}

// Repeat the same process for the remaining images (image2, image3, image4)

// Save Image$image2_back // ภาพด้านหลัง
$image2_back = $_FILES['image2']['tmp_name'];

// Retrieve the existing image path
$stmt2 = $pdo->prepare("SELECT image2 FROM DirectoryDetention22 WHERE idcard = :idcard");
$stmt2->bindParam(':idcard', $idcard);
$stmt2->execute();
$old_image2 = $stmt2->fetchColumn();

if (is_uploaded_file($image2_back)) {
    if (!empty($old_image2) && file_exists($old_image2)) {
        unlink($old_image2);
    }
    $ext = strrchr($_FILES['image2']['name'], ".");
    $image2 = "../uploaded/Image22/Img2_" . $idcard . "_" . $currentDateTime . $ext;

    // Prepare the statement
    $stmt2 = $pdo->prepare("UPDATE DirectoryDetention22 SET image2 = :image2 WHERE idcard = :idcard");
    $stmt2->bindParam(':image2', $image2);
    $stmt2->bindParam(':idcard', $idcard);
    $stmt2->execute(); // Execute the SQL statement

    // Move the uploaded file
    move_uploaded_file($image2_back, $image2);
} else {
    // User has not uploaded a new image, keep the existing one
    $image2 = $old_image2;
}

// Repeat the process for image3 and image4

// Save Image$image3_right // ภาพด้านขวา
$image3_right = $_FILES['image3']['tmp_name'];

// Retrieve the existing image path
$stmt3 = $pdo->prepare("SELECT image3 FROM DirectoryDetention22 WHERE idcard = :idcard");
$stmt3->bindParam(':idcard', $idcard);
$stmt3->execute();
$old_image3 = $stmt3->fetchColumn();

if (is_uploaded_file($image3_right)) {
    if (!empty($old_image3) && file_exists($old_image3)) {
        unlink($old_image3);
    }
    $ext = strrchr($_FILES['image3']['name'], ".");
    $image3 = "../uploaded/Image22/Img3_" . $idcard . "_" . $currentDateTime . $ext;

    // Prepare the statement
    $stmt3 = $pdo->prepare("UPDATE DirectoryDetention22 SET image3 = :image3 WHERE idcard = :idcard");
    $stmt3->bindParam(':image3', $image3);
    $stmt3->bindParam(':idcard', $idcard);
    $stmt3->execute(); // Execute the SQL statement

    // Move the uploaded file
    move_uploaded_file($image3_right, $image3);
} else {
    // User has not uploaded a new image, keep the existing one
    $image3 = $old_image3;
}

// Repeat the process for image4

// Save Image$image4_left //ภาพด้านซ้าย
$image4_left = $_FILES['image4']['tmp_name'];

// Retrieve the existing image path
$stmt4 = $pdo->prepare("SELECT image4 FROM DirectoryDetention22 WHERE idcard = :idcard");
$stmt4->bindParam(':idcard', $idcard);
$stmt4->execute();
$old_image4 = $stmt4->fetchColumn();

if (is_uploaded_file($image4_left)) {
    if (!empty($old_image4) && file_exists($old_image4)) {
        unlink($old_image4);
    }
    $ext = strrchr($_FILES['image4']['name'], ".");
    $image4 = "../uploaded/Image22/Img4_" . $idcard . "_" . $currentDateTime . $ext;

    // Prepare the statement
    $stmt4 = $pdo->prepare("UPDATE DirectoryDetention22 SET image4 = :image4 WHERE idcard = :idcard");
    $stmt4->bindParam(':image4', $image4);
    $stmt4->bindParam(':idcard', $idcard);
    $stmt4->execute(); // Execute the SQL statement

    // Move the uploaded file
    move_uploaded_file($image4_left, $image4);
} else {
    // User has not uploaded a new image, keep the existing one
    $image4 = $old_image4;
}

// Check if an ID is provided to determine whether to perform an INSERT or UPDATE
if (isset($id) && !empty($id)) {
  // UPDATE existing data
  $query = "UPDATE DirectoryDetention22 SET idcard=?, charge=?, pol_arrest=?, position_arrest=?, phone_arrest=?, region=?, provincial=?, station=?, gov_name=?, gov_contact=?, gov_date=?, gov_pdf=?, attorney_name=?, att_contact=?, att_date=?, att_pdf=?, image1=?, image2=?, image3=?, image4=?, remark=? WHERE id=?";
  $stmt = $pdo->prepare($query);
  $stmt->execute([$idcard, $charge, $pol_arrest, $position_arrest, $phone_arrest, $region, $provincial, $station, $gov_name, $gov_contact, $gov_date, $gov_pdf, $attorney_name, $att_contact, $att_date, $att_pdf, $image1, $image2, $image3, $image4, $remark, $id]);
} else {
  // INSERT new data
  $query = "INSERT INTO DirectoryDetention22 (idcard, charge, pol_arrest, position_arrest, phone_arrest, region, provincial, station, gov_name, gov_contact, gov_date, gov_pdf, attorney_name, att_contact, att_date, att_pdf, image1, image2, image3, image4, remark) 
  VALUES (:idcard, :charge, :pol_arrest, :position_arrest, :phone_arrest, :region, :provincial, :station, :gov_name, :gov_contact, :gov_date, :gov_pdf, :attorney_name, :att_contact, :att_date, :att_pdf, :image1, :image2, :image3, :image4, :remark)";
  $stmt = $pdo->prepare($query);
  $stmt->execute([$idcard, $charge, $pol_arrest, $position_arrest, $phone_arrest, $region, $provincial, $station, $gov_name, $gov_contact, $gov_date, $gov_pdf, $attorney_name, $att_contact, $att_date, $att_pdf, $image1, $image2, $image3, $image4, $remark]);
}

$ip_address = $_SERVER['REMOTE_ADDR'];
$action = "Save Directory Edit : $idcard"; // You can set the action to a suitable value
$data = str_replace("'", "", compact_array($_POST));
log_activity($acc, $action, $data, $ip_address);

if ($stmt->rowCount() > 0) {
    $_SESSION['success'] = "Data has been saved successfully";
    showSweetAlert("Saved successfully", "บันทึกข้อมูลเรียบร้อย");
    unset($_SESSION['success']); // Clear the session variable
    header("refresh:2; url=/Bansuan/index.php?pcid={$idcard}&page=Directory");
} else {
    $_SESSION['error'] = "Error";
    showSweetAlert('Error', "บันทึกข้อมูลไม่สำเร็จ", 'error');
    $errorInfo = $stmt->errorInfo();
    echo "Error saving data: " . $errorInfo[2];
    header("refresh:2; url=/Bansuan/index.php?pcid={$idcard}&page=Directory");
}

$pdo = null;

?>
