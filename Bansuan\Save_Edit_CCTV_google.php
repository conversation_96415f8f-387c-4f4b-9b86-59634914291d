<?php
include '../Condb.php'; //PDO
include '../users.inc.php';
include '../Alert.php';

/*echo '<pre>';
print_r($_POST);
echo '</pre>';

echo '<hr';

print_r($_FILES);
exit();*/

$acc = $user['account'];
// Call this function at the top of each page to log the page view
$ip_address = $_SERVER['REMOTE_ADDR'];
$action = 'Save CCTV location'; // You can set the action to a suitable value
$data = str_replace("'", "", compact_array($_POST));
log_activity($acc, $action, $data, $ip_address);

	$id = $_POST['id'];
	$locationName = $_POST['location_name'];
    $latitude = $_POST['latitude'];
    $longitude = $_POST['longitude'];
	$cctv_province = $_POST['cctv_province'];
	$cctv_amphur = $_POST['cctv_amphur'];
	$cctv_tumbon = $_POST['cctv_tumbon'];
	$cctv_moo = $_POST['cctv_moo'];
	$cctv_adr = $_POST['cctv_adr'];
	$cctv_detail = $_POST['cctv_detail'];
	$cctv_admin = $_POST['cctv_admin'];
	$cctv_contact = $_POST['cctv_contact'];
	$region = $_POST['region'];
	$provincial = $_POST['provincial'];
	$station = $_POST['station'];

// Save Image
$file1 = $_FILES['pin_image']['tmp_name'];
// Retrieve the existing image path
$stmt1 = $pdo->prepare("SELECT pin_image FROM locations WHERE id = :id");
$stmt1->bindParam(':id', $id);
$stmt1->execute();
$old_pin_image = $stmt1->fetchColumn();
// Save new image or keep the existing one
if (is_uploaded_file($file1)) {
    // User has uploaded a new image
    // Delete the old image if it exists
    if (!empty($old_pin_image) && file_exists($old_pin_image)) {
        unlink($old_pin_image);
    }
    $ext = strrchr($_FILES['pin_image']['name'], ".");
    $pin_image = "../uploaded/Image1/cctv_" . $id . "_" . time() . $ext; 
    // Prepare the statement
    $stmt1 = $pdo->prepare("UPDATE locations SET pin_image = :pin_image WHERE id=:id ");
    $stmt1->bindParam(':pin_image', $pin_image);
    $stmt1->bindParam(':id', $id);
    // Move the uploaded file
    move_uploaded_file($file1, $pin_image);
} else {
    // User has not uploaded a new image, keep the existing one
    $pin_image = $old_pin_image;
}


$sql2 ="SELECT * FROM `locations` WHERE id=:id ";
    $stmt2 = $pdo->prepare($sql2);
    $stmt2->bindParam(':id', $id);
try{
    $stmt2->execute();
}catch (PDOException $e) {
    echo '$sql Failed: '. $e->getMessage();
}
    $row = $stmt2->fetch(PDO::FETCH_ASSOC);
if($row) {
        $id = $row['id'];

		$sql = "UPDATE locations SET " .
			"location_name = :location_name," .
			"latitude = :latitude," .
			"longitude = :longitude," .
			"cctv_province = :cctv_province," .
			"cctv_amphur = :cctv_amphur," .
			"cctv_tumbon = :cctv_tumbon," .
			"cctv_moo = :cctv_moo," .
			"cctv_adr = :cctv_adr," .
			"cctv_detail = :cctv_detail," .
			"cctv_admin = :cctv_admin," .
			"cctv_contact = :cctv_contact," .
			"region = :region," .
			"provincial = :provincial," .
			"station = :station," .
			"pin_image = :pin_image " .
        "WHERE id = :id ";
	$stmt = $pdo->prepare($sql);
    $stmt->bindParam(':location_name', $locationName);
    $stmt->bindParam(':latitude', $latitude);
    $stmt->bindParam(':longitude', $longitude);
	$stmt->bindParam(':cctv_province', $cctv_province);
	$stmt->bindParam(':cctv_amphur', $cctv_amphur);
	$stmt->bindParam(':cctv_tumbon', $cctv_tumbon);
	$stmt->bindParam(':cctv_moo', $cctv_moo);
	$stmt->bindParam(':cctv_adr', $cctv_adr);
	$stmt->bindParam(':cctv_detail', $cctv_detail);
	$stmt->bindParam(':cctv_admin', $cctv_admin);
	$stmt->bindParam(':cctv_contact', $cctv_contact);
	$stmt->bindParam(':region', $region);
	$stmt->bindParam(':provincial', $provincial);
	$stmt->bindParam(':station', $station);
	$stmt->bindParam(':pin_image', $pin_image);
    $stmt->bindParam(':id', $id);
}
   $result = $stmt->execute();

if ($result) {
    $_SESSION['success'] = "Data has been inserted succesfully";
    showSweetAlert("Saved successfully", "บันทึกข้อมูลเรียบร้อย");
    unset($_SESSION['success']); // Clear the session variable
    header("refresh:2; url=/Bansuan/GoogleMapsDisplay.php");
} else {
    $_SESSION['error'] = "Error";
    showSweetAlert('Error', "บันทึกข้อมูล ไม่สำเร็จ", 'error');
    unset($_SESSION['error']); // Clear the session variable
    header("refresh:2; url=/Bansuan/GoogleMapsDisplay.php");
}

$pdo = null;

?>
