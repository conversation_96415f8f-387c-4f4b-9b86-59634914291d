<?php
include '../Condb.php';  //PDO
include '../users.inc.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);


?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>เพิ่มข้อมูลตำหนิรูปพรรณ</title>
<script src="/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>  
<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 18px;
    }
input::placeholder {
  color: red;
}
.form-control::placeholder {
  color: sandybrown;
}
</style>

</head>

<body>
	
	<div class="container">
	<div class="row">
	<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> เพิ่มข้อมูลตำหนิรูปพรรณ </div>
	<form action="Save_all_mark_person.php" method="POST" enctype="multipart/form-data" class="body">
        
        <label hidden ="hidden">ลำดับ</label>
		<input type = "text" name = "id_mark" class="form-control"  hidden ="hidden" >
		
		<label for="idcard">เลขบัตรประชาชน <span style="color: #F90004">* จำเป็น</span></label>
		<input type="text" name="mk_idcard" id="mk_idcard" class="form-control" placeholder="เลขบัตรประชาชน" required>

		<h3>Items</h3>
		  <label><input type="checkbox" name="item1[]" value="subitem1"> Sub Item 1</label><br>
		  <label><input type="checkbox" name="item1[]" value="subitem2"> Sub Item 2</label><br>
		  <label><input type="checkbox" name="item1[]" value="subitem3"> Sub Item 3</label><br>
		  <label><input type="checkbox" name="item1[]" value="subitem4"> Sub Item 4</label><br>
		  <label><input type="checkbox" name="item1[]" value="subitem5"> Sub Item 5</label><br>

		<h3>Items 2</h3>
		  <label><input type="checkbox" name="item2[]" value="subitem1"> Sub Item 1</label><br>
		  <label><input type="checkbox" name="item2[]" value="subitem2"> Sub Item 2</label><br>
		  <label><input type="checkbox" name="item2[]" value="subitem3"> Sub Item 3</label><br>
		  <label><input type="checkbox" name="item2[]" value="subitem4"> Sub Item 4</label><br>
		  <label><input type="checkbox" name="item2[]" value="subitem5"> Sub Item 5</label><br>

		<h3>Items 3</h3>
		  <label><input type="checkbox" name="item3[]" value="subitem1"> Sub Item 1</label><br>
		  <label><input type="checkbox" name="item3[]" value="subitem2"> Sub Item 2</label><br>
		  <label><input type="checkbox" name="item3[]" value="subitem3"> Sub Item 3</label><br>
		  <label><input type="checkbox" name="item3[]" value="subitem4"> Sub Item 4</label><br>
		  <label><input type="checkbox" name="item3[]" value="subitem5"> Sub Item 5</label><br>

		<h3>Items 4</h3>
		  <label><input type="checkbox" name="item4[]" value="subitem1"> Sub Item 1</label><br>
		  <label><input type="checkbox" name="item4[]" value="subitem2"> Sub Item 2</label><br>
		  <label><input type="checkbox" name="item4[]" value="subitem3"> Sub Item 3</label><br>
		  <label><input type="checkbox" name="item4[]" value="subitem4"> Sub Item 4</label><br>
		  <label><input type="checkbox" name="item4[]" value="subitem5"> Sub Item 5</label><br>

        
		<br>
		<p>
			<input type="submit" value="บันทึกข้อมูล" class="btn btn-success" >
			<td> <a href="index.php?rnd=<?= rand(); ?>&page=mark_person" class="btn btn-warning" >ยกเลิก</a> </td>
		</p>
		<br>
		<br>
	</form>
	</div>
	</div>
	</div>

</body>
</html>
