<?php
include '../Condb.php';
include '../users.inc.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);


$id = isset($_GET['id']) ? $_GET['id'] : "";
$pcid = isset($_GET['pcid']) ? $_GET['pcid'] : "";


//ตาราง อาวุธปืน
$sql = "SELECT * FROM `wm_tb_gun_confidential` WHERE case_no='$pcid' ";
$res = mysqli_query($conn,$sql);
$row = mysqli_fetch_array($res);

//print_r($res_gtr);
//print_r($row_gtr);
$aid_conf_gun = $row['aid_conf_gun'];
$case_no = $row['case_no'];

// สำหรับเลือก จังหวัด อำเภอ ตำบล
include("../config.inc.php");
include("../classes/class.database.inc.php");

// สำหรับเลือก จังหวัด อำเภอ ตำบล
$conn2 = get_connection(); // connect to mySQLDB

// สำหรับเลือกสถานีตำรวจทั่วประเทศ
$region = isset($_GET['region']) ? $_GET['region'] : 0; // sukhothai
$provincial = isset($_GET['provincial']) ? $_GET['provincial'] : 0; // sukhothai
$station = isset($_GET['station']) ? $_GET['station'] : 0; // sukhothai


?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>แก้ไขข้อมูล บัญชีของกลางอาวุธปืน</title>
<script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/policeinnopolis/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 18px;
    }
input::placeholder {
  color: red;
}
.form-control::placeholder {
  color: sandybrown;
}
</style>
<script src="/jquery-ui-1.12.1/jquery-ui.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/jquery-ui-1.12.1/jquery-ui.css">
<link rel="stylesheet" href="/js/datetimepicker2.5.1/jquery.datetimepicker.min.css">
<!-- สำหรับ วันเกิด แบบ พ.ศ. -->
<link rel="stylesheet" href="http://code.jquery.com/ui/1.9.1/themes/base/jquery-ui.css" />
<script src="http://code.jquery.com/jquery-1.8.2.js"></script>
<script src="/policeinnopolis/js/jquery-ui.js"></script>
</head>

<body>
	
	<div class="container">
	<div class="row">
	<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> แก้ไขข้อมูล บัญชีของกลางอาวุธปืน สภ.บ้านสวน (รายเดือน) </div>
	<form action="Save_confidential_gun.php" method="POST" enctype="multipart/form-data" class="body">
        
                    <label hidden ="hidden">ลำดับ</label>
		            <input type = "text" name = "aid_conf_gun" class="form-control" value= "<?= $row['aid_conf_gun']?>" hidden ="hidden" >
        
        <br>
         <label>สถานีตำรวจ <span style="color: #F90004">* จำเป็น</span></label><br>
                <!-- เลือกสถานีตำรวจ -->
        <label> บช. </label><br>
        <select name="region" id="region" onChange="do_region_change()" class="form-control" Required >
                <option value="">&lt;&lt; เลือก บช. &gt;&gt;</option>
                <?php
                $region = $row['region']; // <<ดึงข้อมูลจาก ตารางมา row ซึ่งเป็นคนละตัวกับ row_p
                //------------------------------------
			  	$res_re = $conn2->query("SELECT * FROM `wm_tb_police_region` 
                                            ORDER BY `name_region` ASC");
			     $selected = '';
			     while($row_re = $conn2->fetch_row($res_re)) 
                     {
                      $code_region = $row_re['code_region'];
                      $name_region = $row_re['name_region'];
                      // set default provionce to 64 >> sukhothai
                          if($code_region == $region) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$code_region' $selected> $name_region </option>\n";
                     }
			  ?>
              </select>
        
        
        <label> บก. </label><br>        
        <select name="provincial" id="provincial" onChange="do_provincial_change()" value="<?php echo $provincial; ?>" class="form-control" >
        <option value="0">&lt;&lt; เลือก บก. &gt;&gt;</option>
                <?php
                if($region > 0)
                {
                    $provincial = $row['provincial']; // <<
                    //-------------------------------------
                    $res_prov = $conn2->query("SELECT * FROM `wm_tb_police_provincial` 
                                                WHERE `region_code`='$region' 
                                                ORDER BY `provincial_code` ASC");
                    $selected = '';
                  while($res_prov = $conn2->fetch_row($res_prov)) 
                  {
                      $provincial_code = $res_prov['provincial_code']; // 
                      $provincial = $res_prov['provincial'];
                      // set default provionce to 64 >> sukhothai
                          if($provincial_code == $provincial) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$provincial_code' $selected> $provincial </option>\n";
                  }
                }	
                ?>
              </select>
        
         <label> สน./สภ. </label>
        <br>
                
        <select name="station" id="station" class="form-control" >
                <option value="0">&lt;&lt; เลือก สน./สภ. &gt;&gt;</option>
                <?php
	           if($provincial > 0)
                {
                   $station = $row['station'];
                   //------------------------------------------
                    $res_st = $conn2->query("SELECT * FROM `wm_tb_police_station2` 
                                                WHERE provincial_code='$provincial_code'
                                                ORDER BY `station_code` ASC");
                    $selected = '';
                  while($row_st = $conn2->fetch_row($res_st)) 
                  {
                      $station_code = $row_st['station_code'];
                      $station_name = $row_st['station_name'];
                          if($station_code == $station) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      echo "<option value='$station_code' $selected> $station_name </option>\n";
                  }
                }	
            
                ?>                                                                     
              </select>
		<br>
        
                    <label class="body">เลขคดี</label>
                    <input type = "text" name = "case_no" class="form-control" value= "<?= $row['case_no']?>" placeholder="เลขคดี" >

                    <label class="body">เลขยึด</label>
                    <input type = "text" name = "inspect_no" class="form-control" value= "<?= $row['inspect_no']?>" placeholder="เลขยึด" >

                    <label class="body"> รายการของกลาง </label>
                    <textarea class="form-control" id="gun_treasure_detail" name="gun_treasure_detail"  rows="5" placeholder="รายการของกลาง" ><?php echo $row['gun_treasure_detail'] ?></textarea><br>

                    <label>พนักงานสอบสวน</label>
                    <select id="inquiry_official" name="inquiry_official" class="form-select form-select-sm" placeholder="พนักงานสอบสวน">
                        <option value="" selected> </option>
                        <?php
                            $bs_inq = mysqli_query($conn, "SELECT * FROM `police_name_bs_inqinquiry` ORDER BY `police_name_bs_inqinquiry`.`aid_bs_inq` ASC");
                            while($row_inq = mysqli_fetch_array($bs_inq))
                            {
                                echo "<option value='{$row_inq['aid_bs_inq']}'>{$row_inq['bs_inq_name']}</option>";
                            }
                        ?>
                    </select>
                    <br>

                    <label class="body">ประเภทปืน</label> <!-- สำหรับ นับยอดใส่ ช่อง (1) = (2)+(13) Count(*) From ...WHERE confidential_gun -->
                    <select id="confidential_gun" name="confidential_gun" class="form-select form-select-sm"  placeholder="ประเภทปืน">
                        <option value="" selected> </option>
                        <option value="1">ปืนของกลางที่มีอัตลักษณ์ (ปืนที่ผลิตโดยโรงงาน มีแบรนด์ มีคุณภาพการผลิต)</option> <!-- สำหรับเข้าช่อง (2) AND confidential_gun='1'-->
                        <option value="2">ปืนของกลางที่ไม่มีมีอัตลักษณ์ (ปืนที่ไม่ได้ผลิตโดยโรงงาน ไม่มีแบรนด์ ไม่มีคุณภาพการผลิต หรือปืนไทยประดิษฐ์ เป็นต้น)</option> <!-- สำหรับเข้าช่อง (13) AND confidential_gun='2' -->
                    </select><br>

            <b class="body">ปืนของกลางที่มีอัตลักษณ์ <br>(ปืนที่ผลิตโดยโรงงาน มีแบรนด์ มีคุณภาพการผลิต)</b><br>
        
                    <label class="body">ผลคดีและการดำเนินการ</label><br>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<label class="body"><input name="unique_guns" id="unique_guns1" type="radio" value="1" <? if($row['unique_guns'] == "1") echo 'checked="checked" '; ?>/> ศาลสั่งริบ คดีถึงที่สุด </label><br>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<label class="body"><input name="unique_guns" id="unique_guns2" type="radio" value="2" <? if($row['unique_guns'] == "2") echo 'checked="checked" '; ?>/> ศาลสั่งคืน คดีถึงที่สุด </label><br>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<label class="body"><input name="unique_guns" id="unique_guns3" type="radio" value="3" <? if($row['unique_guns'] == "3") echo 'checked="checked" '; ?>/> คดียังไม่ถึงที่สุด </label><br><br>
        
                    <label class="body">กรณีไม่ปรากฎว่าผู้ใดเป็นผู้กระทำความผิดหรือคดีที่รู้ตัวแต่ยังจับตัวไม่ได้</label><br>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<label class="body"><input name="unique_guns_unknow" id="unique_guns_unknow1" type="checkbox" value="1" <? if($row['unique_guns_unknow'] == "1") echo 'checked="checked" '; ?>/> ขาดอายุความคดีอาญา </label><br><!-- สำหรับเข้าช่อง (6) out_limit='4'-->
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<label class="body"><input name="unique_guns_unknow" id="unique_guns_unknow2" type="checkbox" value="2" <? if($row['unique_guns_unknow'] == "2") echo 'checked="checked" '; ?>/> ยังอยู่ในอายุความคดีอาญา </label><br><br><!-- สำหรับเข้าช่อง (7) -->

                    <label class="body" style="color: black">กรณีศาลสั่งริบ คดีถึงที่สุดแล้ว</label><br>
                    <label class="body">ส่งกองสรรพวุธ</label><br>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<label class="body"><input name="sanpawut" id="sanpawut1" type="checkbox" value="1" <? if($row['sanpawut'] == "1") echo 'checked="checked" '; ?>/> ส่งกองสรรพวุธแล้ว (มีทะเบียน) </label><br><!-- สำหรับเข้าช่อง (6) out_limit='4'-->
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<label class="body"><input name="sanpawut" id="sanpawut2" type="checkbox" value="2" <? if($row['sanpawut'] == "2") echo 'checked="checked" '; ?>/> ส่งกองสรรพวุธแล้ว (ไม่มีทะเบียน) </label><br><!-- สำหรับเข้าช่อง (6) out_limit='4'-->
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<label class="body"><input name="sanpawut" id="sanpawut3" type="checkbox" value="3" <? if($row['sanpawut'] == "3") echo 'checked="checked" '; ?>/> รอส่งกองสรรพวุธ </label><br><br><!-- สำหรับเข้าช่อง (6) out_limit='4'-->
                
                    <label class="body">ประสานนายอำเภอเพิกถอนทะเบียน</label><br>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<label class="body"><input name="DistrictChief_co" id="DistrictChief_co1" type="checkbox" value="1" <? if($row['DistrictChief_co'] == "1") echo 'checked="checked" '; ?>/> ประสานแล้ว </label><br>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<label class="body"><input name="DistrictChief_co" id="DistrictChief_co2" type="checkbox" value="2" <? if($row['DistrictChief_co'] == "2") echo 'checked="checked" '; ?>/> ยังไม่ได้ประสาน </label><br><br>
        
                    <label class="body">นายอำเภอเพิกถอนทะเบียน</label><br>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<label class="body"><input name="DistrictChief_revoke" id="DistrictChief_revoke1" type="checkbox" value="1" <? if($row['DistrictChief_revoke'] == "1") echo 'checked="checked" '; ?>/> เพิกถอนทะเบียนแล้ว </label><br>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<label class="body"><input name="DistrictChief_revoke" id="DistrictChief_revoke2" type="checkbox" value="2" <? if($row['DistrictChief_revoke'] == "2") echo 'checked="checked" '; ?>/> ยังไม่ได้เพิกถอน </label><br><br>

        <!-- สำหรับเข้าช่อง (12) = (11)/(10) * 100-->
        
                  <!--  ===============================  -->
        
            <b class="body">ปืนของกลางที่ไม่มีมีอัตลักษณ์ <br>(ปืนที่ไม่ได้ผลิตโดยโรงงาน ไม่มีแบรนด์ ไม่มีคุณภาพการผลิต หรือปืนไทยประดิษฐ์ เป็นต้น)</b><br>
        
                     <label class="body">ผลคดีและการดำเนินการ</label><br>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<label class="body"><input name="no_identify" id="no_identify1" type="radio" value="1" <? if($row['no_identify'] == "1") echo 'checked="checked" '; ?>/> ศาลสั่งริบ คดีถึงที่สุด </label><br>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<label class="body"><input name="no_identify" id="no_identify3" type="radio" value="3" <? if($row['no_identify'] == "3") echo 'checked="checked" '; ?> /> ศาลสั่งคืน คดีถึงที่สุด </label><br>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<label class="body"><input name="no_identify" id="no_identify2" type="radio" value="2" <? if($row['no_identify'] == "2") echo 'checked="checked" '; ?>/> คดียังไม่ถึงที่สุด </label><br><br>
                    
                    <label class="body">กรณีไม่ปรากฎว่าผู้ใดเป็นผู้กระทำความผิดหรือคดีที่รู้ตัวแต่ยังจับตัวไม่ได้</label><br>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<label class="body"><input name="no_identify_unknow" id="no_identify_unknow1" type="checkbox" value="1" <? if($row['no_identify_unknow'] == "1") echo 'checked="checked" '; ?>/> ขาดอายุความคดีอาญา </label><br><!-- สำหรับเข้าช่อง (6) out_limit='4'-->
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<label class="body"><input name="no_identify_unknow" id="no_identify_unknow2" type="checkbox" value="2" <? if($row['no_identify_unknow'] == "2") echo 'checked="checked" '; ?>/> ยังอยู่ในอายุความคดีอาญา </label><br><br>

                     <label class="body">กรณีศาลสั่งริบ คดีถึงที่สุดแล้ว</label><br>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<label class="body"><input name="destroy_unknow" id="destroy_unknow1" type="checkbox" value="1" <? if($row['destroy_unknow'] == "1") echo 'checked="checked" '; ?>/> ทำลายแล้ว </label><br><!-- สำหรับเข้าช่อง (18) destroy_unknow -->
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<label class="body"><input name="destroy_unknow" id="destroy_unknow2" type="checkbox" value="2" <? if($row['destroy_unknow'] == "2") echo 'checked="checked" '; ?>/> รอทำลาย </label><br><br>
        
        <!-- (19) = (18)/[ (14)+(16) ] * 100 -->

                    <b class="body">สถานะการดำเนินการ</b><br>
                    <label class="body">ดำเนินการอยู่ในขั้นตอนใด</label>
                    <select id="status" name="status" class="form-select form-select-sm"  placeholder="การดำเนินการ">
                        <option value="" selected> </option>
                        <option value="1">ยังอยู่ในคลัง รอคำสั่งศาล</option>
                        <option value="2">ยังอยู่ในคลัง อยู่ระหว่างประสานเจ้าของมารับคืน</option>
                        <option value="3">คืนเจ้าของแล้ว</option>
                        <option value="4">ยังอยู่ในคลัง รอทำลาย</option>
                        <option value="5">ทำลายแล้ว</option>
                        <option value="6">ส่งสรรพาวุธแล้ว</option>
                    </select><br>
        
                    <b class="body">ระบุผู้บันทึก</b><br>
                    <label>เจ้าหน้าที่บันทึก</label>
                    <select id="record_name" name="record_name" class="form-select form-select-sm"  placeholder="เจ้าหน้าที่บันทึก">
                        <option value="" selected> </option>
                        <option value="ส.ต.ต.ณพดล โคกสันเที๊ยะ">ส.ต.ต.ณพดล โคกสันเที๊ยะ</option>
                        <option value="ส.ต.ต.พิเชษฐ์ วิชิตนาค">ส.ต.ต.พิเชษฐ์ วิชิตนาค</option>
                    </select><br>
        
                     <label>วันที่ <span style="color: #F90004">* จำเป็น</span></label>
                       <p><input type="text" name="record_date" id="datepicker" class="form-control" value= "<?= $row['record_date']?>" autocomplete="off" required></p>
                      <script>
                            function set_cal(ele)//function สร้างตัวเลือกปฎิทิน
                            {
                              $( ele ).datepicker({
                                  onSelect:(date_text)=>
                                  {
                                    let arr=date_text.split("/");
                                    let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])+543).toString();
                                    $(ele).val(new_date);
                                    $(ele).css("color","");
                                  },
                                  beforeShow:()=>{

                                    if($(ele).val()!="")
                                    {
                                      let arr=$(ele).val().split("/");
                                      let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])-543).toString();
                                      $(ele).val(new_date);

                                    }

                                    $(ele).css("color","white");
                                  },
                                  onClose:()=>{

                                      $(ele).css("color","");

                                      if($(ele).val()!="")
                                      {
                                          let arr=$(ele).val().split("-");
                                          if(parseInt(arr[2])<2500)
                                          {
                                              let new_date=arr[0]+"-"+arr[1]+"-"+(parseInt(arr[2])).toString();
                                              $(ele).val(new_date);
                                          }
                                      }
                                  },
                                  dateFormat:"yy-mm-dd", //กำหนดรูปแบบวันที่เป็น วัน/เดือน/ปี
                                  changeMonth:true,//กำหนดให้เลือกเดือนได้
                                  changeYear:true,//กำหนดให้เลือกปีได้
                                  showOtherMonths:true,//กำหนดให้แสดงวันของเดือนก่อนหน้าได้
                              });
                            }
                          $(document).ready(function(){
                            //เรียก function set_cal เมื่อเปิดหน้าเว็บ โดยส่ง object element ที่มี id เป็น datepicker เป็นพารามิเตอร์
                            set_cal( $("#datepicker") );
                          })
                          </script>
                        <!-- สำหรับ วันเกิด แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ -->

                    <div class="mb-3">
                        <label for="formFileMultiple" class="form-label">เอกสาร (แนะนำ PDF ขนาดไม่เกิน 5 MB)</label>
                        <input class="form-control" type="file" id="conf_gun_file" name="conf_gun_file" multiple>
                    </div>

                    <p>
                    <br>
                        <input type="submit" value="บันทึกข้อมูล" class="btn btn-success" >
                        <td> <a href="/WatchmanData/Show_gun_treasure_all.php?rnd=<?= rand(); ?>" class="btn btn-warning" >ยกเลิก</a> </td>
                    </p>
                    <br>
                    <br>
	</form>
	</div>
	</div>
	</div>
<script>
// ฟังก์ชั่น เปลี่ยนภาค (บช.) เป็น ภ.จว. (บก.)
function do_region_change(current_value)
{
    current_value = (typeof current_value == 'undefined') ? 0 : current_value;
	var sel_region = document.getElementById("region");		
	var code_region = sel_region.options[sel_region.selectedIndex].value;

	$.get("../ajax/get_provincial.php?region=" + code_region, 
		  function(data, status){
				//alert("Data: " + data + "\nStatus: " + status);
				// jQuery style						
				$('#provincial').find('option').remove().end();
				var data = $.trim(data);
				if(data == '') {
					alert("ไม่พบข้อมูล บก. !");
					return;
				}

					//.append('<option value="whatever">text</option>').val('whatever');
				var datas = data.split(';');
				for(var i=0; i<datas.length; i++) {
					if(datas[i] == '') continue;
					datas[i] = datas[i].split(':');
					var code_region = datas[i][0];
					var name_region = datas[i][1];
					//$('#provincial').append('<option value="'+ code_region+'">' + name_region + '</option>');
                    if(current_value == code_region) {
                        $('#provincial').append('<option value="'+ code_region+'" selected>' + name_region + '</option>'); 
                    }
                    else {
                        $('#provincial').append('<option value="'+ code_region+'">' + name_region + '</option>');
                    }
				}
                // Trigger the change event on the amphur dropdown so the tambon dropdown will be populated     
                $('#provincial').trigger('change');
		});
}
    //เลือก สถานี จากจังหวัด
function do_provincial_change(current_value)
{
    current_value = (typeof current_value == 'undefined') ? 0 : current_value;
    
	var sel_provincial = document.getElementById("region");		
	var provincial_code = sel_provincial.options[sel_provincial.selectedIndex].value;
    
    var sel_provincial2 = document.getElementById("provincial");		
	var station_code = sel_provincial2.options[sel_provincial2.selectedIndex].value;

	$.get("../ajax/get_station.php?region=" + provincial_code + "&provincial=" + station_code, 
			  function(data, status){
					//alert("Data: " + data + "\nStatus: " + status);
					// jQuery style						
					$('#station').find('option').remove().end();
					var data = $.trim(data);
					if(data == '') {
						alert("ไม่พบข้อมูลสถานี !");
						return;
					}

						//.append('<option value="whatever">text</option>').val('whatever');
					var datas = data.split(';');
					for(var i=0; i<datas.length; i++) {
						if(datas[i] == '') continue;
						datas[i] = datas[i].split(':');
						var station_code = datas[i][0];
						var station_name = datas[i][1];
                        if(current_value == station_code) {
                            $('#station').append('<option value="'+ station_code+'" selected>' + station_name + '</option>'); 
                        }
                        else {
                            $('#station').append('<option value="'+ station_code+'">' + station_name + '</option>');   
                        }						
					}
			});
}
</script>
<script>
// ชื่อ >> value >> set selct auto (สคลิปตรวจสอบ Selected) จำเป็นต้องก็อปปี้ <script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js" type="text/javascript">
function auto_select(sid, value)
{
    $('#' + sid).val(value);
}

function auto_thaidate(sid, value)
{
	if(value != '') {
		var dates = value.split('-');
		dates = value.split('-');
		if(dates.length > 2) {
			var ys = dates[0];
			var ms = dates[1];
			var ds = dates[2];
			if(ys.substr(0,2) == '20') {
				ys = parseInt(ys) + 543;
			}
			value = $.digit(ds,2) + '/' + $.digit(ms,2) + '/' +ys;
			$('#' + sid).val(value);
		}
	}
}

$(document).ready(function() {
<?php
    echo "auto_select('inquiry_official', '{$row['inquiry_official']}');\n";
    echo "auto_select('confidential_gun', '{$row['confidential_gun']}');\n";
    echo "auto_select('status', '{$row['status']}');\n";
    echo "auto_select('record_name', '{$row['record_name']}');\n";
    // convert db date to thai dates
	//echo "auto_thaidate('record_date', '{$row['record_date']}');\n";
?>
    setTimeout("do_region_change('<?= $row['provincial'] ?>')", 1000);
    setTimeout("do_provincial_change('<?= $row['station'] ?>')", 2000);
    });
</script>
</body>
</html>
