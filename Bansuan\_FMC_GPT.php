<?php
// Define database credentials
include '../Condb.php';

$personId = 5671100029864;

// Define function to recursively generate family tree chart
function generateChart($personId, $level, $maxLevel) {
    //global $conn;
    // Check recursion depth
    if ($level > $maxLevel) {
        return;
    }
    
    // Get details of current person
    $query = "SELECT * FROM wm_tb_personal WHERE ps_cl_idcard = '$personId'";
    $result = mysqli_query($conn, $query);
    
    echo $query;
    
    if (!$result) {
        die("Error: " . mysqli_error($conn)); // print error message and stop script execution
    }
    $person = mysqli_fetch_assoc($result);
    
    
    // Generate label for current person
    $label = $person['ps_cl_idcard'];
    if ($level == 1) {
        $label .= ' (Father/Mother)';
    }
    
    // Output label as an image of 2.5 cm
    echo '<img src="fmc_label.php?text=' . urlencode($label) . '&size=25" />';
    
    // Check if current person has any children
    $query = "SELECT * FROM wm_tb_personal PS
                LEFT JOIN wm_tb_spouse AS SP ON PS.ps_cl_idcard = SP.sp_cl_idcard
                WHERE PS.ps_cl_father_pid = '$personId' OR PS.ps_cl_mother_pid = '$personId' ";
    
    $result = mysqli_query($conn, $query);
    if (!$result) {
        die("Error: " . mysqli_error($conn)); // print error message and stop script execution
    }
    //echo $query;
    
    if (mysqli_num_rows($result) > 0) {
        // Output connecting line
        echo '<img src="line.png" />';
        
        // Output children recursively
        while ($child = mysqli_fetch_assoc($result)) {
            generateChart($child['ps_cl_idcard'], $level + 1);
            if ($child['ps_cl_sex'] == 'ชาย') {
                // Output connecting line for spouse
                echo '<img src="line.png" />';
                generateChart($child['sp_cl_idcard_spouse'], $level + 1);
            }
        }
    }
}

// Call function for the root person (e.g. the user)
$maxLevel = 10; // set maximum recursion depth
generateChart($personId, 1, $maxLevel); // call function with maximum depth
