<?php
include '../Condb.php';
// Query to fetch data
$query = "SELECT * FROM test_graph_table";
$result = $conn->query($query);

// Include the PHPExcel library
//require '../PHPExcel/Classes/PHPExcel.php';
require 'vendor/autoload.php';

// Create a new PHPExcel object
//$objPHPExcel = new PHPExcel();
$objPHPExcel = new \PhpOffice\PhpSpreadsheet\Spreadsheet();

// Set the column headers
$objPHPExcel->getActiveSheet()->setCellValue('A1', 'aid');
$objPHPExcel->getActiveSheet()->setCellValue('B1', 'g_date');
$objPHPExcel->getActiveSheet()->setCellValue('C1', 'tumbon');
$objPHPExcel->getActiveSheet()->setCellValue('D1', 'category');
$objPHPExcel->getActiveSheet()->setCellValue('E1', 'goals');
$objPHPExcel->getActiveSheet()->setCellValue('F1', 'performance');

$row = 2; // Start from the second row

// Loop through the data and populate the cells
if ($result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $objPHPExcel->getActiveSheet()->setCellValue('A' . $row, $row['aid']);
        $objPHPExcel->getActiveSheet()->setCellValue('B' . $row, $row['g_date']);
        $objPHPExcel->getActiveSheet()->setCellValue('C' . $row, $row['tumbon']);
        $objPHPExcel->getActiveSheet()->setCellValue('D' . $row, $row['category']);
        $objPHPExcel->getActiveSheet()->setCellValue('E' . $row, $row['goals']);
        $objPHPExcel->getActiveSheet()->setCellValue('F' . $row, $row['performance']);
        $row++;
    }
}

// Set the appropriate headers for Excel
//header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
//header('Content-Disposition: attachment;filename="data.xlsx"');
/*header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
header('Content-Disposition: attachment;filename="data.xlsx"');*/
header("Content-Type: application/xls");
header("Content-Disposition: attachment; filename=export.xls");
header("Pragma: no-cache");
header("Expires: 0");

// Save the Excel file
//$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');
//$objWriter->save('php://output');
$objWriter = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($objPHPExcel, 'Xlsx');
$objWriter->save('php://output');

// Close the database connection
$conn->close();
?>