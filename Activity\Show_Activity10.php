<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';
include '../Head_image.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

//get month from select or current month
$month = isset($_GET['month']) ? $_GET['month'] : date("m");
$year = isset($_GET['year']) ? $_GET['year'] : date("Y");

$m1 = '';
$m2 = '';
$m3 = '';
$m4 = '';
$m5 = '';
$m6 = '';
$m7 = '';
$m8 = '';
$m9 = '';
$m10 = '';
$m11 = '';
$m12 = '';
switch($month)
{
    case "01": $m1 = "selected"; break;
    case "02": $m2 = "selected"; break;
    case "03": $m3 = "selected"; break;
    case "04": $m4 = "selected"; break;
    case "05": $m5 = "selected"; break;
    case "06": $m6 = "selected"; break;
    case "07": $m7 = "selected"; break;
    case "08": $m8 = "selected"; break;
    case "09": $m9 = "selected"; break;
    case "10": $m10 = "selected"; break;
    case "11": $m11 = "selected"; break;
    case "12": $m12 = "selected"; break;
}
$current_month = $global_thai_month[0+$month];
$sel_y = ($year + 543);

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<base target="_top">
<title>รายงานการขับเคลื่อนงานสืบสวน</title>
<link rel="stylesheet" href="/bootstrap/css/bootstrap-grid.css">
<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<style type="text/css">
body{
    font-size:16px; 
}
.textAlignVer{
    display:block;
    filter: flipv fliph;
    -webkit-transform: rotate(-90deg); 
    -moz-transform: rotate(-90deg); 
    transform: rotate(-90deg); 
    position:relative;
    width:20px;
    white-space:nowrap;
    font-size:16px;
    margin-bottom:10px;
}
    .box !im{
        border: black;
        border-top-color: black !important;
        border-bottom-color: black !important;
        border-left-color: black !important;
        border-right-color: black !important;
        
    }
</style>
    
</head>
	
<body>
<div class="container-fluid" align="left">
<div class=" h3 text-center  alert alert-success mb-4 mt-4 " role="alert" >กิจกรรม 10 การตรวจสอบรถต้องสงสัย</div>
<h3 align="center">ตารางสรุปผลการตรวจสอบรถต้องสงสัย สภ.บ้านสวน (แยกรายเดือน)</h3>
    
<div>
    <table width="80%" border="0" cellspacing="1" cellpadding="1">
      <tbody>
        <tr>
          <td width="24%">&nbsp;<a href="/WM/index.php?rnd=<?= rand(); ?>&page=report" class="btn btn-warning btn-lg mb-4" >ย้อนกลับ</a>&nbsp;&nbsp;<a href="#Add_mission.php" class="btn btn-success btn-lg mb-4" >เพิ่มข้อมูล</a></td>
          <td width="14%">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<b style="font-size: 25px">ประจำเดือน</b></td>    
          <td width="12%"><label>เลือกปี </label>
            <select id="mi_cl_year" name="mi_cl_year" class="form-select col-8 d-lg-inline" style="background-color: khaki; font-size: 16px" onChange="dates_change()">
              <option value="">เลือก</option>
              <?php
                $cur_y = date("Y") + 2;
                for($y=2020; $y<$cur_y; $y++) {
                    $sel = ($y == $year) ? "selected" : '';
                    echo "<option value='$y' $sel> ". ($y + 543) ." </option>";
                }
            ?>
          </select></td>
          <td width="2%"></td>
          <td width="16%"><label>เดือน</label>
            <select id="mi_cl_month" name="mi_cl_month" class="form-select col-3 d-lg-inline" style="background-color: khaki; font-size: 16px" onChange="dates_change()">
              <option value="">เลือก</option>
              <option <?= $m1 ?> value="01">มกราคม</option>
              <option <?= $m2 ?> value="02">กุมภาพันธ์</option>
              <option <?= $m3 ?> value="03">มีนาคม</option>
              <option <?= $m4 ?> value="04">เมษายน</option>
              <option <?= $m5 ?> value="05">พฤษภาคม</option>
              <option <?= $m6 ?> value="06">มิถุนายน</option>
              <option <?= $m7 ?> value="07">กรกฎาคม</option>
              <option <?= $m8 ?> value="08">สิงหาคม</option>
              <option <?= $m9 ?> value="09">กันยายน</option>
              <option <?= $m10 ?> value="10">ตุลาคม</option>
              <option <?= $m11 ?> value="11">พฤศจิกายน</option>
              <option <?= $m12 ?> value="12">ธันวาคม</option>
          </select></td>
          <td width="27%">&nbsp;</td>
          <td width="5%">&nbsp;</td>
        </tr>
      </tbody>
    </table><br>
</div>
    
<table width="90%" border="2" cellspacing="1" cellpadding="1" class="table table-bordered">
  <tbody>
         <tr class="box" height=28 style='height:21.0pt'>
           <td rowspan="2" bordercolor="#0C0B0B" class="box" style='border:solid #000000;
          height:21.0pt; background-color:yellow'>&nbsp;ลำดับ</td>
           <td rowspan="2" bordercolor="#000000" class="box" style='border:solid #000000; background-color:yellow '>รถตรวจยึด คงเหลือ จากเดือนที่ผ่านมา             <p>&nbsp;</p></td>
           <td height="28" colspan="4" bordercolor="#000000" class="box" style='border:solid #000000; background-color:yellow '>รถตรวจยึด (
             <?= $current_month ?>
           )</td>
           <td height=28 colspan=3 bordercolor="#0C0B0B" class="box" style='border:solid #000000;
          height:21.0pt; background-color:yellow '>ผลการตรวจสอบ (
             <?= $current_month ?>
           )</td>
           <td height=28 colspan="3" bordercolor="#0C0B0B" class="box" style='border:solid #000000;
          height:21.0pt; background-color:yellow '>ผลการดำเนินการ</td>
           <td rowspan="2" bordercolor="#0C0B0B" class="box" style='border:solid #000000;
          height:21.0pt; background-color:yellow '>ความสำเร็จ (%)</td>
           <td rowspan="2" bordercolor="#0C0B0B" class="box" style='border:solid #000000;
          height:21.0pt; background-color:yellow '>หมายเหตุ</td>
         </tr>
         <tr class="box" height=28 style='height:21.0pt'>
           <td  bordercolor="#000000" class="box" style='border:solid #000000; background-color:yellow '><p>รถยนต์</p></td>
          <td  bordercolor="#000000" class="box" style='border:solid #000000; background-color:yellow '><p>รถ จยย.</p></td>
          <td class="box" width=104 style='border:solid #000000;
          border-top:none;width:78pt; background-color:yellow ' bordercolor="#0C0B0B">รถอื่น ๆ</td>
          <td align=left class="box" style='border:solid #000000; background-color:yellow '>รวม</td>
          <td align=left class="box" style='border:solid #000000; background-color:yellow '>เกี่ยวกับคดี</td>
          <td class="box" style='border:solid #000000; background-color:yellow '><p>ไม่เกี่ยวกับคดี</td>
          <td class="box" style='border:solid #000000; background-color:yellow '>ไม่ได้ตรวจสอบ</td>
          <td class="box" style='border:solid #000000; background-color:yellow '>ส่ง พงส.</td>
          <td class="box" style='border:solid #000000; background-color:yellow '>ส่งคืนเจ้าของ</td>
          <td class="box" style='border:solid #000000; background-color:yellow '>คงเหลือ</td>
         </tr>
<?php     
                      
$sql_2 = "SELECT COUNT(*) FROM wm_tb_suspect_vehicle WHERE sv_cl_date ";
    $stmt2 = $pdo->prepare($sql_2);
try{
    $stmt2->execute();
}catch (PDOException $e) {
    echo '$sql_2 Failed: '. $e->getMessage();
}               
	$row_2 = $stmt2->fetch(PDO::FETCH_NUM);
    $total_2 = $row_2[0];
    
    //echo $sql_2;
                      
$sql = "SELECT " .
            "(SELECT COUNT(*) FROM wm_tb_suspect_vehicle WHERE station=:station AND sv_cl_vehicle_type='2' AND MONTH(sv_cl_date) = :month AND YEAR(sv_cl_date) = :year) AS T1, ".   //รถยนต์
            "(SELECT COUNT(*) FROM wm_tb_suspect_vehicle WHERE station=:station AND sv_cl_vehicle_type='1' AND MONTH(sv_cl_date) = :month AND YEAR(sv_cl_date) = :year) AS T2, " .  //รถ จยย.
            "(SELECT COUNT(*) FROM wm_tb_suspect_vehicle WHERE station=:station AND sv_cl_vehicle_type='3' AND MONTH(sv_cl_date) = :month AND YEAR(sv_cl_date) = :year) AS T3, " .  //อื่น ๆ
            "(SELECT COUNT(*) FROM wm_tb_suspect_vehicle WHERE station=:station AND checking='1' AND MONTH(sv_cl_date) = :month AND YEAR(sv_cl_date) = :year) AS T4, " .  //เกี่ยวกับคดี
            "(SELECT COUNT(*) FROM wm_tb_suspect_vehicle WHERE station=:station AND checking='2' AND MONTH(sv_cl_date) = :month AND YEAR(sv_cl_date) = :year) AS T5, " .  //ไม่เกี่ยวกับคดี
            "(SELECT COUNT(*) FROM wm_tb_suspect_vehicle WHERE station=:station AND checking='3' AND MONTH(sv_cl_date) = :month AND YEAR(sv_cl_date) = :year) AS T6, " .  //ยังไม่ได้ตรวจสอบ
            "(SELECT COUNT(*) FROM wm_tb_suspect_vehicle WHERE station=:station AND action='1' AND MONTH(sv_cl_date) = :month AND YEAR(sv_cl_date) = :year) AS T7, " .  //ส่งพนักงานสอบสวน
            "(SELECT COUNT(*) FROM wm_tb_suspect_vehicle WHERE station=:station AND action='2' AND MONTH(sv_cl_date) = :month AND YEAR(sv_cl_date) = :year) AS T8 ".   //ส่งคืนเจ้าของ
            "";
                      
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':month', $month);
    $stmt->bindParam(':year', $year);
	$stmt->bindParam(':station', $station);
try{
    $stmt->execute();
}catch (PDOException $e) {
    echo '$sql Failed: '. $e->getMessage();
}
    
// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no = 1;
while($row = $stmt->fetch(PDO::FETCH_ASSOC))	//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
{
//ฟังก์ชั่น วันที่ ดึงจาก condb
//$strDate = DateThai( $row_mi["date_record"] );
    
$total = $row["T1"] + $row["T2"] + $row["T3"];
$remaining = $total - ($row["T7"]+$row["T8"]);
$return = $row["T7"]+$row["T8"];
    
if($total > 0) {   
    $persen = ($return*100)/$total;
}
else {
    $persen = 0;
    }
    
$to_mo = $remaining;
    //echo $remaining;
   // echo $total;
   // echo $return;
    //echo $persen;
?>	

    <tr>
	  <td style="font-size: 24px"><?= $no ?> </td> 
      <td style="font-size: 24px" nowrap>&nbsp;<?= $to_mo ?></td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T1"] > 0) ? $row["T1"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T2"] > 0) ? $row["T2"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T3"] > 0) ? $row["T3"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px"><?= $total ?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T4"] > 0) ? $row["T4"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T5"] > 0) ? $row["T5"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T6"] > 0) ? $row["T6"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T7"] > 0) ? $row["T7"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T8"] > 0) ? $row["T8"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?= $remaining ?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?= $persen ?>&nbsp;%</td>
      <td style="font-size: 24px">&nbsp;</td>
    </tr>
  </tbody>
<?php
        
	$no++;
}
?>
</table>
</div>

<script>
function Del(url)
{
	if(confirm("คุณจะลบข้อมูล หรือ ไม่ ?"))
	{
		window.location = url;		
	}
}
function dates_change()  // คำสั่งเปลี่ยนเดือน ปี
{
    var ms = $("#mi_cl_month option:selected").val();
    var ys = $("#mi_cl_year option:selected").val();
    window.location = "/Activity/Show_Activity10.php?&month=" + ms + "&year=" + ys + "&rnd=" + Math.random();
}
</script>

<br>
<hr>
<br>
<h3 align="center">ตารางสรุปผลการตรวจสอบรถต้องสงสัย สภ.บ้านสวน (ทั้งหมด ปี <?= $sel_y ?>)</h3>
    
<table width="90%" border="2" cellspacing="1" cellpadding="1" class="table table-bordered">
  <tbody>
         <tr class="box" height=28 style='height:21.0pt'>
           <td rowspan="2" bordercolor="#0C0B0B" class="box" style='border:solid #000000;
          height:21.0pt; background-color:yellow'>ลำดับ</td>
           <td rowspan="2" bordercolor="#000000" class="box" style='border:solid #000000; background-color:yellow '>หน่วยงาน
             <p>&nbsp;</p></td>
           <td height="28" colspan="4" bordercolor="#000000" class="box" style='border:solid #000000; background-color:yellow '>รถตรวจยึด (ทั้งหมด)</td>
           <td height=28 colspan=3 bordercolor="#0C0B0B" class="box" style='border:solid #000000;
          height:21.0pt; background-color:yellow '>ผลการตรวจสอบ (ทั้งหมด)</td>
           <td height=28 colspan="3" bordercolor="#0C0B0B" class="box" style='border:solid #000000;
          height:21.0pt; background-color:yellow '>ผลการดำเนินการ</td>
           <td rowspan="2" bordercolor="#0C0B0B" class="box" style='border:solid #000000;
          height:21.0pt; background-color:yellow '>ความสำเร็จ (%)</td>
           <td rowspan="2" bordercolor="#0C0B0B" class="box" style='border:solid #000000;
          height:21.0pt; background-color:yellow '>หมายเหตุ</td>
         </tr>
         <tr class="box" height=28 style='height:21.0pt'>
           <td  bordercolor="#000000" class="box" style='border:solid #000000; background-color:yellow '><p>รถยนต์</p></td>
          <td  bordercolor="#000000" class="box" style='border:solid #000000; background-color:yellow '><p>รถ จยย.</p></td>
          <td class="box" width=104 style='border:solid #000000;
          border-top:none;width:78pt; background-color:yellow ' bordercolor="#0C0B0B">รถอื่น ๆ</td>
          <td align=left class="box" style='border:solid #000000; background-color:yellow '>รวม</td>
          <td align=left class="box" style='border:solid #000000; background-color:yellow '>เกี่ยวกับคดี</td>
          <td class="box" style='border:solid #000000; background-color:yellow '><p>ไม่เกี่ยวกับคดี</td>
          <td class="box" style='border:solid #000000; background-color:yellow '>ไม่ได้ตรวจสอบ</td>
          <td class="box" style='border:solid #000000; background-color:yellow '>ส่ง พงส.</td>
          <td class="box" style='border:solid #000000; background-color:yellow '>ส่งคืนเจ้าของ</td>
          <td class="box" style='border:solid #000000; background-color:yellow '>คงเหลือ</td>
         </tr>
<?php     
                      
$sql3 = "SELECT " .
            "(SELECT COUNT(*) FROM wm_tb_suspect_vehicle WHERE station=:station AND sv_cl_vehicle_type='2' AND YEAR(sv_cl_date) = :year ) AS T11, ".   //รถยนต์
            "(SELECT COUNT(*) FROM wm_tb_suspect_vehicle WHERE station=:station AND sv_cl_vehicle_type='1' AND YEAR(sv_cl_date) = :year ) AS T21, " .  //รถ จยย.
            "(SELECT COUNT(*) FROM wm_tb_suspect_vehicle WHERE station=:station AND sv_cl_vehicle_type='3' AND YEAR(sv_cl_date) = :year ) AS T31, " .  //อื่น ๆ
            "(SELECT COUNT(*) FROM wm_tb_suspect_vehicle WHERE station=:station AND checking='1' AND YEAR(sv_cl_date) = :year ) AS T41, " .  //เกี่ยวกับคดี
            "(SELECT COUNT(*) FROM wm_tb_suspect_vehicle WHERE station=:station AND checking='2' AND YEAR(sv_cl_date) = :year ) AS T51, " .  //ไม่เกี่ยวกับคดี
            "(SELECT COUNT(*) FROM wm_tb_suspect_vehicle WHERE station=:station AND checking='3' AND YEAR(sv_cl_date) = :year ) AS T61, " .  //ยังไม่ได้ตรวจสอบ
            "(SELECT COUNT(*) FROM wm_tb_suspect_vehicle WHERE station=:station AND action='1' AND YEAR(sv_cl_date) = :year ) AS T71, " .  //ส่งพนักงานสอบสวน
            "(SELECT COUNT(*) FROM wm_tb_suspect_vehicle WHERE station=:station AND action='2' AND YEAR(sv_cl_date) = :year ) AS T81 ".   //ส่งคืนเจ้าของ
            "";
                      
    $stmt3 = $pdo->prepare($sql3);
    $stmt3->bindParam(':year', $year);
	$stmt3->bindParam(':station', $station);
try{
    $stmt3->execute();
}catch (PDOException $e) {
    echo '$sql3 Failed: '. $e->getMessage();
}
    
// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no2 = 1;
while($row2 = $stmt3->fetch(PDO::FETCH_ASSOC))	//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
{
//ฟังก์ชั่น วันที่ ดึงจาก condb
//$strDate = DateThai( $row_mi["date_record"] );
    
$total2 = $row2["T11"] + $row2["T21"] + $row2["T31"];
$remaining2 = $total2 - ($row2["T71"]+$row2["T81"]);
$return2 = $row2["T71"]+$row2["T81"];
    
if($total2 > 0) {   
    $persen2 = ($return2*100)/$total2;
}
else {
    $persen2 = 0;
    }
    
    //echo $remaining;
   // echo $total;
   // echo $return;
    //echo $persen;
?>	

    <tr>
	  <td style="font-size: 24px"><?= $no2 ?> </td> 
      <td style="font-size: 24px" nowrap>&nbsp;<?= $name_station ?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row2["T11"] > 0) ? $row2["T11"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row2["T21"] > 0) ? $row2["T21"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row2["T31"] > 0) ? $row2["T31"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px"><?= $total2 ?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row2["T41"] > 0) ? $row2["T41"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row2["T51"] > 0) ? $row2["T51"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row2["T61"] > 0) ? $row2["T61"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row2["T71"] > 0) ? $row2["T71"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row2["T81"] > 0) ? $row2["T81"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?= $remaining2 ?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?= $persen2 ?>&nbsp;%</td>
      <td style="font-size: 24px">&nbsp;</td>
    </tr>
  </tbody>
<?php
	$no2++;
}
?>
</table>
<p>&nbsp;</p>
<p>&nbsp;</p>  
    