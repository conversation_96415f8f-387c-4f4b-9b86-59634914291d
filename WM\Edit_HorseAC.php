<?php
include "../Condb.php"; //PDO
include '../users.inc.php';
include '../right_user.php';


// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

$id = $_GET['id'];
//$pcid = isset($_GET['pcid']) ? $_GET['pcid'] : "";

$sql = "SELECT * FROM wm_tb_bank WHERE bk_cl_aid=:id ";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':id', $id);
    $stmt->execute();
    $row = $stmt->fetch(PDO::FETCH_ASSOC);

$oldValue = $row['bk_cl_horse'];

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>แก้ไขข้อมูลบัญชีม้า</title>
<link rel="stylesheet" href="/policeinnopolis/bootstrap/css/bootstrap.rtl.min.css">
<!-- สำหรับ วันที่ แบบ พ.ศ. -->
<link rel="stylesheet" href="http://code.jquery.com/ui/1.9.1/themes/base/jquery-ui.css" />
<script src="http://code.jquery.com/jquery-1.8.2.js"></script>
<script src="/policeinnopolis/js/jquery-ui.js"></script>
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 18px;
    }
input::placeholder {
  color: red;
}
.form-control::placeholder {
  color: sandybrown;
}
</style>
</head>

<body>
	<div class="container">
	<div class="row">
		<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> แก้ไขข้อมูลบัญชีม้า </div>
	<form action="Save_HorseAC.php" method="POST" enctype="multipart/form-data" class="body">	
			<label hidden ="hidden">ลำดับ</label>
			<input name="bk_cl_aid" type = "text" class="form-control" value= "<?=$row['bk_cl_aid']?>" hidden ="hidden"  >
			<label>เลขบัตรประชาชนเจ้าของบัญชี</label>
			<input name="bk_cl_idcard" type = "text" class="form-control" value= "<?=$row['bk_cl_idcard']?>" placeholder="เลขบัตร เจ้าของบัญชี" >
			<label>ชื่อเจ้าของบัญชี</label>
			<input type = "text" name="bk_cl_name" class="form-control" value= "<?=$row['bk_cl_name']?>" placeholder="ชื่อเจ้าของบัญชี" >
			<label>รหัสธนาคาร </label>
			<input type = "text" name = "bk_cl_code" class="form-control" value= "<?=$row['bk_cl_code']?>" placeholder="กรอกรหัสธนาคาร" >
			<label>ชื่อธนาคาร</label>
			<input type = "text" name = "bk_cl_bankname" class="form-control" value= "<?=$row['bk_cl_bankname']?>" placeholder="ระบุชื่อธนาคาร"  >
			<label>สาขา</label>
			<input type = "text" name = "bk_cl_branch" class="form-control" value= "<?=$row['bk_cl_branch']?>" placeholder="ระบุสาขา"  >
			<label>ที่อยู่</label>
			<input type = "text" name = "bk_cl_address" class="form-control" value= "<?=$row['bk_cl_address']?>" placeholder="กรอกข้อมูลที่อยู่ธนาคาร"  >
			<label>เลขบัญชี <span style="color: #F90004">* จำเป็น</span> </label>
			<input type = "text" name = "bk_cl_ac_no" class="form-control" value= "<?=$row['bk_cl_ac_no']?>" placeholder="ระบุเลขที่บัญชีธนาคาร"  Required  >
			<label>ยอดเงิน</label>
			<input type = "text" name = "bk_cl_balance" class="form-control" value= "<?=$row['bk_cl_balance']?>" placeholder="ระบุยอดเงินในบัญชี"  >
			<br>
			<b>หากเป็นบัญชีม้า ระบุข้อมูลเพิ่มเติม</b><br>
				<label for="bk_cl_horse">บัญชีม้า แถวที่</label>
					<select id="bk_cl_horse" name="bk_cl_horse" class="form-select form-select-sm">
						<option value="" selected> </option>
						<option value="1">แถว 1 รับจากผู้เสียหายบัญชีแรก</option>
						<option value="2">แถว 2 รับโอนต่อจากแถว 1</option>
						<option value="3">แถว 3 รับโอนต่อจากแถว 2</option>
						<option value="4">แถว 4 รับโอนต่อจากแถว 3</option>
						<option value="5">แถว 5 รับโอนต่อจากแถว 4</option>
						<option value="6">แถว 6 รับโอนต่อจากแถว 5</option>
						<option value="7">แถว 7 รับโอนต่อจากแถว 6</option>
						<option value="8">แถว 8 รับโอนต่อจากแถว 7</option>
						<option value="9">แถว 9 รับโอนต่อจากแถว 8</option>
						<option value="10">แถว 10 รับโอนต่อจากแถว 9</option>
					</select>
			<br>
		
			<label> รายละเอียดพอสังเขป </label>
        		<textarea class="form-control" id="bk_cl_detail" name="bk_cl_detail"  rows="5" placeholder="ระบุรายละเอียดพอสังเขป" ><?php echo $row['bk_cl_detail'] ?></textarea>
		
			<label>วันตรวจสอบ</label>
            <p><input type="text" name="bk_cl_inspect" id="datepicker" value= "<?=$row['bk_cl_inspect']?>" class="form-control" autocomplete="off" ></p>
            <script>
                function set_cal(ele)//function สร้างตัวเลือกปฎิทิน
                {
                  $( ele ).datepicker({
                      onSelect:(date_text)=>
                      {
                        let arr=date_text.split("/");
                        let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])+543).toString();
                        $(ele).val(new_date);
                        $(ele).css("color","");
                      },
                      beforeShow:()=>{

                        if($(ele).val()!="")
                        {
                          let arr=$(ele).val().split("/");
                          let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])-543).toString();
                          $(ele).val(new_date);

                        }

                        $(ele).css("color","white");
                      },
                      onClose:()=>{

                          $(ele).css("color","");

                          if($(ele).val()!="")
                          {
                              let arr=$(ele).val().split("-");
                              if(parseInt(arr[2])<2500)
                              {
                                  let new_date=arr[0]+"-"+arr[1]+"-"+(parseInt(arr[2])).toString();
                                  $(ele).val(new_date);
                              }
                          }
                      },
                      dateFormat:"yy-mm-dd", //กำหนดรูปแบบวันที่เป็น วัน/เดือน/ปี
                      changeMonth:true,//กำหนดให้เลือกเดือนได้
                      changeYear:true,//กำหนดให้เลือกปีได้
                      showOtherMonths:true,//กำหนดให้แสดงวันของเดือนก่อนหน้าได้
                  });
                }
              $(document).ready(function(){
                //เรียก function set_cal เมื่อเปิดหน้าเว็บ โดยส่ง object element ที่มี id เป็น datepicker เป็นพารามิเตอร์
                set_cal( $("#datepicker") );
              })
              </script>
            <!-- สำหรับ วันที่ แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ -->
			<label>หมายเหตุ</label>
			<input type = "text" name = "bk_cl_remark" class="form-control" value= "<?=$row['bk_cl_remark']?>" placeholder="หมายเหตุ"  >
		<br>
	<p>
    	<input type="submit" value="Update" class="btn btn-success" >
		<td> <a href="/policeinnopolis/WM/Show_HorseAC.php" class="btn btn-warning" >ยกเลิก</a> </td>
 	</p>
	</form>
	</div>
	</div>
	</div>

<script>
function auto_select(sid, value)
{
    $('#' + sid).val(value);
}

$(document).ready(function() {
<?php
    echo "auto_select('bk_cl_horse', '{$oldValue}');\n";
?>
    });
</script>
	
</body>
</html>