<?php
include '../Condb.php';  //PDO
include '../users.inc.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

$pcid = $_GET['pcid'];

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Add Home Visit Data</title>
<script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/policeinnopolis/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 18px;
    }
input::placeholder {
  color: red;
}
.form-control::placeholder {
  color: sandybrown;
}
</style>
<!-- สำหรับ วันที่ แบบ พ.ศ. -->
<link rel="stylesheet" href="http://code.jquery.com/ui/1.9.1/themes/base/jquery-ui.css" />
<script src="http://code.jquery.com/jquery-1.8.2.js"></script>
<script src="/policeinnopolis/js/jquery-ui.js"></script>
</head>

<body>
	<div class="container">
	<div class="row">
		<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> เพิ่มข้อมูลการตรวจเยี่ยมบุคคลพ้นโทษ </div>
	<form action="Save_home_visit.php" method="POST" enctype="multipart/form-data" class="body">
	<label> เลขบัตรประชาชน บุคคลพ้นโทษ <span style="color: #F90004">* จำเป็น</span> </label>
	<input type = "text" name="hv_cl_idcard" class="form-control" value="<?= $pcid ?>" readonly="readonly" >
        
    <label>วันที่ตรวจเยี่ยม <span style="color: #F90004">* จำเป็น</span> </label>
    <p><input type="text" name="hv_cl_datevisit" id="datepicker" class="form-control" autocomplete="off" required></p>
        <script>
            function set_cal(ele)//function สร้างตัวเลือกปฎิทิน
            {
              $( ele ).datepicker({
                  onSelect:(date_text)=>
                  {
                    let arr=date_text.split("/");
                    let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])+543).toString();
                    $(ele).val(new_date);
                    $(ele).css("color","");
                  },
                  beforeShow:()=>{

                    if($(ele).val()!="")
                    {
                      let arr=$(ele).val().split("/");
                      let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])-543).toString();
                      $(ele).val(new_date);

                    }

                    $(ele).css("color","white");
                  },
                  onClose:()=>{

                      $(ele).css("color","");

                      if($(ele).val()!="")
                      {
                          let arr=$(ele).val().split("-");
                          if(parseInt(arr[2])<2500)
                          {
                              let new_date=arr[0]+"-"+arr[1]+"-"+(parseInt(arr[2])).toString();
                              $(ele).val(new_date);
                          }
                      }
                  },
                  dateFormat:"yy-mm-dd", //กำหนดรูปแบบวันที่เป็น วัน/เดือน/ปี
                  changeMonth:true,//กำหนดให้เลือกเดือนได้
                  changeYear:true,//กำหนดให้เลือกปีได้
                  showOtherMonths:true,//กำหนดให้แสดงวันของเดือนก่อนหน้าได้
              });
            }
          $(document).ready(function(){
            //เรียก function set_cal เมื่อเปิดหน้าเว็บ โดยส่ง object element ที่มี id เป็น datepicker เป็นพารามิเตอร์
            set_cal( $("#datepicker") );
          })
          </script>
        <!-- สำหรับ วันที่ แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ -->
        
	<label> เจ้าหน้าที่ผู้ตรวจเยี่ยม </label>
	<input type = "text" name = "hv_cl_pol_visit" class="form-control" placeholder="ผู้ไปตรวจเยี่ยม"  >
	
	<label>พบ/ไม่พบ <span style="color: #F90004">* จำเป็น </span></label>
		<select id="hv_cl_find_or_not" name="hv_cl_find_or_not" class="form-select form-select-sm" aria-label=".form-select-sm example" required >
			<option value="" selected> </option>
			<option value="1">พบ</option>
			<option value="2">ไม่พบ</option>
		</select><br>
<b>กรณี พบตัว</b><br>
    <label> จำนวน </label>
        <select id="hv_cl_find" name="hv_cl_find" class="form-select form-select-sm" placeholder="จำนวนที่พบตัว" aria-label=".form-select-sm example" required >
			<option value="0" selected></option>
			<option value="1">1</option>
			<option value="2">2</option>
		</select>
    <label> จำนวนบันทึกใน 4.0 </label>
        <select id="hv_cl_find40" name="hv_cl_find40" class="form-select form-select-sm" placeholder="จำนวนที่พบตัว บันทึกใน Police 4.0" aria-label=".form-select-sm example" required >
			<option value="0" selected></option>
			<option value="1">1</option>
			<option value="2">2</option>
		</select>
    <label> เก็บ DNA </label>
		<select id="hv_cl_dna" name="hv_cl_dna" class="form-select form-select-sm" aria-label=".form-select-sm example" required >
			<option value="0" selected></option>
			<option value="1">เก็บ DNA แล้ว</option>
			<option value="2">เก็บไม่ได้เก็บ DNA</option>
		</select><br>
<b>กรณี ไม่พบตัว</b><br>   
	<label> เหตุที่ไม่พบ </label>
	<input type = "text" name = "hv_cl_cause" class="form-control" placeholder="ระบุสาเหตุที่ไม่พบตัว"  ><br>
<b style="color: black">ไม่พบตัว แต่ทราบที่อยู่ใหม่</b><br>
	<label> ที่อยู่/ที่ทำงานใหม่ </label>
	<input type = "text" name = "hv_cl_new_place" class="form-control" placeholder="ระบุที่อยู่/ที่ทำงานใหม่"  >
    <label> จำนวน </label>
        <select id="hv_cl_adr" name="hv_cl_adr" class="form-select form-select-sm" placeholder="จำนวนที่ทราบที่อยู่" aria-label=".form-select-sm example" >
			<option value="" selected></option>
			<option value="1">1</option>
			<option value="2">2</option>
            </select>
	<label> แจ้งท้องที่ใหม่ </label>
        <select id="hv_cl_action" name="hv_cl_action" class="form-select form-select-sm" placeholder="ระบุท้องที่ใหม่ที่ต้องแจ้งข้อมูล" aria-label=".form-select-sm example" >
			<option value="" selected></option>
			<option value="1">1</option>
			<option value="2">2</option>
            </select>
    <label> ยังไม่ได้แจ้ง </label>
        <select id="hv_cl_no_action" name="hv_cl_no_action" class="form-select form-select-sm" placeholder="ระบุจำนวนที่ยังไม่ได้แจ้งข้อมูล" aria-label=".form-select-sm example" >
			<option value="" selected></option>
			<option value="1">1</option>
			<option value="2">2</option>
            </select><br>
<b style="color: black">ไม่พบตัว และไม่ทราบที่อยู่</b><br>
    <label> จำนวน </label>
        <select id="hv_cl_no_adr" name="hv_cl_no_adr" class="form-select form-select-sm" placeholder="จำนวนที่ไม่ทราบที่อยู่" aria-label=".form-select-sm example" >
			<option value="" selected></option>
			<option value="1">1</option>
			<option value="2">2</option>
            </select>
	<label> แจ้งเรือนจำ และ ทว. </label>
        <select id="hv_cl_torvor" name="hv_cl_torvor" class="form-select form-select-sm" placeholder="ระบุจำนวนแจ้ง เรือนจำและ ทว." aria-label=".form-select-sm example" >
			<option value="" selected></option>
			<option value="1">1</option>
			<option value="2">2</option>
            </select>
    <label> ยังไม่ได้แจ้ง </label>
        <select id="hv_cl_no_torvor" name="hv_cl_no_torvor" class="form-select form-select-sm" placeholder="ระบุจำนวนที่ยังไม่ได้แจ้งข้อมูล ทว." aria-label=".form-select-sm example" >
			<option value="" selected></option>
			<option value="1">1</option>
			<option value="2">2</option>
            </select><br>
<b style="color: black">กระทำผิดอีก/อยู่เรือนจำ</b><br>
        <label> จำนวน </label>
        <select id="hv_cl_prison" name="hv_cl_prison" class="form-select form-select-sm" placeholder="จำนวนกระทำผิดอีก/อยู่เรือนจำ" aria-label=".form-select-sm example" >
			<option value="" selected></option>
			<option value="1">1</option>
			<option value="2">2</option>
            </select>
	<label> แจ้ง ทว. </label>
        <select id="hv_cl_torvor2" name="hv_cl_torvor2" class="form-select form-select-sm" placeholder="ระบุจำนวนแจ้ง ทว." aria-label=".form-select-sm example" >
			<option value="" selected></option>
			<option value="1">1</option>
			<option value="2">2</option>
            </select>
    <label> ยังไม่ได้แจ้ง </label>
        <select id="hv_cl_no_torvor2" name="hv_cl_no_torvor2" class="form-select form-select-sm" placeholder="ระบุจำนวนที่ยังไม่ได้แจ้งข้อมูล ทว." aria-label=".form-select-sm example" >
			<option value="" selected></option>
			<option value="1">1</option>
			<option value="2">2</option>
            </select><br>
        
	<label> หมายเหตุ </label>
	<input type = "text" name = "hv_cl_remark" class="form-control" placeholder="หมายเหตุอื่น ๆ (ถ้ามี)"  >
 <br>
		<p>
    	<input type="submit" value="บันทึกข้อมูล" class="btn btn-success" >
		<td> <a href="home_visit.php?pcid=<?= $pcid ?> " class="btn btn-warning" >ยกเลิก</a> </td>
 	</p>
		<br>
		<br>
	</form>
	</div>
	</div>
	</div>
</body>
</html>