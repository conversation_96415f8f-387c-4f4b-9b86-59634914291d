<?php
// Include the database connection file
include "../Condb_pdo.php";

// Retrieve the form data
$id = $_POST['id'];
$idcard = $_POST['idcard'];
$name = $_POST['name'];
$charge = $_POST['charge'];
$pol_name = $_POST['pol_name'];
$pol_position = $_POST['pol_position'];
$gov_name = $_POST['gov_name'];
$gov_contact = $_POST['gov_contact'];
$gov_date = $_POST['gov_date'];
$attorney_name = $_POST['attorney_name'];
$att_contact = $_POST['att_contact'];
$att_date = $_POST['att_date'];
$remark = $_POST['remark'];

$query = "INSERT INTO DirectoryDetention22 (idcard, name, charge, pol_name, pol_position, gov_name, gov_contact, gov_date, attorney_name, att_contact, att_date, remark) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
$stmt = $pdo->prepare($query);
$stmt->execute([$idcard, $name, $charge, $pol_name, $pol_position, $gov_name, $gov_contact, $gov_date, $attorney_name, $att_contact, $att_date, $remark]);

if ($stmt->rowCount() > 0) {
    echo "Data saved successfully!";
} else {
    $errorInfo = $stmt->errorInfo();
    echo "Error saving data: " . $errorInfo[2];
}

// Redirect back to the data table display page after saving the data
header("Location: DirectoryDetention22.php");
exit();
?>