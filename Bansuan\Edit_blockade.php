<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';


// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

$id = $_GET['id'];

$sql = "SELECT * FROM wm_tb_blockade WHERE bl_cl_aid = :id ";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':id', $id);
    $stmt->execute();
    $row = $stmt->fetch(PDO::FETCH_ASSOC);

// สำหรับเลือก จังหวัด อำเภอ ตำบล /และ สถานีตำรวจ
include("../config.inc.php");
include("../classes/class.database.inc.php");
$conn2 = get_connection(); // connect to mySQLDB

$province = isset($_GET['province']) ? $_GET['province'] : 64; // sukhothai
$amphure = isset($_GET['amphure']) ? $_GET['amphure'] : 0;
$tambon = isset($_GET['tambon']) ? $_GET['tambon'] : 0;

// สำหรับเลือกสถานีตำรวจทั่วประเทศ
$region = isset($_GET['region']) ? $_GET['region'] : 0;
$provincial = isset($_GET['provincial']) ? $_GET['provincial'] : 0;
//$station = isset($_GET['station']) ? $_GET['station'] : 0;

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>แก้ไขข้อมูลการปิดล้อมตรวจค้น</title>
<script src="/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 18px;
    }
input::placeholder {
  color: red;
}
.form-control::placeholder {
  color: sandybrown;
}
</style>
<!-- สำหรับวันเกิด แบบใหม่  -->
<link rel="stylesheet" href="../datepicker_th/jquery.datetimepicker.css" type="text/css">
<script src="../datepicker_th/jquery-1.8.3.min.js"></script>  
<script src="../datepicker_th/jquery.datetimepicker.full.js"></script>
<script type="text/javascript"> 
$(function(){
	
	$.datetimepicker.setLocale('th'); // ต้องกำหนดเสมอถ้าใช้ภาษาไทย และ เป็นปี พ.ศ.
	// กรณีใช้แบบ input
    $("#testdate5").datetimepicker({
        timepicker:false,
        format:'Y-m-d',  // กำหนดรูปแบบวันที่ ที่ใช้ เป็น 00-00-0000			
        lang:'th',  // ต้องกำหนดเสมอถ้าใช้ภาษาไทย และ เป็นปี พ.ศ.
		onSelectDate:function(dp,$input){
			var yearT=new Date(dp).getFullYear()-0;  
			var yearTH=yearT;  // ถ้าจะให้แสดงเป็น พ.ศ. ให้เป็น yearT+543
			var fulldate=$input.val();
			var fulldateTH=fulldate.replace(yearT,yearTH);
			$input.val(fulldateTH);
		},
    });       
	// กรณีใช้กับ input ต้องกำหนดส่วนนี้ด้วยเสมอ เพื่อปรับปีให้เป็น ค.ศ. ก่อนแสดงปฏิทิน
	$("#testdate5").on(function(e){
		var dateValue=$(this).val();
		if(dateValue!=""){
				var arr_date=dateValue.split("-"); // ถ้าใช้ตัวแบ่งรูปแบบอื่น ให้เปลี่ยนเป็นตามรูปแบบนั้น
				// ในที่นี้อยู่ในรูปแบบ 00-00-0000 เป็น d-m-Y  แบ่งด่วย - ดังนั้น ตัวแปรที่เป็นปี จะอยู่ใน array
				//  ตัวที่สอง arr_date[2] โดยเริ่มนับจาก 0 
				dateValue=dateValue.replace(arr_date[2],yearT);
				$(this).val(dateValue);													
		}		
	});
});
</script>
</head>

<body>
	
	<div class="container">
	<div class="row">
	<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> แก้ไขข้อมูลการปิดล้อมตรวจค้น </div>
	<form action="Save_blockade.php" method="POST" enctype="multipart/form-data" class="body">
	<span style="color: #1203F8">
        
        <label hidden ="hidden">ลำดับ</label>
		<input type = "text" name = "bl_cl_aid" class="form-control" value= "<?= $row['bl_cl_aid']?>" hidden ="hidden" >
        
        <label>ครั้งที่ตรวจค้นในรอบเดือน</label>
		<select id="bl_cl_num" name="bl_cl_num" class="form-select form-select-sm"  placeholder="ระบุครั้งที่ตรวจค้นในรอบเดือน">
			<option value="" selected> </option>
			<option value="1">1</option>
			<option value="2">2</option>
			<option value="3">3</option>
			<option value="4">4</option>
            <option value="5">5</option>
            <option value="6">6</option>
            <option value="7">7</option>
            <option value="8">8</option>
            <option value="9">9</option>
            <option value="10">10</option>
		</select>

        <label>วันที่ปิดล้อมตรวจค้น <span style="color: #F90004">* จำเป็น</span> </label>
        <p><input type="text" name="bl_cl_date" id="testdate5" value= "<?= $row['bl_cl_date']?>" class="form-control" style="width:250px;" autocomplete="off" required></p>
		
        <label>วิธีการเข้าค้น</label>
		<select id="bl_cl_method" name="bl_cl_method" class="form-select form-select-sm" placeholder="ระบุวิธีการจะเข้าค้น">
			<option value="" selected> </option>
			<option value="ขอหมายค้น">ขอหมายค้น</option>
			<option value="ใช้บัตร ป.ป.ส.">ใช้บัตร ป.ป.ส.</option>
			<option value="เจ้าบ้านยินยอม">เจ้าบ้านยินยอม</option>
			<option value="วิธีการอื่น ๆ">วิธีการอื่น ๆ</option>
            <option value="ค้นในที่สาธารณะ">ค้นในที่สาธารณะ</option>
		</select>
        
		<label>เลขที่หมายค้น/ลงวันที่</label>
		<input type = "text" name = "bl_cl_search_warrant" class="form-control" value= "<?= $row['bl_cl_search_warrant']?>" placeholder="เลขที่หมายค้น" >
        
        <b>สถานที่ปิดล้อมตรวจค้น</b><br>
        <!-- เลือกจังวัด อำเภอ ตำบล อัตโนมัติ -->
        <label> จังหวัด </label><br>
        <select name="bl_cl_province" id="bl_cl_province" onChange="do_province_change()" class="form-control" >
                <option value="0">&lt;&lt; เลือกจังหวัด &gt;&gt;</option>
                <?php
                $province = $row['bl_cl_province']; // <<ดึงข้อมูลจาก ตารางมา row ซึ่งเป็นคนละตัวกับ row_p
                //------------------------------------
			  	$res_p = $conn2->query("SELECT * FROM `provinces` ORDER BY `provinces`.`pv_name_th` ASC");
			     $selected = '';
			     while($row_p = $conn2->fetch_row($res_p)) 
			     {
				  $pv_code = $row_p['pv_code'];
				  $pv_name = $row_p['pv_name_th'];
				  // set default provionce to 64 >> sukhothai
				  if($pv_code == $province) {
					  $selected = 'selected';
				  }
				  else {
					  $selected = '';
				  }
				  //
				  echo "<option value='$pv_code' $selected> $pv_name </option>\n";
			     }
			  
			  ?>
              </select>
        
        <label> อำเภอ </label><br>
        <select name="bl_cl_amphur" id="bl_cl_amphur" onChange="do_amphure_change()" class="form-control" >
        <option value="0">&lt;&lt; เลือกอำเภอ &gt;&gt;</option>
                <?php
                if($province > 0)
                {
                    $amphure = $row['bl_cl_amphur']; // <<
                    //-------------------------------------
                    $res_a = $conn2->query("SELECT * FROM `amphures` WHERE province_code='$province' ORDER BY `amphures`.`ap_name_th` ASC");
                    $selected = '';
                  while($row_a = $conn2->fetch_row($res_a)) 
                  {
                      $ap_code = $row_a['ap_code']; // 
                      $ap_name = $row_a['ap_name_th'];
                      // set default provionce to 64 >> sukhothai
                      if($ap_code == $amphure) {
                          $selected = 'selected';
                      }
                      else {
                          $selected = '';
                      }
                      //
                      echo "<option value='$ap_code' $selected> $ap_name </option>\n";
                  }
                }	
                ?>
              </select>
        
         <label> ตำบล </label>
        <br>
        <select name="bl_cl_tumbon" id="bl_cl_tumbon" class="form-control" >
                <option value="0">&lt;&lt; เลือกตำบล &gt;&gt;</option>
                <?php
	           if($amphure > 0)
                {
                   $tambon = $row['bl_cl_tumbon'];
                   //------------------------------------------
                    $res_t = $conn2->query("SELECT * FROM `districts` WHERE amphure_code='$amphure' ORDER BY `districts`.`tb_name_th` ASC");
                    $selected = '';
                  while($row_t = $conn2->fetch_row($res_t)) 
                  {
                      $tb_code = $row_t['tb_id']; // 
                      $tb_name = $row_t['tb_name_th'];
                      // set default provionce to 64 >> sukhothai
                      if($tb_code == $tambon) {
                          $selected = 'selected';
                      }
                      else {
                          $selected = '';
                      }
                      //
                      echo "<option value='$tb_code' $selected> $tb_name </option>\n";
                  }
                }	
                ?>                                                                     
              </select>

        <label> หมู่ที่ </label><br>
        <input type = "text" name = "bl_cl_moo" class="form-control" value= "<?= $row['bl_cl_moo']?>"  placeholder="ระบุหมู่ที่" >
        
        <label>บ้านเลขที่</label>
        <input type = "text" name = "bl_cl_adr" class="form-control" value= "<?= $row['bl_cl_adr']?>"  placeholder="บ้านเลขที่" >
        
        <label>เจ้าของบ้าน ผู้นำตรวจค้น</label>
        <input type = "text" name = "bl_cl_conduct" class="form-control" value= "<?= $row['bl_cl_conduct']?>"  placeholder="เจ้าของบ้าน ผู้นำตรวจค้น" >
        
        <label>พบ/ไม่พบสิ่งผิดกฎหมาย</label>
		<select id="bl_cl_find_illegal" name="bl_cl_find_illegal" class="form-select form-select-sm"  placeholder="พบ/ไม่พบสิ่งผิดกฎหมาย">
			<option value="" selected> </option>
			<option value="พบ">พบ</option>
			<option value="ไม่พบ">ไม่พบ</option>
		</select>
        
        <label>บุคคลที่ตรวจค้นพบ</label>
		<select id="bl_cl_find_person" name="bl_cl_find_person" class="form-select form-select-sm"  placeholder="บุคคลที่ตรวจค้นพบ">
			<option value="" selected> </option>
			<?php
               $res_fp = $pdo->prepare("SELECT * FROM wm_tb_blockade_find_person order by fp_aid ASC");
               $res_fp->execute();
               
                while($row_fp = $res_fp->fetch(PDO::FETCH_ASSOC))
                {
                    $fp_aid = htmlspecialchars($row_fp['fp_aid'], ENT_QUOTES, 'UTF-8');
                    $fp_detail = htmlspecialchars($row_fp['fp_detail'], ENT_QUOTES, 'UTF-8');
                    
                    echo "<option value='$fp_aid'>$fp_detail</option>";
                }
            ?>
		</select>
        
        <label>รายการที่พบ</label>
		<select id="bl_cl_illegal_things" name="bl_cl_illegal_things" class="form-select form-select-sm" placeholder="รายการที่พบ">
              <option value="" selected> </option>  <!-- ดึงข้อมูลมาจากตารางใหม่ -->
            <?php
                $res_thing = $pdo->prepare("SELECT * FROM wm_tb_blockade_illegal_things order by aid_things ASC");
                $res_thing->execute();
            
                while($row_thing = $res_thing->fetch(PDO::FETCH_ASSOC))
                {
                    $aid_things = htmlspecialchars($row_thing['aid_things'], ENT_QUOTES, 'UTF-8');
                    $illegal_things = htmlspecialchars($row_thing['illegal_things'], ENT_QUOTES, 'UTF-8');
                    
                    echo "<option value='$aid_things'>$illegal_things</option>";
                }
            ?>
        </select>
        
		<label>บันทึกรายการที่พบจากการตรวจค้น</label>
        <textarea class="form-control" id="bl_cl_remark" name="bl_cl_remark"  rows="5" placeholder="บันทึกรายการที่พบจากการตรวจค้น" ><?php echo $row['bl_cl_remark'] ?></textarea>
        
        <label>กลุ่มคดีอาญาที่พบจากการตรวจค้น</label>
		<select id="bl_cl_group" name="bl_cl_group" class="form-select form-select-sm"  placeholder="กลุ่มคดีอาญาที่พบจากการตรวจค้น">
			<option value="" selected> </option>
			<option value="1">กลุ่มที่ 1 คดีประเภทอุกฉกรรจ์และสะเทือนขวัญ</option>
			<option value="2">กลุ่มที่ 2 คดีประเภทที่เกี่ยวกับชีวิต ร่างกาย และเพศ</option>
            <option value="3">กลุ่มที่ 3 คดีประเภทที่เกี่ยวกับทรัพย์</option>
            <option value="4">กลุ่มที่ 4 คดีประเภทคดีที่น่าสนใจ</option>
            <option value="51">กลุ่ม5 เสพ</option>
            <option value="52">กลุ่ม5 ครอบครอง</option>
            <option value="53">กลุ่ม5 จำหน่าย</option>
            <option value="54">กลุ่ม5 ผลิต</option>
            <option value="55">กลุ่ม5 อื่นๆ</option>
		</select>
        
        <br>
        
        <label>สถานีตำรวจ <span style="color: #F90004">* จำเป็น</span></label><br>
                <!-- เลือกสถานีตำรวจ -->
        <label> บช. </label><br>
        <select name="region" id="region" onChange="do_region_change()" class="form-control" Required >
                <option value="">&lt;&lt; เลือก บช. &gt;&gt;</option>
                <?php
                $region = $row['region']; // <<ดึงข้อมูลจาก ตารางมา row ซึ่งเป็นคนละตัวกับ row_p
                //------------------------------------
			  	$res_re = $conn2->query("SELECT * FROM `wm_tb_police_region` 
                                            ORDER BY `name_region` ASC");
			     $selected = '';
			     while($row_re = $conn2->fetch_row($res_re)) 
                     {
                      $code_region = $row_re['code_region'];
                      $name_region = $row_re['name_region'];
                      // set default provionce to 64 >> sukhothai
                          if($code_region == $region) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$code_region' $selected> $name_region </option>\n";
                     }
			  
			  ?>
              </select>
        
        
        <label> บก. </label><br>        
        <select name="provincial" id="provincial" onChange="do_provincial_change()" value="<?php echo $provincial; ?>" class="form-control" >
        <option value="0">&lt;&lt; เลือก บก. &gt;&gt;</option>
                <?php
                if($region > 0)
                {
                    $provincial = $row['provincial']; // <<
                    //-------------------------------------
                    $res_prov = $conn2->query("SELECT * FROM `wm_tb_police_provincial` 
                                                WHERE `region_code`='$region' 
                                                ORDER BY `provincial_code` ASC");
                    $selected = '';
                  while($res_prov = $conn2->fetch_row($res_prov)) 
                  {
                      $provincial_code = $res_prov['provincial_code']; // 
                      $provincial = $res_prov['provincial'];
                      // set default provionce to 64 >> sukhothai
                          if($provincial_code == $provincial) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$provincial_code' $selected> $provincial </option>\n";
                  }
                }	
                ?>
              </select>
        
        
         <label> สน./สภ. </label>
        <br>
                
        <select name="station" id="station" class="form-control" >
                <option value="0">&lt;&lt; เลือก สน./สภ. &gt;&gt;</option>
                <?php
	           if($provincial > 0)
                {
                   $station = $row['station'];
                   //------------------------------------------
                    $res_st = $conn2->query("SELECT * FROM `wm_tb_police_station2` 
                                                WHERE provincial_code='$provincial_code'
                                                ORDER BY `station_code` ASC");
                    $selected = '';
                  while($row_st = $conn2->fetch_row($res_st)) 
                  {
                      $station_code = $row_st['station_code']; // 
                      $station_name = $row_st['station_name'];
                      // set default provionce to 64 >> sukhothai
                          if($station_code == $station) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$station_code' $selected> $station_name </option>\n";
                  }

                }	
                ?>                                                                     
              </select>
		<br>
		
        <label>หน.ชุดตรวจค้น (ระดับ สว.ขึ้นไป)  <span style="color: #F90004">* จำเป็น</span> </label>
		<input type = "text" name = "bl_cl_leader" value= "<?= $row['bl_cl_leader']?>" class="form-control"  placeholder="หน.ชุดตรวจค้น (ระดับ สว.ขึ้นไป)" required >
		        
		<label>เจ้าหน้าที่สืบสวน/บันทึก</label>
		<?php
			if ($station == 6707) {
				// User's station is 6707, display the dropdown select
				echo '<select id="bl_cl_record" name="bl_cl_record" class="form-select form-select-sm" placeholder="ระบุชื่อผู้บันทึก" required>';
				echo '<option value="" selected> </option>';
				$bsdp = $pdo->prepare("SELECT * FROM `police_name_bsdetective` ORDER BY `police_name_bsdetective`.`aid_bsdp` DESC");
				$bsdp->execute();
				while ($row_bsdp = $bsdp->fetch(PDO::FETCH_ASSOC)) {
					$aid_bsdp = htmlspecialchars($row_bsdp['aid_bsdp'], ENT_QUOTES, 'UTF-8');
					$bsdp_name = htmlspecialchars($row_bsdp['bsdp_name'], ENT_QUOTES, 'UTF-8');

					echo "<option value='$bsdp_name'>$bsdp_name</option>";
				}
				echo '</select>';
			} else {
				// User's station is not 6707, display a text input
				$originalValue = htmlspecialchars($row['bl_cl_record'], ENT_QUOTES, 'UTF-8');
				echo '<input type="text" id="bl_cl_record" name="bl_cl_record" value="' . $originalValue . '" class="form-control" placeholder="ระบุ ยศชื่อ-สกุล ผู้บันทึก" required>';
			}
		?>
    
        <div class="mb-3">
			<label for="formFileMultiple" class="form-label">ไฟล์</label>
			<input class="form-control" type="file" id="bl_cl_file" name="bl_cl_file" multiple>
		</div>
        
		<p>
		<br>
			<input type="submit" value="บันทึกข้อมูล" class="btn btn-success" >
			<td> <a href="index.php?rnd=<?= rand(); ?>&page=blockade" class="btn btn-warning" >ยกเลิก</a> </td>
		</p>
		<br>
		<br>
	</form>
	</div>
	</div>
	</div>
<!-- ฟังก์ชั่น เลือก จังหวัด อำเภอ ตำบล -->
<script>

function do_province_change()
{
	var sel = document.getElementById("bl_cl_province");		
	var code = sel.options[sel.selectedIndex].value;

	$.get("../ajax/get_amphure.php?province=" + code, 
		  function(data, status){
				//alert("Data: " + data + "\nStatus: " + status);
				// jQuery style						
				$('#bl_cl_amphur').find('option').remove().end();
				var data = $.trim(data);
				if(data == '') {
					alert("ไม่พบข้อมูลอำเภอ !");
					return;
				}

					//.append('<option value="whatever">text</option>').val('whatever');
				var datas = data.split(';');
				for(var i=0; i<datas.length; i++) {
					if(datas[i] == '') continue;
					datas[i] = datas[i].split(':');
					var code = datas[i][0];
					var name = datas[i][1];
					$('#bl_cl_amphur').append('<option value="'+ code+'">' + name + '</option>');
				}
                // Trigger the change event on the amphur dropdown so the tambon dropdown will be populated     
                $('#bl_cl_amphur').trigger('change');
		});
}

function do_amphure_change()
{
	var sel = document.getElementById("bl_cl_province");		
	var code = sel.options[sel.selectedIndex].value;

	var sel2 = document.getElementById("bl_cl_amphur");		
	var code2 = sel2.options[sel2.selectedIndex].value;

	$.get("../ajax/get_district.php?province=" + code + "&amphure=" + code2, 
			  function(data, status){
					//alert("Data: " + data + "\nStatus: " + status);
					// jQuery style						
					$('#bl_cl_tumbon').find('option').remove().end();
					var data = $.trim(data);
					if(data == '') {
						alert("ไม่พบข้อมูลตำบล !");
						return;
					}

						//.append('<option value="whatever">text</option>').val('whatever');
					var datas = data.split(';');
					for(var i=0; i<datas.length; i++) {
						if(datas[i] == '') continue;
						datas[i] = datas[i].split(':');
						var code = datas[i][0];
						var name = datas[i][1];
						$('#bl_cl_tumbon').append('<option value="'+ code+'">' + name + '</option>');
					}
			});
}

// ฟังก์ชั่น เปลี่ยนภาค เป็น จังหวัด
function do_region_change(current_value)
{
    current_value = (typeof current_value == 'undefined') ? 0 : current_value;
	var sel_region = document.getElementById("region");		
	var code_region = sel_region.options[sel_region.selectedIndex].value;

	$.get("../ajax/get_provincial.php?region=" + code_region, 
		  function(data, status){
				//alert("Data: " + data + "\nStatus: " + status);
				// jQuery style						
				$('#provincial').find('option').remove().end();
				var data = $.trim(data);
				if(data == '') {
					alert("ไม่พบข้อมูล บก. !");
					return;
				}

					//.append('<option value="whatever">text</option>').val('whatever');
				var datas = data.split(';');
				for(var i=0; i<datas.length; i++) {
					if(datas[i] == '') continue;
					datas[i] = datas[i].split(':');
					var code_region = datas[i][0];
					var name_region = datas[i][1];
					//$('#provincial').append('<option value="'+ code_region+'">' + name_region + '</option>');
                    if(current_value == code_region) {
                        $('#provincial').append('<option value="'+ code_region+'" selected>' + name_region + '</option>'); 
                    }
                    else {
                        $('#provincial').append('<option value="'+ code_region+'">' + name_region + '</option>');
                    }
				}
                // Trigger the change event on the amphur dropdown so the tambon dropdown will be populated     
                $('#provincial').trigger('change');
		});
}
    //เลือก สถานี จากจังหวัด
function do_provincial_change(current_value)
{
    current_value = (typeof current_value == 'undefined') ? 0 : current_value;
    
	var sel_region = document.getElementById("region");		
	var provincial_code = sel_region.options[sel_region.selectedIndex].value;
    
    var sel_provincial = document.getElementById("provincial");		
	var station_code = sel_provincial.options[sel_provincial.selectedIndex].value;

	$.get("../ajax/get_station.php?region=" + provincial_code + "&provincial=" + station_code, 
			  function(data, status){
					//alert("Data: " + data + "\nStatus: " + status);
					// jQuery style						
					$('#station').find('option').remove().end();
					var data = $.trim(data);
					if(data == '') {
						alert("ไม่พบข้อมูลสถานี !");
						return;
					}

						//.append('<option value="whatever">text</option>').val('whatever');
					var datas = data.split(';');
					for(var i=0; i<datas.length; i++) {
						if(datas[i] == '') continue;
						datas[i] = datas[i].split(':');
						var station_code = datas[i][0];
						var station_name = datas[i][1];
                        if(current_value == station_code) {
                            $('#station').append('<option value="'+ station_code+'" selected>' + station_name + '</option>'); 
                        }
                        else {
                            $('#station').append('<option value="'+ station_code+'">' + station_name + '</option>');   
                        }						
					}
			});
}
</script> 
<script>
// ชื่อ >> value >> set selct auto (สคลิปตรวจสอบ Selected) จำเป็นต้องก็อปปี้ <script src="/jQuery/jquery-3.5.1.min.js" type="text/javascript">
function auto_select(sid, value)
{
    $('#' + sid).val(value);
}

function auto_thaidate(sid, value)
{
	if(value != '') {
		var dates = value.split('-');
		dates = value.split('-');
		if(dates.length > 2) {
			var ys = dates[0];
			var ms = dates[1];
			var ds = dates[2];
			if(ys.substr(0,2) == '20') {
				ys = parseInt(ys) + 543;
			}
			value = $.digit(ds,2) + '/' + $.digit(ms,2) + '/' +ys;
			$('#' + sid).val(value);
		}
	}
}

$(document).ready(function() {
<?php
    echo "auto_select('bl_cl_num', '{$row['bl_cl_num']}');\n";
    echo "auto_select('bl_cl_method', '{$row['bl_cl_method']}');\n";
    echo "auto_select('bl_cl_find_illegal', '{$row['bl_cl_find_illegal']}');\n"; 
    echo "auto_select('bl_cl_find_person', '{$row['bl_cl_find_person']}');\n"; 
    echo "auto_select('bl_cl_illegal_things', '{$row['bl_cl_illegal_things']}');\n"; 
    echo "auto_select('bl_cl_group', '{$row['bl_cl_group']}');\n";
    //echo "auto_select('bl_cl_leader', '{$row['bl_cl_leader']}');\n";
    echo "auto_select('bl_cl_record', '{$row['bl_cl_record']}');\n";
    // convert db date to thai dates
	//echo "auto_thaidate('bl_cl_date', '{$row['bl_cl_date']}');\n";
?>
    setTimeout("do_region_change('<?= $row['provincial'] ?>')", 1000);
    setTimeout("do_provincial_change('<?= $row['station'] ?>')", 2000);
    });
</script>
</body>
</html>
 