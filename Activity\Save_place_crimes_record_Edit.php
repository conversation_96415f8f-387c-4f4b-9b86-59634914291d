<?php
include '../Condb.php';
include '../users.inc.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// เมื่อมีการใช้ ตัวแปร Session จะต้องเปิดก่อน
//session_start();  // เปิดตัวแปร session
/*if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
*/

//echo '<pre>';
//print_r($_POST);
//echo '</pre>';
//
//echo '<hr>';
//
//exit();

$aid_crimes_record = $_POST[ 'aid_crimes_record' ];
$pol_st_crimes_region = $_POST[ 'pol_st_crimes_region' ];
$pol_st_crimes_provincial = $_POST[ 'pol_st_crimes_provincial' ];
$pol_st_crimes_record = $_POST[ 'pol_st_crimes_record' ];
$place_gen_report = $_POST[ 'place_gen_report' ];
$place_crimes_report = $_POST[ 'place_crimes_report' ];
$place_gen_record = $_POST[ 'place_gen_record' ];
$place_crimes_record = $_POST[ 'place_crimes_record' ];
$record_complete_p = $_POST[ 'record_complete_p' ];
$date_record = $_POST[ 'date_record' ];		

// convert Thai dates to eng
if($date_record != '') {
	if(strpos($date_record, '/') > 0) {
    	$dates = explode('/', $date_record);	// d/m/y
	}
	elseif(strpos($date_record, '-') > 0) {
		$date = explode('-', $date_record);	// y-m-d
		$dates = array($date[2], $date[1], $date[0]);	// d/m/Y
	}
	// thai dates
	if(substr(''.$dates[2],0,2) ==='25') {
		$date_record = ($dates[2]-543) . '-' . $dates[1] . '-' . $dates[0];
	}
	// eng dates
	else {
    	$date_record = $dates[2] . '-' . $dates[1] . '-' . $dates[0];
	}
}

    // save Image ใหม่แทน
$file1 = $_FILES[ 'image2' ][ 'tmp_name' ];
if ( is_uploaded_file( $file1 ) ) {
    //ลบรูปเก่าในเซิร์ฟเวอร์ก่อน
    $sql_img = "SELECT * FROM wm_tb_crimes_record WHERE aid_crimes_record='aid_crimes_record' ";
    $res_img = mysqli_query($conn,$sql_img);
    if($row_img = mysqli_fetch_array($res_img))
    {
        if(($row_img['image2'] != '') && file_exists($row_img['image2'])) {
            unlink( $row_img['image2'] );
        }    
    }
    //เปลี่ยนชื่อไฟล์ใหม่
    $image2 = "/policeinnopolis/uploaded/Image1/" . basename( $_FILES[ 'image2' ][ 'name' ] );	    
    // file + ext    
    $ext = strrchr($image2, ".");    
    $image2 = "../policeinnopolis/uploaded/image1/File_" . time() . $ext; // file+time+ext        
    move_uploaded_file( $file1, $image2 );  
} 
else {
  $image2 = '';
}

$sql ="SELECT * FROM wm_tb_crimes_record WHERE aid_crimes_record='$aid_crimes_record' ";
$res = mysqli_query($conn,$sql);
$row1 = mysqli_fetch_array($res);
if($row1) {
    // ถ้ามีแล้ว ให้ update
    $aid_crimes_record = $row1['aid_crimes_record'];
    $sql = "UPDATE wm_tb_crimes_record SET ".
            "pol_st_crimes_region = '$pol_st_crimes_region', " .
            "pol_st_crimes_provincial = '$pol_st_crimes_provincial', " .
            "pol_st_crimes_record = '$pol_st_crimes_record', " .
            "place_gen_report = '$place_gen_report', " .
            "place_crimes_report = '$place_crimes_report', " .
            "place_gen_record = '$place_gen_record', " .
            "place_crimes_record = '$place_crimes_record', " .
            "record_complete_p = '$record_complete_p', " .
            "date_record = '$date_record', " .
            "image2 = '$image2' " .
            "WHERE aid_crimes_record = '$aid_crimes_record' ";
}
else {
    // ถ้ายังไม่มี ให้ insert
    $sql = "INSERT INTO wm_tb_crimes_record (pol_st_crimes_region, pol_st_crimes_provincial, pol_st_crimes_record, place_gen_report, place_crimes_report, place_gen_record, place_crimes_record, record_complete_p, date_record, image2) VALUES('$pol_st_crimes_region', '$pol_st_crimes_provincial', '$pol_st_crimes_record', '$place_gen_report', '$place_crimes_report', '$place_gen_record', '$place_crimes_record', '$record_complete_p', '$date_record', '$image2') ";
}

$result=mysqli_query($conn,$sql);
if($result){
	echo "<script>alert('บันทึกข้อมูลเรียบร้อย');</script>";	
}else{
	echo "<script>alert(\"บันทึกข้อมูลไม่สำเร็จ\\n" . print_r(mysqli_error($conn), true) . "\");</script>";
}
mysqli_close($conn);

echo "<script>window.location='/Activity/Show_Activity1.php?pcid={$aid_crimes_record}';</script>";

?>