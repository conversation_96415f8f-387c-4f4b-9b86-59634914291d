 <?php
include "../Condb.php"; //PDO
include '../users.inc.php';
include '../Alert.php';
include '../right_user.php';

// เก็บข้อมูล Action Save
//$user_id = getCurrentUserId(); // You should implement a function to get the current user ID
$acc = $user['account'];
$member_name = $user['member_name'];
$name_position = $user['name_position'];

/*echo '<pre>';
print_r($_POST);
echo '</pre>';

echo '<hr';
print_r($_POST);
print_r($_FILES);
exit();*/

// Rest of your code for assigning POST values...
$ps_cl_type = $_POST['ps_cl_type'] ?? '';
$ps_cl_aid = $_POST['ps_cl_aid'] ?? '';
$ps_cl_gen_detail = $_POST['ps_cl_gen_detail'] ?? '';
$ps_cl_CrimeType2 = $_POST['ps_cl_CrimeType2'] ?? '';
$ps_cl_idcard = $_POST['ps_cl_idcard'] ?? '';
$ps_cl_prefix = $_POST['ps_cl_prefix'] ?? '';
$ps_cl_sex = $_POST['ps_cl_sex'] ?? '';
$ps_cl_name = $_POST['ps_cl_name'] ?? '';
$ps_cl_surname = $_POST['ps_cl_surname'] ?? '';
$ps_cl_nickname = $_POST['ps_cl_nickname'] ?? '';
$ps_cl_birthday2 = $_POST['ps_cl_birthday2'] ?? '';
$ps_cl_father = $_POST['ps_cl_father'] ?? '';
$ps_cl_father_pid = $_POST['ps_cl_father_pid'] ?? '';
$ps_cl_mother = $_POST['ps_cl_mother'] ?? '';
$ps_cl_mother_pid = $_POST['ps_cl_mother_pid'] ?? '';
$ps_cl_marital_status = $_POST['ps_cl_marital_status'] ?? '';
$ps_cl_death = $_POST['ps_cl_death'] ?? '';
$date_of_death = $_POST['date_of_death'] ?? '';
$region = $_POST['region'] ?? '';
$provincial = $_POST['provincial'] ?? '';
$station = $_POST['station'] ?? '';
$ps_cl_recorder = $member_name;
$ps_cl_position = $name_position;
$ps_cl_date_rec = $_POST['ps_cl_date_rec'] ?? '';

$date_of_death = '';
$ps_cl_idcard = $_POST['ps_cl_idcard'];


// Remove non-numeric characters
    $ps_cl_idcard = preg_replace('/[^0-9]/', '', $ps_cl_idcard);
	$ps_cl_father_pid = preg_replace('/[^0-9]/', '', $ps_cl_father_pid);
	$ps_cl_mother_pid = preg_replace('/[^0-9]/', '', $ps_cl_mother_pid);

// List of invalid ID numbers
$invalid_ids = [
    '0000000000000', '0000000000001', '0000000000002',
    '0000000000003', '0000000000004', '0000000000005',
    '0000000000006', '0000000000007', '0000000000008',
    '0000000000009', '0000000000010', '0000000000011',
    '0000000000012', '0000000000013', '0000000000014',
	'0000000000015', '0000000000016', '0000000000017',
	'0000000000018', '0000000000019', '0000000000020'
];

// Check if the ID is in the list of invalid IDs
if (in_array($ps_cl_idcard, $invalid_ids)) {
   //echo "ไม่อนุญาตให้ใช้เลขบัตรประชาชนแบบนี้ (ควรใช้เลขบัตรที่ถูกต้องเท่านั้น)";
	$_SESSION['WrongData'] = "WrongData";
				WrongData(
					"ห้ามใช้เลขบัตรแบบนี้",
					"ไม่อนุญาตให้ใช้เลขบัตรประชาชนผิดกรอกเข้ามา <br><br>(ควรใช้เลขบัตรที่ถูกต้องเท่านั้น)",
					"error",
					"/policeinnopolis/WatchmanData/Add_Personal.php?" // Corrected URL concatenation
				);
				unset($_SESSION['WrongData']); // Clear the session variable
				// No header redirect here, it will be handled by the SweetAlert2 callback
				exit;
	
	} else {

		// Check if ps_cl_idcard contains only numbers and has 13 digits
		if (!preg_match('/^\d{13}$/', $ps_cl_idcard)) {
				// If it contains non-numeric characters or does not have exactly 13 digits, return an error
				//echo "หมายเลขบัตรประชาชน เฉพาะตัวเลข 13 หลักเท่านั้น ไม่ต้องใส่เครื่องหมายใดๆ ";
				// Notify user about duplicate information and provide option to update
				$_SESSION['WrongData'] = "WrongData";
				WrongData(
					"เลขบัตรประชาชนผิด",
					"ไม่ครบ หรือเกิน 13 หลัก หรือไม่ ? <br><br>กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง",
					"error",
					"/policeinnopolis/WatchmanData/Add_Personal.php?" // Corrected URL concatenation
				);
				unset($_SESSION['WrongData']); // Clear the session variable
				// No header redirect here, it will be handled by the SweetAlert2 callback
				exit;

				} else {

				// ตรวจสอบว่า มีข้อมูล เลขบัตรประชาชน อยู่ในฐานข้อมูลแล้วหรือยัง
				// Check if the record already exists in the database
					$stmt_check = $pdo->prepare("SELECT * FROM wm_tb_personal WHERE ps_cl_idcard = :ps_cl_idcard");
					$stmt_check->bindParam(':ps_cl_idcard', $ps_cl_idcard);
					$stmt_check->execute();
					$row = $stmt_check->fetch(PDO::FETCH_ASSOC);

					if ($row > 0) {
						 $id = $row['ps_cl_aid'];
						// Notify user about duplicate information and provide option to update
						   $_SESSION['Duplicate'] = "Duplicate Data";
						DuplicateData(
							"ข้อมูลซ้ำ",
							"มีข้อมูลบุคคลนี้แล้ว <br>ต้องการจะแก้ไขปรับปรุงข้อมูลหรือไม่?",
							"success",
							"/policeinnopolis/WatchmanData/Edit_all_Personal.php?id=" . $id // Corrected URL concatenation
						);
						unset($_SESSION['Duplicate']); // Clear the session variable
						// No header redirect here, it will be handled by the SweetAlert2 callback
						exit;

						} else {
							// Save Image
							$file1 = $_FILES['ps_cl_image']['tmp_name'];  // Corrected variable name
							// Retrieve the existing image path
							$stmt1 = $pdo->prepare("SELECT ps_cl_image FROM wm_tb_personal WHERE ps_cl_idcard = :ps_cl_idcard");
							$stmt1->bindParam(':ps_cl_idcard', $ps_cl_idcard);
							$stmt1->execute();
							$old_ps_cl_image = $stmt1->fetchColumn();

							// ตรวจสอบว่า field วันเกิดและวันที่เสียชีวิตว่างหรือไม่ ถ้าว่าง ให้ตั้งค่าเป็น NULL
							$ps_cl_birthday2 = !empty($ps_cl_birthday2) ? $ps_cl_birthday2 : null;
							$date_of_death = !empty($date_of_death) ? $date_of_death : null;

							// Set the timezone to Thailand
							date_default_timezone_set('Asia/Bangkok');

							// Save new image or keep the existing one
							if (is_uploaded_file($file1)) {
								// User has uploaded a new image
								// Delete the old image if it exists
								if (!empty($old_ps_cl_image) && file_exists($old_ps_cl_image)) {
									unlink($old_ps_cl_image);
								}
								$ext = strrchr($_FILES['ps_cl_image']['name'], ".");
								$currentDateTime = date("Y-m-d_H-i-s");
								$ps_cl_image = "../policeinnopolis/uploaded/Image_PS/Idcard_" . $ps_cl_idcard . "_" . $currentDateTime . $ext; 
								// Prepare the statement
								$stmt1 = $pdo->prepare("UPDATE wm_tb_personal SET ps_cl_image = :ps_cl_image WHERE ps_cl_idcard = :ps_cl_idcard");
								$stmt1->bindParam(':ps_cl_image', $ps_cl_image);
								$stmt1->bindParam(':ps_cl_idcard', $ps_cl_idcard);
								// Move the uploaded file
								move_uploaded_file($file1, $ps_cl_image);
							} else {
								// User has not uploaded a new image, keep the existing one
								$ps_cl_image = $old_ps_cl_image;

							}

							/*if(!empty($ps_cl_birthday2)){
								$date = explode('-', $ps_cl_birthday2);
								if(count($date) >= 3){
								$dates2 = array($date[0], $date[1], $date[2]);
								}
							}*/
							if(!empty($date_of_death)){
								$date = explode('-', $date_of_death);
								if(count($date) >= 3){
								$dates2 = array($date[0], $date[1], $date[2]);
								}
							}

							// Insert new record
							$sql = "INSERT INTO wm_tb_personal (ps_cl_type, ps_cl_gen_detail, ps_cl_CrimeType2, ps_cl_idcard, ps_cl_prefix, ps_cl_sex, ps_cl_name, ps_cl_surname, ps_cl_nickname, ps_cl_birthday2, ps_cl_father, ps_cl_father_pid, ps_cl_mother, ps_cl_mother_pid, ps_cl_marital_status, ps_cl_death, date_of_death, region, provincial, station, ps_cl_recorder, ps_cl_position, ps_cl_date_rec, ps_cl_image) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
							$stmt = $pdo->prepare($sql);
							$stmt->execute([$ps_cl_type, $ps_cl_gen_detail, $ps_cl_CrimeType2, $ps_cl_idcard, $ps_cl_prefix, $ps_cl_sex, $ps_cl_name, $ps_cl_surname, $ps_cl_nickname, $ps_cl_birthday2, $ps_cl_father, $ps_cl_father_pid, $ps_cl_mother, $ps_cl_mother_pid, $ps_cl_marital_status, $ps_cl_death, $date_of_death, $region, $provincial, $station, $ps_cl_recorder, $ps_cl_position, $ps_cl_date_rec, $ps_cl_image]);  // Corrected variable name
						}
				}
	}

if ($stmt->rowCount() > 0) {
    $_SESSION['success'] = "Data has been inserted successfully";
    showSweetAlert3("Saved successfully", "บันทึกข้อมูลเรียบร้อย");
	unset($_SESSION['success']); // Clear the session variable
        // No header redirect here, it will be handled by the SweetAlert2 callback
        //exit;
} else {
    $_SESSION['error'] = "Error";
    showSweetAlert('Error', "บันทึกข้อมูล ไม่สำเร็จ", 'error');
}

$ip_address = $_SERVER['REMOTE_ADDR'];
$data = str_replace("'", "", compact_array($_POST));
$action = "Save Personal : $ps_cl_idcard";
log_activity($acc, $action, $data, $ip_address);

// Redirect after a short delay
header("refresh:2; url=/policeinnopolis/WatchmanData/Show_All.php?pcid={$ps_cl_idcard}");
exit;
$pdo = null;

?>
