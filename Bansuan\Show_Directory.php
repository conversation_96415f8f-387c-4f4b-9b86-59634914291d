<script src="/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>
<script src="../SweetAlert2/dist/sweetalert2.all.min.js"></script>

<?php
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

$id = isset($_GET['pf_cl_aid']) ? $_GET['pf_cl_aid'] : '';

//get month from select or current month
$month = isset($_GET['month']) ? $_GET['month'] : date("m");
$year = isset($_GET['year']) ? $_GET['year'] : date("Y");

$currentMonth = date('m');
$currentYear = date('Y');

$m1 = '';
$m2 = '';
$m3 = '';
$m4 = '';
$m5 = '';
$m6 = '';
$m7 = '';
$m8 = '';
$m9 = '';
$m10 = '';
$m11 = '';
$m12 = '';
switch($month)
{
    case "01": $m1 = "selected"; break;
    case "02": $m2 = "selected"; break;
    case "03": $m3 = "selected"; break;
    case "04": $m4 = "selected"; break;
    case "05": $m5 = "selected"; break;
    case "06": $m6 = "selected"; break;
    case "07": $m7 = "selected"; break;
    case "08": $m8= "selected"; break;
    case "09": $m9 = "selected"; break;
    case "10": $m10 = "selected"; break;
    case "11": $m11 = "selected"; break;
    case "12": $m12 = "selected"; break;
}
$current_month = $global_thai_month[0+$month];
$y2 = $year+543;

?>
<!---->
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link rel="stylesheet" href="/bootstrap/css/bootstrap-grid.css">
<style>
    .swal2-popup {
    width: 500px !important;
    height: auto !important;
    font-size: 14px !important;
}
    @media print {
   a {
      display: none !important;
   }
}
    
    /* styles for screens with a width of 768 pixels or less */
@media screen and (max-width: 768px) {
  /* CSS rules go here */
}

/* styles for screens with a width of 769 pixels or more */
@media screen and (min-width: 769px) {
  /* CSS rules go here */
}

</style>
<link rel="stylesheet" type="text/css" href="print.css" media="print">
<div class="container-fluid" align="left">
<div class=" h3 text-center  alert alert-success mb-4 mt-4 " role="alert" >สารบบคุมการแจ้งการควบคุมตัว ตามมาตรา 22 ของ <?= $name_station ?> </div>
  <div>
    <table width="90%" border="0" cellspacing="1" cellpadding="1">
      <tbody>
        <tr>
          <td width="11%"><div align="left">
            &nbsp;<a href="Add_Directory.php" class="btn btn-success btn-lg mb-4 <?= $add_btn ?>" >เพิ่มข้อมูล</a></td>
          <td width="17%"><label>เลือกปี </label>
            <select id="pf_cl_year" name="pf_cl_year" class="form-select col-8 d-lg-inline" style="background-color: #00D2FE; font-size: 16px" onChange="dates_change()" >
              <option value="" >เลือก</option>
              <?php
                $cur_y = date("Y") + 2;
                for($y=2020; $y<$cur_y; $y++) {
                    $sel = ($y == $year) ? "selected" : '';
                    echo "<option value='$y' $sel> ". ($y + 543) ." </option>";
                }
            ?>
            </select></td>
          <td><label>เดือน</label>
            <select id="pf_cl_month" name="pf_cl_month" class="form-select col-8 d-lg-inline" style="background-color: #00D2FE; font-size: 16px" onChange="dates_change()">
              <option value="">เลือก</option>
              <option <?= $m1 ?> value="01">มกราคม</option>
              <option <?= $m2 ?> value="02">กุมภาพันธ์</option>
              <option <?= $m3 ?> value="03">มีนาคม</option>
              <option <?= $m4 ?> value="04">เมษายน</option>
              <option <?= $m5 ?> value="05">พฤษภาคม</option>
              <option <?= $m6 ?> value="06">มิถุนายน</option>
              <option <?= $m7 ?> value="07">กรกฎาคม</option>
              <option <?= $m8 ?> value="08">สิงหาคม</option>
              <option <?= $m9 ?> value="09">กันยายน</option>
              <option <?= $m10 ?> value="10">ตุลาคม</option>
              <option <?= $m11 ?> value="11">พฤศจิกายน</option>
              <option <?= $m12 ?> value="12">ธันวาคม</option>
            </select></td>
          <td width="15%"><a href="https://arrest.dopa.go.th/0308/arrest/core/user/" target="_blank" class="btn btn-dark btn-lg mb-4 rounded-pill"  >ระบบรับแจ้งมหาดไทย</a>
              </td>
			<td width="2%"></td>
			<?php if ($station == 6707) {
				echo '<td width="18%" style="font-weight: bolder" align="justify">เลขบัตรประชาชนของแต่ละคน<br>D@P@0315<br>สำหรับหน่วยงาน 2500700120203</td>';
			} else {} ?>
          
        </tr>
      </tbody>
    </table>
</div>
<h1 style="font-size: 24px ;background-color: yellow ;padding: 5px; margin: auto" h1 align="center">ประจำเดือน &nbsp;<?= $current_month ?>&nbsp;<?= $y2 ?></h1>	
    
<!--  โค้ดตาราง ผลงานกวาดล้าง 2 ถึง จุดนี้ -->
<table width="100%" height="94" border="1" cellpadding="1" cellspacing="1" class="table-bordered table-hover mb-4 mt-4">
  <tbody>
    <tr>
      <td rowspan="2" bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">&nbsp;ลำดับ&nbsp;</td>
      <td height="44" colspan="2" nowrap="nowrap" bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">&nbsp;ชื่อ-นามสกุล ผู้ต้องหา/ผู้ถูกจับและควบคุม&nbsp;</td>
      <td rowspan="2" bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">&nbsp;ข้อหา&nbsp;</td>
      <td colspan="2" bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">&nbsp;ชื่อ-สกุล เจ้าหน้าที่ผู้แจ้ง&nbsp;</td>
      <td colspan="8" bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">&nbsp;ชื่อ-สกุล เจ้าหน้าที่ผู้รับแจ้ง&nbsp;</td>
      <td rowspan="2" bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">&nbsp;หมายเหตุ&nbsp;</td>
      <td colspan="3" rowspan="2" bgcolor="#995D5D"><span style="color: #F7EFEF; text-align: center;">&nbsp;แบบรายงาน&nbsp;</span></td>
      <td colspan="2" rowspan="2" bgcolor="#995D5D">&nbsp;จัดการข้อมูล&nbsp;</td>
    </tr>
    <tr>
	  <td height="44" nowrap="nowrap" bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">&nbsp;เลขบัตร&nbsp;</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;" nowrap="nowrap">ชื่อ - นามสกุล</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">&nbsp;ชื่อ-สกุล&nbsp;</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">&nbsp;ตำแหน่ง&nbsp;</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">&nbsp;ชื่อฝ่ายปกครอง&nbsp;</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">&nbsp;หมายเลขโทรศัทพ์/ช่องทางที่แจ้ง&nbsp;</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">&nbsp;วัน/เดือน/ปี/เวลาที่แจ้ง&nbsp;</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">&nbsp;ไฟล์PDF&nbsp;</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">&nbsp;ชื่ออัยการ&nbsp;</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">&nbsp;หมายเลขโทรศัทพ์/ช่องทางที่แจ้ง&nbsp;</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">&nbsp;วัน/เดือน/ปี/เวลาที่แจ้ง&nbsp;</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">&nbsp;ไฟล์PDF&nbsp;</td>
	  <!--<td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">&nbsp;ไฟล์ Word&nbsp;</td>-->
    </tr>
	  
<?php
	
$sql = "SELECT PS.ps_cl_idcard, PS.ps_cl_prefix, PS.ps_cl_name, PS.ps_cl_surname,
			Dir.*,
			CH.station, CH.ch_cl_date
		FROM wm_tb_personal PS
			LEFT JOIN DirectoryDetention22 Dir ON Dir.idcard = PS.ps_cl_idcard
			LEFT JOIN wm_tb_crimes_history CH ON CH.ch_cl_idcard = PS.ps_cl_idcard
		WHERE CH.station=:station
			AND MONTH(CH.ch_cl_date)=:month
			AND YEAR(CH.ch_cl_date)=:year
			AND MONTH(Dir.gov_date)=:month
			AND YEAR(Dir.gov_date)=:year
		GROUP BY Dir.id
		ORDER BY Dir.gov_date ASC";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':month', $month);
    $stmt->bindParam(':year', $year);
	$stmt->bindParam(':station', $station);
    $stmt->execute();
    
//echo $sql;
$no = 1;
while($row = $stmt->fetch(PDO::FETCH_ASSOC))	//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป , CH.ch_cl_date
{
        
if(!empty($row["gov_date"])){
    $govDate = DateThai( $row["gov_date"] );
}else{
    $govDate = '';
}
if(!empty($row["att_date"])){
    $attDate = DateThai( $row["att_date"] );
}else{
    $attDate = '';
}
    
//ตรวจสอบไฟล์ สส.1 ก่อนว่า มีไฟล์ในตารางไหม หากมี ให้ส่งเป็นลิงค์
	$govPdf = $row["gov_pdf"];
	if($govPdf !== '')
	{
		$links_gov = '<a href="/' . $row["gov_pdf"] .'" target="_blank">Download</a>';
	}
	else {
		$links_gov = 'ไม่มีไฟล์';
	}
    
//ตรวจสอบไฟล์ สส.1 ก่อนว่า มีไฟล์ในตารางไหม หากมี ให้ส่งเป็นลิงค์
	$attPdf = $row["att_pdf"];
	if($attPdf !== '')
	{
		$links_att = '<a href="/' . $row["att_pdf"] .'" target="_blank">Download</a>';
	}
	else {
		$links_att = 'ไม่มีไฟล์';
	}
	
	if(!empty($row["charge"])){
		$charge = 'อยู่ในรายงาน';
	}else{
		$charge = '';
	}
	 
 
?>	

    <tr>
	  <td> <?= $no ?> </td>
      <td nowrap="nowrap">&nbsp;<?= $row['idcard'] ?>&nbsp;</td>
      <td nowrap="nowrap" style="text-align: left">&nbsp;<?= $row['ps_cl_prefix'] ?><?= $row['ps_cl_name'] ?>&nbsp;<?= $row['ps_cl_surname'] ?></td>
      <td nowrap>&nbsp;<?= $charge ?>&nbsp;</td>
      <td style="background: #C8E7B1; text-align: left">&nbsp;<?= $row["pol_arrest"] ?>&nbsp;</td>
      <td style="background: #C8E7B1; text-align: left">&nbsp;<?= $row["position_arrest"] ?>&nbsp;</td>
      <td style="background: #DDF9FA; text-align: left">&nbsp;<?= $row["gov_name"]?>&nbsp;</td>
      <td style="background: #DDF9FA">&nbsp;<?= $row["gov_contact"]?>&nbsp;</td>
      <td style="background: #DDF9FA">&nbsp;<?= $govDate ?>&nbsp;</td>
      <td style="background: #DDF9FA">&nbsp;<?= $links_gov ?>&nbsp;</td>
      <td style="background: #EBCACB; text-align: left">&nbsp;<?= $row["attorney_name"] ?>&nbsp;</td>
      <td style="background: #EBCACB">&nbsp;<?= $row["att_contact"] ?>&nbsp;</td>
      <td style="background: #EBCACB">&nbsp;<?= $attDate ?>&nbsp;</td>
      <td style="background: #EBCACB">&nbsp;<?= $links_att ?>&nbsp;</td>
      <td>&nbsp;<?= $row["remark"] ?>&nbsp;</td>
      <td>&nbsp;&nbsp;<a href="#" class="btn btn-primary mb-4" onclick="showMessage('อจ.22', 'ago22.php?pcid=<?=$row["idcard"]?>')">อจ.22</a></td>
    <td>&nbsp;&nbsp;<a href="#" class="btn btn-primary mb-4" onclick="showMessage('ปค.22', 'gov22.php?pcid=<?=$row["idcard"]?>')">ปค.22</a></td>
    <td>&nbsp;&nbsp;<a href="#" class="btn btn-primary mb-4" onclick="showMessage('ม.23', 'pagePdf23.php?pcid=<?=$row["idcard"]?>')">ม.23</a></td>
	<!--<td>&nbsp;&nbsp;<a href="#" class="btn btn-primary mb-4" onclick="showMessage('W ม.23', 'pagePdf23_2_word.php?pcid=<?=$row["idcard"]?>')">W. ม.23</a></td>-->
		
	  <td>&nbsp;&nbsp;<a href="Edit_Directory.php?id=<?= $row["id"] ?>&pcid=<?= $row["idcard"]?>" class="btn btn-warning mb-4 <?= $edit_btn ?>" >แก้ไข</a></td>
	  <td>&nbsp;&nbsp;<button href="#" class="btn btn-danger mb-4" onClick="deleteItem(<?= $row['id'] ?>, '<?= $row['idcard'] ?>')"<?=$del_btn?>>ลบ</button></td>
    </tr>
  </tbody>
<?php
	$no++;
}//while
?>
</table>
<script>
function deleteItem(id, pcid) {
    Swal.fire({
        title: 'คุณต้องการลบข้อมูลนี้ใช่ไหม?',
        text: "หลังจากลบแล้ว จะไม่สามารถกู้คืนได้",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'ใช่, ยืนยันลบ',
        cancelButtonText: 'ยกเลิก'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = '/Bansuan/Del_Directory.php?id=' + id + '&pcid=' + pcid;
        }
    });
}
</script>

<script>
function dates_change()  // คำสั่งเปลี่ยนเดือน ปี
{
    var ms = $("#pf_cl_month option:selected").val();
    var ys = $("#pf_cl_year option:selected").val();
    window.location = "/Bansuan/index.php?page=Directory&month=" + ms + "&year=" + ys + "&rnd=" + Math.random();
}
</script>
	
<!-- JavaScript function to display the SweetAlert2 message -->
<!-- JavaScript function to display the SweetAlert2 message and open the link -->
<script>
function showMessage(pageName, link) {
  Swal.fire({
    title: 'การกำหนดค่าก่อน Print',
    html: `คลิกขวา ที่หน้าจอ <br>แล้วเลือกรายการ พิมพ์ หรือ print <br>จากนั้นเอาเครื่องหมายติ๊กถูก ที่ตัวเลือก<br>ช่องส่วนหัวกระดาษท้ายกระดาษออก`,
    icon: 'info',
    confirmButtonText: 'OK',
  }).then((result) => {
    if (result.isConfirmed) {
      // User clicked "OK," open the link in a new tab
      window.open(link, '_blank');
    }
  });
}
</script>