<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../Alert.php';

$id = $_GET['id'];
$caseno = $_GET['caseno'];

if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
// Store $pcid in a session variable
$_SESSION['caseno'] = $caseno;

$acc = $user['account'];
$ip_address = $_SERVER['REMOTE_ADDR'];
$action = 'Del confidential gun'; // Update the action to reflect a delete action
if (isset($_SESSION['caseno'])) {
    $deletedItemId = $_SESSION['caseno'];
    $data = ['deleted_item_id' => $deletedItemId]; // Create a data array with the deleted item's ID
	$dataString = json_encode($data); // Encode the array into a JSON string
    log_activity($acc, $action, $dataString, $ip_address);

    // Clear the session variable after successful deletion if needed
    unset($_SESSION['caseno']);
}

if($user['ua_delete_data'] != 100)
{
    echo '<script>alert("เฉพาะสิทธิ์ Admin เท่านั้น")</script>';
}


try{
$sql="DELETE FROM wm_tb_gun_confidential WHERE `aid_conf_gun`= :id ";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':id', $id);
    $stmt->execute();

    if ($stmt->rowCount() > 0) {
        $_SESSION['success'] = "Data has been deleted successfully";
        showSweetAlert("Delete successfully", "ลบข้อมูลเรียบร้อย");
        unset($_SESSION['success']); // Clear the session variable
        header("refresh:2; url=/WatchmanData/Show_gun_treasure_all.php");
    } else {
        $_SESSION['error'] = "ลบข้อมูลไม่สำเร็จ";
        showSweetAlert("Failed to delete", "ลบข้อมูลไม่สำเร็จ");
        unset($_SESSION['success']); // Clear the session variable
        header("refresh:2; url=/WatchmanData/Show_gun_treasure_all.php");
    }
}catch(Exception $e){
    echo 'Query Failed: '. $e->getMessage();
}

?>