<?php
include '../Condb.php';
include '../users.inc.php';
include '../Alert.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

/*echo '<pre>';
print_r($_POST);
echo '</pre>';

echo '<hr>';

echo '<pre>';
var_dump($_POST);
echo '</pre>';

exit();*/

$pf_cl_aid = $_POST['pf_cl_aid'];
$pf_cl_suspect_idcard = $_POST[ 'pf_cl_suspect_idcard' ];			//เลขบัตรผู้ต้องหา
$pf_cl_dates = $_POST[ 'pf_cl_dates' ];								
$pf_cl_type_main = $_POST[ 'pf_cl_type_main' ];						//กวาดล้าง ยาเสพติด ตรวจค้น หมายจับ
$pf_cl_type_sub = $_POST[ 'pf_cl_type_sub' ];						//ยาเสพติด อาวุธปืน การพนัน คนเข้าเมือง เทคโนโลยี หมายจับ
$pf_cl_drug = $_POST[ 'pf_cl_drug' ];								//ข้อหายาเสพติด
//$pf_cl_drug_sell = $_POST[ 'pf_cl_drug_sell' ];						//จำนวนคดี เพื่อจำหน่าย
$pf_cl_drug_sell = isset($_POST['pf_cl_drug_sell']) ? $_POST['pf_cl_drug_sell'] : null;
//$pf_cl_drug_sell_person = $_POST[ 'pf_cl_drug_sell_person' ];		//จำนวนผู้ต้องหา เพื่อจำหน่าย
$pf_cl_drug_sell_person = isset($_POST['pf_cl_drug_sell_person']) ? $_POST['pf_cl_drug_sell_person'] : null;
//$pf_cl_drug_occupy = $_POST[ 'pf_cl_drug_occupy' ];					//จำนวนคดี ครอบครอง
$pf_cl_drug_occupy = isset($_POST['pf_cl_drug_occupy']) ? $_POST['pf_cl_drug_occupy'] : null;
//$pf_cl_drug_occupy_person = $_POST[ 'pf_cl_drug_occupy_person' ];	//จำนวนผู้ต้องหา ครอบครอง
$pf_cl_drug_occupy_person = isset($_POST['pf_cl_drug_occupy_person']) ? $_POST['pf_cl_drug_occupy_person'] : null;
//$pf_cl_take_drugs = $_POST[ 'pf_cl_take_drugs' ];					//จำนวนคดี เสพ
$pf_cl_take_drugs = isset($_POST['pf_cl_take_drugs']) ? $_POST['pf_cl_take_drugs'] : null;
//$pf_cl_take_drugs_person = $_POST[ 'pf_cl_take_drugs_person' ];		//จำนวนผู้ต้องหา เสพ
$pf_cl_take_drugs_person = isset($_POST['pf_cl_take_drugs_person']) ? $_POST['pf_cl_take_drugs_person'] : null;
//$pf_cl_drug_therapy = $_POST[ 'pf_cl_drug_therapy' ];				//จำนวนคดี คัดกรอง
$pf_cl_drug_therapy = isset($_POST['pf_cl_drug_therapy']) ? $_POST['pf_cl_drug_therapy'] : null;
//$pf_cl_drug_therapy_person = $_POST[ 'pf_cl_drug_therapy_person' ];	//จำนวนผู้ต้องหา คัดกรอง
$pf_cl_drug_therapy_person = isset($_POST['pf_cl_drug_therapy_person']) ? $_POST['pf_cl_drug_therapy_person'] : null;
//$pf_cl_items_drug = $_POST[ 'pf_cl_items_drug' ];					//ของกลางยาเสพติด
$pf_cl_items_drug = isset($_POST['pf_cl_items_drug']) ? $_POST['pf_cl_items_drug'] : null;
//$pf_cl_gun = $_POST[ 'pf_cl_gun' ];									//คดีอาวุธปืน
$pf_cl_gun = isset($_POST['pf_cl_gun']) ? $_POST['pf_cl_gun'] : null;
//$pf_cl_items_gun = $_POST[ 'pf_cl_items_gun' ];						//ของกลางคดีอาวุธปืน
$pf_cl_items_gun = isset($_POST['pf_cl_items_gun']) ? $_POST['pf_cl_items_gun'] : null;
//$pf_cl_gambling_case = $_POST[ 'pf_cl_gambling_case' ];				//คดีการพนัน
$pf_cl_gambling_case = isset($_POST['pf_cl_gambling_case']) ? $_POST['pf_cl_gambling_case'] : null;
//$pf_cl_gambling_person = $_POST[ 'pf_cl_gambling_person' ];			//จำนวนผู้ต้องหาการพนัน
$pf_cl_gambling_person = isset($_POST['pf_cl_gambling_person']) ? $_POST['pf_cl_gambling_person'] : null;
//$pf_cl_immigrant_case = $_POST[ 'pf_cl_immigrant_case' ];			//คดีคนเข้าเมือง
$pf_cl_immigrant_case = isset($_POST['pf_cl_immigrant_case']) ? $_POST['pf_cl_immigrant_case'] : null;
//$pf_cl_immigrant_person = $_POST[ 'pf_cl_immigrant_person' ];		//จำนวนผู้ต้องหา คนเข้าเมือง
$pf_cl_immigrant_person = isset($_POST['pf_cl_immigrant_person']) ? $_POST['pf_cl_immigrant_person'] : null;
//$pf_cl_techno_case = $_POST[ 'pf_cl_techno_case' ];					//คดีเทคโน
$pf_cl_techno_case = isset($_POST['pf_cl_techno_case']) ? $_POST['pf_cl_techno_case'] : null;
//$pf_cl_techno_person = $_POST[ 'pf_cl_techno_person' ];				//จำนวนผู้ต้องหา เทคโน
$pf_cl_techno_person = isset($_POST['pf_cl_techno_person']) ? $_POST['pf_cl_techno_person'] : null;
//$pf_cl_wanted = $_POST[ 'pf_cl_wanted' ];							//หมายจับ
$pf_cl_wanted = isset($_POST['pf_cl_wanted']) ? $_POST['pf_cl_wanted'] : null;
//$pf_cl_wanted_qty = $_POST[ 'pf_cl_wanted_qty' ];
$pf_cl_wanted_qty = isset($_POST['pf_cl_wanted_qty']) ? $_POST['pf_cl_wanted_qty'] : null;
//$pf_cl_catch = $_POST[ 'pf_cl_catch' ];								//จับเอง หรือร่วมจับ
$pf_cl_catch = isset($_POST['pf_cl_catch']) ? $_POST['pf_cl_catch'] : null;
$pf_cl_suspect_name = $_POST[ 'pf_cl_suspect_name' ];				//ชื่อผู้ต้องหา
$pf_cl_search_warrant = $_POST[ 'pf_cl_search_warrant' ];			//หมายค้น
$pf_cl_find = $_POST[ 'pf_cl_find' ];				                //พบ/ไม่พบ
$pf_cl_illegal = $_POST[ 'pf_cl_illegal' ];				            //รายการที่ค้นพบ
$pf_cl_record = $_POST[ 'pf_cl_record' ];							//ผู้บันทึกข้อมูล
$region = $_POST[ 'region' ];
$provincial = $_POST[ 'provincial' ];
$station = $_POST[ 'station' ];
$pf_cl_remark = $_POST[ 'pf_cl_remark' ];							//หมายเหตุ

$sql = "SELECT * FROM wm_tb_performance WHERE pf_cl_aid=:pf_cl_aid AND pf_cl_suspect_idcard=:pf_cl_suspect_idcard ";
$stmt = $pdo->prepare($sql);
$stmt->bindParam(':pf_cl_aid',$pf_cl_aid);
$stmt->bindParam(':pf_cl_suspect_idcard',$pf_cl_suspect_idcard);
$stmt->execute();
$row = $stmt->fetch(PDO::FETCH_ASSOC);

if($row){
    $sql1 = "UPDATE wm_tb_performance SET " .
		"pf_cl_suspect_idcard = '$pf_cl_suspect_idcard'," .
		"pf_cl_dates = '$pf_cl_dates'," .                     // รายการนี้เปิดตอนเช้านี้
		"pf_cl_type_main = '$pf_cl_type_main'," .
		"pf_cl_type_sub = '$pf_cl_type_sub'," .
		"pf_cl_drug = '$pf_cl_drug'," .
		"pf_cl_drug_sell = '$pf_cl_drug_sell'," .
		"pf_cl_drug_sell_person = '$pf_cl_drug_sell_person'," .
		"pf_cl_drug_occupy = '$pf_cl_drug_occupy'," .
		"pf_cl_drug_occupy_person = '$pf_cl_drug_occupy_person'," .
		"pf_cl_take_drugs = '$pf_cl_take_drugs'," .
		"pf_cl_take_drugs_person = '$pf_cl_take_drugs_person'," .
		"pf_cl_drug_therapy = '$pf_cl_drug_therapy'," .
		"pf_cl_items_drug = '$pf_cl_items_drug'," .
		"pf_cl_gun = '$pf_cl_gun'," .
		"pf_cl_items_gun = '$pf_cl_items_gun'," .
		"pf_cl_gambling_case = '$pf_cl_gambling_case'," .
		"pf_cl_gambling_person = '$pf_cl_gambling_person'," .
		"pf_cl_immigrant_case = '$pf_cl_immigrant_case'," .
		"pf_cl_immigrant_person = '$pf_cl_immigrant_person'," .
		"pf_cl_techno_case = '$pf_cl_techno_case'," .
		"pf_cl_techno_person = '$pf_cl_techno_person'," .
		"pf_cl_wanted = '$pf_cl_wanted'," .
        "pf_cl_wanted_qty = '$pf_cl_wanted_qty'," .
		"pf_cl_catch = '$pf_cl_catch'," .
		"pf_cl_suspect_name = '$pf_cl_suspect_name'," .
        "pf_cl_search_warrant = '$pf_cl_search_warrant'," .
        "pf_cl_find = '$pf_cl_find'," .
        "pf_cl_illegal = '$pf_cl_illegal'," .
		"pf_cl_record = '$pf_cl_record'," .
		"region = '$region'," .
		"provincial = '$provincial'," .
		"station = '$station'," .
		"pf_cl_remark = '$pf_cl_remark' " .
		"WHERE pf_cl_aid = '$pf_cl_aid' ";
}else{
    $sql1 = "INSERT INTO wm_tb_performance(pf_cl_suspect_idcard, pf_cl_dates, pf_cl_type_main, pf_cl_type_sub, pf_cl_drug, pf_cl_drug_sell, pf_cl_drug_sell_person, pf_cl_drug_occupy, pf_cl_drug_occupy_person, pf_cl_take_drugs, pf_cl_take_drugs_person, pf_cl_drug_therapy, pf_cl_drug_therapy_person, pf_cl_items_drug, pf_cl_gun, pf_cl_items_gun, pf_cl_gambling_case, pf_cl_gambling_person, pf_cl_immigrant_case, pf_cl_immigrant_person, pf_cl_techno_case, pf_cl_techno_person, pf_cl_wanted, pf_cl_wanted_qty, pf_cl_catch, pf_cl_suspect_name, pf_cl_search_warrant, pf_cl_find, pf_cl_illegal, pf_cl_record, region, provincial, station, pf_cl_remark) VALUES('$pf_cl_suspect_idcard', '$pf_cl_dates', '$pf_cl_type_main', '$pf_cl_type_sub', '$pf_cl_drug', '$pf_cl_drug_sell', '$pf_cl_drug_sell_person', '$pf_cl_drug_occupy', '$pf_cl_drug_occupy_person', '$pf_cl_take_drugs', '$pf_cl_take_drugs_person', '$pf_cl_drug_therapy', '$pf_cl_drug_therapy_person', '$pf_cl_items_drug', '$pf_cl_gun', '$pf_cl_items_gun', '$pf_cl_gambling_case', '$pf_cl_gambling_person', '$pf_cl_immigrant_case', '$pf_cl_immigrant_person', '$pf_cl_techno_case', '$pf_cl_techno_person', '$pf_cl_wanted', '$pf_cl_wanted_qty', '$pf_cl_catch', '$pf_cl_suspect_name', '$pf_cl_search_warrant', '$pf_cl_find', '$pf_cl_illegal', '$pf_cl_record', '$region', '$provincial', '$station', '$pf_cl_remark') ";
}

$stmt1 = $pdo->prepare($sql1);

try{
    $result = $stmt1->execute($params);
}catch(PDOException $e){
    echo 'Query $result Failed: '. $e->getMessage();
}

$ip_address = $_SERVER['REMOTE_ADDR'];
$action = "Save performance: $pf_cl_suspect_idcard"; // You can set the action to a suitable value
$data = str_replace("'", "", compact_array($_POST));
log_activity($acc, $action, $data, $ip_address); // ใช้ function ในไฟล์ Condb.php

if ($result) {
    $_SESSION['success'] = "Data has been inserted succesfully";
    showSweetAlert("Saved successfully", "บันทึกข้อมูลเรียบร้อย");
    unset($_SESSION['success']); // Clear the session variable
    header("refresh:2; url=/Bansuan/index.php?rnd=' + Math.random() + '&page=performance");
} else {
    $_SESSION['error'] = "Error";
    showSweetAlert('Error', "บันทึกข้อมูล ไม่สำเร็จ", 'error');
    unset($_SESSION['error']); // Clear the session variable
    header("refresh:2; url=/Bansuan/index.php?rnd=' + Math.random() + '&page=performance");
}

$pdo = null;
    
?>