<?php //PDO
include '../Condb.php';
include '../users.inc.php';
include '../right_user.php';
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

$loginStation = $station;

$stations = array(
    6707 => array('latitude' => 17.012397, 'longitude' => 99.838612),
    6709 => array('latitude' => 17.***************, 'longitude' => 99.**************),  // เมืองสุโขทัย
	6604 => array('latitude' => 16.**************, 'longitude' => 101.**************),  // ท่าพล เพชรบูรณ์ 
	5207 => array('latitude' => 18.***************, 'longitude' => 99.**************),  // ดอยสะเก็ด เชียงใหม่
	6504 => array('latitude' => 17.***************, 'longitude' => 100.**************), // นครไทย พิษณุโลก
	6912 => array('latitude' => 15.***************, 'longitude' => 100.**************), // อุทัยธานี
	6310 => array('latitude' => 15.***************, 'longitude' => 99.**************),  // บรรพตพิสัย นครสวรรค์
	5903 => array('latitude' => 18.**************5, 'longitude' => 98.99893760891592),  // สืบภาค 5
	61005 => array('latitude' => 16.81279274125541, 'longitude' => 100.25810299723423),  // สืบภาค 6
	1193 => array('latitude' => 13.766551008032348, 'longitude' => 100.54819326566101), // ศปจร.ตร.
);

if (isset($stations[$loginStation])) {
    $centerLat = $stations[$loginStation]['latitude'];
    $centerLng = $stations[$loginStation]['longitude'];
} else {
    // Default center coordinates if the login station is not found in the $stations array
    $centerLat = 0;
    $centerLng = 0;
}
// Now, let's update the mapOptions with the dynamic center coordinates
$mapOptions = array(
    'center' => array('lat' => $centerLat, 'lng' => $centerLng),
    'zoom' => 14 // Set your desired zoom level here
);
?>


<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Maps Display</title>
	<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCIN1hO0EaE0HOhnhxk5i0tC1COym6fWSw&callback=initMap" async defer></script>
    <script>
        function initMap() {
            var mapOptions = <?php echo json_encode($mapOptions); ?>;
            var map = new google.maps.Map(document.getElementById('map'), mapOptions);

            //var map = new google.maps.Map(document.getElementById('map'), mapOptions);

            // Fetch location data from the database using PHP and PDO
            <?php
            $sql = "SELECT *, PV.pv_name_th AS PV_name, AP.ap_name_th AS AP_name, TB.tb_name_th AS TB_name FROM cctv_locations L
					LEFT JOIN provinces as PV ON PV.pv_code = L.cctv_province
					LEFT JOIN amphures as AP ON AP.ap_code = L.cctv_amphur
					LEFT JOIN districts as TB ON TB.tb_id = L.cctv_tumbon
					WHERE station=:station ";
			$stmt = $pdo->prepare($sql);
			$stmt->bindParam(':station',$station);
			$stmt->execute();
			$locations = $stmt->fetchAll(PDO::FETCH_ASSOC);

            foreach ($locations as $location) {
				
				$adr_cctv = $location['cctv_adr'] . ' หมู่ที่ ' . $location['cctv_moo'] . ' ต.' . $location['TB_name'] . ' อ.' . $location['AP_name'] . ' จ.' . $location['PV_name'];
				
                //$color = isset($location['pin_color']) ? $location['pin_color'] : "red"; // Default color if no color specified
                $content = '<div><strong>@ :</strong> ' . $location['location_name'] . '</div>' .
						   '<div>ที่อยู่ : ' . $adr_cctv . '</div>' .
						   '<div>ผู้รับผิดชอบ : ' . $location['cctv_admin'] . ' </div>' .
						   '<div>เบอร์โทร : ' . $location['cctv_contact'] . ' </div>' .
						   '<hr>' .
                           '<div align="center"><img src="' . $location['pin_image'] . '" alt="Image" style="max-width: 200px;"></div>'
						   ;

                // Check if the color has a corresponding marker icon, otherwise use the default color
                //$iconUrl = isset($colorToMarkerIcon[$color]) ? $colorToMarkerIcon[$color] : $colorToMarkerIcon['red'];

                echo "var marker = new google.maps.Marker({
                    position: {lat: {$location['latitude']}, lng: {$location['longitude']}},
                    map: map,
                    title: '{$location['location_name']}',
                    label: '{$location['location_name']}'
                    
                });

               var infoWindow = new google.maps.InfoWindow();

                // Create a function to associate the marker and infoWindow content correctly
                (function(marker, content) {
                    google.maps.event.addListener(marker, 'click', function() {
                        console.log('Marker clicked: ' + marker.title);
                        infoWindow.setContent(content);
                        infoWindow.open(map, marker);
                    });
                })(marker, '$content');";
            }
            ?>
        }
    </script>
	<style>
		.headtext {
			margin-top: 20px;
		}
	</style>
</head>
<body>
	<div align="center" class="headtext"> <h2>ข้อมูลแผนที่กล้องวงจรปิด (CCTV Mapping) ในพื้นที่ <?= $name_station ?></h2></div>
	<p align="right">คุณ Login ด้วย User <?= $name_station ?>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</p><br>
	&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="../WatchmanData/main.php" class="btn btn-primary btn-lg mb-4" >หน้าหลัก</a>
	&nbsp;&nbsp;<a href="../Bansuan/GoogleMapLocation.php" class="btn btn-success btn-lg mb-4 rounded-pill <?= $add_btn ?>" >เพิ่มจุด CCTV ลงแผนที่</a>
	&nbsp;&nbsp;<a href="../WatchmanData/Show_CCTV_googleMap.php" class="btn btn-warning btn-lg mb-4 rounded-pill <?= $edit_btn ?>" >แก้ไขข้อมูล CCTV</a>
	&nbsp;&nbsp;<a href="../Bansuan/GoogleMapDisplay_PV.php" class="btn btn-danger btn-lg mb-4 rounded-pill" target="_blank">ข้อมูล CCTV <?= $name_provincial ?> </a>
	
	<!-- แสดงแผนที่ -->
    <div id="map" style="height: 500px;"></div><br>
	
<?php
	if ($region == 6) {
		echo '<div>	
	&nbsp;&nbsp;<a href="../Bansuan/GoogleMapsDisplay_kp.php" class="btn btn-secondary btn-lg mb-4 rounded-pill" target="_blank">กำแพงเพชร </a>
	&nbsp;&nbsp;<a href="../Bansuan/GoogleMapsDisplay_tk.php" class="btn btn-secondary btn-lg mb-4 rounded-pill" target="_blank">ตาก </a>
	&nbsp;&nbsp;<a href="../Bansuan/GoogleMapsDisplay_nw.php" class="btn btn-secondary btn-lg mb-4 rounded-pill" target="_blank">นครสวรรค์ </a>
	&nbsp;&nbsp;<a href="../Bansuan/GoogleMapsDisplay_pj.php" class="btn btn-secondary btn-lg mb-4 rounded-pill" target="_blank">พิจิตร </a>
	&nbsp;&nbsp;<a href="../Bansuan/GoogleMapsDisplay_pl.php" class="btn btn-secondary btn-lg mb-4 rounded-pill" target="_blank">พิษณุโลก </a>
	&nbsp;&nbsp;<a href="../Bansuan/GoogleMapsDisplay_pb.php" class="btn btn-secondary btn-lg mb-4 rounded-pill" target="_blank">เพชรบูรณ์ </a>
	&nbsp;&nbsp;<a href="../Bansuan/GoogleMapsDisplay_st.php" class="btn btn-secondary btn-lg mb-4 rounded-pill" target="_blank">สุโขทัย </a>
	&nbsp;&nbsp;<a href="../Bansuan/GoogleMapsDisplay_ud.php" class="btn btn-secondary btn-lg mb-4 rounded-pill" target="_blank">อุตรดิตถ์ </a>
	&nbsp;&nbsp;<a href="../Bansuan/GoogleMapsDisplay_ut.php" class="btn btn-secondary btn-lg mb-4 rounded-pill" target="_blank">อุทัยธานี </a>
	</div>';
	}
	
?>
	
	<!--&nbsp;&nbsp;<a href="../Bansuan/GoogleMapDisplayP6.php" class="btn btn-danger btn-lg mb-4 rounded-pill" target="_blank">ข้อมูล CCTV ทั้งภาค 6</a>-->
	
<?php /*?><?php
// Replace $_SESSION['provincial'] with the actual variable that holds the user's provincial value
//$userProvincial = $_SESSION[$provincial]; // Assuming you have this value available

// Define an array to map the provincial values to the corresponding file names
$provincialMap = [
    61 => 'KP',
    62 => 'TK',
    63 => 'NW',
    64 => 'PJ',
    65 => 'PL',
    66 => 'PB',
    67 => 'ST',
    68 => 'UD',
    69 => 'UT'
];

// Check if the user's provincial value exists in the $provincialMap array
if (array_key_exists($provincial, $provincialMap)) {
    // Get the provincial name
    $provincialName = $provincialMap[$provincial];

    // Output the links while excluding the link for the user's provincial value
    echo '<div>';
    foreach ($provincialMap as $provincialValue => $name) {
        if ($provincialValue !== $provincial) {
            echo '&nbsp;&nbsp;<a href="../Bansuan/GoogleMapsDisplay_' . strtolower(substr($name, 0, 2)) . '.php" class="btn btn-secondary btn-lg mb-4 rounded-pill" target="_blank">' . $name . '</a>';
        }
    }
    echo '</div>';
} else {
    // If the user's provincial value is not found in the $provincialMap, display all links
    echo '<div>';
    foreach ($provincialMap as $provincialValue => $name) {
        echo '&nbsp;&nbsp;<a href="../Bansuan/GoogleMapsDisplay_' . strtolower(substr($name, 0, 2)) . '.php" class="btn btn-secondary btn-lg mb-4 rounded-pill" target="_blank">' . $name . '</a>';
    }
    echo '</div>';
}
?><?php */?>

	
</body>
</html>
