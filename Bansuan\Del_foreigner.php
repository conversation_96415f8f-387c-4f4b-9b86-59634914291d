<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../Alert.php';

/*$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);
$ip_address = $_SERVER['REMOTE_ADDR'];
$action = 'Del foreigner'; // You can set the action to a suitable value
$data = str_replace("'", "", compact_array($_POST));
log_activity($acc, $action, $data, $ip_address); // ใช้ function ในไฟล์ Condb.php*/

$acc = $user['account'];
$action = 'Delete foreigner'; // Update the action to reflect a delete action
$data = compact_array($_POST); // Capture the data before deletion
log_activity($acc, $action, $data, $ip_address);

// Perform the data deletion here
// ...
// After deletion, you can also log a confirmation message
$deleteConfirmation = "User deleted: " . json_encode($data);
log_activity($acc, 'Deletion Confirmation', $deleteConfirmation, $ip_address);

$id = $_GET['id'];

$sql="DELETE FROM wm_tb_foreigner WHERE fo_cl_aid = :id ";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':id', $id);
try{
    $result = $stmt->execute();
}catch (PDOException $e) {
    echo $e->getMessage();
}

if ($result) {
    $_SESSION['success'] = "Data has been deleted successfully";
    showSweetAlert("Delete successfully", "ลบข้อมูลเรียบร้อย");
    unset($_SESSION['success']); // Clear the session variable
    header("refresh:2; url=/Bansuan/index.php?rnd=' + Math.random() + '&page=foreigner");
} else {
    $_SESSION['error'] = "ลบข้อมูลไม่สำเร็จ";
    showSweetAlert("Failed to delete", "ลบข้อมูลไม่สำเร็จ");
    unset($_SESSION['success']); // Clear the session variable
    header("refresh:2; url=/Bansuan/index.php?rnd=' + Math.random() + '&page=foreigner");
}

$pdo = null;

?>