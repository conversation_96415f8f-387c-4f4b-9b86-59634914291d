<?php
include '../Condb.php';
include '../users.inc.php';
include '../right_user.php';
include '../Head_image.php';


// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);


$pcid = isset($_GET['pcid']) ? $_GET['pcid'] : '';
if($pcid === '') 
{
	//header("Location: Show_Personal.php");
	exit;
}


$query1 = "SELECT PS.*, PDPA.* FROM wm_tb_personal PS
				LEFT JOIN wm_tb_personal_pdpa PDPA ON PDPA.pdpa_idcard = PS.ps_cl_idcard
			WHERE PS.ps_cl_idcard = :pcid ";
    $stmt = $pdo->prepare($query1);
    $stmt->bindValue(':pcid', $pcid, PDO::PARAM_STR);
    $stmt->execute();
    $row = $stmt->fetch(PDO::FETCH_ASSOC);

// หากไม่มีข้อมูลในฐานข้อมูล
if (!$row) {
    echo '
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        document.addEventListener("DOMContentLoaded", function() {
            Swal.fire({
                title: "ยังไม่ได้บันทึกข้อมูลของบุคคลนี้",
                icon: "warning",
                confirmButtonText: "ตกลง"
            }).then((result) => {
                if (result.isConfirmed) {
                    window.location.href = "/WatchmanData/Add_Personal.php";
                }
            });
        });
    </script>';
    exit;
}

if (!empty($row['ps_cl_nickname'])) {
    $people_name = $row['ps_cl_prefix'] .  $row['ps_cl_name'] . ' ' . $row['ps_cl_surname'] . ' (' . $row['ps_cl_nickname'] . ')';
} else {
    $people_name = $row['ps_cl_prefix'] .  $row['ps_cl_name'] . ' ' . $row['ps_cl_surname'];
}

$people_image = $row["ps_cl_image"];


// ตรวจสอบและแสดงผลอายุ
if (!empty($row["ps_cl_birthday2"])) {
	// คำนวณอายุจาก ps_cl_birthday2 ถ้ามีข้อมูล
	$age = get_personal_age_2($row["ps_cl_birthday2"]);
	$age_display = $age . " ปี";
} elseif (!empty($row["age_rec"])) {
	// แสดงอายุจาก age_rec ถ้ามีข้อมูล
	$age_display = $row["age_rec"] . " ปี";
} else {
	// ถ้าไม่มีข้อมูลทั้งสองคอลัมน์
	$age_display = '-';
}

//ฟังก์ชั่น เปลี่ยนเป็นวันที่ไทย แบบเต็ม
if($row["date_of_death"]!=''){
    $strDateD = DateThaiFull( $row["date_of_death"] );
}else{
    $strDateD = '';
}

// เปลี่ยนรูปแบบ เลขบัตรประชาชน
$id_number = $row['ps_cl_idcard'];
$formatted_id = $id_number[0] . "-" . substr($id_number, 1, 4) . "-" . substr($id_number, 5, 5) . "-" . substr($id_number, 10, 2) . "-" . $id_number[12];


?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>ระบบ WatchmanDB</title>
<!-- เรียกใช้ Bootstrap CSS  //block=auto -->
<link rel="stylesheet" href="/policeinnopolis/bootstrap/css/bootstrap.rtl.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
<script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>
<script src="../SweetAlert2/dist/sweetalert2.all.min.js"></script>
<style type="text/css">
	.container-data {
		overflow: auto;		
		height: auto;
		display:  block;  
	}
	.forMobile {
		display: none;
	}
	#imageContainer{
		padding: 5px;
	}
/*@media(max-width: 500px){
	.forPc {
		display: none;
	}
	#headtext {
		display: none;
	}
	.menu {
		display: none;
	}
	.forMobile {
		display: grid;
		margin: 10px;
	}
	.psimg {
		background: #DFA6A7;
		width: 50%;
		border: solid #1C02FB;
		position: absolute;  
 		top: 10%;  
		right: 25%;
	}
	.dataps {
		text-align: start;
		margin-left: 10px;
		position: absolute;  
 		top: 65%;  
		left: 20%;
	}
}*/
</style>
<style>
	
</style>
<!--<script src="SweetAlert2/dist/sweetalert2.all.min.js"></script>-->
<!--การบันทึก Print Screen-->
	
</head>
<body>

<div class="container">
	<div id="headtext" class=" h4 text-center  alert alert-info mb-4 mt-4" role="alert" > รายละเอียดข้อมูลส่วนบุคคล </div>
<div id="headtext">
<?php
include('../users_info.php');
?>
</div>
<div id="headtext" align="left">
	<div class="menu">
	&nbsp;&nbsp; <a href="main.php" class="btn btn-primary btn-lg mb-4" >หน้าหลัก</a>
	&nbsp;&nbsp; <a href="/policeinnopolis/WM/?rnd=<?= rand(); ?>&page=wanted" class="btn btn-light btn-lg mb-4" >ข้อมูลหมายจับ</a> &nbsp;<a href="/policeinnopolis/WM/?rnd=<?= rand(); ?>&page=freed" class="btn btn-light btn-lg mb-4" >ข้อมูลบุคคลพ้นโทษ</a>&nbsp;<a href="/policeinnopolis/WM/Show_crimes_person.php" class="btn btn-light btn-lg mb-4" > บุคคลอาชญากรรม</a>&nbsp;<a href="/policeinnopolis/WM/Show_Drug_xray.php" class="btn btn-light btn-lg mb-4" > ข้อมูล X-ray</a>
    
    &nbsp;<a href="../WM/Checklist_wanted30_1.php?pcid=<?= $pcid ?>" class="btn btn-primary mb-4" >Check list 32 รายการ </a>
    &nbsp;<a href="../WM/Checklist_wanted.php?pcid=<?= $pcid ?>" class="btn btn-primary mb-4">Click list แบบละเอียด</a>
	</div>
</div>
	
<table class="forPc" border="1">
<tr>
    <td rowspan="2" align="center" valign="top" style="text-align: center; <?php if ($row['ps_cl_death'] == 2) { echo 'background-color: gray;'; } else { echo 'background-color: #1F00F9;'; } ?>">
	<!--<div style="width: 300px !important; display: inline-block !important; font-size: 18px;">
		<?= $people_image ?><br><br>
		<b style="color: aliceblue; font-size: 20px;"><?= $people_name ?></b><br><br>-->

		<div style="width: 300px !important; display: inline-block !important; font-size: 18px;">
			<div id="imageContainer" style="display: none;">	
				<?php if ($people_image != ''): ?>
					<img src="<?= htmlspecialchars($people_image) ?>" width="100%">
				<?php else: ?>
					<p style="color: red">ไม่มีภาพ</p>
				<?php endif; ?>
			</div>
			<button id="showImageButton" style="cursor: pointer; background: none; border: none; color: #fff;">
				ภาพ <i class="fa fa-eye" aria-hidden="true"></i> 
			</button>
			<br><br>
			<b style="color: aliceblue; font-size: 20px;"><?= $people_name ?></b><br><br>
			<!-- ซ่อนข้อความ $formatted_id และแสดงปุ่ม "แสดงเลขบัตร" -->
			<div id="formattedIdContainer" style="display: none;">
				<b style="color: aliceblue; font-size: 20px;"><?= $formatted_id ?></b><br><br>
			</div>
			<button id="showFormattedIdButton" style="cursor: pointer; background: none; border: none; color: #fff;">
				เลขบัตร <i class="fa fa-eye" aria-hidden="true"></i>
			</button>
			<br>
			<b style="color: aliceblue; font-size: 20px;">อายุ  <?= $age_display ?></b><br><br>
			<b style="color: black; font-size: 25px;"><?php  if ($row['ps_cl_death'] == 2) { echo " เสียชีวิต {$strDateD}"; } ?></b><br> <!--  กำหนดเงื่อนไขว่า ถ้า สถานะการตาย=2 ให้แสดงวันที่ -->
			<a href="Show_All.php?pcid=<?= $row["ps_cl_idcard"] ?>" class="btn btn-outline-info" >Info</a>
			<a href="Show_All.php?pcid=<?= $row["ps_cl_idcard"] ?>&page=pdpa" class="btn btn-outline-info" >PDPA</a>
			<br><br>
		</div>

		<script>
			document.getElementById('showImageButton').addEventListener('click', function() {
				var imageContainer = document.getElementById('imageContainer');
				if (imageContainer.style.display === 'none') {
					imageContainer.style.display = 'block';
					this.style.display = 'none'; // Hide the button after clicking
				}
			});
			
			document.getElementById('showFormattedIdButton').addEventListener('click', function() {
				var formattedIdContainer = document.getElementById('formattedIdContainer');
				if (formattedIdContainer.style.display === 'none') {
					formattedIdContainer.style.display = 'block';
					this.style.display = 'none'; // ซ่อนปุ่มหลังคลิก
				}
			});
		</script>


        <?php
            if ($row["ps_cl_type"] == 1) {
                // If ps_cl_type is 1, show button AAA and hide button BBB
                echo '<a href="/policeinnopolis/WM/form_gen_personal.php?pcid=' . $row["ps_cl_idcard"] . '" class="btn btn-success" target="_blank">พิมพ์ประวัติ ผนวก ค.</a>';
                echo '<a href="/policeinnopolis/WM/form_crimes_personal.php?pcid=' . $row["ps_cl_idcard"] . '" class="btn btn-dark" target="_blank" style="display:none;">ผนวก ซ.</a>';
            } else if ($row["ps_cl_type"] == 2) {
                // If ps_cl_type is 2, show button BBB and hide button AAA
                echo '<a href="/policeinnopolis/WM/form_gen_personal.php?pcid=' . $row["ps_cl_idcard"] . '" class="btn btn-success" target="_blank" style="display:none;">ผนวก ค.</a>';
                echo '<a href="/policeinnopolis/WM/form_crimes_personal.php?pcid=' . $row["ps_cl_idcard"] . '" class="btn btn-dark" target="_blank">พิมพ์ประวัติ ผนวก ซ.</a>';
            }
        ?>

        <br><br>
	</div>
</td>
	<?php
$page = $_GET['page'] ?? '';
?>
	<td height="20" valign="top" nowrap>
		<ul class="nav nav-tabs">
			<li class="nav-item">
				<a href="Show_All.php?rnd=<?= rand(); ?>&pcid=<?= $row["ps_cl_idcard"] ?>&page=address" class="btn <?= ($page == 'address') ? 'btn-primary' : 'btn-outline-primary' ?> mb-4">ที่อยู่</a>&nbsp;
			</li>
			<li class="nav-item">
				<a href="Show_All.php?rnd=<?= rand(); ?>&pcid=<?= $row["ps_cl_idcard"] ?>&page=phone" class="btn <?= ($page == 'phone') ? 'btn-primary' : 'btn-outline-primary' ?> mb-4">โทรศัพท์</a>&nbsp;
			</li>
			<li class="nav-item">
				<a href="Show_All.php?rnd=<?= rand(); ?>&pcid=<?= $row["ps_cl_idcard"] ?>&page=spouse" class="btn <?= ($page == 'spouse') ? 'btn-primary' : 'btn-outline-primary' ?> mb-4">คู่ครอง</a>&nbsp;
			</li>
			<li class="nav-item">
				<a href="Show_All.php?rnd=<?= rand(); ?>&pcid=<?= $row["ps_cl_idcard"] ?>&page=child" class="btn <?= ($page == 'child') ? 'btn-primary' : 'btn-outline-primary' ?> mb-4">บุตร</a>&nbsp;
			</li>
			<li class="nav-item">
				<a href="Show_All.php?rnd=<?= rand(); ?>&pcid=<?= $row["ps_cl_idcard"] ?>&page=family" class="btn <?= ($page == 'family') ? 'btn-primary' : 'btn-outline-primary' ?> mb-4">พ่อแม่พี่น้อง</a>&nbsp;
			</li>
			<li class="nav-item">
				<a href="Show_All.php?rnd=<?= rand(); ?>&pcid=<?= $row["ps_cl_idcard"] ?>&page=idcard" class="btn <?= ($page == 'idcard') ? 'btn-primary' : 'btn-outline-primary' ?> mb-4">การทำบัตร</a>&nbsp;
			</li>
			<li class="nav-item">
				<a href="Show_All.php?rnd=<?= rand(); ?>&pcid=<?= $row["ps_cl_idcard"] ?>&page=change" class="btn <?= ($page == 'change') ? 'btn-primary' : 'btn-outline-primary' ?> mb-4">เปลี่ยนชื่อ</a>&nbsp;
			</li>
            <li class="nav-item">
				<a href="Show_All.php?rnd=<?= rand(); ?>&pcid=<?= $row["ps_cl_idcard"] ?>&page=in_house" class="btn <?= ($page == 'in_house') ? 'btn-primary' : 'btn-outline-primary' ?> mb-4">บุคคลในบ้าน</a>&nbsp;<br>
			</li> 
		</ul>
		
		<ul class="nav nav-tabs">
            <li class="nav-item">
				<a href="Show_All.php?rnd=<?= rand(); ?>&pcid=<?= $row["ps_cl_idcard"] ?>&page=crimes" class="btn <?= ($page == 'crimes') ? 'btn-danger' : 'btn-outline-danger' ?> mb-4">* ประวัติความผิด</a>&nbsp;<br>
			</li>
			<li>
				<a href="Show_All.php?rnd=<?= rand(); ?>&pcid=<?= $row["ps_cl_idcard"] ?>&page=warrant" class="btn <?= ($page == 'warrant') ? 'btn-danger' : 'btn-outline-danger' ?> mb-4">มีหมายจับ</a>&nbsp;
			</li>
			<li>
				<a href="Show_All.php?rnd=<?= rand(); ?>&pcid=<?= $row["ps_cl_idcard"] ?>&page=freed" class="btn <?= ($page == 'freed') ? 'btn-dark' : 'btn-outline-dark' ?> mb-4">เป็นบุคคลพ้นโทษ</a>&nbsp;
			</li>
			<li class="nav-item">
				<a href="Show_All.php?rnd=<?= rand(); ?>&pcid=<?= $row["ps_cl_idcard"] ?>&page=SMIV" class="btn <?= ($page == 'SMIV') ? 'btn-danger' : 'btn-outline-danger' ?> mb-4">ประวัติป่วยจิตเภท</a>&nbsp;<br>
			</li>
			<li class="nav-item">
				<a href="Show_All.php?rnd=<?= rand(); ?>&pcid=<?= $row["ps_cl_idcard"] ?>&page=mark_person" class="btn <?= ($page == 'mark_person') ? 'btn-success' : 'btn-outline-success' ?> mb-4">ตำหนิรูปพรรณ</a>&nbsp;<br>
			</li>
			<li class="nav-item">
				<a href="Show_All.php?rnd=<?= rand(); ?>&pcid=<?= $row["ps_cl_idcard"] ?>&page=friend" class="btn <?= ($page == 'friend') ? 'btn-dark' : 'btn-outline-dark' ?> mb-4">เพื่อน+คนรู้จัก+เครือข่าย</a>&nbsp;<br>
			</li>
		</ul>
		
		<ul class="nav nav-tabs">
			<li class="nav-item">
				<a href="Show_All.php?rnd=<?= rand(); ?>&pcid=<?= $row["ps_cl_idcard"] ?>&page=invest" class="btn <?= ($page == 'invest') ? 'btn-success' : 'btn-outline-success' ?> mb-4">สืบสวนโทรศัพท์</a>&nbsp;
			</li>
			<li class="nav-item">
				<a href="Show_All.php?rnd=<?= rand(); ?>&pcid=<?= $row["ps_cl_idcard"] ?>&page=work" class="btn <?= ($page == 'work') ? 'btn-success' : 'btn-outline-success' ?> mb-4">การทำงาน</a>&nbsp;
			</li>
			<li class="nav-item">
				<a href="Show_All.php?rnd=<?= rand(); ?>&pcid=<?= $row["ps_cl_idcard"] ?>&page=bank" class="btn <?= ($page == 'bank') ? 'btn-success' : 'btn-outline-success' ?> mb-4">ธนาคาร</a>&nbsp;
			</li>
			<li class="nav-item">
				<a href="Show_All.php?rnd=<?= rand(); ?>&pcid=<?= $row["ps_cl_idcard"] ?>&page=vehicle" class="btn <?= ($page == 'vehicle') ? 'btn-success' : 'btn-outline-success' ?> mb-4">ยานพาหนะ</a>&nbsp;
			</li>
			<li class="nav-item">
				<a href="Show_All.php?rnd=<?= rand(); ?>&pcid=<?= $row["ps_cl_idcard"] ?>&page=welfare" class="btn <?= ($page == 'welfare') ? 'btn-success' : 'btn-outline-success' ?> mb-4">รัฐ/เอกชน</a>&nbsp;
			</li>
			<li class="nav-item">
				<a href="Show_All.php?rnd=<?= rand(); ?>&pcid=<?= $row["ps_cl_idcard"] ?>&page=utilities" class="btn <?= ($page == 'utilities') ? 'btn-success' : 'btn-outline-success' ?> mb-4">สาธารณูปโภค</a>&nbsp;
			</li>
			<li class="nav-item">
				<a href="Show_All.php?rnd=<?= rand(); ?>&pcid=<?= $row["ps_cl_idcard"] ?>&page=social" class="btn <?= ($page == 'social') ? 'btn-success' : 'btn-outline-success' ?> mb-4">โซเชียล</a>&nbsp;
			</li>
       		<li class="nav-item">
				<a href="Show_All.php?rnd=<?= rand(); ?>&pcid=<?= $row["ps_cl_idcard"] ?>&page=nhso" class="btn <?= ($page == 'nhso') ? 'btn-info' : 'btn-outline-info' ?> mb-4">สปสช.</a>&nbsp;<br>
			</li>
        	<li class="nav-item">
				<a href="Show_All.php?rnd=<?= rand(); ?>&pcid=<?= $row["ps_cl_idcard"] ?>&page=vaccine" class="btn <?= ($page == 'vaccine') ? 'btn-info' : 'btn-outline-info' ?> mb-4">วัคซีน</a>&nbsp;<br>
			</li>
        	<li class="nav-item">
				<a href="Show_All.php?rnd=<?= rand(); ?>&pcid=<?= $row["ps_cl_idcard"] ?>&page=gun" class="btn <?= ($page == 'gun') ? 'btn-dark' : 'btn-outline-dark' ?> mb-4">อาวุธปืน</a>&nbsp;<br>
			</li>
		</ul>
		
		<ul class="nav nav-tabs">
			<li>
				<a href="Edit_all_Personal.php?id=<?= $row["ps_cl_aid"] ?>&pcid=<?= $row["ps_cl_idcard"] ?>" class="btn btn-warning mb-4 <?= $edit_btn ?>" >แก้ไขข้อมูลบุคคล</a>&nbsp;
			</li>
			<li>
				<button href="#" class="btn btn-danger mb-4 <?= $del_btn ?>" onClick="deleteItem1(<?= $row["ps_cl_aid"] ?>, '<?= $row["ps_cl_idcard"] ?>')" >ลบข้อมูลบุคคล</button>
			</li>
		</ul>
  </td>
	</tr>
<tr>
	<!-- สำหรับ มือถือ 
<div class="forMobile">

		<div class="psimg">
		<?= $people_image ?>
		</div>
		<div class="dataps">
		ชื่อ : <?= $people_name ?> <br>
		เลขบัตร : <?= $formatted_id ?> <br>
		อายุ : <?= $age_display ?> <br>
		<?php  if ($row['ps_cl_death'] == 2) { echo " เสียชีวิต {$strDateD}"; } ?> <br>
		<a href="Show_All.php?pcid=<?= $row["ps_cl_idcard"] ?>" class="btn btn-outline-info" >Info</a>
		</div>

</div> -->
	
  <td valign="top" height="100%">
	  <div class="container-data">
        <p>
          <?php
	  $page = isset($_GET['page']) ? $_GET['page'] : '';
		
	  if ($page == '')
	  {
		  include("show_all_person.php");
	  }
	  elseif($page == 'address') {
		  include("show_all_address.php");
	  }
	  elseif($page == 'phone') {
		  include("show_all_phone.php");
	  }
	  elseif($page == 'spouse') {
		  include("show_all_spouse.php");
	  }
	  elseif($page == 'child') {
		  include("show_all_child.php");
	  }
	  elseif($page == 'family') {
		  include("show_all_family.php");
	  }
	  elseif($page == 'change') {
		  include("show_all_change.php");
	  }
	  elseif($page == 'vehicle') {
		  include("show_all_vehicle.php");
	  }
	  elseif($page == 'work') {
		  include("show_all_work.php");
	  }
	  elseif($page == 'nhso') {
		  include("show_all_nhso.php");
	  }
	  elseif($page == 'vaccine') {
		  include("show_all_vaccine.php");
	  }
	  elseif($page == 'crimes') {
		  include("show_all_crimes.php");
	  }
	  elseif($page == 'warrant') {
		  include("show_all_warrant.php");
	  }
	  elseif($page == 'invest') {
		  include("show_all_invest.php");
	  }
	  elseif($page == 'welfare') {
		  include("show_all_welfare.php");
	  }
	  elseif($page == 'utilities') {
		  include("show_all_utilities.php");
	  }
	  elseif($page == 'social') {
		  include("show_all_social.php");
	  }
	  elseif($page == 'idcard') {
		  include("show_all_idcard.php");
	  }
	  elseif($page == 'freed') {
		  include("show_all_freed.php");
	  }
	  elseif($page == 'bank') {
		  include("show_all_bank.php");
	  }
      elseif($page == 'gun') {
		  include("show_all_gun.php");
	  }
      elseif($page == 'friend') {
		  include("show_all_friend.php");
	  }
      elseif($page == 'in_house') {
		  include("show_all_in_house.php");
	  }
	  elseif($page == 'mark_person') {
		  include("show_all_mark_person.php");
	  }
	  elseif($page == 'SMIV') {
		  include("Show_All_SMIV-3.php");
	  }
	  elseif($page == 'pdpa') {
		  include("Show_All_pdpa.php");
	  }
	elseif($page === '') {
		///
	}
	else {
		echo "&nbsp;&nbsp;&nbsp;&nbsp; ยังไม่ได้สร้างหน้า... '$page' ";
	}
	?>
        </p>
        <b>&nbsp;&nbsp;&nbsp;&nbsp;* การลบข้อมูล ต้องใช้สิทธิ์ Admin ของสถานีเท่านั้น (ระดับสารวัตรขึ้นไป)</b>
	  </div>

    </td>
</tr>
</table>
	
		<p>&nbsp;</p>
	</div>	
</body>
</html>

<script language="javascript">
function Del(mypage){
	var agree = confirm("คุณต้องการยืนยันที่จะลบข้อมูลนี้หรือไม่");
	if(agree)
	{
		window.location=mypage;
	}
}
</script>

<script>
function go_warrant(pcid)
{	
	window.location = "../policeinnopolis/WatchmanData/Add_all_Warrant.php?page=warrant&pcid=" +pcid + "&rnd=" + Math.random();
}
</script>

<script>
function deleteItem1(id, pcid) {
    Swal.fire({
        title: 'คุณต้องการลบข้อมูลนี้ใช่ไหม?',
        text: "หลังจากลบแล้ว จะไม่สามารถกู้คืนได้",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'ใช่, ยืนยันลบ',
        cancelButtonText: 'ยกเลิก'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = '/WatchmanData/Del_Personal.php?id=' + id + '&pcid=' +pcid ;
        }
    });
}
</script>

<script>
document.addEventListener('keydown', function(event) {
    if (event.key === 'PrintScreen') {
        // บันทึกกิจกรรมการกด PrintScreen
        logActivity('Capture Screen');
    }
});

	
document.addEventListener('contextmenu', function(event) {
    // บันทึกกิจกรรมการคลิกขวา
    logActivity('Right Click');
    // ป้องกันไม่ให้แสดงเมนูคลิกขวา
    //event.preventDefault();
});



function logActivity(action) {
    var xhr = new XMLHttpRequest();
    xhr.open("POST", "../log_activity.php", true);
    xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
    xhr.send("action=" + encodeURIComponent(action));
}

</script>