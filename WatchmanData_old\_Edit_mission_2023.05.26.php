<?php
include '../Condb.php';
include '../users.inc.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);


$id = $_GET['id'];

$sql = "SELECT * FROM wm_tb_mission WHERE mi_cl_aid = '$id' ";
$result = mysqli_query($conn,$sql);
$row = mysqli_fetch_array($result);

?>


<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>แก้ไขข้อมูลภารกิจ/คำสั่ง/ข่าวสาร</title>
<script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/policeinnopolis/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 18px;
    }
input::placeholder {
  color: red;
}
.form-control::placeholder {
  color: sandybrown;
}
</style>
<!-- สำหรับ วันที่ แบบ พ.ศ. -->
<link rel="stylesheet" href="http://code.jquery.com/ui/1.9.1/themes/base/jquery-ui.css" />
<script src="http://code.jquery.com/jquery-1.8.2.js"></script>
<script src="/policeinnopolis/js/jquery-ui.js"></script>
</head>
    
<body>
	
	<div class="container">
	<div class="row">
	<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> แก้ไขข้อมูลภารกิจ/คำสั่ง/ข่าวสาร</div>
	<form action="Save_mission.php" method="POST" enctype="multipart/form-data" class="body">
	<span style="color: #1203F8">
        
        <label hidden="hidden">ลำดับ</label>
		<input name="mi_cl_aid" type = "text" class="form-control" value= "<?=$row['mi_cl_aid']?>" hidden="hidden"  >
		        
        <label>วันที่ต้องปฏิบัติ/วันที่ออกคำสั่ง/วันแจ้งข่าวสาร <span style="color: #F90004">* จำเป็น</span> </label>
        <p><input type="text" name="mi_cl_date" id="datepicker" value= "<?= $row['mi_cl_date']?>" placeholder="ระบุวันที่" class="form-control" autocomplete="off" required></p>
              <script>
            function set_cal(ele)//function สร้างตัวเลือกปฎิทิน
            {
              $( ele ).datepicker({
                  onSelect:(date_text)=>
                  {
                    let arr=date_text.split("/");
                    let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])+543).toString();
                    $(ele).val(new_date);
                    $(ele).css("color","");
                  },
                  beforeShow:()=>{

                    if($(ele).val()!="")
                    {
                      let arr=$(ele).val().split("/");
                      let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])-543).toString();
                      $(ele).val(new_date);

                    }

                    $(ele).css("color","white");
                  },
                  onClose:()=>{

                      $(ele).css("color","");

                      if($(ele).val()!="")
                      {
                          let arr=$(ele).val().split("-");
                          if(parseInt(arr[2])<2500)
                          {
                              let new_date=arr[0]+"-"+arr[1]+"-"+(parseInt(arr[2])).toString();
                              $(ele).val(new_date);
                          }
                      }
                  },
                  dateFormat:"yy-mm-dd", //กำหนดรูปแบบวันที่เป็น วัน/เดือน/ปี
                  changeMonth:true,//กำหนดให้เลือกเดือนได้
                  changeYear:true,//กำหนดให้เลือกปีได้
                  showOtherMonths:true,//กำหนดให้แสดงวันของเดือนก่อนหน้าได้
              });
            }
          $(document).ready(function(){
            //เรียก function set_cal เมื่อเปิดหน้าเว็บ โดยส่ง object element ที่มี id เป็น datepicker เป็นพารามิเตอร์
            set_cal( $("#datepicker") );
          })
          </script>
        <!-- สำหรับ วันที่ แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ -->
        
        <label>เวลา</label>
		<input type = "text" name = "mi_cl_time" value= "<?= $row['mi_cl_time']?>" class="form-control" placeholder="ระบุเวลา"   ><br>
        
        <label>หัวข้อ</label>
		<select id="mi_cl_mission" name="mi_cl_mission" class="form-select form-select-sm" value= "<?= $row['mi_cl_mission']?>" placeholder="ระบุภารกิจ/ข่าวสาร">
			<option value="" selected> </option>
            <option value="ภารกิจ">ภารกิจ</option>
            <option value="ข่าวสาร">ข่าวสาร</option>
            <option value="ประชุม">ประชุม</option>
            <option value="อื่น ๆ">อื่น ๆ</option>
		</select>
        
        <label>ภารกิจ/ข่าวสาร</label>
		<input type = "text" name = "mi_cl_mission_details" class="form-control" value= "<?= $row['mi_cl_mission_details']?>" placeholder="ระบุหัวข้อ หรือรายละเอียดภารกิจ"   >
        
        <label>ผู้ปฏิบัติ</label>
		<select id="mi_cl_worker" name="mi_cl_worker" class="form-select form-select-sm" placeholder="ระบุผู้ที่ต้องปฏิบัติ">
			<option value="" selected> </option>
            <option value="พ.ต.ท.ณัทรภณ ทรงไทย">พ.ต.ท.ณัทรภณ ทรงไทย</option>
            <option value="ว่าที่ พ.ต.ต.ณัฐวัฒน์ แคนหนอง">ว่าที่ พ.ต.ต.ณัฐวัฒน์ แคนหนอง</option>
            <option value="ร.ต.อ.ณัฐพงษ์ ภู่ทอง">ร.ต.อ.ณัฐพงษ์ ภู่ทอง</option>
            <option value="ร.ต.อ.วุฒิไกร สายทอง">ร.ต.อ.วุฒิไกร สายทอง</option>
            <option value="ร.ต.อ.พิทยา อ่วมเหล็ง">ร.ต.อ.พิทยา อ่วมเหล็ง</option>
            <option value="ร.ต.ท.ปัญญา ศรีวรเดช">ร.ต.ท.ปัญญา ศรีวรเดช</option>
            <option value="ร.ต.ต.ประชัน ทองดี">ร.ต.ต.ประชัน ทองดี</option>
			<option value="ด.ต.จักรพงศ์ สุริยะคำวงศ์">ด.ต.จักรพงศ์ สุริยะคำวงศ์</option>
			<option value="ด.ต.วีระ อินตาโสภี">ด.ต.วีระ อินตาโสภี</option>
            <option value="ส.ต.ต.นนทกานต์ ขังทัศน์">ส.ต.ต.นนทกานต์ ขังทัศน์</option>
            <option value="ทุกนาย">ทุกนาย</option>
		</select>
        <br>
		
		<label>ผู้บันทึก</label>
		<select id="mi_cl_record" name="mi_cl_record" class="form-select form-select-sm" placeholder="ระบุเจ้าหน้าที่สืบสวน/บันทึก">
			<option value="" selected> </option>
            <?php
                    $bsdp = mysqli_query($conn, "SELECT * FROM `police_name_bsdetective` order by `aid_bsdp` DESC");
                    while($row_bsdp = mysqli_fetch_array($bsdp))
                    {
                        echo "<option value='{$row_bsdp['aid_bsdp']}'>{$row_bsdp['bsdp_name']}</option>";
                    }
                ?>
		</select>
        <br>
        <div class="mb-3">
			<label for="formFileMultiple" class="form-label">ไฟล์แนบ</label>
			<input class="form-control" type="file" id="mi_cl_file" name="mi_cl_file" multiple>
		</div>
		
		<br>
		<p>
			<input type="submit" value="บันทึกข้อมูล" class="btn btn-success" >
			<td> <a href="index.php?rnd=<?= rand(); ?>&page=mission" class="btn btn-warning" >ยกเลิก</a> </td>
		</p>
		<br>
		<br>
	</form>
	</div>
	</div>
	</div>
<!--        <script src="/js/datetimepicker2.5.1/jquery.ui.datepicker-th.js" type="text/javascript"></script>-->

<script>
// ชื่อ >> value >> set selct auto (สคลิปตรวจสอบ Selected) จำเป็นต้องก็อปปี้ <script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js" type="text/javascript">
function auto_select(sid, value)
{
    $('#' + sid).val(value);
}
    
function auto_thaidate(sid, value)
{
	if(value != '') {
		var dates = value.split('-');
		dates = value.split('-');
		if(dates.length > 2) {
			var ys = dates[0];
			var ms = dates[1];
			var ds = dates[2];
			if(ys.substr(0,2) == '20') {
				ys = parseInt(ys) + 543;
			}
			value = $.digit(ds,2) + '/' + $.digit(ms,2) + '/' +ys;
			$('#' + sid).val(value);
		}
	}
}

$(document).ready(function() {
<?php
    echo "auto_select('mi_cl_mission', '{$row['mi_cl_mission']}');\n";
    echo "auto_select('mi_cl_worker', '{$row['mi_cl_worker']}');\n";
    echo "auto_select('mi_cl_record', '{$row['mi_cl_record']}');\n";
    // convert db date to thai dates
	//echo "auto_thaidate('mi_cl_date', '{$row['mi_cl_date']}');\n";
?>
    });
</script>
</body>
</html>
