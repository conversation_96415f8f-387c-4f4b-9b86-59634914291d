<script src="/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>
<script src="../SweetAlert2/dist/sweetalert2.all.min.js"></script>

<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';
include '../Head_image.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// Define the SQL query to retrieve the data
$query = "SELECT 
            COUNT(*) AS total_cases,
            SUM(ivc_cl_status_case = '3') AS total_status_3,
            SUM(ivc_cl_status_case = '4') AS total_status_4
          FROM wm_tb_investigating_case
          WHERE station = '$station'";
    $stmt = $pdo->prepare($query);
try{
    $stmt->execute();
}catch(PDOException $e) {
    echo '$query Failed: '. $e->getMessage();
}
    $row_to = $stmt->fetch(PDO::FETCH_ASSOC);

// Extract the relevant values from the results
$total = intval($row_to['total_cases']);
$total3 = intval($row_to['total_status_3']);
$total4 = intval($row_to['total_status_4']);
$total5 = $total3+$total4;
$total6 = $total-$total5;


 // เลขคดี
$sql_no = "SELECT * FROM wm_tb_investigating_case WHERE station='$station' AND `ivc_cl_no` ORDER BY `ivc_cl_no` DESC LIMIT 1";
    $stmt = $pdo->prepare($sql_no);
try{
    $stmt->execute();
}catch (PDOException $e) {
    echo '$sql_no Failed: '. $e->getMessage();
}
    $row_no = $stmt->fetch(PDO::FETCH_ASSOC);

if ($row_no !== false) {
    $running_no = $row_no['ivc_cl_no'];
} else {
  // Handle the case when no information is found
  $running_no = null; // or any default value you prefer
}

?>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <link rel="stylesheet" href="/bootstrap/css/bootstrap-grid.css">
        <base target="_top">
		<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
		<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
  		<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
 		<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js"></script>
<style>
    .swal2-popup {
    width: 500px !important;
    height: auto !important;
    font-size: 14px !important;
}
</style>
<div class="container-fluid">

<div>
	<?php
	include('../users_info.php');
	?>	
</div>
		<div class=" h3 text-center  alert alert-primary mb-4 mt-4 " role="alert" >ข้อมูลคดีที่อยู่ระหว่างการสืบสวนหาผู้กระทำผิด ที่ออกหมายจับแล้ว <?= $name_station ?></div>
		<div align="left"> &nbsp;<a href="Add_investigating_case.php" class="btn btn-success btn-lg mb-4 <?= $add_btn ?>" >เพิ่มข้อมูล</a>&nbsp;
        &nbsp;<a href="index.php?rnd=<?= rand(); ?>&page=investigating" class="btn btn-primary btn-lg mb-4" >ย้อนกลับ</a>&nbsp;
		</div>
		<div>
		<a style="background-color: yellow ;padding: 10px" align="left">จำนวนคดีที่อยู่ระหว่างการสืบสวน จำนวน : <span style="color: #FC0408"><b style="color: crimson; font-size: 24px">
            <?= $total ?> </b> คดี </a>
            <a style="background-color: yellow ;padding: 10px">ออกหมายจับแล้ว จำนวน : <span style="color: #FC0408"><b style="color: crimson; font-size: 24px">
            <?= $total3 ?> </b> คดี </a>
            <a style="background-color: yellow ;padding: 10px">สืบสวนเสร็จสิ้นแล้ว จำนวน : <span style="color: #FC0408"><b style="color: crimson; font-size: 24px">
            <?= $total4 ?> </b> คดี </a>
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <a align="right">เลขรับคดีสืบสวน (ล่าสุด)</a>&nbsp;&nbsp;<b style="color: crimson; font-size: 20px ; background-color:aqua ;padding: 10px"><?= $running_no ?></b>
		</div><br>
            
<table class="table table-striped table-hover table-bordered table-sm mb-4 mt-4 " >
      <tbody>
            <tr class="container-fluid ">
           <!--   <th nowrap="nowrap">ลำดับ</th>  -->
              <th>เลขรับคดีสืบสวน</th>
              <th>เลขคดีอาญา</th>
              <th>ผู้เสียหาย</th>
              <th>รายละเอียดโดยย่อ</th>
              <th>ค่าเสียหาย</th>
           <!--   <th>หน่วยรับผิดชอบ</th>   -->
              <th>พนักงานสอบสวน</th>
              <th>วันรับแจ้ง</th> 
              <th>สถานะ</th>
              <th>ผู้สืบสวน</th>
              <th>รายงานสืบสวน</th>
              <th colspan="3"></th>
            </tr>


<?php
$sql = "SELECT
            T1.*,
            T2.station_name as station,
            T3.ics_cl_status AS status " .		//เงื่อนไข การเลือกข้อมูล จาก 2 ตาราง
        "FROM
            wm_tb_investigating_case AS T1 " .
            "LEFT JOIN wm_tb_police_station2 AS T2 ON T1.station = T2.station_code " .
            "LEFT JOIN wm_tb_investigating_case_staus AS T3 ON T1.ivc_cl_status_case = T3.ics_cl_aid " .
        "WHERE
            T1.station='$station' AND T1.ivc_cl_no AND T1.ivc_cl_status_case='3' " .			//เพิ่มเงื่อนไข เลือกสถานี และสถานะมีผลใช้
        "ORDER BY
            T1.ivc_cl_aid ASC";  //ตั้งตัวแปร sql เลือกฐานข้อมูลจากตารางที่ชื่อ wm_tb_personal

    $stmt = $pdo->prepare($sql);
try{
    $stmt->execute();
}catch (PDOException $e) {
    echo '$sql Failed: '. $e->getMessage();
}

// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no = 1;
while($row_on = $stmt->fetch(PDO::FETCH_ASSOC)) {//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
    $pcid = $row_on['ivc_cl_no']; //เลขบัตร ผู้พ้นโทษ

    //ฟังก์ชั่น วันที่ ดึงจาก condb
$strDate = DateThai( $row_on['ivc_cl_date'] );

//ตรวจสอบรายงานสืบสวนก่อนว่า มีไฟล์ในตารางไหม หากมี ให้ส่งเป็นลิงค์
$doc = $row_on["ivc_cl_file"];
if($doc !== '')
{
    $link = '<a href="/' . $row_on["ivc_cl_file"] .'" target="_blank">Download</a>';
}
else {
    $link = ' ';
}
?>                                                                  

            <tr class="container-fluid">
             <!-- <td>&nbsp; <?= $no ?></td>    -->
          <!--    <td><?= $row_on['ivc_cl_aid'] ?></td>  -->
              <td><?= $row_on['ivc_cl_no'] ?></td>
              <td><?= $row_on['ivc_cl_crimes_no'] ?></td>
              <td><?= $row_on['ivc_cl_name_sufferer'] ?></td>
              <td><?= $row_on['ivc_cl_detail'] ?></td> 
              <td><?= $row_on['ivc_cl_damages'] ?></td> 
          <!--    <td><?= $row_on['station'] ?></td>    -->
              <td><?= $row_on['ivc_cl_inquiry_official'] ?></td>
              <td nowrap><?= $strDate ?></td>
              <td><?= $row_on['status'] ?></td>
              <td><?= $row_on['ivc_cl_detective'] ?></td>
              <td><?= $link ?></td>	
                
              <td><a href="show_detail_investigating_case.php?id=<?= $row_on['ivc_cl_aid'] ?>&pcid=<?= $pcid ?>" class="btn btn-success mb-4" >รายละเอียด</a></td>
              <td><a href="Edit_investigating_case.php?id=<?= $row_on['ivc_cl_aid'] ?>&pcid=<?= $pcid ?>" class="btn btn-warning mb-4 <?= $edit_btn ?>" >แก้ไข</a></td>
              <td><button href="#" class="btn btn-danger mb-4" onClick="deleteItem(<?= $row_on["ivc_cl_aid"] ?>)"<?=$del_btn?>>ลบ</button></td>
            </tr>
                <?php
                    $no++;
                }//while
                ?>
      </tbody>
    
</table>
</div>
<script>
function deleteItem(id) {
    Swal.fire({
        title: 'คุณต้องการลบข้อมูลนี้ใช่ไหม?',
        text: "หลังจากลบแล้ว จะไม่สามารถกู้คืนได้",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'ใช่, ยืนยันลบ',
        cancelButtonText: 'ยกเลิก'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = '/Bansuan/Del_investigating_case.php?id=' + id;
        }
    });
}
</script>