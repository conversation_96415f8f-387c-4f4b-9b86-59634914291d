<?php
include '../Condb.php';
include '../users.inc.php';
include '../right_user.php';
include '../Head_image.php';
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// เมื่อมีการใช้ ตัวแปร Session จะต้องเปิดก่อน
//session_start();  // เปิดตัวแปร session
/*if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
*/

// crime type
//$crime = isset($_GET['crime']) ? $_GET['crime'] : '0';
$crime4 = isset($_GET['crime']) ? $_GET['crime'] : '0';
// ค้นข้อมูล
$search = isset($_GET['search']) ? $_GET['search'] : '';

// เรียงข้อมูล
$sort_by_no = isset($_GET['n']) ? $_GET['n'] : -1;


if($search != '') {                              // ถ้าคำค้นเป็นว่าง    
    $words = explode(',', $search); // 3 ข้อความ
    $s_cid = $words[0];                           // เลขบัตร ตรงกับคำค้น เป็นตำแหน่งที่ 0
    $s_name = $words[1];                          // ชื่อ ตรงกับคำค้น เป็นตำแหน่งที่ 1
    $s_surname = $words[2];   
    $s_nickname = $words[3];
}
else {
    $s_cid = "";
    $s_name = "";
    $s_surname = "";
    $s_nickname = "";
}

$options = "";
if($search != '') {                              // ถ้าคำค้นเป็นว่าง
    $options = "";

    if($s_cid != "") {
        $option1 = "PS.ps_cl_idcard = '{$s_cid}' ";      
        $options = "OR " . $option1;
    }    
    if($s_name != "") {
        $option2 = "PS.ps_cl_name LIKE '%{$s_name}%' ";
        $options .= "OR " . $option2;
    }
    if($s_surname != "") {
        $option3 = "PS.ps_cl_surname LIKE '%{$s_surname}%' ";
        $options .= "OR " . $option3;
    }
    if($s_nickname != "") {
        $option4 = "PS.ps_cl_nickname LIKE '%{$s_nickname}%' ";
        $options .= "OR " . $option4;
    }
    //
    if(substr($options,0,2) == "OR") {
        $options = trim(substr($options, 3));    
    }    
}


// ------------------
// list by crim type 4
// ------------------
if($crime4 = "9")
{
    // search options
    if($options != "") {
        $options = " AND $options ";
    }
        
    $sql = "SELECT * FROM wm_tb_crimes_history AS CH " .
           "LEFT JOIN wm_tb_personal AS PS ON CH.ch_cl_idcard = PS.ps_cl_idcard " .
           "WHERE CH.station='6707' AND CH.`ch_cl_Tumbon` IN ('640102','640107','640108') AND CH.ch_cl_crimes_type2='{$crime4}' {$options} " .
           "GROUP BY CH.ch_cl_idcard ";
    
    //	sort by
    if($sort_by_no == 0){
        $sql .= " ORDER BY CH.ch_cl_aid ASC ";
    }
    elseif($sort_by_no == 1) {
        $sql .= " ORDER BY CH.ch_cl_aid DESC ";
    }
}
// ------------------

// no crime type + no search word
// ------------------
else {
    
    $station = $user['police_station'];
    
    if($options != "") 
    {
        
        
        $sql = "SELECT PS.* FROM wm_tb_personal AS PS WHERE $options ";
    }
    else {    
        $sql = "SELECT PS.* FROM wm_tb_personal AS PS WHERE PS.ps_cl_police_station='$station' ";
    }
    
    //	
    if($sort_by_no == 0){
        $sql .= " ORDER BY PS.ps_cl_aid ASC ";
    }
    elseif($sort_by_no == 1) {
        $sql .= " ORDER BY PS.ps_cl_aid DESC ";
    }
}

$resultCr = mysqli_query($conn, $sql);
$total = mysqli_num_rows($resultCr);

?>

<!DOCTYPE html>
<html>
    <head>
        <title>ระบบ WatchmanDB</title>
		<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
        <base target="_top">
		<link rel="stylesheet" href="../bootstrap/css/bootstrap.rtl.min.css">
		<link rel="stylesheet" href="../bootstrap-3.4.1-dist/css/bootstrap.min.css">
  		<script src="../jQuery/jquery-3.6.1.min.js"></script>
 		<script src="../bootstrap-3.4.1-dist/js/bootstrap.min.js"></script>
        <style>
            body {
              font-family: 'Courier New', Courier, monospace;
              font-size: 16px;
            }
        </style>
        <!--Add CSS styles to position and style the enlarged image:-->
        <style>
          .enlarged-image {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
          }

          .enlarged-image img {
            max-width: 90%;
            max-height: 90%;
          }
        </style>
    </head>
    <body class="container-fluid">

		<div>
            <?php
			include('../users_info.php');
			?>	
		</div>
<div class="container-fluid" align="left">
		<div class=" h3 text-center  alert alert-danger mb-4 mt-4 " role="alert" >บัญชีผู้ที่เกี่ยวข้องกับยาเสพติด สภ.บ้านสวน</div>
		<div align="left">
            &nbsp;<a href="Add_Personal_for_bansuan.php" class="btn btn-success btn-lg mb-4" >เพิ่มข้อมูล</a>&nbsp;&nbsp;
            &nbsp;<a href="Show_data_area.php?rnd=<?= rand(); ?>&page=information" class="btn btn-primary btn-lg mb-4" >ย้อนกลับ</a>&nbsp;&nbsp;
            &nbsp;<a style="background-color: yellow ; padding: 25px">จำนวนผู้มีประวัติเสพยาเสพติด สภ.บ้านสวน : <span style="font-size: 20px; color: #FC0408"><b style="color: crimson; font-size: 26px"> <?= $total ?> </b> ราย </a>
		</div>

<?php /*?>    <div class="col-3" >
    <label style="font-size: 18px">ประเภทความผิด</label>
        <select id="tbCrimeType" name="tbCrimeType" class="form-select form-select-sm" style="font-size: 18px ;background-color: #E2C6E2; border-color: blueviolet" onChange="on_crime_change()">
            <option value="0">เลือกประเภทความผิด</option>
            <?php
                $res3 = mysqli_query($conn, "SELECT * FROM tbCrimeType order by ctID ASC");
                while($row3 = mysqli_fetch_array($res3))
                {
                    if($crime == $row3['ctID']) {
                        $selected = "selected";
                    }
                    else {
                        $selected = "";
                    }

                    echo "<option value='{$row3['ctID']}' {$selected}> {$row3['ctName']} </option>";
                }
            ?>
        </select>
    </div><?php */?>
            
    
    <div class="col-3" >
    <label style="font-size: 18px">ความผิด ตามแบบ ศขส. 23 ประเภท</label>
        <select id="tbCrimeType2" name="tbCrimeType2" class="form-select form-select-sm" style="font-size: 18px ;background-color: #E2C6E2; border-color: blueviolet" onChange="on_crime_change()">
            <option value="0">เลือก</option>
            <?php
                $res4 = mysqli_query($conn, "SELECT * FROM wm_tb_crimes_type order by cm_cl_aid ASC");
                while($row4 = mysqli_fetch_array($res4))
                {
                    if($crime4 == $row4['cm_cl_aid']) {
                        $selected = "selected";
                    }
                    else {
                        $selected = "";
                    }

                    echo "<option value='{$row4['cm_cl_aid']}' {$selected}> {$row4['cm_cl_name']} </option>";
                }
            ?>
        </select>
    </div>
	<!--	 ปุ่ม search  -->
<table width="90%" border="0" cellspacing="5" cellpadding="1" style="flex: auto">
  <tbody>
    <tr align="center" >
      <td width="25% " ><span class="form-group col-md-3; align-items-baseline ">
        <input type="text" placeholder="สืบค้นจาก เลขบัตร" name="searchIdcard" id="searchIdcard" class="form-control" align="right" value="<?= $s_cid ?>" style="background-color: #E8F6F6">
      </span></td>
      <td width="25%">
        <span class="form-group col-md-3; align-items-baseline" >
        <input name="searchName" id="searchName" type="text" class="form-control" placeholder="หรือ สืบค้นจาก ชื่อ" align="right" value="<?= $s_name ?>" style="background-color: #E8F6F6">
      </span> </td>
      <td width="25%"><span class="form-group col-md-3; align-items-baseline">
        <input type="text" placeholder="หรือ สืบค้นจาก นามสกุล" name="searchSurname" id="searchSurname" class="form-control" align="right" value="<?= $s_surname ?>" style="background-color: #E8F6F6">
      </span> </td>
      <td width="20%"><span class="form-group col-md-3; align-items-baseline">
        <input type="text" placeholder="หรือ สืบค้นจาก ชื่อเล่น" name="searchNickname" id="searchNickname" class="form-control" align="right" value="<?= $s_nickname ?>" style="background-color: #E8F6F6">
      </span> </td>
      <td width="13%"><span class="form-group col-md-3"><span class="input-group-btn">
        <a><button name="submit" type="submit" id="submit" value="search" class="btn btn-warning my-4"  align="right" onClick="do_search()"><span class="glyphicon glyphicon-search"></span>ค้นหา</button></a>
      </span></span>
    </tr>
  </tbody>
</table>
			
<div class="container">
<a style="color: green">(คลิกที่ "ลำดับ" เพื่อเรียงข้อมูลจากล่างขึ้นบน)</a>
<table class="table table-striped table-hover mb-4 mt-4" >
  <tbody>
    <tr class="container-fluid">
      	<th onClick="sort_by_order(<?= $sort_by_no ?>)" style="cursor:  pointer; color: green"> ลำดับ </th>
	<!--	<th> เลขบัตรประชาชน </th> -->
		<th> คำนำหน้า </th>
		<th onClick="sort_by_name(<?= $sort_by_name ?>)" style="cursor:  pointer;"> ชื่อ </th>
		<th> นามสกุล </th>
		<th> ชื่อเล่น </th>
	<!--	<th> วันเกิด </th>        -->
		<th> อายุ </th>
	<!--	<th> บิดา </th>   -->
	<!--	<th> มารดา </th>	-->
		<th> รูปภาพ</th>	
		<th></th>
    </tr>
	  

<?php

			
// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no = 1;
while($rowCr = mysqli_fetch_array($resultCr))	{//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
	$pcid = $rowCr['ps_cl_idcard']; //เลขบัตร ผู้พ้นโทษ
    
    // คำนวณอายุอัตโนมัติ 
    //$age = get_personal_age($row["7ps_cl_birthday"]); // thai
    $age = get_personal_age_2( $rowCr["ps_cl_birthday2"] ); // eng
    
?>
	  
    <tr class="container-fluid">
      <td> <?= $no ?> </td>
	<!--	<td> <?=$rowCr["ps_cl_idcard"]?> </td>    -->
		<td> <?=$rowCr["ps_cl_prefix"]?> </td>
		<td> <?=$rowCr["ps_cl_name"]?> </td>
		<td> <?=$rowCr["ps_cl_surname"]?> </td>
		<td> <?=$rowCr["ps_cl_nickname"]?> </td>
	<!--	<td> <?=$rowCr["ps_cl_birthday"]?> </td>  -->
		<td> <?= $age ?> ปี</td>                    <!-- คำนวณอายุอัตโนมัติ -->
	<!--	<td> <?=$rowCr["ps_cl_father"]?> </td>		-->
	<!--	<td> <?=$rowCr["ps_cl_mother"]?> </td>	-->
		<td> <img class="enlarge-image" src="<?= $rowCr["ps_cl_image"] ?>" height="50"> </td>
		<td> <a href="../WatchmanData/Show_All.php?pcid=<?= $rowCr["ps_cl_idcard"] ?>" class="btn btn-success mb-4" >ข้อมูล</a> </td>
    </tr>
<?php
	$no++;
}//while
?>
  </tbody>
</table>
</div>
    </div>
</body>

<script>
function go_detail(pcid)	
{
	window.location = "/WatchmanData/Show_All.php?pcid=" +pcid + "&rnd=" + "&page=freed";
}
    
// ฟังก์ชั่น ค้นหาข้อมูล
function do_search()
{
    var select_id = $('#tbCrimeType2 option:selected').val();
    var cid = $('#searchIdcard').val();
    var name = $('#searchName').val();
    var surname = $('#searchSurname').val();
    var nickname = $('#searchNickname').val();
    //
    var url = "/Bansuan/Show_crimes_person_drug.php?&crime=" + select_id + "&search="+ cid + ',' + name + ',' + surname + ',' + nickname;
    window.location = url;
}

    
/* function type_change()  // คำสั่งเปลี่ยนเดือน ปี
{
    var ms = $("#mi_cl_month option:selected").val();
    var ys = $("#mi_cl_year option:selected").val();
    window.location = "/Bansuan/index.php?page=mission&month=" + ms + "&year=" + ys + "&rnd=" + Math.random();
} */
    
function sort_by_order(sort_type)
{
	if(sort_type == 1) {
		sort_type = 0;
	}
	else {
		sort_type = 1;
	}
	window.location ="Show_crimes_person_drug.php?rnd=<?= rand(); ?>&n=" + sort_type + "&search=<?= $search ?>";
}
    /*
function sort_by_name(sort_type)
{
	if(sort_type == 1) {
		sort_type = 0;
	}
	else {
		sort_type = 1;
	}
	window.location ="index.php?rnd=<?= rand(); ?>&page=บุคคลอาชญากรรม&n=" + sort_type ;
}
*/
    
    // ฟังก์ชั่น เลือกประเภทอาชญากรรม (ยังทำไม่เสร็จ)
function on_crime_change()
{
    var select_id = $('#tbCrimeType2 option:selected').val();
    window.location ="Show_crimes_person_drug.php?rnd=<?= rand(); ?>&crime=" + select_id;
}
</script>
	
<!--Add a JavaScript code to handle the click event:-->

<script>
  // Get all the images with class "enlarge-image"
  const images = document.querySelectorAll(".enlarge-image");

  // Attach a click event listener to each image
  images.forEach(image => {
    image.addEventListener("click", event => {
      // Create a new <div> element to display the enlarged image
      const enlargedImage = document.createElement("div");
      enlargedImage.classList.add("enlarged-image");

      // Create a new <img> element and set its src attribute to the clicked image's src
      const img = document.createElement("img");
      img.src = event.target.src;

      // Append the <img> element to the <div> element
      enlargedImage.appendChild(img);

      // Add the <div> element to the body
      document.body.appendChild(enlargedImage);

      // Attach a click event listener to the <div> element to remove it when clicked
      enlargedImage.addEventListener("click", event => {
        enlargedImage.remove();
      });
    });
  });
</script>

