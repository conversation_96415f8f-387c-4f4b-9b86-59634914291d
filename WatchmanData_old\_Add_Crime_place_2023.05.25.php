<?php
include '../Condb.php';
include '../users.inc.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// สำหรับเลือก จังหวัด อำเภอ ตำบล
include("../config.inc.php");
include("../classes/class.database.inc.php");

// สำหรับเลือก จังหวัด อำเภอ ตำบล
$conn2 = get_connection(); // connect to mySQLDB

// สำหรับเลือกสถานีตำรวจทั่วประเทศ
$region = isset($_GET['region']) ? $_GET['region'] : 0; // sukhothai
$provincial = isset($_GET['provincial']) ? $_GET['provincial'] : 0; // sukhothai
$station = isset($_GET['station']) ? $_GET['station'] : 0; // sukhothai

?>


<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Add Data Crimes Place</title>
<script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/policeinnopolis/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 18px;
    }
input::placeholder {
  color: red;
}
.form-control::placeholder {
  color: sandybrown;
}
</style>
<!-- สำหรับ วันเกิด แบบ พ.ศ. -->
<link rel="stylesheet" href="http://code.jquery.com/ui/1.9.1/themes/base/jquery-ui.css" />
<script src="http://code.jquery.com/jquery-1.8.2.js"></script>
<script src="/policeinnopolis/js/jquery-ui.js"></script>
</head>

<body>
	
	<div class="container">
	<div class="row">
	<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-danger mb-4 mt-4 " role="alert"> เพิ่มข้อมูลสถานที่เกี่ยวข้องกับอาชญากรรม </div>
	<form action="Save_Crimes_place.php" method="POST" enctype="multipart/form-data" class="body">
        
        <label>ประเภทสถานที่ <span style="color: #F90004">* จำเป็น</span></label>
                <select id="gc_cl_place" name="gc_cl_place" class="form-select form-select-sm" aria-label=".form-select-sm example" Required>
                    <option value="" selected> </option>  <!-- ดึงข้อมูลมาจากตารางใหม่ -->
                    <?php
                        $res1 = mysqli_query($conn, "SELECT * FROM `wm_tb_place_type` WHERE `pt_cl_aid`=2");
                        while($row1 = mysqli_fetch_array($res1))
                        {
                            echo "<option value='{$row1['pt_cl_aid']}'>{$row1['pt_cl_type']}</option>";
                        }
                    ?>
                </select>
        
        <label>สถานที่อาชญากรรม 16 ประเภท <span style="color: #F90004">* จำเป็น</span></label>
                <select id="gc_cl_crimes_place_type" name="gc_cl_crimes_place_type" class="form-select form-select-sm" aria-label=".form-select-sm example" Required>
                    <option value="" selected> </option>  <!-- ดึงข้อมูลมาจากตารางใหม่ -->
                    <?php
                        $res2 = mysqli_query($conn, "SELECT * FROM wm_tb_place_crimes order by cr_cl_aid ASC");
                        while($row2 = mysqli_fetch_array($res2))
                        {
                            echo "<option value='{$row2['cr_cl_aid']}'>{$row2['cr_cl_crimes_place']}</option>";
                        }
                    ?>
                </select>
        
        <label>ชื่อสถานที่</label>
        <input type = "text" name = "gc_cl_name" class="form-control" placeholder="ระบุชื่อสถานที่"  >
        
        <label>ที่อยู่</label>
        <input type = "text" name = "gc_cl_place_address" class="form-control" placeholder="ระบุที่อยู่" >
        
        <label>โทรศัพท์</label>
        <input type = "text" name = "gc_cl_place_phone" class="form-control" placeholder="ระบุหมายเลขโทรศัพท์" >
        
        <label>ชื่อเจ้าของ</label>
        <input type = "text" name = "gc_cl_owner_name" class="form-control" placeholder="ระบุชื่อเจ้าของ" >
        
        <label>เลขบัตรเจ้าของ</label>
        <input type = "text" name = "gc_cl_owner_idcard" class="form-control" placeholder="เลขบัตรประชาชน เจ้าของ" >
        
        <label>ชื่อผู้จัดการ/ผู้ดูแล</label>
        <input type = "text" name = "gc_cl_mgr_name" class="form-control" placeholder="ระบุชื่อผู้จัดการ/ผู้ดูแล" >
        
        <label>เลขบัตรผู้จัดการ/ผู้ดูแล</label>
        <input type = "text" name = "gc_cl_mgr_idcard" class="form-control" placeholder="เลขบัตรประชาชน ผู้จัดการ/ผู้ดูแล" >
        
        <label>ลักษณะทั่วไปของสถานที่</label>
        <input type = "text" name = "gc_cl_general_charac" class="form-control" placeholder="ลักษณะทั้วไปของสถานที่" >
        
        <label>กิจกรรมของสถานที่</label>
        <input type = "text" name = "gc_cl_activity" class="form-control" placeholder="กิจกรรมของสถานที่" >
        
        <label>ข้อมูลสำคัญที่ควรรู้</label>
        <input type = "text" name = "gc_cl_important_info" class="form-control" placeholder="ข้อมูลสำคัญที่ควรรู้" >
        
        <label>ประวัติที่เกี่ยวข้องอาชญากรรม</label>
        <input type = "text" name = "gc_cl_crimes" class="form-control" placeholder="ประวัติที่เกี่ยวข้องอาชญากรรม" >

        <label>ผู้บันทึกข้อมูล</label>
        <select id="gc_cl_recorder" name="gc_cl_recorder" class="form-select form-select-sm" placeholder="ระบุเจ้าหน้าที่บันทึก">
		<option value="" selected> </option>
            <?php
                    $bsdp = mysqli_query($conn, "SELECT * FROM `police_name_bsdetective` order by `aid_bsdp` DESC");
                    while($row_bsdp = mysqli_fetch_array($bsdp))
                    {
                        echo "<option value='{$row_bsdp['aid_bsdp']}'>{$row_bsdp['bsdp_name']}</option>";
                    }
                ?>
		</select>
        
        <label>วันที่บันทึก <span style="color: #F90004">* จำเป็น</span></label>
        <p><input type="text" name="gc_cl_date_record" id="datepicker" class="form-control" autocomplete="off"  Required></p>
        <script>
            function set_cal(ele)//function สร้างตัวเลือกปฎิทิน
            {
              $( ele ).datepicker({
                  onSelect:(date_text)=>
                  {
                    let arr=date_text.split("/");
                    let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])+543).toString();
                    $(ele).val(new_date);
                    $(ele).css("color","");
                  },
                  beforeShow:()=>{

                    if($(ele).val()!="")
                    {
                      let arr=$(ele).val().split("/");
                      let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])-543).toString();
                      $(ele).val(new_date);

                    }

                    $(ele).css("color","white");
                  },
                  onClose:()=>{

                      $(ele).css("color","");

                      if($(ele).val()!="")
                      {
                          let arr=$(ele).val().split("-");
                          if(parseInt(arr[2])<2500)
                          {
                              let new_date=arr[0]+"-"+arr[1]+"-"+(parseInt(arr[2])).toString();
                              $(ele).val(new_date);
                          }
                      }
                  },
                  dateFormat:"yy-mm-dd", //กำหนดรูปแบบวันที่เป็น วัน/เดือน/ปี
                  changeMonth:true,//กำหนดให้เลือกเดือนได้
                  changeYear:true,//กำหนดให้เลือกปีได้
                  showOtherMonths:true,//กำหนดให้แสดงวันของเดือนก่อนหน้าได้
              });
            }
          $(document).ready(function(){
            //เรียก function set_cal เมื่อเปิดหน้าเว็บ โดยส่ง object element ที่มี id เป็น datepicker เป็นพารามิเตอร์
            set_cal( $("#datepicker") );
          })
          </script>
        <!-- สำหรับ วันเกิด แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ -->
        
       <label>สถานีตำรวจ <span style="color: #F90004">* จำเป็น</span></label><br>
                <!-- เลือกสถานีตำรวจ -->
        <label> บช. </label><br>
        <select name="region" id="region" onChange="do_region_change()" class="form-control" Required >
                <option value="">&lt;&lt; เลือก บช. &gt;&gt;</option>
                <?php
			  	$res_re = $conn2->query("SELECT * FROM `wm_tb_police_region` 
                                            ORDER BY `name_region` ASC");
			     $selected = '';
			     while($row_re = $conn2->fetch_row($res_re)) 
                     {
                      $code_region = $row_re['code_region'];
                      $name_region = $row_re['name_region'];
                      // set default provionce to 64 >> sukhothai
                          if($code_region == $region) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$code_region' $selected> $name_region </option>\n";
                     }
			  
			  ?>
              </select>
        
        <label> บก. </label><br>
        <select name="provincial" id="provincial" onChange="do_provincial_change()" class="form-control" >
        <option value="0">&lt;&lt; เลือก บก. &gt;&gt;</option>
                <?php
                if($region > 0)
                {
                    $res_prov = $conn2->query("SELECT * FROM `wm_tb_police_provincial` 
                                                WHERE `region_code`='$region' 
                                                ORDER BY `provincial_code` ASC");
                    $selected = '';
                  while($res_prov = $conn2->fetch_row($res_prov)) 
                  {
                      $provincial_code = $res_prov['provincial_code']; // 
                      $provincial = $res_prov['provincial'];
                      // set default provionce to 64 >> sukhothai
                          if($provincial_code == $provincial) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$provincial_code' $selected> $provincial </option>\n";
                  }
                }	
                ?>
              </select>

         <label> สน./สภ. </label>
        <br>
        <select name="station" id="station" class="form-control" >
                <option value="0">&lt;&lt; เลือก สน./สภ. &gt;&gt;</option>
                <?php
	           if($provincial > 0)
                {
                    $res_st = $conn2->query("SELECT * FROM `wm_tb_police_station2` 
                                                WHERE provincial_code='$provincial_code'
                                                ORDER BY `station_code` ASC");
                    $selected = '';
                  while($row_st = $conn2->fetch_row($res_st)) 
                  {
                      $station_code = $row_st['station_code']; // 
                      $station_name = $row_st['station_name'];
                      // set default provionce to 64 >> sukhothai
                          if($station_code == $station) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$station_code' $selected> $station_name </option>\n";
                  }
                }	
                ?>                                                                     
              </select>
		<br>
        
        <label>ละติจูด<span style="color: #F90004">* จำเป็น</span></label>
        <input type = "text" name = "gc_cl_place_lat" class="form-control" placeholder="ละติจูด หากไม่มีให้ระบุเป็น 0" Required>
        
        <label>ลองจิจูด<span style="color: #F90004">* จำเป็น</span></label>
        <input type = "text" name = "gc_cl_place_lon" class="form-control" placeholder="ลองจิจูด หากไม่มีให้ระบุเป็น 0" Required>

        <div class="mb-3">
            <label for="formFileMultiple" class="form-label">รูปภาพสถานที่</label>
             <input class="form-control" type="file" id="gc_cl_place_image" name="gc_cl_place_image" multiple>
        </div>
        <p>
            <input type="submit" value="บันทึกข้อมูล" class="btn btn-success" >
            <td> <a href="Show_Crimes_place.php" class="btn btn-warning" >ยกเลิก</a> </td>
        </p>
            <br>
            <br>
        </form>
        </div>
        </div>
        </div>
<script>
// ฟังก์ชั่น เปลี่ยนภาค (บช.) เป็น ภ.จว. (บก.)
function do_region_change()
{
	var sel_region = document.getElementById("region");		
	var code_region = sel_region.options[sel_region.selectedIndex].value;

	$.get("../ajax/get_provincial.php?region=" + code_region, 
		  function(data, status){
				//alert("Data: " + data + "\nStatus: " + status);
				// jQuery style						
				$('#provincial').find('option').remove().end();
				var data = $.trim(data);
				if(data == '') {
					alert("ไม่พบข้อมูล บก. !");
					return;
				}

					//.append('<option value="whatever">text</option>').val('whatever');
				var datas = data.split(';');
				for(var i=0; i<datas.length; i++) {
					if(datas[i] == '') continue;
					datas[i] = datas[i].split(':');
					var code_region = datas[i][0];
					var name_region = datas[i][1];
					$('#provincial').append('<option value="'+ code_region+'">' + name_region + '</option>');
				}
                // Trigger the change event on the amphur dropdown so the tambon dropdown will be populated     
                $('#provincial').trigger('change');
		});
}
    //เลือก สถานี จากจังหวัด
function do_provincial_change()
{
	var sel_provincial = document.getElementById("region");		
	var provincial_code = sel_provincial.options[sel_provincial.selectedIndex].value;
    
    var sel_provincial2 = document.getElementById("provincial");		
	var station_code = sel_provincial2.options[sel_provincial2.selectedIndex].value;

	$.get("../ajax/get_station.php?region=" + provincial_code + "&provincial=" + station_code, 
			  function(data, status){
					//alert("Data: " + data + "\nStatus: " + status);
					// jQuery style						
					$('#station').find('option').remove().end();
					var data = $.trim(data);
					if(data == '') {
						alert("ไม่พบข้อมูลสถานี !");
						return;
					}

						//.append('<option value="whatever">text</option>').val('whatever');
					var datas = data.split(';');
					for(var i=0; i<datas.length; i++) {
						if(datas[i] == '') continue;
						datas[i] = datas[i].split(':');
						var station_code = datas[i][0];
						var station_name = datas[i][1];
						$('#station').append('<option value="'+ station_code+'">' + station_name + '</option>');
					}
			});
}
</script>
<!--<script src="/js/datetimepicker2.5.1/jquery.ui.datepicker-th.js" type="text/javascript"></script>--> 
</body>
</html>