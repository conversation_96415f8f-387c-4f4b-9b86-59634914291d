<?php   //PDO
include '../Condb.php';
include '../users.inc.php';
include '../right_user.php';
include '../Head_image.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// สำหรับเลือก จังหวัด อำเภอ ตำบล /และ สถานีตำรวจ
include("../config.inc.php");
include("../classes/class.database.inc.php");
$conn2 = get_connection(); // connect to mySQLDB

$province = isset($_GET['province']) ? $_GET['province'] : 64; // sukhothai
$amphure = isset($_GET['amphure']) ? $_GET['amphure'] : 0;
$tambon = isset($_GET['tambon']) ? $_GET['tambon'] : 0;

// สำหรับเลือกสถานีตำรวจทั่วประเทศ
$region = isset($_GET['region']) ? $_GET['region'] : 0;
$provincial = isset($_GET['provincial']) ? $_GET['provincial'] : 0;
//$station = isset($_GET['station']) ? $_GET['station'] : 0;

?>

<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CCTV to Google Maps</title>
	<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
	<script src="/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>
</head>
<body>
    <form action="insert_location.php" method="POST" enctype="multipart/form-data" >
		<div class="container">
		<div class="form-control">
			<label hidden ="hidden">ลำดับ</label>
			<input type = "text" name = "id" class="form-control"  hidden ="hidden" >
			<h2> พอตจุดติดตั้งกล้องวงจรปิด (CCTV) ลงแผนที่ </h2>
			<label for="location_name">CCTV Location Name :</label>
			<input type="text" id="location_name" name="location_name" required> <br>
			<br>
			<label for="latitude">Latitude:</label>
			<input type="text" id="latitude" name="latitude" required> <br>
			<br>
			<label for="longitude">Longitude:</label>
			<input type="text" id="longitude" name="longitude" required> <br>
			<br>
			<b>ที่อยู่ติดตั้ง CCTV</b><br>
			<!-- เลือกจังวัด อำเภอ ตำบล อัตโนมัติ -->
			<label> จังหวัด </label><br>
			<select name="cctv_province" id="cctv_province" onChange="do_province_change()" class="form-control" >
				<option value="0">&lt;&lt; เลือกจังหวัด &gt;&gt;</option>
					<?php
					$res_p = $conn2->query("SELECT * FROM `provinces` ORDER BY `provinces`.`pv_name_th` ASC");
					 $selected = '';
					 while($row_p = $conn2->fetch_row($res_p)) 
					 {
					  $pv_code = $row_p['pv_code'];
					  $pv_name = $row_p['pv_name_th'];
					  // set default provionce to 64 >> sukhothai
					  if($pv_code == $province) {
						  $selected = 'selected';
					  }
					  else {
						  $selected = '';
					  }
					  //
					  echo "<option value='$pv_code' $selected> $pv_name </option>\n";
					 }

				  ?>
				  </select>

			<label> อำเภอ </label><br>
			<select name="cctv_amphur" id="cctv_amphur" onChange="do_amphure_change()" class="form-control" >
			<option value="0">&lt;&lt; เลือกอำเภอ &gt;&gt;</option>
				<?php
				if($province > 0)
				{
					$res_a = $conn2->query("SELECT * FROM `amphures` WHERE province_code='$province' ORDER BY `amphures`.`ap_name_th` ASC");
					$selected = '';
				  while($row_a = $conn2->fetch_row($res_a)) 
				  {
					  $ap_code = $row_a['ap_code']; // 
					  $ap_name = $row_a['ap_name_th'];
					  // set default provionce to 64 >> sukhothai
					  if($ap_code == $amphure) {
						  $selected = 'selected';
					  }
					  else {
						  $selected = '';
					  }
					  //
					  echo "<option value='$ap_code' $selected> $ap_name </option>\n";
				  }
				}	
				?>
			  </select>

			 <label> ตำบล </label>
			<br>
			<select name="cctv_tumbon" id="cctv_tumbon" class="form-control" >
					<option value="0">&lt;&lt; เลือกตำบล &gt;&gt;</option>
					<?php
				   if($amphure > 0)
					{
						$res_t = $conn2->query("SELECT * FROM `districts` WHERE amphure_code='$amphure' ORDER BY `districts`.`tb_name_th` ASC");
						$selected = '';
					  while($row_t = $conn2->fetch_row($res_t)) 
					  {
						  $tb_code = $row_t['tb_id']; // 
						  $tb_name = $row_t['tb_name_th'];
						  // set default provionce to 64 >> sukhothai
						  if($tb_code == $tambon) {
							  $selected = 'selected';
						  }
						  else {
							  $selected = '';
						  }
						  //
						  echo "<option value='$tb_code' $selected> $tb_name </option>\n";
					  }
					}	
					?>                                                                     
				  </select>

			<label> หมู่ที่ </label><br>
			<input type = "text" name = "cctv_moo" class="form-control" placeholder="ระบุหมู่ที่" >

			<label>เลขที่</label>
			<input type = "text" name = "cctv_adr" class="form-control" placeholder="เลขที่" >
			
			<label>สถานะกล้อง/รายละเอียดอื่นๆ</label>
			<input type = "text" name = "cctv_detail" class="form-control" placeholder="สถานะกล้อง/รายละเอียดอื่นๆ" >
			
			<label>ผู้ดูแล</label>
			<input type = "text" name = "cctv_admin" class="form-control" placeholder="ผู้ดูแล" >
			
			<label>เบอร์โทรติดต่อ</label>
			<input type = "text" name = "cctv_contact" class="form-control" placeholder="เบอร์โทรติดต่อ" >
			
			<label>สถานีตำรวจ <span style="color: #F90004">* จำเป็น</span></label><br>
                <!-- เลือกสถานีตำรวจ -->
			<label> บช. </label><br>
			<select name="region" id="region" onChange="do_region_change()" class="form-control" Required >
				<option value="">&lt;&lt; เลือก บช. &gt;&gt;</option>
				<?php
				$res_re = $conn2->query("SELECT * FROM `wm_tb_police_region` 
											ORDER BY `name_region` ASC");
				 $selected = '';
				 while($row_re = $conn2->fetch_row($res_re)) 
					 {
					  $code_region = $row_re['code_region'];
					  $name_region = $row_re['name_region'];
					  // set default provionce to 64 >> sukhothai
						  if($code_region == $region) {
							  $selected = 'selected';
						  }
						  else {
							  $selected = '';
						  }
					  //
					  echo "<option value='$code_region' $selected> $name_region </option>\n";
					 }

			  ?>
			  </select>

			<label> บก. </label><br>
			<select name="provincial" id="provincial" onChange="do_provincial_change()" class="form-control" >
			<option value="0">&lt;&lt; เลือก บก. &gt;&gt;</option>
					<?php
					if($region > 0)
					{
						$res_prov = $conn2->query("SELECT * FROM `wm_tb_police_provincial` 
													WHERE `region_code`='$region' 
													ORDER BY `provincial_code` ASC");
						$selected = '';
					  while($res_prov = $conn2->fetch_row($res_prov)) 
					  {
						  $provincial_code = $res_prov['provincial_code']; // 
						  $provincial = $res_prov['provincial'];
						  // set default provionce to 64 >> sukhothai
							  if($provincial_code == $provincial) {
								  $selected = 'selected';
							  }
							  else {
								  $selected = '';
							  }
						  //
						  echo "<option value='$provincial_code' $selected> $provincial </option>\n";
					  }
					}	
					?>
				  </select>

			 <label> สน./สภ. </label>
			<br>
			<select name="station" id="station" class="form-control" >
					<option value="0">&lt;&lt; เลือก สน./สภ. &gt;&gt;</option>
					<?php
				   if($provincial > 0)
					{
						$res_st = $conn2->query("SELECT * FROM `wm_tb_police_station2` 
													WHERE provincial_code='$provincial_code'
													ORDER BY `station_code` ASC");
						$selected = '';
					  while($row_st = $conn2->fetch_row($res_st)) 
					  {
						  $station_code = $row_st['station_code']; // 
						  $station_name = $row_st['station_name'];
						  // set default provionce to 64 >> sukhothai
							  if($station_code == $station) {
								  $selected = 'selected';
							  }
							  else {
								  $selected = '';
							  }
						  //
						  echo "<option value='$station_code' $selected> $station_name </option>\n";
					  }
					}	
					?>                                                                     
				  </select>
			<br>
			
			 <div class="mb-3">
				 <label for="formFileMultiple" class="form-label">รูปภาพ</label>
				 <input class="form-control" type="file" id="pin_image" name="pin_image" multiple>
        	</div>
			
			<input type="submit" value="บันทึกข้อมูล" class="btn btn-success" >
            <a href="../Bansuan/GoogleMapsDisplay.php" class="btn btn-warning" >ยกเลิก</a>
			<br>
			<br>
		</div>
		</div>
    </form>
<!-- ฟังก์ชั่น เลือก จังหวัด อำเภอ ตำบล -->
<script>

function do_province_change()
{
	var sel = document.getElementById("cctv_province");		
	var code = sel.options[sel.selectedIndex].value;

	$.get("../ajax/get_amphure.php?province=" + code, 
		  function(data, status){
				//alert("Data: " + data + "\nStatus: " + status);
				// jQuery style						
				$('#cctv_amphur').find('option').remove().end();
				var data = $.trim(data);
				if(data == '') {
					alert("ไม่พบข้อมูลอำเภอ !");
					return;
				}

					//.append('<option value="whatever">text</option>').val('whatever');
				var datas = data.split(';');
				for(var i=0; i<datas.length; i++) {
					if(datas[i] == '') continue;
					datas[i] = datas[i].split(':');
					var code = datas[i][0];
					var name = datas[i][1];
					$('#cctv_amphur').append('<option value="'+ code+'">' + name + '</option>');
				}
        // Trigger the change event on the amphur dropdown so the tambon dropdown will be populated     
                $('#cctv_amphur').trigger('change');
		});
}

function do_amphure_change()
{
	var sel = document.getElementById("cctv_province");		
	var code = sel.options[sel.selectedIndex].value;

	var sel2 = document.getElementById("cctv_amphur");		
	var code2 = sel2.options[sel2.selectedIndex].value;

	$.get("../ajax/get_district.php?province=" + code + "&amphure=" + code2, 
			  function(data, status){
					//alert("Data: " + data + "\nStatus: " + status);
					// jQuery style						
					$('#cctv_tumbon').find('option').remove().end();
					var data = $.trim(data);
					if(data == '') {
						alert("ไม่พบข้อมูลตำบล !");
						return;
					}

						//.append('<option value="whatever">text</option>').val('whatever');
					var datas = data.split(';');
					for(var i=0; i<datas.length; i++) {
						if(datas[i] == '') continue;
						datas[i] = datas[i].split(':');
						var code = datas[i][0];
						var name = datas[i][1];
						$('#cctv_tumbon').append('<option value="'+ code+'">' + name + '</option>');
					}
			});
}

// ฟังก์ชั่น เปลี่ยนภาค (บช.) เป็น ภ.จว. (บก.)
function do_region_change()
{
	var sel_region = document.getElementById("region");		
	var code_region = sel_region.options[sel_region.selectedIndex].value;

	$.get("../ajax/get_provincial.php?region=" + code_region, 
		  function(data, status){
				//alert("Data: " + data + "\nStatus: " + status);
				// jQuery style						
				$('#provincial').find('option').remove().end();
				var data = $.trim(data);
				if(data == '') {
					alert("ไม่พบข้อมูล บก. !");
					return;
				}

					//.append('<option value="whatever">text</option>').val('whatever');
				var datas = data.split(';');
				for(var i=0; i<datas.length; i++) {
					if(datas[i] == '') continue;
					datas[i] = datas[i].split(':');
					var code_region = datas[i][0];
					var name_region = datas[i][1];
					$('#provincial').append('<option value="'+ code_region+'">' + name_region + '</option>');
				}
                // Trigger the change event on the amphur dropdown so the tambon dropdown will be populated     
                $('#provincial').trigger('change');
		});
}
    //เลือก สถานี จากจังหวัด
function do_provincial_change()
{
	var sel_provincial = document.getElementById("region");		
	var provincial_code = sel_provincial.options[sel_provincial.selectedIndex].value;
    
    var sel_provincial2 = document.getElementById("provincial");		
	var station_code = sel_provincial2.options[sel_provincial2.selectedIndex].value;

	$.get("../ajax/get_station.php?region=" + provincial_code + "&provincial=" + station_code, 
			  function(data, status){
					//alert("Data: " + data + "\nStatus: " + status);
					// jQuery style						
					$('#station').find('option').remove().end();
					var data = $.trim(data);
					if(data == '') {
						alert("ไม่พบข้อมูลสถานี !");
						return;
					}

						//.append('<option value="whatever">text</option>').val('whatever');
					var datas = data.split(';');
					for(var i=0; i<datas.length; i++) {
						if(datas[i] == '') continue;
						datas[i] = datas[i].split(':');
						var station_code = datas[i][0];
						var station_name = datas[i][1];
						$('#station').append('<option value="'+ station_code+'">' + station_name + '</option>');
					}
			});
}
</script>
	
</body>
</html>
