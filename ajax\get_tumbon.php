<?php
include("../config.inc.php");
include("../classes/class.database.inc.php");


$ampher = isset($_POST['ampher66']) ? $_POST['ampher66'] : 6401;

if($ampher > 0)
{		
	$conn = get_connection();
	
	$res = $conn->query("SELECT * FROM `elect66_tumbon` WHERE ampherCode='$ampher' ORDER BY `tcode` ASC");	
  while($row = $conn->fetch_row($res)) 
	  {
		  $t_code = $row['tcode'];
		  $t_name = $row['tname'];		  
		  //
		  echo "$t_code:$t_name;";
	  }
			  
}	
?>