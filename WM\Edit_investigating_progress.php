<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

$id = $_GET['id'];      //เลขรันนิ่ง
$pcid = $_GET['pcid'];  //เลขรับแจ้ง

$sql = "SELECT * FROM wm_tb_investigating_progress WHERE ivg_cl_aid = :id ";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':id', $id);

try{

    $stmt->execute();

}catch(PDOException $e){
    echo 'Sql failed: ' . $e->getMessage();
}
    
    $row = $stmt->fetch(PDO::FETCH_ASSOC);

//print_r($row);
// สำหรับเลือก จังหวัด อำเภอ ตำบล /และ สถานีตำรวจ
include("../config.inc.php");
include("../classes/class.database.inc.php");
$conn2 = get_connection(); // connect to mySQLDB

// สำหรับเลือกสถานีตำรวจทั่วประเทศ
$region = isset($_GET['region']) ? $_GET['region'] : 0; // sukhothai
$provincial = isset($_GET['provincial']) ? $_GET['provincial'] : 0; // sukhothai
//$station = isset($_GET['station']) ? $_GET['station'] : 0; // sukhothai

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>แก้ไขรายการที่กำลังสืบสวน</title>
<script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/policeinnopolis/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 18px;
    }
input::placeholder {
  color: red;
}
.form-control::placeholder {
  color: sandybrown;
}
</style>
<!-- สำหรับ วันเกิด แบบ พ.ศ. -->
<link rel="stylesheet" href="http://code.jquery.com/ui/1.9.1/themes/base/jquery-ui.css" />
<script src="http://code.jquery.com/jquery-1.8.2.js"></script>
<script src="/policeinnopolis/js/jquery-ui.js"></script>
</head>

<body>
	
	<div class="container">
	<div class="row">
	<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> แก้ไขรายการที่กำลังสืบสวน </div>
	<form action="../WM/Save_investigating_progress.php?pcid=<?= $pcid ?>&id=<?= $id ?>" method="POST" enctype="multipart/form-data" class="body">
	<span style="color: #1203F8">
        
		<label hidden="hidden">ลำดับ</label>
		<input name="ivg_cl_aid" type = "text" class="form-control" value= "<?= $id ?>" hidden="hidden"  >
        
        <label>เลขรับแจ้ง</label>
		<input type = "text" name = "ivg_cl_case_no" class="form-control" value= "<?= $pcid ?>" placeholder="เลขรับแจ้ง" readonly >
        
        <label>วันที่ <span style="color: #F90004">* จำเป็น</span> </label>
        <p><input type="text" name="ivg_cl_date" id="datepicker" value= "<?= $row['ivg_cl_date']?>" class="form-control" autocomplete="off" required></p>
              <script>
            function set_cal(ele)//function สร้างตัวเลือกปฎิทิน
            {
              $( ele ).datepicker({
                  onSelect:(date_text)=>
                  {
                    let arr=date_text.split("/");
                    let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])+543).toString();
                    $(ele).val(new_date);
                    $(ele).css("color","");
                  },
                  beforeShow:()=>{

                    if($(ele).val()!="")
                    {
                      let arr=$(ele).val().split("/");
                      let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])-543).toString();
                      $(ele).val(new_date);

                    }

                    $(ele).css("color","white");
                  },
                  onClose:()=>{

                      $(ele).css("color","");

                      if($(ele).val()!="")
                      {
                          let arr=$(ele).val().split("-");
                          if(parseInt(arr[2])<2500)
                          {
                              let new_date=arr[0]+"-"+arr[1]+"-"+(parseInt(arr[2])).toString();
                              $(ele).val(new_date);
                          }
                      }
                  },
                  dateFormat:"yy-mm-dd", //กำหนดรูปแบบวันที่เป็น วัน/เดือน/ปี
                  changeMonth:true,//กำหนดให้เลือกเดือนได้
                  changeYear:true,//กำหนดให้เลือกปีได้
                  showOtherMonths:true,//กำหนดให้แสดงวันของเดือนก่อนหน้าได้
              });
            }
          $(document).ready(function(){
            //เรียก function set_cal เมื่อเปิดหน้าเว็บ โดยส่ง object element ที่มี id เป็น datepicker เป็นพารามิเตอร์
            set_cal( $("#datepicker") );
          })
          </script>
        <!-- สำหรับ วันที่ แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ -->
        
        <label>รายการสืบสวน</label>
		<input type = "text" name = "ivg_cl_invest" class="form-control" value= "<?= $row['ivg_cl_invest']?>"  placeholder="รายการสืบสวน"   >
        
        <label>สรุปผลการสืบสวน</label>
		<input type = "text" name = "ivg_cl_invest_res" class="form-control" value= "<?= $row['ivg_cl_invest_res']?>" placeholder="สรุปผลการสืบสวน"   >
        
		<label>ผู้สืบสวน/บันทึก</label>
		<select id="ivg_cl_detective" name="ivg_cl_detective" class="form-select form-select-sm" placeholder="ระบุเจ้าหน้าที่สืบสวน/บันทึก">
			<option value="" selected> </option>
            <?php
                    $bsdp = $pdo->prepare("SELECT * FROM `police_name_bsdetective` order by `aid_bsdp` ASC");
                    $bsdp->execute();
          
                    while($row_bsdp = $bsdp->fetch(PDO::FETCH_ASSOC))
                    {
                        $aid_bsdp = htmlspecialchars($row_bsdp['aid_bsdp'], ENT_QUOTES, 'UTF-8');
                        $bsdp_name = htmlspecialchars($row_bsdp['bsdp_name'], ENT_QUOTES, 'UTF-8');
                        
                        echo "<option value='$aid_bsdp'>$bsdp_name</option>";
                    }
                ?>
			</select>
        <br>
		
        <div class="mb-3">
  		<label for="formFileMultiple" class="form-label">รายงานสืบสวน</label>
 		<input class="form-control" type="file" id="ivg_cl_file" name="ivg_cl_file" multiple >
		</div>
        
		<p>
		<br>
			<input type="submit" value="Update" class="btn btn-success" >
			<td> <a href="/policeinnopolis/WM/show_detail_investigating_case.php?pcid=<?= $pcid ?>" class="btn btn-warning" >ยกเลิก</a> </td>
		</p>
		<br>
		<br>
	</form>
	</div>
	</div>
	</div>

<script>
// ชื่อ >> value >> set selct auto (สคลิปตรวจสอบ Selected) จำเป็นต้องก็อปปี้ <script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js" type="text/javascript">
function auto_select(sid, value)
{
    $('#' + sid).val(value);
}

function auto_thaidate(sid, value)
{
	if(value != '') {
		var dates = value.split('-');
		dates = value.split('-');
		if(dates.length > 2) {
			var ys = dates[0];
			var ms = dates[1];
			var ds = dates[2];
			if(ys.substr(0,2) == '20') {
				ys = parseInt(ys) + 543;
			}
			value = $.digit(ds,2) + '/' + $.digit(ms,2) + '/' +ys;
			$('#' + sid).val(value);
		}
	}
}

$(document).ready(function() {
<?php
    echo "auto_select('ivg_cl_detective', '{$row['ivg_cl_detective']}');\n";
    // convert db date to thai dates
	//echo "auto_thaidate('ivg_cl_date', '{$row['ivg_cl_date']}');\n";
?>
    });
</script>
</body>
</html>