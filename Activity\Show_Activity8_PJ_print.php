<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);


$id = isset($_GET['hv_cl_aid']) ? $_GET['hv_cl_aid'] : '';

//get month from select or current month
$month = isset($_GET['month']) ? $_GET['month'] : date("m");
$year = isset($_GET['year']) ? $_GET['year'] : date("Y");

$m1 = '';
$m2 = '';
$m3 = '';
$m4 = '';
$m5 = '';
$m6 = '';
$m7 = '';
$m8 = '';
$m9 = '';
$m10 = '';
$m11 = '';
$m12 = '';
switch($month)
{
    case "01": $m1 = "selected"; break;
    case "02": $m2 = "selected"; break;
    case "03": $m3 = "selected"; break;
    case "04": $m4 = "selected"; break;
    case "05": $m5 = "selected"; break;
    case "06": $m6 = "selected"; break;
    case "07": $m7 = "selected"; break;
    case "08": $m8 = "selected"; break;
    case "09": $m9 = "selected"; break;
    case "10": $m10 = "selected"; break;
    case "11": $m11 = "selected"; break;
    case "12": $m12 = "selected"; break;
}
$current_month = $global_thai_month[0+$month];
$y2 = $year+543;

// สำหรับ พิมพ์หน้า ข้อมูฃ ประกาศ บรรทัด ที่ต้องการพิมพ์
//-----------------
$MAX_ROW = 18;
//-----------------

// สร้างฟังก์ชั่น เพื่อดึง หัวตาราง ไปแสดง ในหน้าต่อไป
function print_header()
{
    echo '
	<h3 align="center">ข้อมูลการติดตามพฤติการณ์และความเคลื่อนไหวของบุคคลพ้นโทษ พักโทษ  <?php echo $name_provincial ?></h3>
<h3 align="center">ประจำเดือน&nbsp; <?= $current_month ?> &nbsp;<?= $y2 ?> &nbsp;</h3>
<div class="table-responsive">
<table height="221" border="1" cellpadding="1" cellspacing="1" class="table-bordered table-hover mb-4 mt-4">
  <tbody>
    <tr>
      <td rowspan="3" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">&nbsp;สถานี&nbsp;</td>
      <td rowspan="3" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">จำนวนครั้ง<br>(รวมทั้งหมด)</td>
      <td height="46" colspan="5" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">เยี่ยมพบ</td>
      <td colspan="9" bgcolor="#FD5F2F" style="color: #00000; text-align: center; border-color: navy">เยี่ยมไม่พบ</td>
      <td rowspan="3" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">มีผลการปฏิบัติ</td>
      <td rowspan="3" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">ไม่มีผลการปฏิบัติ</td>
    </tr>
    <tr>
      <td rowspan="2" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">&nbsp;จำนวน (คน)&nbsp;</td>
      <td rowspan="2" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">&nbsp;ผลใน Police4.0&nbsp;</td>
      <td rowspan="2" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">ร้อยละความสำเร็จ</td>
      <td rowspan="2" bgcolor="#48CF1A" style="color: #00000; text-align: center; border-color: navy">เก็บ DNA</td>
      <td rowspan="2" bgcolor="#48CF1A" style="color: #00000; text-align: center; border-color: navy">ยังไม่เก็บ DNA</td>
      <td height="58" colspan="3" bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">ทราบที่อยู่</td>
      <td colspan="3" bgcolor="#FDBD62" style="color: #00000; text-align: center; border-color: navy">ไม่ทราบที่อยู่</td>
      <td colspan="3" bgcolor="#FA9779" style="color: #00000; text-align: center; border-color: navy">กระทำผิดอีก/อยู่เรือนจำ</td>
      </tr>
    <tr>
	  <td height="58" bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">&nbsp;จำนวน&nbsp;</td>
	  <td bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">&nbsp;แจ้งท้องที่ใหม่&nbsp;</td>
	  <td bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">&nbsp;ยังไม่แจ้ง&nbsp;</td>
	  <td bgcolor="#FDBD62" style="color: #00000; text-align: center; border-color: navy">&nbsp;จำนวน&nbsp;</td>
	  <td bgcolor="#FDBD62" style="color: #00000; text-align: center; border-color: navy">&nbsp;แจ้ง รจ.และทว.&nbsp;</td>
	  <td bgcolor="#FDBD62" style="color: #00000; text-align: center; border-color: navy">&nbsp;ยังไม่แจ้ง&nbsp;</td>
	  <td bgcolor="#FA9779" style="color: #00000; text-align: center; border-color: navy">&nbsp;จำนวน&nbsp;</td>
	  <td bgcolor="#FA9779" style="color: #00000; text-align: center; border-color: navy">&nbsp;แจ้ง ทว.&nbsp;</td>
	  <td bgcolor="#FA9779" style="color: #00000; text-align: center; border-color: navy">&nbsp;ยังไม่แจ้ง&nbsp;</td>
      </tr>
	';
}

?>

<meta charset="utf-8">
<title>รายงานบุคคลพ้นโทษ/พักโทษ <?php echo $name_provincial ?></title>
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
        <base target="_top">
		<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
		<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
  		<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
 		<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js"></script>

<style type="text/css">
body{
    font-size:12px; 
}
.textAlignVer{
    display:block;
    filter: flipv fliph;
    -webkit-transform: rotate(-90deg); 
    -moz-transform: rotate(-90deg); 
    transform: rotate(-90deg); 
    position:relative;
    width:20px;
    white-space:nowrap;
    font-size:12px;
    margin-bottom:10px;
}
	td {
		height: 50px;
	}
</style>
<!-- สไตล์ สำหรับ สั่งพิมพ์ข้อมูล -->
<style>
@media only print
{
   
}
    
@media print {
    /* Set the page size to A4 landscape */
    @page {
    size: A4 landscape;
  }
    /* Set the margins */
  body {
    margin: 0;
    padding: 0;
  }
td{
    color: black;
}
tr{
    color: black;
}
  .printId{visibility: visible;}
    
.footer { page-break-after: always;}
}
.body_text{
    font-size: 16px;
    color: black;
}
/* Avoid breaking content across pages */
  .content {
    page-break-inside: avoid;
  }
/* Hide elements that shouldn't be printed */
  .no-print {
    display: none;
  }

</style>
<!--<p><img src="../Image/Head2.jpg" width="100%" height="" alt=""/></p>-->
<body>
<div class="container-fluid" align="left">
<div class="printId">
<h3 align="center">ข้อมูลการติดตามพฤติการณ์และความเคลื่อนไหวของบุคคลพ้นโทษ พักโทษ  <?php echo $name_provincial ?></h3>
<h3 align="center">ประจำเดือน&nbsp; <?= $current_month ?> &nbsp;<?= $y2 ?> &nbsp;</h3>
<div class="table-responsive">
<table height="221" border="1" cellpadding="1" cellspacing="1" class="table-bordered table-hover mb-4 mt-4">
  <tbody>
    <tr>
      <td rowspan="3" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">&nbsp;สถานี&nbsp;</td>
      <td rowspan="3" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">จำนวนครั้ง<br>(รวมทั้งหมด)</td>
      <td height="46" colspan="5" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">เยี่ยมพบ</td>
      <td colspan="9" bgcolor="#FD5F2F" style="color: #00000; text-align: center; border-color: navy">เยี่ยมไม่พบ</td>
      <td rowspan="3" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">มีผลการปฏิบัติ</td>
      <td rowspan="3" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">ไม่มีผลการปฏิบัติ</td>
    </tr>
    <tr>
      <td rowspan="2" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">&nbsp;จำนวน (คน)&nbsp;</td>
      <td rowspan="2" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">&nbsp;ผลใน Police4.0&nbsp;</td>
      <td rowspan="2" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">ร้อยละความสำเร็จ</td>
      <td rowspan="2" bgcolor="#48CF1A" style="color: #00000; text-align: center; border-color: navy">เก็บ DNA</td>
      <td rowspan="2" bgcolor="#48CF1A" style="color: #00000; text-align: center; border-color: navy">ยังไม่เก็บ DNA</td>
      <td height="58" colspan="3" bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">ทราบที่อยู่</td>
      <td colspan="3" bgcolor="#FDBD62" style="color: #00000; text-align: center; border-color: navy">ไม่ทราบที่อยู่</td>
      <td colspan="3" bgcolor="#FA9779" style="color: #00000; text-align: center; border-color: navy">กระทำผิดอีก/อยู่เรือนจำ</td>
      </tr>
    <tr>
	  <td height="58" bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">&nbsp;จำนวน&nbsp;</td>
	  <td bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">&nbsp;แจ้งท้องที่ใหม่&nbsp;</td>
	  <td bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">&nbsp;ยังไม่แจ้ง&nbsp;</td>
	  <td bgcolor="#FDBD62" style="color: #00000; text-align: center; border-color: navy">&nbsp;จำนวน&nbsp;</td>
	  <td bgcolor="#FDBD62" style="color: #00000; text-align: center; border-color: navy">&nbsp;แจ้ง รจ.และทว.&nbsp;</td>
	  <td bgcolor="#FDBD62" style="color: #00000; text-align: center; border-color: navy">&nbsp;ยังไม่แจ้ง&nbsp;</td>
	  <td bgcolor="#FA9779" style="color: #00000; text-align: center; border-color: navy">&nbsp;จำนวน&nbsp;</td>
	  <td bgcolor="#FA9779" style="color: #00000; text-align: center; border-color: navy">&nbsp;แจ้ง ทว.&nbsp;</td>
	  <td bgcolor="#FA9779" style="color: #00000; text-align: center; border-color: navy">&nbsp;ยังไม่แจ้ง&nbsp;</td>
      </tr>
	  
<?php     
                      
$sql = "SELECT " .
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T1_1, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T1, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T2, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T3, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T4, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T5, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T6, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T7, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T8, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T9, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T10, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T11, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T12, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T13, " .
	
	// ดงเจริญ
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6401) AS T1_1_djr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6401) AS T1_djr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6401) AS T2_djr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6401) AS T3_djr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6401) AS T4_djr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6401) AS T5_djr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6401) AS T6_djr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6401) AS T7_djr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6401) AS T8_djr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6401) AS T9_djr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6401) AS T10_djr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6401) AS T11_djr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6401) AS T12_djr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6401) AS T13_djr, " .
	
	// ตะพานหิน
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6402) AS T1_1_tph, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6402) AS T1_tph, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6402) AS T2_tph, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6402) AS T3_tph, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6402) AS T4_tph, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6402) AS T5_tph, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6402) AS T6_tph, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6402) AS T7_tph, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6402) AS T8_tph, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6402) AS T9_tph, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6402) AS T10_tph, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6402) AS T11_tph, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6402) AS T12_tph, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6402) AS T13_tph, " .
	
	// ทับคล้อ
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6403) AS T1_1_tsl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6403) AS T1_tsl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6403) AS T2_tsl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6403) AS T3_tsl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6403) AS T4_tsl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6403) AS T5_tsl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6403) AS T6_tsl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6403) AS T7_tsl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6403) AS T8_tsl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6403) AS T9_tsl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6403) AS T10_tsl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6403) AS T11_tsl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6403) AS T12_tsl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6403) AS T13_tsl, " .
	
	// บางมูลนาก
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6404) AS T1_1_pmn, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6404) AS T1_pmn, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6404) AS T2_pmn, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6404) AS T3_pmn, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6404) AS T4_pmn, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6404) AS T5_pmn, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6404) AS T6_pmn, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6404) AS T7_pmn, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6404) AS T8_pmn, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6404) AS T9_pmn, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6404) AS T10_pmn, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6404) AS T11_pmn, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6404) AS T12_pmn, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6404) AS T13_pmn, " .
	
	// บึงนาราง
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6405) AS T1_1_bnr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6405) AS T1_bnr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6405) AS T2_bnr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6405) AS T3_bnr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6405) AS T4_bnr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6405) AS T5_bnr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6405) AS T6_bnr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6405) AS T7_bnr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6405) AS T8_bnr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6405) AS T9_bnr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6405) AS T10_bnr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6405) AS T11_bnr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6405) AS T12_bnr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6405) AS T13_bnr, " .
	
	// โพทะเล
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6406) AS T1_1_ptl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6406) AS T1_ptl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6406) AS T2_ptl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6406) AS T3_ptl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6406) AS T4_ptl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6406) AS T5_ptl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6406) AS T6_ptl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6406) AS T7_ptl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6406) AS T8_ptl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6406) AS T9_ptl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6406) AS T10_ptl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6406) AS T11_ptl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6406) AS T12_ptl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6406) AS T13_ptl, " .
	
	// เมืองพิจิตร
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6407) AS T1_1_mpj, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6407) AS T1_mpj, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6407) AS T2_mpj, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6407) AS T3_mpj, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6407) AS T4_mpj, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6407) AS T5_mpj, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6407) AS T6_mpj, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6407) AS T7_mpj, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6407) AS T8_mpj, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6407) AS T9_mpj, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6407) AS T10_mpj, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6407) AS T11_mpj, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6407) AS T12_mpj, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6407) AS T13_mpj, " .
	
	// วชิรบารมี
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6408) AS T1_1_wcr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6408) AS T1_wcr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6408) AS T2_wcr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6408) AS T3_wcr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6408) AS T4_wcr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6408) AS T5_wcr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6408) AS T6_wcr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6408) AS T7_wcr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6408) AS T8_wcr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6408) AS T9_wcr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6408) AS T10_wcr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6408) AS T11_wcr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6408) AS T12_wcr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6408) AS T13_wcr, " .
	
	// วังทรายพูน
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6409) AS T1_1_wsp, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6409) AS T1_wsp, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6409) AS T2_wsp, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6409) AS T3_wsp, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6409) AS T4_wsp, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6409) AS T5_wsp, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6409) AS T6_wsp, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6409) AS T7_wsp, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6409) AS T8_wsp, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6409) AS T9_wsp, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6409) AS T10_wsp, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6409) AS T11_wsp, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6409) AS T12_wsp, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6409) AS T13_wsp, " .
	
	// สากเหล็ก
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6410) AS T1_1_skl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6410) AS T1_skl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6410) AS T2_skl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6410) AS T3_skl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6410) AS T4_skl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6410) AS T5_skl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6410) AS T6_skl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6410) AS T7_skl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6410) AS T8_skl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6410) AS T9_skl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6410) AS T10_skl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6410) AS T11_skl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6410) AS T12_skl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6410) AS T13_skl, " .
	
	// สามง่าม
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6411) AS T1_1_smk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6411) AS T1_smk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6411) AS T2_smk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6411) AS T3_smk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6411) AS T4_smk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6411) AS T5_smk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6411) AS T6_smk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6411) AS T7_smk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6411) AS T8_smk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6411) AS T9_smk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6411) AS T10_smk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6411) AS T11_smk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6411) AS T12_smk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6411) AS T13_smk, " .
	
	// หนองโสน
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6412) AS T1_1_nsn, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6412) AS T1_nsn, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6412) AS T2_nsn, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6412) AS T3_nsn, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6412) AS T4_nsn, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6412) AS T5_nsn, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6412) AS T6_nsn, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6412) AS T7_nsn, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6412) AS T8_nsn, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6412) AS T9_nsn, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6412) AS T10_nsn, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6412) AS T11_nsn, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6412) AS T12_nsn, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6412) AS T13_nsn, " .
	
	// โพธิ์ประทับช้าง
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6413) AS T1_1_ptc, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6413) AS T1_ptc, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6413) AS T2_ptc, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6413) AS T3_ptc, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6413) AS T4_ptc, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6413) AS T5_ptc, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6413) AS T6_ptc, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6413) AS T7_ptc, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6413) AS T8_ptc, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6413) AS T9_ptc, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6413) AS T10_ptc, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6413) AS T11_ptc, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6413) AS T12_ptc, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6413) AS T13_ptc, " .
	
	// วังหว้า
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6414) AS T1_1_wgw, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6414) AS T1_wgw, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6414) AS T2_wgw, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6414) AS T3_wgw, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6414) AS T4_wgw, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6414) AS T5_wgw, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6414) AS T6_wgw, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6414) AS T7_wgw, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6414) AS T8_wgw, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6414) AS T9_wgw, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6414) AS T10_wgw, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6414) AS T11_wgw, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6414) AS T12_wgw, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6414) AS T13_wgw, " .
	
	// ดงป่าคำ
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6415) AS T1_1_dpk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6415) AS T1_dpk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6415) AS T2_dpk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6415) AS T3_dpk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6415) AS T4_dpk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6415) AS T5_dpk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6415) AS T6_dpk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6415) AS T7_dpk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6415) AS T8_dpk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6415) AS T9_dpk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6415) AS T10_dpk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6415) AS T11_dpk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6415) AS T12_dpk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6415) AS T13_dpk, " .
	
	// บางลาย
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6416) AS T1_1_bgl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6416) AS T1_bgl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6416) AS T2_bgl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6416) AS T3_bgl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6416) AS T4_bgl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6416) AS T5_bgl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6416) AS T6_bgl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6416) AS T7_bgl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6416) AS T8_bgl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6416) AS T9_bgl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6416) AS T10_bgl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6416) AS T11_bgl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6416) AS T12_bgl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6416) AS T13_bgl, " .
	
	// ย่านยาว
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6417) AS T1_1_yyo, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6417) AS T1_yyo, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6417) AS T2_yyo, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6417) AS T3_yyo, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6417) AS T4_yyo, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6417) AS T5_yyo, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6417) AS T6_yyo, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6417) AS T7_yyo, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6417) AS T8_yyo, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6417) AS T9_yyo, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6417) AS T10_yyo, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6417) AS T11_yyo, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6417) AS T12_yyo, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6417) AS T13_yyo " .
	
        " ";

$stmt = $pdo->prepare($sql);
$stmt->bindParam(':month', $month);
$stmt->bindParam(':year', $year);
$stmt->bindParam(':provincial', $provincial); // Add station parameter
try {
    $stmt->execute();
} catch (PDOException $e) {
    echo '$sql Failed: '. $e->getMessage();
}

          
// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no = 1;
while($row = $stmt->fetch(PDO::FETCH_ASSOC))	//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
{
//ฟังก์ชั่น วันที่ ดึงจาก condb
//$strDate = DateThai( $row_mi["date_record"] );
    
if($row["T1"] > 0) {   
    $persen = ($row["T2"]*100)/$row["T1"];
}
else {
    $persen=0;
    }
    $sum1 = $row["T1"] + $row["T5"] + $row["T8"] + $row["T11"];
    $sum2 = $row["T1_1"] - $sum1;

// ดงเจริญ
if($row["T1_djr"] > 0) {   
    $persen_djr = ($row["T2_djr"]*100)/$row["T1_djr"];
}
else {
    $persen_djr=0;
    }
    $sum1_djr = $row["T1_djr"] + $row["T5_djr"] + $row["T8_djr"] + $row["T11_djr"];
    $sum2_djr = $row["T1_1_djr"] - $sum1_djr;
	
// ตะพานหิน
if($row["T1_tph"] > 0) {   
    $persen_tph = ($row["T2_tph"]*100)/$row["T1_tph"];
}
else {
    $persen_tph=0;
    }
    $sum1_tph = $row["T1_tph"] + $row["T5_tph"] + $row["T8_tph"] + $row["T11_tph"];
    $sum2_tph = $row["T1_1_tph"] - $sum1_tph;
	
// ทับคล้อ
if($row["T1_tsl"] > 0) {   
    $persen_tsl = ($row["T2_tsl"]*100)/$row["T1_tsl"];
}
else {
    $persen_tsl=0;
    }
    $sum1_tsl = $row["T1_tsl"] + $row["T5_tsl"] + $row["T8_tsl"] + $row["T11_tsl"];
    $sum2_tsl = $row["T1_1_tsl"] - $sum1_tsl;
	
// บางมูลนาก
if($row["T1_pmn"] > 0) {   
    $persen_pmn = ($row["T2_pmn"]*100)/$row["T1_pmn"];
}
else {
    $persen_pmn=0;
    }
    $sum1_pmn = $row["T1_pmn"] + $row["T5_pmn"] + $row["T8_pmn"] + $row["T11_pmn"];
    $sum2_pmn = $row["T1_1_pmn"] - $sum1_pmn;
	
// บึงนาราง
if($row["T1_bnr"] > 0) {   
    $persen_bnr = ($row["T2_bnr"]*100)/$row["T1_bnr"];
}
else {
    $persen_bnr=0;
    }
    $sum1_bnr = $row["T1_bnr"] + $row["T5_bnr"] + $row["T8_bnr"] + $row["T11_bnr"];
    $sum2_bnr = $row["T1_1_bnr"] - $sum1_bnr;
	
// โพทะเล
if($row["T1_ptl"] > 0) {   
    $persen_ptl = ($row["T2_ptl"]*100)/$row["T1_ptl"];
}
else {
    $persen_ptl=0;
    }
    $sum1_ptl = $row["T1_ptl"] + $row["T5_ptl"] + $row["T8_ptl"] + $row["T11_ptl"];
    $sum2_ptl = $row["T1_1_ptl"] - $sum1_ptl;
	
// เมืองพิจิตร
if($row["T1_mpj"] > 0) {   
    $persen_mpj = ($row["T2_mpj"]*100)/$row["T1_mpj"];
}
else {
    $persen_mpj=0;
    }
    $sum1_mpj = $row["T1_mpj"] + $row["T5_mpj"] + $row["T8_mpj"] + $row["T11_mpj"];
    $sum2_mpj = $row["T1_1_mpj"] - $sum1_mpj;
	
// วชิรบารมี
if($row["T1_wcr"] > 0) {   
    $persen_wcr = ($row["T2_wcr"]*100)/$row["T1_wcr"];
}
else {
    $persen_wcr=0;
    }
    $sum1_wcr = $row["T1_wcr"] + $row["T5_wcr"] + $row["T8_wcr"] + $row["T11_wcr"];
    $sum2_wcr = $row["T1_1_wcr"] - $sum1_wcr;
	
// วังทรายพูน
if($row["T1_wsp"] > 0) {   
    $persen_wsp = ($row["T2_wsp"]*100)/$row["T1_wsp"];
}
else {
    $persen_wsp=0;
    }
    $sum1_wsp = $row["T1_wsp"] + $row["T5_wsp"] + $row["T8_wsp"] + $row["T11_wsp"];
    $sum2_wsp = $row["T1_1_wsp"] - $sum1_wsp;
	
// สากเหล็ก
if($row["T1_skl"] > 0) {   
    $persen_skl = ($row["T2_skl"]*100)/$row["T1_skl"];
}
else {
    $persen_skl=0;
    }
    $sum1_skl = $row["T1_skl"] + $row["T5_skl"] + $row["T8_skl"] + $row["T11_skl"];
    $sum2_skl = $row["T1_1_skl"] - $sum1_skl;
	
// สามง่าม
if($row["T1_smk"] > 0) {   
    $persen_smk = ($row["T2_smk"]*100)/$row["T1_smk"];
}
else {
    $persen_smk=0;
    }
    $sum1_smk = $row["T1_smk"] + $row["T5_smk"] + $row["T8_smk"] + $row["T11_smk"];
    $sum2_smk = $row["T1_1_smk"] - $sum1_smk;
	
// หนองโสน
if($row["T1_nsn"] > 0) {   
    $persen_nsn = ($row["T2_nsn"]*100)/$row["T1_nsn"];
}
else {
    $persen_nsn=0;
    }
    $sum1_nsn = $row["T1_nsn"] + $row["T5_nsn"] + $row["T8_nsn"] + $row["T11_nsn"];
    $sum2_nsn = $row["T1_1_nsn"] - $sum1_nsn;
	
// โพธิ์ประทับช้าง
if($row["T1_ptc"] > 0) {   
    $persen_ptc = ($row["T2_ptc"]*100)/$row["T1_ptc"];
}
else {
    $persen_ptc=0;
    }
    $sum1_ptc = $row["T1_ptc"] + $row["T5_ptc"] + $row["T8_ptc"] + $row["T11_ptc"];
    $sum2_ptc = $row["T1_1_ptc"] - $sum1_ptc;
	
// วังหว้า
if($row["T1_wgw"] > 0) {   
    $persen_wgw = ($row["T2_wgw"]*100)/$row["T1_wgw"];
}
else {
    $persen_wgw=0;
    }
    $sum1_wgw = $row["T1_wgw"] + $row["T5_wgw"] + $row["T8_wgw"] + $row["T11_wgw"];
    $sum2_wgw = $row["T1_1_wgw"] - $sum1_wgw;
	
// ดงป่าคำ
if($row["T1_dpk"] > 0) {   
    $persen_dpk = ($row["T2_dpk"]*100)/$row["T1_dpk"];
}
else {
    $persen_dpk=0;
    }
    $sum1_dpk = $row["T1_dpk"] + $row["T5_dpk"] + $row["T8_dpk"] + $row["T11_dpk"];
    $sum2_dpk = $row["T1_1_dpk"] - $sum1_dpk;
	
// บางลาย
if($row["T1_bgl"] > 0) {   
    $persen_bgl = ($row["T2_bgl"]*100)/$row["T1_bgl"];
}
else {
    $persen_bgl=0;
    }
    $sum1_bgl = $row["T1_bgl"] + $row["T5_bgl"] + $row["T8_bgl"] + $row["T11_bgl"];
    $sum2_bgl = $row["T1_1_bgl"] - $sum1_bgl;
	
// ย่านยาว
if($row["T1_yyo"] > 0) {   
    $persen_yyo = ($row["T2_yyo"]*100)/$row["T1_yyo"];
}
else {
    $persen_yyo=0;
    }
    $sum1_yyo = $row["T1_yyo"] + $row["T5_yyo"] + $row["T8_yyo"] + $row["T11_yyo"];
    $sum2_yyo = $row["T1_1_yyo"] - $sum1_yyo;
	
?>	

    <tr>
      <td nowrap style="text-align: left; font-size: 22px; font-weight: bold">&nbsp;<?php echo $name_provincial ?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T1_1"] > 0) ? $row["T1_1"]  : "") ?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T1"] > 0) ? $row["T1"]  : "") ?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T2"] > 0) ? $row["T2"]  : "") ?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold"><?php echo $persen ?>&nbsp;%</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T3"] > 0) ? $row["T3"]  : "") ?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T4"] > 0) ? $row["T4"]  : "") ?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T5"] > 0) ? $row["T5"]  : "")?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T6"] > 0) ? $row["T6"]  : "")?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T7"] > 0) ? $row["T7"]  : "")?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T8"] > 0) ? $row["T8"]  : "")?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T9"] > 0) ? $row["T9"]  : "")?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T10"] > 0) ? $row["T10"]  : "")?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T11"] > 0) ? $row["T11"]  : "")?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T12"] > 0) ? $row["T12"]  : "")?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T13"] > 0) ? $row["T13"]  : "")?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($sum1 > 0) ? $sum1  : "") ?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($sum2 > 0) ? $sum2  : "") ?>&nbsp;</td>
    </tr>
	<!-- ดงเจริญ -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.ดงเจริญ&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_djr"] > 0) ? $row["T1_1_djr"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_djr"] > 0) ? $row["T1_djr"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_djr"] > 0) ? $row["T2_djr"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?php echo $persen_djr ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_djr"] > 0) ? $row["T3_djr"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_djr"] > 0) ? $row["T4_djr"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_djr"] > 0) ? $row["T5_djr"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_djr"] > 0) ? $row["T6_djr"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_djr"] > 0) ? $row["T7_djr"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_djr"] > 0) ? $row["T8_djr"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_djr"] > 0) ? $row["T9_djr"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_djr"] > 0) ? $row["T10_djr"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_djr"] > 0) ? $row["T11_djr"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_djr"] > 0) ? $row["T12_djr"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_djr"] > 0) ? $row["T13_djr"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_djr > 0) ? $sum1_djr : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_djr > 0) ? $sum2_djr : "") ?>&nbsp;</td>
    </tr>
	<!-- ตะพานหิน -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.ตะพานหิน&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_tph"] > 0) ? $row["T1_1_tph"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_tph"] > 0) ? $row["T1_tph"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_tph"] > 0) ? $row["T2_tph"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?= $persen_tph ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_tph"] > 0) ? $row["T3_tph"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_tph"] > 0) ? $row["T4_tph"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_tph"] > 0) ? $row["T5_tph"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_tph"] > 0) ? $row["T6_tph"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_tph"] > 0) ? $row["T7_tph"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_tph"] > 0) ? $row["T8_tph"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_tph"] > 0) ? $row["T9_tph"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_tph"] > 0) ? $row["T10_tph"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_tph"] > 0) ? $row["T11_tph"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_tph"] > 0) ? $row["T12_tph"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_tph"] > 0) ? $row["T13_tph"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_tph > 0) ? $sum1_tph : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_tph > 0) ? $sum2_tph : "")?>&nbsp;</td>
    </tr> 
	<!-- ทับคล้อ -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.ทับคล้อ&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_tsl"] > 0) ? $row["T1_1_tsl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_tsl"] > 0) ? $row["T1_tsl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_tsl"] > 0) ? $row["T2_tsl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?= $persen_tsl ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_tsl"] > 0) ? $row["T3_tsl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_tsl"] > 0) ? $row["T4_tsl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_tsl"] > 0) ? $row["T5_tsl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_tsl"] > 0) ? $row["T6_tsl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_tsl"] > 0) ? $row["T7_tsl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_tsl"] > 0) ? $row["T8_tsl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_tsl"] > 0) ? $row["T9_tsl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_tsl"] > 0) ? $row["T10_tsl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_tsl"] > 0) ? $row["T11_tsl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_tsl"] > 0) ? $row["T12_tsl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_tsl"] > 0) ? $row["T13_tsl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_tsl > 0) ? $sum1_tsl : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_tsl > 0) ? $sum2_tsl : "")?>&nbsp;</td>
    </tr> 
	<!-- บางมูลนาก -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.บางมูลนาก&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_pmn"] > 0) ? $row["T1_1_pmn"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_pmn"] > 0) ? $row["T1_pmn"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_pmn"] > 0) ? $row["T2_pmn"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?= $persen_pmn ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_pmn"] > 0) ? $row["T3_pmn"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_pmn"] > 0) ? $row["T4_pmn"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_pmn"] > 0) ? $row["T5_pmn"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_pmn"] > 0) ? $row["T6_pmn"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_pmn"] > 0) ? $row["T7_pmn"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_pmn"] > 0) ? $row["T8_pmn"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_pmn"] > 0) ? $row["T9_pmn"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_pmn"] > 0) ? $row["T10_pmn"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_pmn"] > 0) ? $row["T11_pmn"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_pmn"] > 0) ? $row["T12_pmn"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_pmn"] > 0) ? $row["T13_pmn"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_pmn > 0) ? $sum1_pmn : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_pmn > 0) ? $sum2_pmn : "")?>&nbsp;</td>
    </tr> 
	<!-- บึงนาราง -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.บึงนาราง&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_bnr"] > 0) ? $row["T1_1_bnr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_bnr"] > 0) ? $row["T1_bnr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_bnr"] > 0) ? $row["T2_bnr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?= $persen_bnr ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_bnr"] > 0) ? $row["T3_bnr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_bnr"] > 0) ? $row["T4_bnr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_bnr"] > 0) ? $row["T5_bnr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_bnr"] > 0) ? $row["T6_bnr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_bnr"] > 0) ? $row["T7_bnr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_bnr"] > 0) ? $row["T8_bnr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_bnr"] > 0) ? $row["T9_bnr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_bnr"] > 0) ? $row["T10_bnr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_bnr"] > 0) ? $row["T11_bnr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_bnr"] > 0) ? $row["T12_bnr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_bnr"] > 0) ? $row["T13_bnr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_bnr > 0) ? $sum1_bnr : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_bnr > 0) ? $sum2_bnr : "")?>&nbsp;</td>
    </tr> 
	<!-- โพทะเล -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.โพทะเล&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_ptl"] > 0) ? $row["T1_1_ptl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_ptl"] > 0) ? $row["T1_ptl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_ptl"] > 0) ? $row["T2_ptl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?= $persen_ptl ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_ptl"] > 0) ? $row["T3_ptl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_ptl"] > 0) ? $row["T4_ptl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_ptl"] > 0) ? $row["T5_ptl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_ptl"] > 0) ? $row["T6_ptl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_ptl"] > 0) ? $row["T7_ptl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_ptl"] > 0) ? $row["T8_ptl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_ptl"] > 0) ? $row["T9_ptl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_ptl"] > 0) ? $row["T10_ptl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_ptl"] > 0) ? $row["T11_ptl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_ptl"] > 0) ? $row["T12_ptl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_ptl"] > 0) ? $row["T13_ptl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_ptl > 0) ? $sum1_ptl : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_ptl > 0) ? $sum2_ptl : "")?>&nbsp;</td>
    </tr> 
	<!-- เมืองพิจิตร -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.เมืองพิจิตร&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_mpj"] > 0) ? $row["T1_1_mpj"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_mpj"] > 0) ? $row["T1_mpj"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_mpj"] > 0) ? $row["T2_mpj"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?= $persen_mpj ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_mpj"] > 0) ? $row["T3_mpj"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_mpj"] > 0) ? $row["T4_mpj"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_mpj"] > 0) ? $row["T5_mpj"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_mpj"] > 0) ? $row["T6_mpj"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_mpj"] > 0) ? $row["T7_mpj"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_mpj"] > 0) ? $row["T8_mpj"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_mpj"] > 0) ? $row["T9_mpj"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_mpj"] > 0) ? $row["T10_mpj"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_mpj"] > 0) ? $row["T11_mpj"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_mpj"] > 0) ? $row["T12_mpj"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_mpj"] > 0) ? $row["T13_mpj"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_mpj > 0) ? $sum1_mpj : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_mpj > 0) ? $sum2_mpj : "")?>&nbsp;</td>
    </tr>
	<!-- วชิรบารมี -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.วชิรบารมี&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_wcr"] > 0) ? $row["T1_1_wcr"] : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_wcr"] > 0) ? $row["T1_wcr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_wcr"] > 0) ? $row["T2_wcr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?= $persen_wcr ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_wcr"] > 0) ? $row["T3_wcr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_wcr"] > 0) ? $row["T4_wcr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_wcr"] > 0) ? $row["T5_wcr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_wcr"] > 0) ? $row["T6_wcr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_wcr"] > 0) ? $row["T7_wcr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_wcr"] > 0) ? $row["T8_wcr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_wcr"] > 0) ? $row["T9_wcr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_wcr"] > 0) ? $row["T10_wcr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_wcr"] > 0) ? $row["T11_wcr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_wcr"] > 0) ? $row["T12_wcr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_wcr"] > 0) ? $row["T13_wcr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_wcr > 0) ? $sum1_wcr : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_wcr > 0) ? $sum2_wcr : "") ?>&nbsp;</td>
    </tr> 
	<!-- วังทรายพูน -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.วังทรายพูน&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_wsp"] > 0) ? $row["T1_1_wsp"] : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_wsp"] > 0) ? $row["T1_wsp"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_wsp"] > 0) ? $row["T2_wsp"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?php echo $persen_wsp ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_wsp"] > 0) ? $row["T3_wsp"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_wsp"] > 0) ? $row["T4_wsp"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_wsp"] > 0) ? $row["T5_wsp"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_wsp"] > 0) ? $row["T6_wsp"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_wsp"] > 0) ? $row["T7_wsp"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_wsp"] > 0) ? $row["T8_wsp"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_wsp"] > 0) ? $row["T9_wsp"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_wsp"] > 0) ? $row["T10_wsp"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_wsp"] > 0) ? $row["T11_wsp"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_wsp"] > 0) ? $row["T12_wsp"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_wsp"] > 0) ? $row["T13_wsp"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_wsp > 0) ? $sum1_wsp : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_wsp > 0) ? $sum2_wsp : "") ?>&nbsp;</td>
    </tr> 
	<!-- สากเหล็ก -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.สากเหล็ก&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_skl"] > 0) ? $row["T1_1_skl"] : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_skl"] > 0) ? $row["T1_skl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_skl"] > 0) ? $row["T2_skl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?php echo $persen_skl ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_skl"] > 0) ? $row["T3_skl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_skl"] > 0) ? $row["T4_skl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_skl"] > 0) ? $row["T5_skl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_skl"] > 0) ? $row["T6_skl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_skl"] > 0) ? $row["T7_skl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_skl"] > 0) ? $row["T8_skl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_skl"] > 0) ? $row["T9_skl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_skl"] > 0) ? $row["T10_skl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_skl"] > 0) ? $row["T11_skl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_skl"] > 0) ? $row["T12_skl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_skl"] > 0) ? $row["T13_skl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_skl > 0) ? $sum1_skl : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_skl > 0) ? $sum2_skl : "") ?>&nbsp;</td>
    </tr> 
	<!-- สามง่าม -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.สามง่าม&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_smk"] > 0) ? $row["T1_1_smk"] : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_smk"] > 0) ? $row["T1_smk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_smk"] > 0) ? $row["T2_smk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?php echo $persen_smk ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_smk"] > 0) ? $row["T3_smk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_smk"] > 0) ? $row["T4_smk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_smk"] > 0) ? $row["T5_smk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_smk"] > 0) ? $row["T6_smk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_smk"] > 0) ? $row["T7_smk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_smk"] > 0) ? $row["T8_smk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_smk"] > 0) ? $row["T9_smk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_smk"] > 0) ? $row["T10_smk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_smk"] > 0) ? $row["T11_smk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_smk"] > 0) ? $row["T12_smk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_smk"] > 0) ? $row["T13_smk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_smk > 0) ? $sum1_smk : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_smk > 0) ? $sum2_smk : "") ?>&nbsp;</td>
    </tr> 
	  <!-- หนองโสน -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.หนองโสน&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_nsn"] > 0) ? $row["T1_1_nsn"] : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_nsn"] > 0) ? $row["T1_nsn"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_nsn"] > 0) ? $row["T2_nsn"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?php echo $persen_nsn ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_nsn"] > 0) ? $row["T3_nsn"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_nsn"] > 0) ? $row["T4_nsn"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_nsn"] > 0) ? $row["T5_nsn"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_nsn"] > 0) ? $row["T6_nsn"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_nsn"] > 0) ? $row["T7_nsn"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_nsn"] > 0) ? $row["T8_nsn"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_nsn"] > 0) ? $row["T9_nsn"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_nsn"] > 0) ? $row["T10_nsn"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_nsn"] > 0) ? $row["T11_nsn"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_nsn"] > 0) ? $row["T12_nsn"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_nsn"] > 0) ? $row["T13_nsn"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_nsn > 0) ? $sum1_nsn : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_nsn > 0) ? $sum2_nsn : "") ?>&nbsp;</td>
    </tr> 
	  <!-- โพธิ์ประทับช้าง -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.โพธิ์ประทับช้าง&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_ptc"] > 0) ? $row["T1_1_ptc"] : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_ptc"] > 0) ? $row["T1_ptc"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_ptc"] > 0) ? $row["T2_ptc"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?php echo $persen_ptc ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_ptc"] > 0) ? $row["T3_ptc"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_ptc"] > 0) ? $row["T4_ptc"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_ptc"] > 0) ? $row["T5_ptc"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_ptc"] > 0) ? $row["T6_ptc"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_ptc"] > 0) ? $row["T7_ptc"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_ptc"] > 0) ? $row["T8_ptc"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_ptc"] > 0) ? $row["T9_ptc"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_ptc"] > 0) ? $row["T10_ptc"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_ptc"] > 0) ? $row["T11_ptc"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_ptc"] > 0) ? $row["T12_ptc"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_ptc"] > 0) ? $row["T13_ptc"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_ptc > 0) ? $sum1_ptc : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_ptc > 0) ? $sum2_ptc : "") ?>&nbsp;</td>
    </tr> 
	  <!-- วังหว้า -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.วังหว้า&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_wgw"] > 0) ? $row["T1_1_wgw"] : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_wgw"] > 0) ? $row["T1_wgw"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_wgw"] > 0) ? $row["T2_wgw"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?php echo $persen_wgw ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_wgw"] > 0) ? $row["T3_wgw"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_wgw"] > 0) ? $row["T4_wgw"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_wgw"] > 0) ? $row["T5_wgw"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_wgw"] > 0) ? $row["T6_wgw"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_wgw"] > 0) ? $row["T7_wgw"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_wgw"] > 0) ? $row["T8_wgw"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_wgw"] > 0) ? $row["T9_wgw"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_wgw"] > 0) ? $row["T10_wgw"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_wgw"] > 0) ? $row["T11_wgw"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_wgw"] > 0) ? $row["T12_wgw"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_wgw"] > 0) ? $row["T13_wgw"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_wgw > 0) ? $sum1_wgw : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_wgw > 0) ? $sum2_wgw : "") ?>&nbsp;</td>
    </tr> 
	  <!-- ดงป่าคำ -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.ดงป่าคำ&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_dpk"] > 0) ? $row["T1_1_dpk"] : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_dpk"] > 0) ? $row["T1_dpk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_dpk"] > 0) ? $row["T2_dpk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?php echo $persen_dpk ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_dpk"] > 0) ? $row["T3_dpk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_dpk"] > 0) ? $row["T4_dpk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_dpk"] > 0) ? $row["T5_dpk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_dpk"] > 0) ? $row["T6_dpk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_dpk"] > 0) ? $row["T7_dpk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_dpk"] > 0) ? $row["T8_dpk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_dpk"] > 0) ? $row["T9_dpk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_dpk"] > 0) ? $row["T10_dpk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_dpk"] > 0) ? $row["T11_dpk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_dpk"] > 0) ? $row["T12_dpk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_dpk"] > 0) ? $row["T13_dpk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_dpk > 0) ? $sum1_dpk : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_dpk > 0) ? $sum2_dpk : "") ?>&nbsp;</td>
    </tr> 
	  <!-- บางลาย -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.บางลาย&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_bgl"] > 0) ? $row["T1_1_bgl"] : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_bgl"] > 0) ? $row["T1_bgl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_bgl"] > 0) ? $row["T2_bgl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?php echo $persen_bgl ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_bgl"] > 0) ? $row["T3_bgl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_bgl"] > 0) ? $row["T4_bgl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_bgl"] > 0) ? $row["T5_bgl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_bgl"] > 0) ? $row["T6_bgl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_bgl"] > 0) ? $row["T7_bgl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_bgl"] > 0) ? $row["T8_bgl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_bgl"] > 0) ? $row["T9_bgl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_bgl"] > 0) ? $row["T10_bgl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_bgl"] > 0) ? $row["T11_bgl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_bgl"] > 0) ? $row["T12_bgl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_bgl"] > 0) ? $row["T13_bgl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_bgl > 0) ? $sum1_bgl : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_bgl > 0) ? $sum2_bgl : "") ?>&nbsp;</td>
    </tr> 
	  <!-- ย่านยาว -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.ย่านยาว&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_yyo"] > 0) ? $row["T1_1_yyo"] : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_yyo"] > 0) ? $row["T1_yyo"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_yyo"] > 0) ? $row["T2_yyo"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?php echo $persen_yyo ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_yyo"] > 0) ? $row["T3_yyo"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_yyo"] > 0) ? $row["T4_yyo"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_yyo"] > 0) ? $row["T5_yyo"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_yyo"] > 0) ? $row["T6_yyo"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_yyo"] > 0) ? $row["T7_yyo"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_yyo"] > 0) ? $row["T8_yyo"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_yyo"] > 0) ? $row["T9_yyo"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_yyo"] > 0) ? $row["T10_yyo"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_yyo"] > 0) ? $row["T11_yyo"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_yyo"] > 0) ? $row["T12_yyo"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_yyo"] > 0) ? $row["T13_yyo"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_yyo > 0) ? $sum1_yyo : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_yyo > 0) ? $sum2_yyo : "") ?>&nbsp;</td>
    </tr> 
	  
  </tbody>
<?php
        
	$no++;
}//while
?>
</table>
</div>
</div>
</div>
</body>

