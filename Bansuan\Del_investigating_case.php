<?php
include '../Condb.php';  //PDo
include '../users.inc.php';
include '../Alert.php';

$id = $_GET['id'];
$ivnum = $_GET['ivnum'];

if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
// Store $ivnum in a session variable
$_SESSION['ivnum'] = $ivnum;

$acc = $user['account'];
$ip_address = $_SERVER['REMOTE_ADDR'];
$action = 'Del Investigating case'; // Update the action to reflect a delete action
if (isset($_SESSION['ivnum'])) {
    $deletedItemId = $_SESSION['ivnum'];
    $data = ['deleted_item_id' => $deletedItemId]; // Create a data array with the deleted item's ID
	$dataString = json_encode($data); // Encode the array into a JSON string
    log_activity($acc, $action, $dataString, $ip_address);

    // Clear the session variable after successful deletion if needed
    unset($_SESSION['ivnum']);
}


if($user['ua_delete_data'] != 100)
{
    echo '<script>alert("เฉพาะสิทธิ์ Admin เท่านั้น")</script>';
}

try{
$sql="DELETE FROM wm_tb_investigating_case WHERE ivc_cl_aid = :id ";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':id', $id);
    $result = $stmt->execute();

    if ($result) {
        $_SESSION['success'] = "Data has been deleted successfully";
        showSweetAlert("Delete successfully", "ลบข้อมูลเรียบร้อย");
        unset($_SESSION['success']); // Clear the session variable
        header("refresh:2; url=/Bansuan/index.php?rnd=' + Math.random() + '&page=investigating");
    } else {
        $_SESSION['error'] = "ลบข้อมูลไม่สำเร็จ";
        showSweetAlert("Failed to delete", "ลบข้อมูลไม่สำเร็จ");
        unset($_SESSION['success']); // Clear the session variable
        header("refresh:2; url=/Bansuan/index.php?rnd=' + Math.random() + '&page=investigating");
    }
} catch (PDOException $e) {
    echo 'Query Failed: ' . $e->getMessage();
}

$pdo = null;

?>