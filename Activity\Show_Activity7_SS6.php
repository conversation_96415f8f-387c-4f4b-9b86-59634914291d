<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';
include '../Head_image.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

//get month from select or current month
$month = isset($_GET['month']) ? $_GET['month'] : '10';
$year = isset($_GET['year']) ? $_GET['year'] : (date("m") < 10 ? date("Y")-1 : date("Y"));
//$station = isset($_GET['station']) ? $_GET['station'] : 6707;
//$station = 6707;   // สภ.บ้านสวน

// Get the year from the query string
$year = isset($_GET['year']) ? $_GET['year'] : date('Y');
// Calculate the start and end dates
$start_date = ($year - 1) . '-10-01';
$end_date = $year . '-09-30';

$limite_date = strtotime("$year-09-30");
$check_date = strtotime("$year-10-01");
$select_date = strtotime("$year-$month-01");

$current_month = $global_thai_month[0+$month];
$y2 = $year+543;

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<base target="_top">
<title>แบบ สส.6</title>
<link rel="stylesheet" href="/bootstrap/css/bootstrap-grid.css">
<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
</head>
    
<body>
<!--<p><img src="../Image/WatchmanDB3.jpg" width="100%" height="" alt=""/></p>-->
<div class=" h3 text-center  alert alert-success mb-4 mt-4 " role="alert" >แบบ สส.6 ผลการจับกุมตามหมายจับ ภาพรวม (รายเดือน) <br><br>ประจำปีงบประมาณ พ.ศ. &nbsp;<?= $y2 ?> &nbsp; <?= $name_station ?> </div>

<div>
    <table width="90%" border="0" cellspacing="1" cellpadding="1">
      <tbody>
        <tr>
          <td width="14%"><div align="left">
            &nbsp;&nbsp;<a href="/WM/index.php?rnd=<?= rand(); ?>&page=report" class="btn btn-warning btn-lg mb-4" >ย้อนกลับ</a></td>
          <td width="16%"><label>เลือกปีงบประมาณ </label>
            <select id="changeyear" name="changeyear" class="form-select col-8 d-lg-inline" style="background-color: #00D2FE; font-size: 16px" onChange="dates_change()" >
              <option value="" >เลือก</option>
              <?php
                    $cur_y = date("Y") + 2;
                    for($y=2019; $y<$cur_y; $y++) {
                        $sel = ($y == $year) ? "selected" : '';
                        $start_month = ($y == date("Y")) ? "10" : "01"; // October for current year, January otherwise
                        echo "<option value='$y' $sel> ". ($y + 543) ." </option>";
                    }
                ?>
            </select></td>
          <td width="39%">&nbsp;</td>
        </tr>
      </tbody>
    </table>
</div>
    
<div class="container" align="left" style="margin-left:auto">
<table width="99%" border="1" cellspacing="1" cellpadding="1" class="table-striped table-hover table-bordered">
  <tbody>
<?php
      
$sql = "SELECT
            CONCAT(YEAR(`wr_cl_date_finish`)+543) AS `year`,
            MONTH(`wr_cl_date_finish`) AS month,
            count(`wr_cl_date_finish`) AS count,
            sum(CASE WHEN wr_cl_status = 5 THEN `wr_cl_date_finish` ELSE 0 END) AS f
        FROM
            `wm_tb_warrant`
        WHERE 
            `station`=:station AND wr_cl_date_finish >= :start_date AND wr_cl_date_finish <= :end_date
        GROUP BY 
            MONTH(`wr_cl_date_finish`), YEAR(`wr_cl_date_finish`)
        ORDER BY
            wr_cl_date_finish ASC ";

      $stmt = $pdo->prepare($sql);
      $stmt->bindParam(':station', $station);
      $stmt->bindParam(':start_date', $start_date);
      $stmt->bindParam(':end_date', $end_date);
try{
    $stmt->execute();
}catch (PDOException $e) {
    echo '$sql Failed: '. $e->getMessage();
}
      
$result = $stmt->fetchAll(PDO::FETCH_ASSOC);

?>

    <tr>
      <td style="font-size: 22px; color: blue ;background: #42FF00">เดือน ปี</td>
      <td style="font-size: 22px; color: blue;background: #42FF00">จำนวน/ราย</td>
      <td style="font-size: 22px; color: blue;background: #42FF00">หมายเหตุ (ขาดอายุความ)</td>
    </tr>
      
<?php foreach ($result as $row): ?>
<?php

switch($row['month'])
{
    case "10": $row['month'] = "ตุลาคม"; break;
    case "11": $row['month'] = "พฤศจิกายน"; break;
    case "12": $row['month'] = "ธันวาคม"; break;
    case "1": $row['month'] = "มกราคม"; break;
    case "2": $row['month'] = "กุมภาพันธ์"; break;
    case "3": $row['month'] = "มีนาคม"; break;
    case "4": $row['month'] = "เมษายน"; break;
    case "5": $row['month'] = "พฤษภาคม"; break;
    case "6": $row['month'] = "มิถุนายน"; break;
    case "7": $row['month'] = "กรกฎาคม"; break;
    case "8": $row['month'] = "สิงหาคม"; break;
    case "9": $row['month'] = "กันยายน"; break;
}
      
?>
    <tr style="font-size: 18px">
      <td style="text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<?php echo $row['month']; ?>&nbsp;&nbsp;<?php echo $row['year']; ?>&nbsp;</td>
      <td style="text-align: center">&nbsp;<?php echo (($row['count'] > 0) ? $row['count']  : "") ?>&nbsp;</td>
      <td style="text-align: center">&nbsp;<?php echo (($row['f'] > 0) ? $row['f']  : "") ?>&nbsp;</td>
    </tr>
  </tbody>
<?php endforeach; ?>
</table>
    </div>
    <br>
    
&nbsp;&nbsp;<button class="btn btn-primary btn-lg mb-4" onClick="do_print()"> พิมพ์รายงาน </button> 
	<!--<button class="btn btn-primary btn-lg mb-4" onClick="if ($station === 6707) { do_print(); }">พิมพ์รายงาน</button>-->
    
</body>
<script>
function dates_change()  // คำสั่งเปลี่ยนเดือน ปี
{
   // var ms = $("#changemonth option:selected").val();
    var ys = $("#changeyear option:selected").val();
    window.location = "/Activity/Show_Activity7_SS6.php?&year=" + ys + "&rnd=" + Math.random();
}    
</script>
</html>
<!-- script สำหรับส่งไปพิมพ์รายงาน  -->
<script>
    
    function do_print()
    {
        var ys = $("#changeyear option:selected").val();
        var newW = window.open("/Activity/Show_Activity7_SS6_print.php?&year=" + ys + "&rnd=" + Math.random(), "_print", "menubar=no,titlebar=no,toolbar=no,status=no");
        newW.print();
        //window.print();
    }
    
</script>