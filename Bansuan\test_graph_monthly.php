<?php
// Step 1: Establish a database connection
/*$host = "localhost";
$username = "your_username";
$password = "your_password";
$dbname = "your_database";

$conn = mysqli_connect($host, $username, $password, $dbname);
if (!$conn) {
    die("Connection failed: " . mysqli_connect_error());
}
*/
include '../Condb.php';
include '../users.inc.php';

// Step 2: Fetch data for goals and performance for each month
$query = "SELECT MONTH(g_date) as month, SUM(goals) as goals, SUM(performance) as performance FROM test_graph_table GROUP BY MONTH(g_date)";
$result = mysqli_query($conn, $query);

//echo $query;
// Step 3: Loop through the result set and store the values in two separate arrays
$goals_array = array();
$performance_array = array();
while ($row = mysqli_fetch_assoc($result)) {
    $goals_array[] = $row['goals'];
    $performance_array[] = $row['performance'];
}

// Step 4: Use a charting library to create a bar graph
// Here's an example using Google Charts
?>

<html>
  <head>
    <script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
    <script type="text/javascript">
      google.charts.load('current', {'packages':['corechart']});
      google.charts.setOnLoadCallback(drawChart);

      function drawChart() {
        var data = google.visualization.arrayToDataTable([
          ['Month', 'Goals', 'Performance'],
          ['Jan', <?php echo $goals_array[0]; ?>, <?php echo $performance_array[0]; ?>],
          ['Feb', <?php echo $goals_array[1]; ?>, <?php echo $performance_array[1]; ?>],
          ['Mar', <?php echo $goals_array[2]; ?>, <?php echo $performance_array[2]; ?>],
          // Add more months as needed
        ]);

        var options = {
          title: 'Monthly Goals vs Performance',
          chartArea: {width: '50%'},
          hAxis: {
            title: 'Goals and Performance',
            minValue: 0
          },
          vAxis: {
            title: 'Month'
          }
        };

        var chart = new google.visualization.BarChart(document.getElementById('chart_div'));
        chart.draw(data, options);
      }
    </script>
  </head>
  <body>
    <div id="chart_div" style="width: 100%; height: 500px;"></div>
  </body>
</html>

<?php
// Step 5: Close the database connection
mysqli_close($conn);
?>
