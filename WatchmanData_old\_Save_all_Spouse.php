<?php
include '../Condb.php';
include '../users.inc.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// เมื่อมีการใช้ ตัวแปร Session จะต้องเปิดก่อน
//session_start();  // เปิดตัวแปร session
/*if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
*/

// $_GET[''] >> url
// $_POST[] >> form
// $_FILES[] >> form >> type file

// ADD ?
// $sp_cl_aid = $_POST[ 'sp_cl_aid' ];

$sp_cl_idcard = $_POST[ 'sp_cl_idcard' ];
$sp_cl_idcard_spouse = $_POST[ 'sp_cl_idcard_spouse' ];
$sp_cl_prefix = $_POST[ 'sp_cl_prefix' ];
$sp_cl_sex = $_POST[ 'sp_cl_sex' ];
$sp_cl_name = $_POST[ 'sp_cl_name' ];
$sp_cl_surname = $_POST[ 'sp_cl_surname' ];
$sp_cl_nickname = $_POST[ 'sp_cl_nickname' ];
$sp_cl_birthday2 = $_POST[ 'sp_cl_birthday2' ];
$sp_cl_father = $_POST[ 'sp_cl_father' ];
$sp_cl_father_pid = $_POST[ 'sp_cl_father_pid' ];
$sp_cl_mother = $_POST[ 'sp_cl_mother' ];
$sp_cl_mother_pid = $_POST[ 'sp_cl_mother_pid' ];
$sp_cl_spouse = $_POST[ 'sp_cl_spouse' ];

//$sp_cl_birthday2 = thai_date_2_eng($sp_cl_birthday);

// save Image
$file1 = $_FILES[ 'sp_cl_image' ][ 'tmp_name' ];
if ( is_uploaded_file( $file1 ) ) {
    /*
  $sp_cl_image = "/policeinnopolis/uploaded/Image1/" . basename( $_FILES[ 'sp_cl_image' ][ 'name' ] );	
  move_uploaded_file( $file1, "../" . $sp_cl_image );  
  */
    $sp_cl_image = "/policeinnopolis/uploaded/Image1/" . basename( $_FILES[ 'sp_cl_image' ][ 'name' ] );	    
    // file + ext    
    $ext = strrchr($sp_cl_image, ".");    
    $sp_cl_image = "../policeinnopolis/uploaded/Image1/File_" . time() . $ext; // file+time+ext        
    move_uploaded_file( $file1, $sp_cl_image ); 
    
} else {
  $sp_cl_image = '';
}


// $sql2 >> insert ไปลงตาราง >> personal
// check exists data in personal เช็คว่ามีข้อมูลในตาราง Personal หรือยัง ถ้ามีแล้ว ไม่ต้องทำอะไร
$sql4 = "SELECT * FROM wm_tb_personal WHERE ps_cl_idcard='$sp_cl_idcard_spouse' ";
//echo $sql3;
$res4 = mysqli_query($conn, $sql4);
// มีข้อมูลแล้ว
if($row4 = mysqli_fetch_array($res4)) {
   
}
// เปนข้อมูลใหม่ >>
else {
    $sql4 = "INSERT INTO wm_tb_personal (ps_cl_idcard, ps_cl_prefix, ps_cl_sex, ps_cl_name, ps_cl_surname, ps_cl_nickname, ps_cl_birthday2, ps_cl_father, ps_cl_father_pid, ps_cl_mother, ps_cl_mother_pid,  ps_cl_image)
    VALUES('$sp_cl_idcard_spouse', '$sp_cl_prefix', '$sp_cl_sex', '$sp_cl_name', '$sp_cl_surname', '$sp_cl_nickname', '$sp_cl_birthday2', '$sp_cl_father', '$sp_cl_father_pid', '$sp_cl_mother', '$sp_cl_mother_pid', '$sp_cl_image') ";
    mysqli_query($conn, $sql4);
    
   // echo( $sql3 );
   // echo mysqli_error($conn);
}
//


$sql = "INSERT INTO wm_tb_spouse(sp_cl_idcard, sp_cl_idcard_spouse, sp_cl_prefix, sp_cl_sex, sp_cl_name, sp_cl_surname, sp_cl_nickname, sp_cl_birthday2, sp_cl_father, sp_cl_father_pid, sp_cl_mother, sp_cl_mother_pid, sp_cl_spouse, sp_cl_image) VALUES('$sp_cl_idcard', '$sp_cl_idcard_spouse', '$sp_cl_prefix', '$sp_cl_sex', '$sp_cl_name', '$sp_cl_surname', '$sp_cl_nickname', '$sp_cl_birthday2', '$sp_cl_father', '$sp_cl_father_pid','$sp_cl_mother', '$sp_cl_mother_pid', '$sp_cl_spouse', '$sp_cl_image') ";
$result=mysqli_query($conn,$sql);
if($result){
	echo "<script>alert('บันทึกข้อมูลเรียบร้อย');</script>";	
}else{
	echo "<script>alert(\"บันทึกข้อมูลไม่สำเร็จ\\n" . print_r(mysqli_error($conn), true) . "\");</script>";
}

// เก็บข้อมูล Action
$inputs = str_replace("'", "", compact_array($_POST));
$prev = "";//str_replace("'", "", implode(',', $row_0));

$acc = $user['account'];
$sql3 = "INSERT wm_tb_user_action (at_cl_user,at_cl_date,at_cl_action,at_cl_input,at_cl_prev) VALUES('$acc',CURRENT_TIMESTAMP,'Add new Spouse {$sp_cl_idcard}', '$inputs', '{$prev}')";
mysqli_query($conn, $sql3);
//

mysqli_close($conn);

echo "<script>window.location='Show_All.php?pcid=" . $sp_cl_idcard . "&page=spouse';</script>";
?>

