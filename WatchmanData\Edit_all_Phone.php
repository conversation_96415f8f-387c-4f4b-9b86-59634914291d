<?php
include "../Condb.php"; //PDO
include '../users.inc.php';
include '../right_user.php';


// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

$id = $_GET['id'];
$pcid = $_GET['pcid'];

$sql = "SELECT * FROM wm_tb_phone WHERE ph_cl_aid = :id ";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':id',$id);
    $stmt->execute();
    $row = $stmt->fetch(PDO::FETCH_ASSOC);

$people_name = '';
if($row) 
{
	$pid2 = $row["ph_cl_idcard"];
	//
	$query1 = "SELECT ps_cl_prefix, ps_cl_name, ps_cl_surname, ps_cl_image FROM wm_tb_personal WHERE ps_cl_idcard = :pid2 ";
        $stmt = $pdo->prepare($query1);
        $stmt->bindParam(':pid2',$pid2);
        $stmt->execute();
        $row1 = $stmt->fetch(PDO::FETCH_ASSOC);
		
        $people_name = $row1['ps_cl_prefix'] . $row1['ps_cl_name'] . ' ' . $row1['ps_cl_surname'];
        $people_image = $row1["ps_cl_image"];
        if($people_image != '') {
            $people_image = "<img src='{$people_image}' height='50'> ";
        }
}
?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>แก้ไขข้อมูลโทรศัพท์</title>
<script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/policeinnopolis/bootstrap/css/bootstrap.rtl.min.css">
	
<!--<link rel="stylesheet" href="http://code.jquery.com/ui/1.9.1/themes/base/jquery-ui.css" />-->
<!--<script src="http://code.jquery.com/jquery-1.8.2.js"></script>-->
<!--<script src="/policeinnopolis/js/jquery-ui.js"></script>-->
	
<link rel="stylesheet" href="../datepicker_th/jquery.datetimepicker.css" type="text/css">
<script src="../datepicker_th/jquery-1.8.3.min.js"></script>  
<script src="../datepicker_th/jquery.datetimepicker.full.js"></script>
	
<style type="text/css">
        body,td,th {
            color: #1203F8;
        }
            .body{
                font-size: 18px;
            }
        input::placeholder {
          color: red;
        }
        .form-control::placeholder {
          color: sandybrown;
        }
	.callcenter {
		width: 20px;
		height: 20px;
	}
</style>
<script>
window.addEventListener('DOMContentLoaded', function() {
  // Get references to the checkboxes
  var checkbox1 = document.getElementById('callcenter1');
  var checkbox2 = document.getElementById('callcenter2');
  
  // Replace this with your logic to determine the default value (1 or 2)
  var defaultValue = 1; // Change this value based on your data
  
  if (defaultValue === 1) {
    checkbox1.checked = true;
    checkbox2.checked = false;
  } else if (defaultValue === 2) {
    checkbox1.checked = false;
    checkbox2.checked = true;
  }
  
  // Add event listeners to checkboxes to manage their state
  checkbox1.addEventListener('change', function() {
    if (checkbox1.checked) {
      checkbox2.checked = false;
    }
  });
  
  checkbox2.addEventListener('change', function() {
    if (checkbox2.checked) {
      checkbox1.checked = false;
    }
  });
});
</script>
</head>

<body>
	<div class="container">
	<div class="row">
		<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> แก้ไขข้อมูลโทรศัพท์ของ <?= $people_name ?> <?= $people_image ?> </div>
	<form action="Save_all_Phone.php" method="POST" enctype="multipart/form-data" class="body">	
		<label>ลำดับ</label>
		<input name="ph_cl_aid" type = "text" class="form-control" value= "<?=$row['ph_cl_aid']?>" hidden ="hidden"  >
		<label>เลขบัตรคนใช้เบอร์</label>
		<input name="ph_cl_idcard" type = "text" class="form-control" value= "<?=$row['ph_cl_idcard']?>" readonly="readonly"  >
		<label>ข้อมูลเจ้าของเบอร์ </label>		
		<input name="AA" type = "text" class="form-control" value= "<?= $people_name ?>" readonly="readonly" >
		<label>หมายเลขโทรศัพท์</label>
		<input type = "text" name = "ph_cl_phone" class="form-control" value= "<?=$row['ph_cl_phone']?>" placeholder="กรอกหมายเลขโทรศัพท์ 10 หลัก" >
		<label>เครือข่ายโทรศัพท์</label>
		<input type = "text" name = "ph_cl_operators" class="form-control" value= "<?=$row['ph_cl_operators']?>" placeholder="กรอกข้อมูลเครือข่าย เช็คโดย DTAC กด *812*เบอร์# โทรออก" >
		<label>ประเภท</label>
		<input type = "text" name = "ph_cl_type" class="form-control" value= "<?=$row['ph_cl_type']?>" placeholder="กรอกข้อมูลประเภท รายเดือน/เติมเงิน" >
		<label>วันจดทะเบียน</label>
		<input type = "text" name = "ph_cl_regist" class="form-control" value= "<?=$row['ph_cl_regist']?>" placeholder="กรอกข้อมูลวันที่จดทะเบียน จำนวนวันที่ใช้งาน"  >
		<label>อายุการใช้งาน</label>
		<input type = "text" name = "ph_cl_status" class="form-control" value= "<?=$row['ph_cl_status']?>" placeholder="กรอกข้อมูลสถานะใช้งาน อายุ เปิด ปิด ระงับ ยกเลิก โอน การใช้บริการอื่นๆ" >
		<label>ยี่ห้อ รุ่น สี</label>
		<input type = "text" name = "ph_cl_brand" class="form-control" value= "<?=$row['ph_cl_brand']?>" placeholder="กรอกข้อมูลยี่ห้อโทรศัพท์ รุ่น สี" >
		<label>เลข IMEI 1</label>
		<input type = "text" name = "ph_cl_IMEI" class="form-control" value= "<?=$row['ph_cl_IMEI']?>" placeholder="กรอกข้อมูลเลข IMEI เช็คอีมี่โดยกด *#06#" >
		<label>เลข IMEI 2</label>
		<input type = "text" name = "ph_cl_IMEI2" class="form-control" value= "<?=$row['ph_cl_IMEI2']?>" placeholder="กรอกข้อมูลเลข IMEI 2 (ถ้ามี)"  >
		<label>เลข IMSI</label>
		<input type = "text" name = "ph_cl_IMSI" class="form-control" value= "<?=$row['ph_cl_IMSI']?>" placeholder="กรอกข้อมูลเลข IMSI ของซิมการ์ด" >
		<label>ชื่อคนจดทะเบียน</label>
		<input type = "text" name = "ph_cl_register_name" class="form-control" value= "<?=$row['ph_cl_register_name']?>" placeholder="กรอกข้อมูล ชื่อคนจดทะเบียน" >
		<label>บัตรคนจดทะเบียน</label>
		<input type = "text" name = "ph_cl_register_idcard" class="form-control" value= "<?=$row['ph_cl_register_idcard']?>" placeholder="กรอกข้อมูล เลขบัตรคนจด" >
		<label>พาสสปอร์ตคนจดทะเบียน</label>
		<input type = "text" name = "ph_cl_register_passport" class="form-control" value= "<?=$row['ph_cl_register_passport']?>" placeholder="กรอกข้อมูล เลขพาสสปอร์ตคนจด"  >
		<label>เบส (LAC,CI)</label>
		<input type = "text" name = "ph_cl_base" class="form-control" value= "<?=$row['ph_cl_base']?>" placeholder="กรอกข้อมูล เบส (LAC,CI)" >
		<label>ที่อยู่/ตำแหน่งเบส</label>
		<input type = "text" name = "ph_cl_base_location" class="form-control" value= "<?=$row['ph_cl_base_location']?>" placeholder="กรอกข้อมูล ที่อยู่/ตำแหน่งเบส" >
        <label>ที่อยู่ส่งบิล</label>
		<textarea class="form-control" id="ph_cl_billing" name="ph_cl_billing" rows="5" placeholder="กรอกข้อมูล ที่อยู่ส่งบิล" ><?php echo $row['ph_cl_billing'] ?></textarea>
		<label>พร้อมเพย์</label>
		<input type = "text" name = "ph_cl_promtpay" value= "<?=$row['ph_cl_promtpay']?>" class="form-control" placeholder="กรอกข้อมูล พร้อมเพย์" >
		<label>ข้อมูลพัสดุ</label>
		<textarea class="form-control" id="ph_cl_logistic" name="ph_cl_logistic"  rows="5" placeholder="กรอกข้อมูล พัสดุ" ><?php echo $row['ph_cl_logistic'] ?></textarea>
		<label>ทรูมันนี่วอลเล็ต</label>
		<input type = "text" name = "ph_cl_truewallet" value= "<?=$row['ph_cl_truewallet']?>" class="form-control" placeholder="กรอกข้อมูล ทรูวอลเล็ต" >
		<label>ออลเมมเบอร์</label>
		<input type = "text" name = "ph_cl_allmember" value= "<?=$row['ph_cl_allmember']?>" class="form-control" placeholder="กรอกข้อมูล ออลเมมเบอร์" >
		<label>Get contact</label>
		<input type = "text" name = "ph_cl_getcontact" value= "<?=$row['ph_cl_getcontact']?>" class="form-control" placeholder="กรอกข้อมูล Get contact" >
		<label>ITEC</label>
		<input type = "text" name = "ph_cl_ITEC" value= "<?=$row['ph_cl_ITEC']?>" class="form-control" placeholder="กรอกข้อมูล ITEC" ><br>
		<b>เป็นเบอร์ของแก๊ง Call center หรือไม่</b>
		&nbsp;&nbsp;&nbsp;&nbsp;<label><input type="checkbox" name="callcenter" id="callcenter1" class="callcenter" value="1" <?php echo ($row['callcenter'] == 1) ? 'checked' : ''; ?> > ไม่เป็น </label>&nbsp;&nbsp;&nbsp;&nbsp;
		<label><input type="checkbox" name="callcenter" id="callcenter2" class="callcenter" value="2" <?php echo ($row['callcenter'] == 2) ? 'checked' : ''; ?> > เป็น </label>
		<br><br>
        <label>วันตรวจสอบ <span style="color: #F90004">* จำเป็น</span> </label>
        <p><input type="text" name="ph_ch_date_inspect" id="datepicker" value= "<?=$row['ph_ch_date_inspect']?>" class="form-control" placeholder="เลือกวันที่ตรวจสอบข้อมูล" autocomplete="off" Required></p>
        <script type="text/javascript"> 
            $(function(){

                $.datetimepicker.setLocale('th'); // ต้องกำหนดเสมอถ้าใช้ภาษาไทย และ เป็นปี พ.ศ.
                // กรณีใช้แบบ input
                $("#datepicker").datetimepicker({
                    timepicker:false,
                    format:'Y-m-d',  // กำหนดรูปแบบวันที่ ที่ใช้ เป็น 00-00-0000			
                    lang:'th',  // ต้องกำหนดเสมอถ้าใช้ภาษาไทย และ เป็นปี พ.ศ.
                    onSelectDate:function(dp,$input){
                        var yearT=new Date(dp).getFullYear()-0;  
                        var yearTH=yearT;  // ถ้าจะให้แสดงเป็น พ.ศ. ให้เป็น yearT+543
                        var fulldate=$input.val();
                        var fulldateTH=fulldate.replace(yearT,yearTH);
                        $input.val(fulldateTH);
						
						// Get the result element and update the displayed year
						var result = document.getElementById('result');
						result.textContent = 'พ.ศ. ' + (yearTH+543);
                    },
                });       
                // กรณีใช้กับ input ต้องกำหนดส่วนนี้ด้วยเสมอ เพื่อปรับปีให้เป็น ค.ศ. ก่อนแสดงปฏิทิน
                $("#datepicker").on(function(e){
                    var dateValue=$(this).val();
                    if(dateValue!=""){
                            var arr_date=dateValue.split("-"); // ถ้าใช้ตัวแบ่งรูปแบบอื่น ให้เปลี่ยนเป็นตามรูปแบบนั้น
                            // ในที่นี้อยู่ในรูปแบบ 00-00-0000 เป็น d-m-Y  แบ่งด่วย - ดังนั้น ตัวแปรที่เป็นปี จะอยู่ใน array
                            //  ตัวที่สอง arr_date[2] โดยเริ่มนับจาก 0 
                            dateValue=dateValue.replace(arr_date[0],yearT);
                            $(this).val(dateValue);													
                    }		
                });
            });
            </script>
        <!-- สำหรับ วันที่ แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ -->
        
		<label>หมายเหตุ</label>
		<textarea class="form-control" id="ph_cl_remark" name="ph_cl_remark" rows="5" placeholder="ระบุข้อมูลแหล่งที่มา หรืออื่น ๆที่เกี่ยวข้อง" ><?php echo $row['ph_cl_remark'] ?></textarea>
        <br>
		<p>
    	<input type="submit" value="Update" class="btn btn-success" >
		<td> <a href="Show_All.php?pcid=<?= $pcid ?>&page=phone" class="btn btn-warning" >ยกเลิก</a> </td>
 		</p>
	</form>
	</div>
	</div>
	</div>
<!--<script src="/js/datetimepicker2.5.1/jquery.ui.datepicker-th.js" type="text/javascript"></script> -->   
</body>
</html>
