<?php
include '../Condb.php';
include '../users.inc.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// เมื่อมีการใช้ ตัวแปร Session จะต้องเปิดก่อน
//session_start();  // เปิดตัวแปร session
/*if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
*/

// สำหรับเลือก จังหวัด อำเภอ ตำบล
include("../config.inc.php");
include("../classes/class.database.inc.php");

$id = $_GET['id'];
$sql = "SELECT * FROM wm_tb_crimes_record WHERE aid_crimes_record = '$id' ";
$result = mysqli_query($conn,$sql);
$row = mysqli_fetch_array($result);

$conn2 = get_connection(); // connect to mySQLDB

//$province = isset($_GET['province']) ? $_GET['province'] : 64; // sukhothai
//$amphure = isset($_GET['amphure']) ? $_GET['amphure'] : 0; // sukhothai
//$tambon = isset($_GET['tambon']) ? $_GET['tambon'] : 0; // sukhothai

// สำหรับเลือกสถานีตำรวจทั่วประเทศ
$region = isset($_GET['region']) ? $_GET['region'] : 0; // sukhothai
$provincial = isset($_GET['provincial']) ? $_GET['provincial'] : 0; // sukhothai
$station = isset($_GET['station']) ? $_GET['station'] : 0; // sukhothai

$region = 0;
$provincial = 0;
$station = 0;
//print_r($id);
?>


<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Edit Person Crimes Record</title>
<script src="/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 18px;
    }
input::placeholder {
  color: red;
}
.form-control::placeholder {
  color: sandybrown;
}
</style>
<script src="/jquery-ui-1.12.1/jquery-ui.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/jquery-ui-1.12.1/jquery-ui.css">
<link rel="stylesheet" href="/js/datetimepicker2.5.1/jquery.datetimepicker.min.css">
<script src="/js/datetimepicker2.5.1/jquery.datetimepicker.full.js" type="text/javascript"></script>    
<script src="/js/utils.js?<?= rand() ?>" type="text/javascript"></script> 
</head>

<body>
	
	<div class="container">
	<div class="row">
	<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> แก้ไขข้อมูล สถานที่ท้องถิ่น</div>
	<form action="Save_place_crimes_record_Edit.php" method="POST" enctype="multipart/form-data" class="body">
        
        <label hidden ="hidden">ลำดับ</label>
		<input type = "text" name = "aid_crimes_record" class="form-control" value= "<?= $row['aid_crimes_record']?>" hidden ="hidden" >
        
        <br>
        <label>สถานีตำรวจ <span style="color: #F90004">* จำเป็น</span></label><br>
        <!-- เลือกสถานีตำรวจ -->
        <label> บช. </label><br>
        <select name="pol_st_crimes_region" id="pol_st_crimes_region" onChange="do_region_change()" class="form-control" >
                <option value="">&lt;&lt; เลือก บช. &gt;&gt;</option>
                <?php
                 $region = $row['pol_st_crimes_region']; // <<ดึงข้อมูลจาก ตารางมา row ซึ่งเป็นคนละตัวกับ row_re
                //------------------------------------
			  	$res_re = $conn2->query("SELECT * FROM `wm_tb_police_region` ORDER BY `wm_tb_police_region`.`id_region` ASC");
			     $selected = '';
			     while($row_re = $conn2->fetch_row($res_re)) 
			     {
				  $code_region = $row_re['code_region'];
				  $name_region = $row_re['name_region'];
				  // set default provionce to 64 >> sukhothai
				  if($code_region == $region) {
					  $selected = 'selected';
				  }
				  else {
					  $selected = '';
				  }
				  //
				  echo "<option value='$code_region' $selected> $name_region </option>\n";
			     }
			  
			  ?>
              </select>
        
        <label> บก. </label><br>
        <select name="pol_st_crimes_provincial" id="pol_st_crimes_provincial" onChange="do_provincial_change()" class="form-control" >
        <option value="0">&lt;&lt; เลือก บก. &gt;&gt;</option>
                <?php
                if($region > 0)
                {
                    $provincial = $row['pol_st_crimes_provincial']; // <<
                    //-------------------------------------
                    $res_prov = $conn2->query("SELECT * FROM `wm_tb_police_provincial` WHERE `region_code`='$region' ORDER BY `wm_tb_police_provincial`.`wm_PID` ASC");
                    $selected = '';
                  while($res_prov = $conn2->fetch_row($res_prov)) 
                  {
                      $provincial_code = $res_prov['provincial_code']; // 
                      $provincial = $res_prov['provincial'];
                      // set default provionce to 64 >> sukhothai
                      if($provincial_code == $provincial) {
                          $selected = 'selected';
                      }
                      else {
                          $selected = '';
                      }
                      //
                      echo "<option value='$provincial_code' $selected> $provincial </option>\n";
                  }
                }	

                ?>
              </select>
        
        
         <label> สน./สภ. </label>
        <br>
        <select name="pol_st_crimes_record" id="pol_st_crimes_record" class="form-control" >
                <option value="0">&lt;&lt; เลือก สน./สภ. &gt;&gt;</option>
                <?php
	           if($provincial > 0)
                {
                    $station = $row['pol_st_crimes_record'];
                   //------------------------------------------
                    $res_st = $conn2->query("SELECT * FROM `wm_tb_police_station2` WHERE provincial_code='$provincial_code' ORDER BY `districts`.`station_name` ASC");
                    $selected = '';
                  while($row_st = $conn2->fetch_row($res_st)) 
                  {
                      $station_code = $row_st['station_code']; // 
                      $station_name = $row_st['station_name'];
                      // set default provionce to 64 >> sukhothai
                      if($station_code == $station) {
                          $selected = 'selected';
                      }
                      else {
                          $selected = '';
                      }
                      //
                      echo "<option value='$station_code' $selected> $station_name </option>\n";
                  }
                }	
                ?>                                                                     
              </select><br>
        
        <b>ข้อมูลที่รายงาน</b><br>
        <label>สถานที่ทั่วไป</label>
        <input type = "text" name = "place_gen_report" class="form-control" value= "<?=$row['place_gen_report']?>" placeholder="ระบุจำนวนสถานที่ทั่วไปที่รายงาน"  >
        
        <label>สถานเกี่ยวข้องกับอาชญากรรม</label>
        <input type = "text" name = "place_crimes_report" class="form-control" value= "<?=$row['place_crimes_report']?>" placeholder="ระบุจำนวนสถานที่เกี่ยวข้องกับอาชญากรรมที่รายงาน"  >
        
        <b>ข้อมูลจากระบบ CRIMES</b><br>
        <label>สถานที่ทั่วไป</label>
        <input type = "text" name = "place_gen_record" class="form-control" value= "<?=$row['place_gen_record']?>" placeholder="ระบุจำนวนสถานที่ทั่วไป ในระบบ CRIMES"  >
        
        <label>สถานที่เกี่ยวข้องกับอาชญากรรม</label>
        <input type = "text" name = "place_crimes_record" class="form-control" value= "<?=$row['place_crimes_record']?>" placeholder="ระบุจำนวนสถานที่เกี่ยวข้องกับอาชญากรรม ในระบบ CRIMES"  >
        
        <b>ความสมบูรณ์ ในระบบ CRIMES</b><br>
        <label>ความสมบูรณ์ในการบันทึกข้อมูลในระบบ CRIMES</label>
        <input type = "text" name = "record_complete_p" class="form-control" value= "<?=$row['record_complete_p']?>" placeholder="ความสมบูรณ์ในการบันทึกข้อมูลในระบบ CRIMES ระบุเป็น %"  >
        
        <label>วันที่บันทึก</label>
		<input type = "text" name="date_record" class="form-control datepicker" value= "<?=$row['date_record']?>" placeholder="ระบุวันที่" autocomplete="off" Required>
        
        <div class="mb-3">
             <label for="formFileMultiple" class="form-label">รูปภาพหน้าจอ CRIMES ข้อมูลสถานที่</label>
             <input class="form-control" type="file" id="image2" name="image2" multiple>
        </div>
        
        <br>
        <p>
            <input type="submit" value="บันทึกข้อมูล" class="btn btn-success" >
            <td> <a href="/Activity/Show_Activity1.php" class="btn btn-warning" >ยกเลิก</a> </td>
        </p>
            <br>
            <br>
        </form>
        </div>
        </div>
        </div>
<script src="/js/datetimepicker2.5.1/jquery.ui.datepicker-th.js" type="text/javascript"></script> 
<script>
// ชื่อ >> value >> set selct auto (สคลิปตรวจสอบ Selected) จำเป็นต้องก็อปปี้ <script src="/jQuery/jquery-3.5.1.min.js" type="text/javascript">
function auto_select(sid, value)
{
    $('#' + sid).val(value);
}

$(document).ready(function() {
<?php
    echo "auto_select('gc_cl_place', '{$row['gc_cl_place']}');\n";
    echo "auto_select('gc_cl_gen_place_type', '{$row['gc_cl_gen_place_type']}');\n";
    echo "auto_select('gc_cl_recorder', '{$row['gc_cl_recorder']}');\n";
    echo "auto_select('gc_cl_police_station', '{$row['gc_cl_police_station']}');\n";
     echo "auto_select('pol_st_crimes_region', '{$row['pol_st_crimes_region']}');\n";
     echo "auto_select('pol_st_crimes_provincial', '{$row['pol_st_crimes_provincial']}');\n";

?>
    });
    
// ฟังก์ชั่น เปลี่ยนภาค เป็น จังหวัด
function do_region_change()
{
	var sel_region = document.getElementById("pol_st_crimes_region");		
	var code_region = sel_region.options[sel_region.selectedIndex].value;

	$.get("../ajax/get_provincial.php?region=" + code_region, 
		  function(data, status){
				//alert("Data: " + data + "\nStatus: " + status);
				// jQuery style						
				$('#pol_st_crimes_provincial').find('option').remove().end();
				var data = $.trim(data);
				if(data == '') {
					alert("ไม่พบข้อมูล บก. !");
					return;
				}

					//.append('<option value="whatever">text</option>').val('whatever');
				var datas = data.split(';');
				for(var i=0; i<datas.length; i++) {
					if(datas[i] == '') continue;
					datas[i] = datas[i].split(':');
					var code_region = datas[i][0];
					var name_region = datas[i][1];
					$('#pol_st_crimes_provincial').append('<option value="'+ code_region+'">' + name_region + '</option>');
				}
                // Trigger the change event on the amphur dropdown so the tambon dropdown will be populated     
                $('#pol_st_crimes_provincial').trigger('change');
		});
}
    //เลือก สถานี จากจังหวัด
function do_provincial_change()
{
	var sel_provincial = document.getElementById("pol_st_crimes_region");		
	var provincial_code = sel_provincial.options[sel_provincial.selectedIndex].value;
    
    var sel_provincial2 = document.getElementById("pol_st_crimes_provincial");		
	var station_code = sel_provincial2.options[sel_provincial2.selectedIndex].value;

	$.get("../ajax/get_station.php?region=" + provincial_code + "&provincial=" + station_code, 
			  function(data, status){
					//alert("Data: " + data + "\nStatus: " + status);
					// jQuery style						
					$('#pol_st_crimes_record').find('option').remove().end();
					var data = $.trim(data);
					if(data == '') {
						alert("ไม่พบข้อมูลสถานี !");
						return;
					}

						//.append('<option value="whatever">text</option>').val('whatever');
					var datas = data.split(';');
					for(var i=0; i<datas.length; i++) {
						if(datas[i] == '') continue;
						datas[i] = datas[i].split(':');
						var station_code = datas[i][0];
						var station_name = datas[i][1];
						$('#pol_st_crimes_record').append('<option value="'+ station_code+'">' + station_name + '</option>');
					}
			});
}
</script>
</body>
</html>