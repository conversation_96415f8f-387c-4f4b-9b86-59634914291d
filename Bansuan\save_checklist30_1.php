<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../Alert.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);


$pcid = $_GET['pcid'];
//echo '<pre>';
//print_r($_POST);
//echo '</pre>';
//
//echo '<hr>';
//
//$pcid = $_GET['pcid'];
//echo '<pre>';
//var_dump($_POST);
//echo '</pre>';
//
//exit();

$cl_aid = $_POST['cl_aid'];
$cl_detective = $_POST['cl_detective'];
$cl_date = $_POST['cl_date'];
$cl_idcard = $_POST['cl_idcard'];
$cl_checklist = $_POST['cl_checklist'];

//ตรวจสอบก่อนว่า ในฐานข้อมูลมีเลขบัตรนี้หรือยัง
$sql ="SELECT * FROM wm_tb_warrant_checklist30 WHERE cl_idcard = :cl_idcard ";
    $stmt1 = $pdo->prepare($sql);
    $stmt1->bindParam(':cl_idcard', $cl_idcard);
try{
    $stmt1->execute();
}catch (PDOException $e) {
    echo '$sql Failed: '. $e->getMessage();
}

$row1 = $stmt1->fetch(PDO::FETCH_ASSOC);
if($row1) {
    // ถ้ามีแล้ว ให้ update
    $cl_aid = $row1['cl_aid'];
    $sql = "UPDATE wm_tb_warrant_checklist30 SET ".
            "cl_idcard = :cl_idcard, " .
            "cl_checklist = :cl_checklist, " .
            "cl_detective = :cl_detective, " .
            "cl_date = :cl_date " .
            "WHERE cl_aid = :cl_aid ";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':cl_aid', $cl_aid);
        $stmt->bindParam(':cl_idcard', $cl_idcard);
        $stmt->bindParam(':cl_checklist', $cl_checklist);
        $stmt->bindParam(':cl_detective', $cl_detective);
        $stmt->bindParam(':cl_date', $cl_date);
}
else {
    // ถ้ายังไม่มี ให้ insert
    $sql = "INSERT INTO wm_tb_warrant_checklist30 (cl_idcard, cl_checklist, cl_detective, cl_date) 
    VALUES(:cl_idcard, :cl_checklist, :cl_detective, :cl_date) ";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':cl_idcard', $cl_idcard);
        $stmt->bindParam(':cl_checklist', $cl_checklist);
        $stmt->bindParam(':cl_detective', $cl_detective);
        $stmt->bindParam(':cl_date', $cl_date);
}
try{
    $result = $stmt->execute();
}catch (PDOException $e) {
    echo $e->getMessage();
}
 
$ip_address = $_SERVER['REMOTE_ADDR'];
$action = "Save checklist30+2 wanted : $cl_idcard"; // You can set the action to a suitable value
$data = str_replace("'", "", compact_array($_POST));
log_activity($acc, $action, $data, $ip_address);


if ($result) {
    $_SESSION['success'] = "Data has been inserted succesfully";
    showSweetAlert("Saved successfully", "บันทึกข้อมูลเรียบร้อย");
    unset($_SESSION['success']); // Clear the session variable
    header("refresh:2; url=/Bansuan/Checklist_wanted30_1.php?pcid={$pcid}");
} else {
    $_SESSION['error'] = "Error";
    showSweetAlert('Error', "บันทึกข้อมูล ไม่สำเร็จ", 'error');
    unset($_SESSION['error']); // Clear the session variable
    header("refresh:2; url=/Bansuan/Checklist_wanted30_1.php?pcid={$pcid}");
}

$pdo = null;

?>