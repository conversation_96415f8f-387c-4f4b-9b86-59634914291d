<?php
include '../Condb.php';

echo '<pre>';
print_r($_POST);
echo '</pre>';

echo '<hr>';
//exit;
//$aid = $_POST[ 'aid' ];
$testdate5 = $_POST['testdate5'];
// แปลงปีในเซฟลงฐานข้อมูลให้ถูกต้อง
// convert Thai dates to eng
if($testdate5 != '') {
	if(strpos($testdate5, '-') > 0) {
    	$dates = explode('-', $testdate5);	// d/m/y  => y-m-d
	}
	elseif(strpos($testdate5, '-') > 0) {
		$date = explode('-', $testdate5);	// y-m-d
		$dates = array($date[0], $date[1], $date[2]);	// d/m/Y  = y-m-d 0 1 2
	}
	// thai dates
	if(substr(''.$dates[0],0,2) ==='25') {
		$testdate5 = ($dates[0]-543) . '-' . $dates[1] . '-' . $dates[2];
	}
	// eng dates
	else {
    	$testdate5 = $dates[0] . '-' . $dates[1] . '-' . $dates[2];
	}
}

print_r($testdate5);
//exit();

$sql = "INSERT INTO test_datepicker(date_picker) VALUES('$testdate5') ";
$result=mysqli_query($conn,$sql);
if($result){
	echo "<script>alert('บันทึกข้อมูลเรียบร้อย');</script>";	
}else{
	echo "<script>alert(\"บันทึกข้อมูลไม่สำเร็จ\\n" . print_r(mysqli_error($conn), true) . "\");</script>";
}
mysqli_close($conn);

?>