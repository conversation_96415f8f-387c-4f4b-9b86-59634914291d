<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';


// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// สำหรับเลือก จังหวัด อำเภอ ตำบล /และ สถานีตำรวจ
include("../config.inc.php");
include("../classes/class.database.inc.php");
$conn2 = get_connection(); // connect to mySQLDB

// สำหรับเลือกสถานีตำรวจทั่วประเทศ
$region = isset($_GET['region']) ? $_GET['region'] : 0;
$provincial = isset($_GET['provincial']) ? $_GET['provincial'] : 0;
//$station = isset($_GET['station']) ? $_GET['station'] : 0;

$id = $_GET['id'];
$pcid = $_GET['pcid'];
$sql = "SELECT * FROM wm_tb_homevisit WHERE hv_cl_aid = :id ";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':id', $id);
try{
    $stmt->execute();
}catch (PDOException $e) {
    echo $e->getMessage();
}

$row = $stmt->fetch(PDO::FETCH_ASSOC);

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Edit Home Visit Data</title>
<script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/policeinnopolis/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 18px;
    }
input::placeholder {
  color: red;
}
.form-control::placeholder {
  color: sandybrown;
}
</style>
<!-- สำหรับ วันที่ แบบ พ.ศ. -->
<link rel="stylesheet" href="http://code.jquery.com/ui/1.9.1/themes/base/jquery-ui.css" />
<script src="http://code.jquery.com/jquery-1.8.2.js"></script>
<script src="/policeinnopolis/js/jquery-ui.js"></script>
</head>

<body>
	<div class="container">
	<div class="row">
		<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> แก้ไขข้อมูลการตรวจเยี่ยมบุคคลพ้นโทษ </div>
	<form action="../WM/Save_home_visit.php" method="POST" enctype="multipart/form-data" class="body">
        
        <label hidden="hidden">ลำดับ</label>
		<input name="hv_cl_aid" type = "text" class="form-control" value= "<?=$row['hv_cl_aid']?>" hidden="hidden"  >
        
		<label> เลขบัตรประชาชน บุคคลพ้นโทษ <span style="color: #F90004">* จำเป็น</span> </label>
		<input type = "text" name="hv_cl_idcard" class="form-control" value="<?= $pcid ?>" readonly="readonly" >
		
        <label>วันที่ตรวจเยี่ยม <span style="color: #F90004">* จำเป็น</span> </label>
        <p><input type="text" name="hv_cl_datevisit" id="datepicker" value= "<?= $row['hv_cl_datevisit']?>" class="form-control" autocomplete="off" required></p>
        <script>
            function set_cal(ele)//function สร้างตัวเลือกปฎิทิน
            {
              $( ele ).datepicker({
                  onSelect:(date_text)=>
                  {
                    let arr=date_text.split("/");
                    let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])+543).toString();
                    $(ele).val(new_date);
                    $(ele).css("color","");
                  },
                  beforeShow:()=>{

                    if($(ele).val()!="")
                    {
                      let arr=$(ele).val().split("/");
                      let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])-543).toString();
                      $(ele).val(new_date);

                    }

                    $(ele).css("color","white");
                  },
                  onClose:()=>{

                      $(ele).css("color","");

                      if($(ele).val()!="")
                      {
                          let arr=$(ele).val().split("-");
                          if(parseInt(arr[2])<2500)
                          {
                              let new_date=arr[0]+"-"+arr[1]+"-"+(parseInt(arr[2])).toString();
                              $(ele).val(new_date);
                          }
                      }
                  },
                  dateFormat:"yy-mm-dd", //กำหนดรูปแบบวันที่เป็น วัน/เดือน/ปี
                  changeMonth:true,//กำหนดให้เลือกเดือนได้
                  changeYear:true,//กำหนดให้เลือกปีได้
                  showOtherMonths:true,//กำหนดให้แสดงวันของเดือนก่อนหน้าได้
              });
            }
          $(document).ready(function(){
            //เรียก function set_cal เมื่อเปิดหน้าเว็บ โดยส่ง object element ที่มี id เป็น datepicker เป็นพารามิเตอร์
            set_cal( $("#datepicker") );
          })
          </script>
        <!-- สำหรับ วันที่ แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ -->
        
        <label> เจ้าหน้าที่ผู้ตรวจเยี่ยม </label>
		<input type = "text" name = "hv_cl_pol_visit" class="form-control" value= "<?= $row['hv_cl_pol_visit']?>" placeholder="ผู้ไปตรวจเยี่ยม"  >

		<label>พบ/ไม่พบ <span style="color: #F90004">* จำเป็น </span></label>
		<select id="hv_cl_find_or_not" name="hv_cl_find_or_not" class="form-select form-select-sm" aria-label=".form-select-sm example" required >
			<option value="" selected> </option>
			<option value="1">พบ</option>
			<option value="2">ไม่พบ</option>
		</select><br>
        <b>กรณี พบตัว</b><br>
            <label> จำนวน </label>
                <select id="hv_cl_find" name="hv_cl_find" class="form-select form-select-sm" placeholder="จำนวนที่พบตัว" aria-label=".form-select-sm example" required >
                    <option value="0" selected></option>
                    <option value="1">1</option>
                    <option value="2">2</option>
                </select>
            <label> จำนวนบันทึกใน 4.0 </label>
                <select id="hv_cl_find40" name="hv_cl_find40" class="form-select form-select-sm" placeholder="จำนวนที่พบตัว บันทึกใน Police 4.0" aria-label=".form-select-sm example" required >
                    <option value="0" selected></option>
                    <option value="1">1</option>
                    <option value="2">2</option>
                </select>
            <label> เก็บ DNA </label>
                <select id="hv_cl_dna" name="hv_cl_dna" class="form-select form-select-sm" aria-label=".form-select-sm example" required >
                    <option value="" selected></option>
                    <option value="1">เก็บ DNA แล้ว</option>
                    <option value="2">เก็บไม่ได้เก็บ DNA</option>
                </select><br>
        <b>กรณี ไม่พบตัว</b><br>   
            <label> เหตุที่ไม่พบ </label>
            <input type = "text" name = "hv_cl_cause" class="form-control" placeholder="ระบุสาเหตุที่ไม่พบตัว" value= "<?= $row['hv_cl_cause']?>" ><br>
        <b style="color: black">ไม่พบตัว แต่ทราบที่อยู่ใหม่</b><br>
            <label> ที่อยู่/ที่ทำงานใหม่ </label>
            <input type = "text" name = "hv_cl_new_place" class="form-control" placeholder="ระบุที่อยู่/ที่ทำงานใหม่" value= "<?= $row['hv_cl_new_place']?>" >
            <label> จำนวน </label>
                <select id="hv_cl_adr" name="hv_cl_adr" class="form-select form-select-sm" placeholder="จำนวนที่ทราบที่อยู่" aria-label=".form-select-sm example" >
                    <option value="" selected></option>
                    <option value="1">1</option>
                    <option value="2">2</option>
                    </select>
            <label> แจ้งท้องที่ใหม่ </label>
                <select id="hv_cl_action" name="hv_cl_action" class="form-select form-select-sm" placeholder="ระบุท้องที่ใหม่ที่ต้องแจ้งข้อมูล" aria-label=".form-select-sm example" >
                    <option value="" selected></option>
                    <option value="1">1</option>
                    <option value="2">2</option>
                    </select>
            <label> ยังไม่ได้แจ้ง </label>
                <select id="hv_cl_no_action" name="hv_cl_no_action" class="form-select form-select-sm" placeholder="ระบุจำนวนที่ยังไม่ได้แจ้งข้อมูล" aria-label=".form-select-sm example" >
                    <option value="" selected></option>
                    <option value="1">1</option>
                    <option value="2">2</option>
                    </select><br>
        <b style="color: black">ไม่พบตัว และไม่ทราบที่อยู่</b><br>
            <label> จำนวน </label>
                <select id="hv_cl_no_adr" name="hv_cl_no_adr" class="form-select form-select-sm" placeholder="จำนวนที่ไม่ทราบที่อยู่" aria-label=".form-select-sm example" >
                    <option value="" selected></option>
                    <option value="1">1</option>
                    <option value="2">2</option>
                    </select>
            <label> แจ้งเรือนจำ และทว. </label>
                <select id="hv_cl_torvor" name="hv_cl_torvor" class="form-select form-select-sm" placeholder="ระบุจำนวนแจ้ง เรือนจำและ ทว." aria-label=".form-select-sm example" >
                    <option value="" selected></option>
                    <option value="1">1</option>
                    <option value="2">2</option>
                    </select>
            <label> ยังไม่ได้แจ้ง </label>
                <select id="hv_cl_no_torvor" name="hv_cl_no_torvor" class="form-select form-select-sm" placeholder="ระบุจำนวนที่ยังไม่ได้แจ้งข้อมูล ทว." aria-label=".form-select-sm example" >
                    <option value="" selected></option>
                    <option value="1">1</option>
                    <option value="2">2</option>
                    </select><br>
            <b style="color: black">กระทำผิดอีก/อยู่เรือนจำ</b><br>
                    <label> จำนวน </label>
                    <select id="hv_cl_prison" name="hv_cl_prison" class="form-select form-select-sm" placeholder="จำนวนกระทำผิดอีก/อยู่เรือนจำ" aria-label=".form-select-sm example" >
                        <option value="" selected></option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        </select>
                <label> แจ้ง ทว. </label>
                    <select id="hv_cl_torvor2" name="hv_cl_torvor2" class="form-select form-select-sm" placeholder="ระบุจำนวนแจ้ง ทว." aria-label=".form-select-sm example" >
                        <option value="" selected></option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        </select>
                <label> ยังไม่ได้แจ้ง </label>
                    <select id="hv_cl_no_torvor2" name="hv_cl_no_torvor2" class="form-select form-select-sm" placeholder="ระบุจำนวนที่ยังไม่ได้แจ้งข้อมูล ทว." aria-label=".form-select-sm example" >
                        <option value="" selected></option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        </select><br>
        
		<label> หมายเหตุ </label>
		<input type = "text" name = "hv_cl_remark" value= "<?= $row['hv_cl_remark']?>" class="form-control" placeholder="หมายเหตุอื่น ๆ (ถ้ามี)"  >
		 <br>
		
		<label>สถานีตำรวจ <span style="color: #F90004">* จำเป็น</span></label><br>
                <!-- เลือกสถานีตำรวจ -->
        <label> บช. </label><br>
        <select name="region" id="region" onChange="do_region_change()" class="form-control" Required >
                <option value="">&lt;&lt; เลือก บช. &gt;&gt;</option>
                <?php
                $region = $row['region']; // <<ดึงข้อมูลจาก ตารางมา row ซึ่งเป็นคนละตัวกับ row_p
                //------------------------------------
			  	$res_re = $conn2->query("SELECT * FROM `wm_tb_police_region` 
                                            ORDER BY `name_region` ASC");
			     $selected = '';
			     while($row_re = $conn2->fetch_row($res_re)) 
                     {
                      $code_region = $row_re['code_region'];
                      $name_region = $row_re['name_region'];
                      // set default provionce to 64 >> sukhothai
                          if($code_region == $region) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$code_region' $selected> $name_region </option>\n";
                     }
			  ?>
              </select>
        
        <label> บก. </label><br>        
        <select name="provincial" id="provincial" onChange="do_provincial_change()" value="<?php echo $provincial; ?>" class="form-control" >
        <option value="0">&lt;&lt; เลือก บก. &gt;&gt;</option>
                <?php
                if($region > 0)
                {
                    $provincial = $row['provincial']; // <<
                    //-------------------------------------
                    $res_prov = $conn2->query("SELECT * FROM `wm_tb_police_provincial` 
                                                WHERE `region_code`='$region' 
                                                ORDER BY `provincial_code` ASC");
                    $selected = '';
                  while($res_prov = $conn2->fetch_row($res_prov)) 
                  {
                      $provincial_code = $res_prov['provincial_code']; // 
                      $provincial = $res_prov['provincial'];
                      // set default provionce to 64 >> sukhothai
                          if($provincial_code == $provincial) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$provincial_code' $selected> $provincial </option>\n";
                  }
                }	
                ?>
              </select>
        
         <label> สน./สภ. </label>
        <br>
                
        <select name="station" id="station" class="form-control" >
                <option value="0">&lt;&lt; เลือก สน./สภ. &gt;&gt;</option>
                <?php
	           if($provincial > 0)
                {
                   $station = $row['station'];
                   //------------------------------------------
                    $res_st = $conn2->query("SELECT * FROM `wm_tb_police_station2` 
                                                WHERE provincial_code='$provincial_code'
                                                ORDER BY `station_code` ASC");
                    $selected = '';
                  while($row_st = $conn2->fetch_row($res_st)) 
                  {
                      $station_code = $row_st['station_code']; // 
                      $station_name = $row_st['station_name'];
                      // set default provionce to 64 >> sukhothai
                          if($station_code == $station) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$station_code' $selected> $station_name </option>\n";
                  }
                }	
                ?>                                                                     
              </select>
		<br>
			<p>
			<input type="submit" value="Update" class="btn btn-success" >
			<td> <a href="../WM/home_visit.php?pcid=<?= $pcid ?> " class="btn btn-warning" >ยกเลิก</a> </td>
		</p>
		<br>
		<br>
	</form>
	</div>
	</div>
	</div>
<script>
// ฟังก์ชั่น เปลี่ยนภาค เป็น จังหวัด
function do_region_change(current_value)
{
    current_value = (typeof current_value == 'undefined') ? 0 : current_value;
	var sel_region = document.getElementById("region");		
	var code_region = sel_region.options[sel_region.selectedIndex].value;

	$.get("../ajax/get_provincial.php?region=" + code_region, 
		  function(data, status){
				//alert("Data: " + data + "\nStatus: " + status);
				// jQuery style						
				$('#provincial').find('option').remove().end();
				var data = $.trim(data);
				if(data == '') {
					alert("ไม่พบข้อมูล บก. !");
					return;
				}

					//.append('<option value="whatever">text</option>').val('whatever');
				var datas = data.split(';');
				for(var i=0; i<datas.length; i++) {
					if(datas[i] == '') continue;
					datas[i] = datas[i].split(':');
					var code_region = datas[i][0];
					var name_region = datas[i][1];
					//$('#provincial').append('<option value="'+ code_region+'">' + name_region + '</option>');
                    if(current_value == code_region) {
                        $('#provincial').append('<option value="'+ code_region+'" selected>' + name_region + '</option>'); 
                    }
                    else {
                        $('#provincial').append('<option value="'+ code_region+'">' + name_region + '</option>');
                    }
				}
                // Trigger the change event on the amphur dropdown so the tambon dropdown will be populated     
                $('#provincial').trigger('change');
		});
}
    //เลือก สถานี จากจังหวัด
function do_provincial_change(current_value)
{
    current_value = (typeof current_value == 'undefined') ? 0 : current_value;
    
	var sel_region = document.getElementById("region");		
	var provincial_code = sel_region.options[sel_region.selectedIndex].value;
    
    var sel_provincial = document.getElementById("provincial");		
	var station_code = sel_provincial.options[sel_provincial.selectedIndex].value;

	$.get("../ajax/get_station.php?region=" + provincial_code + "&provincial=" + station_code, 
			  function(data, status){
					//alert("Data: " + data + "\nStatus: " + status);
					// jQuery style						
					$('#station').find('option').remove().end();
					var data = $.trim(data);
					if(data == '') {
						alert("ไม่พบข้อมูลสถานี !");
						return;
					}

						//.append('<option value="whatever">text</option>').val('whatever');
					var datas = data.split(';');
					for(var i=0; i<datas.length; i++) {
						if(datas[i] == '') continue;
						datas[i] = datas[i].split(':');
						var station_code = datas[i][0];
						var station_name = datas[i][1];
                        if(current_value == station_code) {
                            $('#station').append('<option value="'+ station_code+'" selected>' + station_name + '</option>'); 
                        }
                        else {
                            $('#station').append('<option value="'+ station_code+'">' + station_name + '</option>');   
                        }						
					}
			});
}
</script> 
	
<script>
// ชื่อ >> value >> set selct auto (สคลิปตรวจสอบ Selected) จำเป็นต้องก็อปปี้ <script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js" type="text/javascript">
function auto_select(sid, value)
{
    $('#' + sid).val(value);
}

function auto_thaidate(sid, value)
{
	if(value != '') {
		var dates = value.split('-');
		dates = value.split('-');
		if(dates.length > 2) {
			var ys = dates[0];
			var ms = dates[1];
			var ds = dates[2];
			if(ys.substr(0,2) == '20') {
				ys = parseInt(ys) + 543;
			}
			value = $.digit(ds,2) + '/' + $.digit(ms,2) + '/' +ys;
			$('#' + sid).val(value);
		}
	}
}
    
    
$(document).ready(function() {
<?php
    echo "auto_select('hv_cl_find_or_not', '{$row['hv_cl_find_or_not']}');\n";
    echo "auto_select('hv_cl_find', '{$row['hv_cl_find']}');\n";
    echo "auto_select('hv_cl_find40', '{$row['hv_cl_find40']}');\n";
    echo "auto_select('hv_cl_dna', '{$row['hv_cl_dna']}');\n";
    echo "auto_select('hv_cl_adr', '{$row['hv_cl_adr']}');\n";
    echo "auto_select('hv_cl_action', '{$row['hv_cl_action']}');\n";
    echo "auto_select('hv_cl_no_action', '{$row['hv_cl_no_action']}');\n";
    echo "auto_select('hv_cl_no_adr', '{$row['hv_cl_no_adr']}');\n";
    echo "auto_select('hv_cl_torvor', '{$row['hv_cl_torvor']}');\n";
    echo "auto_select('hv_cl_no_torvor', '{$row['hv_cl_no_torvor']}');\n";
    // convert db date to thai dates
	//echo "auto_thaidate('hv_cl_datevisit', '{$row['hv_cl_datevisit']}');\n";

?>
	setTimeout("do_region_change('<?= $row['provincial'] ?>')", 1000);
    setTimeout("do_provincial_change('<?= $row['station'] ?>')", 2000);
    });
</script>

</body>
</html>