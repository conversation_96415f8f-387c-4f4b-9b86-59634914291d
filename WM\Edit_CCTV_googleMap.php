<?php
include "../Condb.php"; //PDO
include '../users.inc.php';
include '../right_user.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// สำหรับเลือก จังหวัด อำเภอ ตำบล /และ สถานีตำรวจ
include("../config.inc.php");
include("../classes/class.database.inc.php");
$conn2 = get_connection(); // connect to mySQLDB

$province = isset($_GET['province']) ? $_GET['province'] : 0;
if ($provincial == 67) {
    $province = isset($_GET['province']) ? $_GET['province'] : 64; // สุโขทัย
} elseif ($provincial == 68) {
    $province = isset($_GET['province']) ? $_GET['province'] : 53; // อุตรดิตถ์
} elseif ($provincial == 69) {
    $province = isset($_GET['province']) ? $_GET['province'] : 61; // อุทัยธานี
} elseif ($provincial == 65) {
    $province = isset($_GET['province']) ? $_GET['province'] : 65; // พิษณุโลก
} elseif ($provincial == 61) {
    $province = isset($_GET['province']) ? $_GET['province'] : 62; // กำแพงเพชร
} elseif ($provincial == 62) {
    $province = isset($_GET['province']) ? $_GET['province'] : 63; // ตาก
} elseif ($provincial == 63) {
    $province = isset($_GET['province']) ? $_GET['province'] : 60; // นครสวรรค์
} elseif ($provincial == 64) {
    $province = isset($_GET['province']) ? $_GET['province'] : 66; // พิจิตร
} elseif ($provincial == 66) {
    $province = isset($_GET['province']) ? $_GET['province'] : 67; // เพชรบูรณ์
}
$amphure = isset($_GET['amphure']) ? $_GET['amphure'] : 0; // sukhothai
$tambon = isset($_GET['tambon']) ? $_GET['tambon'] : 0; // sukhothai


$id = $_GET['id'];

$sql = "SELECT * FROM cctv_locations WHERE id = :id ";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':id',$id);
    $stmt->execute();
    $row = $stmt->fetch(PDO::FETCH_ASSOC);

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>แก้ไขข้อมูล CCTV</title>
<script language="javascript" src="/policeinnopolis/jQuery/jquery-3.5.1.min.js"></script><!--เจคิวรี่ สำหรับ ฟังก์ชั่นเลือก จังหวัด อำเภอ ตำบล-->
<link rel="stylesheet" href="/policeinnopolis/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 18px;
    }
input::placeholder {
  color: red;
}
.form-control::placeholder {
  color: sandybrown;
}
</style>
<!-- สำหรับ วันที่ แบบ พ.ศ. -->
<link rel="stylesheet" href="http://code.jquery.com/ui/1.9.1/themes/base/jquery-ui.css" />
<script src="http://code.jquery.com/jquery-1.8.2.js"></script>
<script src="/policeinnopolis/js/jquery-ui.js"></script>
</head>

<body>
	
	<div class="container">
	<div class="row">
	<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> แก้ไขข้อมูล CCTV </div>
	<form action="../WM/Save_Edit_CCTV_google.php" method="POST" enctype="multipart/form-data" class="body">
        
        	<label hidden ="hidden">ลำดับ</label>
			<input type = "text" name = "id" class="form-control" value= "<?= $row['id']?>" hidden ="hidden" >
		
			<h2> พอตจุดติดตั้งกล้องวงจรปิด (CCTV) ลงแผนที่ </h2>
			<label for="location_name">CCTV Location Name :</label>
			<input type="text" id="location_name" name="location_name" value= "<?= $row['location_name']?>" required> <br>
			<br>
			<label for="latitude">Latitude:</label>
			<input type="text" id="latitude" name="latitude" value= "<?= $row['latitude']?>" required> <br>
			<br>
			<label for="longitude">Longitude:</label>
			<input type="text" id="longitude" name="longitude" value= "<?= $row['longitude']?>" required> <br>
			<br>
			<b>ที่อยู่ติดตั้ง CCTV</b><br>
			<!-- เลือกจังวัด อำเภอ ตำบล อัตโนมัติ -->
			<label> จังหวัด </label><br>
        <select name="cctv_province" id="cctv_province" onChange="do_province_change()" class="form-control" >
                <option value="0">&lt;&lt; เลือกจังหวัด &gt;&gt;</option>
                <?php
                $province = $row['cctv_province']; // <<ดึงข้อมูลจาก ตารางมา row ซึ่งเป็นคนละตัวกับ row_p
                //------------------------------------
			  	$res_p = $conn2->query("SELECT * FROM `provinces` ORDER BY `provinces`.`pv_name_th` ASC");
			     $selected = '';
			     while($row_p = $conn2->fetch_row($res_p)) 
			     {
				  $pv_code = $row_p['pv_code'];
				  $pv_name = $row_p['pv_name_th'];
				  // set default provionce to 64 >> sukhothai
				  if($pv_code == $province) {
					  $selected = 'selected';
				  }
				  else {
					  $selected = '';
				  }
				  //
				  echo "<option value='$pv_code' $selected> $pv_name </option>\n";
			     }
			  
			  ?>
              </select>
        
        <label> อำเภอ </label><br>
        <select name="cctv_amphur" id="cctv_amphur" onChange="do_amphure_change()" class="form-control" >
        <option value="0">&lt;&lt; เลือกอำเภอ &gt;&gt;</option>
                <?php
                if($province > 0)
                {
                    $amphure = $row['cctv_amphur']; // <<
                    //-------------------------------------
                    $res_a = $conn2->query("SELECT * FROM `amphures` WHERE province_code='$province' ORDER BY `amphures`.`ap_name_th` ASC");
                    $selected = '';
                  while($row_a = $conn2->fetch_row($res_a)) 
                  {
                      $ap_code = $row_a['ap_code']; // 
                      $ap_name = $row_a['ap_name_th'];
                      // set default provionce to 64 >> sukhothai
                      if($ap_code == $amphure) {
                          $selected = 'selected';
                      }
                      else {
                          $selected = '';
                      }
                      //
                      echo "<option value='$ap_code' $selected> $ap_name </option>\n";
                  }
                }	
                ?>
              </select>
        
         <label> ตำบล </label>
        <br>
        <select name="cctv_tumbon" id="cctv_tumbon" class="form-control" >
                <option value="0">&lt;&lt; เลือกตำบล &gt;&gt;</option>
                <?php
	           if($amphure > 0)
                {
                   $tambon = $row['cctv_tumbon'];
                   //------------------------------------------
                    $res_t = $conn2->query("SELECT * FROM `districts` WHERE amphure_code='$amphure' ORDER BY `districts`.`tb_name_th` ASC");
                    $selected = '';
                  while($row_t = $conn2->fetch_row($res_t)) 
                  {
                      $tb_code = $row_t['tb_id']; // 
                      $tb_name = $row_t['tb_name_th'];
                      // set default provionce to 64 >> sukhothai
                      if($tb_code == $tambon) {
                          $selected = 'selected';
                      }
                      else {
                          $selected = '';
                      }
                      //
                      echo "<option value='$tb_code' $selected> $tb_name </option>\n";
                  }
                }	
                ?>                                                                     
              </select>

			<label> หมู่ที่ </label><br>
			<input type = "text" name = "cctv_moo" value= "<?= $row['cctv_moo']?>" class="form-control" placeholder="ระบุหมู่ที่" >

			<label>เลขที่</label>
			<input type = "text" name = "cctv_adr" value= "<?= $row['cctv_adr']?>" class="form-control" placeholder="เลขที่" >
			
			<label>สถานะกล้อง</label>
						<select id="cctv_status" name="cctv_status" class="form-select form-select-sm" >
							<option value="1" selected> </option>
							<option value="1">ใช้ได้ปกติ</option>
							<option value="2">เสีย</option>
						</select>
						<br>
		
			<label>รายละเอียดอื่นๆ</label>
			<input type = "text" name = "cctv_detail" value= "<?= $row['cctv_detail']?>" class="form-control" placeholder="รายละเอียดอื่นๆ เช่น จำนวนวันบันทึก" >
			
			<label>เจ้าของ/ผู้ดูแล/ผู้ประสานงาน</label>
			<input type = "text" name = "cctv_admin" value= "<?= $row['cctv_admin']?>" class="form-control" placeholder="เจ้าของ/ผู้ดูแล/ผู้ประสานงาน" >
			
			<label>เบอร์โทรติดต่อ</label>
			<input type = "text" name = "cctv_contact" value= "<?= $row['cctv_contact']?>" class="form-control" placeholder="เบอร์โทรติดต่อ" >
			
			<label>หน่วยงานเจ้าของกล้อง</label>
						<select id="cctv_owner" name="cctv_owner" class="form-select form-select-sm" >
							<option value="" selected> </option>
							<option value="1">หน่วยราชการ</option>
							<option value="2">เอกชน</option>
						</select>
				<br>
			
			 <div class="mb-3">
				 <label for="formFileMultiple" class="form-label">รูปภาพ</label>
				 <input class="form-control" type="file" id="cctv_img" name="cctv_img" value= "<?= $row['cctv_img']?>" multiple>
        	</div>

		<p>
			<input type="submit" value="บันทึกข้อมูล" class="btn btn-success" >
			<td> <a href="/WatchmanData/Show_CCTV_googleMap.php?rnd=<?= rand(); ?>" class="btn btn-warning" >ยกเลิก</a></td>
		</p>
		<br>
		<br>
	</form>
	</div>
	</div>
	</div>
<!-- ฟังก์ชั่น เลือก จังหวัด อำเภอ ตำบล -->
<script>
function do_province_change()
{
	var sel = document.getElementById("cctv_province");		
	var code = sel.options[sel.selectedIndex].value;

	$.get("../ajax/get_amphure.php?province=" + code, 
		  function(data, status){
				//alert("Data: " + data + "\nStatus: " + status);
				// jQuery style						
				$('#cctv_amphur').find('option').remove().end();
				var data = $.trim(data);
				if(data == '') {
					alert("ไม่พบข้อมูลอำเภอ !");
					return;
				}

					//.append('<option value="whatever">text</option>').val('whatever');
				var datas = data.split(';');
				for(var i=0; i<datas.length; i++) {
					if(datas[i] == '') continue;
					datas[i] = datas[i].split(':');
					var code = datas[i][0];
					var name = datas[i][1];
					$('#cctv_amphur').append('<option value="'+ code+'">' + name + '</option>');
				}

            // Trigger the change event on the amphur dropdown so the tambon dropdown will be populated     
                $('#cctv_amphur').trigger('change');
		});
}

function do_amphure_change()
{
	var sel = document.getElementById("cctv_province");		
	var code = sel.options[sel.selectedIndex].value;

	var sel2 = document.getElementById("cctv_amphur");		
	var code2 = sel2.options[sel2.selectedIndex].value;

	$.get("../ajax/get_district.php?province=" + code + "&amphure=" + code2, 
			  function(data, status){
					//alert("Data: " + data + "\nStatus: " + status);
					// jQuery style						
					$('#cctv_tumbon').find('option').remove().end();
					var data = $.trim(data);
					if(data == '') {
						alert("ไม่พบข้อมูลตำบล !");
						return;
					}

						//.append('<option value="whatever">text</option>').val('whatever');
					var datas = data.split(';');
					for(var i=0; i<datas.length; i++) {
						if(datas[i] == '') continue;
						datas[i] = datas[i].split(':');
						var code = datas[i][0];
						var name = datas[i][1];
						$('#cctv_tumbon').append('<option value="'+ code+'">' + name + '</option>');
					}

                $('#cctv_tumbon').trigger('change');
			});
}

</script> 
<script>
// ชื่อ >> value >> set selct auto (สคลิปตรวจสอบ Selected) จำเป็นต้องก็อปปี้ <script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js" type="text/javascript">
function auto_select(sid, value)
{
    $('#' + sid).val(value);
}


$(document).ready(function() {
<?php
    echo "auto_select('cctv_owner', '{$row['cctv_owner']}');\n";
    echo "auto_select('cctv_status', '{$row['cctv_status']}');\n";
    //echo "auto_select('cc_cl_status', '{$row['cc_cl_status']}');\n";
    //echo "auto_select('cc_cl_pol_station', '{$row['cc_cl_pol_station']}');\n";
	// convert db date to thai dates
	//echo "auto_thaidate('cc_cl_date', '{$row['cc_cl_date']}');\n";
?>
    //setTimeout("do_region_change('<?= $row['provincial'] ?>')", 1000);
    //setTimeout("do_provincial_change('<?= $row['station'] ?>')", 2000);
    });
</script>
</body>
</html>