<?php
include '../Condb.php'; //PDO
include '../users.inc.php';
include '../Alert.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

$ip_address = $_SERVER['REMOTE_ADDR'];
$action = 'Save online case'; // You can set the action to a suitable value
$data = str_replace("'", "", compact_array($_POST));
log_activity($acc, $action, $data, $ip_address); // ใช้ function ในไฟล์ Condb.php

/*echo '<pre>';
print_r($_POST);
echo '</pre>';

echo '<hr>';
exit;*/

$on_cl_aid = $_POST[ 'on_cl_aid' ];
$on_cl_no = $_POST[ 'on_cl_no' ];	
$on_cl_name_sufferer = $_POST[ 'on_cl_name_sufferer' ];
$on_cl_detail = $_POST[ 'on_cl_detail' ];
$on_cl_damages = $_POST[ 'on_cl_damages' ];
$region = $_POST[ 'region' ];
$provincial = $_POST[ 'provincial' ];
$station = $_POST[ 'station' ];
$on_cl_inquiry_official = $_POST[ 'on_cl_inquiry_official' ];
$on_cl_date = $_POST[ 'on_cl_date' ];
$on_cl_status_case = $_POST[ 'on_cl_status_case' ];
$on_cl_detective = $_POST[ 'on_cl_detective' ];

// save file รายงานสืบสวน
$file1 = $_FILES[ 'on_cl_file' ][ 'tmp_name' ];
if ( is_uploaded_file( $file1 ) ) {
  $on_cl_file = "uploaded/Doc/" . $_FILES[ 'on_cl_file' ][ 'name' ];	
  move_uploaded_file( $file1, "../" . $on_cl_file );  
} else {
  $on_cl_file = '';
}

try{
$sql ="SELECT * FROM `wm_tb_online_case` WHERE on_cl_aid = :on_cl_aid ";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':on_cl_aid', $on_cl_aid);
    $stmt->execute();
    $row = $stmt->fetch(PDO::FETCH_ASSOC);
    
if($row) {
    $on_cl_aid = $row['on_cl_aid'];
    $sql = "UPDATE wm_tb_online_case SET " .
		"on_cl_no = :on_cl_no," .
        "on_cl_name_sufferer = :on_cl_name_sufferer," .
        "on_cl_detail = :on_cl_detail," .
        "on_cl_damages = :on_cl_damages," .
        "region = :region," .
        "provincial = :provincial," .
        "station = :station," .
        "on_cl_inquiry_official = :on_cl_inquiry_official," .
        "on_cl_date = :on_cl_date," .
        "on_cl_status_case = :on_cl_status_case," .
        "on_cl_detective = :on_cl_detective " ;
        $params = [
            'on_cl_no' => $on_cl_no,
            'on_cl_name_sufferer' => $on_cl_name_sufferer,
            'on_cl_detail' => $on_cl_detail,
            'on_cl_damages' => $on_cl_damages,
            'region' => $region,
            'provincial' => $provincial,
            'station' => $station,
            'on_cl_inquiry_official' => $on_cl_inquiry_official,
            'on_cl_date' => $on_cl_date,
            'on_cl_status_case' => $on_cl_status_case,
            'on_cl_detective' => $on_cl_detective
            ];
    
            if ($on_cl_file !== '') {
            $sql .= ", on_cl_file = :on_cl_file";
            $params['on_cl_file'] = $on_cl_file;
            }

            $sql .= " WHERE on_cl_aid = :updated_on_cl_aid";
            $params['updated_on_cl_aid'] = $on_cl_aid;

}else{
    $sql = "INSERT INTO wm_tb_online_case (on_cl_no, on_cl_name_sufferer, on_cl_detail, on_cl_damages, region, provincial, station, on_cl_inquiry_official, on_cl_date, on_cl_status_case, on_cl_detective, on_cl_file) 
    VALUES(:on_cl_no, :on_cl_name_sufferer, :on_cl_detail, :on_cl_damages, :region, :provincial, :station, :on_cl_inquiry_official, :on_cl_date, :on_cl_status_case, :on_cl_detective, :on_cl_file) ";
            $params = [
            'on_cl_no' => $on_cl_no,
            'on_cl_name_sufferer' => $on_cl_name_sufferer,
            'on_cl_detail' => $on_cl_detail,
            'on_cl_damages' => $on_cl_damages,
            'region' => $region,
            'provincial' => $provincial,
            'station' => $station,
            'on_cl_inquiry_official' => $on_cl_inquiry_official,
            'on_cl_date' => $on_cl_date,
            'on_cl_status_case' => $on_cl_status_case,
            'on_cl_detective' => $on_cl_detective,
            'on_cl_file' => $on_cl_file
            ];
    }

$stmt = $pdo->prepare($sql);
$result = $stmt->execute($params);

    if ($result) {
        $_SESSION['success'] = "Data has been inserted succesfully";
        showSweetAlert("Saved successfully", "บันทึกข้อมูลเรียบร้อย");
        unset($_SESSION['success']); // Clear the session variable
        header("refresh:2; url=/Bansuan/index.php?id={$on_cl_aid}&page=online");
    } else {
        $_SESSION['error'] = "Error";
        showSweetAlert('Error', "บันทึกข้อมูล ไม่สำเร็จ", 'error');
        unset($_SESSION['error']); // Clear the session variable
        header("refresh:2; url=/Bansuan/index.php?id={$on_cl_aid}&page=online");
    }
    
}catch(PDOException $e){
    echo 'Query Failed: '. $e->getMessage();
}

$pdo = null;

?>