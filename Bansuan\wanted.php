<?php
// ตรวจสอบเวลาการใช้งาน หากไม่มีการใช้งาน เกิน 15 นาที ให้ตัดออกจากระบบที่ login
$_SESSION['last_active'] = time();

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);


$sqls = "SELECT
            (SELECT COUNT(*) FROM wm_tb_warrant WHERE station=:station AND wr_cl_status='1' AND NOT wr_cl_qty=2) AS Total1,
            (SELECT COUNT(*) FROM wm_tb_warrant WHERE station=:station AND wr_cl_status='3') AS Total2,
            (SELECT COUNT(*) FROM wm_tb_warrant WHERE station=:station AND wr_cl_qty=2 AND wr_cl_status='1') AS Total3";

    $stmt = $pdo->prepare($sqls);
	$stmt->bindParam(':station',$station);
    $stmt->execute();
    $row5 = $stmt->fetch(PDO::FETCH_ASSOC);

    //print_r($row5);
    $total = $row5['Total1'];
    $total2 = $row5['Total2'];
    $total3 = $row5['Total3'];

//ยอดคงเหลือทั้งหมด
    $tatal_all = $total + $total2 + $total3;

?>
<link rel="stylesheet" href="../bootstrap/css/bootstrap-grid.css">
<style>
.thhead !important{
			background: #1704F4;
			color: white;
		}
@media print {
   a {
      display: none !important;
   }
}
#btn1 {
	margin-bottom: 10px;
}

</style>
<link rel="stylesheet" type="text/css" href="print.css" media="print">
<div class="align-self-start">
	<div class=" h3 text-center  alert alert-danger mb-4 mt-4 " role="alert" >ข้อมูลหมายจับ <?= $name_station ?></div>
		<div align="left">
			&nbsp;<a href="Add_Personal_for_bansuan.php" class="btn btn-success btn-lg mb-4 <?= $add_btn ?>" >เพิ่มข้อมูล</a>
            &nbsp;<a href="wanted_freeze.php" class="btn btn-dark btn-lg mb-4" >สถานะอายัด</a>
            &nbsp;<a href="wanted_no_quality.php" class="btn btn-dark btn-lg mb-4" >หมายไม่มีคุณภาพ</a>
            &nbsp;<a href="wanted_finish.php" class="btn btn-secondary btn-lg mb-4" >ถอนหมายจับแล้ว</a>
            &nbsp;<a href="/Activity/Show_Activity7.php" class="btn btn-primary btn-lg mb-4" >ตารางสรุปยอด หมายจับ</a>  
		</div>
		<div align="left">
			&nbsp;<a style="background-color: yellow ; padding: 10px">หมายจับ ณ ปัจจุบัน มี : <b style="color: crimson; font-size: 26px"> <?= $tatal_all ?> </b>หมาย แยกเป็น 
            <span style="color: #FC0408"> หมายที่ต้องดำเนินการจับกุม จำนวน</span> <span style="color: #FC0408"><b style="color: crimson ;font-size: 26px"> <?= $total ?> </b> หมาย </span>
			หมายไม่มีคุณภาพ จำนวน <b style="color: crimson; font-size: 26px"><?= $total3 ?></b>  หมาย  และ อายัดตัว จำนวน <b style="color: crimson; font-size: 26px">  <?= $total2 ?> </b> หมาย </span>
            &nbsp;&nbsp;</a> <br>
		</div>
<div class="table-responsive">
  <table class="table table-striped table-hover table-bordered table-sm mb-4 mt-4">
    <tbody>
      <tr>
        <th class="thhead">ลำดับ</th>
        <th class="thhead">ชื่อ-สกุล</th>
        <th class="thhead">เลขบัตร</th>
        <th class="thhead">ศาล</th>
        <th class="thhead">เลขที่หมาย</th>
        <th class="thhead" nowrap>วันที่ออกหมาย</th>
        <th class="thhead" nowrap>ขาดอายุความ</th>
        <th class="thhead">เลขคดี</th>
        <th class="thhead">ฐานความผิด</th>
        <th class="thhead" width="150px">เกิน 180 วัน ต้องแจ้งย้าย</th>
        <th class="thhead">พงส.</th>
        <th class="thhead">ผู้รับผิดชอบ</th>
        <th class="thhead"></th>
      </tr>
	  

<?php
$sql = "SELECT
            T1.*,
            T2.ps_cl_name, T2.ps_cl_surname,
            T3.sw_cl_name , T4.station_name  " .		//เงื่อนไข การเลือกข้อมูล จาก 2 ตาราง
		"FROM
            wm_tb_warrant AS T1 " .
		    "LEFT JOIN wm_tb_personal AS T2 ON T1.wr_cl_idcard = T2.ps_cl_idcard " .
            "LEFT JOIN wm_tb_status_warrent AS T3 ON T1.wr_cl_status = T3.sw_cl_aid " .
            "LEFT JOIN wm_tb_police_station2 AS T4 ON T1.station = T4.station_code " .
		"WHERE
            (T1.station=:station) AND (T1.wr_cl_status='1') AND NOT(T1.`wr_cl_qty`=2) " .	//เพิ่มเงื่อนไข เลือกสถานี และสถานะมีผลใช้
		"ORDER BY
            T1.wr_cl_date ASC "; 
    
	$stmt = $pdo->prepare($sql);
	$stmt->bindParam(':station',$station);
    $stmt->execute();
			
// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no = 1;
while($rowWd = $stmt->fetch(PDO::FETCH_ASSOC))	{//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
    
    //ฟังก์ชั่น วันที่ ดึงจาก condb
    if($rowWd['wr_cl_date']!=''){
        $strDate = DateThai( $rowWd['wr_cl_date'] );
    }else{
        $strDate = '';
    }
    
    if($rowWd['wr_cl_expire']!=''){
        $strDate2 = DateThai( $rowWd['wr_cl_expire'] );
    }else{
        $strDate2 = '';
    }
    
    // แสดงจำนวนวัน 
    $wanted_date = $rowWd['wr_cl_date'];
    $startDate = new DateTime($wanted_date);
    $currentDate = new DateTime();
    $diff = $startDate->diff($currentDate);

?>
	  
    <tr>
        <td>&nbsp;<?= $no ?></td>
        <td style="text-align: start; width: 150px"><?= $rowWd['ps_cl_name'] .' ' . $rowWd['ps_cl_surname'] ?></td>
        <td><?= $rowWd['wr_cl_idcard'] ?></td>
        <td><?= $rowWd['wr_cl_court'] ?></td>
        <td><?= $rowWd['wr_cl_no'] ?></td>
        <td nowrap><?= $strDate ?></td>
        <td nowrap><?= $strDate2 ?></td>
        <td><?= $rowWd['wr_cl_crimes_no'] ?></td>
        <td style="text-align: start"><?= $rowWd['wr_cl_crimes_type'] ?></td>
        <td><?php if($diff->days <= 180){echo '' . $diff->days . ' วัน'; }else{echo 'ครบ 180 วันแล้ว'; }?></td>
        <td><?= $rowWd['wr_cl_officer'] ?></td>
        <td><?= $rowWd['wr_cl_police'] ?></td>
        <td>
          <button onClick="go_detail('<?=$rowWd['wr_cl_idcard'] ?>')" class="btn btn-success" id="btn1">รายละเอียด</button>
          <button onClick="go_progress('<?=$rowWd['wr_cl_idcard'] ?>')" class="btn btn-warning" id="btn2">ความคืบหน้า&ติดตามหมาย</button>
        </td>
      </tr>
<?php
	$no++;
}//while
?>
  </tbody>
  </table>
</div>
</div>
<hr>
<!--<div align="center"><a style="font-size: 20px ;color: blue ;background: #02FCF8 ;padding: 10px"> Dashboard ข้อมูลหมายจับ สภ.บ้านสวน </a><br>
	</div>
    <diV align="center">
	<iframe width="1280" height="960" src="https://datastudio.google.com/embed/reporting/598e8b7e-f3a3-4553-a65f-b0ca6200a8a9/page/fWJiC" frameborder="0" style="border:0" allowfullscreen></iframe>
</div>-->

<script>
function go_detail(pcid)	
{
	window.location = "/WatchmanData/Show_All.php?pcid=" +pcid + "&rnd=" +"&page=warrant";
}
function go_progress(pcid)	
{
	window.location = "progress.php?pcid=" +pcid + "&rnd=" +"&page=wanted";
}
</script>
