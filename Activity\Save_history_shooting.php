<?php
include '../Condb.php';
include '../users.inc.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// เมื่อมีการใช้ ตัวแปร Session จะต้องเปิดก่อน
//session_start();  // เปิดตัวแปร session
/*if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
*/

//echo '<pre>';
//print_r($_POST);
//echo '</pre>';
//
//echo '<hr>';
//exit;
$No = $_POST[ 'No' ];
$code = $_POST[ 'code' ];
$date = $_POST[ 'date' ];
$license_number = $_POST[ 'license_number' ];
$owner = $_POST[ 'owner' ];
$type = $_POST[ 'type' ];
$size = $_POST[ 'size' ];
$brand = $_POST[ 'brand' ];
$model = $_POST[ 'model' ];
$shooter = $_POST[ 'shooter' ];
$recorder = $_POST[ 'recorder' ];
$region = $_POST[ 'region' ];
$provincial = $_POST[ 'provincial' ];
$station = $_POST[ 'station' ];

$sql ="SELECT * FROM `wm_tb_gun_history_shooting` WHERE No='$No' ";
$res = mysqli_query($conn,$sql);
$row = mysqli_fetch_array($res);
if($row) {
    $No = $row['No'];
    $sql = "UPDATE `wm_tb_gun_history_shooting` SET ".
            "code = '$code', " .
            "date = '$date', " .
            "license_number = '$license_number', " .
            "owner = '$owner', " .
            "type = '$type', " .
            "size = '$size', " .
            "brand = '$brand', " .
            "model = '$model', " .
            "shooter = '$shooter', " .
            "recorder = '$recorder', " .
            "region = '$region', " .
            "provincial = '$provincial', " .
            "station = '$station' " .
            "WHERE No = '$No' ";
    
}else{
        $sql = "INSERT INTO `wm_tb_gun_history_shooting`(code,date,license_number,owner,type,size,brand,model,shooter,recorder,region,provincial,station) VALUES('$code', '$date', '$license_number', '$owner', '$type', '$size', '$brand', '$model', '$shooter', '$recorder', '$region', '$provincial', '$station') ";
}
    
$result=mysqli_query($conn,$sql);
if($result){
	echo "<script>alert('บันทึกข้อมูลเรียบร้อย');</script>";	
}else{
	echo "<script>alert(\"บันทึกข้อมูลไม่สำเร็จ\\n" . print_r(mysqli_error($conn), true) . "\");</script>";
}
mysqli_close($conn);

echo "<script>window.location='/Activity/history_shooting.php?id={$No}';</script>";
?>