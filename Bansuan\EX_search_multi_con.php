<?php
// Replace with your database credentials
$servername = "localhost";
$username = "username";
$password = "password";
$dbname = "database_name"; 

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname); 

// Check connection
if ($conn->connect_error) {
    die("Connection failed: " . $conn->connect_error);
} 

// Retrieve dropdown options for parish
$sql_parish = "SELECT DISTINCT parish FROM people";
$result_parish = $conn->query($sql_parish); 

// Retrieve dropdown options for fault type
$sql_fault = "SELECT DISTINCT fault_type FROM people";
$result_fault = $conn->query($sql_fault); 

// Check if form is submitted
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Retrieve selected options
    $parish = $_POST['parish'];
    $fault_type = $_POST['fault_type'];
    $start_date = $_POST['start_date'];
    $end_date = $_POST['end_date']; 

    // Construct SQL query with user-selected conditions
    $sql_data = "SELECT * FROM people WHERE parish='$parish' AND fault_type='$fault_type' AND date BETWEEN '$start_date' AND '$end_date'";
    $result_data = $conn->query($sql_data);
}
?> 

<!-- HTML form for user input -->
<!doctype html>
<html>
<head>
<meta charset="utf-8">
<title>Example Serach Multi condition</title>
</head>

<body>

<form method="post">
    <label for="parish">Parish:</label>
    <select name="parish" id="parish">
        <option value="">-- Select parish --</option>
        <?php while($row = $result_parish->fetch_assoc()): ?>
            <option value="<?php echo $row['parish']; ?>"><?php echo $row['parish']; ?></option>
        <?php endwhile; ?>
    </select>
    <br>
    <label for="fault_type">Fault Type:</label>
    <select name="fault_type" id="fault_type">
        <option value="">-- Select fault type --</option>
        <?php while($row = $result_fault->fetch_assoc()): ?>
            <option value="<?php echo $row['fault_type']; ?>"><?php echo $row['fault_type']; ?></option>
        <?php endwhile; ?>
    </select>
    <br>
    <label for="start_date">Start Date:</label>
    <input type="date" name="start_date" id="start_date">
    <br>
    <label for="end_date">End Date:</label>
    <input type="date" name="end_date" id="end_date">
    <br>
    <input type="submit" value="Submit">
</form> 

</body>
</html>
    
<?php if ($_SERVER['REQUEST_METHOD'] === 'POST'): ?>
    <!-- Display data in table format -->
    <table>
        <thead>
            <tr>
                <th>Name</th>
                <th>Parish</th>
                <th>Fault Type</th>
                <th>Date</th>
            </tr>
        </thead>
        <tbody>
            <?php while($row = $result_data->fetch_assoc()): ?>
                <tr>
                    <td><?php echo $row['name']; ?></td>
                    <td><?php echo $row['parish']; ?></td>
                    <td><?php echo $row['fault_type']; ?></td>
                    <td><?php echo $row['date']; ?></td>
                </tr>
            <?php endwhile; ?>
        </tbody>
    </table>
<?php endif; ?> 

<?php $conn->close(); ?>
