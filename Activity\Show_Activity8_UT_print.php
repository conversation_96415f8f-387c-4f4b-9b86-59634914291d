<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);


$id = isset($_GET['hv_cl_aid']) ? $_GET['hv_cl_aid'] : '';

//get month from select or current month
$month = isset($_GET['month']) ? $_GET['month'] : date("m");
$year = isset($_GET['year']) ? $_GET['year'] : date("Y");

$m1 = '';
$m2 = '';
$m3 = '';
$m4 = '';
$m5 = '';
$m6 = '';
$m7 = '';
$m8 = '';
$m9 = '';
$m10 = '';
$m11 = '';
$m12 = '';
switch($month)
{
    case "01": $m1 = "selected"; break;
    case "02": $m2 = "selected"; break;
    case "03": $m3 = "selected"; break;
    case "04": $m4 = "selected"; break;
    case "05": $m5 = "selected"; break;
    case "06": $m6 = "selected"; break;
    case "07": $m7 = "selected"; break;
    case "08": $m8 = "selected"; break;
    case "09": $m9 = "selected"; break;
    case "10": $m10 = "selected"; break;
    case "11": $m11 = "selected"; break;
    case "12": $m12 = "selected"; break;
}
$current_month = $global_thai_month[0+$month];
$y2 = $year+543;

// สำหรับ พิมพ์หน้า ข้อมูฃ ประกาศ บรรทัด ที่ต้องการพิมพ์
//-----------------
$MAX_ROW = 18;
//-----------------

// สร้างฟังก์ชั่น เพื่อดึง หัวตาราง ไปแสดง ในหน้าต่อไป
function print_header()
{
    echo '
	<h3 align="center">ข้อมูลการติดตามพฤติการณ์และความเคลื่อนไหวของบุคคลพ้นโทษ พักโทษ  <?php echo $name_provincial ?></h3>
<h3 align="center">ประจำเดือน&nbsp; <?= $current_month ?> &nbsp;<?= $y2 ?> &nbsp;</h3>
<div class="table-responsive">
<table height="221" border="1" cellpadding="1" cellspacing="1" class="table-bordered table-hover mb-4 mt-4">
  <tbody>
    <tr>
      <td rowspan="3" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">&nbsp;สถานี&nbsp;</td>
      <td rowspan="3" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">จำนวนครั้ง<br>(รวมทั้งหมด)</td>
      <td height="46" colspan="5" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">เยี่ยมพบ</td>
      <td colspan="9" bgcolor="#FD5F2F" style="color: #00000; text-align: center; border-color: navy">เยี่ยมไม่พบ</td>
      <td rowspan="3" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">มีผลการปฏิบัติ</td>
      <td rowspan="3" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">ไม่มีผลการปฏิบัติ</td>
    </tr>
    <tr>
      <td rowspan="2" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">&nbsp;จำนวน (คน)&nbsp;</td>
      <td rowspan="2" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">&nbsp;ผลใน Police4.0&nbsp;</td>
      <td rowspan="2" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">ร้อยละความสำเร็จ</td>
      <td rowspan="2" bgcolor="#48CF1A" style="color: #00000; text-align: center; border-color: navy">เก็บ DNA</td>
      <td rowspan="2" bgcolor="#48CF1A" style="color: #00000; text-align: center; border-color: navy">ยังไม่เก็บ DNA</td>
      <td height="58" colspan="3" bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">ทราบที่อยู่</td>
      <td colspan="3" bgcolor="#FDBD62" style="color: #00000; text-align: center; border-color: navy">ไม่ทราบที่อยู่</td>
      <td colspan="3" bgcolor="#FA9779" style="color: #00000; text-align: center; border-color: navy">กระทำผิดอีก/อยู่เรือนจำ</td>
      </tr>
    <tr>
	  <td height="58" bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">&nbsp;จำนวน&nbsp;</td>
	  <td bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">&nbsp;แจ้งท้องที่ใหม่&nbsp;</td>
	  <td bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">&nbsp;ยังไม่แจ้ง&nbsp;</td>
	  <td bgcolor="#FDBD62" style="color: #00000; text-align: center; border-color: navy">&nbsp;จำนวน&nbsp;</td>
	  <td bgcolor="#FDBD62" style="color: #00000; text-align: center; border-color: navy">&nbsp;แจ้ง รจ.และทว.&nbsp;</td>
	  <td bgcolor="#FDBD62" style="color: #00000; text-align: center; border-color: navy">&nbsp;ยังไม่แจ้ง&nbsp;</td>
	  <td bgcolor="#FA9779" style="color: #00000; text-align: center; border-color: navy">&nbsp;จำนวน&nbsp;</td>
	  <td bgcolor="#FA9779" style="color: #00000; text-align: center; border-color: navy">&nbsp;แจ้ง ทว.&nbsp;</td>
	  <td bgcolor="#FA9779" style="color: #00000; text-align: center; border-color: navy">&nbsp;ยังไม่แจ้ง&nbsp;</td>
      </tr>
	';
}

?>

<meta charset="utf-8">
<title>รายงานบุคคลพ้นโทษ/พักโทษ <?php echo $name_provincial ?></title>
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
        <base target="_top">
		<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
		<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
  		<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
 		<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js"></script>

<style type="text/css">
body{
    font-size:12px; 
}
.textAlignVer{
    display:block;
    filter: flipv fliph;
    -webkit-transform: rotate(-90deg); 
    -moz-transform: rotate(-90deg); 
    transform: rotate(-90deg); 
    position:relative;
    width:20px;
    white-space:nowrap;
    font-size:12px;
    margin-bottom:10px;
}
	td {
		height: 50px;
	}
</style>
<!-- สไตล์ สำหรับ สั่งพิมพ์ข้อมูล -->
<style>
@media only print
{
   
}
    
@media print {
    /* Set the page size to A4 landscape */
    @page {
    size: A4 landscape;
  }
    /* Set the margins */
  body {
    margin: 0;
    padding: 0;
  }
td{
    color: black;
}
tr{
    color: black;
}
  .printId{visibility: visible;}
    
.footer { page-break-after: always;}
}
.body_text{
    font-size: 16px;
    color: black;
}
/* Avoid breaking content across pages */
  .content {
    page-break-inside: avoid;
  }
/* Hide elements that shouldn't be printed */
  .no-print {
    display: none;
  }

</style>
<!--<p><img src="../Image/Head2.jpg" width="100%" height="" alt=""/></p>-->
<body>
<div class="container-fluid" align="left">
<div class="printId">
<h3 align="center">ข้อมูลการติดตามพฤติการณ์และความเคลื่อนไหวของบุคคลพ้นโทษ พักโทษ  <?php echo $name_provincial ?></h3>
<h3 align="center">ประจำเดือน&nbsp; <?= $current_month ?> &nbsp;<?= $y2 ?> &nbsp;</h3>
<div class="table-responsive">
<table height="221" border="1" cellpadding="1" cellspacing="1" class="table-bordered table-hover mb-4 mt-4">
  <tbody>
    <tr>
      <td rowspan="3" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">&nbsp;สถานี&nbsp;</td>
      <td rowspan="3" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">จำนวนครั้ง<br>(รวมทั้งหมด)</td>
      <td height="46" colspan="5" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">เยี่ยมพบ</td>
      <td colspan="9" bgcolor="#FD5F2F" style="color: #00000; text-align: center; border-color: navy">เยี่ยมไม่พบ</td>
      <td rowspan="3" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">มีผลการปฏิบัติ</td>
      <td rowspan="3" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">ไม่มีผลการปฏิบัติ</td>
    </tr>
    <tr>
      <td rowspan="2" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">&nbsp;จำนวน (คน)&nbsp;</td>
      <td rowspan="2" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">&nbsp;ผลใน Police4.0&nbsp;</td>
      <td rowspan="2" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">ร้อยละความสำเร็จ</td>
      <td rowspan="2" bgcolor="#48CF1A" style="color: #00000; text-align: center; border-color: navy">เก็บ DNA</td>
      <td rowspan="2" bgcolor="#48CF1A" style="color: #00000; text-align: center; border-color: navy">ยังไม่เก็บ DNA</td>
      <td height="58" colspan="3" bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">ทราบที่อยู่</td>
      <td colspan="3" bgcolor="#FDBD62" style="color: #00000; text-align: center; border-color: navy">ไม่ทราบที่อยู่</td>
      <td colspan="3" bgcolor="#FA9779" style="color: #00000; text-align: center; border-color: navy">กระทำผิดอีก/อยู่เรือนจำ</td>
      </tr>
    <tr>
	  <td height="58" bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">&nbsp;จำนวน&nbsp;</td>
	  <td bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">&nbsp;แจ้งท้องที่ใหม่&nbsp;</td>
	  <td bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">&nbsp;ยังไม่แจ้ง&nbsp;</td>
	  <td bgcolor="#FDBD62" style="color: #00000; text-align: center; border-color: navy">&nbsp;จำนวน&nbsp;</td>
	  <td bgcolor="#FDBD62" style="color: #00000; text-align: center; border-color: navy">&nbsp;แจ้ง รจ.และทว.&nbsp;</td>
	  <td bgcolor="#FDBD62" style="color: #00000; text-align: center; border-color: navy">&nbsp;ยังไม่แจ้ง&nbsp;</td>
	  <td bgcolor="#FA9779" style="color: #00000; text-align: center; border-color: navy">&nbsp;จำนวน&nbsp;</td>
	  <td bgcolor="#FA9779" style="color: #00000; text-align: center; border-color: navy">&nbsp;แจ้ง ทว.&nbsp;</td>
	  <td bgcolor="#FA9779" style="color: #00000; text-align: center; border-color: navy">&nbsp;ยังไม่แจ้ง&nbsp;</td>
      </tr>
	  
<?php     
                      
$sql = "SELECT " .
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T1_1, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T1, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T2, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T3, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T4, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T5, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T6, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T7, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T8, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T9, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T10, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T11, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T12, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T13, " .
	
	// เขาบางแกรก
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6901) AS T1_1_kkl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6901) AS T1_kkl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6901) AS T2_kkl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6901) AS T3_kkl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6901) AS T4_kkl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6901) AS T5_kkl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6901) AS T6_kkl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6901) AS T7_kkl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6901) AS T8_kkl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6901) AS T9_kkl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6901) AS T10_kkl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6901) AS T11_kkl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6901) AS T12_kkl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6901) AS T13_kkl, " .
	// ตลุกดู่
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6902) AS T1_1_krm, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6902) AS T1_krm, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6902) AS T2_krm, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6902) AS T3_krm, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6902) AS T4_krm, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6902) AS T5_krm, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6902) AS T6_krm, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6902) AS T7_krm, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6902) AS T8_krm, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6902) AS T9_krm, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6902) AS T10_krm, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6902) AS T11_krm, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6902) AS T12_krm, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6902) AS T13_krm, " .
	// ทัพทัน
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6903) AS T1_1_tsl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6903) AS T1_tsl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6903) AS T2_tsl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6903) AS T3_tsl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6903) AS T4_tsl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6903) AS T5_tsl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6903) AS T6_tsl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6903) AS T7_tsl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6903) AS T8_tsl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6903) AS T9_tsl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6903) AS T10_tsl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6903) AS T11_tsl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6903) AS T12_tsl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6903) AS T13_tsl, " .
	// บ้านไร่
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6904) AS T1_1_bng, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6904) AS T1_bng, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6904) AS T2_bng, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6904) AS T3_bng, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6904) AS T4_bng, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6904) AS T5_bng, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6904) AS T6_bng, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6904) AS T7_bng, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6904) AS T8_bng, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6904) AS T9_bng, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6904) AS T10_bng, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6904) AS T11_bng, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6904) AS T12_bng, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6904) AS T13_bng, " .
	// เมืองการุ้ง
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6905) AS T1_1_bdh, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6905) AS T1_bdh, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6905) AS T2_bdh, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6905) AS T3_bdh, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6905) AS T4_bdh, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6905) AS T5_bdh, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6905) AS T6_bdh, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6905) AS T7_bdh, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6905) AS T8_bdh, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6905) AS T9_bdh, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6905) AS T10_bdh, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6905) AS T11_bdh, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6905) AS T12_bdh, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6905) AS T13_bdh, " .
	
	// เมืองอุทัยธานี
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6906) AS T1_1_bs, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6906) AS T1_bs, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6906) AS T2_bs, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6906) AS T3_bs, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6906) AS T4_bs, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6906) AS T5_bs, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6906) AS T6_bs, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6906) AS T7_bs, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6906) AS T8_bs, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6906) AS T9_bs, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6906) AS T10_bs, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6906) AS T11_bs, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6906) AS T12_bs, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6906) AS T13_bs, " .
	// ลานสัก
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6907) AS T1_1_mgk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6907) AS T1_mgk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6907) AS T2_mgk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6907) AS T3_mgk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6907) AS T4_mgk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6907) AS T5_mgk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6907) AS T6_mgk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6907) AS T7_mgk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6907) AS T8_mgk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6907) AS T9_mgk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6907) AS T10_mgk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6907) AS T11_mgk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6907) AS T12_mgk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6907) AS T13_mgk, " .
	// สว่างอารมณ์
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6908) AS T1_1_mst, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6908) AS T1_mst, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6908) AS T2_mst, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6908) AS T3_mst, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6908) AS T4_mst, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6908) AS T5_mst, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6908) AS T6_mst, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6908) AS T7_mst, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6908) AS T8_mst, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6908) AS T9_mst, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6908) AS T10_mst, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6908) AS T11_mst, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6908) AS T12_mst, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6908) AS T13_mst, " .
	// หนองขาหย่าง
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6909) AS T1_1_snk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6909) AS T1_snk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6909) AS T2_snk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6909) AS T3_snk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6909) AS T4_snk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6909) AS T5_snk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6909) AS T6_snk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6909) AS T7_snk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6909) AS T8_snk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6909) AS T9_snk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6909) AS T10_snk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6909) AS T11_snk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6909) AS T12_snk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6909) AS T13_snk, " .
	// หนองฉาง
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6910) AS T1_1_snl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6910) AS T1_snl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6910) AS T2_snl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6910) AS T3_snl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6910) AS T4_snl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6910) AS T5_snl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6910) AS T6_snl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6910) AS T7_snl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6910) AS T8_snl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6910) AS T9_snl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6910) AS T10_snl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6910) AS T11_snl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6910) AS T12_snl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6910) AS T13_snl, " .
	// ห้วยคต
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6911) AS T1_1_ssr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6911) AS T1_ssr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6911) AS T2_ssr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6911) AS T3_ssr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6911) AS T4_ssr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6911) AS T5_ssr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6911) AS T6_ssr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6911) AS T7_ssr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6911) AS T8_ssr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6911) AS T9_ssr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6911) AS T10_ssr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6911) AS T11_ssr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6911) AS T12_ssr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6911) AS T13_ssr " .
	
        " ";

$stmt = $pdo->prepare($sql);
$stmt->bindParam(':month', $month);
$stmt->bindParam(':year', $year);
$stmt->bindParam(':provincial', $provincial); // Add station parameter
try {
    $stmt->execute();
} catch (PDOException $e) {
    echo '$sql Failed: '. $e->getMessage();
}

          
// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no = 1;
while($row = $stmt->fetch(PDO::FETCH_ASSOC))	//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
{
//ฟังก์ชั่น วันที่ ดึงจาก condb
//$strDate = DateThai( $row_mi["date_record"] );
    
if($row["T1"] > 0) {   
    $persen = ($row["T2"]*100)/$row["T1"];
}
else {
    $persen=0;
    }
    $sum1 = $row["T1"] + $row["T5"] + $row["T8"] + $row["T11"];
    $sum2 = $row["T1_1"] - $sum1;

// เขาบางแกรก
if($row["T1_kkl"] > 0) {   
    $persen_kkl = ($row["T2_kkl"]*100)/$row["T1_kkl"];
}
else {
    $persen_kkl=0;
    }
    $sum1_kkl = $row["T1_kkl"] + $row["T5_kkl"] + $row["T8_kkl"] + $row["T11_kkl"];
    $sum2_kkl = $row["T1_1_kkl"] - $sum1_kkl;
	
// ตลุกดู่
if($row["T1_krm"] > 0) {   
    $persen_krm = ($row["T2_krm"]*100)/$row["T1_krm"];
}
else {
    $persen_krm=0;
    }
    $sum1_krm = $row["T1_krm"] + $row["T5_krm"] + $row["T8_krm"] + $row["T11_krm"];
    $sum2_krm = $row["T1_1_krm"] - $sum1_krm;
	
// ทัพทัน
if($row["T1_tsl"] > 0) {   
    $persen_tsl = ($row["T2_tsl"]*100)/$row["T1_tsl"];
}
else {
    $persen_tsl=0;
    }
    $sum1_tsl = $row["T1_tsl"] + $row["T5_tsl"] + $row["T8_tsl"] + $row["T11_tsl"];
    $sum2_tsl = $row["T1_1_tsl"] - $sum1_tsl;
	
// บ้านไร่
if($row["T1_bng"] > 0) {   
    $persen_bng = ($row["T2_bng"]*100)/$row["T1_bng"];
}
else {
    $persen_bng=0;
    }
    $sum1_bng = $row["T1_bng"] + $row["T5_bng"] + $row["T8_bng"] + $row["T11_bng"];
    $sum2_bng = $row["T1_1_bng"] - $sum1_bng;
	
// เมืองการุ้ง
if($row["T1_bdh"] > 0) {   
    $persen_bdh = ($row["T2_bdh"]*100)/$row["T1_bdh"];
}
else {
    $persen_bdh=0;
    }
    $sum1_bdh = $row["T1_bdh"] + $row["T5_bdh"] + $row["T8_bdh"] + $row["T11_bdh"];
    $sum2_bdh = $row["T1_1_bdh"] - $sum1_bdh;
	
// เมืองอุทัยธานี
if($row["T1_bs"] > 0) {   
    $persen_bs = ($row["T2_bs"]*100)/$row["T1_bs"];
}
else {
    $persen_bs=0;
    }
    $sum1_bs = $row["T1_bs"] + $row["T5_bs"] + $row["T8_bs"] + $row["T11_bs"];
    $sum2_bs = $row["T1_1_bs"] - $sum1_bs;
	
// ลานสัก
if($row["T1_mgk"] > 0) {   
    $persen_mgk = ($row["T2_mgk"]*100)/$row["T1_mgk"];
}
else {
    $persen_mgk=0;
    }
    $sum1_mgk = $row["T1_mgk"] + $row["T5_mgk"] + $row["T8_mgk"] + $row["T11_mgk"];
    $sum2_mgk = $row["T1_1_mgk"] - $sum1_mgk;
	
// สว่างอารมณ์
if($row["T1_mst"] > 0) {   
    $persen_mst = ($row["T2_mst"]*100)/$row["T1_mst"];
}
else {
    $persen_mst=0;
    }
    $sum1_mst = $row["T1_mst"] + $row["T5_mst"] + $row["T8_mst"] + $row["T11_mst"];
    $sum2_mst = $row["T1_1_mst"] - $sum1_mst;
	
// หนองขาหย่าง
if($row["T1_snk"] > 0) {   
    $persen_snk = ($row["T2_snk"]*100)/$row["T1_snk"];
}
else {
    $persen_snk=0;
    }
    $sum1_snk = $row["T1_snk"] + $row["T5_snk"] + $row["T8_snk"] + $row["T11_snk"];
    $sum2_snk = $row["T1_1_snk"] - $sum1_snk;
	
// หนองฉาง
if($row["T1_snl"] > 0) {   
    $persen_snl = ($row["T2_snl"]*100)/$row["T1_snl"];
}
else {
    $persen_snl=0;
    }
    $sum1_snl = $row["T1_snl"] + $row["T5_snl"] + $row["T8_snl"] + $row["T11_snl"];
    $sum2_snl = $row["T1_1_snl"] - $sum1_snl;
	
// ห้วยคต
if($row["T1_ssr"] > 0) {   
    $persen_ssr = ($row["T2_ssr"]*100)/$row["T1_ssr"];
}
else {
    $persen_ssr=0;
    }
    $sum1_ssr = $row["T1_ssr"] + $row["T5_ssr"] + $row["T8_ssr"] + $row["T11_ssr"];
    $sum2_ssr = $row["T1_1_ssr"] - $sum1_ssr;
	
?>	

    <tr>
      <td nowrap style="text-align: left; font-size: 22px; font-weight: bold">&nbsp;<?php echo $name_provincial ?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T1_1"] > 0) ? $row["T1_1"]  : "") ?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T1"] > 0) ? $row["T1"]  : "") ?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T2"] > 0) ? $row["T2"]  : "") ?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold"><?php echo $persen ?>&nbsp;%</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T3"] > 0) ? $row["T3"]  : "") ?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T4"] > 0) ? $row["T4"]  : "") ?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T5"] > 0) ? $row["T5"]  : "")?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T6"] > 0) ? $row["T6"]  : "")?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T7"] > 0) ? $row["T7"]  : "")?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T8"] > 0) ? $row["T8"]  : "")?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T9"] > 0) ? $row["T9"]  : "")?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T10"] > 0) ? $row["T10"]  : "")?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T11"] > 0) ? $row["T11"]  : "")?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T12"] > 0) ? $row["T12"]  : "")?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T13"] > 0) ? $row["T13"]  : "")?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($sum1 > 0) ? $sum1  : "") ?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($sum2 > 0) ? $sum2  : "") ?>&nbsp;</td>
    </tr>
	<!-- เขาบางแกรก -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.เขาบางแกรก&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_kkl"] > 0) ? $row["T1_1_kkl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_kkl"] > 0) ? $row["T1_kkl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_kkl"] > 0) ? $row["T2_kkl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?php echo $persen_kkl ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_kkl"] > 0) ? $row["T3_kkl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_kkl"] > 0) ? $row["T4_kkl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_kkl"] > 0) ? $row["T5_kkl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_kkl"] > 0) ? $row["T6_kkl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_kkl"] > 0) ? $row["T7_kkl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_kkl"] > 0) ? $row["T8_kkl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_kkl"] > 0) ? $row["T9_kkl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_kkl"] > 0) ? $row["T10_kkl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_kkl"] > 0) ? $row["T11_kkl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_kkl"] > 0) ? $row["T12_kkl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_kkl"] > 0) ? $row["T13_kkl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_kkl > 0) ? $sum1_kkl : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_kkl > 0) ? $sum2_kkl : "") ?>&nbsp;</td>
    </tr>
	<!-- ตลุกดู่ -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.ตลุกดู่&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_krm"] > 0) ? $row["T1_1_krm"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_krm"] > 0) ? $row["T1_krm"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_krm"] > 0) ? $row["T2_krm"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?= $persen_krm ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_krm"] > 0) ? $row["T3_krm"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_krm"] > 0) ? $row["T4_krm"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_krm"] > 0) ? $row["T5_krm"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_krm"] > 0) ? $row["T6_krm"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_krm"] > 0) ? $row["T7_krm"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_krm"] > 0) ? $row["T8_krm"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_krm"] > 0) ? $row["T9_krm"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_krm"] > 0) ? $row["T10_krm"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_krm"] > 0) ? $row["T11_krm"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_krm"] > 0) ? $row["T12_krm"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_krm"] > 0) ? $row["T13_krm"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_krm > 0) ? $sum1_krm : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_krm > 0) ? $sum2_krm : "")?>&nbsp;</td>
    </tr> 
	<!-- ทัพทัน -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.ทัพทัน&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_tsl"] > 0) ? $row["T1_1_tsl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_tsl"] > 0) ? $row["T1_tsl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_tsl"] > 0) ? $row["T2_tsl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?= $persen_tsl ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_tsl"] > 0) ? $row["T3_tsl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_tsl"] > 0) ? $row["T4_tsl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_tsl"] > 0) ? $row["T5_tsl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_tsl"] > 0) ? $row["T6_tsl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_tsl"] > 0) ? $row["T7_tsl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_tsl"] > 0) ? $row["T8_tsl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_tsl"] > 0) ? $row["T9_tsl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_tsl"] > 0) ? $row["T10_tsl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_tsl"] > 0) ? $row["T11_tsl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_tsl"] > 0) ? $row["T12_tsl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_tsl"] > 0) ? $row["T13_tsl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_tsl > 0) ? $sum1_tsl : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_tsl > 0) ? $sum2_tsl : "")?>&nbsp;</td>
    </tr> 
	<!-- บ้านไร่ -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.บ้านไร่&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_bng"] > 0) ? $row["T1_1_bng"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_bng"] > 0) ? $row["T1_bng"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_bng"] > 0) ? $row["T2_bng"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?= $persen_bng ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_bng"] > 0) ? $row["T3_bng"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_bng"] > 0) ? $row["T4_bng"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_bng"] > 0) ? $row["T5_bng"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_bng"] > 0) ? $row["T6_bng"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_bng"] > 0) ? $row["T7_bng"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_bng"] > 0) ? $row["T8_bng"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_bng"] > 0) ? $row["T9_bng"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_bng"] > 0) ? $row["T10_bng"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_bng"] > 0) ? $row["T11_bng"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_bng"] > 0) ? $row["T12_bng"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_bng"] > 0) ? $row["T13_bng"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_bng > 0) ? $sum1_bng : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_bng > 0) ? $sum2_bng : "")?>&nbsp;</td>
    </tr> 
	<!-- เมืองการุ้ง -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.เมืองการุ้ง&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_bdh"] > 0) ? $row["T1_1_bdh"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_bdh"] > 0) ? $row["T1_bdh"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_bdh"] > 0) ? $row["T2_bdh"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?= $persen_bdh ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_bdh"] > 0) ? $row["T3_bdh"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_bdh"] > 0) ? $row["T4_bdh"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_bdh"] > 0) ? $row["T5_bdh"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_bdh"] > 0) ? $row["T6_bdh"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_bdh"] > 0) ? $row["T7_bdh"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_bdh"] > 0) ? $row["T8_bdh"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_bdh"] > 0) ? $row["T9_bdh"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_bdh"] > 0) ? $row["T10_bdh"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_bdh"] > 0) ? $row["T11_bdh"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_bdh"] > 0) ? $row["T12_bdh"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_bdh"] > 0) ? $row["T13_bdh"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_bdh > 0) ? $sum1_bdh : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_bdh > 0) ? $sum2_bdh : "")?>&nbsp;</td>
    </tr> 
	<!-- เมืองอุทัยธานี -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.เมืองอุทัยธานี&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_bs"] > 0) ? $row["T1_1_bs"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_bs"] > 0) ? $row["T1_bs"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_bs"] > 0) ? $row["T2_bs"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?= $persen_bs ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_bs"] > 0) ? $row["T3_bs"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_bs"] > 0) ? $row["T4_bs"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_bs"] > 0) ? $row["T5_bs"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_bs"] > 0) ? $row["T6_bs"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_bs"] > 0) ? $row["T7_bs"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_bs"] > 0) ? $row["T8_bs"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_bs"] > 0) ? $row["T9_bs"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_bs"] > 0) ? $row["T10_bs"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_bs"] > 0) ? $row["T11_bs"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_bs"] > 0) ? $row["T12_bs"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_bs"] > 0) ? $row["T13_bs"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_bs > 0) ? $sum1_bs : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_bs > 0) ? $sum2_bs : "")?>&nbsp;</td>
    </tr> 
	<!-- ลานสัก -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.ลานสัก&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_mgk"] > 0) ? $row["T1_1_mgk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_mgk"] > 0) ? $row["T1_mgk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_mgk"] > 0) ? $row["T2_mgk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?= $persen_mgk ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_mgk"] > 0) ? $row["T3_mgk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_mgk"] > 0) ? $row["T4_mgk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_mgk"] > 0) ? $row["T5_mgk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_mgk"] > 0) ? $row["T6_mgk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_mgk"] > 0) ? $row["T7_mgk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_mgk"] > 0) ? $row["T8_mgk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_mgk"] > 0) ? $row["T9_mgk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_mgk"] > 0) ? $row["T10_mgk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_mgk"] > 0) ? $row["T11_mgk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_mgk"] > 0) ? $row["T12_mgk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_mgk"] > 0) ? $row["T13_mgk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_mgk > 0) ? $sum1_mgk : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_mgk > 0) ? $sum2_mgk : "")?>&nbsp;</td>
    </tr>
	<!-- สว่างอารมณ์ -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.สว่างอารมณ์&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_mst"] > 0) ? $row["T1_1_mst"] : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_mst"] > 0) ? $row["T1_mst"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_mst"] > 0) ? $row["T2_mst"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?= $persen_mst ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_mst"] > 0) ? $row["T3_mst"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_mst"] > 0) ? $row["T4_mst"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_mst"] > 0) ? $row["T5_mst"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_mst"] > 0) ? $row["T6_mst"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_mst"] > 0) ? $row["T7_mst"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_mst"] > 0) ? $row["T8_mst"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_mst"] > 0) ? $row["T9_mst"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_mst"] > 0) ? $row["T10_mst"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_mst"] > 0) ? $row["T11_mst"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_mst"] > 0) ? $row["T12_mst"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_mst"] > 0) ? $row["T13_mst"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_mst > 0) ? $sum1_mst : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_mst > 0) ? $sum2_mst : "") ?>&nbsp;</td>
    </tr> 
	<!-- หนองขาหย่าง -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.หนองขาหย่าง&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_snk"] > 0) ? $row["T1_1_snk"] : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_snk"] > 0) ? $row["T1_snk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_snk"] > 0) ? $row["T2_snk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?php echo $persen_snk ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_snk"] > 0) ? $row["T3_snk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_snk"] > 0) ? $row["T4_snk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_snk"] > 0) ? $row["T5_snk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_snk"] > 0) ? $row["T6_snk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_snk"] > 0) ? $row["T7_snk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_snk"] > 0) ? $row["T8_snk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_snk"] > 0) ? $row["T9_snk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_snk"] > 0) ? $row["T10_snk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_snk"] > 0) ? $row["T11_snk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_snk"] > 0) ? $row["T12_snk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_snk"] > 0) ? $row["T13_snk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_snk > 0) ? $sum1_snk : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_snk > 0) ? $sum2_snk : "") ?>&nbsp;</td>
    </tr> 
	<!-- หนองฉาง -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.หนองฉาง&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_snl"] > 0) ? $row["T1_1_snl"] : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_snl"] > 0) ? $row["T1_snl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_snl"] > 0) ? $row["T2_snl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?php echo $persen_snl ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_snl"] > 0) ? $row["T3_snl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_snl"] > 0) ? $row["T4_snl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_snl"] > 0) ? $row["T5_snl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_snl"] > 0) ? $row["T6_snl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_snl"] > 0) ? $row["T7_snl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_snl"] > 0) ? $row["T8_snl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_snl"] > 0) ? $row["T9_snl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_snl"] > 0) ? $row["T10_snl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_snl"] > 0) ? $row["T11_snl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_snl"] > 0) ? $row["T12_snl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_snl"] > 0) ? $row["T13_snl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_snl > 0) ? $sum1_snl : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_snl > 0) ? $sum2_snl : "") ?>&nbsp;</td>
    </tr> 
	<!-- ห้วยคต -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.ห้วยคต&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_ssr"] > 0) ? $row["T1_1_ssr"] : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_ssr"] > 0) ? $row["T1_ssr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_ssr"] > 0) ? $row["T2_ssr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?php echo $persen_ssr ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_ssr"] > 0) ? $row["T3_ssr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_ssr"] > 0) ? $row["T4_ssr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_ssr"] > 0) ? $row["T5_ssr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_ssr"] > 0) ? $row["T6_ssr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_ssr"] > 0) ? $row["T7_ssr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_ssr"] > 0) ? $row["T8_ssr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_ssr"] > 0) ? $row["T9_ssr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_ssr"] > 0) ? $row["T10_ssr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_ssr"] > 0) ? $row["T11_ssr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_ssr"] > 0) ? $row["T12_ssr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_ssr"] > 0) ? $row["T13_ssr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_ssr > 0) ? $sum1_ssr : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_ssr > 0) ? $sum2_ssr : "") ?>&nbsp;</td>
    </tr> 
  </tbody>
<?php
        
	$no++;
}//while
?>
</table>
</div>
</div>
</div>
</body>

