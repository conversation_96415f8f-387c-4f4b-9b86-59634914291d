<?php
include '../Condb.php';
include '../users.inc.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// เมื่อมีการใช้ ตัวแปร Session จะต้องเปิดก่อน
//session_start();  // เปิดตัวแปร session
/*if (session_status() == PHP_SESSION_NONE) {
    session_start();
}*/


// เฉพาะ ยูเซอร์ บ้านเสวน = 1 เท่านั้นที่จะดูหน้านี้ได้
//if($user['ua_view_data'] != 1)
//{
//	header("Location: /WatchmanData/main.php");
//}
//
//$id = isset($_GET['shoot_aid']) ? $_GET['shoot_aid'] : '';
//
////get month from select or current month
//$month = isset($_GET['month']) ? $_GET['month'] : date("m");
//$year = isset($_GET['year']) ? $_GET['year'] : date("Y");
//
//$m1 = '';
//$m2 = '';
//$m3 = '';
//$m4 = '';
//$m5 = '';
//$m6 = '';
//$m7 = '';
//$m8 = '';
//$m9 = '';
//$m10 = '';
//$m11 = '';
//$m12 = '';
//switch($month)
//{
//    case "01": $m1 = "selected"; break;
//    case "02": $m2 = "selected"; break;
//    case "03": $m3 = "selected"; break;
//    case "04": $m4 = "selected"; break;
//    case "05": $m5 = "selected"; break;
//    case "06": $m6 = "selected"; break;
//    case "07": $m7 = "selected"; break;
//    case "08": $m8 = "selected"; break;
//    case "09": $m9 = "selected"; break;
//    case "10": $m10 = "selected"; break;
//    case "11": $m11 = "selected"; break;
//    case "12": $m12 = "selected"; break;
//}
//$current_month = $global_thai_month[0+$month];
//$y2 = $year+543;

?>

<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
        <base target="_top">
		<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
		<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
  		<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
 		<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js"></script>

<style type="text/css">
body{
    font-size:12px; 
}
.textAlignVer{
    display:block;
    filter: flipv fliph;
    -webkit-transform: rotate(-90deg); 
    -moz-transform: rotate(-90deg); 
    transform: rotate(-90deg); 
    position:relative;
    width:20px;
    white-space:nowrap;
    font-size:12px;
    margin-bottom:10px;
}
</style>
<p><img src="../Image/Head2.jpg" width="100%" height="" alt=""/></p>
<div class="container-fluid" align="left">
<div class=" h3 text-center  alert alert-success mb-4 mt-4 " role="alert" >ทะเบียนคุมอาวุธปืนสำหรับการยิงเก็บประวัติของ สภ.บ้านสวน (แบบ TF1)</div>

<div align="left">
<div>
    <table width="84%" border="0" cellspacing="1" cellpadding="1">
      <tbody>
        <tr>
          <td width="12%">&nbsp;<a href="Show_Activity15.php?rnd=<?= rand(); ?>" class="btn btn-warning btn-lg mb-4" >ย้อนกลับ</a></td>
          <td width="88%">&nbsp;<a href="Add_history_shooting.php" class="btn btn-success btn-lg mb-4" >เพิ่มข้อมูล</a></td>
          
        </tr>
      </tbody>
    </table>
</div>
<div class="table-responsive">
  <table border="1" cellpadding="1" cellspacing="1" class="table-bordered table-hover mb-4 mt-4">
  <tbody>
    <tr>
      <td bgcolor="#C8F96D" style="color: #00000; text-align: center; border-color: navy">&nbsp;ลำดับ&nbsp;</td>
      <td bgcolor="#C8F96D" style="color: #00000; text-align: center; border-color: navy">&nbsp;รหัสการจัดเก็บ&nbsp;</td>
      <td height="56" bgcolor="#C8F96D" style="color: #00000; text-align: center; border-color: navy">&nbsp;วันเดือนปีที่ยิง&nbsp;</td>
      <td height="56" bgcolor="#C8F96D" style="color: #00000; text-align: center; border-color: navy">&nbsp;เลขที่ใบอนุญาต (ป4)&nbsp;</td>
      <td height="56" bgcolor="#C8F96D" style="color: #00000; text-align: center; border-color: navy">&nbsp;ชื่อผู้ครอบครองอาวุธปืน&nbsp;</td>
      <td height="56" bgcolor="#C8F96D" style="color: #00000; text-align: center; border-color: navy">&nbsp;ชนิดของปืน&nbsp;</td>
      <td height="56" bgcolor="#C8F96D" style="color: #00000; text-align: center; border-color: navy">&nbsp;ขนาดของปืน&nbsp;</td>
      <td height="56" bgcolor="#C8F96D" style="color: #00000; text-align: center; border-color: navy">&nbsp;ยี่ห้อปืน&nbsp;</td>
      <td height="56" bgcolor="#C8F96D" style="color: #00000; text-align: center; border-color: navy">&nbsp;รุ่นปืน&nbsp;</td>
      <td bgcolor="#F2C0AD" style="color: #00000; text-align: center; border-color: navy">&nbsp;ผู้ยิงจัดเก็บ&nbsp;</td>
      <td bgcolor="#F2C0AD" style="color: #00000; text-align: center; border-color: navy">&nbsp;ผู้บันทึก&nbsp;</td>
    </tr>
	  
<?php
$sql = "SELECT * FROM wm_tb_gun_history_shooting" ;
$result = mysqli_query($conn,$sql);
                      
// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no = 1;
while($row = mysqli_fetch_array($result))	//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
{
    
    //ฟังก์ชั่น วันที่ ดึงจาก condb
 $strDate = DateThai( $row["date"] );

?>	

    <tr>
      <td height="69"><?= $no ?></td>
      <td nowrap>&nbsp;<?= $row["code"]?>&nbsp;</td>
      <td nowrap>&nbsp;<?= $strDate ?>&nbsp;</td>
      <td>&nbsp;<?= $row["license_number"] ?>&nbsp;</td>
      <td>&nbsp;<?= $row["owner"]?>&nbsp;</td>
      <td>&nbsp;<?= $row["type"] ?>&nbsp;</td>
      <td>&nbsp;<?= $row["size"] ?>&nbsp;</td>
      <td>&nbsp;<?= $row["brand"]?>&nbsp;</td>
      <td>&nbsp;<?= $row["model"]?>&nbsp;</td>
      <td>&nbsp;<?= $row["shooter"]?>&nbsp;</td>
      <td>&nbsp;<?= $row["recorder"]?>&nbsp;</td>
    </tr>
<?php
	$no++;
}//while
?>
  </tbody>
</table>
</div>
    
    
<script>
function Del(url)
{
	if(confirm("คุณจะลบข้อมูล หรือ ไม่ ?"))
	{
		window.location = url;		
	}
}
function dates_change()  // คำสั่งเปลี่ยนเดือน ปี
{
    var ms = $("#mi_cl_month option:selected").val();
    var ys = $("#mi_cl_year option:selected").val();
    window.location = "/Activity/Show_Activity15.php?&month=" + ms + "&year=" + ys + "&rnd=" + Math.random();
}
</script>

<br>
<hr>
<br>

    

