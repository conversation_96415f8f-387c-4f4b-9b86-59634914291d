<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../Alert.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);
$ip_address = $_SERVER['REMOTE_ADDR'];
$action = 'Save confidential gun'; // You can set the action to a suitable value
$data = str_replace("'", "", compact_array($_POST));
log_activity($acc, $action, $data, $ip_address); // ใช้ function ในไฟล์ Condb.php

/*echo '<pre>';
print_r($_POST);
echo '</pre>';

echo '<hr>';
exit();*/

$aid_conf_gun = isset($_POST['aid_conf_gun']) ? $_POST['aid_conf_gun'] : '';
$region = isset($_POST['region']) ? $_POST['region'] : '';
$provincial = isset($_POST['provincial']) ? $_POST['provincial'] : '';
$station = isset($_POST['station']) ? $_POST['station'] : '';
$case_no = isset($_POST['case_no']) ? $_POST['case_no'] : '';
$inspect_no = isset($_POST['inspect_no']) ? $_POST['inspect_no'] : '';
$gun_treasure_detail = isset($_POST['gun_treasure_detail']) ? $_POST['gun_treasure_detail'] : '';
$inquiry_official = isset($_POST['inquiry_official']) ? $_POST['inquiry_official'] : '';
$confidential_gun = isset($_POST['confidential_gun']) ? $_POST['confidential_gun'] : '';
$unique_guns = isset($_POST['unique_guns']) ? $_POST['unique_guns'] : '';
$unique_guns_unknow = isset($_POST['unique_guns_unknow']) ? $_POST['unique_guns_unknow'] : '';
$sanpawut = isset($_POST['sanpawut']) ? $_POST['sanpawut'] : '';
$DistrictChief_co = isset($_POST['DistrictChief_co']) ? $_POST['DistrictChief_co'] : '';
$DistrictChief_revoke = isset($_POST['DistrictChief_revoke']) ? $_POST['DistrictChief_revoke'] : '';
$no_identify = isset($_POST['no_identify']) ? $_POST['no_identify'] : '';
$no_identify_unknow = isset($_POST['no_identify_unknow']) ? $_POST['no_identify_unknow'] : '';
$destroy_unknow = isset($_POST['destroy_unknow']) ? $_POST['destroy_unknow'] : '';
$status = isset($_POST['status']) ? $_POST['status'] : '';
$record_name = isset($_POST['record_name']) ? $_POST['record_name'] : '';
$record_date = isset($_POST['record_date']) ? $_POST['record_date'] : '';

// save file สส.1
$file1 = $_FILES[ 'conf_gun_file' ][ 'tmp_name' ];
if ( is_uploaded_file( $file1 ) ) {
  $conf_gun_file = "uploaded/Doc/" . $_FILES[ 'conf_gun_file' ][ 'name' ];	
  move_uploaded_file( $file1, "../" . $conf_gun_file );  
} else {
  $conf_gun_file = '';
}

// Update existing record
    $sql = "UPDATE wm_tb_gun_confidential SET region=?, provincial=?, station=?, case_no=?, inspect_no=?, gun_treasure_detail=?, inquiry_official=?, confidential_gun=?, unique_guns=?, unique_guns_unknow=?, sanpawut=?, DistrictChief_co=?, DistrictChief_revoke=?, no_identify=?, no_identify_unknow=?, destroy_unknow=?, status=?, record_name=?, record_date=?, conf_gun_file=? WHERE aid_conf_gun=?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$region, $provincial, $station, $case_no, $inspect_no, $gun_treasure_detail, $inquiry_official, $confidential_gun, $unique_guns, $unique_guns_unknow, $sanpawut, $DistrictChief_co, $DistrictChief_revoke, $no_identify, $no_identify_unknow, $destroy_unknow, $status, $record_name, $record_date, $conf_gun_file, $aid_conf_gun]);


if ($stmt->rowCount() > 0) {
    $_SESSION['success'] = "Data has been inserted succesfully";
    showSweetAlert("Saved successfully", "บันทึกข้อมูลเรียบร้อย");
    unset($_SESSION['success']); // Clear the session variable
    header("refresh:2; url=/WatchmanData/Show_gun_treasure_all.php");
} else {
    $_SESSION['error'] = "Error";
    showSweetAlert('Error', "บันทึกข้อมูล ไม่สำเร็จ", 'error');
    unset($_SESSION['error']); // Clear the session variable
    header("refresh:2; url=/WatchmanData/Show_gun_treasure_all.php");
}

$pdo = null;

?>