<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';
include '../Head_image.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

$id = isset($_GET['sur_aid']) ? $_GET['sur_aid'] : '';

//get month from select or current month
$month = isset($_GET['month']) ? $_GET['month'] : date("m");
$year = isset($_GET['year']) ? $_GET['year'] : date("Y");

$m1 = '';
$m2 = '';
$m3 = '';
$m4 = '';
$m5 = '';
$m6 = '';
$m7 = '';
$m8 = '';
$m9 = '';
$m10 = '';
$m11 = '';
$m12 = '';
switch($month)
{
    case "01": $m1 = "selected"; break;
    case "02": $m2 = "selected"; break;
    case "03": $m3 = "selected"; break;
    case "04": $m4 = "selected"; break;
    case "05": $m5 = "selected"; break;
    case "06": $m6 = "selected"; break;
    case "07": $m7 = "selected"; break;
    case "08": $m8 = "selected"; break;
    case "09": $m9 = "selected"; break;
    case "10": $m10 = "selected"; break;
    case "11": $m11 = "selected"; break;
    case "12": $m12 = "selected"; break;
}
$current_month = $global_thai_month[0+$month];
$y2 = $year+543;

?>

<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
        <base target="_top">
        <link rel="stylesheet" href="/bootstrap/css/bootstrap-grid.css">
        <link rel="stylesheet" href="../bootstrap-3.4.1-dist/css/bootstrap.min.css">
		<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
		<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
  		<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
 		<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js"></script>
<style>
    .bgcolor{
        background-color: burlywood;
        color: black;
    }
    .bg2{
        background: #D9F505;
        border: #0E0001;
    }
</style>

<style type="text/css">
body{
    font-size:12px; 
}
.textAlignVer{
    display:block;
    filter: flipv fliph;
    -webkit-transform: rotate(-90deg); 
    -moz-transform: rotate(-90deg); 
    transform: rotate(-90deg); 
    position:relative;
    width:20px;
    white-space:nowrap;
    font-size:12px;
    margin-bottom:10px;
}
</style>
<!--<p><img src="../Image/Head2.jpg" width="100%" height="" alt=""/></p>-->
<div class="container-fluid" align="left">
<div class=" h3 text-center  alert alert-success mb-4 mt-4 " role="alert" >กิจกรรม 2 การสำรวจและติดตั้งกล้องวงจรปิดเพิ่มเติม </div>

<div align="left">
<div>
    <table width="80%" border="0" cellspacing="1" cellpadding="1">
      <tbody>
        <tr>
          <td width="11%">&nbsp;<a href="/WM/index.php?rnd=<?= rand(); ?>&page=report" class="btn btn-warning btn-lg mb-4" >ย้อนกลับ</a></td>
          
          <td width="9%"><label>เลือกปี </label>
            <select id="mi_cl_year" name="mi_cl_year" class="form-select col-8 d-lg-inline" style="background-color: khaki; font-size: 16px" onChange="dates_change()">
              <option value="">เลือก</option>
              <?php
                $cur_y = date("Y") + 2;
                for($y=2020; $y<$cur_y; $y++) {
                    $sel = ($y == $year) ? "selected" : '';
                    echo "<option value='$y' $sel> ". ($y + 543) ." </option>";
                }
            ?>
          </select></td>
          <td width="1%"></td>
          <td width="12%">&nbsp;&nbsp;<label>เดือน</label>
            <select id="mi_cl_month" name="mi_cl_month" class="form-select col-3 d-lg-inline" style="background-color: khaki; font-size: 16px" onChange="dates_change()">
              <option value="">เลือก</option>
              <option <?= $m1 ?> value="01">มกราคม</option>
              <option <?= $m2 ?> value="02">กุมภาพันธ์</option>
              <option <?= $m3 ?> value="03">มีนาคม</option>
              <option <?= $m4 ?> value="04">เมษายน</option>
              <option <?= $m5 ?> value="05">พฤษภาคม</option>
              <option <?= $m6 ?> value="06">มิถุนายน</option>
              <option <?= $m7 ?> value="07">กรกฎาคม</option>
              <option <?= $m8 ?> value="08">สิงหาคม</option>
              <option <?= $m9 ?> value="09">กันยายน</option>
              <option <?= $m10 ?> value="10">ตุลาคม</option>
              <option <?= $m11 ?> value="11">พฤศจิกายน</option>
              <option <?= $m12 ?> value="12">ธันวาคม</option>
          </select></td>
          <td width="61%">&nbsp;</td>
          <td width="6%">&nbsp;</td>
        </tr>
      </tbody>
    </table>
</div>
    
<!------------------------------------------------------->
<!------------------------------------------------------->
<!------------------------------------------------------->

<h3 align="center">ข้อมูลการสำรวจกล้องวงจรปิด (แยกรายเดือน)</h3>
<h3 align="center">ประจำเดือน&nbsp; <?= $current_month ?> &nbsp;<?= $y2 ?> &nbsp;</h3>
<div class="table-responsive">
<table class="table table-striped table-hover" width="80%" border="0" cellspacing="1" cellpadding="1">
  <tbody>
    <tr>
      <td width="4%" rowspan="2" style="border: solid black; text-align: center">ลำดับ</td>
      <td width="6%" rowspan="2" style="border: solid black; text-align: center">วันเดือนปี</td>
      <td colspan="6" style="border: solid black; text-align: center" bgcolor="coral" !important>จุดที่เกิดอาชญากรรมบ่อย</td>
      <td colspan="6" style="border: solid black; text-align: center">จุดเสี่ยงจุดล่อแหลม</td>
      <td colspan="6" style="border: solid black; text-align: center">เส้นทางหลัก</td>
      <td width="7%" rowspan="2" style="border: solid black; text-align: center">ไฟล์เอกสาร</td>
    </tr>
    <tr>
      <td style="border: solid black">จำนวน</td>
      <td style="border: solid black">ผล</td>
      <td style="border: solid black">จุด</td>
      <td style="border: solid black">ผล</td>
      <td style="border: solid black">กล้อง</td>
      <td style="border: solid black">ผล</td>
      <td style="border: solid black">จำนวน</td>
      <td style="border: solid black">ผล</td>
      <td style="border: solid black">จุด</td>
      <td style="border: solid black">ผล</td>
      <td style="border: solid black">กล้อง</td>
      <td style="border: solid black">ผล</td>
      <td style="border: solid black">จำนวน</td>
      <td style="border: solid black">ผล</td>
      <td style="border: solid black">จุด</td>
      <td style="border: solid black">ผล</td>
      <td style="border: solid black">กล้อง</td>
      <td style="border: solid black">ผล</td>
      </tr>
	  
<?php
$sql2 = "SELECT *
        FROM
            wm_tb_CCTV_survey 
        WHERE
            MONTH(sur_date_ac)=:month AND YEAR(sur_date_ac)=:year
        ORDER BY
            sur_date_ac ASC ";  //ตั้งตัวแปร sql เลือกฐานข้อมูลจากตารางที่ชื่อ wm_tb_personal
                      
if($id != ''){
	$sql2 = $sql2 . " WHERE sur_aid=:id ";
}

    $stmt2 = $pdo->prepare($sql2);
    $stmt2->bindParam(':month', $month);
    $stmt2->bindParam(':year', $year);

if ($id != '') {
    $stmt2->bindParam(':id', $id);
}

try {
    $stmt2->execute();
} catch (PDOException $e) {
    echo '$sql2 Failed: '. $e->getMessage();
}

// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no2 = 1;
while($row_mi = $stmt2->fetch(PDO::FETCH_ASSOC))	//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
{
    
    //ฟังก์ชั่น วันที่ ดึงจาก condb
 $strDate = DateThai( $row_mi["sur_date_ac"] );
    
    //ตรวจสอบไฟล์ สส.1 ก่อนว่า มีไฟล์ในตารางไหม หากมี ให้ส่งเป็นลิงค์
	$pdf = $row_mi["sur_file"];
	if($pdf !== '')
	{
		$links = '<a href="/' . $row_mi["sur_file"] .'" target="_blank">Download</a>';
	}
	else {
		$links = 'ไม่มีไฟล์';
	}
    
?>	

    <tr>
	<!--  <td><?= $no2++ ?> </td> -->
	  <td style=border:solid><?= $row_mi["sur_aid"]?></td>  
      <td nowrap="nowrap" style=border:solid>&nbsp;<?= $strDate ?>&nbsp;</td>
      <td style=border:solid><?php echo (($row_mi["sur_crimes_n"] > 0) ? $row_mi["sur_crimes_n"] : "") ?></td>
      <td style=border:solid>&nbsp;</td>
      <td style=border:solid><?php echo (($row_mi["sur_crimes_point"] > 0) ? $row_mi["sur_crimes_point"] : "") ?></td>
      <td style=border:solid>&nbsp;</td>
      <td style=border:solid><?php echo (($row_mi["sur_crimes_cctv"] > 0) ? $row_mi["sur_crimes_cctv"] : "") ?></td>
      <td style=border:solid>&nbsp;</td>
      <td style=border:solid><?php echo (($row_mi["sur_risk_n"] > 0) ? $row_mi["sur_risk_n"] : "") ?></td>
      <td style=border:solid>&nbsp;</td>
      <td style=border:solid><?php echo (($row_mi["sur_risk_point"] > 0) ? $row_mi["sur_risk_point"] : "") ?></td>
      <td style=border:solid>&nbsp;</td>
        
      <td style=border:solid><?php echo (($row_mi["sur_risk_cctv"] > 0) ? $row_mi["sur_risk_cctv"] : "") ?></td>
      <td style=border:solid>&nbsp;</td>
      <td style=border:solid><?php echo (($row_mi["sur_mian_road_n"] > 0) ? $row_mi["sur_mian_road_n"] : "") ?></td>
      <td style=border:solid>&nbsp;</td>
      <td style=border:solid><?php echo (($row_mi["sur_mian_road_point"] > 0) ? $row_mi["sur_mian_road_point"] : "") ?></td>
      <td style=border:solid>&nbsp;</td>
      <td style=border:solid><?php echo (($row_mi["sur_mian_road_cctv"] > 0) ? $row_mi["sur_mian_road_cctv"] : "") ?></td>
      <td style=border:solid>&nbsp;</td>  
      <td style=border:solid>&nbsp;<?= $links ?>&nbsp;</td>
	  
	  <!--<td><a href="Edit_mission.php?id=<?= $row_mi["mi_cl_aid"] ?>" class="btn btn-warning mb-4" >แก้ไข</a> </td>
	  <td><a href="Del_mission.php?id=<?= $row_mi["mi_cl_aid"] ?>" class="btn btn-danger mb-4" onClick="Del('Del_mission.php?id=<?= $row_mi["mi_cl_aid"]?>')">ลบ</a>-->
    </tr>
  </tbody>
<?php
	$no2++;
}//while
?>
</table>
</div>

<script>
function Del(url)
{
	if(confirm("คุณจะลบข้อมูล หรือ ไม่ ?"))
	{
		window.location = url;		
	}
}
function dates_change()  // คำสั่งเปลี่ยนเดือน ปี
{
    var ms = $("#mi_cl_month option:selected").val();
    var ys = $("#mi_cl_year option:selected").val();
    window.location = "/Activity/Show_Activity2.php?&month=" + ms + "&year=" + ys + "&rnd=" + Math.random();
}
</script>

<!------------------------------------------------------->
<!------------------------------------------------------->
<!------------------------------------------------------->

<br>
<hr>
<br>
<h3 align="center">ข้อมูลการสำรวจกล้องวงจรปิด ทั้งหมด</h3>
<table border=".5" >
  <tbody>
    <tr align="center" class="bg2">
      <td rowspan="2" align="center" style="color: black; border: solid black; text-align: center" bgcolor="#0706F0">หน่วยงาน</td>
      <td colspan="7" style="color: black; border: solid black; text-align: center" bgcolor="coral" height="40px">จุดที่เกิดอาชญากรรมบ่อย</td>
      <td colspan="7" style="color: black; border: solid black; text-align: center" bgcolor="#E8D183">จุดเสี่ยงจุดล่อแหลม</td>
      <td colspan="7" style="color: black; border: solid black; text-align: center" bgcolor="#92E8B9">เส้นทางหลัก</td>
      <td colspan="6" style="color: aliceblue; border: solid black; text-align: center" bgcolor="#0706F0">รวม</td>
      <td rowspan="2" style="color: aliceblue; border: solid black; text-align: center" bgcolor="#0706F0">&nbsp;ความสำเร็จ (%)&nbsp;</td>&nbsp;&nbsp;
      <!--<td rowspan="2" align="center" style="color: aliceblue" bgcolor="#0706F0">แผนที่</td>-->
    </tr>
    <tr align="center">
      <td style="color: black; border: solid black; text-align: center" bgcolor="coral">ชื่อสถานที่</td>
      <td style="color: black; border: solid black; text-align: center" bgcolor="coral" height="40px">จำนวน</td>
      <td style="color: black; border: solid black; text-align: center" bgcolor="coral">ผลดำเนินการ</td>
      <td style="color: black; border: solid black; text-align: center" bgcolor="coral">จำนวนจุด</td>
      <td style="color: black; border: solid black; text-align: center" bgcolor="coral">ผลดำเนินการ</td>
      <td style="color: black; border: solid black; text-align: center" bgcolor="coral">จำนวนกล้อง</td>
      <td style="color: black; border: solid black; text-align: center" bgcolor="coral">ผลดำเนินการ</td>
      <td style="color: black; border: solid black; text-align: center" bgcolor="#E8D183">ชื่อสถานที่</td>
      <td style="color: black; border: solid black; text-align: center" bgcolor="#E8D183">จำนวน</td>
      <td style="color: black; border: solid black; text-align: center" bgcolor="#E8D183">ผลดำเนินการ</td>
      <td style="color: black; border: solid black; text-align: center" bgcolor="#E8D183">จำนวนจุด</td>
      <td style="color: black; border: solid black; text-align: center" bgcolor="#E8D183">ผลดำเนินการ</td>
      <td style="color: black; border: solid black; text-align: center" bgcolor="#E8D183">จำนวนกล้อง</td>
      <td style="color: black; border: solid black; text-align: center" bgcolor="#E8D183">ผลดำเนินการ</td>
      <td style="color: black; border: solid black; text-align: center" bgcolor="#92E8B9">ชื่อสถานที่</td>
      <td style="color: black; border: solid black; text-align: center" bgcolor="#92E8B9">จำนวน</td>
      <td style="color: black; border: solid black; text-align: center" bgcolor="#92E8B9">ผลดำเนินการ</td>
      <td style="color: black; border: solid black; text-align: center" bgcolor="#92E8B9"    >จำนวนจุด</td>
      <td style="color: black; border: solid black; text-align: center" bgcolor="#92E8B9">ผลดำเนินการ</td>
      <td style="color: black; border: solid black; text-align: center" bgcolor="#92E8B9">จำนวนกล้อง</td>
      <td style="color: black; border: solid black; text-align: center" bgcolor="#92E8B9">ผลดำเนินการ</td>
      <td style="color: aliceblue; border: solid black; text-align: center" bgcolor="#0706F0">สถานที่</td>
      <td style="color: aliceblue; border: solid black; text-align: center" bgcolor="#0706F0">ผลดำเนินการ</td>
      <td style="color: aliceblue; border: solid black; text-align: center" bgcolor="#0706F0">จำนวนจุด</td>
      <td style="color: aliceblue; border: solid black; text-align: center" bgcolor="#0706F0">ผลดำเนินการ</td>
      <td style="color: aliceblue; border: solid black; text-align: center" bgcolor="#0706F0">จำนวนกล้อง</td>
      <td style="color: aliceblue; border: solid black; text-align: center" bgcolor="#0706F0">ผลดำเนินการ</td>
    </tr>
      
      
<?php
$sql= "SELECT T1.*, T2.station_name AS Station FROM wm_tb_CCTV_survey AS T1 " .
      "LEFT JOIN wm_tb_police_station2 AS T2 ON T1.station = T2.station_code " .
      "";

    $stmt = $pdo->prepare($sql);
try{
    $stmt->execute();
}catch (PDOException $e) {
    echo '$sql Failed: '. $e->getMessage();
}

$no = 1;
while($row = $stmt->fetch(PDO::FETCH_ASSOC))
{
    
    $AID = $row['sur_aid'];
		$lat = 0.0 + $row['sur_lat'];
		$lon = 0.0 + $row['sur_lon'];
		if($lat && $lon) {
			$map = "<a href='javascript:goMap({$row['sur_lat']},{$row['sur_lon']})'> Go </a>";
		}
		else {
			$map = "<em><small>ไม่ระบุ </small></em>";
		}
    
    $sum1 = 0;
    $sum2 = 0;
    $sum3 = 0;
    $sum4 = 0;
    $sum5 = 0;
    $sum6 = 0;
    $sum1 += $row['sur_crimes_n'] + $row['sur_risk_n'] + $row["sur_mian_road_n"];
    $sum2 += $row['sur_crimes_ac_n'] + $row['sur_risk_ac_n'] + $row["sur_mian_road_ac_n"];
    $sum3 += $row['sur_crimes_point'] + $row['sur_risk_point'] + $row["sur_mian_road_point"];
    $sum4 += $row['sur_crimes_point_n'] + $row['sur_risk_point_n'] + $row["sur_mian_road_point_n"];
    $sum5 += $row['sur_crimes_cctv'] + $row['sur_risk_cctv'] + $row["sur_mian_road_cctv"];
    $sum6 += $row['sur_crimes_cctv_n'] + $row['sur_risk_cctv_n'] + $row["sur_mian_road_cctv_n"];
    $sum7 = ($sum6 * 100) / $sum5; // สูตรคำนวณ หา%
    $result7 = round($sum7,2);  // จุดทศนิยม 2 ตำแหน่ง
    
?>
      
    <tr align="center">
      <td nowrap  height="40px" style="border-left: solid black">&nbsp;<?=$row["Station"]?>&nbsp;</td>
      <td nowrap  style="border-left: solid black"><?=$row["sur_crimes"]?></td>
      <td  style="border-left: solid black">&nbsp;<?php echo (($row["sur_crimes_n"] > 0) ? $row["sur_crimes_n"]  : "") ?>&nbsp;</td>
      <td style="border-left: solid black">&nbsp;<?php echo (($row["sur_crimes_ac_n"] > 0) ? $row["sur_crimes_ac_n"]  : "") ?>&nbsp;</td>
      <td style="border-left: solid black">&nbsp;<?php echo (($row["sur_crimes_point"] > 0) ? $row["sur_crimes_point"]  : "") ?>&nbsp;</td>
      <td style="border-left: solid black">&nbsp;<?php echo (($row["sur_crimes_point_n"] > 0) ? $row["sur_crimes_point_n"]  : "") ?>&nbsp;</td>
      <td style="border-left: solid black">&nbsp;<?php echo (($row["sur_crimes_cctv"] > 0) ? $row["sur_crimes_cctv"]  : "") ?>&nbsp;</td>
      <td style="border-left: solid black">&nbsp;<?php echo (($row["sur_crimes_cctv_n"] > 0) ? $row["sur_crimes_cctv_n"]  : "") ?>&nbsp;</td>
        
      <td nowrap style="border-left: solid black">&nbsp;<?=$row["sur_risk"]?></td>
      <td style="border-left: solid black">&nbsp;<?php echo (($row["sur_risk_n"] > 0) ? $row["sur_risk_n"]  : "") ?>&nbsp;</td>
      <td height="40px" style="border-left: solid black">&nbsp;<?php echo (($row["sur_risk_ac_n"] > 0) ? $row["sur_risk_ac_n"]  : "") ?>&nbsp;</td>
      <td style="border-left: solid black">&nbsp;<?php echo (($row["sur_risk_point"] > 0) ? $row["sur_risk_point"]  : "") ?>&nbsp;</td>
      <td style="border-left: solid black">&nbsp;<?php echo (($row["sur_risk_point_n"] > 0) ? $row["sur_risk_point_n"]  : "") ?>&nbsp;</td>
      <td style="border-left: solid black">&nbsp;<?php echo (($row["sur_risk_cctv"] > 0) ? $row["sur_risk_cctv"]  : "") ?>&nbsp;</td>
      <td style="border-left: solid black">&nbsp;<?php echo (($row["sur_risk_cctv_n"] > 0) ? $row["sur_risk_cctv_n"]  : "") ?>&nbsp;</td>
        
      <td nowrap  height="40px" style="border-left: solid black">&nbsp;<?=$row["sur_mian_road"]?>&nbsp;</td>      
      <td style="border-left: solid black">&nbsp;<?php echo (($row["sur_mian_road_n"] > 0) ? $row["sur_mian_road_n"]  : "") ?>&nbsp;</td>
      <td style="border-left: solid black">&nbsp;<?php echo (($row["sur_mian_road_ac_n"] > 0) ? $row["sur_mian_road_ac_n"]  : "") ?>&nbsp;</td>
      <td style="border-left: solid black">&nbsp;<?php echo (($row["sur_mian_road_point"] > 0) ? $row["sur_mian_road_point"]  : "") ?>&nbsp;</td>
      <td style="border-left: solid black">&nbsp;<?php echo (($row["sur_mian_road_point_n"] > 0) ? $row["sur_mian_road_point_n"]  : "") ?>&nbsp;</td>
      <td style="border-left: solid black">&nbsp;<?php echo (($row["sur_mian_road_cctv"] > 0) ? $row["sur_mian_road_cctv"]  : "") ?>&nbsp;</td>
      <td style="border-left: solid black">&nbsp;<?php echo (($row["sur_mian_road_cctv_n"] > 0) ? $row["sur_mian_road_cctv_n"]  : "") ?>&nbsp;</td>
      <td style="border-left: solid black">&nbsp;<?php echo (($sum1 > 0) ? $sum1  : "") ?>&nbsp;</td>
      <td style="border-left: solid black">&nbsp;<?php echo (($sum2 > 0) ? $sum2  : "") ?>&nbsp;</td>
      <td style="border-left: solid black">&nbsp;<?php echo (($sum3 > 0) ? $sum3  : "") ?>&nbsp;</td>
      <td style="border-left: solid black">&nbsp;<?php echo (($sum4 > 0) ? $sum4  : "") ?>&nbsp;</td>
      <td style="border-left: solid black">&nbsp;<?php echo (($sum5 > 0) ? $sum5  : "") ?>&nbsp;</td>
      <td style="border-left: solid black">&nbsp;<?php echo (($sum6 > 0) ? $sum6  : "") ?>&nbsp;</td>
      <td style="border-left: solid black"><?= $result7 ?> %</td>
      <!--<td nowrap>&nbsp;<a href="Show_CCTV_detail_survey.php?id=<?= $row["sur_aid"] ?>" class="btn btn-success mb-4" >&nbsp;แผนที่&nbsp;</a>&nbsp;</td>-->
    </tr>
<?php
	$no++;
}
?>
  </tbody>
</table>
              
<br>
<br>
<hr>