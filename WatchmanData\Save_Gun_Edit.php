<?php
include '../Condb.php';
include '../users.inc.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// เมื่อมีการใช้ ตัวแปร Session จะต้องเปิดก่อน
//session_start();  // เปิดตัวแปร session
/*if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
*/

$gu_cl_aid = $_POST['gu_cl_aid'];
$gu_cl_idcard = $_POST[ 'gu_cl_idcard' ];
$gu_cl_rank = $_POST[ 'gu_cl_rank' ];
$gu_cl_reg_no = $_POST[ 'gu_cl_reg_no' ];
$gu_cl_no = $_POST[ 'gu_cl_no' ];
$gu_cl_type = $_POST[ 'gu_cl_type' ];
$gu_cl_kind = $_POST[ 'gu_cl_kind' ];
$gu_cl_bullet_size = $_POST[ 'gu_cl_bullet_size' ];
$gu_cl_brand = $_POST[ 'gu_cl_brand' ];
$gu_cl_license_no = $_POST[ 'gu_cl_license_no' ];
$gu_cl_amphur = $_POST[ 'gu_cl_amphur' ];
$gu_cl_reg_date = $_POST[ 'gu_cl_reg_date' ];
$region = $_POST[ 'region' ];
$provincial = $_POST[ 'provincial' ];
$station = $_POST[ 'station' ];

$ps_cl_aid = $_POST['ps_cl_aid'];
$ps_cl_prefix = $_POST[ 'ps_cl_prefix' ];
$ps_cl_sex = $_POST[ 'ps_cl_sex' ];
$ps_cl_name = $_POST[ 'ps_cl_name' ];               // >> tb_personal
$ps_cl_surname = $_POST[ 'ps_cl_surname' ];         // >> tb_personal
$ps_cl_nickname = $_POST[ 'ps_cl_nickname' ];       // >> tb_personal
$ps_cl_birthday2 = $_POST[ 'ps_cl_birthday2' ];       // >> tb_personal
$ps_cl_father = $_POST[ 'ps_cl_father' ];
$ps_cl_father_pid = $_POST[ 'ps_cl_father_pid' ];
$ps_cl_mother = $_POST[ 'ps_cl_mother' ];
$ps_cl_mother_pid = $_POST[ 'ps_cl_mother_pid' ];
$ps_cl_marital_status = $_POST[ 'ps_cl_marital_status' ];
$region = $_POST[ 'region' ];
$provincial = $_POST[ 'provincial' ];
$station = $_POST[ 'station' ];

//$ps_cl_birthday2 = thai_date_2_eng($ps_cl_birthday);

// Remove non-numeric characters
$gu_cl_idcard = preg_replace('/[^0-9]/', '', $gu_cl_idcard);

// save Image ใหม่แทน
$file1 = $_FILES[ 'ps_cl_image' ][ 'tmp_name' ];
if ( is_uploaded_file( $file1 ) ) {
    //ลบรูปเก่าในเซิร์ฟเวอร์ก่อน
    $sql = "SELECT * FROM wm_tb_place_data WHERE gc_cl_aid='gc_cl_aid' ";
    $res = mysqli_query($conn,$sql);
    if($row = mysqli_fetch_array($res))
    {
        if(($row['ps_cl_image'] != '') && file_exists($row['ps_cl_image'])) {
            unlink( $row['ps_cl_image'] );
        }    
    }
    //เปลี่ยนชื่อไฟล์ใหม่
    $ps_cl_image = "/policeinnopolis/uploaded/Image1/" . basename( $_FILES[ 'ps_cl_image' ][ 'name' ] );	    
    // file + ext    
    $ext = strrchr($ps_cl_image, ".");    
    $ps_cl_image = "../policeinnopolis/uploaded/Image1/File_" . time() . $ext; // file+time+ext        
    move_uploaded_file( $file1, $ps_cl_image );  
} 
else {
  $ps_cl_image = '';
}

// 1. ข้อมูลบุคคล
// check exists data in personal เช็คว่ามีข้อมูลในตาราง Personal หรือยัง ถ้ามีแล้ว ไม่ต้องทำอะไร
$sql_ps = "SELECT * FROM wm_tb_personal WHERE ps_cl_idcard='$gu_cl_aid' ";
//echo $sql3;
$res_ps = mysqli_query($conn, $sql_ps);
// มีข้อมูลแล้ว
if($row_ps = mysqli_fetch_array($res_ps)) {
   
}
// เปนข้อมูลใหม่ >> 
else {
    if($ps_cl_birthday2 == "") 
    {
        $sql_ps = "UPDATE wm_tb_personal SET ps_cl_idcard='$gu_cl_idcard', ps_cl_prefix='$ps_cl_prefix', ps_cl_sex='$ps_cl_sex', ps_cl_name='$ps_cl_name', ps_cl_surname='$ps_cl_surname', ps_cl_nickname='$ps_cl_nickname', ps_cl_father='$ps_cl_father', ps_cl_father_pid='$ps_cl_father_pid', ps_cl_mother='$ps_cl_mother', ps_cl_mother_pid='$ps_cl_mother_pid', ps_cl_marital_status='$ps_cl_marital_status', region='$region', provincial='$provincial', station='$station', ps_cl_image='$ps_cl_image' WHERE ps_cl_aid='$ps_cl_aid' ";
    }
    else {
        $sql_ps = "UPDATE wm_tb_personal SET ps_cl_idcard='$gu_cl_idcard', ps_cl_prefix='$ps_cl_prefix', ps_cl_sex='$ps_cl_sex', ps_cl_name='$ps_cl_name', ps_cl_surname='$ps_cl_surname', ps_cl_nickname='$ps_cl_nickname', ps_cl_birthday2='$ps_cl_birthday2', ps_cl_father='$ps_cl_father', ps_cl_father_pid='$ps_cl_father_pid', ps_cl_mother='$ps_cl_mother', ps_cl_mother_pid='$ps_cl_mother_pid', ps_cl_marital_status='$ps_cl_marital_status', region='$region', provincial='$provincial', station='$station', ps_cl_image='$ps_cl_image' WHERE ps_cl_aid='$ps_cl_aid' ";
    }
    mysqli_query($conn, $sql_ps);
    
    //echo( $sql1 );
    echo mysqli_error($conn);
}
//
// 1. ข้อมูลปืน
// check exists data in personal เช็คว่ามีข้อมูลในตาราง หรือยัง ถ้ามีแล้ว ไม่ต้องทำอะไร
$sql_gu = "SELECT * FROM wm_tb_gun WHERE gu_cl_aid='$gu_cl_aid' ";
//echo $sql3;
$res_gu = mysqli_query($conn, $sql_gu);
// มีข้อมูลแล้ว
if($row_gu = mysqli_fetch_array($res_gu)) 

// $sql2 บันทึกตารางปืน
$sql_gu = "UPDATE wm_tb_gun SET gu_cl_idcard='$gu_cl_idcard', gu_cl_rank='$gu_cl_rank', gu_cl_reg_no='$gu_cl_reg_no', gu_cl_no='$gu_cl_no', gu_cl_type='$gu_cl_type', gu_cl_kind='$gu_cl_kind', gu_cl_bullet_size='$gu_cl_bullet_size', gu_cl_brand='$gu_cl_brand', gu_cl_license_no='$gu_cl_license_no', gu_cl_amphur='$gu_cl_amphur', gu_cl_reg_date='$gu_cl_reg_date', region='$region', provincial='$provincial', station='$station' WHERE gu_cl_aid='$gu_cl_aid' ";

$result_gu=mysqli_query($conn, $sql_gu);


if($result_gu){
	echo "<script>alert('บันทึกข้อมูลเรียบร้อย');</script>";	
}else{
	echo "<script>alert(\"บันทึกข้อมูลไม่สำเร็จ\\n" . print_r(mysqli_error($conn), true) . "\");</script>";
}




// เก็บข้อมูล Action
$inputs = str_replace("'", "", compact_array($_POST));
$prev = "";//str_replace("'", "", implode(',', $row_0));

$acc = $user['account'];
$sql3 = "INSERT wm_tb_user_action (at_cl_user,at_cl_date,at_cl_action,at_cl_input,at_cl_prev) VALUES('$acc',CURRENT_TIMESTAMP,'Add new Gun {$gu_cl_idcard}', '$inputs', '{$prev}')";
mysqli_query($conn, $sql3);
//

mysqli_close($conn);

echo "<script>window.location='Show_Gun.php?pcid=" . $gu_cl_idcard . " ';</script>";

?>

