<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';
include '../Head_image.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

//get month from select or current month
$month = isset($_GET['month']) ? $_GET['month'] : date("m");
$year = isset($_GET['year']) ? $_GET['year'] : date("Y");
//$station = isset($_GET['station']) ? $_GET['station'] : 6807;
//$station = 6807;   // สภ.พิชัย

//--------------------
// last modify 2023-01-20
$limite_date = strtotime("$year-09-30"); // วันสุดท้ายของช่วง เป็น 30 ก.ย.ของปีนั้น ๆ
$current_date = time(); // now()   กำหนดวันปัจจุบัน
$check_date = strtotime("$year-10-01");   // ตัวแปร $check_date เป็นวันตั้งแต่ 1 ต.ค.ของปีนั้น ๆ เป็นต้นไป
$select_date = strtotime("$year-$month-01"); // เป็นตัวแปร สำหรับเลือก ของเดือนนั้น ๆ

//ถ้าเวลายังไม่สิ้นสุด 30 กย. ของปี
if($current_date < $check_date)   // ถ้าวันปัจจุบัน มีค่าน้อยกว่า วันที่ 1 ต.ค.
{
    $limite_date = strtotime(($year-1) . "-09-30");  // ตัวแปร $limite_date จะมีค่าเท่ากับ 30 ก.ย.ของปี ที่ลบค่าไป 1
    $check_date = strtotime(($year-1) . "-10-01");   // ตัวแปร $check_date จะมีค่าเท่ากับ 1 ต.ค. ของปี ที่ลบค่าไป 1
}

$m1 = '';
$m2 = '';
$m3 = '';
$m4 = '';
$m5 = '';
$m6 = '';
$m7 = '';
$m8 = '';
$m9 = '';
$m10 = '';
$m11 = '';
$m12 = '';
switch($month)
{
    case "01": $m1 = "selected"; break;
    case "02": $m2 = "selected"; break;
    case "03": $m3 = "selected"; break;
    case "04": $m4 = "selected"; break;
    case "05": $m5 = "selected"; break;
    case "06": $m6 = "selected"; break;
    case "07": $m7 = "selected"; break;
    case "08": $m8 = "selected"; break;
    case "09": $m9 = "selected"; break;
    case "10": $m10 = "selected"; break;
    case "11": $m11 = "selected"; break;
    case "12": $m12 = "selected"; break;
}
$current_month = $global_thai_month[0+$month];
$y2 = $year+543;

// แปลงเวลา เป็นปีไทย
function date_2_Thai( $date )
{
    global $global_thaimonth;
    $strYear = date("Y", $date) + 543;
    $strMonth = date("n",$date) - 1;
    $strDay = date("j", $date);
    $strMonthThai = $global_thaimonth[$strMonth];
    return "$strDay $strMonthThai $strYear";
}

//สร้างฟังก์ชั่น ย้อนหลัง arrest_summary_count มาใช้งาน เป็นยอดรวมการจับกุม ย้อนหลัง 1 เดือน
// dateM - datetime var
function arrest_summary_count($date)
{
    global $pdo, $station;

    $m1 = date("Y-m-1", $date);
    $m2 = date("Y-m-t", $date);

    $sql_s = "SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial` = :provincial AND `wr_cl_status` IN (2,4,5,6)";

    $stmt = $pdo->prepare($sql_s);
    $stmt->bindParam(':m1', $m1);
    $stmt->bindParam(':m2', $m2);
    $stmt->bindParam(':provincial', $provincial);
    $stmt->execute();

    $row_s = $stmt->fetch(PDO::FETCH_NUM);
    return $row_s[0];
}

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<base target="_top">
<title>รายงานหมายจับ ภ.จว.อุตรดิตถ์</title>
<link rel="stylesheet" href="/bootstrap/css/bootstrap-grid.css">
<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<style type="text/css">
body{
    font-size:12px; 
}
.textAlignVer{
    display:block;
    filter: flipv fliph;
    -webkit-transform: rotate(-90deg); 
    -moz-transform: rotate(-90deg); 
    transform: rotate(-90deg); 
    position:relative;
    width:20px;
    white-space:nowrap;
    font-size:16px;
    margin-bottom:10px;
}
.box !im{
    border: black;
    border-top-color: black !important;
    border-bottom-color: black !important;
    border-left-color: black !important;
    border-right-color: black !important;

}
</style>
    
</head>

<body>
    
<div class="container-fluid" align="left">
<div class=" h3 text-center  alert alert-success mb-4 mt-4 " role="alert" >กิจกรรม 7 หมายจับค้างเก่า <?= $name_provincial ?></div>
<div>
    <table width="80%" border="0" cellspacing="1" cellpadding="1">
      <tbody>
        <tr>
          <td width="21%">&nbsp;<a href="/WM/Show_UD_report.php?rnd=<?= rand(); ?>" class="btn btn-warning btn-lg mb-4" >ย้อนกลับ</a><!--&nbsp;&nbsp;<a href="Add_mission.php" class="btn btn-success btn-lg mb-4" >เพิ่มข้อมูล</a>--></td>
          <td width="17%">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<b style="font-size: 25px">เลือกช่วงข้อมูล</b></td>    
          <td width="10%"><label>เลือกปี </label>
            <select id="mi_cl_year" name="mi_cl_year" class="form-select col-8 d-lg-inline" style="background-color: khaki; font-size: 16px" onChange="dates_change()">
              <option value="">เลือก</option>
              <?php
                $cur_y = date("Y") + 2;
                for($y=2020; $y<$cur_y; $y++) {
                    $sel = ($y == $year) ? "selected" : '';
                    echo "<option value='$y' $sel> ". ($y + 543) ." </option>";
                }
            ?>
          </select></td>
          <td width="3%"></td>
          <td width="16%"><label>เดือน</label>
            <select id="mi_cl_month" name="mi_cl_month" class="form-select col-3 d-lg-inline" style="background-color: khaki; font-size: 16px" onChange="dates_change()">
              <option value="">เลือก</option>
              <option <?= $m1 ?> value="01">มกราคม</option>
              <option <?= $m2 ?> value="02">กุมภาพันธ์</option>
              <option <?= $m3 ?> value="03">มีนาคม</option>
              <option <?= $m4 ?> value="04">เมษายน</option>
              <option <?= $m5 ?> value="05">พฤษภาคม</option>
              <option <?= $m6 ?> value="06">มิถุนายน</option>
              <option <?= $m7 ?> value="07">กรกฎาคม</option>
              <option <?= $m8 ?> value="08">สิงหาคม</option>
              <option <?= $m9 ?> value="09">กันยายน</option>
              <option <?= $m10 ?> value="10">ตุลาคม</option>
              <option <?= $m11 ?> value="11">พฤศจิกายน</option>
              <option <?= $m12 ?> value="12">ธันวาคม</option>
          </select></td>
          <td width="29%">&nbsp;</td>
          <td width="4%">&nbsp;</td>
        </tr>
      </tbody>
    </table><br>
</div>
<h3 align="center">ข้อมูลหมายจับค้างเก่า  <?= $name_provincial ?> </h3>
<h3 align="center">ประจำเดือน&nbsp; <?= $current_month ?> &nbsp;<?= $y2 ?> &nbsp;</h3><br>
<table width="90%" border="1" cellspacing="1" cellpadding="1" class="table table-bordered">
  <tbody>
         <tr  height=28 style='height:21.0pt'>
           <td rowspan="3" bordercolor="#0C0B0B"  style='border:solid #000000;
          height:21.0pt; background-color:aqua '>&nbsp;หน่วย</td>
           <td height="28" colspan="4" bordercolor="#000000"  style='border:solid #000000; background-color:#DDDDDD '>ยอดหมายจับ (ยกมา)</td>
           <td height=28 colspan=12 bordercolor="#0C0B0B"  style='border:solid #000000;
          height:21.0pt; background-color:aqua '><span style="height:21.0pt; background-color:aqua ">หมายจับทั่วไปของ  <?= $name_provincial ?>  ตั้งแต่ 11 ต.ค.45 - <?= date_2_Thai($limite_date) ?></span></td>
          <td colspan=14  style='border:solid #000000; background-color:#ADFDB6'>หมายจับทั่วไปของ
           <?= $name_provincial ?>  ตั้งแต่ <?= date_2_Thai($check_date) ?>  - ปัจจุบัน</td>
          <td colspan=3  style='border:solid #000000; background-color: yellow; text-align: center'>รวม (ในเดือนนั้น)</td>
         </tr>
         <tr  height=28 style='height:21.0pt'>
           <td rowspan=2  bordercolor="#000000"  style='border:solid #000000; background-color:#DDDDDD '><p>ยอดหมายจับ (01)</p></td>
           <td rowspan=2  bordercolor="#000000"  style='border:solid #000000; background-color:#DDDDDD '><p>มีคุณภาพ  (02)</p></td>
           <td rowspan=2  bordercolor="#000000"  style='border:solid #000000; background-color:#DDDDDD '>อายัด (03)</td>
          <td rowspan=2  bordercolor="#000000"  style='border:solid #000000; background-color:#51ADFC '><p>ไม่มีคุณภาพ (04)</p></td>
          <td rowspan=2  width=104 style='border:solid #000000; width:78pt; background-color:aqua ' bordercolor="#0C0B0B">หมายคงเหลือทั้งหมด (1)</td>
          <td colspan="2" align=left  style='border:solid #000000; background-color:aqua '>คุณภาพของหมาย</td>
          <td colspan=6  style='border:solid #000000; background-color:#FA757F '><p>การจำหน่ายหมายจับ
            (เพิ่มเติม)
             (<?= $current_month ?>&nbsp;<?= $y2 ?>)</td>
          <td colspan=2  style='border:solid #000000;
          border-left:none; background-color:aqua '>จำนวนหมายคงเหลือ</td>
          <td rowspan=2  width=93 style='border:solid #000000;
          border-top:none;width:70pt; background-color:aqua '>ร้อยละผลการจับกุมจากหมายจับทั้งหมด (12)</td>
          <td rowspan=2  width=87 style='border:solid #000000;
          border-top:none;width:65pt; background-color:#ADFDB6'>หมายจับคงเหลือทั้งหมดจากการประชุมที่ผ่านมา (13)</td>
          <td rowspan=2  width=81 style='border:solid #000000;
          border-top:none;width:61pt; background-color:#ADFDB6'>จำนวนหมายจับใหม่ (เพิ่มเติม) (14)</td>
          <td rowspan=2  width=65 style='border:solid #000000;
          border-top:none;width:49pt; background-color:#ADFDB6'>รวมหมายจับทั้งหมด (15)</td>
          <td colspan="2" align=left class=xl961464 style='border:solid #000000; background-color:#ADFDB6'>คุณภาพของหมาย</td>
          <td colspan=6  style='border:solid #000000; background-color:#FA757F'>การจำหน่ายหมายจับ
            (เพิ่มเติม)<br>
            (<?= $current_month ?>&nbsp;<?= $y2 ?>)</td>
          <td colspan=2  style='border:solid #000000;
          border-left:none; background-color:#ADFDB6'>จำนวนหมายคงเหลือ</td>
          <td rowspan=2  width=93 style='border:solid #000000;
          border-top:none;width:70pt; background-color:#ADFDB6'>ร้อยละผลการจับกุมจากหมายจับทั้งหมด (26)</td>
          <td rowspan=2  width=70 style='border:solid #000000;width:53pt; background-color: #51ADFC'>รวมหมายจับทั้งหมด
          1 ต.ค.45 - ปัจจุบัน (27)</td>
          <td rowspan=2  width=70 style='border:solid #000000;width:53pt; background-color: #FA757F'>รวมจำหน่ายหมายทั้งหมด (28)</td>
          <td rowspan=2  width=70 style='border:solid #000000;width:53pt; background-color: yellow'>ร้อยละผลการจับกุมจากหมายจับทั้งหมด</td>
         </tr>
         <tr  height=133>
          <td style='border:solid #000000; background-color:#7FFA75 ' align="center" valign="bottom"><span class="textAlignVer">มีคุณภาพ (2)</td>
             <td  style='border:solid #000000; background-color:aqua ' align="center" valign="bottom"><span class="textAlignVer">ไม่มีคุณภาพ (3)</td>
          <td  style='border:solid #000000; background-color:aqua '>จับกุม (4)</td>
          <td  style='border:solid #000000; background-color:aqua '>ขาดอายุความ (5)</td>
          <td  style='border:solid #000000; background-color:aqua '>ตาย (6)</td>
          <td  style='border:solid #000000; background-color:aqua '>อายัด (7)</td>
          <td  style='border:solid #000000; background-color:aqua '>อื่น ๆ (8)</td>
          <td  style='border:solid #000000; background-color:#FA757F '>รวม (9)</td>
          <td  style='border:solid #000000; background-color:#51ADFC ' align="center" valign="bottom"><span class="textAlignVer">มีคุณภาพ (10)</td>
          <td  style='border:solid #000000; background-color:aqua ' align="center" valign="bottom"><span class="textAlignVer">ไม่มีคุณภาพ (11)</td>
          <td  style='border:solid #000000; background-color:#7FFA75' align="center" valign="bottom"><span class="textAlignVer">มีคุณภาพ (16)</td>
          <td  style='border:solid #000000; background-color:#ADFDB6' align="center" valign="bottom"><span class="textAlignVer">ไม่มีคุณภาพ (17)</td>
          <td  style='border:solid #000000; background-color:#ADFDB6'>จับกุม (18)</td>
          <td  style='border:solid #000000; background-color:#ADFDB6'>ขาดอายุความ (19)</td>
          <td  style='border:solid #000000; background-color:#ADFDB6'>ตาย (20)</td>
          <td  style='border:solid #000000; background-color:#ADFDB6'>อายัด (21)</td>
          <td  style='border:solid #000000; background-color:#ADFDB6'>อื่น ๆ (22)</td>
          <td  style='border:solid #000000; background-color:#FA757F'>รวม (23)</td>
          <td  style='border:solid #000000; background-color:#51ADFC' align="center" valign="bottom"><span class="textAlignVer">มีคุณภาพ (24)</td>
          <td  style='border:solid #000000; background-color:#ADFDB6' align="center" valign="bottom"><span class="textAlignVer">ไม่มีคุณภาพ (25)</td>
         </tr>
<?php                                    
    
//$date1 = date("Y", $limite_date) . "-$month-01";        //"$year-$month-01";
$day1 = date("t", $limite_date);
$date2 = "$year-$month-$day1"; // <<<
                      
// ยอดหมายจับเก่า ที่ออกระหว่าง ปี 45 - 30 ก.ย.2565 ทั้งหมด
$date_prev = date("Y-m-d", $limite_date);
$date_next = date("Y-m-01", $check_date); // ตั้งแต่ 1 ตค เปนต้นไป
    $m1 = date("Y-m-01", $select_date);       // m1 = เดือนปัจจุบันที่เลือก
    $m2 = date("Y-m-t", $select_date);        // << end date of current month ได้ค่าวันสุดท้ายของเดือนปัจจุบันที่เลือกมา
                      
//ฟังก์ชั่น เลือกเดือน ย้อนหลัง 1 เดือน ได้ตัวแปร $sum2 ไปใช้งาน
$sum = arrest_summary_count( $select_date );
$prev_y = $year;
$prev_m = $month - 1;
if($prev_m < 0) {
    $prev_m = 12;
    $prev_y--;
}
$mp = strtotime("$prev_y-$prev_m-01");
$sum2 = arrest_summary_count( $mp );  // ยอดย้อนหลังไป 1 เดือน
     
					  
$sql = "SELECT " .
	//////// ยอดรวม ของ ภ.จว.อุตรดิตถ์  ///////////
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `wr_cl_status` IN (1,3)) AS All_01, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
    ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `wr_cl_status` IN (1,3)) AS Old_All_1, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
    ///// ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7 , " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`=6 ) AS Old_other_8, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`IN(2,4,5,6)) AS S_9, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     /////  หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `provincial`=:provincial AND `wr_cl_status` IN (1,3)) AS New_BF_13 , ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND (`wr_cl_status` IN (1,3,2))) AS New_M_14 , ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `provincial`=:provincial AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16 , ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `provincial`=:provincial AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17 , ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18 , ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`=4) AS New_Exp_19 , ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`=5) AS New_Dead_20 , " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`=3) AS New_Hold_21 , " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`=6) AS New_other_22 , " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`IN(2,4,5,6)) AS S_23, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
	
	////////// สภ.ด่านแม่คำมัน ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6801 AND `wr_cl_status` IN (1,3)) AS All_01_dan, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6801 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_dan, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6801 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_dan, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6801 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_dan, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6801 AND `wr_cl_status` IN (1,3)) AS Old_All_1_dan, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6801 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_dan, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6801 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_dan, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     ///// ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6801 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_dan, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6801 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_dan, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6801 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_dan, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6801 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_dan, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6801 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_dan, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6801 AND `wr_cl_status`=6 ) AS Old_other_8_dan, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6801 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_dan, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6801 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_dan, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     /////  หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6801 AND `wr_cl_status` IN (1,3)) AS New_BF_13_dan, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6801 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_dan, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6801 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_dan, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6801 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_dan, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6801 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_dan, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6801 AND `wr_cl_status`=4) AS New_Exp_19_dan, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6801 AND `wr_cl_status`=5) AS New_Dead_20_dan, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6801 AND `wr_cl_status`=3) AS New_Hold_21_dan, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6801 AND `wr_cl_status`=6) AS New_other_22_dan, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6801 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_dan, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6801 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_dan, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
	
	////////// สภ.ตรอน ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6802 AND `wr_cl_status` IN (1,3)) AS All_01_tro, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6802 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_tro, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6802 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_tro, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6802 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_tro, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6802 AND `wr_cl_status` IN (1,3)) AS Old_All_1_tro, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6802 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_tro, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6802 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_tro, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6802 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_tro, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6802 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_tro, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6802 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_tro, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6802 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_tro, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6802 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_tro, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6802 AND `wr_cl_status`=6 ) AS Old_other_8_tro, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6802 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_tro, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6802 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_tro, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     /////  หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6802 AND `wr_cl_status` IN (1,3)) AS New_BF_13_tro, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6802 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_tro, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6802 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_tro, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6802 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_tro, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6802 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_tro, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6802 AND `wr_cl_status`=4) AS New_Exp_19_tro, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6802 AND `wr_cl_status`=5) AS New_Dead_20_tro, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6802 AND `wr_cl_status`=3) AS New_Hold_21_tro, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6802 AND `wr_cl_status`=6) AS New_other_22_tro, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6802 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_tro, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6802 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_tro, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
	
	////////// สภ.ทองแสนขัน ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6803 AND `wr_cl_status` IN (1,3)) AS All_01_tho, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6803 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_tho, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6803 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_tho, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6803 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_tho, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6803 AND `wr_cl_status` IN (1,3)) AS Old_All_1_tho, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6803 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_tho, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6803 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_tho, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6803 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_tho, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6803 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_tho, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6803 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_tho, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6803 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_tho, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6803 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_tho, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6803 AND `wr_cl_status`=6 ) AS Old_other_8_tho, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6803 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_tho, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6803 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_tho, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
    //////หมายจับ ใหม่  //////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6803 AND `wr_cl_status` IN (1,3)) AS New_BF_13_tho, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6803 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_tho, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6803 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_tho, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6803 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_tho, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
    ////// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก  //////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6803 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_tho, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6803 AND `wr_cl_status`=4) AS New_Exp_19_tho, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6803 AND `wr_cl_status`=5) AS New_Dead_20_tho, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6803 AND `wr_cl_status`=3) AS New_Hold_21_tho, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6803 AND `wr_cl_status`=6) AS New_other_22_tho, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6803 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_tho, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6803 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_tho, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  6803
	
	////////// สภ.ท่าปลา ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6804 AND `wr_cl_status` IN (1,3)) AS All_01_thp, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6804 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_thp, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6804 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_thp, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6804 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_thp, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6804 AND `wr_cl_status` IN (1,3)) AS Old_All_1_thp, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6804 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_thp, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6804 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_thp, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6804 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_thp, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6804 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_thp, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6804 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_thp, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6804 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_thp, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6804 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_thp, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6804 AND `wr_cl_status`=6 ) AS Old_other_8_thp, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6804 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_thp, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6804 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_thp, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     /////  หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6804 AND `wr_cl_status` IN (1,3)) AS New_BF_13_thp, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6804 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_thp, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6804 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_thp, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6804 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_thp, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6804 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_thp, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6804 AND `wr_cl_status`=4) AS New_Exp_19_thp, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6804 AND `wr_cl_status`=5) AS New_Dead_20_thp, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6804 AND `wr_cl_status`=3) AS New_Hold_21_thp, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6804 AND `wr_cl_status`=6) AS New_other_22_thp, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6804 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_thp, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6804 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_thp, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  6804 
	
	////////// สภ.น้ำปาด ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6805 AND `wr_cl_status` IN (1,3)) AS All_01_npa, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6805 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_npa, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6805 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_npa, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6805 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_npa, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
    /////////////////////////////// หมายจับเก่า  ///////////////////////////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6805 AND `wr_cl_status` IN (1,3)) AS Old_All_1_npa, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6805 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_npa, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6805 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_npa, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6805 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_npa, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6805 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_npa, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6805 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_npa, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6805 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_npa, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6805 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_npa, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6805 AND `wr_cl_status`=6 ) AS Old_other_8_npa, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6805 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_npa, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6805 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_npa, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     /////  หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6805 AND `wr_cl_status` IN (1,3)) AS New_BF_13_npa, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6805 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_npa, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6805 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_npa, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6805 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_npa, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6805 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_npa, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6805 AND `wr_cl_status`=4) AS New_Exp_19_npa, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6805 AND `wr_cl_status`=5) AS New_Dead_20_npa, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6805 AND `wr_cl_status`=3) AS New_Hold_21_npa, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6805 AND `wr_cl_status`=6) AS New_other_22_npa, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6805 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_npa, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6805 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_npa, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  6805
	
	////////// สภ.บ้านโคก ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6806 AND `wr_cl_status` IN (1,3)) AS All_01_bko, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6806 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_bko, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6806 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_bko, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6806 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_bko, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6806 AND `wr_cl_status` IN (1,3)) AS Old_All_1_bko, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6806 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_bko, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6806 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_bko, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6806 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_bko, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6806 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_bko, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6806 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_bko, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6806 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_bko, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6806 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_bko, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6806 AND `wr_cl_status`=6 ) AS Old_other_8_bko, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6806 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_bko, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6806 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_bko, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     ///// หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6806 AND `wr_cl_status` IN (1,3)) AS New_BF_13_bko, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6806 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_bko, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6806 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_bko, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6806 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_bko, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6806 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_bko, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6806 AND `wr_cl_status`=4) AS New_Exp_19_bko, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6806 AND `wr_cl_status`=5) AS New_Dead_20_bko, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6806 AND `wr_cl_status`=3) AS New_Hold_21_bko, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6806 AND `wr_cl_status`=6) AS New_other_22_bko, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6806 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_bko, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6806 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_bko, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  6806
	
	////////// สภ.พิชัย ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6807 AND `wr_cl_status` IN (1,3)) AS All_01_pic, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6807 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_pic, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6807 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_pic, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6807 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_pic, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6807 AND `wr_cl_status` IN (1,3)) AS Old_All_1_pic, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6807 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_pic, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6807 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_pic, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6807 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_pic, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6807 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_pic, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6807 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_pic, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6807 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_pic, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6807 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_pic, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6807 AND `wr_cl_status`=6 ) AS Old_other_8_pic, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6807 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_pic, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6807 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_pic, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     ///// หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6807 AND `wr_cl_status` IN (1,3)) AS New_BF_13_pic, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6807 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_pic, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6807 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_pic, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6807 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_pic, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6807 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_pic, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6807 AND `wr_cl_status`=4) AS New_Exp_19_pic, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6807 AND `wr_cl_status`=5) AS New_Dead_20_pic, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6807 AND `wr_cl_status`=3) AS New_Hold_21_pic, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6807 AND `wr_cl_status`=6) AS New_other_22_pic, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6807 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_pic, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6807 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_pic, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  6807
	
	////////// สภ.ฟากท่า ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6808 AND `wr_cl_status` IN (1,3)) AS All_01_fag, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6808 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_fag, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6808 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_fag, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6808 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_fag, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6808 AND `wr_cl_status` IN (1,3)) AS Old_All_1_fag, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6808 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_fag, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6808 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_fag, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6808 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_fag, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6808 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_fag, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6808 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_fag, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6808 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_fag, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6808 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_fag, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6808 AND `wr_cl_status`=6 ) AS Old_other_8_fag, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6808 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_fag, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6808 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_fag, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     ///// หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6808 AND `wr_cl_status` IN (1,3)) AS New_BF_13_fag, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6808 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_fag, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6808 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_fag, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6808 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_fag, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6808 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_fag, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6808 AND `wr_cl_status`=4) AS New_Exp_19_fag, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6808 AND `wr_cl_status`=5) AS New_Dead_20_fag, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6808 AND `wr_cl_status`=3) AS New_Hold_21_fag, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6808 AND `wr_cl_status`=6) AS New_other_22_fag, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6808 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_fag, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6808 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_fag, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  6808
	
	////////// สภ.เมืองอุตรดิตถ์ ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6809 AND `wr_cl_status` IN (1,3)) AS All_01_mud, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6809 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_mud, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6809 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_mud, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6809 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_mud, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6809 AND `wr_cl_status` IN (1,3)) AS Old_All_1_mud, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6809 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_mud, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6809 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_mud, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6809 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_mud, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6809 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_mud, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6809 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_mud, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6809 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_mud, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6809 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_mud, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6809 AND `wr_cl_status`=6 ) AS Old_other_8_mud, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6809 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_mud, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6809 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_mud, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     ///// หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6809 AND `wr_cl_status` IN (1,3)) AS New_BF_13_mud, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6809 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_mud, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6809 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_mud, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6809 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_mud, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6809 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_mud, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6809 AND `wr_cl_status`=4) AS New_Exp_19_mud, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6809 AND `wr_cl_status`=5) AS New_Dead_20_mud, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6809 AND `wr_cl_status`=3) AS New_Hold_21_mud, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6809 AND `wr_cl_status`=6) AS New_other_22_mud, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6809 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_mud, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6809 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_mud, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  6809
	
	////////// สภ.ลับแล ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6810 AND `wr_cl_status` IN (1,3)) AS All_01_lap, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6810 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_lap, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6810 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_lap, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6810 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_lap, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6810 AND `wr_cl_status` IN (1,3)) AS Old_All_1_lap, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6810 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_lap, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6810 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_lap, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6810 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_lap, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6810 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_lap, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6810 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_lap, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6810 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_lap, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6810 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_lap, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6810 AND `wr_cl_status`=6 ) AS Old_other_8_lap, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6810 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_lap, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6810 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_lap, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     ///// หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6810 AND `wr_cl_status` IN (1,3)) AS New_BF_13_lap, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6810 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_lap, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6810 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_lap, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6810 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_lap, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6810 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_lap, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6810 AND `wr_cl_status`=4) AS New_Exp_19_lap, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6810 AND `wr_cl_status`=5) AS New_Dead_20_lap, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6810 AND `wr_cl_status`=3) AS New_Hold_21_lap, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6810 AND `wr_cl_status`=6) AS New_other_22_lap, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6810 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_lap, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6810 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_lap, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  6810
	
	////////// สภ.วังกะพี้ ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6811 AND `wr_cl_status` IN (1,3)) AS All_01_wkp, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6811 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_wkp, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6811 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_wkp, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6811 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_wkp, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6811 AND `wr_cl_status` IN (1,3)) AS Old_All_1_wkp, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6811 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_wkp, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6811 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_wkp, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6811 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_wkp, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6811 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_wkp, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6811 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_wkp, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6811 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_wkp, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6811 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_wkp, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6811 AND `wr_cl_status`=6 ) AS Old_other_8_wkp, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6811 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_wkp, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6811 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_wkp, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     ///// หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6811 AND `wr_cl_status` IN (1,3)) AS New_BF_13_wkp, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6811 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_wkp, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6811 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_wkp, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6811 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_wkp, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6811 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_wkp, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6811 AND `wr_cl_status`=4) AS New_Exp_19_wkp, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6811 AND `wr_cl_status`=5) AS New_Dead_20_wkp, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6811 AND `wr_cl_status`=3) AS New_Hold_21_wkp, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6811 AND `wr_cl_status`=6) AS New_other_22_wkp, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6811 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_wkp, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6811 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_wkp, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  6811
	
	////////// สภ.เด่นเหล็ก ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6812 AND `wr_cl_status` IN (1,3)) AS All_01_den, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6812 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_den, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6812 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_den, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6812 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_den, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6812 AND `wr_cl_status` IN (1,3)) AS Old_All_1_den, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6812 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_den, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6812 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_den, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6812 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_den, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6812 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_den, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6812 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_den, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6812 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_den, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6812 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_den, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6812 AND `wr_cl_status`=6 ) AS Old_other_8_den, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6812 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_den, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6812 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_den, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     ///// หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6812 AND `wr_cl_status` IN (1,3)) AS New_BF_13_den, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6812 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_den, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6812 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_den, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6812 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_den, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6812 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_den, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6812 AND `wr_cl_status`=4) AS New_Exp_19_den, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6812 AND `wr_cl_status`=5) AS New_Dead_20_den, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6812 AND `wr_cl_status`=3) AS New_Hold_21_den, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6812 AND `wr_cl_status`=6) AS New_other_22_den, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6812 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_den, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6812 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_den, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  6812
	
	////////// สภ.นาอิน ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6813 AND `wr_cl_status` IN (1,3)) AS All_01_nin, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6813 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_nin, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6813 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_nin, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6813 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_nin, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6813 AND `wr_cl_status` IN (1,3)) AS Old_All_1_nin, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6813 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_nin, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6813 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_nin, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6813 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_nin, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6813 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_nin, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6813 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_nin, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6813 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_nin, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6813 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_nin, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6813 AND `wr_cl_status`=6 ) AS Old_other_8_nin, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6813 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_nin, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6813 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_nin, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     ///// หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6813 AND `wr_cl_status` IN (1,3)) AS New_BF_13_nin, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6813 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_nin, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6813 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_nin, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6813 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_nin, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6813 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_nin, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6813 AND `wr_cl_status`=4) AS New_Exp_19_nin, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6813 AND `wr_cl_status`=5) AS New_Dead_20_nin, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6813 AND `wr_cl_status`=3) AS New_Hold_21_nin, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6813 AND `wr_cl_status`=6) AS New_other_22_nin, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6813 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_nin, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6813 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_nin, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  6813
	
	////////// สภ.น้ำหมัน ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6814 AND `wr_cl_status` IN (1,3)) AS All_01_nmu, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6814 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_nmu, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6814 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_nmu, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6814 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_nmu, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6814 AND `wr_cl_status` IN (1,3)) AS Old_All_1_nmu, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6814 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_nmu, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6814 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_nmu, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6814 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_nmu, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6814 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_nmu, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6814 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_nmu, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6814 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_nmu, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6814 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_nmu, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6814 AND `wr_cl_status`=6 ) AS Old_other_8_nmu, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6814 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_nmu, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6814 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_nmu, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     ///// หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6814 AND `wr_cl_status` IN (1,3)) AS New_BF_13_nmu, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6814 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_nmu, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6814 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_nmu, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6814 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_nmu, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6814 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_nmu, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6814 AND `wr_cl_status`=4) AS New_Exp_19_nmu, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6814 AND `wr_cl_status`=5) AS New_Dead_20_nmu, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6814 AND `wr_cl_status`=3) AS New_Hold_21_nmu, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6814 AND `wr_cl_status`=6) AS New_other_22_nmu, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6814 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_nmu, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6814 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_nmu, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  6814
	
	////////// สภ.พญาแมน ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6815 AND `wr_cl_status` IN (1,3)) AS All_01_pym, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6815 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_pym, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6815 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_pym, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6815 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_pym, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6815 AND `wr_cl_status` IN (1,3)) AS Old_All_1_pym, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6815 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_pym, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6815 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_pym, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6815 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_pym, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6815 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_pym, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6815 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_pym, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6815 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_pym, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6815 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_pym, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6815 AND `wr_cl_status`=6 ) AS Old_other_8_pym, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6815 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_pym, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6815 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_pym, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     ///// หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6815 AND `wr_cl_status` IN (1,3)) AS New_BF_13_pym, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6815 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_pym, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6815 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_pym, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6815 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_pym, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6815 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_pym, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6815 AND `wr_cl_status`=4) AS New_Exp_19_pym, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6815 AND `wr_cl_status`=5) AS New_Dead_20_pym, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6815 AND `wr_cl_status`=3) AS New_Hold_21_pym, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6815 AND `wr_cl_status`=6) AS New_other_22_pym, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6815 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_pym, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6815 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_pym ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  6815
	
    "";
                      
try {
    $stmt = $pdo->prepare($sql);
    // Bind parameter values
    $stmt->bindParam(':m1', $m1);
    $stmt->bindParam(':m2', $m2);
    $stmt->bindParam(':provincial', $provincial);
    $stmt->bindParam(':date_next', $date_next);
    $stmt->bindParam(':date_prev', $date_prev);
    
    // Execute the prepared statement
    $stmt->execute();
    
	
    // Fetch the results if needed
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Process the results as required
    foreach ($results as $row) {
                      
	$numerator = 10;
    $denominator = 0;

    if ($denominator != 0) {
        $result = $numerator / $denominator;
		} else {
		$results = null; // Or any other appropriate value or action.
    }
//echo $sql;
///// ยอดของ ภ.จว.อุตรดิตถ์ ////
$New_All_15 = $row['New_BF_13']+$row['New_M_14'];     //  
$S_27 = $row['Old_All_1']+$New_All_15;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27

// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5 = $row['Old_Exp_1']+$row['Old_Exp_2'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear = $row['Old_C_4'] + $Old_Exp_5 + $row['Old_Dead_6']+ $row['Old_other_8'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear = $row['Old_C_4'] + $row['Old_Exp_1'] + $row['Old_Dead_6']+ $row['Old_other_8'];
$Old_Q_clear_10 = $row['Old_Q_2'] - $Old_Q_clear;
$Old_NQ_clear_11 = $row['Old_NQ_3'] - $row['Old_Exp_2'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13'] >$sum2){
    $New_BF_13 = $row['New_BF_13']-$sum2;
}else{
    $New_BF_13 = $row['New_BF_13'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16'] > $sum2){
    $New_Q_16 = $row['New_Q_16']-$sum2;
}else{
    $New_Q_16 = $row['New_Q_16'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24 = $New_Q_16-$row['S_23'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24 > $sum2){
        $S_24_1 = $S_24-$sum2;
}else{
        $S_24_1 = $S_24;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23 = $row['New_C_18'] + $row['New_Exp_19'] + $row['New_Dead_20']+ $row['New_other_22'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24 = $New_Q_16 - $New_clear_23;
$S_28 = $row['S_9']+$New_clear_23; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen = 0;
$NewPersen = 0;
$AllPersen = 0;
if ($row['All_01'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen = ($row['S_9'] / $row['All_01']) * 100;
	$OldPersen = round($OldPersen,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen = ($row['S_23'] / $row['All_01']) * 100;
	$NewPersen = round($NewPersen,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27 != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen = ($S_28 / $S_27) * 100;
	$AllPersen = round($AllPersen,2);  // จุดทศนิยม 2 ตำแหน่ง  
}

	
/////  สภ.ด่านแม่คำมัน  ////
		
$New_All_15_dan = $row['New_BF_13_dan']+$row['New_M_14_dan'];     //  
$S_27_dan = $row['Old_All_1_dan']+$New_All_15_dan;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_dan = $row['Old_Exp_1_dan']+$row['Old_Exp_2_dan'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_dan = $row['Old_C_4_dan'] + $Old_Exp_5_dan + $row['Old_Dead_6_dan']+ $row['Old_other_8_dan'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_dan = $row['Old_C_4_dan'] + $row['Old_Exp_1_dan'] + $row['Old_Dead_6_dan']+ $row['Old_other_8_dan'];
$Old_Q_clear_10_dan = $row['Old_Q_2_dan'] - $Old_Q_clear_dan;
$Old_NQ_clear_11_dan = $row['Old_NQ_3_dan'] - $row['Old_Exp_2_dan'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_dan'] >$sum2){
    $New_BF_13_dan = $row['New_BF_13_dan']-$sum2;
}else{
    $New_BF_13_dan = $row['New_BF_13_dan'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_dan'] > $sum2){
    $New_Q_16_dan = $row['New_Q_16_dan']-$sum2;
}else{
    $New_Q_16_dan = $row['New_Q_16_dan'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_dan = $New_Q_16_dan - $row['S_23_dan'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_dan > $sum2){
        $S_24_1_dan = $S_24_dan - $sum2;
}else{
        $S_24_1_dan = $S_24_dan;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_dan = $row['New_C_18_dan'] + $row['New_Exp_19_dan'] + $row['New_Dead_20_dan']+ $row['New_other_22_dan'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_dan = $New_Q_16_dan - $New_clear_23_dan;
$S_28_dan = $row['S_9_dan']+$New_clear_23_dan; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_dan = 0;
$NewPersen_dan = 0;
$AllPersen_dan = 0;
if ($row['All_01_dan'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_dan = ($row['S_9_dan'] / $row['All_01_dan']) * 100;
	$OldPersen_dan = round($OldPersen_dan,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_dan = ($row['S_23_dan'] / $row['All_01_dan']) * 100;
	$NewPersen_dan = round($NewPersen_dan,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_dan != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_dan = ($S_28_dan / $S_27_dan) * 100;
	$AllPersen_dan = round($AllPersen_dan,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.ตรอน  ////
		
$New_All_15_tro = $row['New_BF_13_tro']+$row['New_M_14_tro'];     //  
$S_27_tro = $row['Old_All_1_tro']+$New_All_15_tro;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_tro = $row['Old_Exp_1_tro']+$row['Old_Exp_2_tro'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_tro = $row['Old_C_4_tro'] + $Old_Exp_5_tro + $row['Old_Dead_6_tro']+ $row['Old_other_8_tro'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_tro = $row['Old_C_4_tro'] + $row['Old_Exp_1_tro'] + $row['Old_Dead_6_tro']+ $row['Old_other_8_tro'];
$Old_Q_clear_10_tro = $row['Old_Q_2_tro'] - $Old_Q_clear_tro;
$Old_NQ_clear_11_tro = $row['Old_NQ_3_tro'] - $row['Old_Exp_2_tro'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_tro'] >$sum2){
    $New_BF_13_tro = $row['New_BF_13_tro']-$sum2;
}else{
    $New_BF_13_tro = $row['New_BF_13_tro'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_tro'] > $sum2){
    $New_Q_16_tro = $row['New_Q_16_tro']-$sum2;
}else{
    $New_Q_16_tro = $row['New_Q_16_tro'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_tro = $New_Q_16_tro - $row['S_23_tro'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_tro > $sum2){
        $S_24_1_tro = $S_24_tro - $sum2;
}else{
        $S_24_1_tro = $S_24_tro;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_tro = $row['New_C_18_tro'] + $row['New_Exp_19_tro'] + $row['New_Dead_20_tro']+ $row['New_other_22_tro'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_tro = $New_Q_16_tro - $New_clear_23_tro;
$S_28_tro = $row['S_9_tro']+$New_clear_23_tro; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_tro = 0;
$NewPersen_tro = 0;
$AllPersen_tro = 0;
if ($row['All_01_tro'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_tro = ($row['S_9_tro'] / $row['All_01_tro']) * 100;
	$OldPersen_tro = round($OldPersen_tro,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_tro = ($row['S_23_tro'] / $row['All_01_tro']) * 100;
	$NewPersen_tro = round($NewPersen_tro,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_tro != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_tro = ($S_28_tro / $S_27_tro) * 100;
	$AllPersen_tro = round($AllPersen_tro,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.ทองแสนขัน  ////
		
$New_All_15_tho = $row['New_BF_13_tho']+$row['New_M_14_tho'];     //  
$S_27_tho = $row['Old_All_1_tho']+$New_All_15_tho;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27

// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_tho = $row['Old_Exp_1_tho']+$row['Old_Exp_2_tho'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_tho = $row['Old_C_4_tho'] + $Old_Exp_5_tho + $row['Old_Dead_6_tho']+ $row['Old_other_8_tho'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_tho = $row['Old_C_4_tho'] + $row['Old_Exp_1_tho'] + $row['Old_Dead_6_tho']+ $row['Old_other_8_tho'];
$Old_Q_clear_10_tho = $row['Old_Q_2_tho'] - $Old_Q_clear_tho;
$Old_NQ_clear_11_tho = $row['Old_NQ_3_tho'] - $row['Old_Exp_2_tho'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_tho'] >$sum2){
    $New_BF_13_tho = $row['New_BF_13_tho']-$sum2;
}else{
    $New_BF_13_tho = $row['New_BF_13_tho'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_tho'] > $sum2){
    $New_Q_16_tho = $row['New_Q_16_tho']-$sum2;
}else{
    $New_Q_16_tho = $row['New_Q_16_tho'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_tho = $New_Q_16_tho - $row['S_23_tho'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_tho > $sum2){
        $S_24_1_tho = $S_24_tho - $sum2;
}else{
        $S_24_1_tho = $S_24_tho;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_tho = $row['New_C_18_tho'] + $row['New_Exp_19_tho'] + $row['New_Dead_20_tho']+ $row['New_other_22_tho'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_tho = $New_Q_16_tho - $New_clear_23_tho;
$S_28_tho = $row['S_9_tho']+$New_clear_23_tho; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_tho = 0;
$NewPersen_tho = 0;
$AllPersen_tho = 0;
if ($row['All_01_tho'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_tho = ($row['S_9_tho'] / $row['All_01_tho']) * 100;
	$OldPersen_tho = round($OldPersen_tho,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_tho = ($row['S_23_tho'] / $row['All_01_tho']) * 100;
	$NewPersen_tho = round($NewPersen_tho,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_tho != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_tho = ($S_28_tho / $S_27_tho) * 100;
	$AllPersen_tho = round($AllPersen_tho,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.ท่าปลา  ////
		
$New_All_15_thp = $row['New_BF_13_thp']+$row['New_M_14_thp'];     //  
$S_27_thp = $row['Old_All_1_thp']+$New_All_15_thp;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_thp = $row['Old_Exp_1_thp']+$row['Old_Exp_2_thp'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_thp = $row['Old_C_4_thp'] + $Old_Exp_5_thp + $row['Old_Dead_6_thp']+ $row['Old_other_8_thp'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_thp = $row['Old_C_4_thp'] + $row['Old_Exp_1_thp'] + $row['Old_Dead_6_thp']+ $row['Old_other_8_thp'];
$Old_Q_clear_10_thp = $row['Old_Q_2_thp'] - $Old_Q_clear_thp;
$Old_NQ_clear_11_thp = $row['Old_NQ_3_thp'] - $row['Old_Exp_2_thp'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_thp'] >$sum2){
    $New_BF_13_thp = $row['New_BF_13_thp']-$sum2;
}else{
    $New_BF_13_thp = $row['New_BF_13_thp'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_thp'] > $sum2){
    $New_Q_16_thp = $row['New_Q_16_thp']-$sum2;
}else{
    $New_Q_16_thp = $row['New_Q_16_thp'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_thp = $New_Q_16_thp - $row['S_23_thp'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_thp > $sum2){
        $S_24_1_thp = $S_24_thp - $sum2;
}else{
        $S_24_1_thp = $S_24_thp;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_thp = $row['New_C_18_thp'] + $row['New_Exp_19_thp'] + $row['New_Dead_20_thp']+ $row['New_other_22_thp'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_thp = $New_Q_16_thp - $New_clear_23_thp;
$S_28_thp = $row['S_9_thp']+$New_clear_23_thp; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_thp = 0;
$NewPersen_thp = 0;
$AllPersen_thp = 0;
if ($row['All_01_thp'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_thp = ($row['S_9_thp'] / $row['All_01_thp']) * 100;
	$OldPersen_thp = round($OldPersen_thp,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_thp = ($row['S_23_thp'] / $row['All_01_thp']) * 100;
	$NewPersen_thp = round($NewPersen_thp,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_thp != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_thp = ($S_28_thp / $S_27_thp) * 100;
	$AllPersen_thp = round($AllPersen_thp,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.น้ำปาด  ////
		
$New_All_15_npa = $row['New_BF_13_npa']+$row['New_M_14_npa'];     //  
$S_27_npa = $row['Old_All_1_npa']+$New_All_15_npa;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_npa = $row['Old_Exp_1_npa']+$row['Old_Exp_2_npa'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_npa = $row['Old_C_4_npa'] + $Old_Exp_5_npa + $row['Old_Dead_6_npa']+ $row['Old_other_8_npa'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_npa = $row['Old_C_4_npa'] + $row['Old_Exp_1_npa'] + $row['Old_Dead_6_npa']+ $row['Old_other_8_npa'];
$Old_Q_clear_10_npa = $row['Old_Q_2_npa'] - $Old_Q_clear_npa;
$Old_NQ_clear_11_npa = $row['Old_NQ_3_npa'] - $row['Old_Exp_2_npa'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_npa'] >$sum2){
    $New_BF_13_npa = $row['New_BF_13_npa']-$sum2;
}else{
    $New_BF_13_npa = $row['New_BF_13_npa'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_npa'] > $sum2){
    $New_Q_16_npa = $row['New_Q_16_npa']-$sum2;
}else{
    $New_Q_16_npa = $row['New_Q_16_npa'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_npa = $New_Q_16_npa - $row['S_23_npa'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_npa > $sum2){
        $S_24_1_npa = $S_24_npa - $sum2;
}else{
        $S_24_1_npa = $S_24_npa;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_npa = $row['New_C_18_npa'] + $row['New_Exp_19_npa'] + $row['New_Dead_20_npa']+ $row['New_other_22_npa'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_npa = $New_Q_16_npa - $New_clear_23_npa;
$S_28_npa = $row['S_9_npa']+$New_clear_23_npa; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_npa = 0;
$NewPersen_npa = 0;
$AllPersen_npa = 0;
if ($row['All_01_npa'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_npa = ($row['S_9_npa'] / $row['All_01_npa']) * 100;
	$OldPersen_npa = round($OldPersen_npa,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_npa = ($row['S_23_npa'] / $row['All_01_npa']) * 100;
	$NewPersen_npa = round($NewPersen_npa,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_npa != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_npa = ($S_28_npa / $S_27_npa) * 100;
	$AllPersen_npa = round($AllPersen_npa,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.บ้านโคก  ////
		
$New_All_15_bko = $row['New_BF_13_bko']+$row['New_M_14_bko'];     //  
$S_27_bko = $row['Old_All_1_bko']+$New_All_15_bko;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_bko = $row['Old_Exp_1_bko']+$row['Old_Exp_2_bko'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_bko = $row['Old_C_4_bko'] + $Old_Exp_5_bko + $row['Old_Dead_6_bko']+ $row['Old_other_8_bko'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_bko = $row['Old_C_4_bko'] + $row['Old_Exp_1_bko'] + $row['Old_Dead_6_bko']+ $row['Old_other_8_bko'];
$Old_Q_clear_10_bko = $row['Old_Q_2_bko'] - $Old_Q_clear_bko;
$Old_NQ_clear_11_bko = $row['Old_NQ_3_bko'] - $row['Old_Exp_2_bko'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_bko'] >$sum2){
    $New_BF_13_bko = $row['New_BF_13_bko']-$sum2;
}else{
    $New_BF_13_bko = $row['New_BF_13_bko'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_bko'] > $sum2){
    $New_Q_16_bko = $row['New_Q_16_bko']-$sum2;
}else{
    $New_Q_16_bko = $row['New_Q_16_bko'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_bko = $New_Q_16_bko - $row['S_23_bko'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_bko > $sum2){
        $S_24_1_bko = $S_24_bko - $sum2;
}else{
        $S_24_1_bko = $S_24_bko;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_bko = $row['New_C_18_bko'] + $row['New_Exp_19_bko'] + $row['New_Dead_20_bko']+ $row['New_other_22_bko'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_bko = $New_Q_16_bko - $New_clear_23_bko;
$S_28_bko = $row['S_9_bko']+$New_clear_23_bko; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_bko = 0;
$NewPersen_bko = 0;
$AllPersen_bko = 0;
if ($row['All_01_bko'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_bko = ($row['S_9_bko'] / $row['All_01_bko']) * 100;
	$OldPersen_bko = round($OldPersen_bko,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_bko = ($row['S_23_bko'] / $row['All_01_bko']) * 100;
	$NewPersen_bko = round($NewPersen_bko,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_bko != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_bko = ($S_28_bko / $S_27_bko) * 100;
	$AllPersen_bko = round($AllPersen_bko,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.พิชัย  ////
		
$New_All_15_pic = $row['New_BF_13_pic']+$row['New_M_14_pic'];     //  
$S_27_pic = $row['Old_All_1_pic']+$New_All_15_pic;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_pic = $row['Old_Exp_1_pic']+$row['Old_Exp_2_pic'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_pic = $row['Old_C_4_pic'] + $Old_Exp_5_pic + $row['Old_Dead_6_pic']+ $row['Old_other_8_pic'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_pic = $row['Old_C_4_pic'] + $row['Old_Exp_1_pic'] + $row['Old_Dead_6_pic']+ $row['Old_other_8_pic'];
$Old_Q_clear_10_pic = $row['Old_Q_2_pic'] - $Old_Q_clear_pic;
$Old_NQ_clear_11_pic = $row['Old_NQ_3_pic'] - $row['Old_Exp_2_pic'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_pic'] >$sum2){
    $New_BF_13_pic = $row['New_BF_13_pic']-$sum2;
}else{
    $New_BF_13_pic = $row['New_BF_13_pic'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_pic'] > $sum2){
    $New_Q_16_pic = $row['New_Q_16_pic']-$sum2;
}else{
    $New_Q_16_pic = $row['New_Q_16_pic'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_pic = $New_Q_16_pic - $row['S_23_pic'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_pic > $sum2){
        $S_24_1_pic = $S_24_pic - $sum2;
}else{
        $S_24_1_pic = $S_24_pic;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_pic = $row['New_C_18_pic'] + $row['New_Exp_19_pic'] + $row['New_Dead_20_pic']+ $row['New_other_22_pic'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_pic = $New_Q_16_pic - $New_clear_23_pic;
$S_28_pic = $row['S_9_pic']+$New_clear_23_pic; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_pic = 0;
$NewPersen_pic = 0;
$AllPersen_pic = 0;
if ($row['All_01_pic'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_pic = ($row['S_9_pic'] / $row['All_01_pic']) * 100;
	$OldPersen_pic = round($OldPersen_pic,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_pic = ($row['S_23_pic'] / $row['All_01_pic']) * 100;
	$NewPersen_pic = round($NewPersen_pic,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_pic != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_pic = ($S_28_pic / $S_27_pic) * 100;
	$AllPersen_pic = round($AllPersen_pic,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.ฟากท่า  ////
		
$New_All_15_fag = $row['New_BF_13_fag']+$row['New_M_14_fag'];     //  
$S_27_fag = $row['Old_All_1_fag']+$New_All_15_fag;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_fag = $row['Old_Exp_1_fag']+$row['Old_Exp_2_fag'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_fag = $row['Old_C_4_fag'] + $Old_Exp_5_fag + $row['Old_Dead_6_fag']+ $row['Old_other_8_fag'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_fag = $row['Old_C_4_fag'] + $row['Old_Exp_1_fag'] + $row['Old_Dead_6_fag']+ $row['Old_other_8_fag'];
$Old_Q_clear_10_fag = $row['Old_Q_2_fag'] - $Old_Q_clear_fag;
$Old_NQ_clear_11_fag = $row['Old_NQ_3_fag'] - $row['Old_Exp_2_fag'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_fag'] >$sum2){
    $New_BF_13_fag = $row['New_BF_13_fag']-$sum2;
}else{
    $New_BF_13_fag = $row['New_BF_13_fag'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_fag'] > $sum2){
    $New_Q_16_fag = $row['New_Q_16_fag']-$sum2;
}else{
    $New_Q_16_fag = $row['New_Q_16_fag'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_fag = $New_Q_16_fag - $row['S_23_fag'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_fag > $sum2){
        $S_24_1_fag = $S_24_fag - $sum2;
}else{
        $S_24_1_fag = $S_24_fag;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_fag = $row['New_C_18_fag'] + $row['New_Exp_19_fag'] + $row['New_Dead_20_fag']+ $row['New_other_22_fag'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_fag = $New_Q_16_fag - $New_clear_23_fag;
$S_28_fag = $row['S_9_fag']+$New_clear_23_fag; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_fag = 0;
$NewPersen_fag = 0;
$AllPersen_fag = 0;
if ($row['All_01_fag'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_fag = ($row['S_9_fag'] / $row['All_01_fag']) * 100;
	$OldPersen_fag = round($OldPersen_fag,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_fag = ($row['S_23_fag'] / $row['All_01_fag']) * 100;
	$NewPersen_fag = round($NewPersen_fag,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_fag != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_fag = ($S_28_fag / $S_27_fag) * 100;
	$AllPersen_fag = round($AllPersen_fag,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.เมืองอุตรดิตถ์  ////
		
$New_All_15_mud = $row['New_BF_13_mud']+$row['New_M_14_mud'];     //  
$S_27_mud = $row['Old_All_1_mud']+$New_All_15_mud;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_mud = $row['Old_Exp_1_mud']+$row['Old_Exp_2_mud'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_mud = $row['Old_C_4_mud'] + $Old_Exp_5_mud + $row['Old_Dead_6_mud']+ $row['Old_other_8_mud'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_mud = $row['Old_C_4_mud'] + $row['Old_Exp_1_mud'] + $row['Old_Dead_6_mud']+ $row['Old_other_8_mud'];
$Old_Q_clear_10_mud = $row['Old_Q_2_mud'] - $Old_Q_clear_mud;
$Old_NQ_clear_11_mud = $row['Old_NQ_3_mud'] - $row['Old_Exp_2_mud'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_mud'] >$sum2){
    $New_BF_13_mud = $row['New_BF_13_mud']-$sum2;
}else{
    $New_BF_13_mud = $row['New_BF_13_mud'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_mud'] > $sum2){
    $New_Q_16_mud = $row['New_Q_16_mud']-$sum2;
}else{
    $New_Q_16_mud = $row['New_Q_16_mud'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_mud = $New_Q_16_mud - $row['S_23_mud'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_mud > $sum2){
        $S_24_1_mud = $S_24_mud - $sum2;
}else{
        $S_24_1_mud = $S_24_mud;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_mud = $row['New_C_18_mud'] + $row['New_Exp_19_mud'] + $row['New_Dead_20_mud']+ $row['New_other_22_mud'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_mud = $New_Q_16_mud - $New_clear_23_mud;
$S_28_mud = $row['S_9_mud']+$New_clear_23_mud; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_mud = 0;
$NewPersen_mud = 0;
$AllPersen_mud = 0;
if ($row['All_01_mud'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_mud = ($row['S_9_mud'] / $row['All_01_mud']) * 100;
	$OldPersen_mud = round($OldPersen_mud,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_mud = ($row['S_23_mud'] / $row['All_01_mud']) * 100;
	$NewPersen_mud = round($NewPersen_mud,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_mud != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_mud = ($S_28_mud / $S_27_mud) * 100;
	$AllPersen_mud = round($AllPersen_mud,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.ลับแล  ////
		
$New_All_15_lap = $row['New_BF_13_lap']+$row['New_M_14_lap'];     //  
$S_27_lap = $row['Old_All_1_lap']+$New_All_15_lap;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_lap = $row['Old_Exp_1_lap']+$row['Old_Exp_2_lap'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_lap = $row['Old_C_4_lap'] + $Old_Exp_5_lap + $row['Old_Dead_6_lap']+ $row['Old_other_8_lap'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_lap = $row['Old_C_4_lap'] + $row['Old_Exp_1_lap'] + $row['Old_Dead_6_lap']+ $row['Old_other_8_lap'];
$Old_Q_clear_10_lap = $row['Old_Q_2_lap'] - $Old_Q_clear_lap;
$Old_NQ_clear_11_lap = $row['Old_NQ_3_lap'] - $row['Old_Exp_2_lap'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_lap'] >$sum2){
    $New_BF_13_lap = $row['New_BF_13_lap']-$sum2;
}else{
    $New_BF_13_lap = $row['New_BF_13_lap'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_lap'] > $sum2){
    $New_Q_16_lap = $row['New_Q_16_lap']-$sum2;
}else{
    $New_Q_16_lap = $row['New_Q_16_lap'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_lap = $New_Q_16_lap - $row['S_23_lap'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_lap > $sum2){
        $S_24_1_lap = $S_24_lap - $sum2;
}else{
        $S_24_1_lap = $S_24_lap;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_lap = $row['New_C_18_lap'] + $row['New_Exp_19_lap'] + $row['New_Dead_20_lap']+ $row['New_other_22_lap'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_lap = $New_Q_16_lap - $New_clear_23_lap;
$S_28_lap = $row['S_9_lap']+$New_clear_23_lap; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_lap = 0;
$NewPersen_lap = 0;
$AllPersen_lap = 0;
if ($row['All_01_lap'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_lap = ($row['S_9_lap'] / $row['All_01_lap']) * 100;
	$OldPersen_lap = round($OldPersen_lap,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_lap = ($row['S_23_lap'] / $row['All_01_lap']) * 100;
	$NewPersen_lap = round($NewPersen_lap,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_lap != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_lap = ($S_28_lap / $S_27_lap) * 100;
	$AllPersen_lap = round($AllPersen_lap,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.วังกะพี้  ////
		
$New_All_15_wkp = $row['New_BF_13_wkp']+$row['New_M_14_wkp'];     //  
$S_27_wkp = $row['Old_All_1_wkp']+$New_All_15_wkp;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_wkp = $row['Old_Exp_1_wkp']+$row['Old_Exp_2_wkp'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_wkp = $row['Old_C_4_wkp'] + $Old_Exp_5_wkp + $row['Old_Dead_6_wkp']+ $row['Old_other_8_wkp'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_wkp = $row['Old_C_4_wkp'] + $row['Old_Exp_1_wkp'] + $row['Old_Dead_6_wkp']+ $row['Old_other_8_wkp'];
$Old_Q_clear_10_wkp = $row['Old_Q_2_wkp'] - $Old_Q_clear_wkp;
$Old_NQ_clear_11_wkp = $row['Old_NQ_3_wkp'] - $row['Old_Exp_2_wkp'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_wkp'] >$sum2){
    $New_BF_13_wkp = $row['New_BF_13_wkp']-$sum2;
}else{
    $New_BF_13_wkp = $row['New_BF_13_wkp'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_wkp'] > $sum2){
    $New_Q_16_wkp = $row['New_Q_16_wkp']-$sum2;
}else{
    $New_Q_16_wkp = $row['New_Q_16_wkp'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_wkp = $New_Q_16_wkp - $row['S_23_wkp'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_wkp > $sum2){
        $S_24_1_wkp = $S_24_wkp - $sum2;
}else{
        $S_24_1_wkp = $S_24_wkp;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_wkp = $row['New_C_18_wkp'] + $row['New_Exp_19_wkp'] + $row['New_Dead_20_wkp']+ $row['New_other_22_wkp'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_wkp = $New_Q_16_wkp - $New_clear_23_wkp;
$S_28_wkp = $row['S_9_wkp']+$New_clear_23_wkp; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_wkp = 0;
$NewPersen_wkp = 0;
$AllPersen_wkp = 0;
if ($row['All_01_wkp'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_wkp = ($row['S_9_wkp'] / $row['All_01_wkp']) * 100;
	$OldPersen_wkp = round($OldPersen_wkp,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_wkp = ($row['S_23_wkp'] / $row['All_01_wkp']) * 100;
	$NewPersen_wkp = round($NewPersen_wkp,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_wkp != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_wkp = ($S_28_wkp / $S_27_wkp) * 100;
	$AllPersen_wkp = round($AllPersen_wkp,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.เด่นเหล็ก  ////
		
$New_All_15_den = $row['New_BF_13_den']+$row['New_M_14_den'];     //  
$S_27_den = $row['Old_All_1_den']+$New_All_15_den;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_den = $row['Old_Exp_1_den']+$row['Old_Exp_2_den'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_den = $row['Old_C_4_den'] + $Old_Exp_5_den + $row['Old_Dead_6_den']+ $row['Old_other_8_den'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_den = $row['Old_C_4_den'] + $row['Old_Exp_1_den'] + $row['Old_Dead_6_den']+ $row['Old_other_8_den'];
$Old_Q_clear_10_den = $row['Old_Q_2_den'] - $Old_Q_clear_den;
$Old_NQ_clear_11_den = $row['Old_NQ_3_den'] - $row['Old_Exp_2_den'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_den'] >$sum2){
    $New_BF_13_den = $row['New_BF_13_den']-$sum2;
}else{
    $New_BF_13_den = $row['New_BF_13_den'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_den'] > $sum2){
    $New_Q_16_den = $row['New_Q_16_den']-$sum2;
}else{
    $New_Q_16_den = $row['New_Q_16_den'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_den = $New_Q_16_den - $row['S_23_den'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_den > $sum2){
        $S_24_1_den = $S_24_den - $sum2;
}else{
        $S_24_1_den = $S_24_den;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_den = $row['New_C_18_den'] + $row['New_Exp_19_den'] + $row['New_Dead_20_den']+ $row['New_other_22_den'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_den = $New_Q_16_den - $New_clear_23_den;
$S_28_den = $row['S_9_den']+$New_clear_23_den; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_den = 0;
$NewPersen_den = 0;
$AllPersen_den = 0;
if ($row['All_01_den'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_den = ($row['S_9_den'] / $row['All_01_den']) * 100;
	$OldPersen_den = round($OldPersen_den,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_den = ($row['S_23_den'] / $row['All_01_den']) * 100;
	$NewPersen_den = round($NewPersen_den,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_den != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_den = ($S_28_den / $S_27_den) * 100;
	$AllPersen_den = round($AllPersen_den,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.นาอิน  ////
		
$New_All_15_nin = $row['New_BF_13_nin']+$row['New_M_14_nin'];     //  
$S_27_nin = $row['Old_All_1_nin']+$New_All_15_nin;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_nin = $row['Old_Exp_1_nin']+$row['Old_Exp_2_nin'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_nin = $row['Old_C_4_nin'] + $Old_Exp_5_nin + $row['Old_Dead_6_nin']+ $row['Old_other_8_nin'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_nin = $row['Old_C_4_nin'] + $row['Old_Exp_1_nin'] + $row['Old_Dead_6_nin']+ $row['Old_other_8_nin'];
$Old_Q_clear_10_nin = $row['Old_Q_2_nin'] - $Old_Q_clear_nin;
$Old_NQ_clear_11_nin = $row['Old_NQ_3_nin'] - $row['Old_Exp_2_nin'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_nin'] >$sum2){
    $New_BF_13_nin = $row['New_BF_13_nin']-$sum2;
}else{
    $New_BF_13_nin = $row['New_BF_13_nin'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_nin'] > $sum2){
    $New_Q_16_nin = $row['New_Q_16_nin']-$sum2;
}else{
    $New_Q_16_nin = $row['New_Q_16_nin'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_nin = $New_Q_16_nin - $row['S_23_nin'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_nin > $sum2){
        $S_24_1_nin = $S_24_nin - $sum2;
}else{
        $S_24_1_nin = $S_24_nin;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_nin = $row['New_C_18_nin'] + $row['New_Exp_19_nin'] + $row['New_Dead_20_nin']+ $row['New_other_22_nin'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_nin = $New_Q_16_nin - $New_clear_23_nin;
$S_28_nin = $row['S_9_nin']+$New_clear_23_nin; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_nin = 0;
$NewPersen_nin = 0;
$AllPersen_nin = 0;
if ($row['All_01_nin'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_nin = ($row['S_9_nin'] / $row['All_01_nin']) * 100;
	$OldPersen_nin = round($OldPersen_nin,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_nin = ($row['S_23_nin'] / $row['All_01_nin']) * 100;
	$NewPersen_nin = round($NewPersen_nin,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_nin != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_nin = ($S_28_nin / $S_27_nin) * 100;
	$AllPersen_nin = round($AllPersen_nin,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.น้ำหมัน  ////
		
$New_All_15_nmu = $row['New_BF_13_nmu']+$row['New_M_14_nmu'];     //  
$S_27_nmu = $row['Old_All_1_nmu']+$New_All_15_nmu;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_nmu = $row['Old_Exp_1_nmu']+$row['Old_Exp_2_nmu'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_nmu = $row['Old_C_4_nmu'] + $Old_Exp_5_nmu + $row['Old_Dead_6_nmu']+ $row['Old_other_8_nmu'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_nmu = $row['Old_C_4_nmu'] + $row['Old_Exp_1_nmu'] + $row['Old_Dead_6_nmu']+ $row['Old_other_8_nmu'];
$Old_Q_clear_10_nmu = $row['Old_Q_2_nmu'] - $Old_Q_clear_nmu;
$Old_NQ_clear_11_nmu = $row['Old_NQ_3_nmu'] - $row['Old_Exp_2_nmu'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_nmu'] >$sum2){
    $New_BF_13_nmu = $row['New_BF_13_nmu']-$sum2;
}else{
    $New_BF_13_nmu = $row['New_BF_13_nmu'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_nmu'] > $sum2){
    $New_Q_16_nmu = $row['New_Q_16_nmu']-$sum2;
}else{
    $New_Q_16_nmu = $row['New_Q_16_nmu'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_nmu = $New_Q_16_nmu - $row['S_23_nmu'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_nmu > $sum2){
        $S_24_1_nmu = $S_24_nmu - $sum2;
}else{
        $S_24_1_nmu = $S_24_nmu;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_nmu = $row['New_C_18_nmu'] + $row['New_Exp_19_nmu'] + $row['New_Dead_20_nmu']+ $row['New_other_22_nmu'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_nmu = $New_Q_16_nmu - $New_clear_23_nmu;
$S_28_nmu = $row['S_9_nmu']+$New_clear_23_nmu; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_nmu = 0;
$NewPersen_nmu = 0;
$AllPersen_nmu = 0;
if ($row['All_01_nmu'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_nmu = ($row['S_9_nmu'] / $row['All_01_nmu']) * 100;
	$OldPersen_nmu = round($OldPersen_nmu,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_nmu = ($row['S_23_nmu'] / $row['All_01_nmu']) * 100;
	$NewPersen_nmu = round($NewPersen_nmu,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_nmu != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_nmu = ($S_28_nmu / $S_27_nmu) * 100;
	$AllPersen_nmu = round($AllPersen_nmu,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.พญาแมน  ////
		
$New_All_15_pym = $row['New_BF_13_pym']+$row['New_M_14_pym'];     //  
$S_27_pym = $row['Old_All_1_pym']+$New_All_15_pym;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_pym = $row['Old_Exp_1_pym']+$row['Old_Exp_2_pym'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_pym = $row['Old_C_4_pym'] + $Old_Exp_5_pym + $row['Old_Dead_6_pym']+ $row['Old_other_8_pym'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_pym = $row['Old_C_4_pym'] + $row['Old_Exp_1_pym'] + $row['Old_Dead_6_pym']+ $row['Old_other_8_pym'];
$Old_Q_clear_10_pym = $row['Old_Q_2_pym'] - $Old_Q_clear_pym;
$Old_NQ_clear_11_pym = $row['Old_NQ_3_pym'] - $row['Old_Exp_2_pym'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_pym'] >$sum2){
    $New_BF_13_pym = $row['New_BF_13_pym']-$sum2;
}else{
    $New_BF_13_pym = $row['New_BF_13_pym'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_pym'] > $sum2){
    $New_Q_16_pym = $row['New_Q_16_pym']-$sum2;
}else{
    $New_Q_16_pym = $row['New_Q_16_pym'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_pym = $New_Q_16_pym - $row['S_23_pym'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_pym > $sum2){
        $S_24_1_pym = $S_24_pym - $sum2;
}else{
        $S_24_1_pym = $S_24_pym;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_pym = $row['New_C_18_pym'] + $row['New_Exp_19_pym'] + $row['New_Dead_20_pym']+ $row['New_other_22_pym'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_pym = $New_Q_16_pym - $New_clear_23_pym;
$S_28_pym = $row['S_9_pym']+$New_clear_23_pym; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_pym = 0;
$NewPersen_pym = 0;
$AllPersen_pym = 0;
if ($row['All_01_pym'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_pym = ($row['S_9_pym'] / $row['All_01_pym']) * 100;
	$OldPersen_pym = round($OldPersen_pym,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_pym = ($row['S_23_pym'] / $row['All_01_pym']) * 100;
	$NewPersen_pym = round($NewPersen_pym,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_pym != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_pym = ($S_28_pym / $S_27_pym) * 100;
	$AllPersen_pym = round($AllPersen_pym,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
?>
<!-- ภ.จว.อุตรดิตถ์ -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left; align-content: center; align-items: center; font-size: 20px; font-weight: bold' nowrap><span style="height:22.8pt; background-color:aqua; align-content: center; align-items: center" ><?= $name_provincial ?></span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: bold'>
     <?= $row['All_01'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: bold'>
     <?= $row['All_Q_02'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: bold'>&nbsp;<?= $row['All_Hold_03'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: bold'>
    <?= $row['All_NQ_04'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: bold'>&nbsp;<?= $row['Old_All_1'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: bold'>&nbsp;<?= $row['Old_Q_2'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: bold'><?= $row['Old_NQ_3'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: bold'>&nbsp;<?php echo (( $row['Old_C_4'] > 0) ? $row['Old_C_4'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: bold'>&nbsp;<?php echo (($Old_Exp_5 > 0) ? $Old_Exp_5 : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: bold'>&nbsp;<?php echo (($row['Old_Dead_6'] > 0) ? $row['Old_Dead_6'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: bold'><?php echo (($row['Old_Hold_7'] > 0) ? $row['Old_Hold_7'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: bold'>&nbsp;<?php echo (($row['Old_other_8'] > 0) ? $row['Old_other_8'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: bold'>&nbsp;<?php echo (($Old_clear > 0) ? $Old_clear : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: bold'>&nbsp;<?php echo (($Old_Q_clear_10 > 0) ? ($Old_Q_clear_10)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: bold'>&nbsp;<?php echo (($row['Old_NQ_3'] > 0) ? ($row['Old_NQ_3'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: bold'>&nbsp;<?= $OldPersen ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: bold'>&nbsp;<?php echo (($New_BF_13 > 0) ? ($New_BF_13)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: bold'>&nbsp;<?php echo (($row['New_M_14'] > 0) ? $row['New_M_14']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: bold'>&nbsp;<?php echo (($New_All_15 > 0) ? ($New_All_15)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: bold'><?php echo (($New_Q_16 > 0) ? ($New_Q_16)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: bold'><?php echo (($row['New_NQ_17'] > 0) ? $row['New_NQ_17'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: bold'><?php echo (($row['New_C_18'] > 0) ? $row['New_C_18'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: bold'><?php echo (($row['New_Exp_19'] > 0) ? $row['New_Exp_19'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: bold'><?php echo (($row['New_Dead_20'] > 0) ? $row['New_Dead_20'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: bold'><?php echo (($row['New_Hold_21'] > 0) ? $row['New_Hold_21'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: bold'><?php echo (($row['New_other_22'] > 0) ? $row['New_other_22'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: bold'><?php echo (($New_clear_23 > 0) ? $New_clear_23 : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: bold'><?php echo (($New_Q_bl_24 > 0) ? ($New_Q_bl_24)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: bold'><?php echo (($row['S_25'] > 0) ? ($row['S_25'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: bold'><?= $NewPersen ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: bold'><?= $S_27 ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: bold'><?= $S_28 ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: bold'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.ด่านแม่คำมัน -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.ด่านแม่คำมัน</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_dan'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_dan'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_dan'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_dan'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_dan'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_dan'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_dan'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_dan'] > 0) ? $row['Old_C_4_dan'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_dan > 0) ? $Old_Exp_5_dan : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_dan'] > 0) ? $row['Old_Dead_6_dan'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_dan'] > 0) ? $row['Old_Hold_7_dan'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_dan'] > 0) ? $row['Old_other_8_dan'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_dan > 0) ? $Old_clear_dan : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_dan > 0) ? ($Old_Q_clear_10_dan)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_dan'] > 0) ? ($row['Old_NQ_3_dan'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_dan ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_dan > 0) ? ($New_BF_13_dan)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_dan'] > 0) ? $row['New_M_14_dan']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_dan > 0) ? ($New_All_15_dan)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_dan > 0) ? ($New_Q_16_dan)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_dan'] > 0) ? $row['New_NQ_17_dan'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_dan'] > 0) ? $row['New_C_18_dan'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_dan'] > 0) ? $row['New_Exp_19_dan'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_dan'] > 0) ? $row['New_Dead_20_dan'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_dan'] > 0) ? $row['New_Hold_21_dan'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_dan'] > 0) ? $row['New_other_22_dan'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_dan > 0) ? $New_clear_23_dan : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_dan > 0) ? ($New_Q_bl_24_dan)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_dan'] > 0) ? ($row['S_25_dan'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_dan ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_dan ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_dan ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_dan > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_dan < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_dan ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.ตรอน -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.ตรอน</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_tro'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_tro'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_tro'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->

  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_tro'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_tro'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_tro'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_tro'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_tro'] > 0) ? $row['Old_C_4_tro'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_tro > 0) ? $Old_Exp_5_tro : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_tro'] > 0) ? $row['Old_Dead_6_tro'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_tro'] > 0) ? $row['Old_Hold_7_tro'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_tro'] > 0) ? $row['Old_other_8_tro'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_tro > 0) ? $Old_clear_tro : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_tro > 0) ? ($Old_Q_clear_10_tro)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_tro'] > 0) ? ($row['Old_NQ_3_tro'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_tro ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_tro > 0) ? ($New_BF_13_tro)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_tro'] > 0) ? $row['New_M_14_tro']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_tro > 0) ? ($New_All_15_tro)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_tro > 0) ? ($New_Q_16_tro)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_tro'] > 0) ? $row['New_NQ_17_tro'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_tro'] > 0) ? $row['New_C_18_tro'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_tro'] > 0) ? $row['New_Exp_19_tro'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_tro'] > 0) ? $row['New_Dead_20_tro'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_tro'] > 0) ? $row['New_Hold_21_tro'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_tro'] > 0) ? $row['New_other_22_tro'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_tro > 0) ? $New_clear_23_tro : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_tro > 0) ? ($New_Q_bl_24_tro)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_tro'] > 0) ? ($row['S_25_tro'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_tro ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_tro ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_tro ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_tro > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_tro < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_tro ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.่ทองแสนขัน -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.ทองแสนขัน</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_tho'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_tho'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_tho'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_tho'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_tho'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_tho'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_tho'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_tho'] > 0) ? $row['Old_C_4_tho'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_tho > 0) ? $Old_Exp_5_tho : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_tho'] > 0) ? $row['Old_Dead_6_tho'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_tho'] > 0) ? $row['Old_Hold_7_tho'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_tho'] > 0) ? $row['Old_other_8_tho'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_tho > 0) ? $Old_clear_tho : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_tho > 0) ? ($Old_Q_clear_10_tho)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_tho'] > 0) ? ($row['Old_NQ_3_tho'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_tho ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_tho > 0) ? ($New_BF_13_tho)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_tho'] > 0) ? $row['New_M_14_tho']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_tho > 0) ? ($New_All_15_tho)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_tho > 0) ? ($New_Q_16_tho)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_tho'] > 0) ? $row['New_NQ_17_tho'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_tho'] > 0) ? $row['New_C_18_tho'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_tho'] > 0) ? $row['New_Exp_19_tho'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_tho'] > 0) ? $row['New_Dead_20_tho'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_tho'] > 0) ? $row['New_Hold_21_tho'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_tho'] > 0) ? $row['New_other_22_tho'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_tho > 0) ? $New_clear_23_tho : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_tho > 0) ? ($New_Q_bl_24_tho)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_tho'] > 0) ? ($row['S_25_tho'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_tho ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_tho ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_tho ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_tho > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_tho < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_tho ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.่ท่าปลา -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.ท่าปลา</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_thp'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_thp'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_thp'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_thp'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_thp'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_thp'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_thp'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_thp'] > 0) ? $row['Old_C_4_thp'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_thp > 0) ? $Old_Exp_5_thp : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_thp'] > 0) ? $row['Old_Dead_6_thp'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_thp'] > 0) ? $row['Old_Hold_7_thp'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_thp'] > 0) ? $row['Old_other_8_thp'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_thp > 0) ? $Old_clear_thp : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_thp > 0) ? ($Old_Q_clear_10_thp)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_thp'] > 0) ? ($row['Old_NQ_3_thp'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_thp ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_thp > 0) ? ($New_BF_13_thp)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_thp'] > 0) ? $row['New_M_14_thp']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_thp > 0) ? ($New_All_15_thp)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_thp > 0) ? ($New_Q_16_thp)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_thp'] > 0) ? $row['New_NQ_17_thp'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_thp'] > 0) ? $row['New_C_18_thp'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_thp'] > 0) ? $row['New_Exp_19_thp'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_thp'] > 0) ? $row['New_Dead_20_thp'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_thp'] > 0) ? $row['New_Hold_21_thp'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_thp'] > 0) ? $row['New_other_22_thp'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_thp > 0) ? $New_clear_23_thp : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_thp > 0) ? ($New_Q_bl_24_thp)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_thp'] > 0) ? ($row['S_25_thp'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_thp ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_thp ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_thp ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_thp > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_thp < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_thp ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.น้ำปาด -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.น้ำปาด</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_npa'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_npa'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_npa'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_npa'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_npa'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_npa'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_npa'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_npa'] > 0) ? $row['Old_C_4_npa'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_npa > 0) ? $Old_Exp_5_npa : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_npa'] > 0) ? $row['Old_Dead_6_npa'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_npa'] > 0) ? $row['Old_Hold_7_npa'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_npa'] > 0) ? $row['Old_other_8_npa'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_npa > 0) ? $Old_clear_npa : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_npa > 0) ? ($Old_Q_clear_10_npa)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_npa'] > 0) ? ($row['Old_NQ_3_npa'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_npa ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_npa > 0) ? ($New_BF_13_npa)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_npa'] > 0) ? $row['New_M_14_npa']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_npa > 0) ? ($New_All_15_npa)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_npa > 0) ? ($New_Q_16_npa)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_npa'] > 0) ? $row['New_NQ_17_npa'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_npa'] > 0) ? $row['New_C_18_npa'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_npa'] > 0) ? $row['New_Exp_19_npa'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_npa'] > 0) ? $row['New_Dead_20_npa'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_npa'] > 0) ? $row['New_Hold_21_npa'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_npa'] > 0) ? $row['New_other_22_npa'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_npa > 0) ? $New_clear_23_npa : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_npa > 0) ? ($New_Q_bl_24_npa)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_npa'] > 0) ? ($row['S_25_npa'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_npa ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_npa ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_npa ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_npa > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_npa < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_npa ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.บ้านโคก -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.บ้านโคก</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_bko'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_bko'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_bko'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_bko'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_bko'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_bko'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_bko'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_bko'] > 0) ? $row['Old_C_4_bko'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_bko > 0) ? $Old_Exp_5_bko : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_bko'] > 0) ? $row['Old_Dead_6_bko'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_bko'] > 0) ? $row['Old_Hold_7_bko'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_bko'] > 0) ? $row['Old_other_8_bko'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_bko > 0) ? $Old_clear_bko : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_bko > 0) ? ($Old_Q_clear_10_bko)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_bko'] > 0) ? ($row['Old_NQ_3_bko'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_bko ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_bko > 0) ? ($New_BF_13_bko)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_bko'] > 0) ? $row['New_M_14_bko']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_bko > 0) ? ($New_All_15_bko)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_bko > 0) ? ($New_Q_16_bko)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_bko'] > 0) ? $row['New_NQ_17_bko'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_bko'] > 0) ? $row['New_C_18_bko'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_bko'] > 0) ? $row['New_Exp_19_bko'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_bko'] > 0) ? $row['New_Dead_20_bko'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_bko'] > 0) ? $row['New_Hold_21_bko'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_bko'] > 0) ? $row['New_other_22_bko'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_bko > 0) ? $New_clear_23_bko : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_bko > 0) ? ($New_Q_bl_24_bko)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_bko'] > 0) ? ($row['S_25_bko'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_bko ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_bko ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_bko ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_bko > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_bko < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_bko ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.พิชัย -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.พิชัย</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_pic'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_pic'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_pic'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_pic'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_pic'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_pic'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_pic'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_pic'] > 0) ? $row['Old_C_4_pic'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_pic > 0) ? $Old_Exp_5_pic : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_pic'] > 0) ? $row['Old_Dead_6_pic'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_pic'] > 0) ? $row['Old_Hold_7_pic'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_pic'] > 0) ? $row['Old_other_8_pic'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_pic > 0) ? $Old_clear_pic : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_pic > 0) ? ($Old_Q_clear_10_pic)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_pic'] > 0) ? ($row['Old_NQ_3_pic'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_pic ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_pic > 0) ? ($New_BF_13_pic)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_pic'] > 0) ? $row['New_M_14_pic']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_pic > 0) ? ($New_All_15_pic)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_pic > 0) ? ($New_Q_16_pic)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_pic'] > 0) ? $row['New_NQ_17_pic'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_pic'] > 0) ? $row['New_C_18_pic'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_pic'] > 0) ? $row['New_Exp_19_pic'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_pic'] > 0) ? $row['New_Dead_20_pic'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_pic'] > 0) ? $row['New_Hold_21_pic'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_pic'] > 0) ? $row['New_other_22_pic'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_pic > 0) ? $New_clear_23_pic : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_pic > 0) ? ($New_Q_bl_24_pic)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_pic'] > 0) ? ($row['S_25_pic'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_pic ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_pic ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_pic ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_pic > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_pic < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_pic ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.ฟากท่า -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.ฟากท่า</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_fag'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_fag'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_fag'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_fag'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_fag'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_fag'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_fag'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_fag'] > 0) ? $row['Old_C_4_fag'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_fag > 0) ? $Old_Exp_5_fag : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_fag'] > 0) ? $row['Old_Dead_6_fag'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_fag'] > 0) ? $row['Old_Hold_7_fag'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_fag'] > 0) ? $row['Old_other_8_fag'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_fag > 0) ? $Old_clear_fag : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_fag > 0) ? ($Old_Q_clear_10_fag)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_fag'] > 0) ? ($row['Old_NQ_3_fag'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_fag ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_fag > 0) ? ($New_BF_13_fag)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_fag'] > 0) ? $row['New_M_14_fag']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_fag > 0) ? ($New_All_15_fag)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_fag > 0) ? ($New_Q_16_fag)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_fag'] > 0) ? $row['New_NQ_17_fag'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_fag'] > 0) ? $row['New_C_18_fag'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_fag'] > 0) ? $row['New_Exp_19_fag'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_fag'] > 0) ? $row['New_Dead_20_fag'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_fag'] > 0) ? $row['New_Hold_21_fag'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_fag'] > 0) ? $row['New_other_22_fag'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_fag > 0) ? $New_clear_23_fag : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_fag > 0) ? ($New_Q_bl_24_fag)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_fag'] > 0) ? ($row['S_25_fag'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_fag ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_fag ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_fag ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_fag > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_fag < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_fag ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.เมืองอุตรดิตถ์ -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.เมืองอุตรดิตถ์</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_mud'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_mud'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_mud'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_mud'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_mud'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_mud'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_mud'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_mud'] > 0) ? $row['Old_C_4_mud'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_mud > 0) ? $Old_Exp_5_mud : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_mud'] > 0) ? $row['Old_Dead_6_mud'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_mud'] > 0) ? $row['Old_Hold_7_mud'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_mud'] > 0) ? $row['Old_other_8_mud'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_mud > 0) ? $Old_clear_mud : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_mud > 0) ? ($Old_Q_clear_10_mud)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_mud'] > 0) ? ($row['Old_NQ_3_mud'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_mud ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_mud > 0) ? ($New_BF_13_mud)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_mud'] > 0) ? $row['New_M_14_mud']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_mud > 0) ? ($New_All_15_mud)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_mud > 0) ? ($New_Q_16_mud)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_mud'] > 0) ? $row['New_NQ_17_mud'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_mud'] > 0) ? $row['New_C_18_mud'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_mud'] > 0) ? $row['New_Exp_19_mud'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_mud'] > 0) ? $row['New_Dead_20_mud'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_mud'] > 0) ? $row['New_Hold_21_mud'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_mud'] > 0) ? $row['New_other_22_mud'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_mud > 0) ? $New_clear_23_mud : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_mud > 0) ? ($New_Q_bl_24_mud)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_mud'] > 0) ? ($row['S_25_mud'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_mud ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_mud ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_mud ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_mud > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_mud < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_mud ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.ลับแล -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.ลับแล</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_lap'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_lap'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_lap'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_lap'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_lap'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_lap'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_lap'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_lap'] > 0) ? $row['Old_C_4_lap'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_lap > 0) ? $Old_Exp_5_lap : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_lap'] > 0) ? $row['Old_Dead_6_lap'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_lap'] > 0) ? $row['Old_Hold_7_lap'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_lap'] > 0) ? $row['Old_other_8_lap'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_lap > 0) ? $Old_clear_lap : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_lap > 0) ? ($Old_Q_clear_10_lap)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_lap'] > 0) ? ($row['Old_NQ_3_lap'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_lap ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_lap > 0) ? ($New_BF_13_lap)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_lap'] > 0) ? $row['New_M_14_lap']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_lap > 0) ? ($New_All_15_lap)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_lap > 0) ? ($New_Q_16_lap)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_lap'] > 0) ? $row['New_NQ_17_lap'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_lap'] > 0) ? $row['New_C_18_lap'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_lap'] > 0) ? $row['New_Exp_19_lap'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_lap'] > 0) ? $row['New_Dead_20_lap'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_lap'] > 0) ? $row['New_Hold_21_lap'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_lap'] > 0) ? $row['New_other_22_lap'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_lap > 0) ? $New_clear_23_lap : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_lap > 0) ? ($New_Q_bl_24_lap)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_lap'] > 0) ? ($row['S_25_lap'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_lap ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_lap ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_lap ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_lap > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_lap < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_lap ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.วังกะพี้ -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.วังกะพี้</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_wkp'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_wkp'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_wkp'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_wkp'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_wkp'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_wkp'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_wkp'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_wkp'] > 0) ? $row['Old_C_4_wkp'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_wkp > 0) ? $Old_Exp_5_wkp : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_wkp'] > 0) ? $row['Old_Dead_6_wkp'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_wkp'] > 0) ? $row['Old_Hold_7_wkp'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_wkp'] > 0) ? $row['Old_other_8_wkp'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_wkp > 0) ? $Old_clear_wkp : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_wkp > 0) ? ($Old_Q_clear_10_wkp)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_wkp'] > 0) ? ($row['Old_NQ_3_wkp'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_wkp ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_wkp > 0) ? ($New_BF_13_wkp)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_wkp'] > 0) ? $row['New_M_14_wkp']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_wkp > 0) ? ($New_All_15_wkp)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_wkp > 0) ? ($New_Q_16_wkp)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_wkp'] > 0) ? $row['New_NQ_17_wkp'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_wkp'] > 0) ? $row['New_C_18_wkp'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_wkp'] > 0) ? $row['New_Exp_19_wkp'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_wkp'] > 0) ? $row['New_Dead_20_wkp'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_wkp'] > 0) ? $row['New_Hold_21_wkp'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_wkp'] > 0) ? $row['New_other_22_wkp'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_wkp > 0) ? $New_clear_23_wkp : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_wkp > 0) ? ($New_Q_bl_24_wkp)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_wkp'] > 0) ? ($row['S_25_wkp'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_wkp ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_wkp ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_wkp ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_wkp > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_wkp < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_wkp ?>
</div>
	 </td> <!--  -->
 </tr>
	  
	  <!-- สภ.เด่นเหล็ก -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.เด่นเหล็ก</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_den'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_den'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_den'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_den'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_den'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_den'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_den'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_den'] > 0) ? $row['Old_C_4_den'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_den > 0) ? $Old_Exp_5_den : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_den'] > 0) ? $row['Old_Dead_6_den'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_den'] > 0) ? $row['Old_Hold_7_den'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_den'] > 0) ? $row['Old_other_8_den'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_den > 0) ? $Old_clear_den : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_den > 0) ? ($Old_Q_clear_10_den)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_den'] > 0) ? ($row['Old_NQ_3_den'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_den ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_den > 0) ? ($New_BF_13_den)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_den'] > 0) ? $row['New_M_14_den']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_den > 0) ? ($New_All_15_den)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_den > 0) ? ($New_Q_16_den)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_den'] > 0) ? $row['New_NQ_17_den'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_den'] > 0) ? $row['New_C_18_den'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_den'] > 0) ? $row['New_Exp_19_den'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_den'] > 0) ? $row['New_Dead_20_den'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_den'] > 0) ? $row['New_Hold_21_den'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_den'] > 0) ? $row['New_other_22_den'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_den > 0) ? $New_clear_23_den : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_den > 0) ? ($New_Q_bl_24_den)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_den'] > 0) ? ($row['S_25_den'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_den ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_den ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_den ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_den > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_den < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_den ?>
</div>
	 </td> <!--  -->
 </tr>
	    
<!-- สภ.นาอิน -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.นาอิน</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_nin'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_nin'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_nin'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_nin'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_nin'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_nin'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_nin'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_nin'] > 0) ? $row['Old_C_4_nin'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_nin > 0) ? $Old_Exp_5_nin : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_nin'] > 0) ? $row['Old_Dead_6_nin'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_nin'] > 0) ? $row['Old_Hold_7_nin'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_nin'] > 0) ? $row['Old_other_8_nin'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_nin > 0) ? $Old_clear_nin : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_nin > 0) ? ($Old_Q_clear_10_nin)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_nin'] > 0) ? ($row['Old_NQ_3_nin'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_nin ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_nin > 0) ? ($New_BF_13_nin)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_nin'] > 0) ? $row['New_M_14_nin']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_nin > 0) ? ($New_All_15_nin)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_nin > 0) ? ($New_Q_16_nin)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_nin'] > 0) ? $row['New_NQ_17_nin'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_nin'] > 0) ? $row['New_C_18_nin'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_nin'] > 0) ? $row['New_Exp_19_nin'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_nin'] > 0) ? $row['New_Dead_20_nin'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_nin'] > 0) ? $row['New_Hold_21_nin'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_nin'] > 0) ? $row['New_other_22_nin'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_nin > 0) ? $New_clear_23_nin : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_nin > 0) ? ($New_Q_bl_24_nin)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_nin'] > 0) ? ($row['S_25_nin'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_nin ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_nin ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_nin ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_nin > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_nin < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_nin ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.น้ำหมัน -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.น้ำหมัน</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_nmu'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_nmu'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_nmu'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_nmu'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_nmu'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_nmu'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_nmu'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_nmu'] > 0) ? $row['Old_C_4_nmu'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_nmu > 0) ? $Old_Exp_5_nmu : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_nmu'] > 0) ? $row['Old_Dead_6_nmu'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_nmu'] > 0) ? $row['Old_Hold_7_nmu'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_nmu'] > 0) ? $row['Old_other_8_nmu'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_nmu > 0) ? $Old_clear_nmu : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_nmu > 0) ? ($Old_Q_clear_10_nmu)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_nmu'] > 0) ? ($row['Old_NQ_3_nmu'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_nmu ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_nmu > 0) ? ($New_BF_13_nmu)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_nmu'] > 0) ? $row['New_M_14_nmu']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_nmu > 0) ? ($New_All_15_nmu)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_nmu > 0) ? ($New_Q_16_nmu)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_nmu'] > 0) ? $row['New_NQ_17_nmu'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_nmu'] > 0) ? $row['New_C_18_nmu'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_nmu'] > 0) ? $row['New_Exp_19_nmu'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_nmu'] > 0) ? $row['New_Dead_20_nmu'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_nmu'] > 0) ? $row['New_Hold_21_nmu'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_nmu'] > 0) ? $row['New_other_22_nmu'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_nmu > 0) ? $New_clear_23_nmu : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_nmu > 0) ? ($New_Q_bl_24_nmu)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_nmu'] > 0) ? ($row['S_25_nmu'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_nmu ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_nmu ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_nmu ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_nmu > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_nmu < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_nmu ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.พญาแมน -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.พญาแมน</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_pym'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_pym'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_pym'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_pym'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_pym'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_pym'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_pym'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_pym'] > 0) ? $row['Old_C_4_pym'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_pym > 0) ? $Old_Exp_5_pym : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_pym'] > 0) ? $row['Old_Dead_6_pym'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_pym'] > 0) ? $row['Old_Hold_7_pym'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_pym'] > 0) ? $row['Old_other_8_pym'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_pym > 0) ? $Old_clear_pym : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_pym > 0) ? ($Old_Q_clear_10_pym)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_pym'] > 0) ? ($row['Old_NQ_3_pym'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_pym ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_pym > 0) ? ($New_BF_13_pym)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_pym'] > 0) ? $row['New_M_14_pym']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_pym > 0) ? ($New_All_15_pym)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_pym > 0) ? ($New_Q_16_pym)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_pym'] > 0) ? $row['New_NQ_17_pym'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_pym'] > 0) ? $row['New_C_18_pym'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_pym'] > 0) ? $row['New_Exp_19_pym'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_pym'] > 0) ? $row['New_Dead_20_pym'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_pym'] > 0) ? $row['New_Hold_21_pym'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_pym'] > 0) ? $row['New_other_22_pym'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_pym > 0) ? $New_clear_23_pym : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_pym > 0) ? ($New_Q_bl_24_pym)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_pym'] > 0) ? ($row['S_25_pym'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_pym ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_pym ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_pym ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_pym > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_pym < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_pym ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<?php
    }
} catch (PDOException $e) {
    // Handle errors
    echo 'Query failed: ' . $e->getMessage();
    exit;
}
?>
  </tbody>
</table>

</div>    
&nbsp;&nbsp;<button class="btn btn-primary btn-lg mb-4" onClick="do_print()"> พิมพ์รายงาน </button>
<p style="font-size: 18px">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;เป้าหมาย ตร. ให้จับกุม หมายเก่า 3 % &nbsp;&nbsp;&nbsp;&nbsp; รวมหมายเก่า+หมายใหม่ 6.5 %</p>
    <hr>
<!--<p>&nbsp;&nbsp;<a href="#catch_old_w.php" class="btn btn-secondary btn-lg mb-4" >จับหมายเก่า</a>&nbsp;&nbsp;&nbsp;&nbsp;<a href="#catch_new_w.php" class="btn btn-secondary btn-lg mb-4" >จับหมายใหม่</a>&nbsp;&nbsp;</p>-->
<p>&nbsp;</p>
<script>
function dates_change()  // คำสั่งเปลี่ยนเดือน ปี
{
    var ms = $("#mi_cl_month option:selected").val();
    var ys = $("#mi_cl_year option:selected").val();
    window.location = "/Activity/Show_Activity7_UD_station.php?&month=" + ms + "&year=" + ys + "&rnd=" + Math.random();
}
  </script>
</body>

</html>
<!-- script สำหรับส่งไปพิมพ์รายงาน  -->
<script>
    
    function do_print()
    {
        var ms = $("#mi_cl_month option:selected").val();
        var ys = $("#mi_cl_year option:selected").val();
        var newW = window.open("/Activity/Show_Activity7_UD_station_print.php?&month=" + ms + "&year=" + ys + "&rnd=" + Math.random(), "_print", "menubar=no,titlebar=no,toolbar=no,status=no");
        newW.print();
        //window.print();
    }
    
</script>
