<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';
include '../Head_image.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);


?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
        <base target="_top">
		<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
		<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
  		<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
 		<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js"></script>

<title>ผู้ป่วยจิดเวช บ้านสวนใน CRIMES</title>
</head>
<!--<p><img src="../Image/Head2.jpg" width="100%" height="" alt=""/></p>-->
<body>
<div class="container-fluid" align="left">
    <div class=" h3 text-center  alert alert-success mb-4 mt-4 " role="alert" >กิจกรรม 14 ข้อมูลบุคคลคลุ้มคลั่ง วิกลจริต</div>
	<div class=" h3 text-center  alert alert-danger mb-4 mt-4 " role="alert" >ข้อมูลการดำเนินการ ตาม พ.ร.บ.สุขภาพจิต <?= $name_station ?></div>
		<div align="left">
            <a href="/WM/index.php?rnd=<?= rand(); ?>&page=report" class="btn btn-warning btn-lg mb-4" >ย้อนกลับ</a>
            &nbsp;&nbsp;<a href="/WatchmanData/Add_SMIV_action.php?rnd=<?= rand(); ?>" class="btn btn-success btn-lg mb-4" >เพิ่มข้อมูล</a>&nbsp;&nbsp;
        </div>
<div align="center">
    <div align="center">
        <table class="table-bordered table-hover" style="border: solid" width="90%" border="1" cellspacing="1" cellpadding="1">
          <tbody align="center" class="table-bordered table-hover" style="text-align: left">
            <tr>
              <td rowspan="3" style="background: #FCCC2A; border:solid #000000">ลำดับ</td>
              <td rowspan="3" style="background: #FCCC2A; border:solid #000000" nowrap>สถานีตำรวจ</td>
              <td rowspan="3" style="background: #FCCC2A; border:solid #000000">เลขบัตร</td>
              <td rowspan="3" style="background: #FCCC2A; border:solid #000000">ชื่อ สกุล</td>
              <td rowspan="3" style="background: #FCCC2A; border:solid #000000">บุคคลที่มีความผิดปกติทางจิต ตามมาตรา 22 ที่ตรวจพบในพื้นที่ (คน)</td>
              <td rowspan="3" style="background: #FCCC2A; border:solid #000000">ส่งตัวไปสถานพยาบาลของรัฐฯ ซึ่งอยู่ใกล้ เพื่อรับการตรวจวินิจฉัยและประเมินอาการเบื้องต้น (คน)</td>
              <td rowspan="3" style="background: #FCCC2A; border:solid #000000">คงเหลือ ยังไม่ได้ส่งตัว (คน)</td>
              <td colspan="8" style="background: #FCCC2A; border:solid #000000">เป็นบุคคลต้องได้รับการตรวจวินิจฉัยและประเมินอาการโดยละเอียด (คน)</td>
              <td rowspan="3" style="background: #FCCC2A; border:solid #000000">รวมอยู่ระหว่างการรักษา(คน)</td>
              <td rowspan="3" style="background: #FCCC2A; border:solid #000000">รวมอาการทุเรา/จำหน่ายออก(คน)</td>
              <td rowspan="3" style="background: #FCCC2A; border:solid #000000">ไม่ใช่บุคคลที่ต้องได้รับการตรวจวินิจฉัยและประเมินอาการโดยละเอียด/บำบัดรักษาเท่าที่จำเป็น/ส่งกลับ (คน)</td>
              <td rowspan="3" style="background: #FCCC2A; border:solid #000000">เอกสารจาก รพ.</td>
              <td rowspan="3" style="background: #FCCC2A; border:solid #000000">หมายเหตุ</td>
              <td rowspan="3" style="background: #FCCC2A; border:solid #000000">วันที่บันทึก</td>
              <td colspan="2" rowspan="3" style="background: #FCCC2A; border:solid #000000"></td>
            </tr>
            <tr>
              <td colspan="3" style="background: #6DF999; border:solid #000000">บำบัดรักษาแบบผู้ป่วยใน</td>
              <td colspan="5" style="background: #F9B16D; border:solid #000000">บำบัดรักษาแบบผู้ป่วยนอก</td>
            </tr>
            <tr>
              <td style="background: #6DF999; border:solid #000000">จำนวน (คน)</td>
              <td style="background: #6DF999; border:solid #000000">อยู่ระหว่างการบำบัด (คน)</td>
              <td style="background: #6DF999; border:solid #000000">ทุเรา/ จำหน่ายออก (คน)</td>
              <td style="background: #F9B16D; border:solid #000000">มีผู้รับดูแลผู้ป่วย (คน)</td>
              <td style="background: #F9B16D; border:solid #000000">ไม่มีผู้ดูแล/เข้าสถานส่งเคราะห์ (คน)</td>
              <td style="background: #F9B16D; border:solid #000000">รวม(คน)</td>
              <td style="background: #F9B16D; border:solid #000000">อาการทุเรา/จำหน่ายออก (คน)</td>
              <td style="background: #F9B16D; border:solid #000000">คงเหลืออยู่ระหว่างการรักษา (คน)</td>
            </tr>
<?php
              
$sql1= "SELECT
            T1.*,
            T2.station_name AS Station,
            T3.ps_cl_name AS Name, T3.ps_cl_surname AS Surname
        FROM
            `wm_tb_SMIV_action` AS T1 " .
            "LEFT JOIN wm_tb_personal AS T3 ON T1.smivac_idcard = T3.ps_cl_idcard " .
            "LEFT JOIN wm_tb_police_station2 AS T2 ON T1.station = T2.station_code 
		WHERE T1.station='$station'
			";

    $stmt = $pdo->prepare($sql1);
try{
    $stmt->execute();
}catch (PDOException $e) {
    echo '$sql1 Failed: '. $e->getMessage();
}
                   
$no = 1;
              
while($row1 = $stmt->fetch(PDO::FETCH_ASSOC))	{
    
    //ฟังก์ชั่น วันที่ ดึงจาก condb
    $strDate = DateThai( $row1["smivac_date"] );
    
    //ตรวจสอบไฟล์ สส.1 ก่อนว่า มีไฟล์ในตารางไหม หากมี ให้ส่งเป็นลิงค์
	$pdf = $row1["smivac_file"];
	if($pdf !== '')
	{
		$links = '<a href="/' . $row1["smivac_file"] .'" target="_blank">Download</a>';
	}
	else {
		$links = 'ไม่มีไฟล์';
	}

?>
            <tr>
              <td>&nbsp;<?= $no ?></td>
              <td nowrap>&nbsp;<?=$row1["Station"]?>&nbsp;</td>
              <td>&nbsp;<?=$row1["smivac_idcard"]?>&nbsp;</td>
              <td nowrap>&nbsp;<?=$row1["Name"]?> <?=$row1["Surname"]?>&nbsp;</td>
              <td>&nbsp;<?=$row1["smivac_smiv22"]?>&nbsp;</td>
              <td>&nbsp;<?=$row1["smivac_hos"]?>&nbsp;</td>
              <td>&nbsp;<?=$row1["smivac_no_hos"]?>&nbsp;</td>
              <td>&nbsp;<?=$row1["smivac_in_hos"]?>&nbsp;</td>
              <td>&nbsp;<?=$row1["smivac_treatment"]?>&nbsp;</td>
              <td>&nbsp;<?=$row1["smivac_in_hos_out"]?>&nbsp;</td>
              <td>&nbsp;<?=$row1["smivac_care"]?>&nbsp;</td>
              <td>&nbsp;<?=$row1["smivac_no_care"]?>&nbsp;</td>
              <td>&nbsp;&nbsp;</td>
              <td>&nbsp;<?=$row1["smivac_opd_out"]?>&nbsp;</td>
              <td>&nbsp;<?=$row1["smivac_opd_treatment"]?>&nbsp;</td>
              <td>&nbsp;<?=$row1["smivac_during_treatment"]?>&nbsp;</td>
              <td>&nbsp;<?=$row1["smivac_total_out"]?>&nbsp;</td>
              <td>&nbsp;<?=$row1["smivac_not_smiv"]?>&nbsp;</td>
              <td>&nbsp;<?=$links?>&nbsp;</td>
              <td>&nbsp;<?=$row1["smivac_remark"]?>&nbsp;</td>
              <td nowrap>&nbsp;<?=$strDate?>&nbsp;</td>
                
              <td><a href="/WatchmanData/Edit_SMIV_action.php?id=<?= $row1["smivac_aid"] ?>" class="btn btn-warning mb-4" >แก้ไข</a> </td>
	          <td><a href="#" class="btn btn-danger mb-4" onClick="Del('/WatchmanData/Del_SMIV_action.php?id=<?= $row1["smivac_aid"]?>&pcid=<?= $row1["smivac_idcard"]?>')">ลบ</a></td>
            </tr>
<?php  
	$no++;
}
?>
          </tbody>
        </table>
    </div>
</div>

</div>       

</body>
</html>
<script>
function Del(url)
{
	if(confirm("คุณจะลบข้อมูล หรือ ไม่ ?"))
	{
		window.location = url;		
	}
}
</script>

</body>
</html>
    <hr>
<!-- ------------------------------------------------------>
<!-- ------------------------------------------------------>
<!-- ------------------------------------------------------>
    
<div class="container-fluid" align="left">
    <div align="center">
		<?php
			if ($station == 6707) {
            echo '		
    <h3>ข้อมูลผู้ป่วยจิตเวช ในพื้นที่ สภ.บ้านสวน ในระบบ CRIMES</h3><br>
    <img src="../images/SMIV.jpg" class="shadow" width="80%" >
			';
        		} else{
				
			}
		?>
		
    </div>

