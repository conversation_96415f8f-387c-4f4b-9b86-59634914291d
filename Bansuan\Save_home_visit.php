<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../Alert.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);


//echo '<pre>';
//print_r($_POST);
//echo '</pre>';
//
//echo '<hr>';
//exit();

$hv_cl_aid = $_POST[ 'hv_cl_aid' ];
$hv_cl_idcard = $_POST[ 'hv_cl_idcard' ];
$hv_cl_datevisit = $_POST[ 'hv_cl_datevisit' ];
$hv_cl_pol_visit = $_POST[ 'hv_cl_pol_visit' ];
$hv_cl_find_or_not = $_POST[ 'hv_cl_find_or_not' ];
$hv_cl_find = $_POST[ 'hv_cl_find' ];
$hv_cl_find40 = $_POST[ 'hv_cl_find40' ];
$hv_cl_dna = $_POST[ 'hv_cl_dna' ];
$hv_cl_cause = $_POST[ 'hv_cl_cause' ];
$hv_cl_new_place = $_POST[ 'hv_cl_new_place' ];
$hv_cl_adr = $_POST[ 'hv_cl_adr' ];
$hv_cl_action = $_POST[ 'hv_cl_action' ];
$hv_cl_no_action = $_POST[ 'hv_cl_no_action' ];
$hv_cl_no_adr = $_POST[ 'hv_cl_no_adr' ];
$hv_cl_torvor = $_POST[ 'hv_cl_torvor' ];
$hv_cl_no_torvor = $_POST[ 'hv_cl_no_torvor' ];
$hv_cl_prison = $_POST[ 'hv_cl_prison' ];
$hv_cl_torvor2 = $_POST[ 'hv_cl_torvor2' ];
$hv_cl_no_torvor2 = $_POST[ 'hv_cl_no_torvor2' ];
$hv_cl_remark = $_POST[ 'hv_cl_remark' ];
$region = $_POST[ 'region' ];
$provincial = $_POST[ 'provincial' ];
$station = $_POST[ 'station' ];

$sql = "SELECT * FROM wm_tb_homevisit WHERE station=:station AND hv_cl_aid = :hv_cl_aid ";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':hv_cl_aid', $hv_cl_aid);
	$stmt->bindParam(':station', $station);
try{
    $stmt->execute();
}catch (PDOException $e) {
    echo $e->getMessage();
}
    $row = $stmt->fetch(PDO::FETCH_ASSOC);



if($row  > 0){
	 $sql = "UPDATE wm_tb_homevisit SET hv_cl_idcard=?, hv_cl_datevisit=?, hv_cl_pol_visit=?, hv_cl_find_or_not=?, hv_cl_find=?, hv_cl_find40=?, hv_cl_dna=?, hv_cl_cause=?, hv_cl_new_place=?, hv_cl_adr=?, hv_cl_action=?, hv_cl_no_action=?, hv_cl_no_adr=?, hv_cl_torvor=?, hv_cl_no_torvor=?, hv_cl_prison=?, hv_cl_torvor2=?, hv_cl_remark=?, region=?, provincial=?, station=? WHERE hv_cl_aid=?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$hv_cl_idcard, $hv_cl_datevisit, $hv_cl_pol_visit, $hv_cl_find_or_not, $hv_cl_find, $hv_cl_find40, $hv_cl_dna, $hv_cl_cause, $hv_cl_new_place, $hv_cl_adr, $hv_cl_action, $hv_cl_no_action, $hv_cl_no_adr, $hv_cl_torvor, $hv_cl_no_torvor, $hv_cl_prison, $hv_cl_torvor2, $hv_cl_remark, $region, $provincial, $station, $hv_cl_aid]);
}else{
	// Insert new record
    $sql = "INSERT INTO wm_tb_homevisit (hv_cl_idcard, hv_cl_datevisit, hv_cl_pol_visit, hv_cl_find_or_not, hv_cl_find, hv_cl_find40, hv_cl_dna, hv_cl_cause, hv_cl_new_place, hv_cl_adr, hv_cl_action, hv_cl_no_action, hv_cl_no_adr, hv_cl_torvor, hv_cl_no_torvor, hv_cl_prison, hv_cl_torvor2, hv_cl_remark, region, provincial, station) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$hv_cl_idcard, $hv_cl_datevisit, $hv_cl_pol_visit, $hv_cl_find_or_not, $hv_cl_find, $hv_cl_find40, $hv_cl_dna, $hv_cl_cause, $hv_cl_new_place, $hv_cl_adr, $hv_cl_action, $hv_cl_no_action, $hv_cl_no_adr, $hv_cl_torvor, $hv_cl_no_torvor, $hv_cl_prison, $hv_cl_torvor2, $hv_cl_remark, $region, $provincial, $station]);
}

$ip_address = $_SERVER['REMOTE_ADDR'];
$action = "Save home visit : $hv_cl_idcard"; // You can set the action to a suitable value
$data = str_replace("'", "", compact_array($_POST));
log_activity($acc, $action, $data, $ip_address);

if ($stmt->rowCount() > 0) {
    $_SESSION['success'] = "Data has been inserted succesfully";
    showSweetAlert("Saved successfully", "บันทึกข้อมูลเรียบร้อย");
    unset($_SESSION['success']); // Clear the session variable
    header("refresh:2; url=/Bansuan/home_visit.php?pcid={$hv_cl_idcard}");
} else {
    $_SESSION['error'] = "Error";
    showSweetAlert('Error', "บันทึกข้อมูล ไม่สำเร็จ", 'error');
    unset($_SESSION['error']); // Clear the session variable
    header("refresh:2; url=/Bansuan/home_visit.php?pcid={$hv_cl_idcard}");
}

$pdo = null;

?>
