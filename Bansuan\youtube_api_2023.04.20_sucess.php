<style>
.video-grid {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

.video-thumbnail {
    flex: 0 0 calc(20% - 16px);
    margin-bottom: 16px;
    box-sizing: border-box;
    padding: 8px;
    text-align: center;
}

.video-thumbnail img {
    max-width: 80%;
    height: auto;
}

.video-thumbnail h3 {
    margin: 8px 0 0;
    font-size: 16px;
    line-height: 1.4;
    font-weight: normal;
    color: #333;
    decoration:none;
}
</style>

<?php
//include '../Condb.php';
//include '../users.inc.php';


$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);
/*
// Generate the HTML code for the video thumbnail
$html = "<div class='video-thumbnail'>";
$html .= "<a href='https://www.youtube.com/watch?v={$video_id}' target='_blank'>";
$html .= "<img src='{$thumbnail_url}' alt='{$title}' />";
$html .= "<h3>{$title}</h3>";
$html .= "</a>";
$html .= "</div>";
*/

// Retrieve the video IDs from the youtube_video_id table
$sql = "SELECT video_id FROM youtube_video_id";
$result = mysqli_query($conn, $sql);


// Generate the HTML code for the video thumbnails
$html = "<div class='video-grid'>";
$count = 0;
while ($row = mysqli_fetch_assoc($result)) {
    $video_id = $row['video_id'];
    
    //$video_id = 'G-cRItd7XKk'; // Replace VIDEO_ID with the YouTube video ID
    $api_key = 'AIzaSyCIN1hO0EaE0HOhnhxk5i0tC1COym6fWSw'; // Replace YOUR_API_KEY with your YouTube API key
    
    // Build the API request URL
    $url = "https://www.googleapis.com/youtube/v3/videos?part=snippet&id={$video_id}&key={$api_key}";
    
    // Send the API request and decode the JSON response
    $response = file_get_contents($url);
    $data = json_decode($response);

    // Get the video title, description, and thumbnail URL
    $title = $data->items[0]->snippet->title;
    $description = $data->items[0]->snippet->description;
    $thumbnail_url = $data->items[0]->snippet->thumbnails->medium->url;
    
    $count++;
    if ($count == 1) {
        $html .= "<div class='video-row'>";
    }
    $html .= "<div class='video-thumbnail'>";
    $html .= "<a href='https://www.youtube.com/watch?v={$video_id}' target='_blank'>";
    //$html .= "<img src='https://i.ytimg.com/vi/{$video_id}/mqdefault.jpg' alt='Video thumbnail' />";
    $html .= "<img src='{$thumbnail_url}' alt='{$title}' />";
    $html .= "<h3>{$title}</h3>";
    $html .= "</a>";
    $html .= "</div>";
    if ($count == 4) {
        $html .= "</div>";
        $count = 0;
    }
}
if ($count > 0) {
    $html .= "</div>";
}
?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<link rel="stylesheet" href="../bootstrap/css/bootstrap-grid.css">
<title>Knowledge</title>
</head>

<body>
<br>
<div class="container">
 <!--Output the HTML code-->
<?php echo $html; ?>
    
</diV>
<br>
    <br>
    <div style="font-size: 20px"><b>หลักสูตรสืบสวนสอบสวนอาชญากรรมทางเทคโนโลยี</b></div><br>
    <div>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="https://drive.google.com/file/d/1o18Tl5Rzqdy2FCzSw5RiR55nZ2CBvI4B/view?usp=sharing" tabindex="1" target="new">อาชญากรรมทางเทคโนโลยีและการติดตามบุคคลโดยใช้เทคโนโลยี</a><br>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="https://drive.google.com/file/d/1e3qxECPI00CTJL2SrKUcNvMteayg6Gmi/view?usp=share_link" tabindex="1" target="new">การระบุตัวผู้ใช้งานสื่อสังคมออนไลน์</a><br>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="https://docs.google.com/presentation/d/1npxe6NZuqjK41feCHeqqdLrLVtdt-3mR/edit?usp=share_link&ouid=105732044994653255958&rtpof=true&sd=true" tabindex="1" target="new">Evolution of Technology Crime</a><br>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="https://drive.google.com/file/d/10NDhyqNlXSQ---uRdMs0BcM-XBZBTYPk/view?usp=share_link" tabindex="1" target="new">ความรู้พื้นฐานเกี่ยวกับพยานหลักฐานดิจิทัล</a><br>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="https://drive.google.com/file/d/1g4fgaZOxrpM6YlIuUtXsHcVp5yU2Qu4G/view?usp=share_link" tabindex="1" target="new">เตรียมข้อมูลในชั้นสืบสวน</a><br>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="https://drive.google.com/file/d/1ChoeLcGv_Rifpy4xEvIL1Hh09WGCGXDy/view?usp=share_link" tabindex="1" target="new">การจัดเก็บพยานหลักฐานดิจิทัลในที่เกิดเหตุ</a><br>
    </div>
    <br>
    <br>
</body>
</html>



<?php
// Close the MySQL connection
mysqli_close($conn);
?>
