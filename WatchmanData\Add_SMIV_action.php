<?php
include "../Condb.php"; //PDO
include '../users.inc.php';
include '../right_user.php';


// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// สำหรับเลือก จังหวัด อำเภอ ตำบล /และ สถานีตำรวจ
include("../config.inc.php");
include("../classes/class.database.inc.php");
$conn2 = get_connection(); // connect to mySQLDB

// สำหรับเลือกสถานีตำรวจทั่วประเทศ
$region = isset($_GET['region']) ? $_GET['region'] : 0; // sukhothai
$provincial = isset($_GET['provincial']) ? $_GET['provincial'] : 0; // sukhothai
//$station = isset($_GET['station']) ? $_GET['station'] : 0; // sukhothai

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>เพิ่มข้อมูล การดำเนินการตาม พรบ.สุขภาพจิต</title>
<script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>   
<link rel="stylesheet" href="/policeinnopolis/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 18px;
    }
input::placeholder {
  color: red;
}
.form-control::placeholder {
  color: sandybrown;
}
</style>
<!-- สำหรับ วันที่ แบบ พ.ศ. -->
<link rel="stylesheet" href="http://code.jquery.com/ui/1.9.1/themes/base/jquery-ui.css" />
<script src="http://code.jquery.com/jquery-1.8.2.js"></script>
<script src="/policeinnopolis/js/jquery-ui.js"></script>
</head>

<body>
	
	<div class="container">
	<div class="row">
	<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> เพิ่มข้อมูล การดำเนินการตาม พรบ.สุขภาพจิต </div>
	<form action="Save_SMIV_action.php" method="POST" enctype="multipart/form-data" class="body">
        
        <label hidden ="hidden">ลำดับ</label>
		<input type = "text" name = "smivac_aid" class="form-control"  hidden ="hidden" >
        
		<span style="color: #1203F8">
		<label>เลขบัตรประชาชน <span style="color: #F90004">* จำเป็น</span> </label>
		</span>
		<input type = "text" name="smivac_idcard" class="form-control" placeholder="กรอกเลขบัตรประชาชน 13 หลัก" autocomplete="off" Required >

        <label>สถานีตำรวจ <span style="color: #F90004">* จำเป็น</span></label><br>
                <!-- เลือกสถานีตำรวจ -->
        <label> บช. </label><br>
        <select name="region" id="region" onChange="do_region_change()" class="form-control" Required >
                <option value="">&lt;&lt; เลือก บช. &gt;&gt;</option>
                <?php
			  	$res_re = $conn2->query("SELECT * FROM `wm_tb_police_region` 
                                            ORDER BY `name_region` ASC");
			     $selected = '';
			     while($row_re = $conn2->fetch_row($res_re)) 
                     {
                      $code_region = $row_re['code_region'];
                      $name_region = $row_re['name_region'];
                      // set default provionce to 64 >> sukhothai
                          if($code_region == $region) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$code_region' $selected> $name_region </option>\n";
                     }
			  
			  ?>
              </select>
        
        <label> บก. </label><br>
        <select name="provincial" id="provincial" onChange="do_provincial_change()" class="form-control" >
        <option value="0">&lt;&lt; เลือก บก. &gt;&gt;</option>
                <?php
                if($region > 0)
                {
                    $res_prov = $conn2->query("SELECT * FROM `wm_tb_police_provincial` 
                                                WHERE `region_code`='$region' 
                                                ORDER BY `provincial_code` ASC");
                    $selected = '';
                  while($res_prov = $conn2->fetch_row($res_prov)) 
                  {
                      $provincial_code = $res_prov['provincial_code']; // 
                      $provincial = $res_prov['provincial'];
                      // set default provionce to 64 >> sukhothai
                          if($provincial_code == $provincial) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$provincial_code' $selected> $provincial </option>\n";
                  }
                }	
                ?>
              </select>

         <label> สน./สภ. </label>
        <br>
        <select name="station" id="station" class="form-control" >
                <option value="0">&lt;&lt; เลือก สน./สภ. &gt;&gt;</option>
                <?php
	           if($provincial > 0)
                {
                    $res_st = $conn2->query("SELECT * FROM `wm_tb_police_station2` 
                                                WHERE provincial_code='$provincial_code'
                                                ORDER BY `station_code` ASC");
                    $selected = '';
                  while($row_st = $conn2->fetch_row($res_st)) 
                  {
                      $station_code = $row_st['station_code']; // 
                      $station_name = $row_st['station_name'];
                      // set default provionce to 64 >> sukhothai
                          if($station_code == $station) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$station_code' $selected> $station_name </option>\n";
                  }
                }	
                ?>                                                                     
              </select>
		<br>
        
		<label>บุคคลที่มีความผิดปกติทางจิต ตาม ม.22 (คน)</label>
		<select id="smivac_smiv22" name="smivac_smiv22" class="form-select form-select-sm" >
			<option value="" selected> </option>
			<option value="1">1</option>
			<option value="2">2</option>
			<option value="3">3</option>
			<option value="4">4</option>
			<option value="5">5</option>
		</select>
        
        <label>ส่งตัวไป รพ. ตรวจเบื้องต้น (คน)</label>
		<select id="smivac_hos" name="smivac_hos" class="form-select form-select-sm" >
			<option value="" selected> </option>
			<option value="1">1</option>
			<option value="2">2</option>
			<option value="3">3</option>
			<option value="4">4</option>
			<option value="5">5</option>
		</select>
        
        <label>คงเหลือยังไม่ได้ส่ง (คน)</label>
		<select id="smivac_no_hos" name="smivac_no_hos" class="form-select form-select-sm" >
			<option value="" selected> </option>
			<option value="1">1</option>
			<option value="2">2</option>
			<option value="3">3</option>
			<option value="4">4</option>
			<option value="5">5</option>
		</select>

        <b>ต้องตรวจวินิจฉัยและประเมินอาการโดยละเอียด</b><br>
        <b>บำบัดรักษาแบบผู้ป่วยใน</b><br>
        
        <label>จำนวนรักษาแบบผู้ป่วยใน (คน)</label>
		<select id="smivac_in_hos" name="smivac_in_hos" class="form-select form-select-sm" >
			<option value="" selected> </option>
			<option value="1">1</option>
			<option value="2">2</option>
			<option value="3">3</option>
			<option value="4">4</option>
			<option value="5">5</option>
		</select>
        
        <label>อยู่ระหว่างบำบัด (คน)</label>
		<select id="smivac_treatment" name="smivac_treatment" class="form-select form-select-sm" >
			<option value="" selected> </option>
			<option value="1">1</option>
			<option value="2">2</option>
			<option value="3">3</option>
			<option value="4">4</option>
			<option value="5">5</option>
		</select>
        
        <label>ทุเรา/จำหน่ายออก (คน)</label>
		<select id="smivac_in_hos_out" name="smivac_in_hos_out" class="form-select form-select-sm" >
			<option value="" selected> </option>
			<option value="1">1</option>
			<option value="2">2</option>
			<option value="3">3</option>
			<option value="4">4</option>
			<option value="5">5</option>
		</select>
        
        <b>บำบัดรักษาแบบผู้ป่วยนอก</b><br>
        
        <label>มีผู้รับดูแล (คน)</label>
		<select id="smivac_care" name="smivac_care" class="form-select form-select-sm" >
			<option value="" selected> </option>
			<option value="1">1</option>
			<option value="2">2</option>
			<option value="3">3</option>
			<option value="4">4</option>
			<option value="5">5</option>
		</select>
        
        <label>ไม่มีผู้ดูแล (คน)</label>
		<select id="smivac_no_care" name="smivac_no_care" class="form-select form-select-sm" >
			<option value="" selected> </option>
			<option value="1">1</option>
			<option value="2">2</option>
			<option value="3">3</option>
			<option value="4">4</option>
			<option value="5">5</option>
		</select>
        
        <label>อาการทุเรา/จำหน่ายออก (คน)</label>
		<select id="smivac_opd_out" name="smivac_opd_out" class="form-select form-select-sm" >
			<option value="" selected> </option>
			<option value="1">1</option>
			<option value="2">2</option>
			<option value="3">3</option>
			<option value="4">4</option>
			<option value="5">5</option>
		</select>
        
        <label>คงเหลืออยู่ระหว่างการรักษา (คน)</label>
		<select id="smivac_opd_treatment" name="smivac_opd_treatment" class="form-select form-select-sm" >
			<option value="" selected> </option>
			<option value="1">1</option>
			<option value="2">2</option>
			<option value="3">3</option>
			<option value="4">4</option>
			<option value="5">5</option>
		</select>
        
        <b>ยอดรวม</b><br>
        
        <label>รวมอยู่ระหว่างรักษา (คน)</label>
		<select id="smivac_during_treatment" name="smivac_during_treatment" class="form-select form-select-sm" >
			<option value="" selected> </option>
			<option value="1">1</option>
			<option value="2">2</option>
			<option value="3">3</option>
			<option value="4">4</option>
			<option value="5">5</option>
		</select>
        
        <label>รวมอาการทุเรา/จำหน่ายออก (คน)</label>
		<select id="smivac_total_out" name="smivac_total_out" class="form-select form-select-sm" >
			<option value="" selected> </option>
			<option value="1">1</option>
			<option value="2">2</option>
			<option value="3">3</option>
			<option value="4">4</option>
			<option value="5">5</option>
		</select>
        
        <label>ไม่ใช่บุคคลที่ต้องได้รับการตรวจวินิจฉัยและประเมินอาการโดยละเอียด/บำบัดรักษาเท่าที่จำเป็น/ส่งกลับ (คน)</label>
		<select id="smivac_not_smiv" name="smivac_not_smiv" class="form-select form-select-sm" >
			<option value="" selected> </option>
			<option value="1">1</option>
			<option value="2">2</option>
			<option value="3">3</option>
			<option value="4">4</option>
			<option value="5">5</option>
		</select>

		<label>หมายเหตุ</label>
		<input type = "text" name = "smivac_remark" class="form-control" placeholder="หมายเหตุ" >

		<div class="mb-3">
			<label for="formFileMultiple" class="form-label">เอกสารจาก รพ.</label>
			 <input class="form-control" type="file" id="smivac_file" name="smivac_file" multiple>
		</div>
        
        <label>วันที่บันทึก</label>
        <p><input type="text" name="smivac_date" id="datepicker" class="form-control" autocomplete="off" ></p>
        <script>
            function set_cal(ele)//function สร้างตัวเลือกปฎิทิน
            {
              $( ele ).datepicker({
                  onSelect:(date_text)=>
                  {
                    let arr=date_text.split("/");
                    let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])+543).toString();
                    $(ele).val(new_date);
                    $(ele).css("color","");
                  },
                  beforeShow:()=>{

                    if($(ele).val()!="")
                    {
                      let arr=$(ele).val().split("/");
                      let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])-543).toString();
                      $(ele).val(new_date);

                    }

                    $(ele).css("color","white");
                  },
                  onClose:()=>{

                      $(ele).css("color","");

                      if($(ele).val()!="")
                      {
                          let arr=$(ele).val().split("-");
                          if(parseInt(arr[2])<2500)
                          {
                              let new_date=arr[0]+"-"+arr[1]+"-"+(parseInt(arr[2])).toString();
                              $(ele).val(new_date);
                          }
                      }
                  },
                  dateFormat:"yy-mm-dd", //กำหนดรูปแบบวันที่เป็น วัน/เดือน/ปี
                  changeMonth:true,//กำหนดให้เลือกเดือนได้
                  changeYear:true,//กำหนดให้เลือกปีได้
                  showOtherMonths:true,//กำหนดให้แสดงวันของเดือนก่อนหน้าได้
              });
            }
          $(document).ready(function(){
            //เรียก function set_cal เมื่อเปิดหน้าเว็บ โดยส่ง object element ที่มี id เป็น datepicker เป็นพารามิเตอร์
            set_cal( $("#datepicker") );
          })
          </script>
        <!-- สำหรับ วันที่ แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ -->
        
		<p>
			<input type="submit" value="บันทึกข้อมูล" class="btn btn-success" >
			<td> <a href="/WatchmanData/Show_SMIV_action.php" class="btn btn-warning" >ยกเลิก</a> </td>
		</p>
		<br>
		<br>
	</form>
	</div>
	</div>
	</div>
<script>
// ฟังก์ชั่น เปลี่ยนภาค (บช.) เป็น ภ.จว. (บก.)
function do_region_change()
{
	var sel_region = document.getElementById("region");		
	var code_region = sel_region.options[sel_region.selectedIndex].value;

	$.get("../ajax/get_provincial.php?region=" + code_region, 
		  function(data, status){
				//alert("Data: " + data + "\nStatus: " + status);
				// jQuery style						
				$('#provincial').find('option').remove().end();
				var data = $.trim(data);
				if(data == '') {
					alert("ไม่พบข้อมูล บก. !");
					return;
				}

					//.append('<option value="whatever">text</option>').val('whatever');
				var datas = data.split(';');
				for(var i=0; i<datas.length; i++) {
					if(datas[i] == '') continue;
					datas[i] = datas[i].split(':');
					var code_region = datas[i][0];
					var name_region = datas[i][1];
					$('#provincial').append('<option value="'+ code_region+'">' + name_region + '</option>');
				}
                // Trigger the change event on the amphur dropdown so the tambon dropdown will be populated     
                $('#provincial').trigger('change');
		});
}
    //เลือก สถานี จากจังหวัด
function do_provincial_change()
{
	var sel_provincial = document.getElementById("region");		
	var provincial_code = sel_provincial.options[sel_provincial.selectedIndex].value;
    
    var sel_provincial2 = document.getElementById("provincial");		
	var station_code = sel_provincial2.options[sel_provincial2.selectedIndex].value;

	$.get("../ajax/get_station.php?region=" + provincial_code + "&provincial=" + station_code, 
			  function(data, status){
					//alert("Data: " + data + "\nStatus: " + status);
					// jQuery style						
					$('#station').find('option').remove().end();
					var data = $.trim(data);
					if(data == '') {
						alert("ไม่พบข้อมูลสถานี !");
						return;
					}

						//.append('<option value="whatever">text</option>').val('whatever');
					var datas = data.split(';');
					for(var i=0; i<datas.length; i++) {
						if(datas[i] == '') continue;
						datas[i] = datas[i].split(':');
						var station_code = datas[i][0];
						var station_name = datas[i][1];
						$('#station').append('<option value="'+ station_code+'">' + station_name + '</option>');
					}
			});
}
</script>
</body>
</html>