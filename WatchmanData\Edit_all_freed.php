<?php
include "../Condb.php"; //PDO
include '../users.inc.php';
include '../right_user.php';


// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

$id = $_GET['id'];

$sql = "SELECT * FROM wm_tb_freed WHERE fr_cl_aid=:id ";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':id', $id);
    $stmt->execute();
    $row = $stmt->fetch(PDO::FETCH_ASSOC);

$people_name = '';
if($row) 
{
	$pcid = $row["fr_cl_idcard"];		
	//
	$query1 = "SELECT * FROM wm_tb_personal WHERE ps_cl_idcard = :pcid";
    $stmt = $pdo->prepare($query1);
    $stmt->bindParam(':pcid', $pcid);
    $stmt->execute();
    $row1 = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $people_name = $row1['ps_cl_prefix'] . ' ' . $row1['ps_cl_name'] . ' ' . $row1['ps_cl_surname'];
    $people_image = $row1["ps_cl_image"];
    
    if ($people_image != '') {
        $people_image = "<img src='{$people_image}' height='50'> ";
    }
	else {
		//die("ไม่พบข้อมูล pcid: $pcid");
	}
}

// สำหรับเลือก จังหวัด อำเภอ ตำบล
include("../config.inc.php");
include("../classes/class.database.inc.php");
// สำหรับเลือก จังหวัด อำเภอ ตำบล
$conn2 = get_connection(); // connect to mySQLDB
// สำหรับเลือกสถานีตำรวจทั่วประเทศ
$region = isset($_GET['region']) ? $_GET['region'] : 0; // sukhothai
$provincial = isset($_GET['provincial']) ? $_GET['provincial'] : 0; // sukhothai
//$station = isset($_GET['station']) ? $_GET['station'] : 0; // sukhothai

?>


<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>แก้ไขข้อมูลบุคคลพ้นโทษ</title>
<script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/policeinnopolis/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 18px;
    }
input::placeholder {
  color: red;
}
.form-control::placeholder {
  color: sandybrown;
}
</style>
<script src="/jquery-ui-1.12.1/jquery-ui.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/jquery-ui-1.12.1/jquery-ui.css">
<link rel="stylesheet" href="/js/datetimepicker2.5.1/jquery.datetimepicker.min.css">
    
<!-- สำหรับ วันเกิด แบบ พ.ศ. -->
<link rel="stylesheet" href="http://code.jquery.com/ui/1.9.1/themes/base/jquery-ui.css" />
<script src="http://code.jquery.com/jquery-1.8.2.js"></script>
<script src="/policeinnopolis/js/jquery-ui.js"></script>
</head>

<body>
	<div class="container" >
	<div class="row">
		<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> แก้ไขข้อมูลบุคคลพ้นโทษ ของ <?= $people_name ?> <?= $people_image ?> </div>
	<form action="Save_all_freed_Edit.php" method="POST" enctype="multipart/form-data" class="body">	
        
		<label>ลำดับ</label>
		<input name="fr_cl_aid" type = "text" class="form-control" value= "<?= $row['fr_cl_aid'] ?>" hidden ="hidden"  >
        
		<label> เลขบัตรประชาชน </label>
			<input type = "text" name="fr_cl_idcard" class="form-control"  value="<?= $pcid ?>"  readonly="readonly">
			
			<label>สถานะพ้นโทษ หรือพักโทษ</label>
				<select id="fr_cl_status" name="fr_cl_status" class="form-select form-select-sm" >
					<option value="" selected>เลือก</option>
					<option value="บุคคลพ้นโทษ" <?php if($row['fr_cl_status'] == 'บุคคลพ้นโทษ') echo 'selected'; ?> >บุคคลพ้นโทษ</option>
					<option value="พักโทษ" <?php if($row['fr_cl_status'] == 'พักโทษ') echo 'selected'; ?> >พักโทษ</option>
				</select>
		
			<label> ต้องโทษฐาน </label>
			<input type = "text" name = "fr_cl_offense" class="form-control" value= "<?= $row['fr_cl_offense'] ?>" placeholder="ระบุฐานที่ต้องโทษ" >
			<label> กำหนดโทษ (ปี เดือน วัน)</label>
			<input type = "text" name = "fr_cl_in_prison" class="form-control" value= "<?= $row['fr_cl_in_prison'] ?>" placeholder="ระบุจำนวนวันที่ต้องรับโทษ" >
        
            <label>นับตั้งแต่</label>
            <p><input type="text" id="datepicker" name="fr_cl_start" value= "<?= $row['fr_cl_start'] ?>" class="form-control" autocomplete="off" ></p>
        <script>
            function set_cal(ele)//function สร้างตัวเลือกปฎิทิน
            {
              $( ele ).datepicker({
                  onSelect:(date_text)=>
                  {
                    let arr=date_text.split("/");
                    let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])+543).toString();
                    $(ele).val(new_date);
                    $(ele).css("color","");
                  },
                  beforeShow:()=>{

                    if($(ele).val()!="")
                    {
                      let arr=$(ele).val().split("/");
                      let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])-543).toString();
                      $(ele).val(new_date);

                    }

                    $(ele).css("color","white");
                  },
                  onClose:()=>{

                      $(ele).css("color","");

                      if($(ele).val()!="")
                      {
                          let arr=$(ele).val().split("-");
                          if(parseInt(arr[2])<2500)
                          {
                              let new_date=arr[0]+"-"+arr[1]+"-"+(parseInt(arr[2])).toString();
                              $(ele).val(new_date);
                          }
                      }
                  },
                  dateFormat:"yy-mm-dd", //กำหนดรูปแบบวันที่เป็น วัน/เดือน/ปี
                  changeMonth:true,//กำหนดให้เลือกเดือนได้
                  changeYear:true,//กำหนดให้เลือกปีได้
                  showOtherMonths:true,//กำหนดให้แสดงวันของเดือนก่อนหน้าได้
              });
            }
          $(document).ready(function(){
            //เรียก function set_cal เมื่อเปิดหน้าเว็บ โดยส่ง object element ที่มี id เป็น datepicker เป็นพารามิเตอร์
            set_cal( $("#datepicker") );
          })
          </script>
        <!-- สำหรับ วันที่ แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ -->
        
            <label>วันพ้นโทษ <span style="color: #F90004">* จำเป็น</span> </label>
            <p><input type="text" id="datepicker2" name="fr_cl_freed" value= "<?= $row['fr_cl_freed'] ?>" class="form-control" autocomplete="off" required ></p>
        <script>
            function set_cal(ele)//function สร้างตัวเลือกปฎิทิน
            {
              $( ele ).datepicker({
                  onSelect:(date_text)=>
                  {
                    let arr=date_text.split("/");
                    let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])+543).toString();
                    $(ele).val(new_date);
                    $(ele).css("color","");
                  },
                  beforeShow:()=>{

                    if($(ele).val()!="")
                    {
                      let arr=$(ele).val().split("/");
                      let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])-543).toString();
                      $(ele).val(new_date);

                    }

                    $(ele).css("color","white");
                  },
                  onClose:()=>{

                      $(ele).css("color","");

                      if($(ele).val()!="")
                      {
                          let arr=$(ele).val().split("-");
                          if(parseInt(arr[2])<2500)
                          {
                              let new_date=arr[0]+"-"+arr[1]+"-"+(parseInt(arr[2])).toString();
                              $(ele).val(new_date);
                          }
                      }
                  },
                  dateFormat:"yy-mm-dd", //กำหนดรูปแบบวันที่เป็น วัน/เดือน/ปี
                  changeMonth:true,//กำหนดให้เลือกเดือนได้
                  changeYear:true,//กำหนดให้เลือกปีได้
                  showOtherMonths:true,//กำหนดให้แสดงวันของเดือนก่อนหน้าได้
              });
            }
          $(document).ready(function(){
            //เรียก function set_cal เมื่อเปิดหน้าเว็บ โดยส่ง object element ที่มี id เป็น datepicker เป็นพารามิเตอร์
            set_cal( $("#datepicker2") );
          })
          </script>
        <!-- สำหรับ วันที่ แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ -->
			
			<label> ที่อยู่ตามบัตร </label>
			<input type = "text" name = "fr_cl_adrress1" class="form-control" value= "<?= $row['fr_cl_adrress1'] ?>" placeholder="ระบุที่อยู่ตามบัตร" >
        
			<label> ที่อยู่ที่พบตัว </label>
			<input type = "text" name = "fr_cl_adrress2" class="form-control" value= "<?= $row['fr_cl_adrress2'] ?>" placeholder="ระบุที่อยู่ที่พบตัว" >
        
			<label> พิกัดที่อยู่ที่พบตัว </label>
			<input type = "text" name = "fr_cl_location" class="form-control" value= "<?= $row['fr_cl_location'] ?>" placeholder="ระบุพิกัด" >
        
			<label> ผู้รับผิดชอบ </label>
			<input type = "text" name = "fr_cl_police" class="form-control" value= "<?= $row['fr_cl_police'] ?>" placeholder="ระบุผู้รับผิดชอบติดตามตรวจสอบ" >
		
			<label>สถานีตำรวจ <span style="color: #F90004">* จำเป็น</span></label><br>
                <!-- เลือกสถานีตำรวจ -->
        <label> บช. </label><br>
        <select name="region" id="region" onChange="do_region_change()" class="form-control" Required >
                <option value="">&lt;&lt; เลือก บช. &gt;&gt;</option>
                <?php
                $region = $row['region']; // <<ดึงข้อมูลจาก ตารางมา row ซึ่งเป็นคนละตัวกับ row_p
                //------------------------------------
			  	$res_re = $conn2->query("SELECT * FROM `wm_tb_police_region` 
                                            ORDER BY `name_region` ASC");
			     $selected = '';
			     while($row_re = $conn2->fetch_row($res_re)) 
                     {
                      $code_region = $row_re['code_region'];
                      $name_region = $row_re['name_region'];
                      // set default provionce to 64 >> sukhothai
                          if($code_region == $region) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$code_region' $selected> $name_region </option>\n";
                     }
			  ?>
              </select>
        
        
        <label> บก. </label><br>        
        <select name="provincial" id="provincial" onChange="do_provincial_change()" value="<?php echo $provincial; ?>" class="form-control" >
        <option value="0">&lt;&lt; เลือก บก. &gt;&gt;</option>
                <?php
                if($region > 0)
                {
                    $provincial = $row['provincial']; // <<
                    //-------------------------------------
                    $res_prov = $conn2->query("SELECT * FROM `wm_tb_police_provincial` 
                                                WHERE `region_code`='$region' 
                                                ORDER BY `provincial_code` ASC");
                    $selected = '';
                  while($res_prov = $conn2->fetch_row($res_prov)) 
                  {
                      $provincial_code = $res_prov['provincial_code']; // 
                      $provincial = $res_prov['provincial'];
                      // set default provionce to 64 >> sukhothai
                          if($provincial_code == $provincial) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$provincial_code' $selected> $provincial </option>\n";
                  }
                }	
                ?>
              </select>
        
         <label> สน./สภ. </label>
        <br>
                
        <select name="station" id="station" class="form-control" >
                <option value="0">&lt;&lt; เลือก สน./สภ. &gt;&gt;</option>
                <?php
	           if($provincial > 0)
                {
                   $station = $row['station'];
                   //------------------------------------------
                    $res_st = $conn2->query("SELECT * FROM `wm_tb_police_station2` 
                                                WHERE provincial_code='$provincial_code'
                                                ORDER BY `station_code` ASC");
                    $selected = '';
                  while($row_st = $conn2->fetch_row($res_st)) 
                  {
                      $station_code = $row_st['station_code'];
                      $station_name = $row_st['station_name'];
                          if($station_code == $station) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      echo "<option value='$station_code' $selected> $station_name </option>\n";
                  }
                }	
            
                ?>                                                                     
              </select>
		<br>


            <label> วันที่ตรวจเยี่ยม</label>
            <p><input type="text" id="datepicker3" name="fr_cl_check_date" value= "<?= $row['fr_cl_check_date'] ?>" class="form-control" autocomplete="off" ></p>
        <script>
            function set_cal(ele)//function สร้างตัวเลือกปฎิทิน
            {
              $( ele ).datepicker({
                  onSelect:(date_text)=>
                  {
                    let arr=date_text.split("/");
                    let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])+543).toString();
                    $(ele).val(new_date);
                    $(ele).css("color","");
                  },
                  beforeShow:()=>{

                    if($(ele).val()!="")
                    {
                      let arr=$(ele).val().split("/");
                      let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])-543).toString();
                      $(ele).val(new_date);

                    }

                    $(ele).css("color","white");
                  },
                  onClose:()=>{

                      $(ele).css("color","");

                      if($(ele).val()!="")
                      {
                          let arr=$(ele).val().split("-");
                          if(parseInt(arr[2])<2500)
                          {
                              let new_date=arr[0]+"-"+arr[1]+"-"+(parseInt(arr[2])).toString();
                              $(ele).val(new_date);
                          }
                      }
                  },
                  dateFormat:"yy-mm-dd", //กำหนดรูปแบบวันที่เป็น วัน/เดือน/ปี
                  changeMonth:true,//กำหนดให้เลือกเดือนได้
                  changeYear:true,//กำหนดให้เลือกปีได้
                  showOtherMonths:true,//กำหนดให้แสดงวันของเดือนก่อนหน้าได้
              });
            }
          $(document).ready(function(){
            //เรียก function set_cal เมื่อเปิดหน้าเว็บ โดยส่ง object element ที่มี id เป็น datepicker เป็นพารามิเตอร์
            set_cal( $("#datepicker3") );
          })
          </script>
        <!-- สำหรับ วันที่ แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ -->
		
			<select id="fr_cl_dna" name="fr_cl_dna" value="<?= $row['fr_cl_dna'] ?>" class="form-select form-select-sm">
				<option value="" selected>เลือกสถานะ</option>
				<option value="เก็บ DNA แล้ว" <?php if($row['fr_cl_dna'] == 'เก็บ DNA แล้ว') echo 'selected'; ?>>เก็บ DNA แล้ว</option>
				<option value="ยังไม่ได้เก็บ DNA" <?php if($row['fr_cl_dna'] == 'ยังไม่ได้เก็บ DNA') echo 'selected'; ?>>ยังไม่ได้เก็บ DNA</option>
			</select>
		
			<label> เหตุไม่ได้เก็บ DNA </label>
			<input type = "text" name = "fr_cl_cause_no_keepdna" class="form-control" value= "<?= $row['fr_cl_cause_no_keepdna'] ?>" placeholder="ระบุเหตุ ที่ยังไม่ได้เก็บ DNA"  >
		
            <label>สถานะการติดตาม</label>
				<select id="fr_cl_tracking" name="fr_cl_tracking" class="form-select form-select-sm" aria-label=".form-select-sm example">
						<option value="" selected> เลือก</option>  <!-- ดึงข้อมูลมาจากตารางใหม่ -->
						<?php
							$res_tac = $pdo->query("SELECT * FROM wm_tb_freed_tracking order by tac_cl_aid ASC");
							while($row_tac = $res_tac->fetch(PDO::FETCH_ASSOC))
							{
								echo "<option value='{$row_tac['tac_cl_aid']}'>{$row_tac['tac_cl_status']}</option>";
							}
						?>
					</select>
        
			<label> หมายเหตุ </label>
			<input type = "text" name = "fr_cl_remark" class="form-control" value= "<?= $row['fr_cl_remark'] ?>" placeholder="ระบุเหตุ ที่ไม่พบตัว หรือข้อมูลเกี่ยวข้องอื่น ๆ" >
		
			<div class="mb-3">
				<label for="formFileMultiple" class="form-label">ภาพบุคคล</label>
				<input class="form-control" type="file" id="fr_cl_image" name="fr_cl_image" multiple value= "<?= $row['fr_cl_image'] ?>">
			</div>
		
			<div class="mb-3">
				<label for="formFileMultiple" class="form-label">ภาพบ้าน</label>
				<input class="form-control" type="file" id="fr_cl_home_image" name="fr_cl_home_image" multiple value= "<?= $row['fr_cl_home_image'] ?>">
			</div>
		<br>
  	<p>
    	<input type="submit" value="Update" class="btn btn-success" >
		<td> <a href="Show_All.php?pcid=<?= $pcid ?>&page=freed" class="btn btn-warning" >ยกเลิก </a></td>
 	</p>
	</form>
	</div>
	</div>
	</div>
<script>
// ฟังก์ชั่น เปลี่ยนภาค (บช.) เป็น ภ.จว. (บก.)
function do_region_change(current_value)
{
    current_value = (typeof current_value == 'undefined') ? 0 : current_value;
	var sel_region = document.getElementById("region");		
	var code_region = sel_region.options[sel_region.selectedIndex].value;

	$.get("../ajax/get_provincial.php?region=" + code_region, 
		  function(data, status){
				//alert("Data: " + data + "\nStatus: " + status);
				// jQuery style						
				$('#provincial').find('option').remove().end();
				var data = $.trim(data);
				if(data == '') {
					alert("ไม่พบข้อมูล บก. !");
					return;
				}

					//.append('<option value="whatever">text</option>').val('whatever');
				var datas = data.split(';');
				for(var i=0; i<datas.length; i++) {
					if(datas[i] == '') continue;
					datas[i] = datas[i].split(':');
					var code_region = datas[i][0];
					var name_region = datas[i][1];
					//$('#provincial').append('<option value="'+ code_region+'">' + name_region + '</option>');
                    if(current_value == code_region) {
                        $('#provincial').append('<option value="'+ code_region+'" selected>' + name_region + '</option>'); 
                    }
                    else {
                        $('#provincial').append('<option value="'+ code_region+'">' + name_region + '</option>');
                    }
				}
                // Trigger the change event on the amphur dropdown so the tambon dropdown will be populated     
                $('#provincial').trigger('change');
		});
}
    //เลือก สถานี จากจังหวัด
function do_provincial_change(current_value)
{
    current_value = (typeof current_value == 'undefined') ? 0 : current_value;
    
	var sel_provincial = document.getElementById("region");		
	var provincial_code = sel_provincial.options[sel_provincial.selectedIndex].value;
    
    var sel_provincial2 = document.getElementById("provincial");		
	var station_code = sel_provincial2.options[sel_provincial2.selectedIndex].value;

	$.get("../ajax/get_station.php?region=" + provincial_code + "&provincial=" + station_code, 
			  function(data, status){
					//alert("Data: " + data + "\nStatus: " + status);
					// jQuery style						
					$('#station').find('option').remove().end();
					var data = $.trim(data);
					if(data == '') {
						alert("ไม่พบข้อมูลสถานี !");
						return;
					}

						//.append('<option value="whatever">text</option>').val('whatever');
					var datas = data.split(';');
					for(var i=0; i<datas.length; i++) {
						if(datas[i] == '') continue;
						datas[i] = datas[i].split(':');
						var station_code = datas[i][0];
						var station_name = datas[i][1];
                        if(current_value == station_code) {
                            $('#station').append('<option value="'+ station_code+'" selected>' + station_name + '</option>'); 
                        }
                        else {
                            $('#station').append('<option value="'+ station_code+'">' + station_name + '</option>');   
                        }						
					}
			});
}
</script>
<script>
// ชื่อ >> value >> set selct auto (สคลิปตรวจสอบ Selected) จำเป็นต้องก็อปปี้ <script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js" type="text/javascript">
function auto_select(sid, value)
{
    $('#' + sid).val(value);
}

$(document).ready(function() {
<?php
    echo "auto_select('fr_cl_status', '{$row['fr_cl_status']}');\n";
    echo "auto_select('fr_cl_dna', '{$row['fr_cl_dna']}');\n";
    echo "auto_select('fr_cl_tracking', '{$row['fr_cl_tracking']}');\n";
?>
    setTimeout("do_region_change('<?= $row['provincial'] ?>')", 1000);
    setTimeout("do_provincial_change('<?= $row['station'] ?>')", 2000);
    });
</script>
    
</body>
</html>