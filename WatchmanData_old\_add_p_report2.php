<?php
include '../Condb.php';
include '../users.inc.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// สำหรับเลือก จังหวัด อำเภอ ตำบล /และ สถานีตำรวจ
include("../config.inc.php");
include("../classes/class.database.inc.php");
$conn2 = get_connection(); // connect to mySQLDB

$tumbon = isset($_GET['tumbon']) ? $_GET['tumbon'] : 0;
$unit = isset($_GET['unit']) ? $_GET['unit'] : 0;


/*$pcid = isset($_GET['pcid']) ? $_GET['pcid'] : '';

$sql = "SELECT
            po_cl_idcard
        FROM
            `wm_politician`
        WHERE
            po_cl_idcard='$pcid' ";
$result = mysqli_query($conn,$sql);
$row = mysqli_fetch_array($result);*/

?>


<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>เลือกตั้ง 66</title>
<script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/policeinnopolis/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 18px;
    }
input::placeholder {
  color: red;
}
.form-control::placeholder {
  color: sandybrown;
}
</style>
<!-- สำหรับ วันที่ แบบ พ.ศ. -->
<link rel="stylesheet" href="http://code.jquery.com/ui/1.9.1/themes/base/jquery-ui.css" />
<script src="http://code.jquery.com/jquery-1.8.2.js"></script>
<script src="/policeinnopolis/js/jquery-ui.js"></script>
</head>
    
<body>
	
	<div class="container">
	<div class="row">
	<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> เพิ่มข้อมูล </div>
	<form action="Save_p_report.php" method="POST" enctype="multipart/form-data" class="body">
	<span style="color: #1203F8">

        
<label for="tumbon">Tumbon:</label>
<select name="tumbon" id="tumbon">
  <option value="">-- Select Tumbon --</option>
  <?php
    // Fetch all the tumbons from the database and create options for the dropdown
    $query = "SELECT * FROM elect66_tumbon";
    $result = mysqli_query($conn, $query);
    while ($row = mysqli_fetch_assoc($result)) {
      echo '<option value="' . $row['tcode'] . '">' . $row['tname'] . '</option>';
    }
  ?>
</select>

<label for="unit">Unit:</label>
<select name="unit" id="unit">
  <option value="">-- Select Unit --</option>
</select>
        
        
        
        
        
     <!--   <label> เลือกตำบล </label><br>
        <select name="tumbon66" id="tumbon66" onChange="do_tumbon_change()" class="form-control" >
                <option value="0">&lt;&lt; เลือก &gt;&gt;</option>
                <?php
			  	$res_p = $conn2->query("SELECT * FROM `elect66_tumbon` ORDER BY `tcode` ASC");
			     $selected = '';
			     while($row_p = $conn2->fetch_row($res_p)) 
			     {
				  $t_code = $row_p['tcode'];
				  $t_name = $row_p['tname'];
				  // set default provionce to 64 >> sukhothai
				  if($t_code == $tumbon) {
					  $selected = 'selected';
				  }
				  else {
					  $selected = '';
				  }
				  //
				  echo "<option value='$t_code' $selected> $t_name </option>\n";
			     }
			  
			  ?>
              </select>
        
        <label> หน่วยเลือกตั้ง </label><br>
        <select name="unit66" id="unit66" onChange="do_unit_change()" class="form-control" >
        <option value="0">&lt;&lt; เลือก &gt;&gt;</option>
                <?php
                if($unit > 0)
                {
                    $res_a = $conn2->query("SELECT * FROM `elect66_unit` WHERE ucode='$unit' ORDER BY `ucode` ASC");
                    $selected = '';
                  while($row_a = $conn2->fetch_row($res_a)) 
                  {
                      $u_code = $row_a['ucode']; // 
                      $u_name = $row_a['uname'];
                      // set default provionce to 64 >> sukhothai
                      if($u_code == $unit) {
                          $selected = 'selected';
                      }
                      else {
                          $selected = '';
                      }
                      //
                      echo "<option value='$u_code' $selected> $u_name </option>\n";
                  }
                }	
                ?>
              </select>-->
		        
 <!--       <label>เลขประจำตัวประชาชน ผู้สมัคร ส.ส.</label>
		<input type = "text" name = "idcard_candidate" value="<?= $row['po_cl_idcard'] ?>" class="form-control" placeholder="เลขประจำตัวประชาชน ผู้สมัคร ส.ส." required readonly>
        	
        <b>กลุ่มผู้สนับสนุนของผู้สมัครรับเลือกตั้ง ส.ส.</b>
        <label>เลขบัตร ส.จ.</label>
		<input type = "text" name = "idcard_sor_jor" class="form-control" placeholder="ระบุเลขประจำตัวประชาชน ของ ส.จ.ที่สนับสนุน" >
        
        <label>ชื่อ-สกุล กลุ่ม ส.จ. (ตำแหน่ง)</label>
		<input type = "text" name = "sor_jor" class="form-control" placeholder="ระบุชื่อ-สกุล (และตำแหน่ง) ของ กลุ่ม ส.จ.ที่สนับสนุน" >
        
        <label>เลขบัตร กลุ่มกำนัน/ผู้ใหญ่บ้าน</label>
		<input type = "text" name = "idcard_village_headman" class="form-control" placeholder="ระบุเลขประจำตัวประชาชน ของ กลุ่มกำนัน/ผู้ใหญ่บ้าน ที่สนับสนุน" >
        
        <label>ชื่อ-สกุล กลุ่มกำนัน/ผู้ใหญ่บ้าน (ตำแหน่ง)</label>
		<input type = "text" name = "village_headman" class="form-control" placeholder="ระบุชื่อ-สกุล (และตำแหน่ง) ของ กลุ่มกำนัน/ผู้ใหญ่บ้านที่สนับสนุน" >
        
        <label>เลขบัตร กลุ่ม อบต.</label>
		<input type = "text" name = "idcard_SAO" class="form-control" placeholder="ระบุเลขประจำตัวประชาชน ของ กลุ่ม อบต. ที่สนับสนุน" >
        
        <label>ชื่อ-สกุล กลุ่ม อบต. (ตำแหน่ง)</label>
		<input type = "text" name = "SAO" class="form-control" placeholder="ระบุชื่อ-สกุล (และตำแหน่ง) ของ กลุ่ม อบต. ที่สนับสนุน" >
        
        <label>เลขบัตร ผู้มีบารมีทางสังคม </label>
		<input type = "text" name = "idcard_social_prestige" class="form-control" placeholder="ระบุเลขประจำตัวประชาชน ของ ผู้มีบารมีทางสังคม ที่สนับสนุน" >
        
        <label>ชื่อ-สกุล ผู้มีบารมีทางสังคม (ตำแหน่ง)</label>
		<input type = "text" name = "social_prestige" class="form-control" placeholder="ระบุชื่อ-สกุล (และตำแหน่ง) ของ ผู้มีบารมีทางสังคม ที่สนับสนุน" >
        
        <label>เลขบัตร ประธานกลุ่ม อสม./สตรี/ผู้สูงอายุ </label>
		<input type = "text" name = "idcard_group_chairman" class="form-control" placeholder="ระบุเลขประจำตัวประชาชน ของ ประธาน อสม./ประธานกลุ่มสตรี/ประธานกลุ่มผู้สูงอายุ ที่สนับสนุน" >
        
        <label>ชื่อ-สกุล ประธานกลุ่ม อสม./สตรี/ผู้สูงอายุ (ตำแหน่ง)</label>
		<input type = "text" name = "group_chairman" class="form-control" placeholder="ระบุชื่อ-สกุล (และตำแหน่ง) ของ ประธาน อสม./ประธานกลุ่มสตรี/ประธานกลุ่มผู้สูงอายุ ที่สนับสนุน" >
        
        <label>เลขบัตร กลุ่มสถาบันการศึกษา </label>
		<input type = "text" name = "idcard_academy" class="form-control" placeholder="ระบุเลขประจำตัวประชาชน ของ กลุ่มสถาบันการศึกษา ที่สนับสนุน" >
        
        <label>ชื่อ-สกุล กลุ่มสถาบันการศึกษา (ตำแหน่ง)</label>
		<input type = "text" name = "academy" class="form-control" placeholder="ระบุชื่อ-สกุล (และตำแหน่ง) ของ กลุ่มสถาบันการศึกษา ที่สนับสนุน" >
        
        <label>เลขบัตร ผู้นำศาสนา </label>
		<input type = "text" name = "idcard_religious_leader" class="form-control" placeholder="ระบุเลขประจำตัวประชาชน ของ ผู้นำศาสนา ที่สนับสนุน" >
        
        <label>ชื่อ-สกุล ผู้นำศาสนา (ตำแหน่ง)</label>
		<input type = "text" name = "religious_leader" class="form-control" placeholder="ระบุชื่อ-สกุล (และตำแหน่ง) ของ ผู้นำศาสนา ที่สนับสนุน" >
        -->
		<br>
		<p>
			<input type="submit" value="บันทึกข้อมูล" class="btn btn-success" >
			<td> <a href="/Election_66/Show_Election_index.php?rnd=<?= rand(); ?>&page=supporter" class="btn btn-warning" >ยกเลิก</a> </td>
		</p>
		<br>
		<br>
	</form>
	</div>
	</div>
	</div>
</body>
</html>

<script>

function do_tumbon_change()
{
	var sel = document.getElementById("tumbon66");		
	var code = sel.options[sel.selectedIndex].value;

	$.get("../ajax/get_tumbon.php?tumbon=" + code, 
		  function(data, status){
				//alert("Data: " + data + "\nStatus: " + status);
				// jQuery style						
				$('#tumbon').find('option').remove().end();
				var data = $.trim(data);
				if(data == '') {
					alert("ไม่พบข้อมูลตำบล !");
					return;
				}

					//.append('<option value="whatever">text</option>').val('whatever');
				var datas = data.split(';');
				for(var i=0; i<datas.length; i++) {
					if(datas[i] == '') continue;
					datas[i] = datas[i].split(':');
					var code = datas[i][0];
					var name = datas[i][1];
					$('#tumbon66').append('<option value="'+ code+'">' + name + '</option>');
				}
        // Trigger the change event on the amphur dropdown so the tambon dropdown will be populated     
                $('#tumbon').trigger('change');
		});
}

function do_unit_change()
{
	var sel = document.getElementById("tumbon66");		
	var code = sel.options[sel.selectedIndex].value;

	var sel2 = document.getElementById("unit66");		
	var code2 = sel2.options[sel2.selectedIndex].value;

	$.get("../ajax/get_unit_elect.php?tumbon=" + code + "&unit=" + code2, 
			  function(data, status){
					//alert("Data: " + data + "\nStatus: " + status);
					// jQuery style						
					$('#unit').find('option').remove().end();
					var data = $.trim(data);
					if(data == '') {
						alert("ไม่พบข้อมูลหน่วยเลือกตั้ง !");
						return;
					}

						//.append('<option value="whatever">text</option>').val('whatever');
					var datas = data.split(';');
					for(var i=0; i<datas.length; i++) {
						if(datas[i] == '') continue;
						datas[i] = datas[i].split(':');
						var code = datas[i][0];
						var name = datas[i][1];
						$('#unit').append('<option value="'+ code+'">' + name + '</option>');
					}
			});
}
