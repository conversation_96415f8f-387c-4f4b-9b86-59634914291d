<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';


// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);


?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>เพิ่มข้อมูล งาน ป.</title>
<script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/policeinnopolis/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 18px;
    }
input::placeholder {
  color: red;
}
.form-control::placeholder {
  color: sandybrown;
}
</style>
<!-- สำหรับ วันที่ แบบ พ.ศ. -->
<link rel="stylesheet" href="http://code.jquery.com/ui/1.9.1/themes/base/jquery-ui.css" />
<script src="http://code.jquery.com/jquery-1.8.2.js"></script>
<script src="/policeinnopolis/js/jquery-ui.js"></script>
</head>

<body>
	
	<div class="container">
	<div class="row">
	<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> ข้อมูลงาน ป.</div>
	<form action="Save_record_DATA.php" id="Save_record_DATA" name="Save_record_DATA" method="POST" enctype="multipart/form-data" class="body">
        
        <label hidden ="hidden">ลำดับ</label>
		<input type = "text" name = "Aid" class="form-control"  hidden ="hidden" >
        
		<!-- สถานี ภ.จว. ภาค ดึงจากหน้า user -->
		
		<label>ขนาดพื้นที่ (ตร.กม.)</label>
		<input type = "text" name = "Area_ST" id = "Area_ST" class="form-control" placeholder="ขนาดพื้นที่ (ตร.กม.)" >
		
		<label>จำนวนตำบล</label>
		<input type = "text" name = "Qty_TB" id = "Qty_TB" class="form-control" placeholder="จำนวนตำบล" >
		
		<label>จำนวนหมู่บ้าน</label>
		<input type = "text" name = "Qty_moo" id = "Qty_moo" class="form-control" placeholder="จำนวนหมู่บ้าน" >
		
		<label>จำนวนชุมชน</label>
		<input type = "text" name = "Qty_Commu" id = "Qty_Commu" class="form-control" placeholder="จำนวนชุมชน" >
		
		<label>จำนวนเทศบาล</label>
		<input type = "text" name = "Qty_Muni" id = "Qty_Muni" class="form-control" placeholder="จำนวนเทศบาล (แห่ง)" >
		
		<label>ประเภทเทศบาล</label>
		<input type = "text" name = "Type_Muni" id = "Type_Muni" class="form-control" placeholder="ระบุประเภทเทศบาล" >
		
		<hr>
		
		<label>จำนวนประชากร</label>
		<input type = "text" name = "Qty_pop" id = "Qty_pop" class="form-control" placeholder="จำนวนประชากร" >
		
		<label>ชาย</label>
		<input type = "text" name = "male" id = "male" class="form-control" placeholder="จำนวนเพศชาย" >
		
		<label>หญิง</label>
		<input type = "text" name = "female" id = "female" class="form-control" placeholder="จำนวนเพศหญิง" >
		
		<label>จำนวนประชากรแฝง</label>
		<input type = "text" name = "Qty_Hidden_pop" id = "Qty_Hidden_pop" class="form-control" placeholder="จำนวนประชากรแฝง" >
		
		<label>ประชากรแฝงชาย</label>
		<input type = "text" name = "Hidden_male" id = "Hidden_male" class="form-control" placeholder="ประชากรแฝง เพศชาย" >
		
		<label>ประชากรแฝงหญิง</label>
		<input type = "text" name = "Hidden_female" id = "Hidden_female" class="form-control" placeholder="ประชากรแฝง เพศหญิง" >
		
		<hr>
		
		<label>กำลังพล งานป.</label>
		<input type = "text" name = "Crime_Sup_Officer" id = "Crime_Sup_Officer" class="form-control" placeholder="จำนวนกำลังพล งาน ป." >
		
		<label>สัญญาบัตร</label>
		<input type = "text" name = "commission" id = "commission" class="form-control" placeholder="จำนวนสัญญาบัตร" >
		
		<label>ประทวน</label>
		<input type = "text" name = "Non_commiss" id = "Non_commiss" class="form-control" placeholder="จำนวนประทวน" >
		
		<hr>
		
		<label>จำนวนคดีเกิด</label>
		<input type = "text" name = "Qty_Case" id = "Qty_Case" class="form-control" placeholder="จำนวนคดีที่เกิดขึ้น" >
		
		<label>จำนวนจับ</label>
		<input type = "text" name = "Qty_Case_arrest" id = "Qty_Case_arrest" class="form-control" placeholder="จำนวนคดีที่จับกุมได้" >
		
		<label>กลุ่ม 1</label>
		<input type = "text" name = "G1_case" id = "G1_case" class="form-control" placeholder="จำนวน คดีกลุ่ม 1" >
		
		<label>จับกุมกลุ่ม 1</label>
		<input type = "text" name = "G1_arrest" id = "G1_arrest" class="form-control" placeholder="จำนวนจับกุม คดีกลุ่ม 1" >
		
		<label>กลุ่ม 2</label>
		<input type = "text" name = "G2_case" id = "G2_case" class="form-control" placeholder="จำนวน คดีกลุ่ม 2" >
		
		<label>จับกุมกลุ่ม 2</label>
		<input type = "text" name = "G2_arrest" id = "G2_arrest" class="form-control" placeholder="จำนวนจับกุม คดีกลุ่ม 2" >
		
		<label>กลุ่ม 3</label>
		<input type = "text" name = "G3_case" id = "G3_case" class="form-control" placeholder="จำนวน คดีกลุ่ม 3" >
		
		<label>จับกุมกลุ่ม 3</label>
		<input type = "text" name = "G3_arrest" id = "G3_arrest" class="form-control" placeholder="จำนวนจับกุม คดีกลุ่ม 3" >
		
		<label>จับกุมกลุ่ม 5 (ปืน)</label>
		<input type = "text" name = "G5_arrest" id = "G5_arrest" class="form-control" placeholder="จำนวนจับกุม คดีกลุ่ม 5 (คดีปืน)" >
		
		<label>จำนวนผู้ต้องหา (ปืน)</label>
		<input type = "text" name = "G5_pers" id = "G5_pers" class="form-control" placeholder="จำนวนผู้ต้องหา (คดีปืน)" >
		
		<label>จำนวนคดียาเสพติด (ข้อหาร้ายแรง)</label>
		<input type = "text" name = "Drug_serious" id = "Drug_serious" class="form-control" placeholder="จำนวนคดียาเสพติด (ข้อหาร้ายแรง)" >
		
		<label>จำนวนผู้ต้องหา (ข้อหาร้ายแรง)</label>
		<input type = "text" name = "Drug_serious_pers" id = "Drug_serious_pers" class="form-control" placeholder="จำนวนผู้ต้องหา (ข้อหาร้ายแรง)" >
		
		<label>จำนวนคดียาเสพติด (ข้อหาเสพ)</label>
		<input type = "text" name = "Take_drugs" id = "Take_drugs" class="form-control" placeholder="จำนวนคดียาเสพติด (ข้อหาเสพ)" >
		
		<label>จำนวนผู้ต้องหา (ข้อหาเสพ)</label>
		<input type = "text" name = "Take_drugs_pers" id = "Take_drugs_pers" class="form-control" placeholder="จำนวนผู้ต้องหา (ข้อหาเสพ)" >
		
		<hr>
		
		<label>จำนวนเขตตรวจทั้งหมด</label>
		<input type = "text" name = "Ins_area" id = "Ins_area" class="form-control" placeholder="จำนวนเขตตรวจทั้งหมด" >
		
		<label>จำนวนเขตตรวจ ในชุมชน</label>
		<input type = "text" name = "In_commu" id = "In_commu" class="form-control" placeholder="จำนวนเขตตรวจ ในชุมชน" >
		
		<label>จำนวนเขตตรวจ นอกชุมชน</label>
		<input type = "text" name = "Out_commu" id = "Out_commu" class="form-control" placeholder="จำนวนเขตตรวจ นอกชุมชน" >
		
		<label>จำนวนประเภทของสายตรวจ</label>
		<input type = "text" name = "Patrol_system" id = "Patrol_system" class="form-control" placeholder="แบ่งสายตรวจเป็นกี่ประเภท" >
		
		<label>จำนวนสายของ สายตรวจ จยย.</label>
		<input type = "text" name = "Moto_patrol" id = "Moto_patrol" class="form-control" placeholder="จำนวนสายของ สายตรวจ จยย. (สาย)" >
		
		<label>จำนวนชุดของ สายตรวจ จยย.</label>
		<input type = "text" name = "Moto_patrol_set" id = "Moto_patrol_set" class="form-control" placeholder="จำนวนชุดของ สายตรวจ จยย. (ชุด)" >
		
		<label>จำนวน สายตรวจ จยย. ในชุด</label>
		<input type = "text" name = "Moto_patrol_Qty" id = "Moto_patrol_Qty" class="form-control" placeholder="จำนวนสายตรวจ จยย. ต่อชุด (นาย)" >
		
		<label>จำนวนสายของ สายตรวจรถยนต์</label>
		<input type = "text" name = "Car_patrol" id = "Car_patrol" class="form-control" placeholder="จำนวนสายของ สายตรวจรถยนต์ (สาย)" >
		
		<label>จำนวนชุดของ สายตรวจรถยนต์</label>
		<input type = "text" name = "Car_patrol_set" id = "Car_patrol_set" class="form-control" placeholder="จำนวนชุดของ สายตรวจรถยนต์ (ชุด)" >
		
		<label>จำนวน สายตรวจรถยนต์ ในชุด</label>
		<input type = "text" name = "Car_patrol_Qty" id = "Car_patrol_Qty" class="form-control" placeholder="จำนวน สายตรวจรถยนต์ ต่อชุด (นาย)" >
		
		<label>จำนวนสายของ สายตรวจตำบล</label>
		<input type = "text" name = "Sub_patrol" id = "Sub_patrol" class="form-control" placeholder="จำนวนสายของ สายตรวจตำบล (สาย)" >
		
		<label>จำนวนชุดของ สายตรวจตำบล</label>
		<input type = "text" name = "Sub_patrol_set" id = "Sub_patrol_set" class="form-control" placeholder="จำนวนชุดของ สายตรวจตำบล (ชุด)" >
		
		<label>จำนวน สายตรวจตำบล ต่อชุด</label>
		<input type = "text" name = "Sub_patrol_Qty" id = "Sub_patrol_Qty" class="form-control" placeholder="จำนวน สายตรวจตำบล ต่อชุด (นาย)" >
		
		<label>จำนวนสายตรวจอื่นๆ</label>
		<input type = "text" name = "Oth_patrol" id = "Oth_patrol" class="form-control" placeholder="จำนวนสายตรวจอื่นๆ" >
		
		<hr>
		
		<label>ห้องปฏิบัติการสายตรวจ</label>
		<select id="Patrol_room" name="Patrol_room" class="form-select form-select-sm" placeholder="ห้องปฏิบัติการสายตรวจ" >
			<option value="" selected> </option>
			<option value="1">ดำเนินการ</option>
            <option value="2">ยังไม่ดำเนินการ</option>
		</select>
		
		<label class="body"><input name="Map" id="Map" type="checkbox" value="1" />  มีแผนที่ภูมิศาสตร์แล้ว</label><br>

        <label class="body"><input name="Crimes5G" id="Crimes5G" type="checkbox" value="1" />  มีสถิติคดีอาญา 5 กลุ่มแล้ว</label><br>

        <label class="body"><input name="Crime_Watch" id="Crime_Watch" type="checkbox" value="1" />  มีนาฬิกาอาชญากรรมแล้ว</label><br><br>
		
		<label>รายละเอียดที่เป็นคดี</label>
		<input type = "text" name = "Crime_case" id = "Crime_case" class="form-control" placeholder="รายละเอียดที่เป็นคดี" >
		
		<label>รายละเอียดที่ไม่เป็นคดี</label>
		<input type = "text" name = "Crime_not_case" id = "Crime_not_case" class="form-control" placeholder="รายละเอียดที่ไม่เป็นคดี" >
		
		<label>แบบ ป.1-1 ถึง ป.1-7</label>
		<input type = "text" name = "P1_7" id = "P1_7" class="form-control" placeholder="แบบ ป.1-1 ถึง ป.1-7" >
		
		<label class="body"><input name="Crime_Map" id="Crime_Map" type="checkbox" value="1" />แผนที่อาชญากรรม</label><br>

        <label class="body"><input name="Patrol_Map" id="Patrol_Map" type="checkbox" value="1" />แผนที่เขตตรวจ</label><br><br>
		
		<label>จำนวนจุดตรวจ (ตู้แดงหลัก)</label>
		<input type = "text" name = "Main_RedBox" id = "Main_RedBox" class="form-control" placeholder="จำนวนจุดตรวจ (ตู้แดงหลัก)" >
		
		<label>จำนวนจุดตรวจ (จุดรอง)</label>
		<input type = "text" name = "Sec_RedBox" id = "Sec_RedBox" class="form-control" placeholder="จำนวนจุดตรวจ (จุดรอง)" >
		
		<label>จำนวนจุดเสี่ยง/จุดล่อแหลม</label>
		<input type = "text" name = "Risk_point" id = "Risk_point" class="form-control" placeholder="จำนวนจุดเสี่ยง/จุดล่อแหลม" >
		
		<label>จำนวนจุดก้าวสกัดจับ</label>
		<input type = "text" name = "Intercep_point" id = "Intercep_point" class="form-control" placeholder="จำนวนจุดก้าวสกัดจับ" >
		
		<label>จำนวนตู้ / หน่วยบริการประชาชน</label>
		<input type = "text" name = "PoliceBox" id = "PoliceBox" class="form-control" placeholder="จำนวนตู้ / หน่วยบริการประชาชน" >
		
		<label>จำนวน จุด ว.10</label>
		<input type = "text" name = "Waiting_point" id = "Waiting_point" class="form-control" placeholder="จำนวน จุด ว.10" >
		
		<hr>
		
		<label>ห้องสื่อสาร</label>
		<select id="Commu_room" name="Commu_room" class="form-select form-select-sm" placeholder="ห้องสื่อสาร" >
			<option value="" selected> </option>
			<option value="1">ดำเนินการ</option>
            <option value="2">ยังไม่ดำเนินการ</option>
		</select>
		
		<label>จัดกำลังพล ทั้งหมด</label>
		<input type = "text" name = "Qty_com_room" id = "Qty_com_room" class="form-control" placeholder="จัดกำลังพล ทั้งหมด" >
		
		<label>จำนวนผลัดใน 1 วัน</label>
		<input type = "text" name = "Commu_part" id = "Commu_part" class="form-control" placeholder="จำนวนผลัดใน 1 วัน" >
		
		<label>จำนวนชั่วโมงต่อผลัด</label>
		<input type = "text" name = "Commu_pers" id = "Commu_pers" class="form-control" placeholder="จำนวนชั่วโมงต่อผลัด" ><br>
		
		<label class="body"><input name="Com_Map" id="Com_Map" type="checkbox" value="1" />  แผนที่ภูมิศาสตร์</label><br>

        <label class="body"><input name="Com_Patrol_Map" id="Com_Patrol_Map" type="checkbox" value="1" />  แผนที่เขตตรวจ</label><br>

        <label class="body"><input name="Com_Crime_Map" id="Com_Crime_Map" type="checkbox" value="1" />  แผนที่อาชญากรรม</label><br>
		
		<label class="body"><input name="Network_diagram" id="Network_diagram" type="checkbox" value="1" />  ผังข่ายการติดต่อสื่อสาร</label><br>

        <label class="body"><input name="Calendar_plan" id="Calendar_plan" type="checkbox" value="1" />  ปฏิทินซ้อมแผนเผชิญเหตุ</label><br>

        <label class="body"><input name="Inspect_plan" id="Inspect_plan" type="checkbox" value="1" />  แผนการตรวจ</label><br>
		
		<label class="body"><input name="Important_phone" id="Important_phone" type="checkbox" value="1" />  หมายเลขโทรศัพท์ฯ (บุคคลสำคัญ/สถานที่สำคัญ)</label><br>

        <label class="body"><input name="Commu_book" id="Commu_book" type="checkbox" value="1" />  สมุดบันทึกพนักงานวิทยุ (ป.12)</label><br>

        <label class="body"><input name="Call_book" id="Call_book" type="checkbox" value="1" />  สมุดรับแจ้งเหตุทางโทรศัพท์</label><br><br>
		
		<label>ปัญหาและอุปสรรคในการปฏิบัติงาน ป.</label>
		<input type = "text" name = "problem" id = "problem" class="form-control" placeholder="ปัญหาและอุปสรรคในการปฏิบัติงาน ป." >
		
		<!-- ผู้บันทึก และวันเวลาที่บันทึก -->
	
		<p>
		<br>
			<input type="submit" value="บันทึกข้อมูล" class="btn btn-success" >
			<td> <a href="/WatchmanData/Crime_Prevention_and_Suppression.php" class="btn btn-warning" >ยกเลิก</a> </td>
		</p>
		<br>
		<br>
	</form>
	</div>
	</div>
	</div>
</body>
</html>
