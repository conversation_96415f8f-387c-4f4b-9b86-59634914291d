<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';
include '../Head_image.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

$id = isset($_GET['aid_confidential_gun']);
//get month from select or current month
$month = isset($_GET['month']) ? $_GET['month'] : date("m");
$year = isset($_GET['year']) ? $_GET['year'] : date("Y");

$m1 = '';
$m2 = '';
$m3 = '';
$m4 = '';
$m5 = '';
$m6 = '';
$m7 = '';
$m8 = '';
$m9 = '';
$m10 = '';
$m11 = '';
$m12 = '';
switch($month)
{
    case "01": $m1 = "selected"; break;
    case "02": $m2 = "selected"; break;
    case "03": $m3 = "selected"; break;
    case "04": $m4 = "selected"; break;
    case "05": $m5 = "selected"; break;
    case "06": $m6 = "selected"; break;
    case "07": $m7 = "selected"; break;
    case "08": $m8 = "selected"; break;
    case "09": $m9 = "selected"; break;
    case "10": $m10 = "selected"; break;
    case "11": $m11 = "selected"; break;
    case "12": $m12 = "selected"; break;
}
$current_month = $global_thai_month[0+$month];
$y2 = $year+543;

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<base target="_top">
<title>รายงานการขับเคลื่อนงานสืบสวน</title>
<link rel="stylesheet" href="/bootstrap/css/bootstrap-grid.css">
<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<style type="text/css">
body{
    font-size:12px; 
}
.textAlignVer{
    display:block;
    filter: flipv fliph;
    -webkit-transform: rotate(-90deg); 
    -moz-transform: rotate(-90deg); 
    transform: rotate(-90deg); 
    position:relative;
    width:20px;
    white-space:nowrap;
    font-size:16px;
    margin-bottom:10px;
}
    .box !im{
        border: black;
        border-top-color: black !important;
        border-bottom-color: black !important;
        border-left-color: black !important;
        border-right-color: black !important;
        
    }
</style>
    
</head>
<!--   <p><img src="../Image/Head2.jpg" width="100%" height="" alt=""/></p> -->
<body>
<div class="container-fluid" align="left">
<div class=" h3 text-center  alert alert-success mb-4 mt-4 " role="alert" >กิจกรรม 17 การสำรวจอาวุธปืนของกลางประจำเดือน <?= $name_station ?> </div>

<div align="left">
<div>
    <table width="80%" border="0" cellspacing="1" cellpadding="1">
      <tbody>
        <tr>
          <td width="10%">&nbsp;<a href="/WM/index.php?rnd=<?= rand(); ?>&page=report" class="btn btn-warning btn-lg mb-4" >ย้อนกลับ</a></td>
          <td width="12%">&nbsp;<a href="/WM/Add_confidential_gun.php" class="btn btn-success btn-lg mb-4" >เพิ่มข้อมูล</a></td>
          <td width="15%"><label>เลือกปี </label>
            <select id="mi_cl_year" name="mi_cl_year" class="form-select col-8 d-lg-inline" style="background-color: khaki; font-size: 16px" onChange="dates_change()">
              <option value="">เลือก</option>
              <?php
                $cur_y = date("Y") + 3;
                for($y=2020; $y<$cur_y; $y++) {
                    $sel = ($y == $year) ? "selected" : '';
                    echo "<option value='$y' $sel> ". ($y + 543) ." </option>";
                }
            ?>
          </select></td>
            <td width="2%">&nbsp;</td>
          <td width="21%"><label>เดือน</label>
            <select id="mi_cl_month" name="mi_cl_month" class="form-select col-3 d-lg-inline" style="background-color: khaki; font-size: 16px" onChange="dates_change()">
              <option value="">เลือก</option>
              <option <?= $m1 ?> value="01">มกราคม</option>
              <option <?= $m2 ?> value="02">กุมภาพันธ์</option>
              <option <?= $m3 ?> value="03">มีนาคม</option>
              <option <?= $m4 ?> value="04">เมษายน</option>
              <option <?= $m5 ?> value="05">พฤษภาคม</option>
              <option <?= $m6 ?> value="06">มิถุนายน</option>
              <option <?= $m7 ?> value="07">กรกฎาคม</option>
              <option <?= $m8 ?> value="08">สิงหาคม</option>
              <option <?= $m9 ?> value="09">กันยายน</option>
              <option <?= $m10 ?> value="10">ตุลาคม</option>
              <option <?= $m11 ?> value="11">พฤศจิกายน</option>
              <option <?= $m12 ?> value="12">ธันวาคม</option>
          </select></td>
          <td width="32%">&nbsp;</td>
          <td width="8%">&nbsp;</td>
        </tr>
      </tbody>
    </table>
</div><br>

<h3 align="center">ตารางบัญชีอาวุธปืนของกลาง แยกรายเดือน</h3>
<h3 align="center">เดือน&nbsp; <?= $current_month ?> &nbsp;<?= $y2 ?> &nbsp;</h3>
<div class="table-responsive">
<table height="171" border="1" cellpadding="1" cellspacing="1" class="table-bordered table-hover mb-4 mt-4">
  <tbody>
    <tr>
      <td rowspan="4" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">ลำดับ</td>
      <td rowspan="4" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">สถานี</td>
      <td rowspan="4" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">(1) จำนวนอาวุธปืนของกลาง ที่อยู่ในบัญชีของสถานี (กระบอก)</td>
      <td height="46" colspan="13" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">ปืนของกลางที่มีอัตลักษณ์ (ปืนที่ผลิตโดยโรงงาน มีแบรนด์ มีคุณภาพการผลิต)</td>
      <td colspan="9" bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">ปืนของกลางที่ไม่มีมีอัตลักษณ์ (ปืนที่ไม่ได้ผลิตโดยโรงงาน ไม่มีแบรนด์ ไม่มีคุณภาพการผลิต หรือปืนไทยประดิษฐ์ เป็นต้น)</td>
      </tr>
    <tr>
      <td rowspan="3" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">(2) จำนวนปืน ที่มีอัตลักษณ์ (กระบอก)</td>
      <td rowspan="3" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">(3) ศาลสั่งริบ คดีถึงที่สุด (กระบอก)</td>
      <td rowspan="3" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">(4) ศาลสั่งคืน คดีถึงที่สุด (กระบอก)</td>
      <td rowspan="3" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">(5) คดียังไม่ถึงที่สุด (กระบอก)</td>
      <td height="46" colspan="2" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">กรณีไม่ปรากฎว่า ผู้ใดเป็นผู้กระทำความผิด หรือคดีที่รู้ตัวแต่ยังจับตัวไม่ได้</td>
      <td colspan="2" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">(8) ส่งกองสรรพวุธ (กระบอก)</td>
      <td rowspan="3" bgcolor="yellow" style="color: #00000; text-align: center; border-color: navy">(9) ร้อยละ การดำเนินการ ส่งกองสรรพวุธ หลังศาลสั่งริบ</td>
      <td rowspan="3" bgcolor="#F190EF" style="color: #00000; text-align: center; border-color: navy">คงเหลือ ปืนรอส่ง สรรพาวุธ (กระบอก)</td>
      <td rowspan="3" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">(10) ประสาน นายอำเภอ เพิกถอน ทะเบียน (กระบอก)</td>
      <td rowspan="3" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">(11) นายอำเภอ เพิกถอน ทะเบียน (กระบอก)</td>
      <td rowspan="3" bgcolor="yellow" style="color: #00000; text-align: center; border-color: navy">(12) ร้อยละ การเพิกถอน ทะเบียน หลังประสาน นายอำเภอ</td>
      <td rowspan="3" bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">(13) จำนวนปืน ไม่มีอัตลักษณ์ (กระบอก)</td>
      <td rowspan="3" bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">(14) ศาลสั่งริบ คดีถึงที่สุด (กระบอก)</td>
      <td rowspan="3" bgcolor="#F190EF" style="color: #00000; text-align: center; border-color: navy">(21) ศาลสั่งคืน คดีถึงที่สุด (กระบอก)</td>
      <td rowspan="3" bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">(15) คดียังไม่ถึงที่สุด (กระบอก)</td>
      <td colspan="2" rowspan="2" bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">กรณีไม่ปรากฎว่า ผู้ใดเป็นผู้กระทำความผิด หรือคดีที่รู้ตัวแต่ยังจับตัวไม่ได้</td>
      <td rowspan="3" bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">(18) ทำลายแล้ว (กระบอก)</td>
      <td rowspan="3" bgcolor="yellow" style="color: #00000; text-align: center; border-color: navy">(19) ร้อยละ การจำหน่าย อาวุธปืน ออกจากคลัง</td>
      <td rowspan="3" bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">(20) คงเหลือ รอทำลาย (กระบอก)</td>
      </tr>
    <tr>
      <td rowspan="2" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">(6) ขาดอายุความ คดีอาญา (กระบอก)</td>
      <td rowspan="2" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">(7) ยังอยู่ในอายุความ คดีอาญา (กระบอก)</td>
      <td rowspan="2" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">(8.1) มีทะเบียน</td>
      <td rowspan="2" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">(8.2) ไม่มีทะเบีน</td>
      </tr>
    <tr>
      <td bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">(16) ขาดอายุความ คดีอาญา (กระบอก)</td>
	  <td height="58" bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">(17) ยังอยู่ในอายุความ คดีอาญา (กระบอก)</td>
	  </tr>
	  
<?php  
  
$sql = "SELECT " .
            "(SELECT COUNT(*) FROM `wm_tb_gun_confidential` WHERE station='$station' AND `confidential_gun` AND `status` IN ('1','2','4')) AS T1, ".   //จำนวนอาวุธปืนของกลางที่อยู่ในบัญชีของสถานี(กระบอก)
            "(SELECT COUNT(*) FROM `wm_tb_gun_confidential` WHERE station='$station' AND `unique_guns` AND `status` IN ('1','2','4') AND MONTH(record_date)=:month AND YEAR(record_date)=:year) AS T2, " .  //จำนวนปืนของกลางที่มีอัตลักษณ์
            "(SELECT COUNT(*) FROM `wm_tb_gun_confidential` WHERE station='$station' AND `unique_guns`=1 AND `status`IN('1','2','4') AND MONTH(record_date)=:month AND YEAR(record_date)=:year) AS T3, " .  //ศาลสั่งริบ คดีถึงที่สุด
            "(SELECT COUNT(*) FROM `wm_tb_gun_confidential` WHERE station='$station' AND `unique_guns`=2 AND `status` IN('1','2','4') AND MONTH(record_date)=:month AND YEAR(record_date)=:year) AS T4, " .  //ศาลสั่งคืน คดีถึงที่สุด 
            "(SELECT COUNT(*) FROM `wm_tb_gun_confidential` WHERE station='$station' AND `unique_guns`=3 AND `status` IN('1','2','4') AND MONTH(record_date)=:month AND YEAR(record_date)=:year) AS T5, " .  //คดียังไม่ถึงที่สุด
            "(SELECT COUNT(*) FROM `wm_tb_gun_confidential` WHERE station='$station' AND `unique_guns_unknow`=1 AND `status` IN('1','2','4') AND MONTH(record_date)=:month AND YEAR(record_date)=:year) AS T6, " .  //ขาดอายุความคดีอาญา
            "(SELECT COUNT(*) FROM `wm_tb_gun_confidential` WHERE station='$station' AND `unique_guns_unknow`=2 AND MONTH(record_date)=:month AND YEAR(record_date)=:year) AS T7, ".   //ยังอยู่ในอายุความคดีอาญา
            "(SELECT COUNT(*) FROM `wm_tb_gun_confidential` WHERE station='$station' AND `sanpawut`=1 AND MONTH(record_date)=:month AND YEAR(record_date)=:year) AS T8, " .  //ส่งกองสรรพวุธ มีทะเบียน
            "(SELECT COUNT(*) FROM `wm_tb_gun_confidential` WHERE station='$station' AND `sanpawut`=2 AND MONTH(record_date)=:month AND YEAR(record_date)=:year) AS T8_2, " .  //ส่งกองสรรพวุธ ไม่มีทะเบียน
            "(SELECT COUNT(*) FROM `wm_tb_gun_confidential` WHERE station='$station' AND `DistrictChief_co`=1 AND MONTH(record_date)=:month AND YEAR(record_date)=:year) AS T10, " .  //ประสานนายอำเภอ เพิกถอนทะเบียน (กระบอก)
            "(SELECT COUNT(*) FROM `wm_tb_gun_confidential` WHERE station='$station' AND `DistrictChief_revoke`=1 AND MONTH(record_date)=:month AND YEAR(record_date)=:year) AS T11, " .  //นายอำเภอ เพิกถอนทะเบียน (กระบอก)
            "(SELECT COUNT(*) FROM `wm_tb_gun_confidential` WHERE station='$station' AND `no_identify` AND `status`IN ('1','2','4') AND MONTH(record_date)=:month AND YEAR(record_date)=:year) AS T13, " .  //อาวุธปืนไม่มีอัตลักษณ์
            "(SELECT COUNT(*) FROM `wm_tb_gun_confidential` WHERE station='$station' AND `no_identify`=1 AND `status`IN ('1','2','4') AND MONTH(record_date)=:month AND YEAR(record_date)=:year) AS T14, " .  //ศาลสั่งริบ คดีถึงที่สุด
            "(SELECT COUNT(*) FROM `wm_tb_gun_confidential` WHERE station='$station' AND `no_identify`=3 AND `status`IN ('1','2','4') AND MONTH(record_date)=:month AND YEAR(record_date)=:year) AS T21, " .  //ศาลสั่งคืน คดีถึงที่สุด
            "(SELECT COUNT(*) FROM `wm_tb_gun_confidential` WHERE station='$station' AND `no_identify`=2 AND MONTH(record_date)=:month AND YEAR(record_date)=:year) AS T15, " .  //คดียังไม่ถึงที่สุด
            "(SELECT COUNT(*) FROM `wm_tb_gun_confidential` WHERE station='$station' AND `no_identify_unknow`=1 AND `status` IN('1','2','4') AND MONTH(record_date)=:month AND YEAR(record_date)=:year) AS T16, " .  //ขาดอายุความคดีอาญา
            "(SELECT COUNT(*) FROM `wm_tb_gun_confidential` WHERE station='$station' AND `no_identify_unknow`=2 AND MONTH(record_date)=:month AND YEAR(record_date)=:year) AS T17, " .  //ยังอยู่ในอายุความคดีอาญา
            "(SELECT COUNT(*) FROM `wm_tb_gun_confidential` WHERE station='$station' AND `destroy_unknow`=1 AND MONTH(record_date)=:month AND YEAR(record_date)=:year) AS T18 " .  //ทำลายแล้ว (กระบอก)
            "";
                      
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':month', $month);
    $stmt->bindParam(':year', $year);
try{
    $stmt->execute();
}catch (PDOException $e) {
    echo '$sql Failed: '. $e->getMessage();
}

// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no = 1;
while($row = $stmt->fetch(PDO::FETCH_ASSOC))	//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
{
//ฟังก์ชั่น วันที่ ดึงจาก condb
//$strDate = DateThai( $row_mi["date_record"] );   
    
//ร้อยละส่งกองสรรพวุธ (9) = (8)/[ (3)+(6) ] * 100
if($row['T8'] > 0) {   
    $persen_san = ($row['T8']+$row['T8_2'])/($row['T3']+$row['T6'])*100;
}
else {
    $persen_san = 0;
    }                 
    
// คงเหลือ ปืนรอส่งสรรพาวุธ
$supawut_at_st1 = $row["T3"]-($row["T8"]+$row["T8_2"]);
    
//ร้อยละการเพิกถอนทะเบียนหลังประสานนายอาเภอ     (12) = (11)/(10) * 100               
if($row['T11'] > 0) {   
    $persen_revoke = $row['T11']/$row['T10']*100;
}
else {
    $persen_revoke = 0;
    }

//ร้อยละการจาหน่ายอาวุธปืนออกจากคลัง (19) = (18)/[ (14)+(16) ] * 100 
if($row['T18'] > 0) {   
$persen_clear = $row['T18']/($row['T14']+$row['T16'])*100;
}
else {
    $persen_clear = 0;
    }
?>	

    <tr>
	  <td style="font-size: 24px"><?= $no ?> </td> 
      <td nowrap>&nbsp;<?= $name_station ?> &nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T1"] > 0) ? $row["T1"]  : "")?>&nbsp;</td> <!-- SELECT COUNT(*) FROM `wm_tb_gun_confidential` WHERE `confidential_gun` AND status IN ('1','2','4'); จำนวนอาวุธปืนของกลางที่อยู่ในบัญชีของสถานีทั้ง 2 แบบ -->
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T2"] > 0) ? $row["T2"]  : "")?>&nbsp;</td> <!-- ยอดมาจาก Count(*) From ...WHERE confidential_gun='1' AND status IN ('1','2','4') จำนวนปืนของกลางที่มีอัตลักษณ์-->
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T3"] > 0) ? $row["T3"]  : "")?>&nbsp;</td> <!-- ยอดมาจาก qty_court_forfeiture='1' ศาลสั่งริบ คดีถึงที่สุด -->
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T4"] > 0) ? $row["T4"]  : "")?>&nbsp;</td> <!-- ยอดมาจาก qty_court_forfeiture='2' ศาลสั่งคืน คดีถึงที่สุด -->
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T5"] > 0) ? $row["T5"]  : "")?>&nbsp;</td> <!-- ยอดมาจาก qty_court_forfeiture='3' คดียังไม่ถึงที่สุด -->
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T6"] > 0) ? $row["T6"]  : "")?>&nbsp;</td> <!-- ยอดมาจาก  out_limit='4' ขาดอายุความคดีอาญา -->
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T7"] > 0) ? $row["T7"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T8"] > 0) ? $row["T8"]  : "")?>&nbsp;</td> 
      <!-- ยอดมาจาก  out_limit='5' ยังอยู่ในอายุความคดีอาญา ส่งสรรพาวุธ มีทะเบียน-->
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T8_2"] > 0) ? $row["T8_2"]  : "")?>&nbsp;</td> <!-- ยอดมาจาก sanpawut ส่งกองสรรพวุธ ส่งสรรพาวุธ ไม่มีทะเบียน-->
      <td style="font-size: 24px; background-color: yellow">&nbsp;<?= $persen_san ?>&nbsp;%</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($supawut_at_st1 > 0) ? $supawut_at_st1 : "")?>&nbsp;</td> 
      <!-- ยอดมาจาก สำหรับเข้าช่อง (9) = (8)/[ (3)+(6) ] * 100  -->
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T10"] > 0) ? $row["T10"]  : "")?>&nbsp;</td> <!-- ยอดมาจาก DistrictChief_co ประสานนายอำเภอ เพิกถอนทะเบียน (กระบอก) -->
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T11"] > 0) ? $row["T11"]  : "")?>&nbsp;</td> <!-- ยอดมาจาก DistrictChief_revoke นายอำเภอ เพิกถอนทะเบียน (กระบอก) -->
      <td style="font-size: 24px; background-color: yellow">&nbsp;<?= $persen_revoke ?>&nbsp;%</td> <!-- สำหรับเข้าช่อง (12) = (11)/(10) * 100-->
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T13"] > 0) ? $row["T13"]  : "")?>&nbsp;</td> <!-- ยอดมาจาก Count(*) From ...WHERE confidential_gun='2' AND status IN ('1','2','4') อาวุธปืนไม่มีอัตลักษณ์ -->
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T14"] > 0) ? $row["T14"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T21"] > 0) ? $row["T21"]  : "")?>&nbsp;</td> 
      <!-- สำหรับเข้าช่อง (14) qty_court_forfeiture_unknow ='1' ศาลสั่งริบ คดีถึงที่สุด -->
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T15"] > 0) ? $row["T15"]  : "")?>&nbsp;</td> <!-- สำหรับเข้าช่อง (15)  qty_court_forfeiture_unknow ='2' คดียังไม่ถึงที่สุด -->
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T16"] > 0) ? $row["T16"]  : "")?>&nbsp;</td>  <!-- สำหรับเข้าช่อง (16) out_limit_unknow='3' ขาดอายุความคดีอาญา -->
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T17"] > 0) ? $row["T17"]  : "")?>&nbsp;</td> <!-- สำหรับเข้าช่อง (17) out_limit_unknow='4' ยังอยู่ในอายุความคดีอาญา -->
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T18"] > 0) ? $row["T18"]  : "")?>&nbsp;</td> <!-- สำหรับเข้าช่อง (18) destroy_unknow ทำลายแล้ว (กระบอก) -->
      <td style="font-size: 24px; background-color: yellow">&nbsp;<?= $persen_clear ?> &nbsp;%&nbsp;</td> <!-- (19) = (18)/[ (14)+(16) ] * 100 -->
      <td style="font-size: 24px"><span style="font-size: 24px"><?php echo (($row["T13"] > 0) ? $row["T13"]  : "")?> </span></td> <!-- สำหรับเข้าช่อง (20) wait_destroy_unknow รอทำลาย (กระบอก) -->
    </tr>
  </tbody>
<?php
        
	$no++;
}//while
?>
</table>
</div>
    
<script>
function Del(url)
{
	if(confirm("คุณจะลบข้อมูล หรือ ไม่ ?"))
	{
		window.location = url;		
	}
}
function dates_change()  // คำสั่งเปลี่ยนเดือน ปี
{
    var ms = $("#mi_cl_month option:selected").val();
    var ys = $("#mi_cl_year option:selected").val();
    window.location = "/Activity/Show_Activity17.php?&month=" + ms + "&year=" + ys + "&rnd=" + Math.random();
}
</script>

<br>
    <hr>
<br>
    <h2 align="center">ตารางสรุปยอดรวมทั้งหมด ณ ปัจจุบัน (&nbsp;<?= $y2 ?> &nbsp;)</h2>
<div class="table-responsive">
<table height="171" border="1" cellpadding="1" cellspacing="1" class="table-bordered table-hover mb-4 mt-4">
  <tbody>
    <tr>
      <td rowspan="4" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">ลำดับ</td>
      <td rowspan="4" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">สถานี</td>
      <td rowspan="4" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">(1) จำนวนอาวุธปืนของกลาง ที่อยู่ในบัญชีของสถานี (กระบอก)</td>
      <td height="46" colspan="13" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">ปืนของกลางที่มีอัตลักษณ์ (ปืนที่ผลิตโดยโรงงาน มีแบรนด์ มีคุณภาพการผลิต)</td>
      <td colspan="8" bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">ปืนของกลางที่ไม่มีมีอัตลักษณ์ (ปืนที่ไม่ได้ผลิตโดยโรงงาน ไม่มีแบรนด์ ไม่มีคุณภาพการผลิต หรือปืนไทยประดิษฐ์ เป็นต้น)</td>
      <td bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">&nbsp;</td>
    </tr>
    <tr>
      <td rowspan="3" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">(2) จำนวนปืน ที่มีอัตลักษณ์ (กระบอก)</td>
      <td rowspan="3" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">(3) ศาลสั่งริบ คดีถึงที่สุด (กระบอก)</td>
      <td rowspan="3" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">(4) ศาลสั่งคืน คดีถึงที่สุด (กระบอก)</td>
      <td rowspan="3" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">(5) คดียังไม่ถึงที่สุด (กระบอก)</td>
      <td height="46" colspan="2" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">กรณีไม่ปรากฎว่า ผู้ใดเป็นผู้กระทำความผิด หรือคดีที่รู้ตัวแต่ยังจับตัวไม่ได้</td>
      <td colspan="2" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">(8) ส่งกองสรรพวุธ (กระบอก)</td>
      <td rowspan="3" bgcolor="yellow" style="color: #00000; text-align: center; border-color: navy">(9) ร้อยละ การดำเนินการ ส่งกองสรรพวุธ หลังศาลสั่งริบ</td>
      <td rowspan="3" bgcolor="#F190EF" style="color: #00000; text-align: center; border-color: navy">คงเหลือ ปืนรอส่ง สรรพาวุธ (กระบอก)</td>
      <td rowspan="3" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">(10) ประสาน นายอำเภอ เพิกถอน ทะเบียน (กระบอก)</td>
      <td rowspan="3" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">(11) นายอำเภอ เพิกถอน ทะเบียน (กระบอก)</td>
      <td rowspan="3" bgcolor="yellow" style="color: #00000; text-align: center; border-color: navy">(12) ร้อยละ การเพิกถอน ทะเบียน หลังประสาน นายอำเภอ</td>
      <td rowspan="3" bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">(13) จำนวนปืน ไม่มีอัตลักษณ์ (กระบอก)</td>
      <td rowspan="3" bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">(14) ศาลสั่งริบ คดีถึงที่สุด (กระบอก)</td>
      <td rowspan="3" bgcolor="#F190EF" style="color: #00000; text-align: center; border-color: navy">(21) ศาลสั่งคืน คดีถึงที่สุด (กระบอก)</td>
      <td rowspan="3" bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">(15) คดียังไม่ถึงที่สุด (กระบอก)</td>
      <td colspan="2" rowspan="2" bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">กรณีไม่ปรากฎว่า ผู้ใดเป็นผู้กระทำความผิด หรือคดีที่รู้ตัวแต่ยังจับตัวไม่ได้</td>
      <td rowspan="3" bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">(18) ทำลายแล้ว (กระบอก)</td>
      <td rowspan="3" bgcolor="yellow" style="color: #00000; text-align: center; border-color: navy">(19) ร้อยละ การจำหน่าย อาวุธปืน ออกจากคลัง</td>
      <td rowspan="3" bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">(20) คงเหลือ รอทำลาย (กระบอก)</td>
      </tr>
    <tr>
      <td rowspan="2" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">(6) ขาดอายุความ คดีอาญา (กระบอก)</td>
      <td rowspan="2" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">(7) ยังอยู่ในอายุความ คดีอาญา (กระบอก)</td>
      <td rowspan="2" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">(8.1) มีทะเบียน</td>
      <td rowspan="2" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">(8.2) ไม่มีทะเบีน</td>
      </tr>
    <tr>
      <td bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">(16) ขาดอายุความ คดีอาญา (กระบอก)</td>
	  <td height="58" bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">(17) ยังอยู่ในอายุความ คดีอาญา (กระบอก)</td>
	  </tr>
	  
<?php  
    
$sql_21 = "SELECT " .
            "(SELECT COUNT(*) FROM `wm_tb_gun_confidential` WHERE station='$station' AND `confidential_gun` AND `status` IN ('1','2','4') AND YEAR(record_date) * 100 + MONTH(record_date) <= $year * 100 + $month) AS T11, " .   //จำนวนอาวุธปืนของกลางที่อยู่ในบัญชีของสถานี(กระบอก)
            "(SELECT COUNT(*) FROM `wm_tb_gun_confidential` WHERE station='$station' AND `unique_guns` AND `status` IN('1','2','4') AND YEAR(record_date) * 100 + MONTH(record_date) <= $year * 100 + $month) T21, " .  //จำนวนปืนของกลางที่มีอัตลักษณ์
            "(SELECT COUNT(*) FROM `wm_tb_gun_confidential` WHERE station='$station' AND `unique_guns`=1 AND `status`IN('1','2','4') AND YEAR(record_date) * 100 + MONTH(record_date) <= $year * 100 + $month) T31, " .  //ศาลสั่งริบ คดีถึงที่สุด
            "(SELECT COUNT(*) FROM `wm_tb_gun_confidential` WHERE station='$station' AND `unique_guns`=2 AND `status` IN('1','2','4') AND YEAR(record_date) * 100 + MONTH(record_date) <= $year * 100 + $month) T41, " .  //ศาลสั่งคืน คดีถึงที่สุด 
            "(SELECT COUNT(*) FROM `wm_tb_gun_confidential` WHERE station='$station' AND `unique_guns`=3 AND `status` IN('1','2','4') AND YEAR(record_date) * 100 + MONTH(record_date) <= $year * 100 + $month) T51, " .  //คดียังไม่ถึงที่สุด
            "(SELECT COUNT(*) FROM `wm_tb_gun_confidential` WHERE station='$station' AND `unique_guns_unknow`=1 AND `status` IN('1','2','4') AND YEAR(record_date) * 100 + MONTH(record_date) <= $year * 100 + $month) T61, " .  //ขาดอายุความคดีอาญา
            "(SELECT COUNT(*) FROM `wm_tb_gun_confidential` WHERE station='$station' AND `unique_guns_unknow`=2 AND YEAR(record_date) * 100 + MONTH(record_date) <= $year * 100 + $month) T71, ".   //ยังอยู่ในอายุความคดีอาญา
            "(SELECT COUNT(*) FROM `wm_tb_gun_confidential` WHERE station='$station' AND `sanpawut`=1 AND YEAR(record_date) * 100 + MONTH(record_date) <= $year * 100 + $month) T81, " .  //ส่งกองสรรพวุธ ปืนมีทะเบียน
            "(SELECT COUNT(*) FROM `wm_tb_gun_confidential` WHERE station='$station' AND `sanpawut`=2 AND YEAR(record_date) * 100 + MONTH(record_date) <= $year * 100 + $month) T82, " .  //ส่งกองสรรพวุธ ปืนไม่มีทะเบียน
            "(SELECT COUNT(*) FROM `wm_tb_gun_confidential` WHERE station='$station' AND `DistrictChief_co`=1 AND YEAR(record_date) * 100 + MONTH(record_date) <= $year * 100 + $month) T101, " .  //ประสานนายอำเภอ เพิกถอนทะเบียน (กระบอก)
            "(SELECT COUNT(*) FROM `wm_tb_gun_confidential` WHERE station='$station' AND `DistrictChief_revoke`=1 AND YEAR(record_date) * 100 + MONTH(record_date) <= $year * 100 + $month) T111, " .  //นายอำเภอ เพิกถอนทะเบียน (กระบอก)
            "(SELECT COUNT(*) FROM `wm_tb_gun_confidential` WHERE station='$station' AND `no_identify` AND `status`IN ('1','2','4') AND YEAR(record_date) * 100 + MONTH(record_date) <= $year * 100 + $month) T131, " .  //อาวุธปืนไม่มีอัตลักษณ์
             "(SELECT COUNT(*) FROM `wm_tb_gun_confidential` WHERE station='$station' AND `no_identify`=1 AND `status`IN ('1','2','4') AND YEAR(record_date) * 100 + MONTH(record_date) <= $year * 100 + $month) T141, " .  //ศาลสั่งริบ คดีถึงที่สุด
             "(SELECT COUNT(*) FROM `wm_tb_gun_confidential` WHERE station='$station' AND `no_identify`=3 AND `status`IN ('1','2','4') AND YEAR(record_date) * 100 + MONTH(record_date) <= $year * 100 + $month) T211, " .  //ศาลสั่งคืน คดีถึงที่สุด
             "(SELECT COUNT(*) FROM `wm_tb_gun_confidential` WHERE station='$station' AND `no_identify`=2 AND YEAR(record_date) * 100 + MONTH(record_date) <= $year * 100 + $month) T151, " .  //คดียังไม่ถึงที่สุด
             "(SELECT COUNT(*) FROM `wm_tb_gun_confidential` WHERE station='$station' AND `no_identify_unknow`=1 AND YEAR(record_date) * 100 + MONTH(record_date) <= $year * 100 + $month) T161, " .  //ขาดอายุความคดีอาญา
             "(SELECT COUNT(*) FROM `wm_tb_gun_confidential` WHERE station='$station' AND `no_identify_unknow`=2 AND YEAR(record_date) * 100 + MONTH(record_date) <= $year * 100 + $month) T171, " .  //ยังอยู่ในอายุความคดีอาญา
            "(SELECT COUNT(*) FROM `wm_tb_gun_confidential` WHERE station='$station' AND `destroy_unknow`=1 AND YEAR(record_date) * 100 + MONTH(record_date) <= $year * 100 + $month) T181 " .  //ทำลายแล้ว (กระบอก)
            "";
    $stmt21 = $pdo->prepare($sql_21);
try{
    $stmt21->execute();
}catch (PDOException $e) {
    echo '$sql_21 Failed: '. $e->getMessage();
}

//echo $sql_21;
// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no_21 = 1;
while($row_21 = $stmt21->fetch(PDO::FETCH_ASSOC))	//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
{
//ฟังก์ชั่น วันที่ ดึงจาก condb
//$strDate = DateThai( $row_mi["date_record"] );
    
//ร้อยละส่งกองสรรพวุธ (9) = (8)/[ (3)+(6) ] * 100
if($row_21['T31'] != 0) {   
    $persen_san_21 = ($row_21['T81']+$row_21['T82'])/$row_21['T31']+$row_21['T61']*100;
}
else {
    $persen_san_21 = 0;
    } 
    
// คงเหลือ ปืนรอส่งสรรพาวุธ
$supawut_at_st = $row_21["T31"]-($row_21["T81"]+$row_21["T82"]);
    
//ร้อยละการเพิกถอนทะเบียนหลังประสานนายอาเภอ     (12) = (11)/(10) * 100               

if($row_21['T111'] > 0) {   
    $persen_revoke_21 = $row_21['T111']/$row_21['T101']*100;
}
else {
    $persen_revoke_21 = 0;
    }

//ร้อยละการจาหน่ายอาวุธปืนออกจากคลัง (19) = (18)/[ (14)+(16) ] * 100 
if($row_21['T141'] != 0) {   
    $persen_clear_21 = $row_21['T181']/(($row_21['T141']+$row_21['T161']))*100;
}
else {
    $persen_clear_21 = 0;
    }

?>	

    <tr>
	  <td style="font-size: 24px"><?= $no_21 ?> </td> 
      <td nowrap>&nbsp;<?= $name_station ?> &nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row_21["T11"] > 0) ? $row_21["T11"]  : "") ?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row_21["T21"] > 0) ? $row_21["T21"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row_21["T31"] > 0) ? $row_21["T31"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row_21["T41"] > 0) ? $row_21["T41"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row_21["T51"] > 0) ? $row_21["T51"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row_21["T61"] > 0) ? $row_21["T61"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row_21["T71"] > 0) ? $row_21["T71"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row_21["T81"] > 0) ? $row_21["T81"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row_21["T82"] > 0) ? $row_21["T82"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px; background-color: yellow">&nbsp;<?= $persen_san_21 ?>&nbsp;%</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($supawut_at_st >0) ? $supawut_at_st : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row_21["T101"] > 0) ? $row_21["T101"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row_21["T111"] > 0) ? $row_21["T111"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px; background-color: yellow">&nbsp;<?= $persen_revoke_21 ?>&nbsp;%</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row_21["T131"] > 0) ? $row_21["T131"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row_21["T141"] > 0) ? $row_21["T141"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row_21["T211"] > 0) ? $row_21["T211"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row_21["T151"] > 0) ? $row_21["T151"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row_21["T161"] > 0) ? $row_21["T161"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row_21["T171"] > 0) ? $row_21["T171"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row_21["T181"] > 0) ? $row_21["T181"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px; background-color: yellow">&nbsp;<?= $persen_clear_21 ?>&nbsp;%</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row_21["T131"] > 0) ? $row_21["T131"]  : "")?>&nbsp;</td>
    </tr>
  </tbody>
<?php
        
	$no_21++;
}
?>
</table>
<p>&nbsp;</p>
</div>
    
