<?php
include '../Condb.php';
include '../users.inc.php';
include '../Alert.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);
$ip_address = $_SERVER['REMOTE_ADDR'];
$action = 'Save CCTV survey'; // You can set the action to a suitable value
//$data = json_encode($_POST); // You can store the $_POST data as a JSON string
$data = str_replace("'", "", compact_array($_POST));
log_activity($acc, $action, $data, $ip_address); // ใช้ function ในไฟล์ Condb.php

//echo '<pre>';
//print_r($_POST);
//echo '</pre>';
//
//echo '<hr>';
//
//exit();

$sur_aid = $_POST[ 'sur_aid' ];
$region = $_POST[ 'region' ];
$provincial = $_POST[ 'provincial' ];
$station = $_POST[ 'station' ];
$sur_crimes = $_POST[ 'sur_crimes' ];
$sur_crimes_n = $_POST[ 'sur_crimes_n' ];
$sur_crimes_ac = $_POST[ 'sur_crimes_ac' ];
$sur_crimes_ac_n = $_POST[ 'sur_crimes_ac_n' ];
$sur_crimes_point = $_POST[ 'sur_crimes_point' ];
$sur_crimes_point_n = $_POST[ 'sur_crimes_point_n' ];
$sur_crimes_cctv = $_POST[ 'sur_crimes_cctv' ];
$sur_crimes_cctv_n = $_POST[ 'sur_crimes_cctv_n' ];
$sur_risk = $_POST[ 'sur_risk' ];
$sur_risk_n = $_POST[ 'sur_risk_n' ];
$sur_risk_ac = $_POST[ 'sur_risk_ac' ];
$sur_risk_ac_n = $_POST[ 'sur_risk_ac_n' ];
$sur_risk_point = $_POST[ 'sur_risk_point' ];
$sur_risk_point_n = $_POST[ 'sur_risk_point_n' ];
$sur_risk_cctv = $_POST[ 'sur_risk_cctv' ];
$sur_risk_cctv_n = $_POST[ 'sur_risk_cctv_n' ];
$sur_mian_road = $_POST[ 'sur_mian_road' ];
$sur_mian_road_n = $_POST[ 'sur_mian_road_n' ];
$sur_mian_road_ac = $_POST[ 'sur_mian_road_ac' ];
$sur_mian_road_ac_n = $_POST[ 'sur_mian_road_ac_n' ];
$sur_mian_road_point = $_POST[ 'sur_mian_road_point' ];
$sur_mian_road_point_n = $_POST[ 'sur_mian_road_point_n' ];
$sur_mian_road_cctv = $_POST[ 'sur_mian_road_cctv' ];
$sur_mian_road_cctv_n = $_POST[ 'sur_mian_road_cctv_n' ];
$sur_lat = $_POST[ 'sur_lat' ];
$sur_lon = $_POST[ 'sur_lon' ];
$sur_remark = $_POST[ 'sur_remark' ];
$sur_date_ac = $_POST[ 'sur_date_ac' ];
$sur_n_doc = $_POST[ 'sur_n_doc' ];
$sur_from = $_POST[ 'sur_from' ];
$sur_sender = $_POST[ 'sur_sender' ];
$sur_to = $_POST[ 'sur_to' ];
$sur_receiver = $_POST[ 'sur_receiver' ];
$sur_receive_date = $_POST[ 'sur_receive_date' ];
$sur_status = $_POST[ 'sur_status' ];

// save Image
$file1 = $_FILES[ 'sur_image' ][ 'tmp_name' ];
if ( is_uploaded_file( $file1 ) ) {
    
    // เปลี่ยนชื่อไฟล์ก่อนบันทึก แก้ปัญหา ชื่อซ้ำกัน
    $sur_image = "/policeinnopolis/uploaded/Image1/" . basename( $_FILES[ 'sur_image' ][ 'name' ] );	    
    // file + ext    
    $ext = strrchr($sur_image, ".");    
    $sur_image = "../policeinnopolis/uploaded/Image1/File_" . time() . "_" . $station . $ext; // file+time+ext        
    move_uploaded_file( $file1, $sur_image );
    
} else {
  $sur_image = '';
}


// save file
$file2 = $_FILES[ 'sur_file' ][ 'tmp_name' ];
if ( is_uploaded_file( $file2 ) ) {
  $sur_file = "/policeinnopolis/uploaded/Doc/" . $_FILES[ 'sur_file' ][ 'name' ];	
  move_uploaded_file( $file2, "../" . $sur_file );  
} else {
  $sur_file = '';
}

// convert Thai dates to eng
if($sur_date_ac != '') {
	if(strpos($sur_date_ac, '/') > 0) {
    	$dates = explode('/', $sur_date_ac);	// d/m/y
	}
	elseif(strpos($sur_date_ac, '-') > 0) {
		$date = explode('-', $sur_date_ac);	// y-m-d
		$dates = array($date[0], $date[1], $date[2]);	//Y-m-d
	}
	// thai dates
	if(substr(''.$dates[0],0,2) ==='25') {
		$sur_date_ac = ($dates[0]-543) . '-' . $dates[1] . '-' . $dates[2];
	}
	// eng dates
	else {
    	$sur_date_ac = $dates[0] . '-' . $dates[1] . '-' . $dates[2];
	}
}

// convert Thai dates to eng
if($sur_receive_date != '') {
	if(strpos($sur_receive_date, '/') > 0) {
    	$dates = explode('/', $sur_receive_date);	// d/m/y
	}
	elseif(strpos($sur_receive_date, '-') > 0) {
		$date = explode('-', $sur_receive_date);	// y-m-d
		$dates = array($date[0], $date[1], $date[2]);	//Y-m-d
	}
	// thai dates
	if(substr(''.$dates[0],0,2) ==='25') {
		$sur_receive_date = ($dates[0]-543) . '-' . $dates[1] . '-' . $dates[2];
	}
	// eng dates
	else {
    	$sur_receive_date = $dates[0] . '-' . $dates[1] . '-' . $dates[2];
	}
}


$sql ="SELECT * FROM `wm_tb_CCTV_survey` WHERE sur_aid='$sur_aid' ";
$result = mysqli_query($conn,$sql);
$row = mysqli_fetch_array($result);
if($row) {
        $sur_aid = $row['sur_aid'];
        $sql = "UPDATE wm_tb_CCTV_survey SET " .
                    "region = '$region'," .
                    "provincial = '$provincial'," .
                    "station = '$station'," .
                    "sur_crimes = '$sur_crimes'," .
                    "sur_crimes_n = '$sur_crimes_n'," .
                    "sur_crimes_ac = '$sur_crimes_ac'," .
                    "sur_crimes_ac_n = '$sur_crimes_ac_n'," .
                    "sur_crimes_point = '$sur_crimes_point'," .
                    "sur_crimes_point_n = '$sur_crimes_point_n'," .
                    "sur_crimes_cctv = '$sur_crimes_cctv'," .
                    "sur_crimes_cctv_n = '$sur_crimes_cctv_n'," .
                    "sur_risk = '$sur_risk'," .
                    "sur_risk_n = '$sur_risk_n'," .
                    "sur_risk_ac = '$sur_risk_ac'," .
                    "sur_risk_ac_n = '$sur_risk_ac_n'," .
                    "sur_risk_point = '$sur_risk_point'," .
                    "sur_risk_point_n = '$sur_risk_point_n'," .
                    "sur_risk_cctv = '$sur_risk_cctv'," .
                    "sur_risk_cctv_n = '$sur_risk_cctv_n'," .
                    "sur_mian_road = '$sur_mian_road'," .
                    "sur_mian_road_n = '$sur_mian_road_n'," .
                    "sur_mian_road_ac = '$sur_mian_road_ac'," .
                    "sur_mian_road_ac_n = '$sur_mian_road_ac_n'," .
                    "sur_mian_road_point = '$sur_mian_road_point'," .
                    "sur_mian_road_point_n = '$sur_mian_road_point_n'," .
                    "sur_mian_road_cctv = '$sur_mian_road_cctv'," .
                    "sur_mian_road_cctv_n = '$sur_mian_road_cctv_n'," .
                    "sur_lat = '$sur_lat'," .
                    "sur_lon = '$sur_lon'," .
                    "sur_remark = '$sur_remark'," .
                    "sur_date_ac = '$sur_date_ac'," .
                    "sur_n_doc = '$sur_n_doc'," .
                    "sur_from = '$sur_from'," .
                    "sur_sender = '$sur_sender'," .
                    "sur_to = '$sur_to'," .
                    "sur_receiver = '$sur_receiver'," .
                    "sur_receive_date = '$sur_receive_date'," .
                    "sur_status = '$sur_status' ";

                    if($sur_file !== '')
                        {
                            $sql =  $sql . ", sur_file = '$sur_file' ";
                        }
                    elseif($sur_image !== '')
                        {
                            $sql =  $sql . ", sur_image = '$sur_image' ";
                        }
                        $sql = $sql . " WHERE sur_aid = '$sur_aid' ";
        }else{
            $sql = "INSERT INTO wm_tb_CCTV_survey (region, provincial, station, sur_crimes, sur_crimes_n, sur_crimes_ac, sur_crimes_ac_n, sur_crimes_point, sur_crimes_point_n, sur_crimes_cctv, sur_crimes_cctv_n, sur_risk, sur_risk_n, sur_risk_ac, sur_risk_ac_n, sur_risk_point, sur_risk_point_n, sur_risk_cctv, sur_risk_cctv_n, sur_mian_road, sur_mian_road_n, sur_mian_road_ac, sur_mian_road_ac_n, sur_mian_road_point, sur_mian_road_point_n, sur_mian_road_cctv, sur_mian_road_cctv_n, sur_lat, sur_lon, sur_remark, sur_date_ac, sur_n_doc, sur_from, sur_sender, sur_to, sur_receiver, sur_receive_date, sur_status, sur_image, sur_file) VALUES('$region', '$provincial', '$station', '$sur_crimes', '$sur_crimes_n', '$sur_crimes_ac', '$sur_crimes_ac_n', '$sur_crimes_point', '$sur_crimes_point_n', '$sur_crimes_cctv', '$sur_crimes_cctv_n', '$sur_risk', '$sur_risk_n', '$sur_risk_ac', '$sur_risk_ac_n', '$sur_risk_point', '$sur_risk_point_n', '$sur_risk_cctv', '$sur_risk_cctv_n', '$sur_mian_road', '$sur_mian_road_n', '$sur_mian_road_ac', '$sur_mian_road_ac_n', '$sur_mian_road_point', '$sur_mian_road_point_n', '$sur_mian_road_cctv', '$sur_mian_road_cctv_n', '$sur_lat', '$sur_lon', '$sur_remark', '$sur_date_ac', '$sur_n_doc', '$sur_from', '$sur_sender', '$sur_to', '$sur_receiver', '$sur_receive_date', '$sur_status', '$sur_image', '$sur_file') ";
        }

$result=mysqli_query($conn,$sql);

mysqli_report(MYSQLI_REPORT_ERROR | MYSQLI_REPORT_STRICT);
if ($result) {
    $_SESSION['success'] = "Data has been inserted succesfully";
    showSweetAlert("Saved successfully", "บันทึกข้อมูลเรียบร้อย");
    unset($_SESSION['success']); // Clear the session variable
    header("refresh:2; url=/WatchmanData/Show_CCTV_install.php");
} else {
    $_SESSION['error'] = "Error";
    showSweetAlert('Error', "บันทึกข้อมูล ไม่สำเร็จ", 'error');
    unset($_SESSION['error']); // Clear the session variable
    header("refresh:2; url=/WatchmanData/Show_CCTV_install.php");
}

mysqli_close($conn);

?>