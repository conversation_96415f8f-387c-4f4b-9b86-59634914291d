<?php
// Step 1: Establish a database connection
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';
include '../Head_image.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

//get month from select or current month
$month = isset($_GET['month']) ? $_GET['month'] : date("m");
$year = isset($_GET['year']) ? $_GET['year'] : date("Y");

?>
<html>
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
    
  </head>
  <body>
    <div class="container-fluid" align="left">
		<p></p>
        <div mb-4 mt-4><a href="/Bansuan/index.php?rnd=<?= rand(); ?>&page=report" class="btn btn-warning" >ย้อนกลับ</a></div>
<?php
// Step 2: Fetch data for goals and performance for each month  // CASE WHEN wr_cl_status = 2 THEN `wr_cl_date_finish` ELSE 0 END  // WHERE MONTH(wr_cl_date_finish)='$month' AND YEAR(wr_cl_date_finish)='$year'
$query = "SELECT MONTH(wr_cl_date) as month,
                count(wr_cl_status IN (1,4)) as status1
            FROM wm_tb_warrant
            WHERE station='$station' AND YEAR(wr_cl_date) = YEAR(CURDATE())
            GROUP BY MONTH(wr_cl_date)";
    $stmt = $pdo->prepare($query);
try{
    $stmt->execute();
}catch (PDOException $e) {
    echo '$query Failed: '. $e->getMessage();
}
    
//echo $query;
//WHERE MONTH(pf_cl_dates)='$month' ส่วนนี้ ยังไม่สำเร็จ
// Step 3: Loop through the result set and store the values in two separate arrays
$status1_array = array();

while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $status1_array[] = $row['status1'];
}

$query2 = "SELECT MONTH(wr_cl_date_finish) as month,
                count(wr_cl_status IN (2,3,5,6,7)) as status2
            FROM wm_tb_warrant
            WHERE station='$station' AND YEAR(wr_cl_date_finish) = YEAR(CURDATE())
            GROUP BY MONTH(wr_cl_date_finish)";
    $stmt2 = $pdo->prepare($query2);
try{
    $stmt2->execute();
}catch (PDOException $e) {
    echo '$query2 Failed: '. $e->getMessage();
}

//echo $query2;
//WHERE MONTH(pf_cl_dates)='$month' ส่วนนี้ ยังไม่สำเร็จ
// Step 3: Loop through the result set and store the values in two separate arrays
$status2_array = array();
while ($row2 = $stmt2->fetch(PDO::FETCH_ASSOC)) {
    $status2_array[] = $row2['status2'];
}
// Step 4: Use a charting library to create a bar graph
?>
        <!-- แสดงกราฟ -->
        <div id="chart_div" style="width: 100%; height: 500px;"></div>
       <!-- <div id="chart_div2" style="width: 100%; height: 500px;"></div>-->
    </div>
  </body>
</html>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
    <script type="text/javascript">
      google.charts.load('current', {'packages':['corechart']});
      google.charts.setOnLoadCallback(drawChart);

      function drawChart() {
        var data = google.visualization.arrayToDataTable([    
          ['Month', 'ออกหมาย', 'จำหน่ายหมาย'],
          ['ม.ค.', <?php echo $status1_array[0]; ?>, <?php echo $status2_array[0]; ?>],
          ['ก.พ.', <?php echo $status1_array[1]; ?>, <?php echo $status2_array[1]; ?>],
          ['มี.ค.', <?php echo $status1_array[2]; ?>, <?php echo $status2_array[2]; ?>],
          ['เม.ย.', <?php echo $status1_array[3]; ?>, <?php echo $status2_array[3]; ?>],
          ['พ.ค.', <?php echo $status1_array[4]; ?>, <?php echo $status2_array[4]; ?>],
		  ['มิ.ย.', <?php echo $status1_array[5]; ?>, <?php echo $status2_array[5]; ?>],
		  ['ก.ค.', <?php echo $status1_array[6]; ?>, <?php echo $status2_array[6]; ?>],
          // Add more months as needed
        ]);
         
        var options = {
          title: 'แผนภูมิแสดงข้อมูลเปรียบเทียบ หมายจับ สภ.บ้านสวน รายเดือน',
          chartArea: {width: '50%'},
          hAxis: {
            title: '',
            minValue: 0
          },
          vAxis: {
            title: 'จำนวนหมายจับ'
          }
        };

        var chart = new google.visualization.ColumnChart(document.getElementById('chart_div'));
        chart.draw(data, options);
      }
    </script>
