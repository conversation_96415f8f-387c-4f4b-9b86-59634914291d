<?php
include '../Condb.php';
include '../users.inc.php';
include '../Alert.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);
$ip_address = $_SERVER['REMOTE_ADDR'];
$action = 'Save Edit foreigner'; // You can set the action to a suitable value
$data = str_replace("'", "", compact_array($_POST));
log_activity($acc, $action, $data, $ip_address); // ใช้ function ในไฟล์ Condb.php

//echo '<pre>';
//print_r($_POST);
//echo '</pre>';
//
//echo '<hr>';
//
//echo '<pre>';
//var_dump($_POST);
//echo '</pre>';
//
//exit();
    $fo_cl_aid = $_POST['fo_cl_aid'];
    $fo_cl_type_passport = $_POST['fo_cl_type_passport'];
    $fo_cl_passport = $_POST['fo_cl_passport'];
    $fo_cl_nationality = $_POST['fo_cl_nationality'];
    $fo_cl_prefix_eng = $_POST['fo_cl_prefix_eng'];
    $fo_cl_sex = $_POST['fo_cl_sex'];
    $fo_cl_name = $_POST['fo_cl_name'];
    $fo_cl_midname = $_POST['fo_cl_midname'];
    $fo_cl_surname = $_POST['fo_cl_surname'];
    $fo_cl_birthday = $_POST['fo_cl_birthday'];
    $fo_cl_age = $_POST['fo_cl_age'];
    $fo_cl_province = $_POST['fo_cl_province'];
    $fo_cl_amphur = $_POST['fo_cl_amphur'];
    $fo_cl_tumbon = $_POST['fo_cl_tumbon'];
    $fo_cl_moo = $_POST['fo_cl_moo'];
    $fo_cl_road = $_POST['fo_cl_road'];
    $fo_cl_adr_building = $_POST['fo_cl_adr_building'];
    $fo_cl_adr = $_POST['fo_cl_adr'];
    $fo_cl_country = $_POST['fo_cl_country'];
    $fo_cl_date_in = $_POST['fo_cl_date_in'];
    $fo_cl_imm_chk_in = $_POST['fo_cl_imm_chk_in'];
    $fo_cl_stay_exp = $_POST['fo_cl_stay_exp'];
    $fo_cl_workpermit_no = $_POST['fo_cl_workpermit_no'];
    $fo_cl_position = $_POST['fo_cl_position'];
    $fo_cl_workpermit_exp = $_POST['fo_cl_workpermit_exp'];
    $fo_cl_status = $_POST['fo_cl_status'];
    $fo_cl_reason = $_POST['fo_cl_reason'];
    $fo_cl_employer_no = $_POST['fo_cl_employer_no'];
    $fo_cl_employer_name = $_POST['fo_cl_employer_name'];
    $fo_cl_employer_type = $_POST['fo_cl_employer_type'];
    $fo_cl_employer_address = $_POST['fo_cl_employer_address'];
    $fo_cl_employer_phone = $_POST['fo_cl_employer_phone'];
    $region = $_POST['region'];
    $provincial = $_POST['provincial'];
    $station = $_POST['station'];
    
    //แปลงปี จากปีไทย เป็นปีอังกฤษ
    //$fo_cl_birthday = thai_date_2_eng($fo_cl_birthday);
    //เมื่อได้วันเกิดอังกฤษแล้ว ใช้ฟังก์ชั่นแปลงวันที่ เป็นอายุ
    $fo_cl_age = get_personal_age_2($fo_cl_birthday);
    

// save Image ใหม่แทน
$file1 = $_FILES[ 'fo_cl_image' ][ 'tmp_name' ];

if ( is_uploaded_file( $file1 ) ) {

    //ลบรูปเก่าในเซิร์ฟเวอร์ก่อน
    $sql = "SELECT * FROM wm_tb_foreigner WHERE fo_cl_aid='fo_cl_aid' ";
    $res = mysqli_query($conn,$sql);
    if($row = mysqli_fetch_array($res))
    {
        if(($row['fo_cl_image'] != '') && file_exists($row['fo_cl_image'])) {
            unlink( $row['fo_cl_image'] );
        }    
    }

    //เปลี่ยนชื่อไฟล์ใหม่
    $fo_cl_image = "/policeinnopolis/uploaded/Image1/" . basename( $_FILES[ 'fo_cl_image' ][ 'name' ] );	    
    // file + ext    
    $ext = strrchr($fo_cl_image, ".");    
    $fo_cl_image = "../policeinnopolis/uploaded/Image1/File_" . time() . $ext; // file+time+ext        
    move_uploaded_file( $file1, $fo_cl_image );  
} 
else {
  $fo_cl_image = '';
}


    $sql1 = "SELECT * FROM wm_tb_foreigner WHERE fo_cl_aid='fo_cl_aid' ";
    if($fo_cl_birthday == "") 
    {
        $sql1 =  "UPDATE wm_tb_foreigner SET " .
                "fo_cl_type_passport = '$fo_cl_type_passport', " .
                "fo_cl_passport = '$fo_cl_passport', " .
                "fo_cl_nationality = '$fo_cl_nationality', " .
                "fo_cl_prefix_eng = '$fo_cl_prefix_eng', " .
                "fo_cl_sex = '$fo_cl_sex', " .
                "fo_cl_name = '$fo_cl_name', " .
                "fo_cl_midname = '$fo_cl_midname', " .
                "fo_cl_surname = '$fo_cl_surname', " .
                "fo_cl_province = '$fo_cl_province', " .
                "fo_cl_amphur = '$fo_cl_amphur', " .
                "fo_cl_tumbon = '$fo_cl_tumbon', " .
                "fo_cl_moo = '$fo_cl_moo', " .
                "fo_cl_road = '$fo_cl_road', " .
                "fo_cl_adr_building = '$fo_cl_adr_building', " .
                "fo_cl_adr = '$fo_cl_adr', " .
                "fo_cl_country = '$fo_cl_country', " .
                "fo_cl_date_in = '$fo_cl_date_in', " .
                "fo_cl_imm_chk_in = '$fo_cl_imm_chk_in', " .
                "fo_cl_stay_exp = '$fo_cl_stay_exp', " .
                "fo_cl_workpermit_no = '$fo_cl_workpermit_no', " .
                "fo_cl_position = '$fo_cl_position', " .
                "fo_cl_workpermit_exp = '$fo_cl_workpermit_exp', " .
                "fo_cl_status = '$fo_cl_status', " .
                "fo_cl_reason = '$fo_cl_reason', " .
                "fo_cl_employer_no = '$fo_cl_employer_no', " .
                "fo_cl_employer_name = '$fo_cl_employer_name', " .
                "fo_cl_employer_type = '$fo_cl_employer_type', " .
                "fo_cl_employer_address = '$fo_cl_employer_address', " .
                "fo_cl_employer_phone = '$fo_cl_employer_phone', " .
                "region = '$region', " .
                "provincial = '$provincial', " .
                "station = '$station' " ;
                if($fo_cl_image !== '')
                {
                    $sql1 =  $sql1 . ", fo_cl_image = '$fo_cl_image' ";
                }
                $sql1 = $sql1 . " WHERE fo_cl_aid = '$fo_cl_aid' ";  
    } else {
        $sql1 =  "UPDATE wm_tb_foreigner SET " .
                "fo_cl_type_passport = '$fo_cl_type_passport', " .
                "fo_cl_passport = '$fo_cl_passport', " .
                "fo_cl_nationality = '$fo_cl_nationality', " .
                "fo_cl_prefix_eng = '$fo_cl_prefix_eng', " .
                "fo_cl_sex = '$fo_cl_sex', " .
                "fo_cl_name = '$fo_cl_name', " .
                "fo_cl_midname = '$fo_cl_midname', " .
                "fo_cl_surname = '$fo_cl_surname', " .
                "fo_cl_birthday = '$fo_cl_birthday', " .
                "fo_cl_age = '$fo_cl_age', " .
                "fo_cl_province = '$fo_cl_province', " .
                "fo_cl_amphur = '$fo_cl_amphur', " .
                "fo_cl_tumbon = '$fo_cl_tumbon', " .
                "fo_cl_moo = '$fo_cl_moo', " .
                "fo_cl_road = '$fo_cl_road', " .
                "fo_cl_adr_building = '$fo_cl_adr_building', " .
                "fo_cl_adr = '$fo_cl_adr', " .
                "fo_cl_country = '$fo_cl_country', " .
                "fo_cl_date_in = '$fo_cl_date_in', " .
                "fo_cl_imm_chk_in = '$fo_cl_imm_chk_in', " .
                "fo_cl_stay_exp = '$fo_cl_stay_exp', " .
                "fo_cl_workpermit_no = '$fo_cl_workpermit_no', " .
                "fo_cl_position = '$fo_cl_position', " .
                "fo_cl_workpermit_exp = '$fo_cl_workpermit_exp', " .
                "fo_cl_status = '$fo_cl_status', " .
                "fo_cl_reason = '$fo_cl_reason', " .
                "fo_cl_employer_no = '$fo_cl_employer_no', " .
                "fo_cl_employer_name = '$fo_cl_employer_name', " .
                "fo_cl_employer_type = '$fo_cl_employer_type', " .
                "fo_cl_employer_address = '$fo_cl_employer_address', " .
                "fo_cl_employer_phone = '$fo_cl_employer_phone', " .
                "region = '$region', " .
                "provincial = '$provincial', " .
                "station = '$station' " ;
            
                if($fo_cl_image !== '')
                {
                    $sql1 =  $sql1 . ", fo_cl_image = '$fo_cl_image' ";
                }
                $sql1 = $sql1 . " WHERE fo_cl_aid = '$fo_cl_aid' ";
            }
        
    $result = mysqli_query($conn, $sql1);
    $err =  mysqli_error($conn);
    print_r($err);
    
//echo '<pre>';
//print_r($result);
//echo '</pre>';
//
//echo '<hr>';
//
//echo '<pre>';
//var_dump($result);
//echo '</pre>';

mysqli_report(MYSQLI_REPORT_ERROR | MYSQLI_REPORT_STRICT);
if ($result) {
    $_SESSION['success'] = "Data has been inserted succesfully";
    showSweetAlert("Saved successfully", "บันทึกข้อมูลเรียบร้อย");
    unset($_SESSION['success']); // Clear the session variable
    header("refresh:2; url=/Bansuan/index.php?pcid={$fo_cl_aid}&page=foreigner");
} else {
    $_SESSION['error'] = "Error";
    showSweetAlert('Error', "บันทึกข้อมูล ไม่สำเร็จ", 'error');
    unset($_SESSION['error']); // Clear the session variable
    header("refresh:2; url=/Bansuan/index.php?pcid={$fo_cl_aid}&page=foreigner");
}

mysqli_close($conn);

?>