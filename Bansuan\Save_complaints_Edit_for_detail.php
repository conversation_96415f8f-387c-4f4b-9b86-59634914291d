<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../Alert.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);
$ip_address = $_SERVER['REMOTE_ADDR'];
$action = 'Save complaints Edit for detail'; // You can set the action to a suitable value
$data = str_replace("'", "", compact_array($_POST));
log_activity($acc, $action, $data, $ip_address); // ใช้ function ในไฟล์ Condb.php

$cp_cl_aid = $_POST['cp_cl_aid'];
$cp_cl_case_id = $_POST[ 'cp_cl_case_id' ];									//รหัสรับแจ้งเหตุ (ไทม์แสตมป์)
$cp_cl_case = $_POST[ 'cp_cl_case' ];   									//เหตุ
$cp_cl_source = $_POST[ 'cp_cl_source' ];
$cp_cl_date_complaints = $_POST[ 'cp_cl_date_complaints' ];					//วันที่รับแจ้งเหตุ
$cp_cl_time_complaints = $_POST[ 'cp_cl_time_complaints' ];					//เวลารับแจ้งเหตุ
$cp_cl_date_incident = $_POST[ 'cp_cl_date_incident' ];						//วันที่เกิดเหตุ
$cp_cl_time_start_incident = $_POST[ 'cp_cl_time_start_incident' ];			//เวลาเกิดเหตุตั้งแต่
$cp_cl_time_end_incident = $_POST[ 'cp_cl_time_end_incident' ];				//ถึงเวลา
$cp_cl_place = $_POST[ 'cp_cl_place' ];										//สถานที่เกิดเหตุ
$cp_cl_sufferer_quantity = $_POST[ 'cp_cl_sufferer_quantity' ];				//จำนวนผู้เสียหาย (ราย)
$cp_cl_sufferer_name1 = $_POST[ 'cp_cl_sufferer_name1' ];					//ชื่อผู้เสียหาย 1
$cp_cl_age1 = $_POST[ 'cp_cl_age1' ];										//อายุผู้เสียหาย 1
$cp_cl_address1 = $_POST[ 'cp_cl_address1' ];								//ที่อยู่ผู้เสียหาย 1
$cp_cl_phone1 = $_POST[ 'cp_cl_phone1' ];									//โทรศัพท์ 1
$cp_cl_sufferer_name2 = $_POST[ 'cp_cl_sufferer_name2' ];					//ชื่อผู้เสียหาย 2
$cp_cl_age2 = $_POST[ 'cp_cl_age2' ];										//อายุผู้เสียหาย 2
$cp_cl_address2 = $_POST[ 'cp_cl_address2' ];								//ที่อยู่ผู้เสียหาย2
$cp_cl_phone2 = $_POST[ 'cp_cl_phone2' ];									//โทรศัพท์2
$cp_cl_sufferer_name3 = $_POST[ 'cp_cl_sufferer_name3' ];					//ชื่อผู้เสียหาย 3
$cp_cl_age3 = $_POST[ 'cp_cl_age3' ];										//อายุผู้เสียหาย 3
$cp_cl_address3 = $_POST[ 'cp_cl_address3' ];								//ที่อยู่ผู้เสียหาย 3
$cp_cl_phone3 = $_POST[ 'cp_cl_phone3' ];									//โทรศัพท์ 3
$cp_cl_suspect1 = $_POST[ 'cp_cl_suspect1' ];								//ผู้ต้องสงสัย 1
$cp_cl_suspect2 = $_POST[ 'cp_cl_suspect2' ];								//ผู้ต้องสงสัย 2
$cp_cl_suspect3 = $_POST[ 'cp_cl_suspect3' ];								//ผู้ต้องสงสัย 3
$cp_cl_description = $_POST[ 'cp_cl_description' ];							//ตำหนิรูปพรรณ
$cp_cl_vehicle = $_POST[ 'cp_cl_vehicle' ];									//ยานพาหนะที่ใช้ก่อเหตุ
$cp_cl_property = $_POST[ 'cp_cl_property' ];								//ทรัพย์สินที่ถูกประทุษร้าย
$cp_cl_case_behavior = $_POST[ 'cp_cl_case_behavior' ];						//พฤติการณ์
$cp_cl_map = $_POST[ 'cp_cl_map' ];											//แผนที่สถานที่เกิดเหตุ
$cp_cl_investigative_sergeant = $_POST[ 'cp_cl_investigative_sergeant' ];	//ร้อยเวรสืบสวน
$cp_cl_investigative_officer = $_POST[ 'cp_cl_investigative_officer' ];		//เจ้าหน้าที่สืบสวน
$cp_cl_action = $_POST[ 'cp_cl_action' ];	

// save file สส.1
$file1 = $_FILES[ 'cp_cl_complaints_file' ][ 'tmp_name' ];
if ( is_uploaded_file( $file1 ) ) {
  $cp_cl_complaints_file = "uploaded/Doc/" . $_FILES[ 'cp_cl_complaints_file' ][ 'name' ];	
  move_uploaded_file( $file1, "../" . $cp_cl_complaints_file );  
} else {
  $cp_cl_complaints_file = '';
}

// convert Thai dates to eng 1
if($cp_cl_date_complaints != '') {
	if(strpos($cp_cl_date_complaints, '/') > 0) {
    	$dates = explode('/', $cp_cl_date_complaints);	// d/m/y
	}
	elseif(strpos($cp_cl_date_complaints, '-') > 0) {
		$date = explode('-', $cp_cl_date_complaints);	// y-m-d
		$dates = array($date[2], $date[1], $date[0]);	// d/m/Y
	}
	// thai dates
	if(substr(''.$dates[2],0,2) ==='25') {
		$cp_cl_date_complaints = ($dates[2]-543) . '-' . $dates[1] . '-' . $dates[0];
	}
	// eng dates
	else {
    	$cp_cl_date_complaints = $dates[2] . '-' . $dates[1] . '-' . $dates[0];
	}
}

// convert Thai dates to eng 2
if($cp_cl_date_incident != '') {
	if(strpos($cp_cl_date_incident, '/') > 0) {
    	$dates = explode('/', $cp_cl_date_incident);	// d/m/y
	}
	elseif(strpos($cp_cl_date_incident, '-') > 0) {
		$date = explode('-', $cp_cl_date_incident);	// y-m-d
		$dates = array($date[2], $date[1], $date[0]);	// d/m/Y
	}
	// thai dates
	if(substr(''.$dates[2],0,2) ==='25') {
		$cp_cl_date_incident = ($dates[2]-543) . '-' . $dates[1] . '-' . $dates[0];
	}
	// eng dates
	else {
    	$cp_cl_date_incident = $dates[2] . '-' . $dates[1] . '-' . $dates[0];
	}
}

$sql = "UPDATE wm_tb_Complaints SET " .
		"cp_cl_case_id = :cp_cl_case_id," .
		"cp_cl_case = :cp_cl_case," .
        "cp_cl_source = :cp_cl_source," .
        "cp_cl_date_complaints = :cp_cl_date_complaints," .
    	"cp_cl_time_complaints = :cp_cl_time_complaints," .
        "cp_cl_date_incident = :cp_cl_date_incident," .
    	"cp_cl_time_start_incident = :cp_cl_time_start_incident," .
    	"cp_cl_time_end_incident = :cp_cl_time_end_incident," .
		"cp_cl_place = :cp_cl_place," .
		"cp_cl_sufferer_quantity = :cp_cl_sufferer_quantity," .
        "cp_cl_sufferer_name1 = :cp_cl_sufferer_name1," .
		"cp_cl_age1 = :cp_cl_age1," .
		"cp_cl_address1 = :cp_cl_address1," .
		"cp_cl_phone1 = :cp_cl_phone1," .
        "cp_cl_sufferer_name2 = :cp_cl_sufferer_name2," .
		"cp_cl_age2 = :cp_cl_age2," .
		"cp_cl_address2 = :cp_cl_address2," .
		"cp_cl_phone2 = :cp_cl_phone2," .
		"cp_cl_sufferer_name3 = :cp_cl_sufferer_name3," .
		"cp_cl_age3 = :cp_cl_age3," .
		"cp_cl_address3 = :cp_cl_address3," .
		"cp_cl_phone3 = :cp_cl_phone3," .
		"cp_cl_suspect1 = :cp_cl_suspect1," .
		"cp_cl_suspect2 = :cp_cl_suspect2," .
		"cp_cl_suspect3 = :cp_cl_suspect3,".
		"cp_cl_description = :cp_cl_description, ".
		"cp_cl_vehicle = :cp_cl_vehicle, ".
		"cp_cl_property = :cp_cl_property, ".
		"cp_cl_case_behavior = :cp_cl_case_behavior, " .
		"cp_cl_map = :cp_cl_map, " .
		"cp_cl_investigative_sergeant = :cp_cl_investigative_sergeant, " .
		"cp_cl_investigative_officer = :cp_cl_investigative_officer, " .
        "cp_cl_action = :cp_cl_action " ;

$params = [
            'cp_cl_case_id' => $cp_cl_case_id,
            'cp_cl_case' => $cp_cl_case,
            'cp_cl_source' => $cp_cl_source,
            'cp_cl_date_complaints' => $cp_cl_date_complaints,
            'cp_cl_time_complaints' => $cp_cl_time_complaints,
            'cp_cl_date_incident' => $cp_cl_date_incident,
            'cp_cl_time_start_incident' => $cp_cl_time_start_incident,
            'cp_cl_time_end_incident' => $cp_cl_time_end_incident,
            'cp_cl_place' => $cp_cl_place,
            'cp_cl_sufferer_quantity' => $cp_cl_sufferer_quantity,
            'cp_cl_sufferer_name1' => $cp_cl_sufferer_name1,
            'cp_cl_age1' => $cp_cl_age1,
            'cp_cl_address1' => $cp_cl_address1,
            'cp_cl_phone1' => $cp_cl_phone1,
            'cp_cl_sufferer_name2' => $cp_cl_sufferer_name2,
            'cp_cl_age2' => $cp_cl_age2,
            'cp_cl_address2' => $cp_cl_address2,
            'cp_cl_phone2' => $cp_cl_phone2,
            'cp_cl_sufferer_name3' => $cp_cl_sufferer_name3,
            'cp_cl_age3' => $cp_cl_age3,
            'cp_cl_address3' => $cp_cl_address3,
            'cp_cl_phone3' => $cp_cl_phone3,
            'cp_cl_suspect1' => $cp_cl_suspect1,
            'cp_cl_suspect2' => $cp_cl_suspect2,
            'cp_cl_suspect3' => $cp_cl_suspect3,
            'cp_cl_description' => $cp_cl_description,
            'cp_cl_vehicle' => $cp_cl_vehicle,
            'cp_cl_property' => $cp_cl_property,
            'cp_cl_case_behavior' => $cp_cl_case_behavior,
            'cp_cl_map' => $cp_cl_map,
            'cp_cl_investigative_sergeant' => $cp_cl_investigative_sergeant,
            'cp_cl_investigative_officer' => $cp_cl_investigative_officer,
            'cp_cl_action' => $cp_cl_action
            ];
    
            if ($cp_cl_complaints_file !== '') {
            $sql .= ", cp_cl_complaints_file = :cp_cl_complaints_file";
            $params['cp_cl_complaints_file'] = $cp_cl_complaints_file;
            }

            $sql .= " WHERE cp_cl_aid = :updated_cp_cl_aid";
            $params['updated_cp_cl_aid'] = $cp_cl_aid;


$stmt = $pdo->prepare($sql);
$result = $stmt->execute($params);

if ($result) {
    $_SESSION['success'] = "Data has been inserted succesfully";
    showSweetAlert("Saved successfully", "บันทึกข้อมูลเรียบร้อย");
    unset($_SESSION['success']); // Clear the session variable
    header("refresh:2; url=/Bansuan/show_detail_complaints.php?id={$cp_cl_aid}&pcid={$cp_cl_case_id}");
} else {
    $_SESSION['error'] = "Error";
    showSweetAlert('Error', "บันทึกข้อมูล ไม่สำเร็จ", 'error');
    unset($_SESSION['error']); // Clear the session variable
    header("refresh:2; url=/Bansuan/show_detail_complaints.php?id={$cp_cl_aid}&pcid={$cp_cl_case_id}");
}

$pdo = null;

?>