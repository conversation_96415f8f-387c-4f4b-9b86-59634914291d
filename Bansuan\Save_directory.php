<?php  //PDO
// Include the database connection file
include "../Condb.php";
include '../users.inc.php';
include '../Alert.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);


/*echo '<pre>';
print_r($_POST);
echo '</pre>';

echo '<hr';

echo '<pre>';
var_dump($_POST);
echo '</pre>';

exit();*/

// Retrieve the form data
$id = $_POST['id'];
$idcard = $_POST['idcard'];
$charge = $_POST['charge'];
$pol_arrest = $_POST['pol_arrest'];
$position_arrest = $_POST['position_arrest'];
$phone_arrest = $_POST['phone_arrest'];
$region = $_POST['region'];
$provincial = $_POST['provincial'];
$station = $_POST['station'];
$gov_name = $_POST['gov_name'];
$gov_contact = $_POST['gov_contact'];
$gov_date = $_POST['gov_date'];
$attorney_name = $_POST['attorney_name'];
$att_contact = $_POST['att_contact'];
$att_date = $_POST['att_date'];
$remark = $_POST['remark'];

// ไฟล์ PDF ฝ่ายปกครอง
$file1 = $_FILES[ 'gov_pdf' ][ 'tmp_name' ];
if ( is_uploaded_file( $file1 ) ) {
  $gov_pdf = "uploaded/PDF22/Gov_" . $_FILES[ 'gov_pdf' ][ 'name' ];	
  move_uploaded_file( $file1, "../" . $gov_pdf );  
} else {
  $gov_pdf = '';
}

// ไฟล์ PDF อัยการ
$file2 = $_FILES[ 'att_pdf' ][ 'tmp_name' ];
if ( is_uploaded_file( $file2 ) ) {
  $att_pdf = "uploaded/PDF22/Att_" . $_FILES[ 'att_pdf' ][ 'name' ];	
  move_uploaded_file( $file2, "../" . $att_pdf );  
} else {
  $att_pdf = '';
}

// Set the timezone to Thailand
date_default_timezone_set('Asia/Bangkok');

// Get the current date and time
$currentDateTime = date("Y-m-d_H-i-s");

// Save Image$image1_front // ภาพด้านหน้า
$image1_front = $_FILES['image1']['tmp_name'];
// save new image
if (is_uploaded_file($image1_front)) {
    $ext = strrchr($_FILES['image1']['name'], ".");
    $image1 = "../uploaded/Image22/Img1_" . $idcard . "_" . $currentDateTime . $ext;
    move_uploaded_file($image1_front, $image1);
} else {
    $image1 = '';
}

// Save Image$image2_back // ภาพด้านหลัง
$image2_back = $_FILES['image2']['tmp_name'];
if (is_uploaded_file($image2_back)) {
    $ext = strrchr($_FILES['image2']['name'], ".");
    $image2 = "../uploaded/Image22/Img2_" . $idcard . "_" . $currentDateTime . $ext;
    move_uploaded_file($image2_back, $image2);
} else {
    $image2 = '';
}

// Save Image$image3_right // ภาพด้านขวา
$image3_right = $_FILES['image3']['tmp_name'];
if (is_uploaded_file($image3_right)) {
    $ext = strrchr($_FILES['image3']['name'], ".");
    $image3 = "../uploaded/Image22/Img3_" . $idcard . "_" . $currentDateTime . $ext;
    move_uploaded_file($image3_right, $image3);
} else {
    $image3 = '';
}

// Save Image$image4_left // ภาพด้านซ้าย
$image4_left = $_FILES['image4']['tmp_name'];
if (is_uploaded_file($image4_left)) {
    $ext = strrchr($_FILES['image4']['name'], ".");
    $image4 = "../uploaded/Image22/Img4_" . $idcard . "_" . $currentDateTime . $ext;
    move_uploaded_file($image4_left, $image4);
} else {
    $image4 = '';
}

  // UPDATE existing data
  // INSERT new data
  $query = "INSERT INTO DirectoryDetention22 (idcard, charge, pol_arrest, position_arrest, phone_arrest, region, provincial, station, gov_name, gov_contact, gov_date, gov_pdf, attorney_name, att_contact, att_date, att_pdf, image1, image2, image3, image4, remark) 
  VALUES (:idcard, :charge, :pol_arrest, :position_arrest, :phone_arrest, :region, :provincial, :station, :gov_name, :gov_contact, :gov_date, :gov_pdf, :attorney_name, :att_contact, :att_date, :att_pdf, :image1, :image2, :image3, :image4, :remark)";
        $stmt = $pdo->prepare($query);
        $stmt->bindParam(':idcard', $idcard);
        $stmt->bindParam(':charge', $charge);
		$stmt->bindParam(':pol_arrest', $pol_arrest);
		$stmt->bindParam(':position_arrest', $position_arrest);
		$stmt->bindParam(':phone_arrest', $phone_arrest);
        $stmt->bindParam(':region', $region);
		$stmt->bindParam(':provincial', $provincial);
		$stmt->bindParam(':station', $station);
        $stmt->bindParam(':gov_name', $gov_name);
        $stmt->bindParam(':gov_contact', $gov_contact);
        $stmt->bindParam(':gov_date', $gov_date);
        $stmt->bindParam(':gov_pdf', $gov_pdf);
        $stmt->bindParam(':attorney_name', $attorney_name);
        $stmt->bindParam(':att_contact', $att_contact);
        $stmt->bindParam(':att_date', $att_date);
        $stmt->bindParam(':att_pdf', $att_pdf);
        $stmt->bindParam(':image1', $image1);
        $stmt->bindParam(':image2', $image2);
        $stmt->bindParam(':image3', $image3);
        $stmt->bindParam(':image4', $image4);
        $stmt->bindParam(':remark', $remark);
try{
    $stmt->execute();
}catch (PDOException $e) {
    echo $e->getMessage();
}
      
$ip_address = $_SERVER['REMOTE_ADDR'];
$action = "Save Directory : $idcard"; // You can set the action to a suitable value
$data = str_replace("'", "", compact_array($_POST));
log_activity($acc, $action, $data, $ip_address);


if ($stmt->rowCount() > 0) {
    $_SESSION['success'] = "Data has been saved successfully";
    showSweetAlert("Saved successfully", "บันทึกข้อมูลเรียบร้อย");
    unset($_SESSION['success']); // Clear the session variable
    header("refresh:2; url=/Bansuan/index.php?pcid={$idcard}&page=Directory");
} else {
    $_SESSION['error'] = "Error";
    showSweetAlert('Error', "บันทึกข้อมูลไม่สำเร็จ", 'error');
    $errorInfo = $stmt->errorInfo();
    echo "Error saving data: " . $errorInfo[2];
    header("refresh:2; url=/Bansuan/index.php?pcid={$idcard}&page=Directory");
}

$pdo = null;

?>
