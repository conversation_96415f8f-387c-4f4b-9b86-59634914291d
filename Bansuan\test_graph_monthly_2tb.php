<?php
// Step 1: Establish a database connection
include '../Condb.php';

// Step 2: Fetch data for performance for each month
$query = "SELECT MONTH(pf_cl_dates) as month, 
                SUM(pf_cl_drug) as drug,
                SUM(pf_cl_drug_sell) as p_drug_sell,
                SUM(pf_cl_drug_occupy) as p_drug_occupy,
                SUM(pf_cl_take_drugs) as p_take_drugs,
                SUM(pf_cl_drug_therapy) as p_drug_therapy,
                SUM(pf_cl_gun) as p_gun,
                SUM(pf_cl_gambling_case) as p_gambling,
                SUM(pf_cl_immigrant_case) as p_immigrant,
                SUM(pf_cl_techno_case) as p_techno,
                SUM(pf_cl_wanted) as p_wanted
            FROM wm_tb_performance
            GROUP BY MONTH(pf_cl_dates)";
$result = mysqli_query($conn, $query);

// Step 3: Loop through the result set and store the performance values in an array
$performance_array_drug_sell = array();
$performance_array_drug_occupy = array();
$performance_array_take_drugs = array();
$performance_array_drug_therapy = array();
$performance_array_gun = array();
$performance_array_gambling = array();
$performance_array_immigrant = array();
$performance_array_techno = array();
$performance_array_wanted = array();
while ($row = mysqli_fetch_assoc($result)) {
    $performance_array_drug_sell[] = $row['p_drug_sell'];
    $performance_array_drug_occupy[] = $row['p_drug_occupy'];
    $performance_array_take_drugs[] = $row['p_take_drugs'];
    $performance_array_drug_therapy[] = $row['p_drug_therapy'];
    $performance_array_gun[] = $row['p_gun'];
    $performance_array_gambling[] = $row['p_gambling'];
    $performance_array_immigrant[] = $row['p_immigrant'];
    $performance_array_techno[] = $row['p_techno'];
    $performance_array_wanted[] = $row['p_wanted'];
}

// Step 4: Fetch goal data for each month
$goal_query = "SELECT month, goal_value, drug_sell, drug_occupy, take_drugs, drug_therapy, gun, gambling, immigrant, techno, wanted FROM wm_tb_performance_goal";
$goal_result = mysqli_query($conn, $goal_query);

// Step 5: Loop through the goal result set and store the goal values in an array
$goal_array = array();
$goal_array_drug_sell = array();
$goal_array_drug_occupy = array();
$goal_array_take_drugs = array();
$goal_array_drug_therapy = array();
$goal_array_gun = array();
$goal_array_gambling = array();
$goal_array_immigrant = array();
$goal_array_techno = array();
$goal_array_wanted = array();
while ($row = mysqli_fetch_assoc($goal_result)) {
    $goal_array[$row['month']] = $row['goal_value'];
    $goal_array_drug_sell[$row['month']] = $row['drug_sell'];
    $goal_array_drug_occupy[$row['month']] = $row['drug_occupy'];
    $goal_array_take_drugs[$row['month']] = $row['take_drugs'];
    $goal_array_drug_therapy[$row['month']] = $row['drug_therapy'];
    $goal_array_gun[$row['month']] = $row['gun'];
    $goal_array_gambling[$row['month']] = $row['gambling'];
    $goal_array_immigrant[$row['month']] = $row['immigrant'];
    $goal_array_techno[$row['month']] = $row['techno'];
    $goal_array_wanted[$row['month']] = $row['wanted'];
}

// Step 6: Merge the goal and performance arrays into a single array
$data_array = array();
for ($i = 1; $i <= 12; $i++) {
    $data_array[] = array('Month' => date('M', mktime(0, 0, 0, $i, 1)), 
    'goal_value' => isset($goal_array[$i]) ? $goal_array[$i] : 0,
    'drug_sell' => isset($goal_array_drug_sell[$i]) ? $goal_array_drug_sell[$i] : 0,
    'drug_occupy' => isset($goal_array_drug_occupy[$i]) ? $goal_array_drug_occupy[$i] : 0,
    'take_drugs' => isset($goal_array_take_drugs[$i]) ? $goal_array_take_drugs[$i] : 0,
    'drug_therapy' => isset($goal_array_drug_therapy[$i]) ? $goal_array_drug_therapy[$i] : 0,
    'gun' => isset($goal_array_gun[$i]) ? $goal_array_gun[$i] : 0,
    'gambling' => isset($goal_array_gambling[$i]) ? $goal_array_gambling[$i] : 0,
    'immigrant' => isset($goal_array_immigrant[$i]) ? $goal_array_immigrant[$i] : 0,
    'techno' => isset($goal_array_techno[$i]) ? $goal_array_techno[$i] : 0,
    'wanted' => isset($goal_array_wanted[$i]) ? $goal_array_wanted[$i] : 0,
    'p_drug_sell' => isset($performance_array[$i-1]) ? $performance_array[$i-1] : 0, 
    'p_drug_occupy' => isset($performance_array_drug_occupy[$i-1]) ? $performance_array_drug_occupy[$i-1] : 0,
    'p_take_drugs' => isset($performance_array_take_drugs[$i-1]) ? $performance_array_take_drugs[$i-1] : 0,
    'p_drug_therapy' => isset($performance_array_drug_therapy[$i-1]) ? $performance_array_drug_therapy[$i-1] : 0,
    'p_gun' => isset($performance_array_gun[$i-1]) ? $performance_array_gun[$i-1] : 0,
    'p_gambling' => isset($performance_array_gambling[$i-1]) ? $performance_array_gambling[$i-1] : 0,
    'p_immigrant' => isset($performance_array_immigrant[$i-1]) ? $performance_array_immigrant[$i-1] : 0,
    'p_techno' => isset($performance_array_techno[$i-1]) ? $performance_array_techno[$i-1] : 0,
    'p_wanted' => isset($performance_array_wanted[$i-1]) ? $performance_array_wanted[$i-1] : 0
    );
    
}

// Step 7: Use a charting library to create a bar graph
// Here's an example using Google Charts
?>

<html>
  <head>
    <script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
    <script type="text/javascript">
      google.charts.load('current', {'packages':['corechart']});
      google.charts.setOnLoadCallback(drawChart);

      function drawChart() {
        var data = google.visualization.arrayToDataTable(<?php echo json_encode($data_array); ?>);

        var options = {
          title: 'Monthly Goals vs Performance',
          chartArea: {width: '50%'},
          hAxis: {
            title: 'Goals and Performance',
            minValue: 0
          },
          vAxis: {
            title: 'Month'
          }
        };

        var chart = new google.visualization.BarChart(document.getElementById('chart_div'));
        chart.draw(data, options);
      }
    </script>
  </head>
  <body>
    <div id="chart_div" style="width: 100%; height: 500px;"></div>
  </body>
</html>

<?php
// Step 8: Close the database connection
mysqli_close($conn);

?>