<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

$id = isset($_GET['bl_cl_aid']) ? $_GET['bl_cl_aid'] : '';

//get month from select or current month
$month = isset($_GET['month']) ? $_GET['month'] : date("m");
$year = isset($_GET['year']) ? $_GET['year'] : date("Y");

$m1 = '';
$m2 = '';
$m3 = '';
$m4 = '';
$m5 = '';
$m6 = '';
$m7 = '';
$m8 = '';
$m9 = '';
$m10 = '';
$m11 = '';
$m12 = '';
switch($month)
{
    case "01":  $m1 = "selected"; break;
    case "02": $m2 = "selected"; break;
    case "03":   $m3 = "selected"; break;
    case "04":  $m4 = "selected"; break;
    case "05": $m5 = "selected"; break;
    case "06":  $m6 = "selected"; break;
    case "07": $m7 = "selected"; break;
    case "08":  $m8= "selected"; break;
    case "09": $m9 = "selected"; break;
    case "10": $m10 = "selected"; break;
    case "11": $m11 = "selected"; break;
    case "12": $m12 = "selected"; break;
}
$current_month = $global_thai_month[0+$month];
$y2 = $year+543;

$currentDate = date('d F Y');

// สำหรับ พิมพ์หน้า ข้อมูฃ ประกาศ บรรทัด ที่ต้องการพิมพ์
//-----------------
$MAX_ROW = 18;
//-----------------

// สร้างฟังก์ชั่น เพื่อดึง หัวตาราง ไปแสดง ในหน้าต่อไป
function print_header()
{
    echo '
<div class="container body_text" align="left" style="margin-left:auto">
<table width="99%" border="1" cellspacing="1" cellpadding="1" class="table-striped table-hover table-bordered">
  <tbody>
    <tr>
	  <td height="44" style="text-align: center;">ลำดับ</td>
      <td style="text-align: center;">เลขหมายค้น / วันที่ออกหมายค้น</td>
      <td style="text-align: center;">วัน/เดือน/ปี  เวลา ที่ค้น</td>
      <td style="text-align: center;">ผลการค้น</td>
      <td style="text-align: center;">เจ้าของบ้านผู้นำตรวจค้น</td>
      <td style="text-align: center;">หน.ชุดตรวจค้น</td>
      <td style="text-align: center;">หมายเหตุ</td>
      </tr>
    ';
}

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link rel="stylesheet" href="/bootstrap/css/bootstrap-grid.css">
<title>แบบ สส.16</title>
<link rel="stylesheet" href="/bootstrap/css/bootstrap-grid.css">
<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<!-- สไตล์ สำหรับ สั่งพิมพ์ข้อมูล -->
<style>
@media only print
{
   
}
    
@media print {
body{ padding:  0; margin:  0; }
  .printId{visibility: visible;}
    
.footer { page-break-after: always;}
}
.body_text{
    font-size: 16px;
    color: black; !important
}
td{
    color: black;
}
tr{
    color: black;
}
</style>
</head>

<body>
    <!-- นำสไตล์ สำหรับ สั่งพิมพ์ข้อมูล มาใช้งาน ภายใต้ <dvi></div>-->
<div class="printId">
<div class=" h3 text-center body_text mb-4 mt-4 " role="alert" >รายละเอียดข้อมูลหมายค้น &nbsp;&nbsp;(แบบ สส.16)<br><br>ประจำเดือน <?= $current_month ?> &nbsp;<?= $y2 ?> &nbsp; <?= $name_station ?> </div>

    <br>
<div class="container body_text" align="left" style="margin-left:auto">
<table width="99%" border="1" cellspacing="1" cellpadding="1" class="table-striped table-hover table-bordered">
  <tbody class="body_text">
    <tr>
	  <td height="44" style="text-align: center;color: black">ลำดับ</td>
      <td style="text-align: center;color: black">เลขหมายค้น / วันที่ออกหมายค้น</td>
      <td style="text-align: center;color: black">วัน/เดือน/ปี  เวลา ที่ค้น</td>
      <td style="text-align: center;color: black">ผลการค้น</td>
      <td style="text-align: center;color: black">เจ้าของบ้านผู้นำตรวจค้น</td>
      <td style="text-align: center;color: black">หน.ชุดตรวจค้น</td>
      <td style="text-align: center;color: black">หมายเหตุ</td>
    </tr>
	  
<?php
$sql_bl = "SELECT
                T1.*,
                T2.station_name AS station,
                T3.ap_name_th AS amphur,
                T4.tb_name_th AS tumbon,
                T5.pv_name_th AS province,
                T6.illegal_things AS Thing,
                T7.`fp_detail` AS FindPerson,
                T8.name_leader AS leader,
                T_BSDP.`bsdp_name` AS BSDP
            FROM
                `wm_tb_blockade` AS T1 " .
                "LEFT JOIN wm_tb_police_station2 AS T2 ON T1.station = T2.station_code " .
                "LEFT JOIN amphures AS T3 ON T1.bl_cl_amphur = T3.ap_code " .
                "LEFT JOIN districts AS T4 ON T1.bl_cl_tumbon = T4.tb_id " .
                "LEFT JOIN provinces AS T5 ON T1.bl_cl_province = T5.pv_code " .
                "LEFT JOIN `police_name_bsdetective` AS T_BSDP ON T1.`bl_cl_record` = T_BSDP.`aid_bsdp` " .  // ชุดสืบสวน
                "LEFT JOIN wm_tb_blockade_illegal_things AS T6 ON T1.bl_cl_illegal_things = T6.aid_things " .
                "LEFT JOIN wm_tb_blockade_find_person AS T7 ON T1.`bl_cl_find_person` = T7.`fp_aid` " .
                "LEFT JOIN police_name_bs_leader AS T8 ON T1.`bl_cl_leader` = T8.`aid_leader`
            WHERE
                station='$station' AND `bl_cl_method`='ขอหมายค้น' AND MONTH(bl_cl_date)= :month AND YEAR(bl_cl_date)= :year
            ORDER BY
                bl_cl_num ASC ";  //ตั้งตัวแปร sql เลือกฐานข้อมูลจากตารางที่ชื่อ wm_tb_personal
                      
        $stmt = $pdo->prepare($sql_bl);
        $stmt->bindParam(':month', $month);
        $stmt->bindParam(':year', $year);
    try{
        $stmt->execute();
    }catch (PDOException $e) {
        echo '$sql_bl Failed: '. $e->getMessage();
    }

// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no = 1;
while($row_bl = $stmt->fetch(PDO::FETCH_ASSOC))	//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
{
     $id = $row_bl['bl_cl_aid'];  
    
    //ฟังก์ชั่น วันที่ ดึงจาก condb
    $strDate = DateThai( $row_bl["bl_cl_date"] );
    
    //ตรวจสอบไฟล์ สส.1 ก่อนว่า มีไฟล์ในตารางไหม หากมี ให้ส่งเป็นลิงค์
	$pdf = $row_bl["bl_cl_file"];
	if($pdf !== '')
	{
		$links = '<a href="/' . $row_bl["bl_cl_file"] .'" target="_blank">Download</a>';
	}
	else {
		$links = 'ไม่มีไฟล์';
	}
    
?>	

    <tr>
	  <td> <?= $no ?> </td>
      <td><?= $row_bl["bl_cl_search_warrant"]?></td>
      <td nowrap>&nbsp;<?= $strDate ?>&nbsp;</td>
      <td>&nbsp;&nbsp;
        <?= $row_bl["bl_cl_find_illegal"]?> / <?= $row_bl["FindPerson"]?> /<?= $row_bl["Thing"]?> / <?= $row_bl["bl_cl_remark"]?></td>
      <td>&nbsp;<?= $row_bl["bl_cl_conduct"]?>&nbsp;</td>
      <td>&nbsp;<?= $row_bl["leader"]?>&nbsp;</td>
      <td>&nbsp;&nbsp;</td>
      </tr>
  </tbody>
<?php
    $no++;
          
        // กำหนด คุณสมบัติของหน้าที่จะส่งไปพิมพ์
         // if(($no % ($row_per_page + 1)) == 0) {
         if(($no % ($MAX_ROW)) == 0) {
            echo "</tbody></table></div>";
            
              echo "<div class='footer'>&nbsp;</div> "; // สิ้นสุดการพิม 1 หน้า
            
              print_header();
        }
}//while
?>
</table>
</div>
    
<script>
function Del(url)
{
	if(confirm("คุณจะลบข้อมูล หรือ ไม่ ?"))
	{
		window.location = url;		
	}
}
function dates_change()  // คำสั่งเปลี่ยนเดือน ปี
{
    var ms = $("#changemonth option:selected").val();
    var ys = $("#changeyear option:selected").val();
    window.location = "/Activity/Show_Activity3_SS16.php?&month=" + ms + "&year=" + ys + "&rnd=" + Math.random();
}

</script>
<br>
    <div align="center">
       <?php
       if ($station == 6707) {
		   echo '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ตรวจแล้วรับรองว่าถูกต้อง<br>
        <br>
        	&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;พ.ต.ท.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br>
		   &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br>
			';
	   } else {
		   echo '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ตรวจแล้วรับรองว่าถูกต้อง<br>
        <br>
        	&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;พ.ต.ท.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br><br>
		   &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br>
			';
	   }
		
	?>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;หน.งานสืบสวน <?= $name_station ?> <br><br>
        
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<?php echo "พิมพ์เอกสารเมื่อ " . DateThaiFull($currentDate) . "<br>"; ?>&nbsp;&nbsp;<br>
    </div>
</body>
</html>