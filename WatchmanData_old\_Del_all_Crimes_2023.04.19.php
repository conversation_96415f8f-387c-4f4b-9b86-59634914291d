<?php
include '../Condb.php';
include '../users.inc.php';
include '../Alert.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);
$ip_address = $_SERVER['REMOTE_ADDR'];
$action = 'Del all Crimes'; // You can set the action to a suitable value
$data = str_replace("'", "", compact_array($_POST));
log_activity($acc, $action, $data, $ip_address); // ใช้ function ในไฟล์ Condb.php


$id = $_GET['id'];
$pcid = $_GET['pcid'];

/*if($user['ua_delete_data'] != 100)
{
    header("Location: /WatchmanData/Show_All.php?pcid=$pcid&page=crimes");
}*/

//print_r($id);
//exit;
// Store $pcid in a session variable
$_SESSION['pcid'] = $pcid;

$sql="DELETE FROM wm_tb_crimes_history WHERE ch_cl_aid='$id' ";
$result = mysqli_query($conn, $sql);
mysqli_report(MYSQLI_REPORT_ERROR | MYSQLI_REPORT_STRICT);
if ($result) {
    $_SESSION['success'] = "Data has been deleted successfully";
    showSweetAlert("Delete successfully", "ลบข้อมูลเรียบร้อย");
    unset($_SESSION['success']); // Clear the session variable
    header("refresh:2; url=/Bansuan/home_visit.php?id={$id}&pcid={$pcid}");
} else {
    $_SESSION['error'] = "ลบข้อมูลไม่สำเร็จ";
}

if ($result) {
    $_SESSION['success'] = "Data has been deleted successfully";
    echo "<script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js' type='text/javascript'></script>";
    echo "<script src='../SweetAlert2/dist/sweetalert2.all.min.js'></script>";
    echo "<script>
        $(document).ready(function() {
            Swal.fire({
                title: 'Delete successfully',
                text: 'ลบข้อมูลเรียบร้อย',
                icon: 'success',
                timer: 5000,
                showConfirmButton: false
            });
        })
    </script>";
    // Unset the 'success' session variable
    unset($_SESSION['success']);
    header("refresh:2; url=/WatchmanData/Show_All.php?pcid={$pcid}&page=crimes");
} else {
    $_SESSION['error'] = "ลบข้อมูล ไม่สำเร็จ";
    echo "<script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js' type='text/javascript'></script>";
    echo "<script src='../SweetAlert2/dist/sweetalert2.all.min.js'></script>";
    echo "<script>
        $(document).ready(function() {
            Swal.fire({
                title: 'Error',
                text: 'เกิดข้อผิดพลาดในการลบข้อมูล',
                icon: 'error',
                timer: 5000,
                showConfirmButton: false
            });
        })
    </script>";
    // Unset the 'error' session variable
    unset($_SESSION['error']);
    header("refresh:2; url=/WatchmanData/Show_All.php?pcid={$pcid}&page=crimes");
}

mysqli_close($conn);

?>
