<?php


?>


<link rel="stylesheet" href="/policeinnopolis/bootstrap/css/bootstrap.rtl.min.css">
<!-- สำหรับ วันที่ แบบ พ.ศ. -->
<link rel="stylesheet" href="http://code.jquery.com/ui/1.9.1/themes/base/jquery-ui.css" />
<script src="http://code.jquery.com/jquery-1.8.2.js"></script>
<script src="/policeinnopolis/js/jquery-ui.js"></script>
<script src="../SweetAlert2/dist/sweetalert2.all.min.js"></script>
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 18px;
    }
input::placeholder {
  color: red;
}
.form-control::placeholder {
  color: sandybrown;
}
	.callcenter {
		width: 20px;
		height: 20px;
	}
</style>
<script>
window.addEventListener('DOMContentLoaded', function() {
  // Get references to the checkboxes
  var checkbox1 = document.getElementById('callcenter1');
  var checkbox2 = document.getElementById('callcenter2');
  
  // Set the default value
  checkbox1.checked = true;
  
  // Add an event listener to checkboxes to manage their state
  checkbox1.addEventListener('change', function() {
    if (checkbox1.checked) {
      checkbox2.checked = false;
    }
  });
  
  checkbox2.addEventListener('change', function() {
    if (checkbox2.checked) {
      checkbox1.checked = false;
    }
  });
});
</script>
</head>


		<label>หมายเลขโทรศัพท์ <span style="color: #F90004">* จำเป็น</span></label>
		<input type = "text" name = "ph_cl_phone" class="form-control" autocomplete="off" placeholder="กรอกหมายเลขโทรศัพท์ 10 หลัก"   Required  >
		<label>เครือข่ายโทรศัพท์</label>
		<input type = "text" name = "ph_cl_operators" class="form-control" placeholder="กรอกข้อมูลเครือข่าย เช็คโดย DTAC กด *812*เบอร์# โทรออก"  >
		<label>ประเภท</label>
		<input type = "text" name = "ph_cl_type" class="form-control" placeholder="กรอกข้อมูลประเภท รายเดือน/เติมเงิน"  >
		<label>วันจดทะเบียน</label>
		<input type = "text" name = "ph_cl_regist" class="form-control" placeholder="กรอกข้อมูลวันที่จดทะเบียน จำนวนวันที่ใช้งาน"  >
		<label>การใช้งาน</label>
		<input type = "text" name = "ph_cl_status" class="form-control" placeholder="กรอกข้อมูลสถานะใช้งาน เปิด ปิด ระงับ ยกเลิก โอน การใช้บริการอื่นๆ"  >
		<label>ยี่ห้อ รุ่น สี</label>
		<input type = "text" name = "ph_cl_brand" class="form-control" placeholder="กรอกข้อมูลยี่ห้อโทรศัพท์ รุ่น สี"  >
		<label>เลข IMEI 1</label>
		<input type = "text" name = "ph_cl_IMEI" class="form-control" placeholder="กรอกข้อมูลเลข IMEI เช็คอีมี่โดยกด *#06#"  >
		<label>เลข IMEI 2</label>
		<input type = "text" name = "ph_cl_IMEI2" class="form-control" placeholder="กรอกข้อมูลเลข IMEI 2 (ถ้ามี)"  >
		<label>เลข IMSI</label>
		<input type = "text" name = "ph_cl_IMSI" class="form-control" placeholder="กรอกข้อมูลเลข IMSI ของซิมการ์ด"  >
		<label>คนจดทะเบียน</label>
		<input type = "text" name = "ph_cl_register_name" class="form-control" placeholder="กรอกข้อมูล ชื่อคนจดทะเบียน"  >
		<label>บัตรคนจด</label>
		<input type = "text" name = "ph_cl_register_idcard" class="form-control" placeholder="กรอกข้อมูล เลขบัตรคนจด"  >
		<label>เบส (LAC,CI)</label>
		<input type = "text" name = "ph_cl_base" class="form-control" placeholder="กรอกข้อมูล เบส (LAC,CI)"  >
		<label>ที่อยู่/ตำแหน่งเบส</label>
		<input type = "text" name = "ph_cl_base_location" class="form-control" placeholder="กรอกข้อมูล ที่อยู่/ตำแหน่งเบส"  >
		<label>ที่อยู่ส่งบิล</label>
		<textarea class="form-control" id="ph_cl_billing" name="ph_cl_billing"  rows="5" placeholder="กรอกข้อมูล ที่อยู่ส่งบิล" ></textarea>
		<label>พร้อมเพย์</label>
		<input type = "text" name = "ph_cl_promtpay" class="form-control" placeholder="กรอกข้อมูล พร้อมเพย์" >
		<label>ข้อมูลพัสดุ</label>
		<textarea class="form-control" id="ph_cl_logistic" name="ph_cl_logistic"  rows="5" placeholder="กรอกข้อมูล พัสดุ" ></textarea>
		<label>ทรูวอลเล็ต</label>
		<input type = "text" name = "ph_cl_truewallet" class="form-control" placeholder="กรอกข้อมูล ทรูวอลเล็ต" >
		<label>ออลเมมเบอร์</label>
		<input type = "text" name = "ph_cl_allmember" class="form-control" placeholder="กรอกข้อมูล ออลเมมเบอร์" >
		<label>Get contact</label>
		<input type = "text" name = "ph_cl_getcontact" class="form-control" placeholder="กรอกข้อมูล Get contact" >
		<label>ITEC</label>
		<input type = "text" name = "ph_cl_ITEC" class="form-control" placeholder="กรอกข้อมูล ITEC" ><br>
		<b>เป็นเบอร์ของแก๊ง Call center หรือไม่</b>
		&nbsp;&nbsp;&nbsp;&nbsp;<label><input type="checkbox" name="callcenter" id="callcenter1" class="callcenter" value="1" > ไม่เป็น </label>&nbsp;&nbsp;&nbsp;&nbsp;
		<label><input type="checkbox" name="callcenter" id="callcenter2" class="callcenter" value="2" > เป็น </label>
		<br><br>
        <label>วันตรวจสอบ/บันทึก <span style="color: #F90004">* จำเป็น</span> </label>
        <p><input type="text" name="ph_ch_date_inspect" id="datepicker" placeholder="เพื่อข้อมูลถูกต้อง กรุณาเลือกจากปฎิทินด้านล่าง" class="form-control"   autocomplete="off" Required></p>
        <script>
            function set_cal(ele)//function สร้างตัวเลือกปฎิทิน
            {
              $( ele ).datepicker({
                  onSelect:(date_text)=>
                  {
                    let arr=date_text.split("/");
                    let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])+543).toString();
                    $(ele).val(new_date);
                    $(ele).css("color","");
                  },
                  beforeShow:()=>{

                    if($(ele).val()!="")
                    {
                      let arr=$(ele).val().split("/");
                      let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])-543).toString();
                      $(ele).val(new_date);

                    }

                    $(ele).css("color","white");
                  },
                  onClose:()=>{

                      $(ele).css("color","");

                      if($(ele).val()!="")
                      {
                          let arr=$(ele).val().split("-");
                          if(parseInt(arr[2])<2500)
                          {
                              let new_date=arr[0]+"-"+arr[1]+"-"+(parseInt(arr[2])).toString();
                              $(ele).val(new_date);
                          }
                      }
                  },
                  dateFormat:"yy-mm-dd", //กำหนดรูปแบบวันที่เป็น วัน/เดือน/ปี
                  changeMonth:true,//กำหนดให้เลือกเดือนได้
                  changeYear:true,//กำหนดให้เลือกปีได้
                  showOtherMonths:true,//กำหนดให้แสดงวันของเดือนก่อนหน้าได้
              });
            }
          $(document).ready(function(){
            //เรียก function set_cal เมื่อเปิดหน้าเว็บ โดยส่ง object element ที่มี id เป็น datepicker เป็นพารามิเตอร์
            set_cal( $("#datepicker") );
          })
          </script>
        <!-- สำหรับ วันที่ แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ -->
        
		<label>หมายเหตุ</label>
        <textarea class="form-control" id="ph_cl_remark" name="ph_cl_remark"  rows="5" placeholder="ระบุข้อมูลแหล่งที่มา หรืออื่น ๆที่เกี่ยวข้อง" ></textarea>
	 	<br>

