<?php
include '../Condb.php';
$id = $_GET['id'];

$sql = "SELECT * FROM wm_tb_gambling_football WHERE wc_cl_aid = '$id' ";
$result = mysqli_query($conn,$sql);
$row = mysqli_fetch_array($result);

//print_r($id);
?>


<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>แก้ไขข้อมูลผลการปฏิบัติ Wolrd Cup 2022</title>
<script src="/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
</style>

<script src="/jquery-ui-1.12.1/jquery-ui.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/jquery-ui-1.12.1/jquery-ui.css">
<link rel="stylesheet" href="/js/datetimepicker2.5.1/jquery.datetimepicker.min.css">
<script src="/js/datetimepicker2.5.1/jquery.datetimepicker.full.js" type="text/javascript"></script>    
<script src="/js/utils.js?<?= rand() ?>" type="text/javascript"></script>    
</head>

<body>
	
	<div class="container">
	<div class="row">
	<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> แก้ไขข้อมูลผลการปฏิบัติงาน Wolrd Cup 2022</div>
	<form action="Save_worldcup2022_Edit.php" method="POST" enctype="multipart/form-data">
        
        <label hidden ="hidden">ลำดับ</label>
		<input type = "text" name = "wc_cl_aid" class="form-control" value= "<?= $row['wc_cl_aid']?>" hidden ="hidden" >
			        
		<label>วันเดือนปีที่จับกุม<span style="color: #F90004">* จำเป็น</span></label>
		<input type = "text" name="wc_cl_date" class="form-control datepicker" value= "<?= $row['wc_cl_date']?>" placeholder="ระบุวันที่จับกุม" autocomplete="off" required>
        
        <label>เลขบัตรผู้ต้องหา<span style="color: #F90004">* จำเป็น</span></label> 
		<input type = "text" name = "wc_cl_idcard" class="form-control" value= "<?= $row['wc_cl_idcard']?>" placeholder="เลขบัตรผู้ต้องหา" >
        
        <label>ชื่อผู้ต้องหา</label>
		<input type = "text" name = "wc_cl_name" class="form-control" value= "<?= $row['wc_cl_name']?>" placeholder="ชื่อผู้ต้องหา" >
		<hr color="#F8060A">
		<br>
        <b>พนันทายผล</b>
        <label>การแข่งขันฟุตบอล</label>
		<select id="wc_cl_football_match" name="wc_cl_football_match" class="form-select form-select-sm" aria-label=".form-select-sm example" placeholder="การแข่งขันฟุตบอล">
			<option value="" selected> </option>
			<option value="World Cub 2022">World Cub 2022</option>
		</select>
        
        <label>เจ้ามือ (ราย)</label>
		<select id="wc_cl_dealer_case" name="wc_cl_dealer_case" class="form-select form-select-sm" aria-label=".form-select-sm example" placeholder="ระบุจำนวน ราย">
			<option value="0" selected> </option>
			<option value="1">1</option>
			<option value="2">2</option>
			<option value="3">3</option>
			<option value="4">4</option>
			<option value="5">5</option>
		</select>
        
        <label>เจ้ามือ (จำนวนคน)</label>
		<select id="wc_cl_dealer_person" name="wc_cl_dealer_person" class="form-select form-select-sm" aria-label=".form-select-sm example" placeholder="ระบุจำนวน คน">
			<option value="0" selected> </option>
			<option value="1">1</option>
			<option value="2">2</option>
			<option value="3">3</option>
			<option value="4">4</option>
			<option value="5">5</option>
		</select>
		
        <label>ผู้เล่น (ราย)</label>
		<select id="wc_cl_players_case" name="wc_cl_players_case" class="form-select form-select-sm" aria-label=".form-select-sm example" placeholder="ระบุจำนวน ราย">
			<option value="0" selected> </option>
			<option value="1">1</option>
			<option value="2">2</option>
			<option value="3">3</option>
			<option value="4">4</option>
			<option value="5">5</option>
		</select>
		
		<label>ผู้เล่น (คน)</label>
		<select id="wc_cl_players_person" name="wc_cl_players_person" class="form-select form-select-sm" aria-label=".form-select-sm example" placeholder="ระบุจำนวน คน">
			<option value="0" selected> </option>
			<option value="1">1</option>
			<option value="2">2</option>
			<option value="3">3</option>
			<option value="4">4</option>
			<option value="5">5</option>
		</select>
        
        <label>เดินโพย (ราย)</label>
		<select id="wc_cl_walker_case" name="wc_cl_walker_case" class="form-select form-select-sm" aria-label=".form-select-sm example" placeholder="ระบุจำนวน ราย">
			<option value="0" selected> </option>
			<option value="1">1</option>
			<option value="2">2</option>
			<option value="3">3</option>
			<option value="4">4</option>
			<option value="5">5</option>
		</select>
        
        <label>เดินโพย (คน)</label>
		<select id="wc_cl_walker_person" name="wc_cl_walker_person" class="form-select form-select-sm" aria-label=".form-select-sm example" placeholder="ระบุจำนวน คน">
			<option value="0" selected> </option>
			<option value="1">1</option>
			<option value="2">2</option>
			<option value="3">3</option>
			<option value="4">4</option>
			<option value="5">5</option>
		</select>
		
		<br>
		<hr color="#F8060A">
		<br>
		
        <b>พนันทายผลผ่านสื่ออินเทอร์เน็ต</b>
        <label>เจ้ามือ (ราย)</label>
		<select id="wc_cl_dealer_net_case" name="wc_cl_dealer_net_case" class="form-select form-select-sm" aria-label=".form-select-sm example" placeholder="ระบุจำนวน ราย">
			<option value="0" selected> </option>
			<option value="1">1</option>
			<option value="2">2</option>
			<option value="3">3</option>
			<option value="4">4</option>
			<option value="5">5</option>
		</select>
        
        <label>เจ้ามือ (จำนวนคน)</label>
		<select id="wc_cl_dealer_net_person" name="wc_cl_dealer_net_person" class="form-select form-select-sm" aria-label=".form-select-sm example" placeholder="ระบุจำนวน คน">
			<option value="0" selected> </option>
			<option value="1">1</option>
			<option value="2">2</option>
			<option value="3">3</option>
			<option value="4">4</option>
			<option value="5">5</option>
		</select>
		
        <label>ผู้เล่น (ราย)</label>
		<select id="wc_cl_players_net_case" name="wc_cl_players_net_case" class="form-select form-select-sm" aria-label=".form-select-sm example" placeholder="ระบุจำนวน ราย">
			<option value="0" selected> </option>
			<option value="1">1</option>
			<option value="2">2</option>
			<option value="3">3</option>
			<option value="4">4</option>
			<option value="5">5</option>
		</select>
		
		<label>ผู้เล่น (คน)</label>
		<select id="wc_cl_players_net_person" name="wc_cl_players_net_person" class="form-select form-select-sm" aria-label=".form-select-sm example" placeholder="ระบุจำนวน คน">
			<option value="0" selected> </option>
			<option value="1">1</option>
			<option value="2">2</option>
			<option value="3">3</option>
			<option value="4">4</option>
			<option value="5">5</option>
		</select>
		
		<br>
		<hr color="#F8060A">
		<br>
		
        <b>ของกลาง</b>
		<label>โพยบอล จำนวน(ใบ)</label>
		<select id="wc_cl_poiball" name="wc_cl_poiball" class="form-select form-select-sm" placeholder="ระบุจำนวนโพยบอล (ใบ)">
			<option value="0" selected> </option>
			<option value="1">1</option>
			<option value="2">2</option>
			<option value="3">3</option>
			<option value="4">4</option>
			<option value="5">5</option>
			<option value="6">6</option>
			<option value="7">7</option>
			<option value="8">8</option>
			<option value="9">9</option>
			<option value="10">10</option>
		</select>
        
        <label>จำนวนมูลค่าเงิน (บาท) กรอกเฉพาะตัวเลข</label> </span>
		<input type = "text" name = "wc_cl_bath" class="form-control" value= "<?= $row['wc_cl_bath']?>" placeholder="จำนวนเงิน (บาท) เฉพาะตัวเลขเท่านั้น ไม่ต้องใส่คอมม่า ไม่มีข้อมูล ให้ใส่เลข 0" required >
        
        <label>จำนวนเงินสด (บาท) กรอกเฉพาะตัวเลข</label> </span>
		<input type = "text" name = "wc_cl_cash" class="form-control" value= "<?= $row['wc_cl_cash']?>" placeholder="จำนวนเงินสด (บาท) เฉพาะตัวเลขเท่านั้น ไม่ต้องใส่คอมม่าไม่มีข้อมูล ให้ใส่เลข 0" required >
        
        <label>อื่น ๆ</label> </span>
		<input type = "text" name = "wc_cl_other" class="form-control" value= "<?= $row['wc_cl_other']?>" placeholder="อื่น ๆ" >
    
        <label>หมายเหตุ</label> </span>
		<input type = "text" name = "wc_cl_remark" class="form-control" value= "<?= $row['wc_cl_remark']?>" placeholder="หมายเหตุ" >
		
		<br>
		<hr color="#F8060A">
		<br>
        
		<label>ผู้บันทึกข้อมูล</label>
		<select id="wc_cl_record" name="wc_cl_record" class="form-select form-select-sm" placeholder="ระบุผู้บันทึกข้อมูล">
			<option value="" selected> </option>
            <option value="ส.ต.ต.นนทกานต์ ขังทัศน์">ส.ต.ต.นนทกานต์ ขังทัศน์</option>
			<option value="ด.ต.จักรพงศ์ สุริยะคำวงศ์">ด.ต.จักรพงศ์ สุริยะคำวงศ์</option>
			<option value="ด.ต.วีระ อินตาโสภี">ด.ต.วีระ อินตาโสภี</option>
			<option value="ด.ต.ประชัน ทองดี">ด.ต.ประชัน ทองดี</option>
			<option value="ร.ต.ต.ปัญญา ศรีวรเดช">ร.ต.ต.ปัญญา ศรีวรเดช</option>
			<option value="ร.ต.อ.พิทยา อ่วมเหล็ง">ร.ต.อ.พิทยา อ่วมเหล็ง</option>
			<option value="ร.ต.อ.วุฒิไกร สายทอง">ร.ต.อ.วุฒิไกร สายทอง</option>
			<option value="ร.ต.อ.ณัฐพงษ์ ภู่ทอง">ร.ต.อ.ณัฐพงษ์ ภู่ทอง</option>
			<option value="พ.ต.ท.รัตนพัฒน์ ลำพูน">พ.ต.ท.รัตนพัฒน์ ลำพูน</option>
			<option value="พ.ต.ท.ณัทรภณ ทรงไทย">พ.ต.ท.ณัทรภณ ทรงไทย</option>
		</select>
		
		<p>
		<br>
			<input type="submit" value="บันทึกข้อมูล" class="btn btn-success" >
			<td> <a href="Show_worldcup2022.php?rnd=<?= rand(); ?>" class="btn btn-warning" >ยกเลิก</a> </td>
		</p>
		<br>
		<br>
	</form>
	</div>
	</div>
	</div>
<script src="/js/datetimepicker2.5.1/jquery.ui.datepicker-th.js" type="text/javascript"></script>
<script>
// ชื่อ >> value >> set selct auto (สคลิปตรวจสอบ Selected) จำเป็นต้องก็อปปี้ <script src="/jQuery/jquery-3.5.1.min.js" type="text/javascript">
function auto_select(sid, value)
{
    $('#' + sid).val(value);
}

function auto_thaidate(sid, value)
{
	if(value != '') {
		var dates = value.split('-');
		dates = value.split('-');
		if(dates.length > 2) {
			var ys = dates[0];
			var ms = dates[1];
			var ds = dates[2];
			if(ys.substr(0,2) == '20') {
				ys = parseInt(ys) + 543;
			}
			value = $.digit(ds,2) + '/' + $.digit(ms,2) + '/' +ys;
			$('#' + sid).val(value);
		}
	}
}

$(document).ready(function() {
<?php
    echo "auto_select('wc_cl_football_match', '{$row['wc_cl_football_match']}');\n";
    echo "auto_select('wc_cl_dealer_case', '{$row['wc_cl_dealer_case']}');\n";
    echo "auto_select('wc_cl_dealer_person', '{$row['wc_cl_dealer_person']}');\n";
    echo "auto_select('wc_cl_players_case', '{$row['wc_cl_players_case']}');\n";
    echo "auto_select('wc_cl_players_person', '{$row['wc_cl_players_person']}');\n";
    echo "auto_select('wc_cl_walker_case', '{$row['wc_cl_walker_case']}');\n";
    echo "auto_select('wc_cl_walker_person', '{$row['wc_cl_walker_person']}');\n";
    echo "auto_select('wc_cl_dealer_net_case', '{$row['wc_cl_dealer_net_case']}');\n";
    echo "auto_select('wc_cl_dealer_net_person', '{$row['wc_cl_dealer_net_person']}');\n";
    echo "auto_select('wc_cl_players_net_case', '{$row['wc_cl_players_net_case']}');\n";
    echo "auto_select('wc_cl_players_net_person', '{$row['wc_cl_players_net_person']}');\n";
    echo "auto_select('wc_cl_poiball', '{$row['wc_cl_poiball']}');\n";
    echo "auto_select('wc_cl_record', '{$row['wc_cl_record']}');\n";
	// convert db date to thai dates
	echo "auto_thaidate('wc_cl_date', '{$row['wc_cl_date']}');\n";
?>
    });
</script>
</body>
</html>