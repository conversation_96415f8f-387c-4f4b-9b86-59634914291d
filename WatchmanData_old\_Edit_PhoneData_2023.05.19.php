<?php
include '../Condb.php';
include '../users.inc.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

//$id = $_GET['ph_cl_aid'];
$pcid = $_GET['pcid']; 

$sql = "SELECT * FROM wm_tb_phone WHERE ph_cl_phone = '$pcid' ";
$result = mysqli_query($conn,$sql);
$row = mysqli_fetch_array($result);
?>


<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link rel="icon" type="image/x-icon" href="/favicon.ico">
<title>เพิ่มข้อมูลโทรศัพท์เข้าฐานข้อมูล</title>
<script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/policeinnopolis/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
</style>
<!-- สำหรับ วันที่ แบบ พ.ศ. -->
<link rel="stylesheet" href="http://code.jquery.com/ui/1.9.1/themes/base/jquery-ui.css" />
<script src="http://code.jquery.com/jquery-1.8.2.js"></script>
<script src="/policeinnopolis/js/jquery-ui.js"></script>
</head>

<body>
	<div class="container">
	<div class="row">
		<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> แก้ไขข้อมูลโทรศัพท์ทั่วไป เก็บในฐานข้อมูล</div>
	<form action="Save_all_PhoneData_Edit.php" method="POST" enctype="multipart/form-data">
        
        <label hidden ="hidden">ลำดับ</label>
		<input type = "text" name = "ph_cl_aid" value= "<?=$row['ph_cl_aid']?>" class="form-control"  hidden ="hidden" >
        
		<label>เลขบัตรประชาชน</label>
		<input type = "text" name="ph_cl_idcard" value= "<?=$row['ph_cl_idcard']?>" class="form-control" placeholder="กรอกเลขบัตรประชาชน 13 หลัก"  >
		<label>หมายเลขโทรศัพท์ <span style="color: #F90004">* จำเป็น</span></label>
		<input type = "text" name = "ph_cl_phone" value= "<?=$row['ph_cl_phone']?>" class="form-control" autocomplete="off" placeholder="กรอกหมายเลขโทรศัพท์ 10 หลัก"   Required  >
		<label>เครือข่ายโทรศัพท์</label>
		<input type = "text" name = "ph_cl_operators" value= "<?=$row['ph_cl_operators']?>" class="form-control" placeholder="กรอกข้อมูลเครือข่าย เช็คโดย DTAC กด *812*เบอร์# โทรออก"  >
		<label>ประเภท</label>
		<input type = "text" name = "ph_cl_type" value= "<?=$row['ph_cl_type']?>" class="form-control" placeholder="กรอกข้อมูลประเภท รายเดือน/เติมเงิน"  >
		<label>วันจดทะเบียน</label>
		<input type = "text" name = "ph_cl_regist" value= "<?=$row['ph_cl_regist']?>" class="form-control" placeholder="กรอกข้อมูลวันที่จดทะเบียน จำนวนวันที่ใช้งาน"  >
		<label>การใช้งาน</label>
		<input type = "text" name = "ph_cl_status" value= "<?=$row['ph_cl_status']?>" class="form-control" placeholder="กรอกข้อมูลสถานะใช้งาน เปิด ปิด ระงับ ยกเลิก โอน การใช้บริการอื่นๆ"  >
		<label>ยี่ห้อ รุ่น</label>
		<input type = "text" name = "ph_cl_brand" value= "<?=$row['ph_cl_brand']?>" class="form-control" placeholder="กรอกข้อมูลยี่ห้อโทรศัพท์ รุ่น สี"  >
		<label>เลข IMEI</label>
		<input type = "text" name = "ph_cl_IMEI" value= "<?=$row['ph_cl_IMEI']?>" class="form-control" placeholder="กรอกข้อมูลเลข IMEI เช็คอีมี่โดยกด *#06#"  >
		<label>เลข IMSI</label>
		<input type = "text" name = "ph_cl_IMSI" value= "<?=$row['ph_cl_IMSI']?>" class="form-control" placeholder="กรอกข้อมูลเลข IMSI ของซิมการ์ด"  >
		<label>คนจดทะเบียน</label>
		<input type = "text" name = "ph_cl_register_name" value= "<?=$row['ph_cl_register_name']?>" class="form-control" placeholder="กรอกข้อมูล ชื่อคนจดทะเบียน"  >
		<label>บัตรคนจด</label>
		<input type = "text" name = "ph_cl_register_idcard" value= "<?=$row['ph_cl_register_idcard']?>" class="form-control" placeholder="กรอกข้อมูล เลขบัตรคนจด"  >
		<label>เบส (LAC,CI)</label>
		<input type = "text" name = "ph_cl_base" value= "<?=$row['ph_cl_base']?>" class="form-control" placeholder="กรอกข้อมูล เบส (LAC,CI)"  >
		<label>ที่อยู่/ตำแหน่งเบส</label>
		<input type = "text" name = "ph_cl_base_location" value= "<?=$row['ph_cl_base_location']?>" class="form-control" placeholder="กรอกข้อมูล ที่อยู่/ตำแหน่งเบส"  >
        
        <label>วันตรวจสอบ <span style="color: #F90004">* จำเป็น</span> </label>
        <p><input type="text" name="ph_ch_date_inspect" id="datepicker" value= "<?=$row['ph_ch_date_inspect']?>" class="form-control" placeholder="เลือกวันที่ตรวจสอบข้อมูล" autocomplete="off" Required></p>
        <script>
            function set_cal(ele)//function สร้างตัวเลือกปฎิทิน
            {
              $( ele ).datepicker({
                  onSelect:(date_text)=>
                  {
                    let arr=date_text.split("/");
                    let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])+543).toString();
                    $(ele).val(new_date);
                    $(ele).css("color","");
                  },
                  beforeShow:()=>{

                    if($(ele).val()!="")
                    {
                      let arr=$(ele).val().split("/");
                      let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])-543).toString();
                      $(ele).val(new_date);

                    }

                    $(ele).css("color","white");
                  },
                  onClose:()=>{

                      $(ele).css("color","");

                      if($(ele).val()!="")
                      {
                          let arr=$(ele).val().split("-");
                          if(parseInt(arr[2])<2500)
                          {
                              let new_date=arr[0]+"-"+arr[1]+"-"+(parseInt(arr[2])).toString();
                              $(ele).val(new_date);
                          }
                      }
                  },
                  dateFormat:"yy-mm-dd", //กำหนดรูปแบบวันที่เป็น วัน/เดือน/ปี
                  changeMonth:true,//กำหนดให้เลือกเดือนได้
                  changeYear:true,//กำหนดให้เลือกปีได้
                  showOtherMonths:true,//กำหนดให้แสดงวันของเดือนก่อนหน้าได้
              });
            }
          $(document).ready(function(){
            //เรียก function set_cal เมื่อเปิดหน้าเว็บ โดยส่ง object element ที่มี id เป็น datepicker เป็นพารามิเตอร์
            set_cal( $("#datepicker") );
          })
          </script>
        <!-- สำหรับ วันที่ แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ -->
        
		<label>หมายเหตุ</label>
        <textarea class="form-control" id="ph_cl_remark" name="ph_cl_remark"  rows="5" placeholder="ระบุข้อมูลแหล่งที่มา หรืออื่น ๆที่เกี่ยวข้อง" ><?php echo $row['ph_cl_remark'] ?></textarea>
	 	<br>
			<p>
			<input type="submit" value="บันทึกข้อมูล" class="btn btn-success" >
			<td> <a href="main.php?pcid=<?= rand() ?>&page=phone" class="btn btn-warning" >ยกเลิก</a> </td>
			</p>
		<br>
		<br>
	</form>
	</div>
	</div>
	</div>
<!--<script src="/js/datetimepicker2.5.1/jquery.ui.datepicker-th.js" type="text/javascript"></script>-->
</body>
</html>