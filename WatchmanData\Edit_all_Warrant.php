<?php
include "../Condb.php"; //PDO
include '../users.inc.php';
include '../right_user.php';


// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

$id = $_GET['id'];

$sql = "SELECT * FROM wm_tb_warrant WHERE wr_cl_aid=:id";
$stmt = $pdo->prepare($sql);
$stmt->bindValue(':id', $id);
$stmt->execute();
$row = $stmt->fetch(PDO::FETCH_ASSOC);

$people_name = '';
if ($row) {
    $pcid2 = $row["wr_cl_idcard"];

    if (!empty($pcid2)) {
        $query1 = "SELECT ps_cl_prefix, ps_cl_name, ps_cl_surname, ps_cl_image FROM wm_tb_personal WHERE ps_cl_idcard = :pcid2";
        $stmt2 = $pdo->prepare($query1);
        $stmt2->bindValue(':pcid2', $pcid2);
        $stmt2->execute();
        $row1 = $stmt2->fetch(PDO::FETCH_ASSOC);

        if ($row1) {
            $people_name = $row1['ps_cl_prefix'] . $row1['ps_cl_name'] . ' ' . $row1['ps_cl_surname'];
            $people_image = $row1["ps_cl_image"];
            if ($people_image != '') {
                $people_image = "<img src='{$people_image}' height='50'> ";
            }
        }
    } else {
        die("ไม่พบข้อมูล pcid2");
    }
}

// สำหรับเลือก จังหวัด อำเภอ ตำบล
include("../config.inc.php");
include("../classes/class.database.inc.php");
// สำหรับเลือก จังหวัด อำเภอ ตำบล
$conn2 = get_connection(); // connect to mySQLDB

// สำหรับเลือกสถานีตำรวจทั่วประเทศ
$region = isset($_GET['region']) ? $_GET['region'] : 0; // sukhothai
$provincial = isset($_GET['provincial']) ? $_GET['provincial'] : 0; // sukhothai
//$station = isset($_GET['station']) ? $_GET['station'] : 0; // sukhothai

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>แก้ไขข้อมูลหมายจับ</title>
<script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/policeinnopolis/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 18px;
    }
input::placeholder {
  color: red;
}
.form-control::placeholder {
  color: sandybrown;
}
</style>
<script src="/policeinnopolis/jquery-ui-1.12.1/jquery-ui.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/policeinnopolis/jquery-ui-1.12.1/jquery-ui.css">
<!-- สำหรับวันที่ เดือนไทย ปี พ.ศ. และ เลือนเลือกปี พ.ศ.ได้  -->
<link rel="stylesheet" href="../datepicker_th/jquery.datetimepicker.css" type="text/css">
<script src="../datepicker_th/jquery-1.8.3.min.js"></script>  
<script src="../datepicker_th/jquery.datetimepicker.full.js"></script>
	
</head>

<body>
	<div class="container" >
	<div class="row">
		<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> แก้ไขข้อมูลหมายจับ ของ <?= $people_name ?> <?= $people_image ?> </div>
	<form action="Save_all_Warrant.php" method="POST" enctype="multipart/form-data" class="body">	
		<label>ลำดับ</label>
		<input name="wr_cl_aid" type = "text" class="form-control" value= "<?= $row['wr_cl_aid'] ?>" hidden ="hidden"  >
		<label>เลขบัตรประชาชน</label>
		<input name="wr_cl_idcard" type = "text" class="form-control" value= "<?= $row['wr_cl_idcard']?>" readonly="readonly"  >
		<label>ข้อมูลชื่อ </label>		
		<input type="text" class="form-control" value="<?= $people_name ?>" readonly="readonly">
        
        <label> คุณภาพหมายจับ </label>
		<select id="wr_cl_qty" name="wr_cl_qty" class="form-select form-select-sm" >
			<option value="" selected> </option>
			<option value="1">หมายจับที่มีคุณภาพ</option>
			<option value="2">หมายจับไม่มีคุณภาพ</option>
		</select>
        
		<label>ศาล</label>
		<input type = "text" name = "wr_cl_court" class="form-control" value= "<?=$row['wr_cl_court']?>" placeholder="ระบุชื่อศาลที่ออกหมายจับ" >
		<label>เลขที่หมายจับ</label>
		<input type = "text" name = "wr_cl_no" class="form-control" value= "<?=$row['wr_cl_no']?>" placeholder="ระบุเลขที่หมายจับ" >
        
	    <label>วันที่ออกหมาย <span style="color: #F90004">* จำเป็น</span> </label>
        <p><input type="text" id="datepicker" name="wr_cl_date" value= "<?=$row['wr_cl_date']?>" class="form-control" autocomplete="off" Required ></p>
		<script type="text/javascript"> 
            $(function(){

                $.datetimepicker.setLocale('th'); // ต้องกำหนดเสมอถ้าใช้ภาษาไทย และ เป็นปี พ.ศ.
                // กรณีใช้แบบ input
                $("#datepicker").datetimepicker({
                    timepicker:false,
                    format:'Y-m-d',  // กำหนดรูปแบบวันที่ ที่ใช้ เป็น 00-00-0000			
                    lang:'th',  // ต้องกำหนดเสมอถ้าใช้ภาษาไทย และ เป็นปี พ.ศ.
                    onSelectDate:function(dp,$input){
                        var yearT=new Date(dp).getFullYear()-0;  
                        var yearTH=yearT;  // ถ้าจะให้แสดงเป็น พ.ศ. ให้เป็น yearT+543
                        var fulldate=$input.val();
                        var fulldateTH=fulldate.replace(yearT,yearTH);
                        $input.val(fulldateTH);
						
						// Get the result element and update the displayed year
						var result = document.getElementById('result');
						result.textContent = 'พ.ศ. ' + (yearTH+543);
                    },
                });       
                // กรณีใช้กับ input ต้องกำหนดส่วนนี้ด้วยเสมอ เพื่อปรับปีให้เป็น ค.ศ. ก่อนแสดงปฏิทิน
                $("#datepicker").on(function(e){
                    var dateValue=$(this).val();
                    if(dateValue!=""){
                            var arr_date=dateValue.split("-"); // ถ้าใช้ตัวแบ่งรูปแบบอื่น ให้เปลี่ยนเป็นตามรูปแบบนั้น
                            // ในที่นี้อยู่ในรูปแบบ 00-00-0000 เป็น d-m-Y  แบ่งด่วย - ดังนั้น ตัวแปรที่เป็นปี จะอยู่ใน array
                            //  ตัวที่สอง arr_date[2] โดยเริ่มนับจาก 0 
                            dateValue=dateValue.replace(arr_date[0],yearT);
                            $(this).val(dateValue);													
                    }		
                });
            });
            </script>
        
        <label> วันขาดอายุ </label>
        <p><input type="text" id="datepicker2" name="wr_cl_expire" value= "<?=$row['wr_cl_expire']?>" class="form-control" autocomplete="off" ></p>
        <script type="text/javascript"> 
            $(function(){

                $.datetimepicker.setLocale('th'); // ต้องกำหนดเสมอถ้าใช้ภาษาไทย และ เป็นปี พ.ศ.
                // กรณีใช้แบบ input
                $("#datepicker2").datetimepicker({
                    timepicker:false,
                    format:'Y-m-d',  // กำหนดรูปแบบวันที่ ที่ใช้ เป็น 00-00-0000			
                    lang:'th',  // ต้องกำหนดเสมอถ้าใช้ภาษาไทย และ เป็นปี พ.ศ.
                    onSelectDate:function(dp,$input){
                        var yearT=new Date(dp).getFullYear()-0;  
                        var yearTH=yearT;  // ถ้าจะให้แสดงเป็น พ.ศ. ให้เป็น yearT+543
                        var fulldate=$input.val();
                        var fulldateTH=fulldate.replace(yearT,yearTH);
                        $input.val(fulldateTH);
						
						// Get the result element and update the displayed year
						var result = document.getElementById('result');
						result.textContent = 'พ.ศ. ' + (yearTH+543);
                    },
                });       
                // กรณีใช้กับ input ต้องกำหนดส่วนนี้ด้วยเสมอ เพื่อปรับปีให้เป็น ค.ศ. ก่อนแสดงปฏิทิน
                $("#datepicker2").on(function(e){
                    var dateValue=$(this).val();
                    if(dateValue!=""){
                            var arr_date=dateValue.split("-"); // ถ้าใช้ตัวแบ่งรูปแบบอื่น ให้เปลี่ยนเป็นตามรูปแบบนั้น
                            // ในที่นี้อยู่ในรูปแบบ 00-00-0000 เป็น d-m-Y  แบ่งด่วย - ดังนั้น ตัวแปรที่เป็นปี จะอยู่ใน array
                            //  ตัวที่สอง arr_date[2] โดยเริ่มนับจาก 0 
                            dateValue=dateValue.replace(arr_date[0],yearT);
                            $(this).val(dateValue);													
                    }		
                });
            });
            </script>

        <label>หน่วยเจ้าของหมาย<span style="color: #F90004">* จำเป็น</span></label><br>
                <!-- เลือกสถานีตำรวจ -->
        <label> บช. </label><br>
        <select name="region" id="region" onChange="do_region_change()" class="form-control" Required >
                <option value="">&lt;&lt; เลือก บช. &gt;&gt;</option>
                <?php
                $region = $row['region']; // <<ดึงข้อมูลจาก ตารางมา row ซึ่งเป็นคนละตัวกับ row_p
                //------------------------------------
			  	$res_re = $conn2->query("SELECT * FROM `wm_tb_police_region` 
                                            ORDER BY `name_region` ASC");
			     $selected = '';
			     while($row_re = $conn2->fetch_row($res_re)) 
                     {
                      $code_region = $row_re['code_region'];
                      $name_region = $row_re['name_region'];
                      // set default provionce to 64 >> sukhothai
                          if($code_region == $region) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$code_region' $selected> $name_region </option>\n";
                     }
			  ?>
              </select>
        
        
        <label> บก. </label><br>        
        <select name="provincial" id="provincial" onChange="do_provincial_change()" value="<?php echo $provincial; ?>" class="form-control" >
        <option value="0">&lt;&lt; เลือก บก. &gt;&gt;</option>
                <?php
                if($region > 0)
                {
                    $provincial = $row['provincial']; // <<
                    //-------------------------------------
                    $res_prov = $conn2->query("SELECT * FROM `wm_tb_police_provincial`
                                                WHERE `region_code`='$region'
                                                ORDER BY `provincial_code` ASC");
                    $selected = '';
                  while($row_prov = $conn2->fetch_row($res_prov))
                  {
                      $provincial_code = $row_prov['provincial_code']; //
                      $provincial = $row_prov['provincial'];
                      // set default provionce to 64 >> sukhothai
                          if($provincial_code == $provincial) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$provincial_code' $selected> $provincial </option>\n";
                  }
                }	
                ?>
              </select>
        
         <label> สน./สภ. </label>
        <br>
                
        <select name="station" id="station" class="form-control" >
                <option value="0">&lt;&lt; เลือก สน./สภ. &gt;&gt;</option>
                <?php
	           if($provincial > 0)
                {
                   $station = $row['station'];
                   //------------------------------------------
                    $res_st = $conn2->query("SELECT * FROM `wm_tb_police_station2` 
                                                WHERE provincial_code='$provincial_code'
                                                ORDER BY `station_code` ASC");
                    $selected = '';
                  while($row_st = $conn2->fetch_row($res_st)) 
                  {
                      $station_code = $row_st['station_code'];
                      $station_name = $row_st['station_name'];
                          if($station_code == $station) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      echo "<option value='$station_code' $selected> $station_name </option>\n";
                  }
                }	
            
                ?>                                                                     
              </select>
		<br>
        
		<label>เลขคดีอาญา</label>
		<input type = "text" name = "wr_cl_crimes_no" class="form-control" value= "<?=$row['wr_cl_crimes_no']?>" placeholder="ระบุเลขคดีอาญา" >
        
		<label> วันที่ร้องทุกข์ </label>
        <p><input type="text" id="datepicker3" name="wr_cl_complaints"  value= "<?=$row['wr_cl_complaints']?>" class="form-control" autocomplete="off" ></p>
		<script type="text/javascript"> 
            $(function(){

                $.datetimepicker.setLocale('th'); // ต้องกำหนดเสมอถ้าใช้ภาษาไทย และ เป็นปี พ.ศ.
                // กรณีใช้แบบ input
                $("#datepicker3").datetimepicker({
                    timepicker:false,
                    format:'Y-m-d',  // กำหนดรูปแบบวันที่ ที่ใช้ เป็น 00-00-0000			
                    lang:'th',  // ต้องกำหนดเสมอถ้าใช้ภาษาไทย และ เป็นปี พ.ศ.
                    onSelectDate:function(dp,$input){
                        var yearT=new Date(dp).getFullYear()-0;  
                        var yearTH=yearT;  // ถ้าจะให้แสดงเป็น พ.ศ. ให้เป็น yearT+543
                        var fulldate=$input.val();
                        var fulldateTH=fulldate.replace(yearT,yearTH);
                        $input.val(fulldateTH);
						
						// Get the result element and update the displayed year
						var result = document.getElementById('result');
						result.textContent = 'พ.ศ. ' + (yearTH+543);
                    },
                });       
                // กรณีใช้กับ input ต้องกำหนดส่วนนี้ด้วยเสมอ เพื่อปรับปีให้เป็น ค.ศ. ก่อนแสดงปฏิทิน
                $("#datepicker3").on(function(e){
                    var dateValue=$(this).val();
                    if(dateValue!=""){
                            var arr_date=dateValue.split("-"); // ถ้าใช้ตัวแบ่งรูปแบบอื่น ให้เปลี่ยนเป็นตามรูปแบบนั้น
                            // ในที่นี้อยู่ในรูปแบบ 00-00-0000 เป็น d-m-Y  แบ่งด่วย - ดังนั้น ตัวแปรที่เป็นปี จะอยู่ใน array
                            //  ตัวที่สอง arr_date[2] โดยเริ่มนับจาก 0 
                            dateValue=dateValue.replace(arr_date[0],yearT);
                            $(this).val(dateValue);													
                    }		
                });
            });
            </script>
        
		<label>ฐานความผิด</label>
		<input type = "text" name = "wr_cl_crimes_type" class="form-control" value= "<?=$row['wr_cl_crimes_type']?>" placeholder="ระบุฐานความผิด" >
		
        <b>การดำเนินการหมายจับ</b>
		<label> สถานะหมายจับ </label>
			<select id="wr_cl_status" name="wr_cl_status" class="form-select form-select-sm" value="<?=$row['wr_cl_status']?>" >
				<option value="<?=$row3['wr_cl_status']?>" selected></option>  <!-- ดึงข้อมูลมาจากตารางใหม่ -->
				<?php
                    $query3 = $pdo->query("SELECT * FROM wm_tb_status_warrent order by sw_cl_value ASC");
					while($row3 = $query3->fetch(PDO::FETCH_ASSOC))
					{
						echo "<option value='{$row3['sw_cl_value']}'>{$row3['sw_cl_name']}</option>";
					}
				?>
			</select>
		
        <label> วันที่จับกุม/อายัด/จำหน่ายหมาย </label>
        <p><input type="text" id="datepicker4" name="wr_cl_date_finish"  value= "<?=$row['wr_cl_date_finish']?>" class="form-control" autocomplete="off" ></p>
		<script type="text/javascript"> 
            $(function(){

                $.datetimepicker.setLocale('th'); // ต้องกำหนดเสมอถ้าใช้ภาษาไทย และ เป็นปี พ.ศ.
                // กรณีใช้แบบ input
                $("#datepicker4").datetimepicker({
                    timepicker:false,
                    format:'Y-m-d',  // กำหนดรูปแบบวันที่ ที่ใช้ เป็น 00-00-0000			
                    lang:'th',  // ต้องกำหนดเสมอถ้าใช้ภาษาไทย และ เป็นปี พ.ศ.
                    onSelectDate:function(dp,$input){
                        var yearT=new Date(dp).getFullYear()-0;  
                        var yearTH=yearT;  // ถ้าจะให้แสดงเป็น พ.ศ. ให้เป็น yearT+543
                        var fulldate=$input.val();
                        var fulldateTH=fulldate.replace(yearT,yearTH);
                        $input.val(fulldateTH);
						
						// Get the result element and update the displayed year
						var result = document.getElementById('result');
						result.textContent = 'พ.ศ. ' + (yearTH+543);
                    },
                });       
                // กรณีใช้กับ input ต้องกำหนดส่วนนี้ด้วยเสมอ เพื่อปรับปีให้เป็น ค.ศ. ก่อนแสดงปฏิทิน
                $("#datepicker4").on(function(e){
                    var dateValue=$(this).val();
                    if(dateValue!=""){
                            var arr_date=dateValue.split("-"); // ถ้าใช้ตัวแบ่งรูปแบบอื่น ให้เปลี่ยนเป็นตามรูปแบบนั้น
                            // ในที่นี้อยู่ในรูปแบบ 00-00-0000 เป็น d-m-Y  แบ่งด่วย - ดังนั้น ตัวแปรที่เป็นปี จะอยู่ใน array
                            //  ตัวที่สอง arr_date[2] โดยเริ่มนับจาก 0 
                            dateValue=dateValue.replace(arr_date[0],yearT);
                            $(this).val(dateValue);													
                    }		
                });
            });
            </script>
        
		<label> พงส. </label>
		<input type = "text" name = "wr_cl_officer" class="form-control" value= "<?=$row['wr_cl_officer']?>" placeholder="พนักงานสอบสวน" >
		<label> เบอร์ พงส. </label>
		<input type = "text" name = "wr_cl_officer_phone" class="form-control" value= "<?=$row['wr_cl_officer_phone']?>" placeholder="เบอร์โทรพนักงานสอบสวน" >
		<label> ผู้รับผิดชอบ </label>
		<input type = "text" name = "wr_cl_police" class="form-control" value= "<?=$row['wr_cl_police']?>" placeholder="ผู้รับผิดชอบหมายจับ" >
		
		<div class="mb-3">
  		<label for="formFileMultiple" class="form-label">รายงานสืบสวน</label>
 		<input class="form-control" type="file" id="wr_cl_file_invest" name="wr_cl_file_invest"  value=<?=$row['wr_cl_file_invest']?> multiple>
		</div>
		
		<div class="mb-3">
  		<label for="formFileMultiple" class="form-label">ไฟล์หมายจับ</label>
 		<input class="form-control" type="file" id="wr_cl_file" name="wr_cl_file" value=<?=$row['wr_cl_file']?> multiple  >
		</div>
		<br>
  		<p>
			<input type="submit" value="Update" class="btn btn-success" >
			<td> <a href="Show_All.php?pcid=<?= $pcid2 ?>&page=warrant" class="btn btn-warning" >ยกเลิก </a></td>
 		</p>
	</form>
	</div>
	</div>
	</div>
    
<script>
    // ฟังก์ชั่น เปลี่ยนภาค เป็น จังหวัด
function do_region_change(current_value)
{
    current_value = (typeof current_value == 'undefined') ? 0 : current_value;
	var sel_region = document.getElementById("region");		
	var code_region = sel_region.options[sel_region.selectedIndex].value;

	$.get("../ajax/get_provincial.php?region=" + code_region, 
		  function(data, status){
				//alert("Data: " + data + "\nStatus: " + status);
				// jQuery style						
				$('#provincial').find('option').remove().end();
				var data = $.trim(data);
				if(data == '') {
					alert("ไม่พบข้อมูล บก. !");
					return;
				}

					//.append('<option value="whatever">text</option>').val('whatever');
				var datas = data.split(';');
				for(var i=0; i<datas.length; i++) {
					if(datas[i] == '') continue;
					datas[i] = datas[i].split(':');
					var code_region = datas[i][0];
					var name_region = datas[i][1];
					//$('#provincial').append('<option value="'+ code_region+'">' + name_region + '</option>');
                    if(current_value == code_region) {
                        $('#provincial').append('<option value="'+ code_region+'" selected>' + name_region + '</option>'); 
                    }
                    else {
                        $('#provincial').append('<option value="'+ code_region+'">' + name_region + '</option>');
                    }
				}
                // Trigger the change event on the amphur dropdown so the tambon dropdown will be populated     
                $('#provincial').trigger('change');
		});
}
    //เลือก สถานี จากจังหวัด
function do_provincial_change(current_value)
{
    current_value = (typeof current_value == 'undefined') ? 0 : current_value;
    
	var sel_provincial = document.getElementById("region");		
	var provincial_code = sel_provincial.options[sel_provincial.selectedIndex].value;
    
    var sel_provincial2 = document.getElementById("provincial");		
	var station_code = sel_provincial2.options[sel_provincial2.selectedIndex].value;

	$.get("../ajax/get_station.php?region=" + provincial_code + "&provincial=" + station_code, 
			  function(data, status){
					//alert("Data: " + data + "\nStatus: " + status);
					// jQuery style						
					$('#station').find('option').remove().end();
					var data = $.trim(data);
					if(data == '') {
						alert("ไม่พบข้อมูลสถานี !");
						return;
					}

						//.append('<option value="whatever">text</option>').val('whatever');
					var datas = data.split(';');
					for(var i=0; i<datas.length; i++) {
						if(datas[i] == '') continue;
						datas[i] = datas[i].split(':');
						var station_code = datas[i][0];
						var station_name = datas[i][1];
                        if(current_value == station_code) {
                            $('#station').append('<option value="'+ station_code+'" selected>' + station_name + '</option>'); 
                        }
                        else {
                            $('#station').append('<option value="'+ station_code+'">' + station_name + '</option>');   
                        }						
					}
			});
}
</script>
<script>
// ชื่อ >> value >> set selct auto (สคลิปตรวจสอบ Selected) จำเป็นต้องก็อปปี้ <script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js" type="text/javascript">
function auto_select(sid, value)
{
    $('#' + sid).val(value);
}

$(document).ready(function() {
<?php
    echo "auto_select('wr_cl_qty', '{$row['wr_cl_qty']}');\n";
    echo "auto_select('wr_cl_status', '{$row['wr_cl_status']}');\n";
    // convert db date to thai dates
    //echo "auto_thaidate('wr_cl_date', '{$row['wr_cl_date']}');\n";
	//echo "auto_thaidate('wr_cl_date_finish', '{$row['wr_cl_date_finish']}');\n";
?>
    setTimeout("do_region_change('<?= $row['provincial'] ?>')", 1000);
    setTimeout("do_provincial_change('<?= $row['station'] ?>')", 2000);
});
</script>
</body>
</html>