<script src="/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>
<script src="../SweetAlert2/dist/sweetalert2.all.min.js"></script>

<?php //PDO
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

$id = isset($_GET['fun_cl_aid']) ? $_GET['fun_cl_aid'] : '';

?>

<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link rel="stylesheet" href="/bootstrap/css/bootstrap-grid.css">
<style>
    .swal2-popup {
    width: 500px !important;
    height: auto !important;
    font-size: 14px !important;
}
</style>
<div class="container-fluid" align="left">
<div class=" h3 text-center  alert alert-success mb-4 mt-4 " role="alert" >แฟ้มกองทุนสืบสวน <?= $name_station ?> </div>
		<div align="left">
&nbsp;<a href="Add_criminal_fund.php" class="btn btn-success btn-lg mb-4 <?= $add_btn ?>" >เพิ่มข้อมูล</a>
&nbsp;&nbsp;&nbsp;&nbsp;<a href="https://www.financecop.com/newversion/" target="new" class="btn btn-secondary btn-lg mb-4">ระบบจัดการข้อมูลกองทุน</a>
&nbsp;<a href="https://drive.google.com/drive/folders/1rlMf1IqMcInbnQ-OiMhG_dcwJbnA5Um0?usp=sharing" target="new" class="btn btn-secondary btn-lg mb-4">แนวทางปฏิบัติในการเบิกจ่ายเงินกองทุน 2566</a>
&nbsp;<a href="https://drive.google.com/file/d/1Ue9GvgEBgZrAQUZS62NAW20sm6wiAtwj/view?usp=drive_link" target="new" class="btn btn-secondary btn-lg mb-4">แนวทางปฏิบัติด้านการเงินในระบบ New GFMIS Thai</a>

<div class="table-responsive">
<table width="100%" height="50" border="1" cellpadding="1" cellspacing="1" class="table-bordered table-hover mb-4 mt-4">
  <tbody>
    <tr>
	  <td bgcolor="#94FC60" style="color: #00000; text-align: center; border-color: navy">ลำดับ</td>   
      <td bgcolor="#94FC60" style="color: #00000; text-align: center; border-color: navy">วันที่</td>
      <td bgcolor="#94FC60" style="color: #00000; text-align: center; border-color: navy">รายการ</td>
      <td bgcolor="#94FC60" style="color: #00000; text-align: center; border-color: navy">ผู้บันทึก</td>
      <td bgcolor="#94FC60" style="color: #00000; text-align: center; border-color: navy">เอกสารประกอบ</td>
      <td colspan="3" bgcolor="#94FC60" style="color: #00000; text-align: center; border-color: navy"></td>
      <td colspan="3" bgcolor="#94FC60" style="color: #00000; text-align: center; border-color: navy"></td>
    </tr>
	  
<?php

$sql = "SELECT * FROM wm_tb_criminalfund " .
        "WHERE station='$station' AND fun_cl_date ORDER BY fun_cl_aid DESC "; 
if($id != ''){
	$sql = $sql . " WHERE fun_cl_aid = :id ";
}
      
      $stmt = $pdo->prepare($sql);
      $stmt->bindParam(':id', $id);
      $stmt->execute();
      
// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no = 1;
while($row_fun = $stmt->fetch(PDO::FETCH_ASSOC))	//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
{
    //ฟังก์ชั่น วันที่ ดึงจาก condb
    $strDate = DateThai( $row_fun["fun_cl_date"] );
    
    //ตรวจสอบไฟล์ สส.1 ก่อนว่า มีไฟล์ในตารางไหม หากมี ให้ส่งเป็นลิงค์
	$pdf = $row_fun["fun_cl_file"];
	if($pdf !== '')
	{
		$links = '<a href="/' . $row_fun["fun_cl_file"] .'" target="_blank">Download</a>';
	}
	else {
		$links = 'ไม่มีไฟล์';
	}
    
?>	

    <tr>
	  <td><?= $no ?> </td>
	  <!--<td><?= $row_fun["fun_cl_aid"]?></td>-->  
      <td nowrap>&nbsp;<?= $strDate ?>&nbsp;</td>
      <td>&nbsp;<?= $row_fun["fun_cl_detail"]?>&nbsp;</td>
      <td>&nbsp;<?= $row_fun["fun_cl_record"]?>&nbsp;</td>
      <td>&nbsp;<?= $links ?>&nbsp;</td>
	  <td>&nbsp;<a href="Edit_criminal_fund.php?id=<?= $row_fun["fun_cl_aid"] ?>" class="btn btn-warning mb-4 <?= $edit_btn ?>" >แก้ไข</a>&nbsp;</td>
	  <td>&nbsp;<button href="#" class="btn btn-danger mb-4" onClick="deleteItem(<?= $row_fun["fun_cl_aid"] ?>)"<?=$del_btn?>>ลบ</button>&nbsp;</td>
    </tr>
  </tbody>
<?php
	$no++;
}
?>
</table>
</div>
<script>
function deleteItem(id) {
    Swal.fire({
        title: 'คุณต้องการลบข้อมูลนี้ใช่ไหม?',
        text: "หลังจากลบแล้ว จะไม่สามารถกู้คืนได้",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'ใช่, ยืนยันลบ',
        cancelButtonText: 'ยกเลิก'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = '/Bansuan/Del_criminal_fund.php?id=' + id;
        }
    });
}
</script>