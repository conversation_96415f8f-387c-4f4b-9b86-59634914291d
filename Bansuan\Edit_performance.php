<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';


// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// สำหรับเลือก จังหวัด อำเภอ ตำบล /และ สถานีตำรวจ
include("../config.inc.php");
include("../classes/class.database.inc.php");
$conn2 = get_connection(); // connect to mySQLDB

// สำหรับเลือกสถานีตำรวจทั่วประเทศ
$region = isset($_GET['region']) ? $_GET['region'] : 0;
$provincial = isset($_GET['provincial']) ? $_GET['provincial'] : 0;
//$station = isset($_GET['station']) ? $_GET['station'] : 0;


$id = $_GET['id'];
$pcid = $_GET['pcid'];

$sql = "SELECT * FROM wm_tb_performance WHERE pf_cl_aid = :id AND pf_cl_suspect_idcard = :pcid ";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':id', $id);
    $stmt->bindParam(':pcid', $pcid);
    
try{
    $stmt->execute();    
}catch(PDOException $e){
    echo 'Query $sql Failed: '. $e->getMessage();
}

    $row = $stmt->fetch(PDO::FETCH_ASSOC);

?>

<!doctype html>
<html><head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>แก้ไขข้อมูลผลการปฏิบัติ</title>
<script src="/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 18px;
    }
input::placeholder {
  color: red;
}
.form-control::placeholder {
  color: sandybrown;
}
</style>
<!-- สำหรับ วันที่ แบบ พ.ศ. -->
<link rel="stylesheet" href="http://code.jquery.com/ui/1.9.1/themes/base/jquery-ui.css" />
<script src="http://code.jquery.com/jquery-1.8.2.js"></script>
<script src="/js/jquery-ui.js"></script>
</head>

<body>
	
	<div class="container">
	<div class="row">
	<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> แก้ไขข้อมูลผลการปฏิบัติงาน </div>
	<form action="Save_performance.php" method="POST" enctype="multipart/form-data" class="body" >

		<label hidden ="hidden">ลำดับ</label>
		<input type = "text" name = "pf_cl_aid" class="form-control" value= "<?= $row['pf_cl_aid']?>" hidden ="hidden" >
		<label>เลขบัตรผู้ต้องหา</label>
		<input type = "text" name = "pf_cl_suspect_idcard" class="form-control" value= "<?= $row['pf_cl_suspect_idcard']?>" placeholder="เลขบัตรผู้ต้องหา" readonly >
        
		<label>วันเดือนปีที่ตรวจค้น/จับกุม <span style="color: #F90004">* จำเป็น</span> </label>
        <p><input type="text" name="pf_cl_dates" id="datepicker" value="<?= $row['pf_cl_dates'] ?>" class="form-control" autocomplete="off" required></p>
              <script>
            function set_cal(ele)//function สร้างตัวเลือกปฎิทิน
            {
              $( ele ).datepicker({
                  onSelect:(date_text)=>
                  {
                    let arr=date_text.split("/");
                    let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])+543).toString();
                    $(ele).val(new_date);
                    $(ele).css("color","");
                  },
                  beforeShow:()=>{

                    if($(ele).val()!="")
                    {
                      let arr=$(ele).val().split("/");
                      let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])-543).toString();
                      $(ele).val(new_date);

                    }

                    $(ele).css("color","white");
                  },
                  onClose:()=>{

                      $(ele).css("color","");

                      if($(ele).val()!="")
                      {
                          let arr=$(ele).val().split("-");
                          if(parseInt(arr[2])<2500)
                          {
                              let new_date=arr[0]+"-"+arr[1]+"-"+(parseInt(arr[2])).toString();
                              $(ele).val(new_date);
                          }
                      }
                  },
                  dateFormat:"yy-mm-dd", //กำหนดรูปแบบวันที่เป็น วัน/เดือน/ปี
                  changeMonth:true,//กำหนดให้เลือกเดือนได้
                  changeYear:true,//กำหนดให้เลือกปีได้
                  showOtherMonths:true,//กำหนดให้แสดงวันของเดือนก่อนหน้าได้
              });
            }
          $(document).ready(function(){
            //เรียก function set_cal เมื่อเปิดหน้าเว็บ โดยส่ง object element ที่มี id เป็น datepicker เป็นพารามิเตอร์
            set_cal( $("#datepicker") );
          })
          </script>
        <!-- สำหรับ วันที่ แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ -->
		
		<label>ประเภทการปฏิบัติ</label>
		<select id="pf_cl_type_main" name="pf_cl_type_main" class="form-select form-select-sm" >
			<option value="" selected> </option>
			<option value="กวาดล้าง">กวาดล้าง</option>
			<option value="จับกุมทั่วไป">จับกุมทั่วไป</option>
			<option value="จับตามหมาย">จับตามหมาย</option>
			<option value="ตรวจค้น">ตรวจค้น</option>
            <option value="กวาดล้าง2">กวาดล้าง รอบ2</option>
		</select><br>
		
		<label>ประเภทความผิด</label>
		<select id="pf_cl_type_sub" name="pf_cl_type_sub" class="form-select form-select-sm" >
			<option value="" selected> </option>
			<option value="พ.ร.บ.ยาเสพติด (ผลิต)">พ.ร.บ.ยาเสพติด (ผลิต)</option>
			<option value="พ.ร.บ.ยาเสพติด (จำหน่าย)">พ.ร.บ.ยาเสพติด (จำหน่าย)</option>
			<option value="พ.ร.บ.ยาเสพติด (ครอบครอง)">พ.ร.บ.ยาเสพติด (ครอบครอง)</option>
			<option value="พ.ร.บ.ยาเสพติด (เสพ)">พ.ร.บ.ยาเสพติด (เสพ)</option>
			<option value="พ.ร.บ.ยาเสพติด (คัดกรอง)">พ.ร.บ.ยาเสพติด (คัดกรอง)</option>
			<option value="พ.ร.บ.อาวุธปืน">พ.ร.บ.อาวุธปืน</option>
			<option value="พ.ร.บ.การพนัน">พ.ร.บ.การพนัน</option>
			<option value="พ.ร.บ.คนเข้าเมือง">พ.ร.บ.คนเข้าเมือง</option>
			<option value="อาชญากรรมทางเทคโนโลยี">อาชญากรรมทางเทคโนโลยี</option>
			<option value="หมายจับ">หมายจับ</option>
            <option value="ชีวิตและร่างกาย">ชีวิตและร่างกาย</option>
            <option value="เกี่ยวกับทรัพย์">เกี่ยวกับทรัพย์</option>
            <option value="พ.ร.บ.อื่น ๆ">พ.ร.บ.อื่น ๆ</option>
		</select>
		
		<hr color="#F8060A">

        <b>ความผิดเกี่ยวกับยาเสพติด</b><br>
		<label>เลือกประเภทความผิดยาเสพติด</label>
		<select id="pf_cl_drug" name="pf_cl_drug" class="form-select form-select-sm" >
			<option value="" selected> </option>
			<?php
                $res_dg = $pdo->prepare("SELECT * FROM wm_tb_performance_drug order by aid_drug ASC");
               
               try{
                   $res_dg->execute();
               }catch(PDOException $e){
                   echo 'Query $res_dg Failed: '. $e->getMessage();
               }
                
               while($row_dg = $res_dg->fetch(PDO::FETCH_ASSOC))
                {
                   $aid_drug = htmlspecialchars($row_dg['aid_drug'], ENT_QUOTES, 'UTF-8');
                   $drug_type = htmlspecialchars($row_dg['drug_type'], ENT_QUOTES, 'UTF-8');
                   
                   echo "<option value='$aid_drug'>$drug_type</option>";
                }
            ?>
		</select>
		
        <label class="body"><input name="pf_cl_drug_sell" id="pf_cl_drug_sell" type="checkbox" value="1" <?php if($row['pf_cl_drug_sell'] == "1") echo 'checked="checked" '; ?>/> ข้อหาผลิต/นำเข้า/ส่งออก/จำหน่าย/ครอบครองเพื่อจำหน่าย 1 คดี</label><br>

        <label class="body"><input name="pf_cl_drug_sell_person" id="pf_cl_drug_sell_person" type="checkbox" value="1" <?php if($row['pf_cl_drug_sell_person'] == "1") echo 'checked="checked" '; ?> /> มีผู้ต้องหา ผลิต/จำหน่าย  1 ราย</label><br><br>

        <label class="body"><input name="pf_cl_drug_occupy" id="pf_cl_drug_occupy" type="checkbox" value="1" <?php if($row['pf_cl_drug_occupy'] == "1") echo 'checked="checked" '; ?> /> ข้อหาครอบครองฯ  1 คดี</label><br>

        <label class="body"><input name="pf_cl_drug_occupy_person" id="pf_cl_drug_occupy_person" type="checkbox" value="1" <?php if($row['pf_cl_drug_occupy_person'] == "1") echo 'checked="checked" '; ?> /> มีผู้ต้องหาครอบครองฯ  1 ราย</label><br><br>

        <label class="body"><input name="pf_cl_take_drugs" id="pf_cl_take_drugs" type="checkbox" value="1" <?php if($row['pf_cl_take_drugs'] == "1") echo 'checked="checked" '; ?> /> ข้อหาเสพฯ  1 คดี</label><br>

        <label class="body"><input name="pf_cl_take_drugs_person" id="pf_cl_take_drugs_person" type="checkbox" value="1" <?php if($row['pf_cl_take_drugs_person'] == "1") echo 'checked="checked" '; ?> /> มีผู้ต้องหาเสพฯ 1 ราย</label><br><br>

        <label class="body"><input name="pf_cl_drug_therapy" id="pf_cl_drug_therapy" type="checkbox" value="1" <?php if($row['pf_cl_drug_therapy'] == "1") echo 'checked="checked" '; ?> /> ส่งศูนย์คัดกรอง  1 คดี</label><br>

        <label class="body"><input name="pf_cl_drug_therapy_person" id="pf_cl_drug_therapy_person" type="checkbox" value="1" <?php if($row['pf_cl_drug_therapy_person'] == "1") echo 'checked="checked" '; ?> /> ส่งคัดกรอง 1 ราย</label><br><br>
		
		<label>จำนวนของกลาง ยาเสพติด</label>
		<input type = "text" name = "pf_cl_items_drug" class="form-control" value= "<?= $row['pf_cl_items_drug']?>" placeholder="ระบุจำนวนของกลาง ยาเสพติด"   >

		<hr color="#F8060A">
		
        <b>ความผิด พ.ร.บ.อาวุธปืน</b><br>

        <label class="body"><input name="pf_cl_gun" id="pf_cl_gun" type="checkbox" value="1" <?php if($row['pf_cl_gun'] == "1") echo 'checked="checked" '; ?> /> พ.ร.บ.อาวุธปืน 1 คดี</label><br>

        <label class="body"><input name="pf_cl_items_gun" id="pf_cl_items_gun" type="checkbox" value="1" <?php if($row['pf_cl_items_gun'] == "1") echo 'checked="checked" '; ?> /> ของกลาง 1 รายการ</label><br><br>
		
		<hr color="#F8060A">
		
        <b>ความผิด พ.ร.บ.การพนัน</b><br>

        <label class="body"><input name="pf_cl_gambling_case" id="pf_cl_gambling_case" type="checkbox" value="1" <?php if($row['pf_cl_gambling_case'] == "1") echo 'checked="checked" '; ?> /> คดีการพนัน 1 คดี</label><br>
		
		<label>จำนวนผู้ต้องหา >> การพนัน</label>
		<select id="pf_cl_gambling_person" name="pf_cl_gambling_person" class="form-select form-select-sm"  placeholder="ระบุจำนวนผู้ต้องหา การพนัน">
			<option value="" selected> </option>
			<option value="1">1</option>
            <option value="2">2</option>
            <option value="3">3</option>
            <option value="4">4</option>
            <option value="5">5</option>
            <option value="6">6</option>
            <option value="7">7</option>
            <option value="8">8</option>
            <option value="9">9</option>
            <option value="10">10</option>
		</select>
		
		<br>
		<hr color="#F8060A">
		
        <b>ความผิด พ.ร.บ.คนเข้าเมือง</b><br>

        <label class="body"><input name="pf_cl_immigrant_case" id="pf_cl_immigrant_case" type="checkbox" value="1" <?php if($row['pf_cl_immigrant_case'] == "1") echo 'checked="checked" '; ?>/> คดีคนเข้าเมือง 1 คดี</label><br>
		
        <label class="body"><input name="pf_cl_immigrant_person" id="pf_cl_immigrant_person" type="checkbox" value="1" <?php if($row['pf_cl_immigrant_person'] == "1") echo 'checked="checked" '; ?>/> ผู้ต้องหาคนเข้าเมือง 1 ราย</label><br>
		
		<br>
		<hr color="#F8060A">
		
        <b>ความผิดอาชญากรรมทางเทคโนโลยี</b><br>

        <label class="body"><input name="pf_cl_techno_case" id="pf_cl_techno_case" type="checkbox" value="1" <?php if($row['pf_cl_techno_case'] == "1") echo 'checked="checked" '; ?>/> คดีความผิดอาชญากรรมทางเทคโนโลยี 1 คดี</label><br>
		
        <label class="body"><input name="pf_cl_techno_person" id="pf_cl_techno_person" type="checkbox" value="1" <?php if($row['pf_cl_techno_person'] == "1") echo 'checked="checked" '; ?> /> มีผู้ต้องหา 1 ราย</label><br>
		
		<br>
		<hr color="#F8060A">
		
        <b>จับกุม ตามหมายจับ</b><br>
        <label class="body"><input name="pf_cl_wanted_qty" id="pf_cl_wanted_qty" type="checkbox" value="1" <?php if($row['pf_cl_wanted_qty'] == "1") echo 'checked="checked" '; ?>/> จับกุม ตามหมายจับ 1 ราย</label><br>
        
		<label>จับกุมตามหมายจับ</label>
		<select id="pf_cl_wanted" name="pf_cl_wanted" class="form-select form-select-sm" >
			<option value="" selected> </option>
			<option value="หมายจับใหม่">หมายจับใหม่ (ภายในปี)</option>
			<option value="หมายจับค้างเก่า">หมายจับค้างเก่า</option>
			<option value="หมายจับศาล">หมายจับศาล</option>
			<option value="หมายจับท้องที่อื่น">หมายจับท้องที่อื่น</option>
		</select>
		
		<label>การจับกุมตามหมายจับโดย</label>
		<select id="pf_cl_catch" name="pf_cl_catch" class="form-select form-select-sm" >
			<option value="" selected> </option>
			<option value="จับกุมเอง">จับกุมเอง</option>
			<option value="ร่วมจับกุม">ร่วมจับกุม</option>
            <option value="อายัดตัว">อายัดตัว</option>
		</select>
		
		<br>
		<hr color="#F8060A">
		
		<label>ชื่อ-สกุลผู้ต้องหา <span style="color: #F90004">* จำเป็น</span> </label>
		<input type = "text" name = "pf_cl_suspect_name" class="form-control" value= "<?= $row['pf_cl_suspect_name']?>" placeholder="ชื่อ - สกุลผู้ต้องหา" Required >
        
        <br>
		<hr color="#F8060A">
        
        <label>หมายค้น </label>
		<input type = "text" name = "pf_cl_search_warrant" class="form-control" value= "<?= $row['pf_cl_search_warrant']?>" placeholder="ระบุรายละเอียดหมายค้น" >

        <label>สิ่งผิดกฎหมายตามหมายค้น</label>
		<select id="pf_cl_find" name="pf_cl_find" class="form-select form-select-sm"  placeholder="สิ่งหมายกฎหมาย">
			<option value="" selected> </option>
			<option value="พบ">พบ</option>
			<option value="ไม่พบ">ไม่พบ</option>
		</select>
        
        <label>รายการที่พบตามหมายค้น </label>
		<input type = "text" name = "pf_cl_illegal" class="form-control" value= "<?= $row['pf_cl_illegal']?>" placeholder="ระบุรายละเอียดที่พบ" >
		
        <br>
		<hr color="#F8060A">
        
		<label>ผู้บันทึกข้อมูล</label>
		<?php
			if ($station == 6707) {
				// User's station is 6707, display the dropdown select
				echo '<select id="pf_cl_record" name="pf_cl_record" class="form-select form-select-sm" placeholder="ระบุชื่อผู้บันทึก" required>';
				echo '<option value="" selected> </option>';
				$bsdp = $pdo->prepare("SELECT * FROM `police_name_bsdetective` ORDER BY `police_name_bsdetective`.`aid_bsdp` DESC");
				$bsdp->execute();
				while ($row_bsdp = $bsdp->fetch(PDO::FETCH_ASSOC)) {
					$aid_bsdp = htmlspecialchars($row_bsdp['aid_bsdp'], ENT_QUOTES, 'UTF-8');
					$bsdp_name = htmlspecialchars($row_bsdp['bsdp_name'], ENT_QUOTES, 'UTF-8');

					echo "<option value='$bsdp_name'>$bsdp_name</option>";
				}
				echo '</select>';
			} else {
				// User's station is not 6707, display a text input
				$originalValue = htmlspecialchars($row['pf_cl_record'], ENT_QUOTES, 'UTF-8');
				echo '<input type="text" id="pf_cl_record" name="pf_cl_record" value="' . $originalValue . '" class="form-control" placeholder="ระบุ ยศชื่อ-สกุล ผู้บันทึก" required>';
			}
		?>
				
		<label>สถานีตำรวจ <span style="color: #F90004">* จำเป็น</span></label><br>
                <!-- เลือกสถานีตำรวจ -->
        <label> บช. </label><br>
        <select name="region" id="region" onChange="do_region_change()" class="form-control" Required >
                <option value="">&lt;&lt; เลือก บช. &gt;&gt;</option>
                <?php
                $region = $row['region']; // <<ดึงข้อมูลจาก ตารางมา row ซึ่งเป็นคนละตัวกับ row_p
                //------------------------------------
			  	$res_re = $conn2->query("SELECT * FROM `wm_tb_police_region` 
                                            ORDER BY `name_region` ASC");
			     $selected = '';
			     while($row_re = $conn2->fetch_row($res_re)) 
                     {
                      $code_region = $row_re['code_region'];
                      $name_region = $row_re['name_region'];
                      // set default provionce to 64 >> sukhothai
                          if($code_region == $region) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$code_region' $selected> $name_region </option>\n";
                     }
			  
			  ?>
              </select>
        
        
        <label> บก. </label><br>        
        <select name="provincial" id="provincial" onChange="do_provincial_change()" value="<?php echo $provincial; ?>" class="form-control" >
        <option value="0">&lt;&lt; เลือก บก. &gt;&gt;</option>
                <?php
                if($region > 0)
                {
                    $provincial = $row['provincial']; // <<
                    //-------------------------------------
                    $res_prov = $conn2->query("SELECT * FROM `wm_tb_police_provincial` 
                                                WHERE `region_code`='$region' 
                                                ORDER BY `provincial_code` ASC");
                    $selected = '';
                  while($res_prov = $conn2->fetch_row($res_prov)) 
                  {
                      $provincial_code = $res_prov['provincial_code']; // 
                      $provincial = $res_prov['provincial'];
                      // set default provionce to 64 >> sukhothai
                          if($provincial_code == $provincial) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$provincial_code' $selected> $provincial </option>\n";
                  }
                }	
                ?>
              </select>
        
        
         <label> สน./สภ. </label>
        <br>
                
        <select name="station" id="station" class="form-control" >
                <option value="0">&lt;&lt; เลือก สน./สภ. &gt;&gt;</option>
                <?php
	           if($provincial > 0)
                {
                   $station = $row['station'];
                   //------------------------------------------
                    $res_st = $conn2->query("SELECT * FROM `wm_tb_police_station2` 
                                                WHERE provincial_code='$provincial_code'
                                                ORDER BY `station_code` ASC");
                    $selected = '';
                  while($row_st = $conn2->fetch_row($res_st)) 
                  {
                      $station_code = $row_st['station_code']; // 
                      $station_name = $row_st['station_name'];
                      // set default provionce to 64 >> sukhothai
                          if($station_code == $station) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$station_code' $selected> $station_name </option>\n";
                  }

                }	
                ?>                                                                     
              </select>
		<br>
		
		<label>หมายเหตุ</label>
		<input type = "text" name = "pf_cl_remark" class="form-control" value= "<?= $row['pf_cl_remark']?>" placeholder="หมายเหตุ กรณีอื่น ๆ ที่ไม่มีในรายการให้ใส่ช่องหมายเหตุ" >
		
		<p>
		<br>
			<input type="submit" value="Update" class="btn btn-success" >
			<td> <a href="index.php?rnd=<?= rand(); ?>&page=performance" class="btn btn-warning" >ยกเลิก</a> </td>
		</p>
		<br>
		<br>
	</form>
	</div>
	</div>
	</div>
<!-- ฟังก์ชั่น เลือก จังหวัด อำเภอ ตำบล -->
<script>
// ฟังก์ชั่น เปลี่ยนภาค เป็น จังหวัด
function do_region_change(current_value)
{
    current_value = (typeof current_value == 'undefined') ? 0 : current_value;
	var sel_region = document.getElementById("region");		
	var code_region = sel_region.options[sel_region.selectedIndex].value;

	$.get("../ajax/get_provincial.php?region=" + code_region, 
		  function(data, status){
				//alert("Data: " + data + "\nStatus: " + status);
				// jQuery style						
				$('#provincial').find('option').remove().end();
				var data = $.trim(data);
				if(data == '') {
					alert("ไม่พบข้อมูล บก. !");
					return;
				}

					//.append('<option value="whatever">text</option>').val('whatever');
				var datas = data.split(';');
				for(var i=0; i<datas.length; i++) {
					if(datas[i] == '') continue;
					datas[i] = datas[i].split(':');
					var code_region = datas[i][0];
					var name_region = datas[i][1];
					//$('#provincial').append('<option value="'+ code_region+'">' + name_region + '</option>');
                    if(current_value == code_region) {
                        $('#provincial').append('<option value="'+ code_region+'" selected>' + name_region + '</option>'); 
                    }
                    else {
                        $('#provincial').append('<option value="'+ code_region+'">' + name_region + '</option>');
                    }
				}
                // Trigger the change event on the amphur dropdown so the tambon dropdown will be populated     
                $('#provincial').trigger('change');
		});
}
    //เลือก สถานี จากจังหวัด
function do_provincial_change(current_value)
{
    current_value = (typeof current_value == 'undefined') ? 0 : current_value;
    
	var sel_region = document.getElementById("region");		
	var provincial_code = sel_region.options[sel_region.selectedIndex].value;
    
    var sel_provincial = document.getElementById("provincial");		
	var station_code = sel_provincial.options[sel_provincial.selectedIndex].value;

	$.get("../ajax/get_station.php?region=" + provincial_code + "&provincial=" + station_code, 
			  function(data, status){
					//alert("Data: " + data + "\nStatus: " + status);
					// jQuery style						
					$('#station').find('option').remove().end();
					var data = $.trim(data);
					if(data == '') {
						alert("ไม่พบข้อมูลสถานี !");
						return;
					}

						//.append('<option value="whatever">text</option>').val('whatever');
					var datas = data.split(';');
					for(var i=0; i<datas.length; i++) {
						if(datas[i] == '') continue;
						datas[i] = datas[i].split(':');
						var station_code = datas[i][0];
						var station_name = datas[i][1];
                        if(current_value == station_code) {
                            $('#station').append('<option value="'+ station_code+'" selected>' + station_name + '</option>'); 
                        }
                        else {
                            $('#station').append('<option value="'+ station_code+'">' + station_name + '</option>');   
                        }						
					}
			});
}
</script> 
	
<script>
// ชื่อ >> value >> set selct auto (สคลิปตรวจสอบ Selected) จำเป็นต้องก็อปปี้ <script src="/jQuery/jquery-3.5.1.min.js" type="text/javascript">
function auto_select(sid, value)
{
    $('#' + sid).val(value);
}

function auto_thaidate(sid, value)
{
	if(value != '') {
		var dates = value.split('-');
		dates = value.split('-');
		if(dates.length > 2) {
			var ys = dates[0];
			var ms = dates[1];
			var ds = dates[2];
			if(ys.substr(0,2) == '20') {
				ys = parseInt(ys) + 543;
			}
			value = $.digit(ds,2) + '-' + $.digit(ms,2) + '-' +ys;
			$('#' + sid).val(value);
		}
	}
}

$(document).ready(function() {
<?php
  //  echo "auto_select('pf_cl_dates', '{$row['pf_cl_dates']}');\n";
    echo "auto_select('pf_cl_type_main', '{$row['pf_cl_type_main']}');\n";
    echo "auto_select('pf_cl_type_sub', '{$row['pf_cl_type_sub']}');\n";
    echo "auto_select('pf_cl_drug', '{$row['pf_cl_drug']}');\n";
    echo "auto_select('pf_cl_drug_sell', '{$row['pf_cl_drug_sell']}');\n";
    echo "auto_select('pf_cl_drug_sell_person', '{$row['pf_cl_drug_sell_person']}');\n";
    echo "auto_select('pf_cl_drug_occupy', '{$row['pf_cl_drug_occupy']}');\n";
    echo "auto_select('pf_cl_drug_occupy_person', '{$row['pf_cl_drug_occupy_person']}');\n";
    echo "auto_select('pf_cl_take_drugs', '{$row['pf_cl_take_drugs']}');\n";
    echo "auto_select('pf_cl_take_drugs_person', '{$row['pf_cl_take_drugs_person']}');\n";
    echo "auto_select('pf_cl_drug_therapy', '{$row['pf_cl_drug_therapy']}');\n";
    echo "auto_select('pf_cl_drug_therapy_person', '{$row['pf_cl_drug_therapy_person']}');\n";
    echo "auto_select('pf_cl_gun', '{$row['pf_cl_gun']}');\n";
    echo "auto_select('pf_cl_gambling_case', '{$row['pf_cl_gambling_case']}');\n";
    echo "auto_select('pf_cl_gambling_person', '{$row['pf_cl_gambling_person']}');\n";
    echo "auto_select('pf_cl_immigrant_case', '{$row['pf_cl_immigrant_case']}');\n";
    echo "auto_select('pf_cl_immigrant_person', '{$row['pf_cl_immigrant_person']}');\n";
    echo "auto_select('pf_cl_techno_case', '{$row['pf_cl_techno_case']}');\n";
    echo "auto_select('pf_cl_techno_person', '{$row['pf_cl_techno_person']}');\n";
    echo "auto_select('pf_cl_wanted', '{$row['pf_cl_wanted']}');\n";
    echo "auto_select('pf_cl_wanted_qty', '{$row['pf_cl_wanted_qty']}');\n";
    echo "auto_select('pf_cl_catch', '{$row['pf_cl_catch']}');\n";  
    echo "auto_select('pf_cl_find', '{$row['pf_cl_find']}');\n";
    echo "auto_select('pf_cl_record', '{$row['pf_cl_record']}');\n";
	// convert db date to thai dates
	//echo "auto_thaidate('pf_cl_dates', '{$row['pf_cl_dates']}');\n";
?>
	setTimeout("do_region_change('<?= $row['provincial'] ?>')", 1000);
    setTimeout("do_provincial_change('<?= $row['station'] ?>')", 2000);
    });
</script>
    
</body>
</html>
