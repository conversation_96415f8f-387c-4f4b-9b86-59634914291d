<?php
include '../Condb.php'; //PDO
include '../users.inc.php';
include '../right_user.php';
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

$loginStation = $station;

$stations = array(
    6707 => array('latitude' => 17.012397, 'longitude' => 99.838612),
    6709 => array('latitude' => 17.***************, 'longitude' => 99.**************),  // เมืองสุโขทัย
	6604 => array('latitude' => 16.**************, 'longitude' => 101.**************),  // ท่าพล เพชรบูรณ์ 
	5207 => array('latitude' => 18.***************, 'longitude' => 99.**************),  // ดอยสะเก็ด เชียงใหม่
	6504 => array('latitude' => 17.***************, 'longitude' => 100.**************), // นครไทย พิษณุโลก
	6912 => array('latitude' => 15.***************, 'longitude' => 100.**************), // อุทัยธานี
	6310 => array('latitude' => 15.***************, 'longitude' => 99.**************),  // บรรพตพิสัย นครสวรรค์
	5903 => array('latitude' => 18.**************5, 'longitude' => 98.99893760891592),  // สืบภาค 5
	61005 => array('latitude' => 16.81279274125541, 'longitude' => 100.25810299723423),  // สืบภาค 6
);

if (isset($stations[$loginStation])) {
    $centerLat = $stations[$loginStation]['latitude'];
    $centerLng = $stations[$loginStation]['longitude'];
} else {
    // Default center coordinates if the login station is not found in the $stations array
    $centerLat = 0;
    $centerLng = 0;
}
// Now, let's update the mapOptions with the dynamic center coordinates
/*$mapOptions = array(
    'center' => array('lat' => $centerLat, 'lng' => $centerLng),
    'zoom' => 9 // Set your desired zoom level here
);*/
// Fixed center coordinates
$fixedCenterLat = 17.209138125837107;
$fixedCenterLng = 99.7446282161556;

// Now, let's update the mapOptions with the fixed center coordinates
$mapOptions = array(
    'center' => array('lat' => $fixedCenterLat, 'lng' => $fixedCenterLng),
    'zoom' => 9 // Set your desired zoom level here
);

?>


<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Maps Display</title>
	<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCIN1hO0EaE0HOhnhxk5i0tC1COym6fWSw&callback=initMap" async defer></script>
    <script>
        function initMap() {
            var mapOptions = <?php echo json_encode($mapOptions); ?>;
            var map = new google.maps.Map(document.getElementById('map'), mapOptions);

            //var map = new google.maps.Map(document.getElementById('map'), mapOptions);

            // Fetch location data from the database using PHP and PDO
            <?php
            $sql = "SELECT *, PV.pv_name_th AS PV_name, AP.ap_name_th AS AP_name, TB.tb_name_th AS TB_name FROM cctv_locations L
					LEFT JOIN provinces as PV ON PV.pv_code = L.cctv_province
					LEFT JOIN amphures as AP ON AP.ap_code = L.cctv_amphur
					LEFT JOIN districts as TB ON TB.tb_id = L.cctv_tumbon
					WHERE provincial=67 ";
			$stmt = $pdo->prepare($sql);
			$stmt->execute();
			$locations = $stmt->fetchAll(PDO::FETCH_ASSOC);

            foreach ($locations as $location) {
				
				$adr_cctv = $location['cctv_adr'] . ' หมู่ที่ ' . $location['cctv_moo'] . ' ต.' . $location['TB_name'] . ' อ.' . $location['AP_name'] . ' จ.' . $location['PV_name'];
				
                //$color = isset($location['pin_color']) ? $location['pin_color'] : "red"; // Default color if no color specified
                $content = '<div><strong>@ :</strong> ' . $location['location_name'] . '</div>' .
						   '<div>ที่อยู่ : ' . $adr_cctv . '</div>' .
						   '<div>ผู้รับผิดชอบ : ' . $location['cctv_admin'] . ' </div>' .
						   '<div>เบอร์โทร : ' . $location['cctv_contact'] . ' </div>' .
						   '<hr>' .
                           '<div align="center"><img src="' . $location['pin_image'] . '" alt="Image" style="max-width: 200px;"></div>'
						   ;

                // Check if the color has a corresponding marker icon, otherwise use the default color
                //$iconUrl = isset($colorToMarkerIcon[$color]) ? $colorToMarkerIcon[$color] : $colorToMarkerIcon['red'];

                echo "var marker = new google.maps.Marker({
                    position: {lat: {$location['latitude']}, lng: {$location['longitude']}},
                    map: map,
                    title: '{$location['location_name']}',
                    label: '{$location['location_name']}'
                    
                });

               var infoWindow = new google.maps.InfoWindow();

                // Create a function to associate the marker and infoWindow content correctly
                (function(marker, content) {
                    google.maps.event.addListener(marker, 'click', function() {
                        console.log('Marker clicked: ' + marker.title);
                        infoWindow.setContent(content);
                        infoWindow.open(map, marker);
                    });
                })(marker, '$content');";
            }
            ?>
        }
    </script>
	<style>
		.headtext {
			margin-top: 20px;
		}
	</style>
</head>
<body>
	<div align="center" class="headtext"> <h2>ข้อมูลแผนที่กล้องวงจรปิด (CCTV Mapping) ในพื้นที่ ภ.จว.สุโขทัย</h2></div>
	<p align="right">คุณ Login ด้วย User <?= $name_station ?>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</p><br>
    <div id="map" style="height: 500px;"></div><br>
	<div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="../WatchmanData/main.php" class="btn btn-primary btn-lg mb-4" >หน้าหลัก</a>
	&nbsp;&nbsp;<a href="../Bansuan/GoogleMapsDisplay.php" class="btn btn-warning btn-lg mb-4 rounded-pill" >ย้อนกลับ</a>
	</div>
</body>
</html>
