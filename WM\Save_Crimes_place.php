<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../Alert.php';
include '../right_user.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);
$ip_address = $_SERVER['REMOTE_ADDR'];
$action = 'Save Crimes place'; // You can set the action to a suitable value
$data = str_replace("'", "", compact_array($_POST));
log_activity($acc, $action, $data, $ip_address); // ใช้ function ในไฟล์ Condb.php

$timestamp = '';
$timezone = new DateTimeZone('Asia/Bangkok');
$datetime = new DateTime($timestamp);
$datetime->setTimezone($timezone);
$timestamp = $datetime->format('Y-m-d H:i:s');

//echo '<pre>';
//print_r($_POST);
//echo '</pre>';
//
//echo '<hr>';
//exit;
$gc_cl_aid = $_POST['gc_cl_aid'];
$gc_cl_place = $_POST[ 'gc_cl_place' ];
$gc_cl_crimes_place_type = $_POST[ 'gc_cl_crimes_place_type' ];
$gc_cl_name = $_POST[ 'gc_cl_name' ];
$gc_cl_place_address = $_POST[ 'gc_cl_place_address' ];
$gc_cl_place_phone = $_POST[ 'gc_cl_place_phone' ];
$gc_cl_owner_name = $_POST[ 'gc_cl_owner_name' ];
$gc_cl_owner_bd1 = $_POST[ 'gc_cl_owner_bd1' ];
$gc_cl_owner_bd2 = $_POST[ 'gc_cl_owner_bd2' ];
$gc_cl_owner_idcard = $_POST[ 'gc_cl_owner_idcard' ];
$gc_cl_owner_adr = $_POST[ 'gc_cl_owner_adr' ];
$gc_cl_mgr_name = $_POST[ 'gc_cl_mgr_name' ];
$gc_cl_mgr_bd1 = $_POST[ 'gc_cl_mgr_bd1' ];
$gc_cl_mgr_bd2 = $_POST[ 'gc_cl_mgr_bd2' ];
$gc_cl_mgr_idcard = $_POST[ 'gc_cl_mgr_idcard' ];
$gc_cl_mgr_adr = $_POST[ 'gc_cl_mgr_adr' ];
$gc_cl_mgr_adr_phone = $_POST[ 'gc_cl_mgr_adr_phone' ];
$gc_cl_mgr_adr_current = $_POST[ 'gc_cl_mgr_adr_current' ];
$gc_cl_mgr_adr_current_phone = $_POST[ 'gc_cl_mgr_adr_current_phone' ];
$gc_cl_general_charac = $_POST[ 'gc_cl_general_charac' ];
$gc_cl_activity = $_POST[ 'gc_cl_activity' ];
$gc_cl_important_info = $_POST[ 'gc_cl_important_info' ];
$gc_cl_crimes = $_POST[ 'gc_cl_crimes' ];
$gc_cl_recorder = $member_name;			
$gc_cl_date_record = $timestamp;		
$region = $region_user;
$provincial = $provincial_user;
$station = $station_user;
$gc_cl_place_lat = $_POST[ 'gc_cl_place_lat' ];
$gc_cl_place_lon = $_POST[ 'gc_cl_place_lon' ];

if (!is_numeric($gc_cl_place_lat)) {
    $gc_cl_place_lat = 0;
}
if (!is_numeric($gc_cl_place_lon)) {
    $gc_cl_place_lon = 0;
}

// ล้างข้อมูล Lat Lon ที่ติดเครื่องหมายคอมม่ามา
$sanitizedLat = sanitizeCoordinate($gc_cl_place_lat);
$sanitizedLon = sanitizeCoordinate($gc_cl_place_lon);

// Remove non-numeric characters
$gc_cl_owner_idcard = preg_replace('/[^0-9]/', '', $gc_cl_owner_idcard);
$gc_cl_mgr_idcard = preg_replace('/[^0-9]/', '', $gc_cl_mgr_idcard);

// save Image
$file1 = $_FILES[ 'gc_cl_place_image' ][ 'tmp_name' ];
if ( is_uploaded_file( $file1 ) ) {
    // เปลี่ยนชื่อไฟล์ก่อนบันทึก แก้ปัญหา ชื่อซ้ำกัน
    $gc_cl_place_image = "/policeinnopolis/uploaded/Image1/" . basename( $_FILES[ 'gc_cl_place_image' ][ 'name' ] );	    
    // file + ext    
    $ext = strrchr($gc_cl_place_image, ".");    
    // เพิ่มค่า $station ต่อท้ายชื่อไฟล์
    $gc_cl_place_image = "../policeinnopolis/uploaded/Image1/CrimesPlace_" . time() . "_" . $station . $ext; // file+time+station+ext        
    move_uploaded_file( $file1, $gc_cl_place_image );
    
} else {
  $gc_cl_place_image = '';
}

try{
$sql ="SELECT * FROM `wm_tb_place_data` WHERE gc_cl_aid = :gc_cl_aid ";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':gc_cl_aid', $gc_cl_aid);
    $stmt->execute();
    $row = $stmt->fetch(PDO::FETCH_ASSOC);
    
if($row) {
    $gc_cl_aid = $row['gc_cl_aid'];
    
    if($gc_cl_place_image !== ''){
        $sql = "UPDATE wm_tb_place_data SET " .
            "gc_cl_place = :gc_cl_place," .
            "gc_cl_crimes_place_type = :gc_cl_crimes_place_type," .
            "gc_cl_name = :gc_cl_name, " . 
            "gc_cl_place_address = :gc_cl_place_address," .
            "gc_cl_place_phone = :gc_cl_place_phone,".
            "gc_cl_owner_name = :gc_cl_owner_name, ".
			
			"gc_cl_owner_bd1 = :gc_cl_owner_bd1, ".
			"gc_cl_owner_bd2 = :gc_cl_owner_bd2, ".
			
            "gc_cl_owner_idcard = :gc_cl_owner_idcard, " .
			
			"gc_cl_owner_adr = :gc_cl_owner_adr, " .
			
            "gc_cl_mgr_name = :gc_cl_mgr_name, " .
			
			"gc_cl_mgr_bd1 = :gc_cl_mgr_bd1, " .
			"gc_cl_mgr_bd2 = :gc_cl_mgr_bd2, " .
			
            "gc_cl_mgr_idcard = :gc_cl_mgr_idcard, " .
			
			"gc_cl_mgr_adr = :gc_cl_mgr_adr, " .
			"gc_cl_mgr_adr_phone = :gc_cl_mgr_adr_phone, " .
			"gc_cl_mgr_adr_current = :gc_cl_mgr_adr_current, " .
			"gc_cl_mgr_adr_current_phone = :gc_cl_mgr_adr_current_phone, " .
			
            "gc_cl_general_charac = :gc_cl_general_charac, " .
            "gc_cl_activity = :gc_cl_activity, " .
            "gc_cl_important_info = :gc_cl_important_info, " .
            "gc_cl_crimes = :gc_cl_crimes, " .
            "gc_cl_recorder = :gc_cl_recorder, " .
            "gc_cl_date_record = :gc_cl_date_record, " .
            "region = :region, " .
            "provincial = :provincial, " .
            "station = :station, " .
            "gc_cl_place_lat = :gc_cl_place_lat, " .
            "gc_cl_place_lon = :gc_cl_place_lon, " .
            "gc_cl_place_image = :gc_cl_place_image " .
            "WHERE gc_cl_aid = :gc_cl_aid ";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':gc_cl_aid', $gc_cl_aid);
        $stmt->bindParam(':gc_cl_place', $gc_cl_place);
        $stmt->bindParam(':gc_cl_crimes_place_type', $gc_cl_crimes_place_type);
        $stmt->bindParam(':gc_cl_name', $gc_cl_name);
        $stmt->bindParam(':gc_cl_place_address', $gc_cl_place_address);
        $stmt->bindParam(':gc_cl_place_phone', $gc_cl_place_phone);
        $stmt->bindParam(':gc_cl_owner_name', $gc_cl_owner_name);
		$stmt->bindParam(':gc_cl_owner_bd1', $gc_cl_owner_bd1);
		$stmt->bindParam(':gc_cl_owner_bd2', $gc_cl_owner_bd2);
        $stmt->bindParam(':gc_cl_owner_idcard', $gc_cl_owner_idcard);
		$stmt->bindParam(':gc_cl_owner_adr', $gc_cl_owner_adr);
        $stmt->bindParam(':gc_cl_mgr_name', $gc_cl_mgr_name);
		$stmt->bindParam(':gc_cl_mgr_bd1', $gc_cl_mgr_bd1);
		$stmt->bindParam(':gc_cl_mgr_bd2', $gc_cl_mgr_bd2);
        $stmt->bindParam(':gc_cl_mgr_idcard', $gc_cl_mgr_idcard);
		$stmt->bindParam(':gc_cl_mgr_adr', $gc_cl_mgr_adr);
		$stmt->bindParam(':gc_cl_mgr_adr_phone', $gc_cl_mgr_adr_phone);
		$stmt->bindParam(':gc_cl_mgr_adr_current', $gc_cl_mgr_adr_current);
		$stmt->bindParam(':gc_cl_mgr_adr_current_phone', $gc_cl_mgr_adr_current_phone);
        $stmt->bindParam(':gc_cl_general_charac', $gc_cl_general_charac);
        $stmt->bindParam(':gc_cl_activity', $gc_cl_activity);
        $stmt->bindParam(':gc_cl_important_info', $gc_cl_important_info);
        $stmt->bindParam(':gc_cl_crimes', $gc_cl_crimes);
        $stmt->bindParam(':gc_cl_recorder', $gc_cl_recorder);
        $stmt->bindParam(':gc_cl_date_record', $gc_cl_date_record);
        $stmt->bindParam(':region', $region);
        $stmt->bindParam(':provincial', $provincial);
        $stmt->bindParam(':station', $station);
        $stmt->bindParam(':gc_cl_place_lat', $sanitizedLat);
        $stmt->bindParam(':gc_cl_place_lon', $sanitizedLon);
        $stmt->bindParam(':gc_cl_place_image', $gc_cl_place_image);
    }else{
        $sql = "UPDATE wm_tb_place_data SET " .
            "gc_cl_place = :gc_cl_place," .
            "gc_cl_crimes_place_type = :gc_cl_crimes_place_type," .
            "gc_cl_name = :gc_cl_name, " . 
            "gc_cl_place_address = :gc_cl_place_address," .
            "gc_cl_place_phone = :gc_cl_place_phone,".
            "gc_cl_owner_name = :gc_cl_owner_name, ".
			"gc_cl_owner_bd1 = :gc_cl_owner_bd1, ".
			"gc_cl_owner_bd2 = :gc_cl_owner_bd2, ".
            "gc_cl_owner_idcard = :gc_cl_owner_idcard, " .
			"gc_cl_owner_adr = :gc_cl_owner_adr, " .
            "gc_cl_mgr_name = :gc_cl_mgr_name, " .
			"gc_cl_mgr_bd1 = :gc_cl_mgr_bd1, " .
			"gc_cl_mgr_bd2 = :gc_cl_mgr_bd2, " .
            "gc_cl_mgr_idcard = :gc_cl_mgr_idcard, " .
			"gc_cl_mgr_adr = :gc_cl_mgr_adr, " .
			"gc_cl_mgr_adr_phone = :gc_cl_mgr_adr_phone, " .
			"gc_cl_mgr_adr_current = :gc_cl_mgr_adr_current, " .
			"gc_cl_mgr_adr_current_phone = :gc_cl_mgr_adr_current_phone, " .
            "gc_cl_general_charac = :gc_cl_general_charac, " .
            "gc_cl_activity = :gc_cl_activity, " .
            "gc_cl_important_info = :gc_cl_important_info, " .
            "gc_cl_crimes = :gc_cl_crimes, " .
            "gc_cl_recorder = :gc_cl_recorder, " .
            "gc_cl_date_record = :gc_cl_date_record, " .
            "region = :region, " .
            "provincial = :provincial, " .
            "station = :station, " .
            "gc_cl_place_lat = :gc_cl_place_lat, " .
            "gc_cl_place_lon = :gc_cl_place_lon " .
            "WHERE gc_cl_aid = :gc_cl_aid ";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':gc_cl_aid', $gc_cl_aid);
        $stmt->bindParam(':gc_cl_place', $gc_cl_place);
        $stmt->bindParam(':gc_cl_crimes_place_type', $gc_cl_crimes_place_type);
        $stmt->bindParam(':gc_cl_name', $gc_cl_name);
        $stmt->bindParam(':gc_cl_place_address', $gc_cl_place_address);
        $stmt->bindParam(':gc_cl_place_phone', $gc_cl_place_phone);
        $stmt->bindParam(':gc_cl_owner_name', $gc_cl_owner_name);
		$stmt->bindParam(':gc_cl_owner_bd1', $gc_cl_owner_bd1);
		$stmt->bindParam(':gc_cl_owner_bd2', $gc_cl_owner_bd2);
        $stmt->bindParam(':gc_cl_owner_idcard', $gc_cl_owner_idcard);
		$stmt->bindParam(':gc_cl_owner_adr', $gc_cl_owner_adr);
        $stmt->bindParam(':gc_cl_mgr_name', $gc_cl_mgr_name);
		$stmt->bindParam(':gc_cl_mgr_bd1', $gc_cl_mgr_bd1);
		$stmt->bindParam(':gc_cl_mgr_bd2', $gc_cl_mgr_bd2);	
        $stmt->bindParam(':gc_cl_mgr_idcard', $gc_cl_mgr_idcard);	
		$stmt->bindParam(':gc_cl_mgr_adr', $gc_cl_mgr_adr);
		$stmt->bindParam(':gc_cl_mgr_adr_phone', $gc_cl_mgr_adr_phone);
		$stmt->bindParam(':gc_cl_mgr_adr_current', $gc_cl_mgr_adr_current);
		$stmt->bindParam(':gc_cl_mgr_adr_current_phone', $gc_cl_mgr_adr_current_phone);
        $stmt->bindParam(':gc_cl_general_charac', $gc_cl_general_charac);
        $stmt->bindParam(':gc_cl_activity', $gc_cl_activity);
        $stmt->bindParam(':gc_cl_important_info', $gc_cl_important_info);
        $stmt->bindParam(':gc_cl_crimes', $gc_cl_crimes);
        $stmt->bindParam(':gc_cl_recorder', $gc_cl_recorder);
        $stmt->bindParam(':gc_cl_date_record', $gc_cl_date_record);
        $stmt->bindParam(':region', $region);
        $stmt->bindParam(':provincial', $provincial);
        $stmt->bindParam(':station', $station);
        $stmt->bindParam(':gc_cl_place_lat', $sanitizedLat);
        $stmt->bindParam(':gc_cl_place_lon', $sanitizedLon);
    }
}else{
$sql = "INSERT INTO wm_tb_place_data(gc_cl_place, gc_cl_crimes_place_type, gc_cl_name, gc_cl_place_address, gc_cl_place_phone, gc_cl_owner_name, gc_cl_owner_bd1, gc_cl_owner_bd2, gc_cl_owner_idcard, gc_cl_owner_adr, gc_cl_mgr_name, gc_cl_mgr_bd1, gc_cl_mgr_bd2, gc_cl_mgr_idcard, gc_cl_mgr_adr, gc_cl_mgr_adr_phone, gc_cl_mgr_adr_current, gc_cl_mgr_adr_current_phone, gc_cl_general_charac, gc_cl_activity, gc_cl_important_info, gc_cl_crimes, gc_cl_recorder, gc_cl_date_record, region, provincial, station, gc_cl_place_lat, gc_cl_place_lon, gc_cl_place_image) 
VALUES(:gc_cl_place, :gc_cl_crimes_place_type, :gc_cl_name, :gc_cl_place_address, :gc_cl_place_phone, :gc_cl_owner_name, :gc_cl_owner_bd1, :gc_cl_owner_bd2, :gc_cl_owner_idcard, :gc_cl_owner_adr, :gc_cl_mgr_name, :gc_cl_mgr_bd1, :gc_cl_mgr_bd2, :gc_cl_mgr_idcard, :gc_cl_mgr_adr, :gc_cl_mgr_adr_phone, :gc_cl_mgr_adr_current,  :gc_cl_mgr_adr_current_phone, :gc_cl_general_charac, :gc_cl_activity, :gc_cl_important_info, :gc_cl_crimes, :gc_cl_recorder, :gc_cl_date_record, :region, :provincial, :station, :gc_cl_place_lat, :gc_cl_place_lon, :gc_cl_place_image) ";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':gc_cl_place', $gc_cl_place);
        $stmt->bindParam(':gc_cl_crimes_place_type', $gc_cl_crimes_place_type);
        $stmt->bindParam(':gc_cl_name', $gc_cl_name);
        $stmt->bindParam(':gc_cl_place_address', $gc_cl_place_address);
        $stmt->bindParam(':gc_cl_place_phone', $gc_cl_place_phone);
        $stmt->bindParam(':gc_cl_owner_name', $gc_cl_owner_name);
        $stmt->bindParam(':gc_cl_owner_bd1', $gc_cl_owner_bd1);
		$stmt->bindParam(':gc_cl_owner_bd2', $gc_cl_owner_bd2);
        $stmt->bindParam(':gc_cl_owner_idcard', $gc_cl_owner_idcard);
		$stmt->bindParam(':gc_cl_owner_adr', $gc_cl_owner_adr);
        $stmt->bindParam(':gc_cl_mgr_name', $gc_cl_mgr_name);
		$stmt->bindParam(':gc_cl_mgr_bd1', $gc_cl_mgr_bd1);
		$stmt->bindParam(':gc_cl_mgr_bd2', $gc_cl_mgr_bd2);	
        $stmt->bindParam(':gc_cl_mgr_idcard', $gc_cl_mgr_idcard);	
		$stmt->bindParam(':gc_cl_mgr_adr', $gc_cl_mgr_adr);
		$stmt->bindParam(':gc_cl_mgr_adr_phone', $gc_cl_mgr_adr_phone);
		$stmt->bindParam(':gc_cl_mgr_adr_current', $gc_cl_mgr_adr_current);
		$stmt->bindParam(':gc_cl_mgr_adr_current_phone', $gc_cl_mgr_adr_current_phone);
        $stmt->bindParam(':gc_cl_general_charac', $gc_cl_general_charac);
        $stmt->bindParam(':gc_cl_activity', $gc_cl_activity);
        $stmt->bindParam(':gc_cl_important_info', $gc_cl_important_info);
        $stmt->bindParam(':gc_cl_crimes', $gc_cl_crimes);
        $stmt->bindParam(':gc_cl_recorder', $gc_cl_recorder);
        $stmt->bindParam(':gc_cl_date_record', $gc_cl_date_record);
        $stmt->bindParam(':region', $region);
        $stmt->bindParam(':provincial', $provincial);
        $stmt->bindParam(':station', $station);
        $stmt->bindParam(':gc_cl_place_lat', $sanitizedLat);
        $stmt->bindParam(':gc_cl_place_lon', $sanitizedLon);
        $stmt->bindParam(':gc_cl_place_image', $gc_cl_place_image);
}

$result = $stmt->execute();

    if ($result) {
        $_SESSION['success'] = "Data has been inserted succesfully";
        showSweetAlert("Saved successfully", "บันทึกข้อมูลเรียบร้อย");
        unset($_SESSION['success']); // Clear the session variable
        header("refresh:2; url=/WM/Show_Crimes_place.php?pcid={$gc_cl_aid}");
    } else {
        $_SESSION['error'] = "Error";
        showSweetAlert('Error', "บันทึกข้อมูล ไม่สำเร็จ", 'error');
        unset($_SESSION['error']); // Clear the session variable
        header("refresh:2; url=/WM/Show_Crimes_place.php?pcid={$gc_cl_aid}");
    }
}catch(Exception $e){
    echo 'Query Failed: '. $e->getMessage();
}

?>