<?php
// Database connection parameters
include '../Condb.php';
include '../users.inc.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

$userLat = $_POST[ 'lat' ];
$userLong = $_POST[ 'long' ];
// User's latitude and longitude
//$userLat = $_GET['lat'];
//$userLong = $_GET['long'];
/*$userLat = 17.***************;
$userLong = 99.**************;*/

// Radius in kilometers
$radius = 2;

// Query nearby places
$query = "SELECT ad_cl_aid, ad_cl_num, ad_cl_lat, ad_cl_lon,
          (6371 * ACOS(COS(RADIANS(:lat)) * COS(RADIANS(ad_cl_lat)) * COS(RADIANS(ad_cl_lon) - RADIANS(:long)) + SIN(RADIANS(:lat)) * SIN(RADIANS(ad_cl_lat)))) AS distance
          FROM wm_tb_address
          HAVING distance <= :radius
          ORDER BY distance";

$stmt = $pdo->prepare($query);
$stmt->bindParam(':lat', $userLat);
$stmt->bindParam(':long', $userLong);
$stmt->bindParam(':radius', $radius);
$stmt->execute();
$places = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html>
<head>
    <title>Nearby Places</title>
    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCIN1hO0EaE0HOhnhxk5i0tC1COym6fWSw&callback=initMap" async defer></script>
	<!--<script src='https://maps.googleapis.com/maps/api/distancematrix/json?origins=Washington%2C%20DC&destinations=New%20York%20City%2C%20NY&units=imperial&key=YOUR_API_KEY'
	</script>-->
    <script>
        var map;
		var userLocation = {lat: <?php echo $userLat; ?>, lng: <?php echo $userLong; ?>};
		var radiusCircle;

        function initMap() {
        map = new google.maps.Map(document.getElementById('map'), {
            center: userLocation,
            zoom: 12
        });

        radiusCircle = new google.maps.Circle({
            strokeColor: '#FF0000',
            strokeOpacity: 0.8,
            strokeWeight: 2,
            fillColor: '#FF0000',
            fillOpacity: 0.35,
            map: map,
            center: userLocation,
            radius: <?php echo $radius * 1000; ?> // Convert radius to meters
        });

        <?php foreach ($places as $place) { ?>
            var marker = new google.maps.Marker({
                position: {lat: <?php echo $place['ad_cl_lat']; ?>, lng: <?php echo $place['ad_cl_lon']; ?>},
                map: map,
                title: '<?php echo $place['ad_cl_num']; ?>'
            });
        <?php } ?>
    }
</script>
</head>
<body>
    <h1>Nearby Places</h1>
    <div id="map" style="height: 400px;"></div>
</body>
</html>