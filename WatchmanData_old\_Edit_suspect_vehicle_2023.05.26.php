<?php
include '../Condb.php';
include '../users.inc.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// เมื่อมีการใช้ ตัวแปร Session จะต้องเปิดก่อน
//session_start();  // เปิดตัวแปร session
/*if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
*/
$id = $_GET['id'];
$pcid = $_GET['pcid'];

$sql = "SELECT * FROM `wm_tb_suspect_vehicle` WHERE `sv_cl_aid`='$id' " ;
$result = mysqli_query($conn,$sql);
$row = mysqli_fetch_array($result);

// สำหรับเลือก จังหวัด อำเภอ ตำบล
include("../config.inc.php");
include("../classes/class.database.inc.php");
$conn2 = get_connection(); // connect to mySQLDB

// สำหรับเลือกสถานีตำรวจทั่วประเทศ
$region = isset($_GET['region']) ? $_GET['region'] : 0; // sukhothai
$provincial = isset($_GET['provincial']) ? $_GET['provincial'] : 0; // sukhothai
$station = isset($_GET['station']) ? $_GET['station'] : 0; // sukhothai

?>


<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>แก้ไขข้อมูลการตรวจยึดรถต้องสงสัย</title>
<script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/policeinnopolis/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 18px;
    }
input::placeholder {
  color: red;
}
.form-control::placeholder {
  color: sandybrown;
}
</style>
<!-- สำหรับ วันที่ แบบ พ.ศ. -->
<link rel="stylesheet" href="http://code.jquery.com/ui/1.9.1/themes/base/jquery-ui.css" />
<script src="http://code.jquery.com/jquery-1.8.2.js"></script>
<script src="/policeinnopolis/js/jquery-ui.js"></script>
</head>

<body>
	
	<div class="container">
	<div class="row">
	<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> แก้ไขข้อมูลการตรวจยึดรถต้องสงสัย </div>
	<form action="Save_suspect_vehicle.php" method="POST" enctype="multipart/form-data" class="body">
	<span style="color: #1203F8">
		
        <label hidden ="hidden">ลำดับ</label>
		<input type = "text" name = "sv_cl_aid" class="form-control" value= "<?= $row['sv_cl_aid']?>" hidden ="hidden" >
        
        <label>วันที่ตรวจยึดรถ <span style="color: #F90004">* จำเป็น</span> </label>
        <p><input type="text" name="sv_cl_date" id="datepicker" class="form-control" value= "<?= $row['sv_cl_date']?>" autocomplete="off" required></p>
        <script>
            function set_cal(ele)//function สร้างตัวเลือกปฎิทิน
            {
              $( ele ).datepicker({
                  onSelect:(date_text)=>
                  {
                    let arr=date_text.split("/");
                    let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])+543).toString();
                    $(ele).val(new_date);
                    $(ele).css("color","");
                  },
                  beforeShow:()=>{

                    if($(ele).val()!="")
                    {
                      let arr=$(ele).val().split("/");
                      let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])-543).toString();
                      $(ele).val(new_date);

                    }

                    $(ele).css("color","white");
                  },
                  onClose:()=>{

                      $(ele).css("color","");

                      if($(ele).val()!="")
                      {
                          let arr=$(ele).val().split("-");
                          if(parseInt(arr[2])<2500)
                          {
                              let new_date=arr[0]+"-"+arr[1]+"-"+(parseInt(arr[2])).toString();
                              $(ele).val(new_date);
                          }
                      }
                  },
                  dateFormat:"yy-mm-dd", //กำหนดรูปแบบวันที่เป็น วัน/เดือน/ปี
                  changeMonth:true,//กำหนดให้เลือกเดือนได้
                  changeYear:true,//กำหนดให้เลือกปีได้
                  showOtherMonths:true,//กำหนดให้แสดงวันของเดือนก่อนหน้าได้
              });
            }
          $(document).ready(function(){
            //เรียก function set_cal เมื่อเปิดหน้าเว็บ โดยส่ง object element ที่มี id เป็น datepicker เป็นพารามิเตอร์
            set_cal( $("#datepicker") );
          })
          </script>
        <!-- สำหรับ วันที่ แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ -->
		
		<label>ประเภทรถ</label>
		    <select id="sv_cl_vehicle_type" name="sv_cl_vehicle_type" class="form-select form-select-sm" >
                <option value="" selected> </option>  <!-- ดึงข้อมูลมาจากตารางใหม่ -->
                <?php
                    $res_v = mysqli_query($conn, "SELECT * FROM wm_tb_vehicle_type order by vt_cl_aid ASC");
                    while($row_v = mysqli_fetch_array($res_v))
                    {
                        echo "<option value='{$row_v['vt_cl_aid']}'>{$row_v['vt_cl_vehicle_type']}</option>";
                    }
                ?>
            </select>
		
		<label>ทะเบียนรถ</label>
		<input type = "text" name = "sv_cl_veh_plate" class="form-control" value= "<?= $row['sv_cl_veh_plate']?>" placeholder="ระบุทะเบียนรถ"   >
        
        <label>ยี่ห้อ</label>
		<input type = "text" name = "sv_cl_brand" class="form-control" value= "<?= $row['sv_cl_brand']?>" placeholder="ระบุยี่ห้อรถ"   >
        
        <label>รุ่น</label>
		<input type = "text" name = "sv_cl_model" class="form-control" value= "<?= $row['sv_cl_model']?>" placeholder="ระบุรุ่นรถ"   >
        
        <label>สี</label>
		<input type = "text" name = "sv_cl_color" class="form-control" value= "<?= $row['sv_cl_color']?>" placeholder="ระบุสีรถ"   >
        
        <label>เหตุที่ต้องสงสัย</label>
		<input type = "text" name = "sv_cl_suspect" class="form-control" value= "<?= $row['sv_cl_suspect']?>" placeholder="ระบุเหตุที่ต้องสงสัย"   >
        
        <label>สถานที่ตรวจยึด</label>
		<input type = "text" name = "sv_cl_place" class="form-control" value= "<?= $row['sv_cl_place']?>" placeholder="สถานที่ตรวจยึด"   >
        
        <label>ผู้ขับขี่</label>
		<input type = "text" name = "sv_cl_driver" class="form-control" value= "<?= $row['sv_cl_driver']?>" placeholder="ผู้ขับขี่ขณะตรวจยึด"   >
        
        <label>เบอร์โทร</label>
		<input type = "text" name = "sv_cl_phone_driver" class="form-control" value= "<?= $row['sv_cl_phone_driver']?>" placeholder="ระบุเบอร์โทร"   >
        
		<label>ผู้ตรวจยึด</label>
		<select id="sv_cl_police" name="sv_cl_police" class="form-select form-select-sm" placeholder="ระบุผู้ตรวจยึด">
			<option value="" selected> </option>
            <?php
                    $bsdp = mysqli_query($conn, "SELECT * FROM `police_name_bsdetective` order by `aid_bsdp` DESC");
                    while($row_bsdp = mysqli_fetch_array($bsdp))
                    {
                        echo "<option value='{$row_bsdp['aid_bsdp']}'>{$row_bsdp['bsdp_name']}</option>";
                    }
                ?>
		</select>
        
        <b>การดำเนินการตรวจสอบ</b><br>
        <label>ผลการตรวจสอบ</label>
		<select id="checking" name="checking" class="form-select form-select-sm" placeholder="ผลการตรวจสอบ">
			<option value="" selected></option>
            <option value="1">เกี่ยวกับคดี</option>
            <option value="2">ไม่เกี่ยวกับคดี</option>
            <option value="3">ยังไม่ได้ตรวจสอบ</option>
		</select>
        
        <label>ผลการดำเนินการ</label>
		<select id="action" name="action" class="form-select form-select-sm" placeholder="ผลการดำเนินการ">
			<option value="" selected></option>
            <option value="1">ส่ง พงส.</option>
            <option value="2">ส่งคืนเจ้าของ</option>
		</select>
        
         <label>ส่งพนักงานสอบสวน</label>
		<select id="Send_inquiry_official" name="Send_inquiry_official" class="form-select form-select-sm" placeholder="ระบุจำนวนที่ส่งพนักงานสอบสวน">
			<option value="" selected></option>
            <option value="1">1</option>
            <option value="2">2</option>
            <option value="3">3</option>
            <option value="4">4</option>
		</select>
        
        <label>พนักงานสอบสวน</label>
		<select id="sv_cl_inquiry_official" name="sv_cl_inquiry_official" class="form-select form-select-sm" placeholder="ระบุพนักงานสอบสวน">
			<option value="" selected> </option>
            <?php
                $bs_inq = mysqli_query($conn, "SELECT * FROM `police_name_bs_inqinquiry` ORDER BY `police_name_bs_inqinquiry`.`aid_bs_inq` ASC");
                while($row_inq = mysqli_fetch_array($bs_inq))
                {
                    echo "<option value='{$row_inq['aid_bs_inq']}'>{$row_inq['bs_inq_name']}</option>";
                }
            ?>
		</select>
		
        <br>
        <label>สถานีตำรวจ <span style="color: #F90004">* จำเป็น</span></label><br>
                <!-- เลือกสถานีตำรวจ -->
        <label> บช. </label><br>
        <select name="region" id="region" onChange="do_region_change()" class="form-control" Required >
                <option value="">&lt;&lt; เลือก บช. &gt;&gt;</option>
                <?php
                $region = $row['region']; // <<ดึงข้อมูลจาก ตารางมา row ซึ่งเป็นคนละตัวกับ row_p
                //------------------------------------
			  	$res_re = $conn2->query("SELECT * FROM `wm_tb_police_region` 
                                            ORDER BY `name_region` ASC");
			     $selected = '';
			     while($row_re = $conn2->fetch_row($res_re)) 
                     {
                      $code_region = $row_re['code_region'];
                      $name_region = $row_re['name_region'];
                      // set default provionce to 64 >> sukhothai
                          if($code_region == $region) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$code_region' $selected> $name_region </option>\n";
                     }
			  ?>
              </select>
        
        
        <label> บก. </label><br>        
        <select name="provincial" id="provincial" onChange="do_provincial_change()" value="<?php echo $provincial; ?>" class="form-control" >
        <option value="0">&lt;&lt; เลือก บก. &gt;&gt;</option>
                <?php
                if($region > 0)
                {
                    $provincial = $row['provincial']; // <<
                    //-------------------------------------
                    $res_prov = $conn2->query("SELECT * FROM `wm_tb_police_provincial` 
                                                WHERE `region_code`='$region' 
                                                ORDER BY `provincial_code` ASC");
                    $selected = '';
                  while($res_prov = $conn2->fetch_row($res_prov)) 
                  {
                      $provincial_code = $res_prov['provincial_code']; // 
                      $provincial = $res_prov['provincial'];
                      // set default provionce to 64 >> sukhothai
                          if($provincial_code == $provincial) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$provincial_code' $selected> $provincial </option>\n";
                  }
                }	
                ?>
              </select>
        
         <label> สน./สภ. </label>
        <br>
                
        <select name="station" id="station" class="form-control" >
                <option value="0">&lt;&lt; เลือก สน./สภ. &gt;&gt;</option>
                <?php
	           if($provincial > 0)
                {
                   $station = $row['station'];
                   //------------------------------------------
                    $res_st = $conn2->query("SELECT * FROM `wm_tb_police_station2` 
                                                WHERE provincial_code='$provincial_code'
                                                ORDER BY `station_code` ASC");
                    $selected = '';
                  while($row_st = $conn2->fetch_row($res_st)) 
                  {
                      $station_code = $row_st['station_code'];
                      $station_name = $row_st['station_name'];
                          if($station_code == $station) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      echo "<option value='$station_code' $selected> $station_name </option>\n";
                  }
                }	
            
                ?>                                                                     
              </select>
		<br>
        
		<label>การดำเนินการ</label>
		<input type = "text" name = "sv_cl_action" class="form-control" value= "<?= $row['sv_cl_action']?>" placeholder="การดำเนินการ" >
        
        <label>เจ้าของกรรมสิทธิ์</label>
		<input type = "text" name = "sv_cl_owner" class="form-control" value= "<?= $row['sv_cl_owner']?>" placeholder="เจ้าของกรรมสิทธิ์" >
        
        <label>ผู้รับคืนรถ</label>
		<input type = "text" name = "sv_cl_return" class="form-control" value= "<?= $row['sv_cl_return']?>" placeholder="ผู้รับคืนรถ" >
        
        <label>เบอร์โทรผู้รับคืน</label>
		<input type = "text" name = "sv_cl_phone_return" class="form-control" value= "<?= $row['sv_cl_phone_return']?>" placeholder="เบอร์โทรผู้รับคืน" >
        
        <label>วันที่ดำเนินการ <span style="color: #F90004">* จำเป็น</span> </label>
       <p><input type="text" name="sv_cl_action_date" id="datepicker2" class="form-control" value= "<?= $row['sv_cl_action_date']?>" autocomplete="off" required></p>
        <script>
            function set_cal(ele)//function สร้างตัวเลือกปฎิทิน
            {
              $( ele ).datepicker({
                  onSelect:(date_text)=>
                  {
                    let arr=date_text.split("/");
                    let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])+543).toString();
                    $(ele).val(new_date);
                    $(ele).css("color","");
                  },
                  beforeShow:()=>{

                    if($(ele).val()!="")
                    {
                      let arr=$(ele).val().split("/");
                      let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])-543).toString();
                      $(ele).val(new_date);

                    }

                    $(ele).css("color","white");
                  },
                  onClose:()=>{

                      $(ele).css("color","");

                      if($(ele).val()!="")
                      {
                          let arr=$(ele).val().split("-");
                          if(parseInt(arr[2])<2500)
                          {
                              let new_date=arr[0]+"-"+arr[1]+"-"+(parseInt(arr[2])).toString();
                              $(ele).val(new_date);
                          }
                      }
                  },
                  dateFormat:"yy-mm-dd", //กำหนดรูปแบบวันที่เป็น วัน/เดือน/ปี
                  changeMonth:true,//กำหนดให้เลือกเดือนได้
                  changeYear:true,//กำหนดให้เลือกปีได้
                  showOtherMonths:true,//กำหนดให้แสดงวันของเดือนก่อนหน้าได้
              });
            }
          $(document).ready(function(){
            //เรียก function set_cal เมื่อเปิดหน้าเว็บ โดยส่ง object element ที่มี id เป็น datepicker เป็นพารามิเตอร์
            set_cal( $("#datepicker2") );
          })
          </script>
        <!-- สำหรับ วันที่ แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ --> 
        
        <label>สถานะรถ</label>
		<select id="sv_cl_status" name="sv_cl_status" class="form-select form-select-sm" placeholder="สถานะรถ">
                    <option value="" selected> </option>  <!-- ดึงข้อมูลมาจากตารางใหม่ -->
                    <?php
                        $res_svs = mysqli_query($conn, "SELECT * FROM wm_tb_suspect_vehicle_status order by svs_cl_aid ASC");
                        while($row_svs = mysqli_fetch_array($res_svs))
                        {
                            echo "<option value='{$row_svs['svs_cl_aid']}'>{$row_svs['svs_cl_status']}</option>";
                        }
                    ?>
                </select>
		<p>
		<br>
			<input type="submit" value="บันทึกข้อมูล" class="btn btn-success" >
			<td> <a href="index.php?rnd=<?= rand(); ?>&page=suspect" class="btn btn-warning" >ยกเลิก</a> </td>
		</p>
		<br>
		<br>
	</form>
	</div>
	</div>
	</div>
<script>
// ฟังก์ชั่น เปลี่ยนภาค (บช.) เป็น ภ.จว. (บก.)
function do_region_change(current_value)
{
    current_value = (typeof current_value == 'undefined') ? 0 : current_value;
	var sel_region = document.getElementById("region");		
	var code_region = sel_region.options[sel_region.selectedIndex].value;

	$.get("../ajax/get_provincial.php?region=" + code_region, 
		  function(data, status){
				//alert("Data: " + data + "\nStatus: " + status);
				// jQuery style						
				$('#provincial').find('option').remove().end();
				var data = $.trim(data);
				if(data == '') {
					alert("ไม่พบข้อมูล บก. !");
					return;
				}

					//.append('<option value="whatever">text</option>').val('whatever');
				var datas = data.split(';');
				for(var i=0; i<datas.length; i++) {
					if(datas[i] == '') continue;
					datas[i] = datas[i].split(':');
					var code_region = datas[i][0];
					var name_region = datas[i][1];
					//$('#provincial').append('<option value="'+ code_region+'">' + name_region + '</option>');
                    if(current_value == code_region) {
                        $('#provincial').append('<option value="'+ code_region+'" selected>' + name_region + '</option>'); 
                    }
                    else {
                        $('#provincial').append('<option value="'+ code_region+'">' + name_region + '</option>');
                    }
				}
                // Trigger the change event on the amphur dropdown so the tambon dropdown will be populated     
                $('#provincial').trigger('change');
		});
}
    //เลือก สถานี จากจังหวัด
function do_provincial_change(current_value)
{
    current_value = (typeof current_value == 'undefined') ? 0 : current_value;
    
	var sel_provincial = document.getElementById("region");		
	var provincial_code = sel_provincial.options[sel_provincial.selectedIndex].value;
    
    var sel_provincial2 = document.getElementById("provincial");		
	var station_code = sel_provincial2.options[sel_provincial2.selectedIndex].value;

	$.get("../ajax/get_station.php?region=" + provincial_code + "&provincial=" + station_code, 
			  function(data, status){
					//alert("Data: " + data + "\nStatus: " + status);
					// jQuery style						
					$('#station').find('option').remove().end();
					var data = $.trim(data);
					if(data == '') {
						alert("ไม่พบข้อมูลสถานี !");
						return;
					}

						//.append('<option value="whatever">text</option>').val('whatever');
					var datas = data.split(';');
					for(var i=0; i<datas.length; i++) {
						if(datas[i] == '') continue;
						datas[i] = datas[i].split(':');
						var station_code = datas[i][0];
						var station_name = datas[i][1];
                        if(current_value == station_code) {
                            $('#station').append('<option value="'+ station_code+'" selected>' + station_name + '</option>'); 
                        }
                        else {
                            $('#station').append('<option value="'+ station_code+'">' + station_name + '</option>');   
                        }						
					}
			});
}
</script>
<script>
// ชื่อ >> value >> set selct auto (สคลิปตรวจสอบ Selected) จำเป็นต้องก็อปปี้ <script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js" type="text/javascript">
function auto_select(sid, value)
{
    $('#' + sid).val(value);
}

function auto_thaidate(sid, value)
{
	if(value != '') {
		var dates = value.split('-');
		dates = value.split('-');
		if(dates.length > 2) {
			var ys = dates[0];
			var ms = dates[1];
			var ds = dates[2];
			if(ys.substr(0,2) == '20') {
				ys = parseInt(ys) + 543;
			}
			value = $.digit(ds,2) + '/' + $.digit(ms,2) + '/' +ys;
			$('#' + sid).val(value);
		}
	}
}

$(document).ready(function() {
<?php
    echo "auto_select('sv_cl_vehicle_type', '{$row['sv_cl_vehicle_type']}');\n";
    echo "auto_select('sv_cl_police', '{$row['sv_cl_police']}');\n";
    echo "auto_select('checking', '{$row['checking']}');\n";
    echo "auto_select('action', '{$row['action']}');\n";
    echo "auto_select('Send_inquiry_official', '{$row['Send_inquiry_official']}');\n";
    echo "auto_select('sv_cl_inquiry_official', '{$row['sv_cl_inquiry_official']}');\n"; 
    echo "auto_select('sv_cl_status', '{$row['sv_cl_status']}');\n";
    // convert db date to thai dates
	//echo "auto_thaidate('sv_cl_date', '{$row['sv_cl_date']}');\n";
?>
    setTimeout("do_region_change('<?= $row['provincial'] ?>')", 1000);
    setTimeout("do_provincial_change('<?= $row['station'] ?>')", 2000);
    });
</script>
</body>
</html>