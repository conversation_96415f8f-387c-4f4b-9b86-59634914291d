<?php
include '../Condb.php';  //PDO
include '../users.inc.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);



?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>เพิ่มข้อมูลภารกิจ/คำสั่ง/ข่าวสาร</title>
<!--<script src="/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>   -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<link rel="stylesheet" href="../bootstrap/css/bootstrap.rtl.min.css">
<link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/css/bootstrap.min.css">
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.3.1/js/bootstrap.min.js"></script>
</head>
	
<body>
		<form method="post" action="Save_charge.php">
		  <label for="charge">Select Charge:</label>
		  <select name="charge" id="charge">
			<option value="charge_item1">Charge Item 1</option>
			<option value="charge_item2">Charge Item 2</option>
			<!-- Add more options as needed -->
		  </select>

		  <button type="button" data-toggle="modal" data-target="#myModal">+</button>

		  <!-- Bootstrap modal -->
		  <!--<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
			<div class="modal-dialog" role="document">
			  <div class="modal-content">
				 <!--Modal content goes here--> 
				<!--</div>
			</div>
		  </div>-->
			

			
			
		</form>
	</body>
</html>

<script>
  $(document).ready(function() {
    // Initialize the modal when the document is ready
    $('#myModal').modal('hide');
  });
</script>