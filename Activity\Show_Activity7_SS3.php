<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';
include '../Head_image.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

//get month from select or current month
$month = isset($_GET['month']) ? $_GET['month'] : date("m");
$year = isset($_GET['year']) ? $_GET['year'] : date("Y");
//$station = isset($_GET['station']) ? $_GET['station'] : 6707;
//$station = 6707;   // สภ.บ้านสวน

//--------------------
// last modify 2023-01-20
$limite_date = strtotime("$year-09-30"); // วันสุดท้ายของช่วง เป็น 30 ก.ย.ของปีนั้น ๆ
$current_date = time(); // now()   กำหนดวันปัจจุบัน
$check_date = strtotime("$year-10-01");   // ตัวแปร $check_date เป็นวันตั้งแต่ 1 ต.ค.ของปีนั้น ๆ เป็นต้นไป
$select_date = strtotime("$year-$month-01"); // เป็นตัวแปร สำหรับเลือก ของเดือนนั้น ๆ

//ถ้าเวลายังไม่สิ้นสุด 30 กย. ของปี
if($current_date < $check_date)   // ถ้าวันปัจจุบัน มีค่าน้อยกว่า วันที่ 1 ต.ค.
{
    $limite_date = strtotime(($year-1) . "-09-30");  // ตัวแปร $limite_date จะมีค่าเท่ากับ 30 ก.ย.ของปี ที่ลบค่าไป 1
    $check_date = strtotime(($year-1) . "-10-01");   // ตัวแปร $check_date จะมีค่าเท่ากับ 1 ต.ค. ของปี ที่ลบค่าไป 1
}

$m1 = '';
$m2 = '';
$m3 = '';
$m4 = '';
$m5 = '';
$m6 = '';
$m7 = '';
$m8 = '';
$m9 = '';
$m10 = '';
$m11 = '';
$m12 = '';
switch($month)
{
    case "01": $m1 = "selected"; break;
    case "02": $m2 = "selected"; break;
    case "03": $m3 = "selected"; break;
    case "04": $m4 = "selected"; break;
    case "05": $m5 = "selected"; break;
    case "06": $m6 = "selected"; break;
    case "07": $m7 = "selected"; break;
    case "08": $m8 = "selected"; break;
    case "09": $m9 = "selected"; break;
    case "10": $m10 = "selected"; break;
    case "11": $m11 = "selected"; break;
    case "12": $m12 = "selected"; break;
}
$current_month = $global_thai_month[0+$month];
$y2 = $year+543;

// แปลงเวลา เป็นปีไทย
function date_2_Thai( $date )
{
    global $global_thaimonth;
    $strYear = date("Y", $date) + 543;
    $strMonth = date("n",$date) - 1;
    $strDay = date("j", $date);
    $strMonthThai = $global_thaimonth[$strMonth];
    return "$strDay $strMonthThai $strYear";
}

//สร้างฟังก์ชั่น ย้อนหลัง arrest_summary_count มาใช้งาน เป็นยอดรวมการจับกุม ย้อนหลัง 1 เดือน
// dateM - datetime var
function arrest_summary_count($date)
{
    global $pdo, $station;

    $m1 = date("Y-m-1", $date);
    $m2 = date("Y-m-t", $date);

    $sql_s = "SELECT COUNT(*) FROM wm_tb_warrant WHERE station='$station' AND (`wr_cl_date` BETWEEN :m1 AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station` = :station AND `wr_cl_status` IN ('2', '5', '6', '7')";

    $stmt = $pdo->prepare($sql_s);
    $stmt->bindParam(':m1', $m1);
    $stmt->bindParam(':m2', $m2);
    $stmt->bindParam(':station', $station);
    $stmt->execute();

    $row_s = $stmt->fetch(PDO::FETCH_NUM);
    return $row_s[0];
}

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<base target="_top">
<title>แบบ สส.3</title>
<link rel="stylesheet" href="/bootstrap/css/bootstrap-grid.css">
<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
</head>

<body>
<!--<p><img src="../Image/WatchmanDB3.jpg" width="100%" height="" alt=""/></p>-->
<div class=" h3 text-center  alert alert-success mb-4 mt-4 " role="alert" >แบบ สส.3 บัญชีรายละเอียดหมายจับ  ปีงบประมาณ &nbsp;<?= $y2 ?> &nbsp; <?= $name_station ?></div>
    
&nbsp;&nbsp;&nbsp;&nbsp;<a href="/WM/index.php?rnd=<?= rand(); ?>&page=report" class="btn btn-warning btn-lg mb-4" >ย้อนกลับ</a>
     &nbsp;&nbsp;<button class="btn btn-primary btn-lg mb-4" onClick="do_print()"> พิมพ์รายงาน </button> 
	<!--<button class="btn btn-primary btn-lg mb-4" onClick="if ($station === 6707) { do_print(); }">พิมพ์รายงาน</button>-->
    <br>
    
<div class="container-fluid" align="left">
<table class="table table-striped table-hover table-bordered table-sm mb-4 mt-4" style="border-radius: 2" width="80%" border="1" cellspacing="1" cellpadding="1">
  <tbody>
    <tr>
      <td style="background: #42FF00">&nbsp;ลำดับ&nbsp;</td>
      <td nowrap style="background: #42FF00">&nbsp;เลขที่คดี&nbsp;</td>
      <td nowrap style="background: #42FF00">&nbsp;เลขที่หมายจับ&nbsp;</td>
      <td nowrap style="background: #42FF00">&nbsp;ชื่อ-สกุล ผู้ต้องหา&nbsp;</td>
      <td nowrap style="background: #42FF00">&nbsp;เลขประจำตัวประชาชน&nbsp;</td>
      <td style="background: #42FF00">&nbsp;ข้อหา&nbsp;</td>
      <td nowrap style="background: #42FF00">&nbsp;ว.ด.ป. ที่ออกหมาย&nbsp;</td>
      <td nowrap style="background: #42FF00">&nbsp;ว.ด.ป. ขาดอายุความ&nbsp;</td>
      <td style="background: #42FF00">&nbsp;หมายเหตุ&nbsp;</td>
    </tr>
<?php
  //$date1 = date("Y", $limite_date) . "-$month-01";        //"$year-$month-01";
$day1 = date("t", $limite_date);
$date2 = "$year-$month-$day1"; // <<<
                     
// ยอดหมายจับเก่า ที่ออกระหว่าง ปี 45 - 30 ก.ย.2565 ทั้งหมด
$date_prev = date("Y-m-d", $limite_date);
      
$date_next = date("Y-m-01", $check_date); // ตั้งแต่ 1 ตค เปนต้นไป
    $m1 = date("Y-m-01", $select_date);       // m1 = เดือนปัจจุบันที่เลือก
    $m2 = date("Y-m-t", $select_date);        // << end date of current month ได้ค่าวันสุดท้ายของเดือนปัจจุบันที่เลือกมา
      
$sql = "SELECT
            T1.*,
            T2.`ps_cl_prefix` AS prefix, T2.`ps_cl_name` AS name ,T2.`ps_cl_surname` AS surname, 
            T3.sw_cl_name AS status
        FROM
            wm_tb_warrant AS T1
            LEFT JOIN `wm_tb_personal` AS T2 ON T1.`wr_cl_idcard` = T2.`ps_cl_idcard`
            LEFT JOIN wm_tb_status_warrent AS T3 ON T1.wr_cl_status = T3.sw_cl_aid
        WHERE
            (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND T1.`station`='$station'
        ORDER BY
            `wr_cl_date` ASC ";
      
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':date_next', $date_next);
    $stmt->bindParam(':m2', $m2);
try{
    $stmt->execute();
}catch (PDOException $e) {
    echo '$sql Failed: '. $e->getMessage();
}
 
$no = 1;
      //echo $sql;
while($row = $stmt->fetch(PDO::FETCH_ASSOC)){
        
        // ตรวจสอบก่อนว่า วันที่ มีค่าไหม ถ้าว่าง ก็ให้แสดงค่าว่าง
    if($row["wr_cl_date"] != ''){
    $strDate = DateThai( $row["wr_cl_date"] );
    }else{
        $strDate = '';
    }
    
    // ตรวจสอบก่อนว่า วันที่ มีค่าไหม ถ้าว่าง ก็ให้แสดงค่าว่าง
    if($row["wr_cl_expire"] != ''){
    $strDate2 = DateThai( $row["wr_cl_expire"] );
    }else{
        $strDate3 = '';
    }
?>
    <tr>
      <td>&nbsp; <?= $no ?> &nbsp;</td>
      <td nowrap>&nbsp; <?= $row['wr_cl_crimes_no']?> &nbsp;</td>
      <td nowrap>&nbsp; <?= $row['wr_cl_no']?> &nbsp;</td>
      <td nowrap align="left">&nbsp; <?= $row['prefix']?><?= $row['name']?> &nbsp;<?= $row['surname']?> &nbsp;</td>
      <td>&nbsp; <?= $row['wr_cl_idcard']?> &nbsp;</td>
      <td style="text-align: left">&nbsp; <?= $row['wr_cl_crimes_type']?> &nbsp;</td>
      <td nowrap>&nbsp; <?= $strDate ?> &nbsp;</td>
      <td nowrap>&nbsp; <?= $strDate2 ?> &nbsp;</td>
      <td align="left">&nbsp; <?= $row['status']?> &nbsp;</td>
    </tr>
<?php
      $no++;
    }
?>
  </tbody>
</table>
</div>
    
</body>
</html>
<!-- script สำหรับส่งไปพิมพ์รายงาน  -->
<script>
    
    function do_print()
    {
        var newW = window.open("/Activity/Show_Activity7_SS3_print.php", "_print", "menubar=no,titlebar=no,toolbar=no,status=no");
        newW.print();
        //window.print();
    }
    
</script>