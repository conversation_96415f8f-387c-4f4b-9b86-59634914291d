<?php
include '../Condb.php';
include '../users.inc.php';
include '../right_user.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// เมื่อมีการใช้ ตัวแปร Session จะต้องเปิดก่อน
//session_start();  // เปิดตัวแปร session
/*if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
*/
$id = $_GET['id'];
$pcid = $_GET['pcid'];

?>


<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
<title>ระบบ WatchmanDB</title>
</head>

<body>
	<div class="container-fluid">
		<div class=" h3 text-center  alert alert-success mb-2 mt-2 " role="alert" >ข้อมูลการรับแจ้งเหตุ ฝ่ายสืบสวน (แบบ สส.1) <?= $name_station ?></div>
		<div style="flex: auto"> &nbsp;<a href="/Bansuan/index.php?rnd=<?= rand(); ?>&page=complain" class="btn btn-primary mb-4" >ย้อนกลับ</a>            
        &nbsp;<a href="Edit_complaints_for_detail.php?id=<?= $id ?>&pcid=<?= $pcid ?>" class="btn btn-success mb-4" >การดำเนินการ</a> 
        &nbsp;<a href="ss1.php?id=<?= $id ?>&pcid=<?= $pcid ?>" class="btn btn-warning mb-4" >เช็คลิสต์แบบตรวจที่เกิดเหตุ (คดีทั่วไป)</a> &nbsp;<a href="ss1_capital.php?id=<?= $id ?>&pcid=<?= $pcid ?>" class="btn btn-danger mb-4" >เช็คลิสต์แบบตรวจที่เกิดเหตุ (คดีอุกฉกรรจ์)</a></div>
            
<?php
$sql = "SELECT T1.*, T2.sco_cl_data AS data FROM wm_tb_Complaints  AS T1 " . //ตั้งตัวแปร sql เลือกฐานข้อมูลจากตารางที่ชื่อ wm_tb_personal
        "LEFT JOIN wm_tb_source_complaints AS T2 ON T1.cp_cl_source = T2.sco_cl_aid " ;
        if($pcid != ''){
            $sql = $sql . " WHERE `cp_cl_source` IN (8,9,10,11,13,14) AND `cp_cl_status`=1 AND cp_cl_aid='$id' ";
        }
                 
$resultCp = mysqli_query($conn,$sql);
// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no = 1;
while($rowCp=mysqli_fetch_array($resultCp))	//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
{
    //ฟังก์ชั่น วันที่ ดึงจาก condb
    $strDate = DateThai( $rowCp["cp_cl_date_complaints"] );
    $strDate2 = DateThai( $rowCp["cp_cl_date_incident"] );
    
     $pcid = $rowCp['cp_cl_case_id'];  
    //ตรวจสอบไฟล์ สส.1 ก่อนว่า มีไฟล์ในตารางไหม หากมี ให้ส่งเป็นลิงค์
	$pdf = $rowCp["cp_cl_complaints_file"];
	if($pdf !== '')
	{
		$links = '<a href="/' . $rowCp["cp_cl_complaints_file"] .'" target="_blank">Download</a>';
	}
	else {
		$links = 'ไม่มีไฟล์';
	}
    
    
?>	
            
 
<div class="container">
<table width="92%" border="1" cellspacing="1" cellpadding="1" class="table-striped table-hover table-bordered">
  <tbody>
    <tr align="center" >
      <td width="20%" align="center" bgcolor="#1D04F3" height="40px"><b style="color: white" >รายการ</b></td>
      <td width="74%" align="center" bgcolor="#1D04F3" height="40px"><b style="color: white" >รายละเอียด</b></td>
    </tr>
    <tr>
      <td>รหัสรับแจ้งเหตุ</td>
      <td><?= $rowCp["cp_cl_case_id"]?></td>
    </tr>
    <tr>
      <td>เหตุ</td>
      <td><?= $rowCp["cp_cl_case"]?></td>
    </tr>
    <tr>
      <td>ที่มา</td>
      <td><?= $rowCp["data"]?></td>
    </tr>
    <tr>
      <td>วันเวลา รับแจ้งเหตุ</td>
      <td><?= $strDate ?>
/
  <?= $rowCp["cp_cl_time_complaints"]?></td>
    </tr>
    <tr>
      <td>วันที่เกิดเหตุ</td>
      <td><?= $strDate2 ?></td>
    </tr>
    <tr>
      <td>เวลา</td>
      <td><?= $rowCp["cp_cl_time_start_incident"]?>
-
  <?= $rowCp["cp_cl_time_end_incident"]?></td>
    </tr>
    <tr>
      <td>สถานที่เกิดเหตุ</td>
      <td><?= $rowCp["cp_cl_place"]?></td>
    </tr>
    <tr>
      <td>ผู้เสียหาย (ราย)</td>
      <td><?= $rowCp["cp_cl_sufferer_quantity"]?></td>
    </tr>
    <tr>
      <td>ผู้เสียหาย 1</td>
      <td><?= $rowCp["cp_cl_sufferer_name1"]?>
  อายุ
  <?= $rowCp["cp_cl_age1"]?>
ปี  
<?= $rowCp["cp_cl_address1"]?>
โทร
<?= $rowCp["cp_cl_phone1"]?></td>
    </tr>
    <tr>
      <td>ผู้เสียหาย 2</td>
      <td><?= $rowCp["cp_cl_sufferer_name2"]?>
  อายุ
  <?= $rowCp["cp_cl_age2"]?>
ปี  
<?= $rowCp["cp_cl_address2"]?>
โทร 
<?= $rowCp["cp_cl_phone2"]?></td>
    </tr>
    <tr>
      <td>ผู้เสียหาย 3</td>
      <td><?= $rowCp["cp_cl_sufferer_name3"]?>
  อายุ
  <?= $rowCp["cp_cl_age3"]?>
ปี 
<?= $rowCp["cp_cl_address3"]?>
โทร 
<?= $rowCp["cp_cl_phone3"]?></td>
    </tr>
    <tr>
      <td>ผู้ก่อเหตุ/ผู้ต้องสงสัย 1</td>
      <td><?= $rowCp["cp_cl_suspect1"]?></td>
    </tr>
    <tr>
      <td>ผู้ก่อเหตุ/ผู้ต้องสงสัย 2</td>
      <td><?= $rowCp["cp_cl_suspect2"]?></td>
    </tr>
    <tr>
      <td>ผู้ก่อเหตุ/ผู้ต้องสงสัย 3</td>
      <td><?= $rowCp["cp_cl_suspect3"]?></td>
    </tr>
    <tr>
      <td>ตำหนิรูปพรรณ</td>
      <td><?= $rowCp["cp_cl_description"]?></td>
    </tr>
    <tr>
      <td>ยานพาหนะ</td>
      <td><?= $rowCp["cp_cl_vehicle"]?></td>
    </tr>
    <tr>
      <td>ทรัพย์สิน</td>
      <td><?= $rowCp["cp_cl_property"]?></td>
    </tr>
    <tr>
      <td>พฤติการณ์</td>
      <td><?= $rowCp["cp_cl_case_behavior"]?></td>
    </tr>
    <tr>
      <td>แผนที่</td>
      <td><?= $rowCp["cp_cl_map"]?></td>
    </tr>
    <tr>
      <td>ร้อยเวรสืบสวน</td>
      <td><?= $rowCp["cp_cl_investigative_sergeant"]?></td>
    </tr>
    <tr>
      <td>เจ้าหน้าที่สืบสวน</td>
      <td><?= $rowCp["cp_cl_investigative_officer"]?></td>
    </tr>
    <tr>
      <td>ไฟล์ สส.1</td>
      <td><?= $links ?></td>
    </tr>
  </tbody>
</table>
    <div class="alert alert-warning table-hover table-bordered" >
        <div class="col-8"> การดำเนินการ : <?= $rowCp["cp_cl_action"]?>  </div>
    
    </div>
</div>
            
	<!--		<td> <a href="Edit_home_visit.php?id=<?= $rowHv["hv_cl_aid"]?>&pcid=<?=$pcid ?>" class="btn btn-warning mb-4" >แก้ไข</a> </td>
			<td> <a href="#" 
				onClick="Del('Del_home_visit.php?id=<?= $rowHv["hv_cl_aid"]?>&pcid=<?=$pcid ?>')" class="btn btn-danger mb-4" >ลบ</a> </td>
		</tr>  -->
	
</div>

        
        <?php
	$no++;
	}
//	mysqli_close($conn);	//ปิดการเชื่อมต่อฐานข้อมูล

	?>  
        
        
<?php /*?><script>
function Del(url)
{
	if(confirm("คุณจะลบข้อมูล หรือ ไม่ ?"))
	{
		window.location = url;		
	}
}
</script><?php */?>
</body>
</html>