<?php
// Step 1: Establish a database connection
include '../Condb.php';
include '../users.inc.php';

// Prepare and bind SQL statement with parameters
$stmt = $conn->prepare("SELECT * FROM members WHERE account = ? AND password = ?");
$stmt->bind_param("ss", $account, $password);

// Sanitize user input and set parameters
$account = mysqli_real_escape_string($conn, $_POST['account']);
$password = mysqli_real_escape_string($conn, $_POST['password']);

// Execute the query
$stmt->execute();

// Handle the results
$result = $stmt->get_result();
if ($result->num_rows > 0) {
  ?>
    
<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>เลือกตั้ง 66</title>
<script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/policeinnopolis/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 18px;
    }
input::placeholder {
  color: red;
}
.form-control::placeholder {
  color: sandybrown;
}
</style>
<!-- สำหรับ วันที่ แบบ พ.ศ. -->
<link rel="stylesheet" href="http://code.jquery.com/ui/1.9.1/themes/base/jquery-ui.css" />
<script src="http://code.jquery.com/jquery-1.8.2.js"></script>
<script src="/policeinnopolis/js/jquery-ui.js"></script>
<script src="../SweetAlert2/dist/sweetalert2.all.min.js"></script>
</head>
    
<body>
	
	<div class="container">
	<div class="row">
	<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> เพิ่มข้อมูลพรรคการเมือง </div>
	<form action="Save_party66.php" method="POST" enctype="multipart/form-data" class="body">
	<span style="color: #1203F8">
        
        <label hidden="hidden">ลำดับ</label>
		<input name="pa_aid" type = "text" class="form-control" hidden="hidden"  >
		        
        <label>หมายเลขประจำพรรค</label>
		<input type = "text" name = "pa_number" value="" class="form-control" placeholder="หมายเลขประจำพรรค" required readonly>
        	
        <label>ชื่อพรรคการเมือง</label>
		<input type = "text" name = "pa_name" class="form-control" placeholder="ชื่อพรรคการเมือง" >
        
        <label>ผู้สมัคร ส.ส.ในพื้นที่ที่สังกัดพรรค</label>
		<input type = "text" name = "pa_member" class="form-control" placeholder="ผู้สมัคร ส.ส.ที่สังกัดพรรค" >
        <br>
        <div class="mb-3">
			<label for="formFileMultiple" class="form-label">ภาพโลโก้พรรค</label>
			 <input class="form-control" type="file" id="pa_logo" name="pa_logo" multiple>
		</div>
        
		<br>
		<p>
			<input type="submit" value="บันทึกข้อมูล" class="btn btn-success" >
			<td> <a href="/Election_66/Show_Election_index.php?rnd=<?= rand(); ?>&page=party" class="btn btn-warning" >ยกเลิก</a> </td>
		</p>
		<br>
		<br>
	</form>
	</div>
	</div>
	</div>
</body>
</html>
   
<?php
} else {
  // User not found, do something else
}

// Close the connection
$stmt->close();
$conn->close();
?>



