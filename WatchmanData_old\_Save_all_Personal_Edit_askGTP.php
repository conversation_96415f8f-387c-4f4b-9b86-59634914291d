<?php
include '../Condb.php';
include '../users.inc.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// เมื่อมีการใช้ ตัวแปร Session จะต้องเปิดก่อน
//session_start();  // เปิดตัวแปร session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}


//print_r($_POST);
//print_r($_FILES);
//exit();

$ps_cl_aid = $_POST[ 'ps_cl_aid' ];
$ps_cl_idcard = $_POST[ 'ps_cl_idcard' ];
$ps_cl_prefix = $_POST[ 'ps_cl_prefix' ];
$ps_cl_sex = $_POST[ 'ps_cl_sex' ];
$ps_cl_name = $_POST[ 'ps_cl_name' ];
$ps_cl_surname = $_POST[ 'ps_cl_surname' ];
$ps_cl_nickname = $_POST[ 'ps_cl_nickname' ];
$ps_cl_birthday2 = $_POST[ 'ps_cl_birthday2' ];
$ps_cl_father = $_POST[ 'ps_cl_father' ];
$ps_cl_father_pid = $_POST[ 'ps_cl_father_pid' ];
$ps_cl_mother = $_POST[ 'ps_cl_mother' ];
$ps_cl_mother_pid = $_POST[ 'ps_cl_mother_pid' ];
$ps_cl_marital_status = $_POST[ 'ps_cl_marital_status' ];
$ps_cl_death = $_POST[ 'ps_cl_death' ];
$date_of_death = $_POST[ 'date_of_death' ];
$region = $_POST[ 'region' ];
$provincial = $_POST[ 'provincial' ];
$station = $_POST[ 'station' ];
$ps_cl_type = $_POST[ 'ps_cl_type' ];

// save Image
$file1 = $_FILES[ 'ps_cl_image' ][ 'tmp_name' ];
if ( is_uploaded_file( $file1 ) ) {
    /*
  $ps_cl_image = "/policeinnopolis/uploaded/Image1/" . basename( $_FILES[ 'ps_cl_image' ][ 'name' ] );
	
  move_uploaded_file( $file1, "../" . $ps_cl_image );  
  */
    //ลบรูปเก่าในเซิร์ฟเวอร์ก่อน
    $sql = "SELECT * FROM wm_tb_personal WHERE ps_cl_aid='ps_cl_aid' ";
    $res = mysqli_query($conn,$sql);
    if($row = mysqli_fetch_array($res))
    {
        if(($row['ps_cl_image'] != '') && file_exists($row['ps_cl_image'])) {
            unlink( $row['ps_cl_image'] );
        }    
    }

    //เปลี่ยนชื่อไฟล์ใหม่
    $ps_cl_image = "/policeinnopolis/uploaded/Image1/" . basename( $_FILES[ 'ps_cl_image' ][ 'name' ] );	    
    // file + ext    
    $ext = strrchr($ps_cl_image, ".");    
    $ps_cl_image = "../policeinnopolis/uploaded/Image1/File_" . time() . $ext; // file+time+ext        
    move_uploaded_file( $file1, $ps_cl_image );  
    
} 
else {
  $ps_cl_image = '';
}

/*$sql2 = "UPDATE FROM wm_tb_family SET " .
            "fm_cl_idcard = '$ps_cl_idcard', " .
            "fm_cl_idcard_family = '$ps_cl_father_pid' ".
        "WHERE fm_cl_aid = '$ps_cl_aid' ";
    
$sql4 = "UPDATE FROM wm_tb_family SET " .
            "fm_cl_idcard = '$ps_cl_idcard', " .
            "fm_cl_idcard_family = '$ps_cl_mother_pid' ".
        "WHERE fm_cl_aid = '$ps_cl_aid' ";
*/

$sql2 = "INSERT INTO wm_tb_family(fm_cl_idcard, fm_cl_idcard_family) VALUES('$ps_cl_idcard', '$ps_cl_father_pid')";

$sql4 = "INSERT INTO wm_tb_family(fm_cl_idcard, fm_cl_idcard_family) VALUES('$fm_cl_idcard', '$ps_cl_mother_pid')";

// เช็คก่อนว่า มีข้อมูลวันเกิดหรือไม่
$date = explode('-', $ps_cl_birthday2);
$dates2 = array($date[0], $date[1], $date[2]); // เช็คว่า รูปแบบวันเกิด เป็นแบบ ค.ศ.-เดือน-วัน 0000-00-00 หรือไม่

if(!empty($ps_cl_birthday2) && !empty($date_of_death)){
    // if both columns are not empty, execute this update query
    $sql = "UPDATE wm_tb_personal SET " .
            "ps_cl_idcard = '$ps_cl_idcard', " .
            "ps_cl_prefix = '$ps_cl_prefix', ".
            "ps_cl_sex = '$ps_cl_sex', " .
            "ps_cl_name = '$ps_cl_name', " .
            "ps_cl_surname = '$ps_cl_surname', " .
            "ps_cl_nickname = '$ps_cl_nickname', ".
            "ps_cl_birthday2 = '$ps_cl_birthday2', ".
            "ps_cl_father = '$ps_cl_father', ".
            "ps_cl_father_pid = '$ps_cl_father_pid', ".
            "ps_cl_mother = '$ps_cl_mother', ".
            "ps_cl_mother_pid = '$ps_cl_mother_pid', ".
            "ps_cl_marital_status = '$ps_cl_marital_status', ".
            "ps_cl_death = '$ps_cl_death', ".
            "date_of_death = '$date_of_death', ".
            "region = '$region', ".
            "provincial = '$provincial', ".
            "station = '$station', ".
            "ps_cl_type = '$ps_cl_type' ".
            (($ps_cl_image == '') ? '' : (",ps_cl_image = '$ps_cl_image' ")) .
            "WHERE ps_cl_aid = '$ps_cl_aid' ";
}else{
    // if either column is empty, execute this update query
    $sql = "UPDATE wm_tb_personal SET " .
            "ps_cl_idcard = '$ps_cl_idcard', " .
            "ps_cl_prefix = '$ps_cl_prefix', ".
            "ps_cl_sex = '$ps_cl_sex', " .
            "ps_cl_name = '$ps_cl_name', " .
            "ps_cl_surname = '$ps_cl_surname', " .
            "ps_cl_nickname = '$ps_cl_nickname', ".
            "ps_cl_father = '$ps_cl_father', ".
            "ps_cl_father_pid = '$ps_cl_father_pid', ".
            "ps_cl_mother = '$ps_cl_mother', ".
            "ps_cl_mother_pid = '$ps_cl_mother_pid', ".
            "ps_cl_marital_status = '$ps_cl_marital_status', ".
            "ps_cl_death = '$ps_cl_death', ".
            "region = '$region', ".
            "provincial = '$provincial', ".
            "station = '$station', ".
            "ps_cl_type = '$ps_cl_type' ".
            (($ps_cl_image == '') ? '' : (",ps_cl_image = '$ps_cl_image' ")) .
            "WHERE ps_cl_aid = '$ps_cl_aid' ";
}

$result = mysqli_query($conn, $sql);

//echo "$sql <hr>";
//print_r(mysqli_error($conn));

if($result){
	//echo "<script>alert('บันทึกข้อมูลเรียบร้อย');</script>";	
    echo "<script> window.location='Edit_all_Personal.php?pcid=$ps_cl_idcard'</script>"; 
    $_SESSION['Success']="บันทึกสำเร็จ"; // ตัวแปร session ตั้งชื่อว่า Success
}else{
	//echo "<script>alert(\"บันทึกข้อมูลไม่สำเร็จ\\n" . print_r(mysqli_error($conn), true) . "\");</script>";
    echo "<script> window.location='Edit_all_Personal.php?pcid=$ps_cl_idcard'</script>"; 
    $_SESSION['error']="บันทึกไม่สำเร็จ"; // ตัวแปร session ตั้งชื่อว่า error หากบันทึกไม่สำเร็จ
}

// เก็บข้อมูล Action สำหรับ EDIT
$inputs = str_replace("'", "", compact_array($_POST));
$prev = str_replace("'", "", implode(',', $row_0));

$acc = $user['account'];
$sql3 = "INSERT wm_tb_user_action (at_cl_user,at_cl_date,at_cl_action,at_cl_input,at_cl_prev) VALUES('$acc',CURRENT_TIMESTAMP,'Edit Personal {$ps_cl_idcard}', '$inputs', '{$prev}')";
mysqli_query($conn, $sql3);
//

mysqli_close($conn);

//echo "<script>window.location='Show_All.php?pcid={$ps_cl_idcard}&page';</script>";
?>
