<?php
include '../Condb.php';
include '../users.inc.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// เมื่อมีการใช้ ตัวแปร Session จะต้องเปิดก่อน
//session_start();  // เปิดตัวแปร session
/*if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
*/

$pcid = $_GET['pcid'];

$people_name = '';
if($pcid != '') {
	$query1 = "SELECT * FROM wm_tb_personal WHERE ps_cl_idcard ='$pcid' ";
	$result1 = mysqli_query($conn, $query1);
	$row1 = mysqli_fetch_array($result1);	
	
	$people_name = $row1['ps_cl_prefix'] .  $row1['ps_cl_name'] . ' ' . $row1['ps_cl_surname'];
	$people_image = $row1["ps_cl_image"];
	if($people_image != '') {
		$people_image = "<img src='{$people_image}' height='80'> ";
	}
}

?>


<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>เพิ่มข้อมูลญาติพี่น้อง</title>
<link rel="stylesheet" href="/policeinnopolis/bootstrap/css/bootstrap.rtl.min.css">
<script language="javascript" src="/policeinnopolis/jQuery/jquery-3.5.1.min.js"></script> 
<!-- สำหรับ วันที่ แบบ พ.ศ. -->
<link rel="stylesheet" href="http://code.jquery.com/ui/1.9.1/themes/base/jquery-ui.css" />
<script src="http://code.jquery.com/jquery-1.8.2.js"></script>
<script src="/policeinnopolis/js/jquery-ui.js"></script>
   
<script>
    function doPrefixChange()
    {
        var selected = $("#fm_cl_prefix option:selected").val();
        if(selected == "นาย") {
            $("#fm_cl_sex1").prop("checked", true);
        }
        else if(selected == "นาง"){
            $("#fm_cl_sex2").prop("checked", true);
        }
        else if(selected == "น.ส."){
            $("#fm_cl_sex2").prop("checked", true);
        }
        else if(selected == "ด.ช."){
            $("#fm_cl_sex1").prop("checked", true);
        }
        else if(selected == "ด.ญ."){
            $("#fm_cl_sex2").prop("checked", true);
        }
        else {
            $("#fm_cl_sex1").prop("checked", false);
            $("#fm_cl_sex2").prop("checked", false);
            $("#fm_cl_sex3").prop("checked", false);
        }
    }
</script>

</head>

<body>
	<div class="container">
	<div class="row">
		<div class="col-sm-10">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> เพิ่มข้อมูลพี่น้อง ของ <?= $people_image ?> <?=$people_name?> <?= $pcid ?> </div>
	<form action="Save_all_family_M.php" method="POST" enctype="multipart/form-data">
		
		<input type="hidden" name="fm_cl_aid" id="fm_cl_aid" value="0">
		
		<label> เลขบัตรประชาชน </label>
		<input type = "text" name="fm_cl_idcard" class="form-control"  value="<?= $pcid ?>"  readonly="readonly">

			<label>สถานะ</label>
		<select id="fm_cl_status" name="fm_cl_status" class="form-select form-select-sm" aria-label=".form-select-sm example">
			<option value="" selected>เลือกความสัมพันธ์</option>
                <option value="พ่อ">พ่อ</option>
                <option value="แม่">แม่</option>
                <option value="พี่">พี่</option>
                <option value="น้อง">น้อง</option>
                <option value="น้า">น้า</option>
                <option value="อา">อา</option>
                <option value="ลุง">ลุง</option>
                <option value="ป้า">ป้า</option>
                <option value="ปู่">ปู่</option>
                <option value="ย่า">ย่า</option>
                <option value="ตา">ตา</option>
                <option value="ยาย">ยาย</option>
		</select>

			<label>เลขบัตรประชาชนพี่น้อง <span style="color: #F90004">* จำเป็น</span></label>
		<input type = "text" name="fm_cl_idcard_family" class="form-control" placeholder="กรอกเลขบัตรประชาชน 13 หลักของพี่น้อง"  Required >

		<label>คำนำหน้า</label>
		<select id="fm_cl_prefix" name="fm_cl_prefix" class="form-select form-select-sm" aria-label=".form-select-sm example" onChange="doPrefixChange()">
			<option value="" selected>เลือกคำนำหน้า</option>
			<option value="นาย">นาย</option>
			<option value="นาง">นาง</option>
			<option value="น.ส.">น.ส.</option>
			<option value="ด.ช.">ด.ช.</option>
			<option value="ด.ญ.">ด.ญ.</option>
		</select>

		<label>เพศ</label><br>
		<label><input name="fm_cl_sex" id="fm_cl_sex1" type="radio" value="ชาย" checked="checked" /> ชาย </label>
		<label><input name="fm_cl_sex" id="fm_cl_sex2" type="radio" value="หญิง" /> หญิง </label>					<label><input name="fm_cl_sex" id="fm_cl_sex3" type="radio" value="LGBTQ" /> LGBTQ </label><br>

		<label>ชื่อ <span style="color: #F90004">* จำเป็น</span></label>
		<input type = "text" name = "fm_cl_name" class="form-control" placeholder="กรอกชื่อจริง"   Required  >
		<label>นามสกุล <span style="color: #F90004">* จำเป็น</span></label>
		<input type = "text" name = "fm_cl_surname" class="form-control" placeholder="กรอกนามสกุลจริง"  Required >
		<label>ชื่อเล่น</label>
		<input type = "text" name = "fm_cl_nickname" class="form-control" placeholder="กรอกชื่อเล่น" >

            
        <label>วันเดือนปีเกิด</label> 
        <p><input type="text" name="fm_cl_birthday2" id="datepicker" class="form-control" autocomplete="off" ></p>
        <script>
            function set_cal(ele)//function สร้างตัวเลือกปฎิทิน
            {
              $( ele ).datepicker({
                  onSelect:(date_text)=>
                  {
                    let arr=date_text.split("/");
                    let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])+543).toString();
                    $(ele).val(new_date);
                    $(ele).css("color","");
                  },
                  beforeShow:()=>{

                    if($(ele).val()!="")
                    {
                      let arr=$(ele).val().split("/");
                      let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])-543).toString();
                      $(ele).val(new_date);

                    }

                    $(ele).css("color","white");
                  },
                  onClose:()=>{

                      $(ele).css("color","");

                      if($(ele).val()!="")
                      {
                          let arr=$(ele).val().split("-");
                          if(parseInt(arr[2])<2500)
                          {
                              let new_date=arr[0]+"-"+arr[1]+"-"+(parseInt(arr[2])).toString();
                              $(ele).val(new_date);
                          }
                      }
                  },
                  dateFormat:"yy-mm-dd", //กำหนดรูปแบบวันที่เป็น วัน/เดือน/ปี
                  changeMonth:true,//กำหนดให้เลือกเดือนได้
                  changeYear:true,//กำหนดให้เลือกปีได้
                  showOtherMonths:true,//กำหนดให้แสดงวันของเดือนก่อนหน้าได้
              });
            }
          $(document).ready(function(){
            //เรียก function set_cal เมื่อเปิดหน้าเว็บ โดยส่ง object element ที่มี id เป็น datepicker เป็นพารามิเตอร์
            set_cal( $("#datepicker") );
          })
          </script>
        <!-- สำหรับ วันที่ แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ -->
        
		<label>บิดา</label>
		<input type = "text" name = "fm_cl_father" class="form-control" placeholder="ชื่อบิดา" >
		<label>เลขบัตรบิดา</label>
		<input type = "text" name = "fm_cl_father_pid" class="form-control" placeholder= "เลขบัตรประชาชน บิดา"  >
		<label>มารดา</label>
		<input type = "text" name = "fm_cl_mother" class="form-control" placeholder="ชื่อมารดา" >
		<label>เลขบัตรมารดา</label>
		<input type = "text" name = "fm_cl_mother_pid" class="form-control" placeholder= "เลขบัตรประชาชน มารดา"  >
		<label>คู่สมรส</label>
		<input type = "text" name = "fm_cl_spouse" class="form-control" placeholder="ชื่อคู่สมรส หรือแฟน" >
		<div class="mb-3">
			<label for="formFileMultiple" class="form-label">รูปภาพ</label>
			 <input class="form-control" type="file" id="fm_cl_image" name="fm_cl_image" multiple>
		</div>
		<p>
			<input type="submit" value="บันทึกข้อมูล" class="btn btn-success" >
			<td> <a href="Show_All_SMIV_mueang.php?pcid=<?= $pcid ?>&page=family" class="btn btn-warning" >ยกเลิก</a> </td>
		</p>
		<br>
		<br>
	</form>
	</div>
	</div>
	</div>
</body>
</html>