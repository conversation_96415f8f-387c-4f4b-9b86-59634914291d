<?php
include '../Condb.php';
include '../users.inc.php';
if($user['ua_view_data'] != 1)
{
	header("Location: /WatchmanData/main.php");
}

$id = isset($_GET['wc_cl_aid']) ? $_GET['wc_cl_aid'] : '';

?>
    
<!---->
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link rel="stylesheet" href="/bootstrap/css/bootstrap-grid.css">
<link rel="stylesheet" href="../bootstrap/css/bootstrap.rtl.min.css">
<link rel="stylesheet" href="../bootstrap-3.4.1-dist/css/bootstrap.min.css">
<script src="../jQuery/jquery-3.5.1.min.js"></script>
<script src="../bootstrap/js/bootstrap.min.js"></script>

<p><img src="../Image/Head2.jpg" width="100%" height="" alt=""/></p>
<div>
            <?php
			include('../users_info.php');
			?>	
</div>

<div class="container-fluid" align="left">
<div class=" h3 text-center  alert alert-success mb-4 mt-4 " role="alert" >ผลการจับกุม ทายผลพนันฟุตบอลโลก 2022 (19 พ.ย.65 - 18 ธ.ค.65)</div>
<div>
  <div>
    <table width="80%" border="0" cellspacing="1" cellpadding="1">
      <tbody>
        <tr>
          <td width="13%"><div align="left">
            &nbsp;<a href="#Add_worldcup2022.php" class="btn btn-success btn-lg mb-4" disabled >เพิ่มข้อมูล</a>
            &nbsp;<a href="index.php?rnd=<?= rand(); ?>&page=performance" class="btn btn-primary btn-lg mb-4" >ย้อนกลับ</a>
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="wc2022_broadcast.php" class="btn btn-danger btn-lg mb-4" target="_blank" >ตารางถ่ายทอดสด</a>
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="https://www.google.com/search?q=%E0%B8%9C%E0%B8%A5%E0%B8%9F%E0%B8%B8%E0%B8%95%E0%B8%9A%E0%B8%AD%E0%B8%A5%E0%B9%82%E0%B8%A5%E0%B8%81+2022&client=ms-android-samsung-rev2&source=android-home&sxsrf=ALiCzsbSKKznnRAQs1QeeUi7cx0kndzJ4A%3A1670117661154&source=hp&ei=HfmLY5OlBcio4-EPxZGSmAk&oq=%E0%B8%9C%E0%B8%A5&gs_lcp=ChFtb2JpbGUtZ3dzLXdpei1ocBABGAAyBggjECcQEzIGCCMQJxATMgQIABADMgQIABADMgUIABCABDIFCAAQgAQyBQgAEIAEMgUIABCABDoHCCMQ6gIQJzoECCMQJ1C7HFiHIWDNL2gBcAB4AIABnAGIAa8CkgEDMC4ymAEAoAEBsAEP&sclient=mobile-gws-wiz-hp#sie=lg;/m/0fp_8fm;2;/m/030q7;br;fp;1;;;&wptab=si:AC1wQDCkhWdw0MlC9cMeRELsUzQhAhIG3MuaO6tqNOfgAS3P9qDDN2z4YFwInAz7KqYV4Lboem0UBL7aYvZEk91dotSmoIagU_6-ezWvmi4ntyGwiPnUCsuYK1VUeT0JROf5OTSYxS5EjFRwzybXz4tW_LMXySQMD2hzsciXp8r4XEC75pm8lkkh1bM2XwDLD13ipNkl_vVQ" class="btn btn-danger btn-lg mb-4" target="_blank" >ผลการแข่งขัน</a>  
            &nbsp;&nbsp;&nbsp;<b> ** เป้าหมายจับกุม ไม่น้อยกว่า 2 ราย</b> <a href="https://drive.google.com/file/d/139aS2q108boYVcsAf469rNLtRtTv3rli/view?usp=share_link" target="_blank">Click</a></td>
              
        </tr>
      </tbody>
    </table>
      
<p>&nbsp;&nbsp;&nbsp;ผลการแข่งขัน อาเจนติน่า ชนะ ฝรั่งเศส ได้ครองแชมป์ฟุตบอลโลก 2022</p>
<h1 style="font-size: 24px ;background-color: yellow ;padding: 5px" h1 align="center" >ตารางแสดงผลการจับกุม <span style="font-size: 24px ;background-color: yellow ;padding: 5px ">พนันทายผลฟุตบอลโลก 2022</span></h1>
<table width="100%" height="160" border="3" cellpadding="1" cellspacing="1" class="table-bordered table-hover mb-4 mt-4" >
  <tbody>
    <tr >
      <td rowspan="3" bgcolor="#C9C7C7" style="text-align: center; color: #000;" nowrap="nowrap">วันที่จับกุม</td>
      <td rowspan="3" bgcolor="#C9C7C7" style="text-align: center; color: #000;" nowrap="nowrap">เลขบัตร</td>
      <td rowspan="3" bgcolor="#C9C7C7" style="text-align: center; color: #000;" nowrap="nowrap">ชื่อผู้ต้องหา</td>
      <td colspan="6" bgcolor="#F3F803" style="text-align: center; font-size: 18px">พนันทายผล</td>
      <td colspan="4" bgcolor="#2EFC00" style="text-align: center; font-size: 18px">พนันทายผลผ่านสื่ออินเทอร์เน็ต</td>
      <td colspan="4" bgcolor="#0CD4FA" style="color: #000000; text-align: center; font-size: 18px">ของกลาง</td>
      <td rowspan="3" bgcolor="#C9C7C7" style="text-align: center">ผู้บันทึก</td>
      <td rowspan="3" bgcolor="#C9C7C7" style="text-align: center">หมายเหตุ</td>
    </tr>
    <tr>
      <td colspan="2" bgcolor="#F3F803" style="text-align: center">เจ้ามือ</td>
      <td colspan="2" nowrap="nowrap" bgcolor="#F3F803" style="text-align: center">ผู้เล่น</td>
      <td colspan="2" nowrap="nowrap" bgcolor="#F3F803" style="text-align: center">เดินโพย</td>
      <td colspan="2" bgcolor="#2EFC00" style="text-align: center">เจ้ามือ</td>
      <td colspan="2" bgcolor="#2EFC00" style="text-align: center">ผู้เล่น</td>
      <td colspan="2" bgcolor="#0CD4FA" style="color: #000000; text-align: center">โพยบอล</td>
      <td rowspan="2" bgcolor="#0CD4FA" style="color: #000000; text-align: center">เงินสด (บาท)</td>
      <td rowspan="2" bgcolor="#0CD4FA" style="color: #000000; text-align: center">อื่น ๆ</td>
    </tr>
    <tr>
      <td bgcolor="#F3F803" style="text-align: center">ราย</td>
      <td bgcolor="#F3F803" style="text-align: center" nowrap="nowrap">คน</td>
      <td bgcolor="#F3F803" style="text-align: center" nowrap="nowrap">ราย</td>
      <td bgcolor="#F3F803" style="text-align: center" nowrap="nowrap">คน</td>
      <td bgcolor="#F3F803" style="text-align: center" nowrap="nowrap">ราย</td>
      <td bgcolor="#F3F803" style="text-align: center" nowrap="nowrap">คน</td>
      <td bgcolor="#2EFC00" style="text-align: center">ราย</td>
      <td bgcolor="#2EFC00" style="text-align: center">คน</td>
      <td bgcolor="#2EFC00" style="text-align: center">ราย</td>
      <td bgcolor="#2EFC00" style="text-align: center">คน</td>
      <td bgcolor="#0CD4FA" style="color: #000000; text-align: center">จำนวน (ใบ)</td>
      <td bgcolor="#0CD4FA" style="color: #000000; text-align: center">มูลค่า (บาท)</td>
    </tr>
	  
<?php
$sql = "SELECT * FROM wm_tb_gambling_football WHERE wc_cl_aid ";  //ตั้งตัวแปร sql เลือกฐานข้อมูลจากตารางที่ชื่อ wm_tb_personal
if($id != ''){
	$sql = $sql . " WHERE wc_cl_aid='$id' ";
}
$res_FB = mysqli_query($conn,$sql);
// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no = 1;

// อ้างอิง ไว้ใช้สำหรับคำนวณ ยอดรวมการจับกุม
$wc_cl_dealer_case = 0;
$wc_cl_dealer_person = 0;
$wc_cl_players_case = 0;
$wc_cl_players_person = 0;
$wc_cl_walker_case = 0;
$wc_cl_walker_person = 0;
$wc_cl_dealer_net_case = 0;
$wc_cl_dealer_net_person = 0;
$wc_cl_players_net_case = 0;
$wc_cl_players_net_person = 0;
$wc_cl_poiball = 0;
$wc_cl_bath = 0;
$wc_cl_cash = 0;

//
while($row_FB=mysqli_fetch_array($res_FB))	//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
{
    //ฟังก์ชั่น วันที่ ดึงจาก condb
    $strDate = DateThai( $row_FB["wc_cl_date"] );
?>
	  
    <tr>
      <td style="text-align: center" nowrap="nowrap" ><?= $strDate ?></td>
      <td style="text-align: center" nowrap="nowrap" ><?= $row_FB["wc_cl_idcard"]?></td>
      <td style="text-align: center" nowrap="nowrap" ><?= $row_FB["wc_cl_name"]?></td>
	  <td style="text-align: center; font-weight: bold"><?= (($row_FB["wc_cl_dealer_case"] > 0) ? $row_FB["wc_cl_dealer_case"]  : "") ?></td>
	  <td style="text-align: center; font-weight: bold"><?= (($row_FB["wc_cl_dealer_person"] > 0) ? $row_FB["wc_cl_dealer_person"]  : "") ?></td>
      <td style="text-align: center; font-weight: bold"><?= (($row_FB["wc_cl_players_case"] > 0) ? $row_FB["wc_cl_players_case"] : "") ?></td>
      <td style="text-align: center; font-weight: bold"><?= (($row_FB["wc_cl_players_person"] > 0) ? $row_FB["wc_cl_players_person"] : "") ?></td>
      <td style="text-align: center; font-weight: bold"><?= (($row_FB["wc_cl_walker_case"] > 0) ? $row_FB["wc_cl_walker_case"] : "") ?></td>
      <td style="text-align: center; font-weight: bold"><?= (($row_FB["wc_cl_walker_person"] > 0) ? $row_FB["wc_cl_walker_person"] : "") ?></td>

      <td style="text-align: center; font-weight: bold"><?= (($row_FB["wc_cl_dealer_net_case"] > 0) ? $row_FB["wc_cl_dealer_net_case"] : "") ?></td>
      <td style="text-align: center; font-weight: bold"><?= (($row_FB["wc_cl_dealer_net_person"] > 0) ? $row_FB["wc_cl_dealer_net_person"] : "") ?></td>
      <td style="text-align: center; font-weight: bold"><?= (($row_FB["wc_cl_players_net_case"] > 0) ? $row_FB["wc_cl_players_net_case"] : "") ?></td>
      <td style="text-align: center; font-weight: bold"><?= (($row_FB["wc_cl_players_net_person"] > 0) ? $row_FB["wc_cl_players_net_person"] : "") ?></td>
      <td style="text-align: center; font-weight: bold"><?= (($row_FB["wc_cl_poiball"] > 0) ? $row_FB["wc_cl_poiball"] : "") ?></td>
      <td style="text-align: center; font-weight: bold"><?= (($row_FB["wc_cl_bath"] > 0) ? $row_FB["wc_cl_bath"] : "") ?></td>
      <td style="text-align: center; font-weight: bold"><?= (($row_FB["wc_cl_cash"] > 0) ? $row_FB["wc_cl_cash"] : "") ?></td>
      <td style="text-align: center; font-weight: bold"><?= $row_FB["wc_cl_other"]?></td>
      <td style="text-align: center; font-weight: bold"><?= $row_FB["wc_cl_record"]?></td>
      <td style="text-align: center; font-weight: bold"><?= $row_FB["wc_cl_remark"]?></td>
    </tr>    
<?php
    $wc_cl_dealer_case += $row_FB["wc_cl_dealer_case"]; // 1+1 -> 2, "1"+"1" >> "11111111"
    $wc_cl_dealer_person += $row_FB["wc_cl_dealer_person"]; 
    $wc_cl_players_case += $row_FB["wc_cl_players_case"];
    $wc_cl_players_person += $row_FB["wc_cl_players_person"];
    $wc_cl_walker_case += $row_FB["wc_cl_walker_case"];
    $wc_cl_walker_person += $row_FB["wc_cl_walker_person"];
    $wc_cl_dealer_net_case += $row_FB["wc_cl_dealer_net_case"];
    $wc_cl_dealer_net_person += $row_FB["wc_cl_dealer_net_person"];
    $wc_cl_players_net_case += $row_FB["wc_cl_players_net_case"];
    $wc_cl_players_net_person += $row_FB["wc_cl_players_net_person"];
    $wc_cl_poiball += $row_FB["wc_cl_poiball"];
    $wc_cl_bath += $row_FB["wc_cl_bath"];
    $wc_cl_cash += $row_FB["wc_cl_cash"];
 
    $no++;
}//while
?>
      <tr>
        <td colspan="3" nowrap="nowrap" bgcolor="#C9C7C7" style="text-align: center" ><strong>ยอดรวม</strong></td>
        <td bgcolor="#F3F803" style="text-align: center; font-weight: bold"><?= $wc_cl_dealer_case ?></td>
      <td bgcolor="#F3F803" style="text-align: center; font-weight: bold"><?= $wc_cl_dealer_person ?></td>
      <td bgcolor="#F3F803" style="text-align: center; font-weight: bold"><?= $wc_cl_players_case ?></td>
      <td bgcolor="#F3F803" style="text-align: center; font-weight: bold"><?= $wc_cl_players_person ?></td>
      <td bgcolor="#F3F803" style="text-align: center; font-weight: bold"><?= $wc_cl_walker_case ?></td>
      <td bgcolor="#F3F803" style="text-align: center; font-weight: bold"><?= $wc_cl_walker_person ?></td>
      <td bgcolor="#2EFC00" style="text-align: center; font-weight: bold"><?= $wc_cl_dealer_net_case ?></td>
      <td bgcolor="#2EFC00" style="text-align: center; font-weight: bold"><?= $wc_cl_dealer_net_person ?></td>
      <td bgcolor="#2EFC00" style="text-align: center; font-weight: bold"><?= $wc_cl_players_net_case ?></td>
      <td bgcolor="#2EFC00" style="text-align: center; font-weight: bold"><?= $wc_cl_players_net_person ?></td>
      <td bgcolor="#0CD4FA" style="text-align: center; font-weight: bold"><?= $wc_cl_poiball ?></td>
      <td bgcolor="#0CD4FA" style="text-align: center; font-weight: bold"><?= $wc_cl_bath ?></td>
      <td bgcolor="#0CD4FA" style="text-align: center; font-weight: bold"><?= $wc_cl_cash ?></td>
      <td bgcolor="#0CD4FA" style="text-align: center; font-weight: bold">&nbsp;</td>
      <td bgcolor="#C9C7C7" style="text-align: center" >&nbsp;</td>
      <td bgcolor="#C9C7C7" style="text-align: center" >&nbsp;</td>
    </tr>
  </tbody>
</table>
<br>
<hr>
			
<!--<h1 style="font-size: 24px ;background-color: yellow ;padding: 5px " h1 align="center" >การจับกุม การพนันทายผลฟุตบอล</h1>
<table width="100%" height="94" border="1" cellpadding="1" cellspacing="1" class="table-bordered table-hover mb-4 mt-4">
  <tbody>
    <tr>
	  <td height="44" bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">ลำดับ</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;" nowrap="nowrap">วันที่จับกุม</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;" nowrap="nowrap">เลขบัตรผู้ต้องหา</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">ชื่อผู้ต้องหา</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">ของกลาง</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">หมายเหตุ</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">ผู้บันทึกข้อมูล</td>
	  <td colspan="2" bgcolor="#995D5D"></td>
    </tr>
	  
<?php

$sql2 = "SELECT * FROM wm_tb_gambling_football WHERE wc_cl_idcard ";  //ตั้งตัวแปร sql เลือกฐานข้อมูลจากตารางที่ชื่อ wm_tb_personal
$res_FB2 = mysqli_query($conn,$sql2);
// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no = 1;
while($row_FB2=mysqli_fetch_array($res_FB2))	//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
{
     
?>	

    <tr>
	  <td> <?= $no ?> </td>
      <td nowrap="nowrap"><?= $row_FB2["wc_cl_date"]?></td>
      <td nowrap="nowrap">&nbsp;<?= $row_FB2["wc_cl_idcard"]?>&nbsp;</td>
      <td>&nbsp;<?= $row_FB2["wc_cl_name"]?>&nbsp;</td>
      <td>โพย <?= $row_FB2["wc_cl_poiball"]?> , มูลค่า <?= $row_FB2["wc_cl_bath"]?> , เงินสด <?= $row_FB2["wc_cl_cash"]?></td>
      <td>&nbsp;<?= $row_FB2["wc_cl_remark"]?>&nbsp;</td>
      <td>&nbsp;<?= $row_FB2["wc_cl_record"]?>&nbsp;</td>
	  <td><a href="Edit_worldcup2022.php?id=<?= $row_FB2["wc_cl_aid"] ?>" class="btn btn-warning mb-4" >แก้ไข</a> </td>
	  <td><a href="#" class="btn btn-danger mb-4" onClick="Del('Del_worldcup2022.php?id=<?= $row_FB2["wc_cl_aid"]?>')">ลบ</a></td>
    </tr>
  </tbody>
<?php
	$no++;
}//while
?>
</table>-->

<script>
function Del(url)
{
	if(confirm("คุณจะลบข้อมูล หรือ ไม่ ?"))
	{
		window.location = url;		
	}
}
//function dates_change()  // คำสั่งเปลี่ยนเดือน ปี
//{
//    var ms = $("#pf_cl_month option:selected").val();
//    var ys = $("#pf_cl_year option:selected").val();
//    window.location = "/Bansuan/index.php?page=performance&month=" + ms + "&year=" + ys + "&rnd=" + Math.random();
//}

</script>
