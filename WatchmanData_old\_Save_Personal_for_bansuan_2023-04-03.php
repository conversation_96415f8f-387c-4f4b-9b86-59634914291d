<?php
include '../Condb.php';
include '../users.inc.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// เมื่อมีการใช้ ตัวแปร Session จะต้องเปิดก่อน
//session_start();  // เปิดตัวแปร session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}


$ps_cl_idcard = $_POST[ 'ps_cl_idcard' ];
$ps_cl_prefix = $_POST[ 'ps_cl_prefix' ];
$ps_cl_sex = $_POST[ 'ps_cl_sex' ];
$ps_cl_name = $_POST[ 'ps_cl_name' ];
$ps_cl_surname = $_POST[ 'ps_cl_surname' ];
$ps_cl_nickname = $_POST[ 'ps_cl_nickname' ];
$ps_cl_birthday2 = $_POST[ 'ps_cl_birthday2' ];
$ps_cl_father = $_POST[ 'ps_cl_father' ];
$ps_cl_father_pid = $_POST[ 'ps_cl_father_pid' ];
$ps_cl_mother = $_POST[ 'ps_cl_mother' ];
$ps_cl_mother_pid = $_POST[ 'ps_cl_mother_pid' ];
$ps_cl_marital_status = $_POST[ 'ps_cl_marital_status' ];
$ps_cl_death = $_POST[ 'ps_cl_death' ];
$date_of_death = $_POST[ 'date_of_death' ];
$region = $_POST[ 'region' ];
$provincial = $_POST[ 'provincial' ];
$station = $_POST[ 'station' ];
$ps_cl_type = $_POST[ 'ps_cl_type' ];

// save Image
$file1 = $_FILES[ 'ps_cl_image' ][ 'tmp_name' ];
if ( is_uploaded_file( $file1 ) ) {
 /* $ps_cl_image = "/policeinnopolis/uploaded/Image1/" . basename( $_FILES[ 'ps_cl_image' ][ 'name' ] );
	
  move_uploaded_file( $file1, "../" . $ps_cl_image );  */
    // เปลี่ยนชื่อไฟล์ก่อนบันทึก แก้ปัญหา ชื่อซ้ำกัน
    $ps_cl_image = "/policeinnopolis/uploaded/Image1/" . basename( $_FILES[ 'ps_cl_image' ][ 'name' ] );	    
    // file + ext    
    $ext = strrchr($ps_cl_image, ".");    
    $ps_cl_image = "../policeinnopolis/uploaded/Image1/File_" . time() . $ext; // file+time+ext        
    move_uploaded_file( $file1, $ps_cl_image );
    
} else {
  $ps_cl_image = '';
}


//เช็คข้อมูลซ้ำ
//เรียกข้อมูลที่จะบันทึกขึ้นมาตรวจสอบก่อน ว่ามีหรือไม่
$query = "SELECT ps_cl_idcard FROM wm_tb_personal WHERE ps_cl_idcard='$ps_cl_idcard' ";
$result1 = mysqli_query($conn, $query) or die("Error in sql : $query" .
	mysqli_error($query));

if(mysqli_num_rows($result1) > 0){
	echo "<script type='text/javascript'>";
		echo "alert('บุคคลนี้เคยกรอกใส่ฐานข้อมูลไว้แล้ว');";
		echo "window.location = '../WatchmanData/Show_All.php?pcid={$ps_cl_idcard}'; ";
	echo "</script>";
}else{
    if(!empty($date_of_death)){
        $sql = "INSERT INTO wm_tb_personal (ps_cl_idcard, ps_cl_prefix, ps_cl_sex, ps_cl_name, ps_cl_surname, ps_cl_nickname, ps_cl_birthday2, ps_cl_father, ps_cl_father_pid, ps_cl_mother, ps_cl_mother_pid, ps_cl_marital_status, ps_cl_death, date_of_death, region, provincial, station, ps_cl_type, ps_cl_image) VALUES('$ps_cl_idcard', '$ps_cl_prefix', '$ps_cl_sex', '$ps_cl_name', '$ps_cl_surname', '$ps_cl_nickname', '$ps_cl_birthday2', '$ps_cl_father', '$ps_cl_father_pid', '$ps_cl_mother', '$ps_cl_mother_pid', '$ps_cl_marital_status', '$ps_cl_death', '$date_of_death', '$region', '$provincial', '$station', '$ps_cl_type', '$ps_cl_image') ";
    }else{
        $sql = "INSERT INTO wm_tb_personal (ps_cl_idcard, ps_cl_prefix, ps_cl_sex, ps_cl_name, ps_cl_surname, ps_cl_nickname, ps_cl_birthday2, ps_cl_father, ps_cl_father_pid, ps_cl_mother, ps_cl_mother_pid, ps_cl_marital_status, region, provincial, station, ps_cl_type, ps_cl_image) VALUES('$ps_cl_idcard', '$ps_cl_prefix', '$ps_cl_sex', '$ps_cl_name', '$ps_cl_surname', '$ps_cl_nickname', '$ps_cl_birthday2', '$ps_cl_father', '$ps_cl_father_pid', '$ps_cl_mother', '$ps_cl_mother_pid', '$ps_cl_marital_status', '$region', '$provincial', '$station', '$ps_cl_type', '$ps_cl_image') ";
    }
    
$result=mysqli_query($conn,$sql);
if($result){
	//echo "<script>alert('บันทึกข้อมูลเรียบร้อย');</script>";
//	echo "<script>window.location='../WatchmanData/Show_All.php?pcid={$ps_cl_idcard}';</script>";
    echo "<script> window.location='Add_Personal_for_bansuan.php?pcid=$ps_cl_idcard'; </script>";
    $_SESSION['Success']="บันทึกสำเร็จ"; // ตัวแปร session ตั้งชื่อว่า Success
}else{
	//echo "<script>alert(\"บันทึกข้อมูลไม่สำเร็จ\\n" . print_r(mysqli_error($conn), true) . "\");</script>";
//    echo "<script>window.location='../WatchmanData/Show_All.php?pcid={$ps_cl_idcard}';</script>";
    echo "<script> window.location='Add_Personal_for_bansuan.php?pcid=$ps_cl_idcard'; </script>";
    $_SESSION['error']="บันทึกไม่สำเร็จ"; // ตัวแปร session ตั้งชื่อว่า error หากบันทึกไม่สำเร็จ
}
}



//$sql = "INSERT INTO wm_tb_personal (ps_cl_idcard, ps_cl_prefix, ps_cl_sex, ps_cl_name, ps_cl_surname, ps_cl_nickname, ps_cl_birthday, ps_cl_birthday2, ps_cl_father, ps_cl_father_pid, ps_cl_mother, ps_cl_mother_pid, ps_cl_marital_status, ps_cl_police_station, ps_cl_type, ps_cl_image) VALUES('$ps_cl_idcard', '$ps_cl_prefix', '$ps_cl_sex', '$ps_cl_name', '$ps_cl_surname', '$ps_cl_nickname', '$ps_cl_birthday', '$ps_cl_birthday2', '$ps_cl_father', '$ps_cl_father_pid', '$ps_cl_mother', '$ps_cl_mother_pid', '$ps_cl_marital_status', '$ps_cl_police_station', '$ps_cl_type', '$ps_cl_image') ";
//$result=mysqli_query($conn,$sql);
//if($result){
//	echo "<script>alert('บันทึกข้อมูลเรียบร้อย');</script>";
//    echo "<script>window.location='../WatchmanData/Show_All.php?pcid={$ps_cl_idcard}';</script>";
//
//}else{
//	echo "<script>alert(\"บันทึกข้อมูลไม่สำเร็จ\\n" . print_r(mysqli_error($conn), true) . "\");</script>";
//    echo "<script>window.location='../WatchmanData/Show_All.php?pcid={$ps_cl_idcard}';</script>";
//}

$inputs = str_replace("'", "", compact_array($_POST));
$prev = "";//str_replace("'", "", implode(',', $row_0));

// เก็บข้อมูล Action
$acc = $user['account'];
$sql3 = "INSERT wm_tb_user_action (at_cl_user,at_cl_date,at_cl_action,at_cl_input,at_cl_prev) VALUES('$acc',CURRENT_TIMESTAMP,'Add new personal for bansuan {$ps_cl_idcard}', '$inputs', '{$prev}')";
mysqli_query($conn, $sql3);
//

mysqli_close($conn);

?>
