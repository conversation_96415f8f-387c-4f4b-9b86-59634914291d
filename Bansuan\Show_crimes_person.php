<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';
include '../Head_image.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// Pagination parameters
$items_per_page = 100;
$page = isset($_GET['page']) ? $_GET['page'] : 0;
$current_page = $page;
$start_index = $current_page * $items_per_page;


$crimes_type = isset($_GET['crime']) ? $_GET['crime'] : '0';
// ค้นข้อมูล
$search = isset($_GET['search']) ? $_GET['search'] : '';
// เรียงข้อมูล
$sort_by_no = isset($_GET['n']) ? $_GET['n'] : -1;

if($search != '') {                              // ถ้าคำค้นเป็นว่าง    
    $words = explode(',', $search); // 3 ข้อความ
    $s_cid = $words[0];                      // เลขบัตร ตรงกับคำค้น เป็นตำแหน่งที่ 0
    $s_name = $words[1];                     // ชื่อ ตรงกับคำค้น เป็นตำแหน่งที่ 1
    $s_surname = $words[2];   
    $s_nickname = $words[3];
}
else {
    $s_cid = "";
    $s_name = "";
    $s_surname = "";
    $s_nickname = "";
}

$options = "";
if($search != '') {                              // ถ้าคำค้นเป็นว่าง
    $options = "";

    if($s_cid != "") {
        $option1 = "PS.ps_cl_idcard = '{$s_cid}' ";      // $option1 = ค้นหาด้วยเลขบัตร
        $options = "OR " . $option1;
    }    
    if ($s_name != "" && $s_surname != "") {
		$option2 = "PS.ps_cl_name LIKE '%{$s_name}%' ";			// $option2 = ค้นหาด้วย ชื่อ
		$option3 = "PS.ps_cl_surname LIKE '%{$s_surname}%' ";	// $option3 = ค้นหาด้วย นามสกุล
		$options .= "OR (" . $option2 . "AND " . $option3 . ")";
	} elseif ($s_name != "") {
		$option2 = "PS.ps_cl_name LIKE '%{$s_name}%' ";
		$options .= "OR " . $option2;
	} elseif ($s_surname != "") {
		$option3 = "PS.ps_cl_surname LIKE '%{$s_surname}%' ";
		$options .= "OR " . $option3;
	}
    if($s_nickname != "") {
        $option4 = "PS.ps_cl_nickname LIKE '%{$s_nickname}%' ";		// $option4 = ค้นหาด้วย ชื่อเล่น
        $options .= "OR " . $option4;
    }
    //
    if(substr($options,0,2) == "OR") {
        $options = trim(substr($options, 3));    
    }    
}

// list by crim type
if($crimes_type == "0") {
	$where = "WHERE PS.ps_cl_type=2 AND PS.station='$station'";
    // search option 
	
    $sql = "SELECT PS.*, ST.station_name AS ST_name
            FROM wm_tb_personal AS PS
            LEFT JOIN wm_tb_police_station2 AS ST ON ST.station_code = PS.station
           $where
           GROUP BY PS.ps_cl_idcard ";

	if($options != "") {
        $sql .= " AND (" . $options . ")"; // Added parentheses around the options for correct filtering.
		
		$where .= " AND (" . $options . ")";
  }
}

// no crime type + no search word
else {
    $where = "WHERE PS.ps_cl_type=2 AND PS.station='$station' AND PS.ps_cl_CrimeType2='{$crimes_type}'";
    //$station = $user['police_station'];
    $sql = "SELECT PS.* FROM wm_tb_personal AS PS 
			$where";
		
    if ($options != "") {
        $sql .= " AND (" . $options . ")"; // Added parentheses around the options for correct filtering.
		$where .= " AND (" . $options . ")";
    }
}

// get total data
$sql2 = "SELECT COUNT(*) FROM wm_tb_personal AS PS $where ";
//echo "$sql <hr>";
$stmt2 = $pdo->prepare($sql2);
$stmt2->execute();
$total = $stmt2->fetchColumn();

//echo $sql . "<br>";
$sql .= " LIMIT :start_index, :items_per_page";
$stmt = $pdo->prepare($sql);
$stmt->bindValue(':start_index', $start_index, PDO::PARAM_INT);
$stmt->bindValue(':items_per_page', $items_per_page, PDO::PARAM_INT);
$stmt->execute();
$result = $stmt->fetchAll(PDO::FETCH_ASSOC);

//echo $sql;
// Calculate the total number of pages for pagination
$total_pages = ceil($total / $items_per_page);

?>

<!DOCTYPE html>
<html>
    <head>
        <title>ระบบ Watchman DB</title>
		<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
        <base target="_top">
		<link rel="stylesheet" href="../bootstrap/css/bootstrap.rtl.min.css">
		<link rel="stylesheet" href="../bootstrap-3.4.1-dist/css/bootstrap.min.css">
  		<script src="../jQuery/jquery-3.6.1.min.js"></script>
 		<script src="../bootstrap-3.4.1-dist/js/bootstrap.min.js"></script>
        <style>
            body {
              font-family: 'Courier New', Courier, monospace;
              font-size: 16px;
            }
              .enlarged-image {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.8);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 9999;
              }

              .enlarged-image img {
                max-width: 90%;
                max-height: 90%;
              }
			.selectpage {
				margin: 10px;
				padding: 5px;
				font-size: 18px;
			}
        </style>
    </head>
    <body class="container-fluid">
<!--		<div>
            <img src="../Image/Head2.jpg" alt="Head สภ.บ้านสวน" width="100%" >
		</div>-->
		<div>
            <?php
			include('../users_info.php');
			?>	
		</div>
<div class="container-fluid" align="left">
		<div class=" h3 text-center alert alert-danger mb-4 mt-4 " role="alert" >ข้อมูลบุคคลเกี่ยวข้องกับอาชญากรรม <?= $name_station ?></div>
		<div align="left">
            &nbsp;<a href="Add_Personal_for_bansuan.php" class="btn btn-success btn-lg mb-4" >เพิ่มข้อมูล</a>&nbsp;
            &nbsp;<a href="/Bansuan/?rnd=<?= rand(); ?>&page=information" class="btn btn-primary btn-lg mb-4" >ย้อนกลับ</a>&nbsp;
            &nbsp;<a style="background-color: yellow ; padding: 25px">พบข้อมูล จำนวน : <span style="font-size: 20px; color: #FC0408"><b style="color: crimson; font-size: 26px"> <?= number_format($total, 0, '.', ','); ?> </b> ราย </a>
            &nbsp;<a href="https://datastudio.google.com/s/iiTY86ahQrg" target="_blank" style="font-size: 20px;">ข้อมูลบุคคลเฝ้าระวัง</a>
            &nbsp;<a href="Show_crimes_person_tb.php" class="btn btn-primary  btn-lg mb-4" >บัญชีข้อมูลบุคคลเกี่ยวข้องกับอาชญากรรม</a>&nbsp;
			&nbsp;<a href="Check_crimes_nearme.php" class="btn btn-danger  btn-lg mb-4" >บุคคลใกล้ฉัน</a>&nbsp;
		</div>
    
    <div class="col-3" >
    <label style="font-size: 18px">ความผิด ตามแบบ ศขส. 23 ประเภท</label>
        <select id="tbCrimeType2" name="tbCrimeType2" class="form-select form-select-sm" style="font-size: 18px ;background-color: #E2C6E2; border-color: blueviolet" onChange="on_crime_change()">
            <option value="0">เลือก</option>
            <?php
                $res4 = $pdo->prepare("SELECT * FROM wm_tb_crimes_type order by cm_cl_aid ASC");
                    try{
                        $res4->execute();
                    }catch(PDOException $e){
                        echo 'Query $res4 Failed: '. $e->getMessage();
                    }
                
                    while($row4 = $res4->fetch(PDO::FETCH_ASSOC))
                    {
                        if($crimes_type == $row4['cm_cl_aid']) {
                            $selected = "selected";
                        }
                        else {
                            $selected = "";
                        }
                        $cm_cl_aid = htmlspecialchars($row4['cm_cl_aid'], ENT_QUOTES, 'UTF-8');
                        $cm_cl_name = htmlspecialchars($row4['cm_cl_name'], ENT_QUOTES, 'UTF-8');
                        
                        echo "<option value='$cm_cl_aid' {$selected}> $cm_cl_name </option>";
                    }
            ?>
        </select>
    </div>
<!-- Add pagination links outside the select element -->
<div class="selectpage" style="background-color: aquamarine">
    
	
	<?php
	// หน้าแรก
	echo '<a href="../Bansuan/Show_crimes_person.php?page=0"> หน้าแรก </a>';		
	?>
	<?php if ($current_page > 0): ?>
        <a href="../Bansuan/Show_crimes_person.php?page=<?php echo ($current_page - 1); ?>">Previous</a>
    <?php endif; ?>	
	<?php
		$total_show = 20;
	
		$start_ind = $current_page - 10;
		if($start_ind < 0) $start_ind = 0;
		$end_ind = $current_page + 10;
		if($end_ind > $total_pages-1) $end_ind = $total_pages-1;
		// ถ้าแสดงผลน้อยเกินไป + หน้าเพิ่ม
		if($end_ind - $start_ind <$total_show) {
			$end_ind = $start_ind + $total_show;
			if($end_ind > $total_pages-1) $end_ind = $total_pages-1;
			//หน้าสุด - หน้าเพิ่ม
			if($end_ind - $start_ind < $total_show) {
				$start_ind = $end_ind - $total_show;
				if($start_ind < 0) $start_ind = 0;
			}
		}
		
		
		for($i=$start_ind; $i<$end_ind; $i++) {
			$pn = $i + 1;
			if($current_page == $i) {
				echo "[<b> $pn </b>]";
			}
			else {
				echo "<a href='../Bansuan/Show_crimes_person.php?page=$i'> $pn </a> ";
			}
		}
	?>
    <?php if ($current_page < $total_pages-1): ?>
        <a href="../Bansuan/Show_crimes_person.php?page=<?php echo ($current_page + 1); ?>"> Next </a>
    <?php endif; ?>	
	<?php
	echo '<a href="../Bansuan/Show_crimes_person.php?page='. ($total_pages-1) .'"> หน้าสุดท้าย </a>';
	?>
</div>
		
	</div>
			
			
	<!--	 ปุ่ม search  -->
<form>
<table width="90%" border="0" cellspacing="5" cellpadding="1" style="flex: auto">
  <tbody>
    <tr align="center" >
      <td width="25% " ><span class="form-group col-md-3; align-items-baseline ">
        <input type="text" placeholder="สืบค้นจาก เลขบัตร" name="searchIdcard" id="searchIdcard" class="form-control" align="right" value="<?= $s_cid ?>" style="background-color: #E8F6F6">
      </span></td>
      <td width="25%">
        <span class="form-group col-md-3; align-items-baseline" >
        <input name="searchName" id="searchName" type="text" class="form-control" placeholder="หรือ สืบค้นจาก ชื่อ" align="right" value="<?= $s_name ?>" style="background-color: #E8F6F6">
      </span> </td>
      <td width="25%"><span class="form-group col-md-3; align-items-baseline">
        <input type="text" placeholder="หรือ สืบค้นจาก นามสกุล" name="searchSurname" id="searchSurname" class="form-control" align="right" value="<?= $s_surname ?>" style="background-color: #E8F6F6">
      </span> </td>
      <td width="20%"><span class="form-group col-md-3; align-items-baseline">
        <input type="text" placeholder="หรือ สืบค้นจาก ชื่อเล่น" name="searchNickname" id="searchNickname" class="form-control" align="right" value="<?= $s_nickname ?>" style="background-color: #E8F6F6">
      </span> </td>
      <td width="13%"><span class="form-group col-md-3"><span class="input-group-btn">
        <a><button name="submit" type="submit" id="submit" value="search" class="btn btn-warning my-4"  align="right" onClick="do_search()"><span class="glyphicon glyphicon-search"></span>ค้นหา</button></a>
      </span></span>
    </tr>
  </tbody>
</table>
</form>
            
<script>
    const form = document.querySelector('form');
    form.addEventListener('submit', function(event) {
    event.preventDefault();
    const idcardValue = document.getElementById("searchIdcard").value;
    const nameValue = document.getElementById("searchName").value;
    const surnameValue = document.getElementById("searchSurname").value;
    const nicknameValue = document.getElementById("searchNickname").value;
    do_search(idcardValue, nameValue, surnameValue, nicknameValue);
    });
</script>
			
<div class="container">
<a style="color: green">(คลิกที่ "ลำดับ" เพื่อเรียงข้อมูลจากล่างขึ้นบน)</a>
<table class="table table-striped table-hover mb-4 mt-4" >
  <tbody>
    <tr class="container-fluid">
      	<th onClick="sort_by_order(<?= $sort_by_no ?>)" style="cursor:  pointer; color: green"> ลำดับ </th>
		<th> คำนำหน้า </th>
		<th onClick="sort_by_name(<?= $sort_by_name ?>)" style="cursor:  pointer;"> ชื่อ </th>
		<th> นามสกุล </th>
		<th> ชื่อเล่น </th>
		<th> อายุ </th>
		<th> รูปภาพ</th>	
		<th></th>
    </tr>
	  
<?php

// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no = 1;

foreach ($result AS $rowCr) {
	$pcid = $rowCr['ps_cl_idcard']; //เลขบัตร ผู้พ้นโทษ
    
    // คำนวณอายุอัตโนมัติ 
    //$age = get_personal_age($row["7ps_cl_birthday"]); // thai
    $age = get_personal_age_2( $rowCr["ps_cl_birthday2"] ); // eng
    
?>
	  
    <tr class="container-fluid">
      <td> <?= $no ?> </td>
		<td> <?=$rowCr["ps_cl_prefix"]?> </td>
		<td> <?=$rowCr["ps_cl_name"]?> </td>
		<td> <?=$rowCr["ps_cl_surname"]?> </td>
		<td> <?=$rowCr["ps_cl_nickname"]?> </td>
		<td> <?= $age ?> ปี</td>                    <!-- คำนวณอายุอัตโนมัติ -->
		<td> <img class="enlarge-image" src="<?= $rowCr["ps_cl_image"] ?>" height="50"> </td> <!-- Script เมื่อคลิกแล้ว แสดงภาพใหญ่ -->
		<td> <a href="../WatchmanData/Show_All.php?pcid=<?= $rowCr["ps_cl_idcard"] ?>" class="btn btn-success mb-4" >ข้อมูล</a> </td>
    </tr>
<?php
	$no++;
}
?>
  </tbody>
</table>
</div>
    </div>
</body>

<script>
function go_detail(pcid)	
{
	window.location = "/WatchmanData/Show_All.php?pcid=" +pcid + "&rnd=" + "&page=freed";
}
    
// ฟังก์ชั่น ค้นหาข้อมูล
function do_search()
{
    var select_id = $('#tbCrimeType2 option:selected').val();
    var idcardValue = $('#searchIdcard').val();
    var nameValue = $('#searchName').val();
    var surnameValue = $('#searchSurname').val();
    var nicknameValue = $('#searchNickname').val();
    //
    var url = "/Bansuan/Show_crimes_person.php?&crime=" + select_id + "&search="+ idcardValue + ',' + nameValue + ',' + surnameValue + ',' + nicknameValue;
    window.location = url;
}

    
/* function type_change()  // คำสั่งเปลี่ยนเดือน ปี
{
    var ms = $("#mi_cl_month option:selected").val();
    var ys = $("#mi_cl_year option:selected").val();
    window.location = "/Bansuan/index.php?page=mission&month=" + ms + "&year=" + ys + "&rnd=" + Math.random();
} */
    
function sort_by_order(sort_type)
{
	if(sort_type == 1) {
		sort_type = 0;
	}
	else {
		sort_type = 1;
	}
	window.location ="Show_crimes_person.php?rnd=<?= rand(); ?>&n=" + sort_type + "&search=<?= $search ?>";
}
    /*
function sort_by_name(sort_type)
{
	if(sort_type == 1) {
		sort_type = 0;
	}
	else {
		sort_type = 1;
	}
	window.location ="index.php?rnd=<?= rand(); ?>&page=บุคคลอาชญากรรม&n=" + sort_type ;
}
*/
    
    // ฟังก์ชั่น เลือกประเภทอาชญากรรม (ยังทำไม่เสร็จ)
function on_crime_change()
{
    var select_id = $('#tbCrimeType2 option:selected').val();
    window.location ="Show_crimes_person.php?rnd=<?= rand(); ?>&crime=" + select_id;
}
</script>
	
<!-- Script เมื่อคลิกแล้ว แสดงภาพใหญ่ -->
<script>
  // Get all the images with class "enlarge-image"
  const images = document.querySelectorAll(".enlarge-image");

  // Attach a click event listener to each image
  images.forEach(image => {
    image.addEventListener("click", event => {
      // Create a new <div> element to display the enlarged image
      const enlargedImage = document.createElement("div");
      enlargedImage.classList.add("enlarged-image");

      // Create a new <img> element and set its src attribute to the clicked image's src
      const img = document.createElement("img");
      img.src = event.target.src;

      // Append the <img> element to the <div> element
      enlargedImage.appendChild(img);

      // Add the <div> element to the body
      document.body.appendChild(enlargedImage);

      // Attach a click event listener to the <div> element to remove it when clicked
      enlargedImage.addEventListener("click", event => {
        enlargedImage.remove();
      });
    });
  });
</script>
