<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../Alert.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);
$ip_address = $_SERVER['REMOTE_ADDR'];
$action = 'Save confidential gun'; // You can set the action to a suitable value
$data = str_replace("'", "", compact_array($_POST));
log_activity($acc, $action, $data, $ip_address); // ใช้ function ในไฟล์ Condb.php

/*echo '<pre>';
print_r($_POST);
echo '</pre>';

echo '<hr>';
exit();*/

$aid_conf_gun = isset($_POST['aid_conf_gun']) ? $_POST['aid_conf_gun'] : '';
$region = isset($_POST['region']) ? $_POST['region'] : '';
$provincial = isset($_POST['provincial']) ? $_POST['provincial'] : '';
$station = isset($_POST['station']) ? $_POST['station'] : '';
$case_no = isset($_POST['case_no']) ? $_POST['case_no'] : '';
$inspect_no = isset($_POST['inspect_no']) ? $_POST['inspect_no'] : '';
$gun_treasure_detail = isset($_POST['gun_treasure_detail']) ? $_POST['gun_treasure_detail'] : '';
$inquiry_official = isset($_POST['inquiry_official']) ? $_POST['inquiry_official'] : '';
$confidential_gun = isset($_POST['confidential_gun']) ? $_POST['confidential_gun'] : '';
$unique_guns = isset($_POST['unique_guns']) ? $_POST['unique_guns'] : '';
$unique_guns_unknow = isset($_POST['unique_guns_unknow']) ? $_POST['unique_guns_unknow'] : '';
$sanpawut = isset($_POST['sanpawut']) ? $_POST['sanpawut'] : '';
$DistrictChief_co = isset($_POST['DistrictChief_co']) ? $_POST['DistrictChief_co'] : '';
$DistrictChief_revoke = isset($_POST['DistrictChief_revoke']) ? $_POST['DistrictChief_revoke'] : '';
$no_identify = isset($_POST['no_identify']) ? $_POST['no_identify'] : '';
$no_identify_unknow = isset($_POST['no_identify_unknow']) ? $_POST['no_identify_unknow'] : '';
$destroy_unknow = isset($_POST['destroy_unknow']) ? $_POST['destroy_unknow'] : '';
$status = isset($_POST['status']) ? $_POST['status'] : '';
$record_name = isset($_POST['record_name']) ? $_POST['record_name'] : '';
$record_date = isset($_POST['record_date']) ? $_POST['record_date'] : '';

// save file สส.1
$file1 = $_FILES[ 'conf_gun_file' ][ 'tmp_name' ];
if ( is_uploaded_file( $file1 ) ) {
  $conf_gun_file = "uploaded/Doc/" . $_FILES[ 'conf_gun_file' ][ 'name' ];	
  move_uploaded_file( $file1, "../" . $conf_gun_file );  
} else {
  $conf_gun_file = '';
}
try{
$sql ="SELECT * FROM wm_tb_gun_confidential WHERE aid_conf_gun = :aid_conf_gun ";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':aid_conf_gun', $aid_conf_gun);
    $stmt->execute();
    $row = $stmt->fetch(PDO::FETCH_ASSOC);

if($row) {
    $aid_conf_gun = $row['aid_conf_gun'];
    
    if($conf_gun_file !== ''){
        $sql = "UPDATE wm_tb_gun_confidential SET ".
            "region = :region, " .
            "provincial = :provincial, " .
            "station = :station, " .
            "case_no = :case_no, " .
            "inspect_no = :inspect_no, " .
            "gun_treasure_detail = :gun_treasure_detail, " .
            "inquiry_official = :inquiry_official, " .
            "confidential_gun = :confidential_gun, " .
            //"unique_guns = :unique_guns, " .
			(($unique_guns == '') ? '' : (",unique_guns = :unique_guns ")) .
            //"unique_guns_unknow = :unique_guns_unknow, " .
			(($unique_guns_unknow == '') ? '' : (",unique_guns_unknow = :unique_guns_unknow ")) .
            //"sanpawut = :sanpawut, " .
			(($sanpawut == '') ? '' : (",sanpawut = :sanpawut ")) .
            //ตรวจสอบก่อนว่า มีการส่งค่ามาหรือไม่
            //"DistrictChief_co = :DistrictChief_co, " .
            (($DistrictChief_co == '') ? '' : (",DistrictChief_co = :DistrictChief_co ")) .
            //"DistrictChief_revoke = :DistrictChief_revoke, " .
            (($DistrictChief_revoke == '') ? '' : (",DistrictChief_revoke = :DistrictChief_revoke ")) .
            //"no_identify = :no_identify, " .
            (($no_identify == '') ? '' : (",no_identify = :no_identify ")) .
            //"no_identify_unknow = :no_identify_unknow, " .
            (($no_identify_unknow == '') ? '' : (",no_identify_unknow = :no_identify_unknow ")) .
            //"destroy_unknow = :destroy_unknow, " .
            (($destroy_unknow == '') ? '' : (",destroy_unknow = :destroy_unknow ")) .
            "status = :status, " .
            "record_name = :record_name, " .
            "record_date = :record_date, " .
            "conf_gun_file = :conf_gun_file ";
            "WHERE aid_conf_gun = :aid_conf_gun ";
                $stmt = $pdo->prepare($sql);
                $stmt->bindParam(':aid_conf_gun', $aid_conf_gun, PDO::PARAM_INT);
                $stmt->bindParam(':region', $region, PDO::PARAM_STR);
                $stmt->bindParam(':provincial', $provincial, PDO::PARAM_STR);
                $stmt->bindParam(':station', $station, PDO::PARAM_STR);
                $stmt->bindParam(':case_no', $case_no, PDO::PARAM_STR);
                $stmt->bindParam(':inspect_no', $inspect_no, PDO::PARAM_STR);
                $stmt->bindParam(':gun_treasure_detail', $gun_treasure_detail, PDO::PARAM_STR);
                $stmt->bindParam(':inquiry_official', $inquiry_official, PDO::PARAM_STR);
                $stmt->bindParam(':confidential_gun', $confidential_gun, PDO::PARAM_STR);
                $stmt->bindParam(':unique_guns', $unique_guns, PDO::PARAM_STR);
                $stmt->bindParam(':unique_guns_unknow', $unique_guns_unknow, PDO::PARAM_STR);
                $stmt->bindParam(':sanpawut', $sanpawut, PDO::PARAM_STR);
                $stmt->bindParam(':DistrictChief_co', $DistrictChief_co, PDO::PARAM_STR);
                $stmt->bindParam(':DistrictChief_revoke', $DistrictChief_revoke, PDO::PARAM_STR);
                $stmt->bindParam(':no_identify', $no_identify, PDO::PARAM_STR);
                $stmt->bindParam(':no_identify_unknow', $no_identify_unknow, PDO::PARAM_STR);
                $stmt->bindParam(':destroy_unknow', $destroy_unknow, PDO::PARAM_STR);
                $stmt->bindParam(':status', $status, PDO::PARAM_STR);
                $stmt->bindParam(':record_name', $record_name, PDO::PARAM_STR);
                $stmt->bindParam(':record_date', $record_date, PDO::PARAM_STR);
                $stmt->bindParam(':conf_gun_file', $conf_gun_file, PDO::PARAM_STR);
    }else{
        $sql = "UPDATE wm_tb_gun_confidential SET ".
            "region = :region, " .
            "provincial = :provincial, " .
            "station = :station, " .
            "case_no = :case_no, " .
            "inspect_no = :inspect_no, " .
            "gun_treasure_detail = :gun_treasure_detail, " .
            "inquiry_official = :inquiry_official, " .
            "confidential_gun = :confidential_gun, " .
           //"unique_guns = :unique_guns, " .
			(($unique_guns == '') ? '' : (",unique_guns = :unique_guns ")) .
            //"unique_guns_unknow = :unique_guns_unknow, " .
			(($unique_guns_unknow == '') ? '' : (",unique_guns_unknow = :unique_guns_unknow ")) .
            //"sanpawut = :sanpawut, " .
			(($sanpawut == '') ? '' : (",sanpawut = :sanpawut ")) .
			//ตรวจสอบก่อนว่า มีการส่งค่ามาหรือไม่
            //"DistrictChief_co = :DistrictChief_co, " .
            (($DistrictChief_co == '') ? '' : (",DistrictChief_co = :DistrictChief_co ")) .
            //"DistrictChief_revoke = :DistrictChief_revoke, " .
            (($DistrictChief_revoke == '') ? '' : (",DistrictChief_revoke = :DistrictChief_revoke ")) .
            //"no_identify = :no_identify, " .
            (($no_identify == '') ? '' : (",no_identify = :no_identify ")) .
            //"no_identify_unknow = :no_identify_unknow, " .
            (($no_identify_unknow == '') ? '' : (",no_identify_unknow = :no_identify_unknow ")) .
            //"destroy_unknow = :destroy_unknow, " .
            (($destroy_unknow == '') ? '' : (",destroy_unknow = :destroy_unknow ")) .
			
            /*"DistrictChief_co = :DistrictChief_co, " .
            "DistrictChief_revoke = :DistrictChief_revoke, " .
            "no_identify = :no_identify, " .
            "no_identify_unknow = :no_identify_unknow, " .
            "destroy_unknow = :destroy_unknow, " .*/
			
            "status = :status, " .
            "record_name = :record_name, " .
            "record_date = :record_date " .
            "WHERE aid_conf_gun = :aid_conf_gun ";
                $stmt = $pdo->prepare($sql);
                $stmt->bindParam(':aid_conf_gun', $aid_conf_gun, PDO::PARAM_INT);
                $stmt->bindParam(':region', $region, PDO::PARAM_STR);
                $stmt->bindParam(':provincial', $provincial, PDO::PARAM_STR);
                $stmt->bindParam(':station', $station, PDO::PARAM_STR);
                $stmt->bindParam(':case_no', $case_no, PDO::PARAM_STR);
                $stmt->bindParam(':inspect_no', $inspect_no, PDO::PARAM_STR);
                $stmt->bindParam(':gun_treasure_detail', $gun_treasure_detail, PDO::PARAM_STR);
                $stmt->bindParam(':inquiry_official', $inquiry_official, PDO::PARAM_STR);
                $stmt->bindParam(':confidential_gun', $confidential_gun, PDO::PARAM_STR);
                $stmt->bindParam(':unique_guns', $unique_guns, PDO::PARAM_STR);
                $stmt->bindParam(':unique_guns_unknow', $unique_guns_unknow, PDO::PARAM_STR);
                $stmt->bindParam(':sanpawut', $sanpawut, PDO::PARAM_STR);
                $stmt->bindParam(':DistrictChief_co', $DistrictChief_co, PDO::PARAM_STR);
                $stmt->bindParam(':DistrictChief_revoke', $DistrictChief_revoke, PDO::PARAM_STR);
                $stmt->bindParam(':no_identify', $no_identify, PDO::PARAM_STR);
                $stmt->bindParam(':no_identify_unknow', $no_identify_unknow, PDO::PARAM_STR);
                $stmt->bindParam(':destroy_unknow', $destroy_unknow, PDO::PARAM_STR);
                $stmt->bindParam(':status', $status, PDO::PARAM_STR);
                $stmt->bindParam(':record_name', $record_name, PDO::PARAM_STR);
                $stmt->bindParam(':record_date', $record_date, PDO::PARAM_STR);
    }
}else{
        $sql = "INSERT INTO wm_tb_gun_confidential(region, provincial, station, case_no, inspect_no, gun_treasure_detail, inquiry_official, confidential_gun, unique_guns, unique_guns_unknow, sanpawut, DistrictChief_co, DistrictChief_revoke, no_identify, no_identify_unknow, destroy_unknow, status, record_name, record_date, conf_gun_file) VALUES(:region, :provincial, :station, :case_no, :inspect_no, :gun_treasure_detail, :inquiry_official, :confidential_gun, :unique_guns, :unique_guns_unknow, :sanpawut, :DistrictChief_co, :DistrictChief_revoke, :no_identify, :no_identify_unknow, :destroy_unknow, :status, :record_name, :record_date, :conf_gun_file) ";
                $stmt = $pdo->prepare($sql);
                $stmt->bindParam(':region', $region, PDO::PARAM_STR);
                $stmt->bindParam(':provincial', $provincial, PDO::PARAM_STR);
                $stmt->bindParam(':station', $station, PDO::PARAM_STR);
                $stmt->bindParam(':case_no', $case_no, PDO::PARAM_STR);
                $stmt->bindParam(':inspect_no', $inspect_no, PDO::PARAM_STR);
                $stmt->bindParam(':gun_treasure_detail', $gun_treasure_detail, PDO::PARAM_STR);
                $stmt->bindParam(':inquiry_official', $inquiry_official, PDO::PARAM_STR);
                $stmt->bindParam(':confidential_gun', $confidential_gun, PDO::PARAM_STR);
                $stmt->bindParam(':unique_guns', $unique_guns, PDO::PARAM_STR);
                $stmt->bindParam(':unique_guns_unknow', $unique_guns_unknow, PDO::PARAM_STR);
                $stmt->bindParam(':sanpawut', $sanpawut, PDO::PARAM_STR);
                $stmt->bindParam(':DistrictChief_co', $DistrictChief_co, PDO::PARAM_STR);
                $stmt->bindParam(':DistrictChief_revoke', $DistrictChief_revoke, PDO::PARAM_STR);
                $stmt->bindParam(':no_identify', $no_identify, PDO::PARAM_STR);
                $stmt->bindParam(':no_identify_unknow', $no_identify_unknow, PDO::PARAM_STR);
                $stmt->bindParam(':destroy_unknow', $destroy_unknow, PDO::PARAM_STR);
                $stmt->bindParam(':status', $status, PDO::PARAM_STR);
                $stmt->bindParam(':record_name', $record_name, PDO::PARAM_STR);
                $stmt->bindParam(':record_date', $record_date, PDO::PARAM_STR);
                $stmt->bindParam(':conf_gun_file', $conf_gun_file, PDO::PARAM_STR);
}
    
	
	
	
	
	
	
$result = $stmt->execute();

if ($result) {
    $_SESSION['success'] = "Data has been inserted succesfully";
    showSweetAlert("Saved successfully", "บันทึกข้อมูลเรียบร้อย");
    unset($_SESSION['success']); // Clear the session variable
    header("refresh:2; url=/WatchmanData/Show_gun_treasure_all.php");
} else {
    $_SESSION['error'] = "Error";
    showSweetAlert('Error', "บันทึกข้อมูล ไม่สำเร็จ", 'error');
    unset($_SESSION['error']); // Clear the session variable
    header("refresh:2; url=/WatchmanData/Show_gun_treasure_all.php");
}

}catch(Exception $e){
    echo 'Query Failed: '. $e->getMessage();
}

?>