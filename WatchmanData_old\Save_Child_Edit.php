<?php
include 'Condb.php';
// EDIT > AID >> to update
$cd_cl_aid = $_POST[ 'cd_cl_aid' ];

$cd_cl_idcard = $_POST[ 'cd_cl_idcard' ];
$cd_cl_idcard_child = $_POST[ 'cd_cl_idcard_child' ];
$cd_cl_prefix = $_POST[ 'cd_cl_prefix' ];
$cd_cl_sex = $_POST[ 'cd_cl_sex' ];
$cd_cl_name = $_POST[ 'cd_cl_name' ];
$cd_cl_surname = $_POST[ 'cd_cl_surname' ];
$cd_cl_nickname = $_POST[ 'cd_cl_nickname' ];
$cd_cl_birthday = $_POST[ 'cd_cl_birthday' ];
$cd_cl_age = $_POST[ 'cd_cl_age' ];
$cd_cl_father = $_POST[ 'cd_cl_father' ];
$cd_cl_father_pid = $_POST[ 'cd_cl_father_pid' ];
$cd_cl_mother = $_POST[ 'cd_cl_mother' ];
$cd_cl_mother_pid = $_POST[ 'cd_cl_mother_pid' ];
$cd_cl_spouse = $_POST[ 'cd_cl_spouse' ];

// save Image
$file1 = $_FILES[ 'cd_cl_image' ][ 'tmp_name' ];
if ( is_uploaded_file( $file1 ) ) {
  $cd_cl_image = "/policeinnopolis/uploaded/Image1/" . basename( $_FILES[ 'cd_cl_image' ][ 'name' ] );
	
  move_uploaded_file( $file1, "../" . $cd_cl_image );  
} else {
  $cd_cl_image = '';
}
//php >> $[AA-z]


$sql = "UPDATE wm_tb_child SET " .
		"cd_cl_idcard = '$cd_cl_idcard', ".
		"cd_cl_idcard_child = '$cd_cl_idcard_child', " .
		"cd_cl_prefix = '$cd_cl_prefix', " .
		"cd_cl_sex = '$cd_cl_sex', " .
		"cd_cl_name = '$cd_cl_name', " .
		"cd_cl_surname = '$cd_cl_surname', " .
		"cd_cl_nickname = '$cd_cl_nickname', " .
		"cd_cl_birthday = '$cd_cl_birthday', " .
		"cd_cl_age = '$cd_cl_age', " .
		"cd_cl_father = '$cd_cl_father', " .
		"cd_cl_father_pid = '$cd_cl_father_pid', " .
		"cd_cl_mother = '$cd_cl_mother', " .
		"cd_cl_mother_pid = '$cd_cl_mother_pid', " .
		"cd_cl_spouse = '$cd_cl_spouse', " .
		"cd_cl_image = '$cd_cl_image' " .
		"WHERE cd_cl_aid = '$cd_cl_aid' ";

$result=mysqli_query($conn,$sql);
if($result){
	echo "<script>alert('บันทึกข้อมูลเรียบร้อย');</script>";	
}else{
	echo "<script>alert(\"บันทึกข้อมูลไม่สำเร็จ\\n" . print_r(mysqli_error($conn), true) . "\");</script>";
}
mysqli_close($conn);
echo "<script>window.location='Show_Child.php?pcid=" . $cd_cl_idcard . "';</script>";
?>

