<script src="/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>
<script src="../SweetAlert2/dist/sweetalert2.all.min.js"></script>

<?php //PDO
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

$id = isset($_GET['bl_cl_aid']) ? $_GET['bl_cl_aid'] : '';

//get month from select or current month
$month = isset($_GET['month']) ? $_GET['month'] : date("m");
$year = isset($_GET['year']) ? $_GET['year'] : date("Y");

$m1 = '';
$m2 = '';
$m3 = '';
$m4 = '';
$m5 = '';
$m6 = '';
$m7 = '';
$m8 = '';
$m9 = '';
$m10 = '';
$m11 = '';
$m12 = '';
switch($month)
{
    case "01":  $m1 = "selected"; break;
    case "02": $m2 = "selected"; break;
    case "03":   $m3 = "selected"; break;
    case "04":  $m4 = "selected"; break;
    case "05": $m5 = "selected"; break;
    case "06":  $m6 = "selected"; break;
    case "07": $m7 = "selected"; break;
    case "08":  $m8= "selected"; break;
    case "09": $m9 = "selected"; break;
    case "10": $m10 = "selected"; break;
    case "11": $m11 = "selected"; break;
    case "12": $m12 = "selected"; break;
}
$current_month = $global_thai_month[0+$month];
$y2 = $year+543;

?>
    
<!---->
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link rel="stylesheet" href="/bootstrap/css/bootstrap-grid.css">
<style>
    .swal2-popup {
    width: 500px !important;
    height: auto !important;
    font-size: 14px !important;
}
@media print {
   a {
      display: none !important;
   }
}
</style>
<link rel="stylesheet" type="text/css" href="print.css" media="print">
<div class="container-fluid" align="left">
<div class=" h3 text-center  alert alert-success mb-4 mt-4 " role="alert" ><h1><span style="font-size: 24px ;padding: 5px">ตารางแสดงผลการปฏิบัติ ปิดล้อมตรวจค้น <?= $name_station ?> ประจำเดือน <?= $current_month ?></span></h1></div>
  <div>
    <table width="80%" border="0" cellspacing="1" cellpadding="1">
      <tbody>
        <tr>
          <td width="28%"><div align="left">
            &nbsp;<a href="Add_blockade.php" class="btn btn-success btn-lg mb-4 <?= $add_btn ?>" >เพิ่มข้อมูล</a>&nbsp;&nbsp;<a href="https://vv19-bgs.in.th/" class="btn btn-primary btn-lg mb-4" target="_blank">Police 4.0</a></td>
          <td width="22%"><label>เลือกปี </label>
            <select id="bl_cl_year" name="bl_cl_year" class="form-select col-8 d-lg-inline" style="background-color: #00D2FE; font-size: 16px" onChange="dates_change()" >
              <option value="" >เลือก</option>
              <?php
                $cur_y = date("Y") + 2;
                for($y=2020; $y<$cur_y; $y++) {
                    $sel = ($y == $year) ? "selected" : '';
                    echo "<option value='$y' $sel> ". ($y + 543) ." </option>";
                }
            ?>
            </select></td>
          <td width="46%"><label>เดือน</label>
            <select id="bl_cl_month" name="bl_cl_month" class="form-select col-3 d-lg-inline" style="background-color: #00D2FE; font-size: 16px" onChange="dates_change()">
              <option value="">เลือก</option>
              <option <?= $m1 ?> value="01">มกราคม</option>
              <option <?= $m2 ?> value="02">กุมภาพันธ์</option>
              <option <?= $m3 ?> value="03">มีนาคม</option>
              <option <?= $m4 ?> value="04">เมษายน</option>
              <option <?= $m5 ?> value="05">พฤษภาคม</option>
              <option <?= $m6 ?> value="06">มิถุนายน</option>
              <option <?= $m7 ?> value="07">กรกฎาคม</option>
              <option <?= $m8 ?> value="08">สิงหาคม</option>
              <option <?= $m9 ?> value="09">กันยายน</option>
              <option <?= $m10 ?> value="10">ตุลาคม</option>
              <option <?= $m11 ?> value="11">พฤศจิกายน</option>
              <option <?= $m12 ?> value="12">ธันวาคม</option>
            </select></td>
          <td width="2%">&nbsp;</td>
          <td width="2%">&nbsp;</td>
        </tr>
      </tbody>
    </table>
</div>
<!--<h1 style="font-size: 24px ;background-color: yellow ;padding: 5px" h1 align="center" >ตารางแสดงผลการปฏิบัติ ปิดล้อมตรวจค้น ประจำเดือน</h1>-->

<table width="100%" height="94" border="1" cellpadding="1" cellspacing="1" class="table-bordered table-hover mb-4 mt-4">
  <tbody>
    <tr>
	  <td height="44" bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">ลำดับ</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">ครั้งที่</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">วันตรวจค้น</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">วิธีเข้าค้น</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">เลขที่หมายค้น</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">สถานที่ตรวจค้น</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">ผู้นำตรวจค้น</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">พบ/ไม่พบ</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">รายการที่พบ</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">หมายเหตุ</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">หน่วยงาน</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">ผู้บันทึกข้อมูล</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">ไฟล์</td>
	  <td colspan="2" bgcolor="#995D5D"></td>
    </tr>
	  
<?php
$sql_bl = "SELECT
                T1.*,
                T2.station_name AS station,
                T3.ap_name_th AS amphur,
                T4.tb_name_th AS tumbon,
                T5.pv_name_th AS province,
                T6.illegal_things AS Thing
            FROM
                `wm_tb_blockade` AS T1 " .
                "LEFT JOIN wm_tb_police_station2 AS T2 ON T1.station = T2.station_code " .
                "LEFT JOIN amphures AS T3 ON T1.bl_cl_amphur = T3.ap_code " .
                "LEFT JOIN districts AS T4 ON T1.bl_cl_tumbon = T4.tb_id " .
                "LEFT JOIN provinces AS T5 ON T1.bl_cl_province = T5.pv_code " .
                "LEFT JOIN wm_tb_blockade_illegal_things AS T6 ON T1.bl_cl_illegal_things = T6.aid_things " .
          "WHERE
                T1.station='$station' AND MONTH(bl_cl_date)=:month AND YEAR(bl_cl_date)=:year
          ORDER BY
                bl_cl_num ASC ";  //ตั้งตัวแปร sql เลือกฐานข้อมูลจากตารางที่ชื่อ wm_tb_personal
                      
    $stmt = $pdo->prepare($sql_bl);
    $stmt->bindParam(':month', $month);
    $stmt->bindParam(':year', $year);
    $stmt->execute();

// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no = 1;
while($row_bl = $stmt->fetch(PDO::FETCH_ASSOC))	//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
{
     $id = $row_bl['bl_cl_aid'];  
    
    //ฟังก์ชั่น วันที่ ดึงจาก condb
    if($row_bl["bl_cl_date"]!=''){
        $strDate = DateThai( $row_bl["bl_cl_date"] );
    }else{
        $strDate = '';
    }
    
    
    //ตรวจสอบไฟล์ สส.1 ก่อนว่า มีไฟล์ในตารางไหม หากมี ให้ส่งเป็นลิงค์
	$pdf = $row_bl["bl_cl_file"];
	if($pdf !== '')
	{
		$links = '<a href="/' . $row_bl["bl_cl_file"] .'" target="_blank">Download</a>';
	}
	else {
		$links = 'ไม่มีไฟล์';
	}
    
?>	

    <tr>
	  <td> <?= $no ?> </td>
      <td><?= $row_bl["bl_cl_num"]?></td>
      <td nowrap>&nbsp;<?= $strDate ?>&nbsp;</td>
      <td>&nbsp;<?= $row_bl["bl_cl_method"]?>&nbsp;</td>
      <td>&nbsp;<?= $row_bl["bl_cl_search_warrant"]?>&nbsp;</td>
      <td style="text-align: left">&nbsp;<?= $row_bl["bl_cl_adr"]?>&nbsp;ม.<?= $row_bl["bl_cl_moo"]?>&nbsp;ต.<?= $row_bl["tumbon"]?>&nbsp;อ.<?= $row_bl["amphur"]?>&nbsp;จ.<?= $row_bl["province"]?></td>
      <td>&nbsp;<?= $row_bl["bl_cl_conduct"]?>&nbsp;</td>
      <td nowrap>&nbsp;<?= $row_bl["bl_cl_find_illegal"]?>&nbsp;</td>
      <td>&nbsp;<?= $row_bl["Thing"]?>&nbsp;</td>
      <td>&nbsp;<?= $row_bl["bl_cl_remark"]?>&nbsp;</td>
      <td>&nbsp;<?= $row_bl["station"]?>&nbsp;</td>
      <td>&nbsp;<?= $row_bl["bl_cl_record"]?>&nbsp;</td>
      <td><?= $links ?></td>
	  <td>&nbsp;<a href="Edit_blockade.php?id=<?= $row_bl["bl_cl_aid"] ?>" class="btn btn-warning mb-4 <?= $edit_btn ?>" >แก้ไข</a>&nbsp; </td>
	  <td>&nbsp;<button href="#" class="btn btn-danger mb-4" onClick="deleteItem(<?= $row_bl['bl_cl_aid'] ?>, '<?= $row_bl["bl_cl_num"]?>')"<?=$del_btn?>>ลบ</button>&nbsp;</td>
    </tr>
  </tbody>
<?php
	$no++;
}
?>
</table>
   
<script>
function deleteItem(id, num) {
    Swal.fire({
        title: 'คุณต้องการลบข้อมูลนี้ใช่ไหม?',
        text: "หลังจากลบแล้ว จะไม่สามารถกู้คืนได้",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'ใช่, ยืนยันลบ',
        cancelButtonText: 'ยกเลิก'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = '/Bansuan/Del_blockade.php?id=' + id + '&num=' + num;
        }
    });
}
</script>
<script>
/*function Del(url)
{
	if(confirm("คุณจะลบข้อมูล หรือ ไม่ ?"))
	{
		window.location = url;		
	}
}*/
function dates_change()  // คำสั่งเปลี่ยนเดือน ปี
{
    var ms = $("#bl_cl_month option:selected").val();
    var ys = $("#bl_cl_year option:selected").val();
    window.location = "/Bansuan/index.php?page=blockade&month=" + ms + "&year=" + ys + "&rnd=" + Math.random();
}

</script>
