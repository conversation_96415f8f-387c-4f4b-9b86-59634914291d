<?php //PDO
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

$id = isset($_GET['lw_cl_aid']) ? $_GET['lw_cl_aid'] : '';
// type
$lw_type = isset($_GET['lw_cl_type']) ? $_GET['lw_cl_type'] : '0';
// ค้นข้อมูล
$name = isset($_GET['search']) ? $_GET['search'] : '';
// เรียงข้อมูล
$sort_by_no = isset($_GET['n']) ? $_GET['n'] : -1;

$options = "";
$params = [];
if ($name != '') {
    $options = " LW.lw_cl_heading LIKE :name ";
    $params['name'] = "%$name%";
}

$orderBy = " ORDER BY lw_cl_date DESC";
if ($sort_by_no == 0) {
    $orderBy = " ORDER BY LW.lw_cl_aid ASC";
} elseif ($sort_by_no == 1) {
    $orderBy = " ORDER BY LW.lw_cl_aid DESC";
}

$sql_lw = "SELECT LW.*, LT.lwt_type AS type 
            FROM wm_tb_law_order AS LW
            LEFT JOIN wm_tb_law_order_type AS LT ON LW.lw_cl_type = LT.lwt_aid ";

$whereClause = "";
if ($lw_type == "0") {
    if ($options !== "") {
        $whereClause = "WHERE $options";
    }
} else {
    $whereClause = "WHERE LW.lw_cl_type = :lw_type";
    $params['lw_type'] = $lw_type;
    if ($options !== "") {
        $whereClause .= " AND $options";
    }
}

$sql_lw .= $whereClause . $orderBy;

$stmt = $pdo->prepare($sql_lw);
$stmt->execute($params);
$result_lw = $stmt->fetchAll(PDO::FETCH_ASSOC);

$total = count($result_lw);

//echo '<pre>';
//print_r($lw_type);
//echo '</pre>';
////
//echo '<hr>';

?>

<!DOCTYPE html>
<html>
<head>
    <title>ระบบ WatchmanDB</title>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="/bootstrap/css/bootstrap-grid.css">
    <script src="../jQuery/jquery-3.6.1.min.js"></script>
    <script src="../bootstrap-3.4.1-dist/js/bootstrap.min.js"></script>
    <style>
        body {
          font-family: 'Courier New', Courier, monospace;
          font-size: 16px;
        }
        .swal2-popup {
        width: 500px !important;
        height: auto !important;
        font-size: 14px !important;
        }
    </style>
</head>
    <body class="container-fluid">
<div class="container-fluid" align="left">
		<div class=" h3 text-center  alert alert-success mb-4 mt-4 " role="alert" >กฎหมาย ระเบียบ คำสั่ง และแนวทางปฏิบัติราชการ</div>
        <div align="left" style="flex: initial"> &nbsp;<a href="Add_law_order.php" class="btn btn-success btn-lg mb-4 <?= $add_btn ?>" >เพิ่มข้อมูล</a>&nbsp;&nbsp; <a href="link_law_order.php" class="btn btn-primary btn-lg mb-4" >ไฟล์กฎหมาย ระเบียบ คำสั่ง แนวทาง (เพิ่มเติม)</a></div>

    <div class="col-3" >
    <label style="font-size: 18px">กฎหมาย ระเบียบ คำสั่ง แนวทาง</label>
        <select id="lw_cl_type" name="lw_cl_type" class="form-select form-select-sm" style="font-size: 18px ;background-color: #01FDFD; border-color: blueviolet" onChange="on_type_change()">
            <option value="0">แสดงรายการทั้งหมด</option>
            <?php
                $res_ty = $pdo->prepare("SELECT * FROM wm_tb_law_order_type order by lwt_aid ASC");
                $res_ty->execute();
            
                while($row_ty = $res_ty->fetch(PDO::FETCH_ASSOC))
                {
                    if($lw_type == $row_ty['lwt_aid']) {
                        $selected = "selected";
                    }
                    else {
                        $selected = "";
                    }

                    echo "<option value='{$row_ty['lwt_aid']}' {$selected}> {$row_ty['lwt_type']} </option>";
                }
            ?>
        </select>

    </div>

	<!--	 ปุ่ม search  -->
<table width="90%" border="0" cellspacing="5" cellpadding="1" style="flex: auto">
  <tbody>
    <tr align="center" >
      <td width="25%">
        <span class="form-group col-md-3; align-items-baseline" >
        <input name="searchName" id="searchName" type="text" class="form-control" placeholder="สืบค้นจาก ชื่อเรื่อง" align="right" value="<?= $name ?>" style="background-color: #E8F6F6">
      </span> </td>
      <td width="13%"><span class="form-group col-md-3"><span class="input-group-btn">
        <a><button name="submit" type="submit" id="submit" value="search" class="btn btn-warning my-4"  align="right" onClick="do_search()"><span class="glyphicon glyphicon-search">ค้นหา</span></button></a>
      </span></span>
    </tr>
  </tbody>
</table> 

<script>
  const input = document.getElementById("searchName");
  input.addEventListener("keyup", function(event) {
    if (event.keyCode === 13) {
      event.preventDefault();
      do_search();
    }
  });
</script> 
			
<a style="color: green">(คลิกที่ "ลำดับ" เพื่อเรียงข้อมูลจากล่างขึ้นบน)</a>
<table class="table table-striped table-hover mb-4 mt-4" >
  <tbody>
    <table class="table table-striped table-hover" > 
	<tr>	
		<th onClick="sort_by_no(<?= $sort_by_no ?>)" style="cursor:  pointer;"> ลำดับ </th>
		<th> ประเภท </th>
		<th> เลขที่ </th>
		<th> เรื่อง </th>
		<th> วันที่ใช้ </th>
        <th> ผู้บันทึก </th>
		<th> ไฟล์</th>
		<th></th>
        <th></th>
	</tr>
	  

<?php
if($total == 0) {
?>
      <tr class="container-fluid">
          <td>&nbsp;</td>
          <td colspan="9">ไม่พบข้อมูล</td>
          <td>&nbsp;</td>
     </tr>
<?php      
}
// ค้นเจอ
else {
    
    // ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
    $no = 1;
    foreach ($result_lw as $row_lw) {
    //echo $row_lw;
      //เพิ่มเข้ามาใหม่ 12/09/2022 จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
     $id = $row_lw['lw_cl_aid'];  
    
    //ฟังก์ชั่น วันที่ ดึงจาก condb
    $strDate = DateThai( $row_lw["lw_cl_date"] );
    
    //ตรวจสอบไฟล์ สส.1 ก่อนว่า มีไฟล์ในตารางไหม หากมี ให้ส่งเป็นลิงค์
	$pdf = $row_lw["lw_cl_file"];
	if($pdf !== '')
	{
		$links = '<a href="/' . $row_lw["lw_cl_file"] .'" target="_blank">Download</a>';
	}
	else {
		$links = 'ไม่มีไฟล์';
	}
    
?>
	  
    <tr>	
		<td> <?= $no ?> </td>
		<td> <?=$row_lw["type"]?> </td>  
		<td> <?=$row_lw["lw_cl_no"]?> </td>
		<td> <?=$row_lw["lw_cl_heading"]?> </td>
		<td nowrap> <?= $strDate ?> </td>
		<td> <?=$row_lw["lw_cl_record"]?> </td>
        <td> <?=$links?></td>  
		
        <td> <a href="Edit_law_order.php?id=<?=$row_lw["lw_cl_aid"] ?>" class="btn btn-warning mb-4 <?= $edit_btn ?>" >แก้ไข</a> </td>
		<td> <button href="#" class="btn btn-danger mb-4" onClick="deleteItem(<?= $row_lw["lw_cl_aid"] ?>)"<?=$del_btn?>>ลบ</button> </td>
        
<?php
	   $no++;
    }//while
    
} // else ค้นเจอ   
?>
  </tbody>
</table>
        
</body>
</html>

<script>
// ฟังก์ชั่น ค้นหาข้อมูล
function do_search()
{
    var select_id = $('#lw_cl_type option:selected').val();    
    var name = $('#searchName').val();
    //
    var url = "index.php?page=law_order&lw_cl_type=" + select_id + "&search="+ name + "&rnd=" + Math.random();
    
    window.location = url;
}

function sort_by_order(sort_type)
{
    var select_id = $('#lw_cl_type option:selected').val();
    
	if(sort_type == 1) {
		sort_type = 0;
	}
	else {
		sort_type = 1;
	}
    
	window.location ="index.php?page=law_order&lw_cl_type=" + select_id + "&n=" + sort_type + "&search=<?= $name ?>" + "&rnd=" + Math.random();
}
    
    // ฟังก์ชั่น เลือกประเภท
function on_type_change()
{
    var select_id = $('#lw_cl_type option:selected').val();
    window.location ="index.php?page=law_order&lw_cl_type=" + select_id + "&rnd=" + Math.random();
}
</script>
	
<script src="/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>
<script src="../SweetAlert2/dist/sweetalert2.all.min.js"></script>
<script>
function deleteItem(id) {
    Swal.fire({
        title: 'คุณต้องการลบข้อมูลนี้ใช่ไหม?',
        text: "หลังจากลบแล้ว จะไม่สามารถกู้คืนได้",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'ใช่, ยืนยันลบ',
        cancelButtonText: 'ยกเลิก'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = '/Bansuan/Del_law_order.php?id=' + id;
        }
    });
}
</script>
