<?php
include '../Condb.php';
include '../users.inc.php';
include '../right_user.php';
include '../Head_image.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// เมื่อมีการใช้ ตัวแปร Session จะต้องเปิดก่อน
//session_start();  // เปิดตัวแปร session
/*if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
*/

$sql_all = "SELECT " .
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE station=:station AND ps_cl_gen_detail='1') AS T1, ".   //พระบรมวงศานุวงศ์
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE station=:station AND ps_cl_gen_detail='2') AS T2, " .  //องคมนตรี
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE station=:station AND ps_cl_gen_detail='3') AS T3, " .  //พระสงฆ์ชั้นผู้ใหญ่
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE station=:station AND ps_cl_gen_detail='4') AS T4, " .  //ทูตานุทูต
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE station=:station AND ps_cl_gen_detail='5') AS T5, " .  //กงสุล
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE station=:station AND ps_cl_gen_detail='6') AS T6, " .  //หัวหน้าองค์กรของต่างประเทศ
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE station=:station AND ps_cl_gen_detail='7') AS T7, " .  //หัวหน้าส่วนราชการ
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE station=:station AND ps_cl_gen_detail='8') AS T8, ".   //หัวหน้าหน่วยงานรัฐวิสาหกิจ
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE station=:station AND ps_cl_gen_detail='9') AS T9, " .  //สมาชิกวุฒิสภา ส.ว.
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE station=:station AND ps_cl_gen_detail='10') AS T10, " .  //ส.ส.
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE station=:station AND ps_cl_gen_detail='11') AS T11, " .  //ส.อบจ.
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE station=:station AND ps_cl_gen_detail='12') AS T12, " .  //ส.ท.
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE station=:station AND ps_cl_gen_detail='13') AS T13, " .  //ส.อบต.
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE station=:station AND ps_cl_gen_detail='14') AS T14, " .  //ส.ก.
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE station=:station AND ps_cl_gen_detail='15') AS T15, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE station=:station AND ps_cl_gen_detail='16') AS T16, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE station=:station AND ps_cl_gen_detail='17') AS T17, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE station=:station AND ps_cl_gen_detail='18') AS T18, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE station=:station AND ps_cl_gen_detail='19') AS T19, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE station=:station AND ps_cl_gen_detail='20') AS T20, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE station=:station AND ps_cl_gen_detail='21') AS T21, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE station=:station AND ps_cl_gen_detail='22') AS T22, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE station=:station AND ps_cl_gen_detail='23') AS T23, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE station=:station AND ps_cl_gen_detail='24') AS T24, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE station=:station AND ps_cl_gen_detail='25') AS T25, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE station=:station AND ps_cl_gen_detail='26') AS T26, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE station=:station AND ps_cl_gen_detail='27') AS T27, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE station=:station AND ps_cl_gen_detail='28') AS T28, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE station=:station AND ps_cl_gen_detail='29') AS T29, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE station=:station AND ps_cl_gen_detail='30') AS T30, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE station=:station AND ps_cl_gen_detail='31') AS T31, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE station=:station AND ps_cl_gen_detail='32') AS T32 " .
            "";
    // Prepare the statement
    $stmt = $pdo->prepare($sql_all);

    // Bind the station value to the parameter
    $stmt->bindParam(':station', $station);

try {
    // Execute the query
    $stmt->execute();

    // Fetch the result as an associative array
    $row_all = $stmt->fetch(PDO::FETCH_ASSOC);

    // You can access the results using the column aliases (T1, T2, T31, T32)
/*    $T1 = $row_all['T1'];
    $T32 = $row_all['T32'];*/

} catch (PDOException $e) {
    // Handle any errors that may occur during the database operation
    echo "Error: " . $e->getMessage();
}

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
        <base target="_top">
        <title>ระบบ WatchmanDB</title>
		<link rel="stylesheet" href="../bootstrap/css/bootstrap.rtl.min.css">
		<link rel="stylesheet" href="../bootstrap-3.4.1-dist/css/bootstrap.min.css">
  		<script src="../jQuery/jquery-3.6.1.min.js"></script>
 		<script src="../bootstrap-3.4.1-dist/js/bootstrap.min.js"></script>
        <style>
            body {
              font-family: 'Courier New', Courier, monospace;
              font-size: 16px;
            }
        </style>
</head>

<body>

<div>
  <?php
    include('../users_info.php');
    ?>	
</div>

<div class=" h3 text-center  alert alert-success mb-2 mt-2 " role="alert" >บัญชีข้อมูลบุคคลทั่วไป (แบบ ศขส.) 32 ประเภท <?= $name_station ?></div>
    <div style="flex: auto">
        &nbsp;&nbsp;&nbsp;<a href="/Bansuan/Show_Gen_person.php?rnd=<?= rand(); ?>" class="btn btn-primary btn-lg mb-4" >กลับ</a>&nbsp;
    </div>
    
<div class="container">
<table width="95%" border="1" cellspacing="1" cellpadding="1" class="table-striped table-hover table-bordered">
  <tbody>
    <tr>
      <td height="40" width="54%" bgcolor="#1D04F3" style="text-align: center"><strong>รายการ</strong></td>
      <td height="40" width="19%" bgcolor="#1D04F3" style="text-align: center" ><strong>จำนวน</strong></td>
      <td height="40" width="27%" bgcolor="#1D04F3" style="text-align: center"><strong>หมายเหตุ</strong></td>
      
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;1. พระบรมวงศานุวงศ์</td>
      <td style="text-align: center"><b>
          <?php          
		  $t1Value = $row_all['T1'];
			if ($t1Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $t1Value . '</b>';
			} else {
				echo $t1Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;2. องคมนตรี</td>
      <td style="text-align: center"><b>
          <?php
		  $t2Value = $row_all['T2'];
			if ($t2Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $t2Value . '</b>';
			} else {
				echo $t2Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;3. พระสงฆ์ชั้นผู้ใหญ่</td>
      <td style="text-align: center"><b>
          <?php
		  $t3Value = $row_all['T3'];
			if ($t3Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $t3Value . '</b>';
			} else {
				echo $t3Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" colspan="3" style="font-size: 18px; text-align: left">&nbsp;4. คณะผู้แทนจากต่างประเทศ</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;4.1 ทูตานุทูต</td>
      <td style="text-align: center"><b>
          <?php
		  $t4Value = $row_all['T4'];
			if ($t4Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $t4Value . '</b>';
			} else {
				echo $t4Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;4.2 กงสุล</td>
      <td style="text-align: center"><b>
          <?php
		  $t5Value = $row_all['T5'];
			if ($t5Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $t5Value . '</b>';
			} else {
				echo $t5Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;4.3 หัวหน้าองค์กรของต่างประเทศ</td>
      <td style="text-align: center"><b>
          <?php
		  $t6Value = $row_all['T6'];
			if ($t6Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $t6Value . '</b>';
			} else {
				echo $t6Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;5. หัวหน้าส่วนราชการ</td>
      <td style="text-align: center"><b>
          <?php
		  $t7Value = $row_all['T7'];
			if ($t7Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $t7Value . '</b>';
			} else {
				echo $t7Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;6. หัวหน้าหน่วยงานรัฐวิสาหกิจ</td>
      <td style="text-align: center"><b>
          <?php
		  $t8Value = $row_all['T8'];
			if ($t8Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $t8Value . '</b>';
			} else {
				echo $t8Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" colspan="3" style="font-size: 18px; text-align: left">&nbsp;7. นักการเมืองทุกระดับ</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;7.1 สมาชิกวุฒิสภา (ส.ว.)</td>
      <td style="text-align: center"><b>
          <?php
		  $t9Value = $row_all['T9'];
			if ($t9Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $t9Value . '</b>';
			} else {
				echo $t9Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;7.2 สมาชิกสภาผู้แทนราษฎร (ส.ส.)</td>
      <td style="text-align: center"><b>
          <?php
		  $t10Value = $row_all['T10'];
			if ($t10Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $t10Value . '</b>';
			} else {
				echo $t10Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;7.3 สมาชิกองค์การบริหารส่วนจังหวัด (ส.อบจ.)</td>
      <td style="text-align: center"><b>
          <?php
		  $t11Value = $row_all['T11'];
			if ($t11Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $t11Value . '</b>';
			} else {
				echo $t11Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;7.4 สมาชิกสภาเทศบาล (ส.ท.)</td>
      <td style="text-align: center"><b>
          <?php           
          $t12Value = $row_all['T12'];
			if ($t12Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $t12Value . '</b>';
			} else {
				echo $t12Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;7.5 สมาชิกองค์การบริหารส่วนตำบล (ส.อบต.)</td>
      <td style="text-align: center"><b>
          <?php
		  $t13Value = $row_all['T13'];
			if ($t13Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $t13Value . '</b>';
			} else {
				echo $t13Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;7.6 สมาชิกสภากรุงเทพมหานคร (ส.ก.)</td>
      <td style="text-align: center"><b>
          <?php
		  $t14Value = $row_all['T14'];
			if ($t14Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $t14Value . '</b>';
			} else {
				echo $t14Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;7.7 สมาชิกสภาเขตกรุงเทพมหานคร (ส.ข.)</td>
      <td style="text-align: center"><b>
          <?php
		  $t15Value = $row_all['T15'];
			if ($t15Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $t15Value . '</b>';
			} else {
				echo $t15Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;7.8 อื่น ๆ</td>
      <td style="text-align: center"><b>
          <?php
		  $t16Value = $row_all['T16'];
			if ($t16Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $t16Value . '</b>';
			} else {
				echo $t16Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" colspan="3" style="font-size: 18px; text-align: left">&nbsp;8. หัวหน้า/ประธาน/นายก/อุปนายก</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;8.1 สมาคม</td>
      <td style="text-align: center"><b>
          <?php
		  $t17Value = $row_all['T17'];
			if ($t17Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $t17Value . '</b>';
			} else {
				echo $t17Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;8.2 มูลนิธิ</td>
      <td style="text-align: center"><b>
          <?php
		  $t18Value = $row_all['T18'];
			if ($t18Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $t18Value . '</b>';
			} else {
				echo $t18Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;8.3 ชุมชน</td>
      <td style="text-align: center"><b>
          <?php
		  $t19Value = $row_all['T19'];
			if ($t19Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $t19Value . '</b>';
			} else {
				echo $t19Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;8.4 องค์กร, อาสาสมัคร, ศูนย์, สภา, สมาพันธ์</td>
      <td style="text-align: center"><b>
          <?php
		  $t20Value = $row_all['T20'];
			if ($t20Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $t20Value . '</b>';
			} else {
				echo $t20Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;8.5 อื่น ๆ เช่น กลุ่ม, ชมรม</td>
      <td style="text-align: center"><b>
          <?php
		  $t21Value = $row_all['T21'];
			if ($t21Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $t21Value . '</b>';
			} else {
				echo $t21Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" colspan="3" style="font-size: 18px; text-align: left">&nbsp;9. นักธุรกิจ</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;9.1 เจ้าของ</td>
      <td style="text-align: center"><b>
          <?php
		  $t22Value = $row_all['T22'];
			if ($t22Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $t22Value . '</b>';
			} else {
				echo $t22Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;9.2 ผู้จัดการ</td>
      <td style="text-align: center"><b>
          <?php
		  $t23Value = $row_all['T23'];
			if ($t23Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $t23Value . '</b>';
			} else {
				echo $t23Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;9.3 หุ้นส่วนผู้จัดการ, กรรมการ, ประธานกรรมการ</td>
      <td style="text-align: center"><b>
          <?php
		  $t24Value = $row_all['T24'];
			if ($t24Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $t24Value . '</b>';
			} else {
				echo $t24Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;10. นักหนังสือพิมพ์, บรรณาธิการ, ผู้สื่อข่าว, นักจัดรายการ, นักเขียน</td>
      <td style="text-align: center"><b>
          <?php
		  $t25Value = $row_all['T25'];
			if ($t25Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $t25Value . '</b>';
			} else {
				echo $t25Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;11. กำนัน, สารวัตรกำนัน, แพทย์ประจำตำบล</td>
      <td style="text-align: center"><b>
          <?php
		  $t26Value = $row_all['T26'];
			if ($t26Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $t26Value . '</b>';
			} else {
				echo $t26Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;12. ผู้ใหญ่บ้าน, ผู้ช่วยผู้ใหญ่บ้าน</td>
      <td style="text-align: center"><b>
          <?php
		  $t27Value = $row_all['T27'];
			if ($t27Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $t27Value . '</b>';
			} else {
				echo $t27Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;13. บุคคลที่ประกอบอาชีพเกี่ยวกับการแลกเปลี่ยนเงินตรา, เล่นหุ้น</td>
      <td style="text-align: center"><b>
          <?php
		  $t28Value = $row_all['T28'];
			if ($t28Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $t28Value . '</b>';
			} else {
				echo $t28Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" colspan="3" style="font-size: 18px; text-align: left">&nbsp;14. ผู้ประกอบอาชีพเกี่ยวกับรถรับจ้าง (รถโดยสารประจำทาง, รถแท็กซี่, รถตู้, รถสองแถว, รถสามล้อ, รถจักรยานยนต์, รถบรรทุก)</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;14.1 เจ้าของอู่</td>
      <td style="text-align: center"><b>
          <?php
		  $t29Value = $row_all['T29'];
			if ($t29Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $t29Value . '</b>';
			} else {
				echo $t29Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;14.2 หัวหน้าคิว</td>
      <td style="text-align: center"><b>
          <?php
		  $t30Value = $row_all['T30'];
			if ($t30Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $t30Value . '</b>';
			} else {
				echo $t30Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;14.3 ผู้ขับขี่</td>
      <td style="text-align: center"><b>
          <?php
		  $t31Value = $row_all['T31'];
			if ($t31Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $t31Value . '</b>';
			} else {
				echo $t31Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;15. อื่น ๆ เช่น ข้าราชการชั้นผู้ใหญ่ที่เกษียณอายุราชการ, อดีตนักการเมือง, ที่ปรึกษานักการเมือง, นักเคลื่อนไหวทางการเมือง เป็นต้น</td>
      <td style="text-align: center"><b>
          <?php
		  $t32Value = $row_all['T32'];
			if ($t32Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $t32Value . '</b>';
			} else {
				echo $t32Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
  </tbody>
</table>
    <br>
    <div style="flex: auto" align="right">
        &nbsp;&nbsp;&nbsp;<a href="/Bansuan/Show_Gen_person.php?rnd=<?= rand(); ?>" class="btn btn-primary btn-lg mb-4" >กลับ</a>&nbsp;&nbsp;
    </div>
    <hr>
</div>
    
</body>
</html>