<?php
include '../Condb.php';
include '../users.inc.php';
include '../right_user.php';
include '../Head_image.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// เมื่อมีการใช้ ตัวแปร Session จะต้องเปิดก่อน
//session_start();  // เปิดตัวแปร session
/*if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
*/

$sql_all = "SELECT " .
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='1' AND station=:station) AS T1, ".   //บุคคลที่มีหมายจับ
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='1' AND station=:station) AS T1, ". 
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='2' AND station=:station) AS T2, " .  //บุคคลพ้นโทษ
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='3' AND station=:station) AS T3, " .  //บุคคลพักการลงโทษ
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='4' AND station=:station) AS T4, " .  //มือปืนรับจ้าง
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='5' AND station=:station) AS T5, " .  //ผู้มีอิทธิพล
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='6' AND station=:station) AS T6, " .  //ฐานผลิต
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='7' AND station=:station) AS T7, " .  //จำหน่าย
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='8' AND station=:station) AS T8, ".   //ครอบครอง
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='9' AND station=:station) AS T9, " .  //เสพ
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='10' AND station=:station) AS T10, " .  //ฐานผลิตวัตถุออกฤทธิ์
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='11' AND station=:station) AS T11, " .  //จำหน่าย
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='12' AND station=:station) AS T12, " .  //ครอบครอง
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='13' AND station=:station) AS T13, " .  //เสพ
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='14' AND station=:station) AS T14, " .  //ทำให้เกิดอันตรายแก่ รถไฟ, รถราง
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='15' AND station=:station) AS T15, " .  // ทำให้เกิดระเบิด, วางเพลิง
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='16' AND station=:station) AS T16, " .  //วิชาชีพ
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='17' AND station=:station) AS T17, " .  //ฯลฯ
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='18' AND station=:station) AS T18, " .  //หน่วงเหนี่ยวกักขัง
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='19' AND station=:station) AS T19, " .  //จับตัวเรียกค่าไถ่
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='20' AND station=:station) AS T20, " .  //ฯลฯ
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='21' AND station=:station) AS T21, " .  //ปลอมเอกสาร
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='22' AND station=:station) AS T22, " .  //ปลอมเงินตรา
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='23' AND station=:station) AS T23, " .  //ข่มขืน
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='24' AND station=:station) AS T24, " .  //อนาจาร
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='25' AND station=:station) AS T25, " .  //รถยนต์
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='26' AND station=:station) AS T26, " .  //รถจักรยานยนต์
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='27' AND station=:station) AS T27, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='28' AND station=:station) AS T28, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='29' AND station=:station) AS T29, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='30' AND station=:station) AS T30, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='31' AND station=:station) AS T31, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='32' AND station=:station) AS T32, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='33' AND station=:station) AS T33, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='34' AND station=:station) AS T34, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='35' AND station=:station) AS T35, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='36' AND station=:station) AS T36, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='37' AND station=:station) AS T37, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='38' AND station=:station) AS T38, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='39' AND station=:station) AS T39, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='40' AND station=:station) AS T40, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='41' AND station=:station) AS T41, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='42' AND station=:station) AS T42, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='43' AND station=:station) AS T43, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='44' AND station=:station) AS T44, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='45' AND station=:station) AS T45, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='46' AND station=:station) AS T46, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='47' AND station=:station) AS T47, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='48' AND station=:station) AS T48, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='49' AND station=:station) AS T49, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='50' AND station=:station) AS T50, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='51' AND station=:station) AS T51, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='52' AND station=:station) AS T52, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='53' AND station=:station) AS T53, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='54' AND station=:station) AS T54, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='55' AND station=:station) AS T55, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='56' AND station=:station) AS T56, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='57' AND station=:station) AS T57, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='58' AND station=:station) AS T58, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='59' AND station=:station) AS T59, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='60' AND station=:station) AS T60, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='61' AND station=:station) AS T61, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='62' AND station=:station) AS T62, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='63' AND station=:station) AS T63, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='64' AND station=:station) AS T64, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='65' AND station=:station) AS T65, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='66' AND station=:station) AS T66, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='67' AND station=:station) AS T67, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='68' AND station=:station) AS T68, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='69' AND station=:station) AS T69, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='70' AND station=:station) AS T70, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='71' AND station=:station) AS T71, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='72' AND station=:station) AS T72, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='73' AND station=:station) AS T73, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='74' AND station=:station) AS T74, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='75' AND station=:station) AS T75, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='76' AND station=:station) AS T76, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='77' AND station=:station) AS T77, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='78' AND station=:station) AS T78, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='79' AND station=:station) AS T79, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='80' AND station=:station) AS T80, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='81' AND station=:station) AS T81, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='82' AND station=:station) AS T82, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='83' AND station=:station) AS T83, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='84' AND station=:station) AS T84, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='85' AND station=:station) AS T85, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='86' AND station=:station) AS T86, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='87' AND station=:station) AS T87, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='88' AND station=:station) AS T88, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='89' AND station=:station) AS T89, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='90' AND station=:station) AS T90, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='91' AND station=:station) AS T91, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='92' AND station=:station) AS T92, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='93' AND station=:station) AS T93, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='94' AND station=:station) AS T94, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='95' AND station=:station) AS T95, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='96' AND station=:station) AS T96, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='97' AND station=:station) AS T97, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='98' AND station=:station) AS T98, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='99' AND station=:station) AS T99, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='100' AND station=:station) AS T100, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='101' AND station=:station) AS T101, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='102' AND station=:station) AS T102, " .  //
            "(SELECT COUNT(*) FROM wm_tb_personal WHERE ps_cl_CrimeType2='103' AND station=:station) AS T103 " .
            "";

    // Prepare the statement
    $stmt = $pdo->prepare($sql_all);

    // Bind the station value to the parameter
    $stmt->bindParam(':station', $station);

try {
    // Execute the query
    $stmt->execute();

    // Fetch the result as an associative array
    $row_all = $stmt->fetch(PDO::FETCH_ASSOC);

    // You can access the results using the column aliases (T1, T2, T31, T32)
/*    $T1 = $row_all['T1'];
    $T32 = $row_all['T32'];*/

} catch (PDOException $e) {
    // Handle any errors that may occur during the database operation
    echo "Error: " . $e->getMessage();
}

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
        <base target="_top">
        <title>ระบบ WatchmanDB</title>
		<link rel="stylesheet" href="../bootstrap/css/bootstrap.rtl.min.css">
		<link rel="stylesheet" href="../bootstrap-3.4.1-dist/css/bootstrap.min.css">
  		<script src="../jQuery/jquery-3.6.1.min.js"></script>
 		<script src="../bootstrap-3.4.1-dist/js/bootstrap.min.js"></script>
        <style>
            body {
              font-family: 'Courier New', Courier, monospace;
              font-size: 16px;
            }
        </style>
</head>

<body>

<div>
  <?php
    include('../users_info.php');
    ?>	
</div>

<div class=" h3 text-center  alert alert-danger mb-2 mt-2 " role="alert" >บัญชีข้อมูลบุคคลเกี่ยวข้องกับอาชญากรรม (แบบ ศขส.) 23 ประเภท <?= $name_station ?></div>
    <div style="flex: auto">
        &nbsp;<a href="/Bansuan/Show_crimes_person.php?rnd=<?= rand(); ?>" class="btn btn-primary btn-lg mb-4" >ย้อนกลับ</a>&nbsp;
    </div>
    
<div class="container">
<table width="95%" border="1" cellspacing="1" cellpadding="1" class="table-striped table-hover table-bordered">
  <tbody>
    <tr>
      <td height="40" width="68%" bgcolor="#1D04F3" style="text-align: center"><strong>รายการ</strong></td>
      <td height="40" width="11%" bgcolor="#1D04F3" style="text-align: center" ><strong>จำนวน</strong></td>
      <td height="40" width="21%" bgcolor="#1D04F3" style="text-align: center"><strong>หมายเหตุ</strong></td>
      
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;1. บุคคลที่มีหมายจับ</td>
      <td style="text-align: center"><b>
          <?php
		  $T1Value = $row_all['T1'];
			if ($T1Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T1Value . '</b>';
			} else {
				echo $T1Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;2. บุคคลพ้นโทษ</td>
      <td style="text-align: center"><b>
          <?php
		  $T2Value = $row_all['T2'];
			if ($T2Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T2Value . '</b>';
			} else {
				echo $T2Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;3. บุคคลพักการลงโทษ</td>
      <td style="text-align: center"><b>
          <?php
		  $T3Value = $row_all['T3'];
			if ($T3Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T3Value . '</b>';
			} else {
				echo $T3Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;4. บุคคลที่เกี่ยวข้องหรือเคยกระทำความผิดเกี่ยวกับมือปืนรับจ้าง</td>
      <td style="text-align: center"><b>
          <?php
		  $T4Value = $row_all['T4'];
			if ($T4Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T4Value . '</b>';
			} else {
				echo $T4Value;
			}
          ?></b></td>
      <td</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;5. บุคคลที่เกี่ยวข้องหรือเคยกระทำความผิดเกี่ยวกับผู้มีอิทธิพล</td>
      <td style="text-align: center"><b>
          <?php
		  $T5Value = $row_all['T5'];
			if ($T5Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T5Value . '</b>';
			} else {
				echo $T5Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" colspan="3" style="font-size: 18px; text-align: left">&nbsp;6. บุคคลที่เกี่ยวข้องหรือเคยกระทำความผิดเกี่ยวกับยาเสพติดให้โทษ (ตาม พ.ร.บ.ยาเสพติดให้โทษฯ)</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;6.1 ฐานผลิต</td>
      <td style="text-align: center"><b>
          <?php
		  $T6Value = $row_all['T6'];
			if ($T6Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T6Value . '</b>';
			} else {
				echo $T6Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;6.2 ฐานจำหน่าย</td>
      <td style="text-align: center"><b>
          <?php
		  $T7Value = $row_all['T7'];
			if ($T7Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T7Value . '</b>';
			} else {
				echo $T7Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;6.3 ฐานครอบครอง</td>
      <td style="text-align: center"><b>
          <?php
		  $T8Value = $row_all['T8'];
			if ($T8Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T8Value . '</b>';
			} else {
				echo $T8Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;6.4 ฐานเสพ</td>
      <td style="text-align: center"><b>
          <?php
		  $T9Value = $row_all['T9'];
			if ($T9Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T9Value . '</b>';
			} else {
				echo $T9Value;
			}
          ?></b></td>
      <td></td>
      </tr>
    <tr>
      <td height="40" colspan="3" style="font-size: 18px; text-align: left">&nbsp;7. บุคคลที่เกี่ยวข้องหรือเคยกระทำความผิดเกี่ยวกับยาเสพติดให้โทษ (ตาม พ.ร.บ.วัตถุออกฤทธิ์ฯ, สารระเหยฯ, ควบคุมโภคภัณฑ์)</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;7.1 ฐานผลิต</td>
      <td style="text-align: center"><b>
          <?php
		  $T10Value = $row_all['T10'];
			if ($T10Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T10Value . '</b>';
			} else {
				echo $T10Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;7.2 ฐานจำหน่าย</td>
      <td style="text-align: center"><b>
          <?php
		  $T11Value = $row_all['T11'];
			if ($T11Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T11Value . '</b>';
			} else {
				echo $T11Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;7.3 ฐานครอบครอง</td>
      <td style="text-align: center"><b>
          <?php
		  $T12Value = $row_all['T12'];
			if ($T12Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T12Value . '</b>';
			} else {
				echo $T12Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;7.4 ฐานเสพ</td>
      <td style="text-align: center"><b>
          <?php
		  $T13Value = $row_all['T13'];
			if ($T13Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T13Value . '</b>';
			} else {
				echo $T13Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" colspan="3" style="font-size: 18px; text-align: left">&nbsp;8. บุคคลที่เกี่ยวข้องหรือเคยกระทำความผิดเกี่ยวกับการก่อให้เกิดอันตรายต่อประชาชน (ป.อาญา ลักษณะ 6)</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;8.1 ทำให้เกิดอันตรายแก่ รถไฟ, รถราง, โรงเรียน, เรือ, แพ, ท่าอากาศยาน ฯลฯ</td>
      <td style="text-align: center"><b>
          <?php
		  $T14Value = $row_all['T14'];
			if ($T14Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T14Value . '</b>';
			} else {
				echo $T14Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;8.2 ทำให้เกิดระเบิด, วางเพลิง</td>
      <td style="text-align: center"><b>
          <?php
		  $T15Value = $row_all['T15'];
			if ($T15Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T15Value . '</b>';
			} else {
				echo $T15Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;8.3 วิชาชีพ</td>
      <td style="text-align: center"><b>
          <?php
		  $T16Value = $row_all['T16'];
			if ($T16Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T16Value . '</b>';
			} else {
				echo $T16Value;
			}
          ?></b></td>
      <td height="40" style="font-size: 18px">&nbsp;</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;8.4 ฯลฯ</td>
      <td style="text-align: center"><b>
          <?php
		  $T17Value = $row_all['T17'];
			if ($T17Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T17Value . '</b>';
			} else {
				echo $T17Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" colspan="3" style="font-size: 18px; text-align: left">&nbsp;9. บุคคลที่เกี่ยวข้องหรือเคยกระทำความผิดเกี่ยวกับเสรีภาพ (ป.อาญา ลักษณะ 11 หมวด 1)</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;9.1 หน่วงเหนี่ยวกักขัง</td>
      <td style="text-align: center"><b>
          <?php
		  $T18Value = $row_all['T18'];
			if ($T18Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T18Value . '</b>';
			} else {
				echo $T18Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;9.2 จับตัวเรียกค่าไถ่</td>
      <td style="text-align: center"><b>
          <?php
		  $T19Value = $row_all['T19'];
			if ($T19Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T19Value . '</b>';
			} else {
				echo $T19Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;9.3 ฯลฯ</td>
      <td style="text-align: center"><b>
          <?php
		  $T20Value = $row_all['T20'];
			if ($T20Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T20Value . '</b>';
			} else {
				echo $T20Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" colspan="3" style="font-size: 18px; text-align: left">&nbsp;10. บุคคลที่เกี่ยวข้องหรือเคยกระทำความผิดเกี่ยวกับการปลอมแปลง</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;10.1 ปลอมเอกสาร</td>
      <td style="text-align: center"><b>
          <?php
		  $T21Value = $row_all['T21'];
			if ($T21Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T21Value . '</b>';
			} else {
				echo $T21Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;10.2 ปลอมเงินตรา</td>
      <td style="text-align: center"><b>
          <?php
		  $T22Value = $row_all['T22'];
			if ($T22Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T22Value . '</b>';
			} else {
				echo $T22Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" colspan="3" style="font-size: 18px; text-align: left">&nbsp;11. บุคคลที่เกี่ยวข้องหรือเคยกระทำความผิดเกี่ยวกับเพศ</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;11.1 ข่มขืน</td>
      <td style="text-align: center"><b>
          <?php
		  $T23Value = $row_all['T23'];
			if ($T23Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T23Value . '</b>';
			} else {
				echo $T23Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;11.2 อนาจาร</td>
      <td style="text-align: center"><b>
          <?php
		  $T24Value = $row_all['T24'];
			if ($T24Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T24Value . '</b>';
			} else {
				echo $T24Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" colspan="3" style="font-size: 18px; text-align: left">&nbsp;12. บุคคลที่เกี่ยวข้องหรือเคยกระทำความผิดเกี่ยวกับ การลักทรัพย์</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;12.1 รถยนต์</td>
      <td style="text-align: center"><b>
          <?php
		  $T25Value = $row_all['T25'];
			if ($T25Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T25Value . '</b>';
			} else {
				echo $T25Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;12.2 รถจักรยานยนต์</td>
      <td style="text-align: center"><b>
          <?php
		  $T26Value = $row_all['T26'];
			if ($T26Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T26Value . '</b>';
			} else {
				echo $T26Value;
			}
          ?></b></td>
      <td height="40" style="font-size: 18px">&nbsp;</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;12.3 ทรัพย์สินในรถยนต์ (ทุบกระจก, งัดรถ)</td>
      <td style="text-align: center"><b>
          <?php
		  $T27Value = $row_all['T27'];
			if ($T27Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T27Value . '</b>';
			} else {
				echo $T27Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;12.4 มินิมาร์ท/ซุปเปอร์มาร์เก็ต</td>
      <td style="text-align: center"><b>
          <?php
		  $T28Value = $row_all['T28'];
			if ($T28Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T28Value . '</b>';
			} else {
				echo $T28Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;12.5 ตู้บริการเงินด่วน (A.T.M.)</td>
      <td style="text-align: center"><b>
          <?php
		  $T29Value = $row_all['T29'];
			if ($T29Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T29Value . '</b>';
			} else {
				echo $T29Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;12.6 สถานที่ราชการ</td>
      <td style="text-align: center"><b>
          <?php
		  $T30Value = $row_all['T30'];
			if ($T30Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T30Value . '</b>';
			} else {
				echo $T30Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;12.7 ตู้โทรศัพท์สาธารณะ</td>
      <td style="text-align: center"><b>
          <?php
		  $T31Value = $row_all['T31'];
			if ($T31Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T31Value . '</b>';
			} else {
				echo $T31Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;12.8 ในเคหะสถาน (ตีนแมว, กุญแจเฝ้าบ้าน)</td>
      <td style="text-align: center"><b>
          <?php
		  $T32Value = $row_all['T32'];
			if ($T32Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T32Value . '</b>';
			} else {
				echo $T32Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;12.9 เครื่องมือเกษตร</td>
      <td style="text-align: center"><b>
          <?php
		  $T33Value = $row_all['T33'];
			if ($T33Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T33Value . '</b>';
			} else {
				echo $T33Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;12.10 ห้างสรรพสินค้า</td>
      <td style="text-align: center"><b>
          <?php
		  $T34Value = $row_all['T34'];
			if ($T34Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T34Value . '</b>';
			} else {
				echo $T34Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;12.11 สำนักงาน</td>
      <td style="text-align: center"><b>
          <?php
		  $T35Value = $row_all['T35'];
			if ($T35Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T35Value . '</b>';
			} else {
				echo $T35Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;12.12 สายไฟฟ้า, โทรศัพท์</td>
      <td style="text-align: center"><b>
          <?php
		  $T36Value = $row_all['T36'];
			if ($T36Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T36Value . '</b>';
			} else {
				echo $T36Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;12.13 เป็นคนรับใช้, ช่าง</td>
      <td style="text-align: center"><b>
          <?php
		  $T37Value = $row_all['T37'];
			if ($T37Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T37Value . '</b>';
			} else {
				echo $T37Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;12.14 วัตถุโบราณ</td>
      <td style="text-align: center"><b>
          <?php
		  $T38Value = $row_all['T38'];
			if ($T38Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T38Value . '</b>';
			} else {
				echo $T38Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;12.15 เครื่องมือแพทย์</td>
      <td style="text-align: center"><b>
          <?php
		  $T39Value = $row_all['T39'];
			if ($T39Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T39Value . '</b>';
			} else {
				echo $T39Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;12.16 โดยใช้อุบาย</td>
      <td style="text-align: center"><b>
          <?php
		  $T40Value = $row_all['T40'];
			if ($T40Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T40Value . '</b>';
			} else {
				echo $T40Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;12.17 อื่น ๆ</td>
      <td style="text-align: center"><b>
          <?php
		  $T41Value = $row_all['T41'];
			if ($T41Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T41Value . '</b>';
			} else {
				echo $T41Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" colspan="3" style="font-size: 18px; text-align: left">&nbsp;13. บุคคลที่เกี่ยวข้องหรือเคยกระทำความผิดเกี่ยวกับการวิ่งราวทรัพย์</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;13.1 ใช้ยานพาหนะ</td>
      <td style="text-align: center"><b>
          <?php
		  $T42Value = $row_all['T42'];
			if ($T42Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T42Value . '</b>';
			} else {
				echo $T42Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;13.2 ไม่ใช้ยานพาหนะ</td>
      <td style="text-align: center"><b>
          <?php
		  $T43Value = $row_all['T43'];
			if ($T43Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T43Value . '</b>';
			} else {
				echo $T43Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" colspan="3" style="font-size: 18px; text-align: left">&nbsp;14. บุคคลที่เกี่ยวข้องหรือเคยกระทำความผิดเกี่ยวกับ การชิงทรัพย์</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;14.1 ธนาคาร</td>
      <td style="text-align: center"><b>
          <?php
		  $T44Value = $row_all['T44'];
			if ($T44Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T44Value . '</b>';
			} else {
				echo $T44Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;14.2 ร้านทองรูปพรรณ, ร้านอัญมณี</td>
      <td style="text-align: center"><b>
          <?php
		  $T45Value = $row_all['T45'];
			if ($T45Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T45Value . '</b>';
			} else {
				echo $T45Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;14.3 ปั๊มน้ำมัน, ก๊าซ</td>
      <td style="text-align: center"><b>
          <?php
		  $T46Value = $row_all['T46'];
			if ($T46Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T46Value . '</b>';
			} else {
				echo $T46Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;14.4 มินิมาร์ท/ซุปเปอร์มาเก็ต</td>
      <td style="text-align: center"><b>
          <?php
		  $T47Value = $row_all['T47'];
			if ($T47Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T47Value . '</b>';
			} else {
				echo $T47Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;14.5 คลินิกแพทย์</td>
      <td style="text-align: center"><b>
          <?php
		  $T48Value = $row_all['T48'];
			if ($T48Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T48Value . '</b>';
			} else {
				echo $T48Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;14.6 ร้านเสริมสวย</td>
      <td style="text-align: center"><b>
          <?php
		  $T49Value = $row_all['T49'];
			if ($T49Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T49Value . '</b>';
			} else {
				echo $T49Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;14.7 รถยนต์</td>
      <td style="text-align: center"><b>
          <?php
		  $T50Value = $row_all['T50'];
			if ($T50Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T50Value . '</b>';
			} else {
				echo $T50Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;14.8 รถจักรยานยนต์</td>
      <td style="text-align: center"><b>
          <?php
		  $T51Value = $row_all['T51'];
			if ($T51Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T51Value . '</b>';
			} else {
				echo $T51Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;14.9 รถยนต์แท็กซี่รับจ้าง</td>
      <td style="text-align: center"><b>
          <?php
		  $T52Value = $row_all['T52'];
			if ($T52Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T52Value . '</b>';
			} else {
				echo $T52Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;14.10 บนรถโดยสารประจำทาง (รถทัวร์, รถ บขส., รถปรับอากาศ, รถสองแถว, รถตู้)</td>
      <td style="text-align: center"><b>
          <?php
		  $T53Value = $row_all['T53'];
			if ($T53Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T53Value . '</b>';
			} else {
				echo $T53Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;14.11 ที่พักอาศัย</td>
      <td style="text-align: center"><b>
          <?php
		  $T54Value = $row_all['T54'];
			if ($T54Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T54Value . '</b>';
			} else {
				echo $T54Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;14.12 ที่สาธารณะ</td>
      <td style="text-align: center"><b>
          <?php
		  $T55Value = $row_all['T55'];
			if ($T55Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T55Value . '</b>';
			} else {
				echo $T55Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;14.13 สามล้อเครื่อง, สามล้อถีบ</td>
      <td style="text-align: center"><b>
          <?php
		  $T56Value = $row_all['T56'];
			if ($T56Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T56Value . '</b>';
			} else {
				echo $T56Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;14.14 อื่น ๆ</td>
      <td style="text-align: center"><b>
          <?php
		  $T57Value = $row_all['T57'];
			if ($T57Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T57Value . '</b>';
			} else {
				echo $T57Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" colspan="3" style="font-size: 18px; text-align: left">&nbsp;15. บุคคลที่เกี่ยวข้องหรือเคยกระทำความผิดเกี่ยวกับการปล้นทรัพย์</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;15.1 ธนาคาร</td>
      <td style="text-align: center"><b>
          <?php
		  $T58Value = $row_all['T58'];
			if ($T58Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T58Value . '</b>';
			} else {
				echo $T58Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;15.2 ร้านทองรูปพรรณ, ร้านอัญมณี</td>
      <td style="text-align: center"><b>
          <?php
		  $T59Value = $row_all['T59'];
			if ($T59Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T59Value . '</b>';
			} else {
				echo $T59Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;15.3 ปั๊มน้ำมัน, ก๊าซ</td>
      <td style="text-align: center"><b>
          <?php
		  $T60Value = $row_all['T60'];
			if ($T60Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T60Value . '</b>';
			} else {
				echo $T60Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;15.4 มินิมาร์ท/ซุปเปอร์มาเก็ต</td>
      <td style="text-align: center"><b>
          <?php
		  $T61Value = $row_all['T61'];
			if ($T61Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T61Value . '</b>';
			} else {
				echo $T61Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;15.5 คลินิกแพทย์</td>
      <td style="text-align: center"><b>
          <?php
		  $T62Value = $row_all['T62'];
			if ($T62Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T62Value . '</b>';
			} else {
				echo $T62Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;15.6 ร้านเสริมสวย</td>
      <td style="text-align: center"><b>
          <?php
		  $T63Value = $row_all['T63'];
			if ($T63Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T63Value . '</b>';
			} else {
				echo $T63Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;15.7 รถยนต์</td>
      <td style="text-align: center"><b>
          <?php
		  $T64Value = $row_all['T64'];
			if ($T64Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T64Value . '</b>';
			} else {
				echo $T64Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;15.8 รถจักรยานยนต์</td>
      <td style="text-align: center"><b>
          <?php
		  $T65Value = $row_all['T65'];
			if ($T65Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T65Value . '</b>';
			} else {
				echo $T65Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;15.9 รถยนต์แท็กซี่รับจ้าง</td>
      <td style="text-align: center"><b>
          <?php
		  $T66Value = $row_all['T66'];
			if ($T66Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T66Value . '</b>';
			} else {
				echo $T66Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;15.10 บนรถโดยสารประจำทาง (รถทัวร์, รถ บขส., รถปรับอากาศ, รถสองแถว, รถตู้)</td>
      <td style="text-align: center"><b>
          <?php
		  $T67Value = $row_all['T67'];
			if ($T67Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T67Value . '</b>';
			} else {
				echo $T67Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;15.11 ที่พักอาศัย</td>
      <td style="text-align: center"><b>
          <?php
		  $T68Value = $row_all['T68'];
			if ($T68Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T68Value . '</b>';
			} else {
				echo $T68Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;15.12 ที่สาธารณะ</td>
      <td style="text-align: center"><b>
          <?php
		  $T69Value = $row_all['T69'];
			if ($T69Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T69Value . '</b>';
			} else {
				echo $T69Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;15.13 สามล้อเครื่อง, สามล้อถีบ</td>
      <td style="text-align: center"><b>
          <?php
		  $T70Value = $row_all['T70'];
			if ($T70Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T70Value . '</b>';
			} else {
				echo $T70Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;15.14 อื่น ๆ</td>
      <td style="text-align: center"><b>
          <?php
		  $T71Value = $row_all['T71'];
			if ($T71Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T71Value . '</b>';
			} else {
				echo $T71Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" colspan="3" style="font-size: 18px; text-align: left">&nbsp;16. บุคคลที่เกี่ยวข้องหรือเคยกระทำความผิดเกี่ยวกับ การฉ้อโกง</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;16.1 แก๊งตกทอง</td>
      <td style="text-align: center"><b>
          <?php
		  $T72Value = $row_all['T72'];
			if ($T72Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T72Value . '</b>';
			} else {
				echo $T72Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;16.2 แก๊งไพ่สามใบ</td>
      <td style="text-align: center"><b>
          <?php
		  $T73Value = $row_all['T73'];
			if ($T73Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T73Value . '</b>';
			} else {
				echo $T73Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;16.3 ส่งคนงานไปต่างประเทศ</td>
      <td style="text-align: center"><b>
          <?php
		  $T74Value = $row_all['T74'];
			if ($T74Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T74Value . '</b>';
			} else {
				echo $T74Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;16.4 อื่น ๆ เช่น แชร์ลูกโซ่, แชร์รากหญ้า, ออนไลน์</td>
      <td style="text-align: center"><b>
          <?php
		  $T75Value = $row_all['T75'];
			if ($T75Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T75Value . '</b>';
			} else {
				echo $T75Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" colspan="3" style="font-size: 18px; text-align: left">&nbsp;17. บุคคลที่เกี่ยวข้องหรือเคยกระทำความผิดเกี่ยวกับ การค้าประเวณี</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;17.1 เจ้าบ้าน</td>
      <td style="text-align: center"><b>
          <?php
		  $T76Value = $row_all['T76'];
			if ($T76Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T76Value . '</b>';
			} else {
				echo $T76Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;17.2 ผู้ดูแล, ควบคุม, นายหน้า, ธุระจัดหา</td>
      <td style="text-align: center"><b>
          <?php
		  $T77Value = $row_all['T77'];
			if ($T77Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T77Value . '</b>';
			} else {
				echo $T77Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;17.3 โสเภณี</td>
      <td style="text-align: center"><b>
          <?php
		  $T78Value = $row_all['T78'];
			if ($T78Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T78Value . '</b>';
			} else {
				echo $T78Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" colspan="3" style="font-size: 18px; text-align: left">&nbsp;18. บุคคลที่เกี่ยวข้องหรือเคยกระทำความผิดเกี่ยวกับ การพนัน</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;18.1 เจ้าของบ่อน (บาคาร่า, แปด-เก้า ฯลฯ)</td>
      <td style="text-align: center"><b>
          <?php
		  $T79Value = $row_all['T79'];
			if ($T79Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T79Value . '</b>';
			} else {
				echo $T79Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;18.2 เจ้ามือสลากกินรวบ, เดินโพย</td>
      <td style="text-align: center"><b>
          <?php
		  $T80Value = $row_all['T80'];
			if ($T80Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T80Value . '</b>';
			} else {
				echo $T80Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;18.3 โต๊ะม้าจำลอง</td>
      <td style="text-align: center"><b>
          <?php
		  $T81Value = $row_all['T81'];
			if ($T81Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T81Value . '</b>';
			} else {
				echo $T81Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;18.4 เครื่องจักกลไฟฟ้า</td>
      <td style="text-align: center"><b>
          <?php
		  $T82Value = $row_all['T82'];
			if ($T82Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T82Value . '</b>';
			} else {
				echo $T82Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;18.5 จับยี่กี</td>
      <td style="text-align: center"><b>
          <?php
		  $T83Value = $row_all['T83'];
			if ($T83Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T83Value . '</b>';
			} else {
				echo $T83Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;18.6 หวยปิงปอง</td>
      <td style="text-align: center"><b>
          <?php
		  $T84Value = $row_all['T84'];
			if ($T84Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T84Value . '</b>';
			} else {
				echo $T84Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;18.7 อื่น ๆ เช่น เจ้ามือรับพนันฟุตบอล, เจ้ามือหวยหุ้น</td>
      <td style="text-align: center"><b>
          <?php
		  $T85Value = $row_all['T85'];
			if ($T85Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T85Value . '</b>';
			} else {
				echo $T85Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" colspan="3" style="font-size: 18px; text-align: left">&nbsp;19. บุคคลที่เกี่ยวข้องหรือเคยกระทำความผิดเกี่ยวกับ การค้าอาวุธปืน</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;19.1 อาวุธปืนเถื่อน</td>
      <td style="text-align: center"><b>
          <?php
		  $T86Value = $row_all['T86'];
			if ($T86Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T86Value . '</b>';
			} else {
				echo $T86Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;19.2 อาวุธปืนสงคราม</td>
      <td style="text-align: center"><b>
          <?php
		  $T87Value = $row_all['T87'];
			if ($T87Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T87Value . '</b>';
			} else {
				echo $T87Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;19.3 วัตถุระเบิด</td>
      <td style="text-align: center"><b>
          <?php
		  $T88Value = $row_all['T88'];
			if ($T88Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T88Value . '</b>';
			} else {
				echo $T88Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" colspan="3" style="font-size: 18px; text-align: left">&nbsp;20. บุคคลที่เกี่ยวข้องหรือเคยกระทำความผิดเกี่ยวกับ การทำลายทรัพยากรธรรมชาติ</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;20.1 แร่ธาตุ</td>
      <td style="text-align: center"><b>
          <?php
		  $T89Value = $row_all['T89'];
			if ($T89Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T89Value . '</b>';
			} else {
				echo $T89Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;20.2 ป่าไม้</td>
      <td style="text-align: center"><b>
          <?php
		  $T90Value = $row_all['T90'];
			if ($T90Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T90Value . '</b>';
			} else {
				echo $T90Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;20.3 สัตว์สงวน</td>
      <td style="text-align: center"><b>
          <?php
		  $T91Value = $row_all['T91'];
			if ($T91Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T91Value . '</b>';
			} else {
				echo $T91Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;20.4 พืชต้องห้าม</td>
      <td style="text-align: center"><b>
          <?php
		  $T92Value = $row_all['T92'];
			if ($T92Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T92Value . '</b>';
			} else {
				echo $T92Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" colspan="3" style="font-size: 18px; text-align: left">&nbsp;21. บุคคลที่เกี่ยวข้องหรือเคยกระทำความผิดเกี่ยวกับ บุคคลต่างด้าว</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;21.1 หลบหนีเข้าเมือง</td>
      <td style="text-align: center"><b>
          <?php
		  $T93Value = $row_all['T93'];
			if ($T93Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T93Value . '</b>';
			} else {
				echo $T93Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;21.2 ให้ที่พักพิง</td>
      <td style="text-align: center"><b>
          <?php
		  $T94Value = $row_all['T94'];
			if ($T94Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T94Value . '</b>';
			} else {
				echo $T94Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;21.3 การประกอบอาชีพ</td>
      <td style="text-align: center"><b>
          <?php
		  $T95Value = $row_all['T95'];
			if ($T95Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T95Value . '</b>';
			} else {
				echo $T95Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" colspan="3" style="font-size: 18px; text-align: left">&nbsp;22. บุคคลที่เกี่ยวข้องหรือเคยกระทำความผิดเกี่ยวกับ การเอารัดเอาเปรียบทางการค้า</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;22.1 สินค้าเกินราคา</td>
      <td style="text-align: center"><b>
          <?php
		  $T96Value = $row_all['T96'];
			if ($T96Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T96Value . '</b>';
			} else {
				echo $T96Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;22.2 ฉ้อโกง</td>
      <td style="text-align: center"><b>
          <?php
		  $T97Value = $row_all['T97'];
			if ($T97Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T97Value . '</b>';
			} else {
				echo $T97Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;22.3 โฆษณาเกินจริง</td>
      <td style="text-align: center"><b>
          <?php
		  $T98Value = $row_all['T98'];
			if ($T98Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T98Value . '</b>';
			} else {
				echo $T98Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;22.4 อื่น ๆ</td>
      <td style="text-align: center"><b>
          <?php
		  $T99Value = $row_all['T99'];
			if ($T99Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T99Value . '</b>';
			} else {
				echo $T99Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" colspan="3" style="font-size: 18px; text-align: left">&nbsp;23. บุคคลที่เกี่ยวข้องหรือเคยกระทำความผิดเกี่ยวกับสินค้า ในกรณี</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;23.1 หนีภาษี</td>
      <td style="text-align: center"><b>
          <?php
		  $T100Value = $row_all['T100'];
			if ($T100Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T100Value . '</b>';
			} else {
				echo $T100Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;23.2 ไม่ผ่านพิธีการทางศุลกากร</td>
      <td style="text-align: center"><b>
          <?php
		  $T101Value = $row_all['T101'];
			if ($T101Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T101Value . '</b>';
			} else {
				echo $T101Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;23.3 ไม่เสียภาษีสรรพสามิต</td>
      <td style="text-align: center"><b>
          <?php
		  $T102Value = $row_all['T102'];
			if ($T102Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T102Value . '</b>';
			} else {
				echo $T102Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;23.4 มีไว้ในความครอบครองโดยไม่ได้รับอนุญาต</td>
      <td style="text-align: center"><b>
          <?php
		  $T103Value = $row_all['T103'];
			if ($T103Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T103Value . '</b>';
			} else {
				echo $T103Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
  </tbody>
</table>
    <br>
    <div style="flex: auto" align="right">
        &nbsp;&nbsp;&nbsp;<a href="/Bansuan/Show_crimes_person.php?rnd=<?= rand(); ?>" class="btn btn-primary btn-lg mb-4" >กลับ</a>&nbsp;
    </div>
    <hr>
</div>
    
</body>
</html>