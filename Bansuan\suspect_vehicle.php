<script src="/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>
<script src="../SweetAlert2/dist/sweetalert2.all.min.js"></script>

<?php //PDO
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

$id = isset($_GET['sv_cl_aid']) ? $_GET['sv_cl_aid'] : '';

// นับข้อมูลในตาราง ข้อมูลทั้งหมด
$sql_to = "SELECT COUNT(*) AS Total FROM wm_tb_suspect_vehicle WHERE station='$station' ";
    $stmt = $pdo->prepare($sql_to);
    $stmt->execute();
    $row_to = $stmt->fetch(PDO::FETCH_ASSOC);
	$total = $row_to['Total'];

// นับข้อมูลในตาราง
$sql_to3 = "SELECT COUNT(*) AS Total FROM wm_tb_suspect_vehicle WHERE station='$station' AND sv_cl_status='3' ";
	$stmt = $pdo->prepare($sql_to3);
    $stmt->execute();
    $row_to3 = $stmt->fetch(PDO::FETCH_ASSOC);
	$total3 = $row_to3['Total']; //

    $sum = $total - $total3
//print_r($user);
//เฉพาะ ยูเซอร์ บ้านเสวน = 1 เท่านั้นที่จะดูหน้านี้ได้
//$ua_delete_data = $user['ua_delete_data'];
//$del_btn = ($ua_delete_data == 0) ? "disabled" : "";

//echo '<pre>';
//print_r($res);
//echo '</pre>';
//
//echo '<hr>';
//
//echo '<pre>';
//var_dump($res);
//echo '</pre>';
//
//echo '<hr>';
//
//echo '<pre>';
//var_dump($row1);
//echo '</pre>';


//exit();

?>

<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link rel="stylesheet" href="/bootstrap/css/bootstrap-grid.css">
<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js"></script>
<style>
    .swal2-popup {
    width: 500px !important;
    height: auto !important;
    font-size: 14px !important;
}
</style>
<div class="container-fluid" align="left">
		<div class=" h3 text-center  alert alert-primary mb-4 mt-4 " role="alert" >บัญชีการตรวจยึดรถต้องสงสัยไว้ตรวจสอบ <?= $name_station ?> </div>
		<div align="left"> &nbsp;<a href="Add_suspect_vehicle.php" class="btn btn-success btn-lg mb-4 <?= $add_btn ?>" >เพิ่มข้อมูล</a>
        <a href="suspect_vehicle_return.php" class="btn btn-dark btn-lg mb-4" style="flex:initial">บัญชีคืนรถที่ยึดไว้ตรวจสอบแล้ว</a>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="https://eservice.dlt.go.th/b2bapp/login.jsf" target="new">เว็บไซต์ สนง.ขนส่ง ตรวจสอบยานพาหนะ</a>
		</div>
    <div align="left">
		<a style="background-color: yellow ;padding: 10px">ข้อมูลการยึดรถต้องสงสัยในพื้นที่  <?= $name_station ?> จำนวน : <span style="color: #FC0408"><b style="color: crimson; font-size: 24px">
            <?= $total ?> </b> คัน </a>
        <a style="background-color: yellow ;padding: 10px">คืนรถแล้ว จำนวน : <span style="color: #FC0408"><b style="color: crimson; font-size: 24px">
            <?= $total3 ?> </b> คัน </a>
        <a style="background-color: yellow ;padding: 10px">คงเหลือ จำนวน : <span style="color: #FC0408"><b style="color: crimson; font-size: 24px">
            <?= $sum ?> </b> คัน </a>
		</div>

<table class="table table-striped table-hover table-bordered mb-4 mt-4" >
  <tbody>
    <tr class="container-fluid" >
      	<th> ลำดับ </th>
		<th> วันตรวจยึด </th> 
		<th> ประเภทรถ </th>
		<th> ทะเบียน </th>
		<th> ยี่ห้อ </th>
		<th> รุ่น </th>
		<th> สี </th>   
		<th> เหตุต้องสงสัย </th>
        <th> สถานที่ตรวจยึด </th>
        <th> ผู้ขับขี่ </th>
        <th> เบอร์โทร </th>
		<th> ผู้ตรวจยึด </th>   
		<th> พนักงานสอบสวน </th>
        <th> สถานีตำรวจ </th>
        <th> การดำเนินการ </th>
        <th> เจ้าของกรรมสิทธิ์ </th>
        <th> ผู้รับคืนรถ </th>
        <th> เบอร์โทรผู้รับคืน </th>
        <th> วันที่ดำเนินการ </th>
        <th> สถานะ </th>
		<th></th>
        <th></th>
    </tr>  

<?php
$sql =  "SELECT
            T1.*,
            T2.station_name as station,
            T3.vt_cl_vehicle_type AS type,
            T4.svs_cl_status AS status
        FROM
            `wm_tb_suspect_vehicle` AS T1 " .
            "LEFT JOIN wm_tb_vehicle_type AS T3 ON T1.sv_cl_vehicle_type = T3.vt_cl_aid " .
            "LEFT JOIN wm_tb_suspect_vehicle_status AS T4 ON T1.sv_cl_status = T4.svs_cl_aid " .
            "LEFT JOIN wm_tb_police_station2 AS T2 ON T1.station = T2.station_code " .
        "WHERE
            (T1.sv_cl_status='1' OR T1.sv_cl_status='2' OR T1.sv_cl_status='4') AND station='$station' " .			//เพิ่มเงื่อนไข เลือกสถานี และสถานะมีผลใช้
        "ORDER BY
            T1.sv_cl_aid ASC"; 

//if($id != ''){
//	$sql = $sql . " WHERE T1.sv_cl_aid='$id' ";
//}
$stmt = $pdo->prepare($sql);
$stmt->execute();

$no = 1;
  // ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
while($row1 = $stmt->fetch(PDO::FETCH_ASSOC))	{//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
    
    //ฟังก์ชั่น วันที่ ดึงจาก condb
    $strDate = DateThai( $row1["sv_cl_date"] );
    $strDate2 = DateThai( $row1["sv_cl_action_date"] );
    
    //if($row1 <= '0'){
//    echo "ไม่มีรถต้องสงสัยที่ยึดไว้เพื่อตรวจสอบ";
//}
//echo '<pre>';
//print_r($row1);
//echo '</pre>';
//
//echo '<hr>';

    $pcid = $row1['sv_cl_aid'];  
    
?>
    <tr class="container-fluid">
        <td> <?= $no ?> </td> 
		<td nowrap> <?= $strDate ?> </td>   
		<td nowrap> <?=$row1["type"]?> </td>
		<td> <?=$row1["sv_cl_veh_plate"]?> </td>
		<td> <?=$row1["sv_cl_brand"]?> </td>
		<td> <?=$row1["sv_cl_model"]?> </td>
		<td> <?=$row1["sv_cl_color"]?> </td>		
		<td> <?=$row1["sv_cl_suspect"]?> </td>
        <td> <?=$row1["sv_cl_place"]?> </td>
        <td> <?=$row1["sv_cl_driver"]?> </td>
        <td> <?=$row1["sv_cl_phone_driver"]?> </td>
        <td> <?=$row1["sv_cl_police"]?> </td>
        <td> <?=$row1["sv_cl_inquiry_official"]?> </td>
        <td> <?=$row1["station"]?> </td>
        <td> <?=$row1["sv_cl_action"]?> </td>
        <td> <?=$row1["sv_cl_owner"]?> </td>
        <td> <?=$row1["sv_cl_return"]?> </td>
        <td> <?=$row1["sv_cl_phone_return"]?> </td>
        <td nowrap> <?= $strDate2 ?> </td>
        <td> <?=$row1["status"]?> </td>

        <td>&nbsp;<a href="Edit_suspect_vehicle.php?pcid=<?= $row1['sv_cl_veh_plate'] ?>&id=<?= $row1['sv_cl_aid'] ?> " class="btn btn-warning mb-4 <?= $edit_btn ?>" >แก้ไข</a>&nbsp;</td>
        
        <td>&nbsp;<button href="#" class="btn btn-danger mb-4" onClick="deleteItem(<?= $row1['sv_cl_aid'] ?>)"<?=$del_btn?>>ลบ</button>&nbsp;</td>
    </tr>    
<?php
    $no++;
}//while
?>

  </tbody>
</table>
</div>
<script>
function deleteItem(id) {
    Swal.fire({
        title: 'คุณต้องการลบข้อมูลนี้ใช่ไหม?',
        text: "หลังจากลบแล้ว จะไม่สามารถกู้คืนได้",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'ใช่, ยืนยันลบ',
        cancelButtonText: 'ยกเลิก'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = '/Bansuan/Del_suspect_vehicle.php?id=' + id;
        }
    });
}
</script>
