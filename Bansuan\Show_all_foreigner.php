<?php
include '../Condb.php'; //PDO
include '../users.inc.php';
include '../right_user.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);


$pcid = isset($_GET['pcid']) ? $_GET['pcid'] : '';
$id = isset($_GET['id']) ? $_GET['id'] : '';

$query1 = "SELECT * FROM `wm_tb_foreigner` WHERE `fo_cl_passport` = :pcid ";
    $stmt = $pdo->prepare($query1);
    $stmt->bindParam(':pcid', $pcid);
try{
    $stmt->execute();
}catch (PDOException $e) {
    echo '$query1 Failed: '. $e->getMessage();
}
    $row = $stmt->fetch(PDO::FETCH_ASSOC);

//echo '<pre>';
//print_r($result1);
//echo '</pre>';

if($row != ''){
    $people_name = $row['fo_cl_prefix_eng'] .  $row['fo_cl_name'] . ' ' .  $row['fo_cl_midname'] . ' ' . $row['fo_cl_surname']  ;
    $people_image = $row["fo_cl_image"];
    
}

    $people_image = "<img src='{$people_image}' width='100%'> ";
    
    $birthday = $row["fo_cl_birthday"];
//echo '<pre>';
//print_r($birthday);
//echo '</pre>';

//exit();
        if($birthday != "") 
        {
            $age = get_personal_age_2($birthday);
        }
        else {
            $age = $row["fo_cl_age"];
        }

//echo '<pre>';
//print_r($row);
//echo '</pre>';
//
//echo '<hr>';
//
//echo '<pre>';
//print_r($result1);
//echo '</pre>';
//
//echo '<hr>';

//echo '<pre>';
//print_r($result1);
//echo '</pre>';
//
//echo '<hr>';
//
//echo '<pre>';
//var_dump($result1);
//echo '</pre>';
//
//echo '<hr>';

?>
<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>ระบบ WatchmanDB</title>
<!-- เรียกใช้ Bootstrap CSS  //block=auto -->
<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
	.container-data {
		overflow: auto;		
		height: auto;
		display:  block;  
	}
@media print {
   a {
      display: none !important;
   }
}
</style>
<link rel="stylesheet" type="text/css" href="print.css" media="print">
</head>
<body>
	
<div class="container">
	<div class=" h4 text-center  alert alert-info mb-4 mt-4" role="alert" > รายละเอียดข้อมูลส่วนบุคคล </div>
	<?php
	include('../users_info.php');
	?>	
	<div align="left">
	&nbsp;&nbsp; <a href="/Bansuan/index.php?rnd=<?= rand(); ?>&page=foreigner" class="btn btn-primary btn-lg mb-4" >ย้อนกลับ</a>  
	</div>
	
<table border="1">
<tr>
	<td rowspan="2" align="center" valign="top" bgcolor="#1F00F9" style="text-align: center">
		<div style="width: 350px !important; display: inline-block !important; font-size: 18px;">
		<?= $people_image ?><br>
		<b style="color: aliceblue; font-size: 20px;"><?= $people_name ?></b><br>
		<b style="color: aliceblue; font-size: 20px;"><?= $row['fo_cl_passport'] ?></b><br>
        <b style="color: aliceblue; font-size: 20px;">อายุ  <?= $age ?>  ปี</b><br>     <!-- อายุ -->
		
        <a href="Show_all_foreigner.php?pcid=<?= $row["fo_cl_passport"] ?>" class="btn btn-outline-info" >Info</a>
		</div>
	</td>
	<td height="20" valign="top" nowrap>
		<ul class="nav nav-tabs">
			  <li class="nav-item">
				<a href="Show_all_foreigner.php?rnd=<?= rand(); ?>&pcid=<?= $row["fo_cl_passport"] ?>&page=info" class="btn btn-outline-primary mb-4" >ข้อมูลส่วนบุคคล</a>&nbsp;
			  </li>
			  <li class="nav-item">
				<a href="Show_all_foreigner.php?rnd=<?= rand(); ?>&pcid=<?= $row["fo_cl_passport"] ?>&page=address" class="btn btn-outline-primary mb-4" >ที่อยู่ในไทย</a>&nbsp;
			  </li>
			  <li class="nav-item">
				<a href="Show_all_foreigner.php?rnd=<?= rand(); ?>&pcid=<?= $row["fo_cl_passport"] ?>&page=travel" class="btn btn-outline-primary mb-4" >การเดินทาง</a>&nbsp;
			  </li>
			  <li class="nav-item">
				<a href="Show_all_foreigner.php?rnd=<?= rand(); ?>&pcid=<?= $row["fo_cl_passport"] ?>&page=employer" class="btn btn-outline-primary mb-4" >นายจ้าง</a>&nbsp;
			  </li>
        </ul>
      </td>
</tr>
<tr>
  <td valign="top" height="100%">
	  <div class="container-data">
        <p>
<?php
	  $page = isset($_GET['page']) ? $_GET['page'] : '';
		
	  if ($page == '')
	  {
		  include("show_fo_person.php");
	  }
	  elseif($page == 'address') {
		  include("show_fo_address.php");
	  }
	  elseif($page == 'travel') {
		  include("show_fo_travel.php");
	  }
	  elseif($page == 'employer') {
		  include("show_fo_employer.php");
	  }
	  elseif($page == 'info') {
		  include("show_fo_person.php");
	  }
      elseif($page === '') {
      }
      else {
          echo "&nbsp;&nbsp;&nbsp;&nbsp; ยังไม่ได้สร้างหน้า... '$page' ";
      }
?>
        </p>
        <p style="color: red">&nbsp;&nbsp;&nbsp;&nbsp;* การลบข้อมูล ต้องใช้สิทธิ์ Admin เท่านั้น</p>
	  </div>
    </td>
</tr>
	</table>
		<p>&nbsp;</p>
	</div>	
</body>
</html>

<script language="javascript">
function Del(mypage){
	var agree = confirm("คุณต้องการยืนยันที่จะลบข้อมูลนี้หรือไม่");
	if(agree)
	{
		window.location=mypage;
	}
}
</script>
<script>
function go_warrant(pcid)
{	
	window.location = "../WatchmanData/Add_all_foreigner.php?page=info &pcid=" +pcid + "&rnd=" + Math.random();
}
</script>