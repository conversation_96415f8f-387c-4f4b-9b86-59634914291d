<?php //PDO
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

$sql2 = "SELECT COUNT(*) AS Total, 
               COUNT(CASE WHEN on_cl_status_case = 'สิ้นสุด' THEN 1 END) AS Total2 
        FROM wm_tb_online_case 
        WHERE station = '$station'";

    $stmt2 = $pdo->prepare($sql2);
try{
    $stmt2->execute();
}catch (PDOException $e) {
    echo '$sql Failed: '. $e->getMessage();
}
    
    $row2 = $stmt2->fetch(PDO::FETCH_ASSOC);
    $total = $row2['Total'];
    $total3 = $row2['Total2'];
 
?>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link rel="stylesheet" href="/bootstrap/css/bootstrap-grid.css">
<style>
    .swal2-popup {
    width: 500px !important;
    height: auto !important;
    font-size: 14px !important;
}
</style>
<div class="container-fluid">
		<div class=" h3 text-center  alert alert-warning mb-4 mt-4 " role="alert" >ข้อมูลคดีรับแจ้งความออนไลน์ <?= $name_station ?> </div>
		<div align="left"> &nbsp;<a href="Add_online_case.php" class="btn btn-success btn-lg mb-4 <?= $add_btn ?>" >เพิ่มข้อมูล</a>
        &nbsp;<a href="online_case_finish.php" class="btn btn-dark btn-lg mb-4" >ข้อมูลคดีสิ้นสุดแล้ว</a>&nbsp;
        <a href="https://www.thaipoliceonline.com/pct-in/login" target="new" class="btn btn-primary btn-lg mb-4">ระบบรับแจ้งความออนไลน์ ตร.</a>
		</div>
		<div align="left">
		<a style="background-color: yellow ;padding: 10px">จำนวนคดีรับแจ้งความออนไลน์ <?= $name_station ?> ณ ปัจจุบัน มีทั้งหมดจำนวน : <span style="color: #FC0408"><b style="color: crimson; font-size: 24px">
            <?= $total ?> </b> คดี </a> <a style="background-color: yellow ;padding: 10px">สิ้นสุดแล้ว จำนวน : <span style="color: #FC0408"><b style="color: crimson; font-size: 24px">
            <?= $total3 ?> </b> คดี </a>
            <a style="background-color: yellow ;padding: 10px">คงเหลือ จำนวน : <span style="color: #FC0408"><b style="color: crimson; font-size: 24px">
            <?= $total - $total3 ?> </b> คดี </a>
		</div>

    
<table class="table table-striped table-hover table-bordered table-sm mb-4 mt-4 " >
      <tbody>
            <tr class="container-fluid ">
              <th nowrap="nowrap">ลำดับ</th>
              <th>เลขรับแจ้ง</th>
              <th>ผู้เสียหาย</th>
              <th>รายละเอียดโดยย่อ</th>
              <th>ค่าเสียหาย</th>
           <!--   <th>หน่วยรับผิดชอบ</th>   -->
              <th>พนักงานสอบสวน</th>
              <th>วันรับแจ้ง</th> 
              <th>สถานะ</th>
              <th>ผู้สืบสวน</th>
              <th>รายงานสืบสวน</th>
              <th></th>
              <th></th>
              <th></th>
            </tr>


<?php
$sql = "SELECT T1.*, T2.station_name as station " .
        "FROM wm_tb_online_case AS T1 " .
        "LEFT JOIN wm_tb_police_station2 AS T2 ON T1.`station` = T2.station_code " .
        "WHERE T1.station='$station' AND on_cl_status_case!='สิ้นสุด' " .
        "ORDER BY T1.on_cl_date DESC";

$stmt = $pdo->prepare($sql);
try {
    $stmt->execute();
} catch (PDOException $e) {
    echo '$sql Failed: ' . $e->getMessage();
}
    
// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no = 1;
while ($row_on = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $pcid = $row_on['on_cl_no'];

	//print_r($row_on);
    //ฟังก์ชั่น วันที่ ดึงจาก condb
    $strDate = DateThai( $row_on['on_cl_date'] );

//ตรวจสอบรายงานสืบสวนก่อนว่า มีไฟล์ในตารางไหม หากมี ให้ส่งเป็นลิงค์
$doc = $row_on["on_cl_file"];
if($doc !== '')
{
    $link = '<a href="/' . $row_on["on_cl_file"] .'" target="_blank">Download</a>';
}
else {
    $link = '';
}
?>                                                                  

            <tr class="container-fluid">
              <td>&nbsp; <?= $no ?></td>    
              <td><?= $pcid ?></td>
              <td><?= $row_on['on_cl_name_sufferer'] ?></td>
              <td style="text-align: left"><?= $row_on['on_cl_detail'] ?></td> 
              <td><?= $row_on['on_cl_damages'] ?></td> 
              <td style="text-align: left"><?= $row_on['on_cl_inquiry_official'] ?></td>
              <td nowrap><?= $strDate ?></td>
              <td><?= $row_on['on_cl_status_case'] ?></td>
              <td style="text-align: left"><?= $row_on['on_cl_detective'] ?></td>
              <td><?= $link ?></td>	
                
              <td><a href="show_detail_online.php?id=<?= $row_on['on_cl_aid'] ?>&pcid=<?= $pcid ?>" class="btn btn-success mb-4" >รายละเอียด</a></td>
              <td><a href="Edit_online_case.php?id=<?= $row_on['on_cl_aid'] ?>&pcid=<?= $pcid ?>" class="btn btn-warning mb-4 <?= $edit_btn ?>" >แก้ไข</a></td>
              <td><button href="#" class="btn btn-danger mb-4" onClick="deleteItem(<?= $row_on["on_cl_aid"] ?>, '<?= $row_on['on_cl_no'] ?>')"<?=$del_btn?>>ลบ</button></td>
            </tr>
                <?php
                    $no++;
                }
                ?>
      </tbody>
</table>
</div>
<script src="/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>
<script src="../SweetAlert2/dist/sweetalert2.all.min.js"></script>
<script>
function deleteItem(id, pcid) {
    Swal.fire({
        title: 'คุณต้องการลบข้อมูลนี้ใช่ไหม?',
        text: "หลังจากลบแล้ว จะไม่สามารถกู้คืนได้",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'ใช่, ยืนยันลบ',
        cancelButtonText: 'ยกเลิก'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = '/Bansuan/Del_online_case.php?id=' + id + '&pcid=' + pcid;
        }
    });
}
</script>
