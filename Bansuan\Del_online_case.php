<?php
include '../Condb.php';  //PDO 
include '../users.inc.php';
include '../Alert.php';

$id = $_GET['id'];
$pcid = $_GET['pcid'];

if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
// Store $pcid in a session variable
$_SESSION['pcid'] = $pcid;

$acc = $user['account'];
$ip_address = $_SERVER['REMOTE_ADDR'];
$action = 'Del online case'; // Update the action to reflect a delete action
if (isset($_SESSION['pcid'])) {
    $deletedItemId = $_SESSION['pcid'];
    $data = ['deleted_item_id' => $deletedItemId]; // Create a data array with the deleted item's ID
	$dataString = json_encode($data); // Encode the array into a JSON string
    log_activity($acc, $action, $dataString, $ip_address);

    // Clear the session variable after successful deletion if needed
    unset($_SESSION['pcid']);
}


if($user['ua_delete_data'] != 100)
{
    echo '<script>alert("เฉพาะสิทธิ์ Admin เท่านั้น")</script>';
}

try{
$sql="DELETE FROM wm_tb_online_case WHERE on_cl_aid = :id ";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':id', $id);
    $result = $stmt->execute();

    if ($result) {
        $_SESSION['success'] = "Data has been deleted successfully";
        showSweetAlert("Delete successfully", "ลบข้อมูลเรียบร้อย");
        unset($_SESSION['success']); // Clear the session variable
        header("refresh:2; url=/Bansuan/index.php?rnd=' + Math.random() + '&page=online");
    } else {
        $_SESSION['error'] = "ลบข้อมูลไม่สำเร็จ";
        showSweetAlert("Failed to delete", "ลบข้อมูลไม่สำเร็จ");
        unset($_SESSION['success']); // Clear the session variable
        header("refresh:2; url=/Bansuan/index.php?rnd=' + Math.random() + '&page=online");
    }
    
}catch(PDOException $e){
    echo 'Query Failed : ' . $e->getMessage();
}

$pdo = null;

?>