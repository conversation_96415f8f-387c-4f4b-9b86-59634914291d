<?php
include '../Condb.php'; //PDO
include '../users.inc.php';
include '../right_user.php';
include '../Head_image.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

$pcid = isset($_GET['pcid']) ? $_GET['pcid'] : '';

// นับข้อมูลในตาราง
$sql_out = "SELECT COUNT(*) AS Total FROM wm_tb_freed AS T1 WHERE station='$station' AND fr_cl_tracking='3'";
    $stmt = $pdo->prepare($sql_out);
    $stmt->execute();
    $row_out = $stmt->fetch(PDO::FETCH_ASSOC);
	$total_out = $row_out['Total']; //

?>
 <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <link rel="stylesheet" href="/bootstrap/css/bootstrap-grid.css">
        <base target="_top">
		<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
		<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
  		<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
 		<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js"></script>
<div class="container-fluid">

<div>
	<?php
	include('../users_info.php');
	?>	
</div>
		<div class=" h3 text-center  alert alert-dark mb-4 mt-4 " role="alert" >ข้อมูลบุคคลพ้นโทษ/พักการลงโทษ ย้ายไปอยู่นอกพื้นที่  <?= $name_station ?> </div>
        <div align="left"> &nbsp;&nbsp;<a href="index.php?rnd=<?= rand() ?>&page=freed" class="btn btn-warning btn-lg mb-4" >ย้อนกลับ</a>
		&nbsp;&nbsp;<a style="background-color: yellow ;padding: 10px">อยู่นอกพื้นที่ มีจำนวน : <span style="color: #FC0408"><b style="color: crimson; font-size: 24px">
            <?= $total_out ?> </b> ราย </a>
		</div>
            
<table class="table table-striped table-hover table-bordered table-sm mb-4 mt-4 " >
      <tbody>
            <tr class="container-fluid ">
              <th nowrap="nowrap">ลำดับ</th>
              <th>ชื่อ</th>
              <th>เลขบัตร</th>
              <th>ประเภท</th>
              <th>ต้องโทษฐาน</th>
              <th>กำหนดโทษ</th> 
              <th>นับตั้งแต่</th>
              <th>พ้นโทษ<br>/พักโทษ</th>
              <th>ที่อยู่ตามบัตร</th>
              <th>ที่อยู่ที่พบตัว</th>
              <th>ผู้รับผิดชอบ</th> 
              <th>สถานะ</th> 
              <th>หมายเหตุ</th>
              <th></th>
            </tr>

<?php

$sql = "SELECT
            T1.*,
            T2.ps_cl_name, T2.ps_cl_surname,
            T3.tac_cl_status AS tracking  " .		//เงื่อนไข การเลือกข้อมูล จาก 2 ตาราง
        "FROM
            wm_tb_freed AS T1 " .
            "LEFT JOIN wm_tb_personal AS T2 ON T1.fr_cl_idcard = T2.ps_cl_idcard " .
            "LEFT JOIN wm_tb_freed_tracking AS T3 ON T1.fr_cl_tracking = T3.tac_cl_aid " .
        "WHERE
            T1.station='$station' AND fr_cl_tracking='3' " .			//เพิ่มเงื่อนไข เลือกสถานี และสถานะมีผลใช้
        "ORDER BY
            T1.fr_cl_freed ASC";  //ตั้งตัวแปร sql เลือกฐานข้อมูลจากตารางที่ชื่อ wm_tb_personal

    $stmt = $pdo->prepare($sql);
try{
    $stmt->execute();
}catch (PDOException $e) {
    echo '$sql Failed: '. $e->getMessage();
}

// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no = 1;
while($rowFr = $stmt->fetch(PDO::FETCH_ASSOC))	{//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
    $pcid = $rowFr['fr_cl_idcard']; //เลขบัตร ผู้พ้นโทษ

    //ฟังก์ชั่น วันที่ ดึงจาก condb
    if($rowFr['fr_cl_freed'] != ""){
        $strDate = DateThai( $rowFr['fr_cl_freed'] );
    }else{
        $strDate = "";
    }

    if($rowFr['fr_cl_check_date'] != ""){
        $strDate2 = DateThai( $rowFr['fr_cl_check_date'] );
    }else{
        $strDate2 = "";
    }

    if($rowFr['fr_cl_start'] != ""){
        $strDate3 = DateThai( $rowFr['fr_cl_start'] );
    }else{
        $strDate3 = "";
    }
?>                                                                  

            <tr class="container-fluid">
              <td>&nbsp; <?= $no ?></td>
              <td nowrap><?= $rowFr['ps_cl_name'] .' ' . $rowFr['ps_cl_surname'] ?></td>
              <td nowrap><?= $pcid ?></td>
              <td><?= $rowFr['fr_cl_status'] ?></td>
              <td><?= $rowFr['fr_cl_offense'] ?></td>
              <td><?= $rowFr['fr_cl_in_prison'] ?></td> 
              <td nowrap><?= $strDate3 ?></td>
              <td nowrap><?= $strDate ?></td>
              <td><?= $rowFr['fr_cl_adrress1'] ?></td>
              <td><?= $rowFr['fr_cl_adrress2'] ?></td>
              <td><?= $rowFr['fr_cl_police'] ?></td>
              <td nowrap><?= $rowFr['tracking'] ?></td> 
              <td><?= $rowFr['fr_cl_remark'] ?></td>	
              <td><button onClick="go_detail('<?= $pcid ?>')" class="btn btn-success">รายละเอียด</button><br>
                  &nbsp; <a href="home_visit.php?id=<?= $rowFr["fr_cl_aid"] ?>&pcid=<?= $pcid ?>" class="btn btn-warning mb-4" >ประวัติการเยี่ยม</a> </td>
            </tr>
                <?php
                    $no++;
                }//while
          ?>
      </tbody>
</table>
</div>
    <hr>

<script>
function go_detail(pcid)	
{
	window.location = "/WatchmanData/Show_All.php?pcid=" +pcid + "&rnd=" + "&page=freed";
}
</script>

