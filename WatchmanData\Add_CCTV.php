<?php
include '../Condb.php';
include '../users.inc.php';
include '../right_user.php';


// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// เมื่อมีการใช้ ตัวแปร Session จะต้องเปิดก่อน
//session_start();  // เปิดตัวแปร session
/*if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
*/

// สำหรับเลือก จังหวัด อำเภอ ตำบล /และ สถานีตำรวจ
include("../config.inc.php");
include("../classes/class.database.inc.php");
$conn2 = get_connection(); // connect to mySQLDB

$province = isset($_GET['province']) ? $_GET['province'] : 64; // sukhothai
$amphure = isset($_GET['amphure']) ? $_GET['amphure'] : 0; // sukhothai
$tambon = isset($_GET['tambon']) ? $_GET['tambon'] : 0; // sukhothai

// สำหรับเลือกสถานีตำรวจทั่วประเทศ
$region = isset($_GET['region']) ? $_GET['region'] : 0; // sukhothai
$provincial = isset($_GET['provincial']) ? $_GET['provincial'] : 0; // sukhothai
//$station = isset($_GET['station']) ? $_GET['station'] : 0; // sukhothai

?>


<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>เพิ่มข้อมูล CCTV</title>
<script language="javascript" src="/policeinnopolis/jQuery/jquery-3.5.1.min.js"></script><!--เจคิวรี่ สำหรับ ฟังก์ชั่นเลือก จังหวัด อำเภอ ตำบล-->
<link rel="stylesheet" href="/policeinnopolis/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 18px;
    }
input::placeholder {
  color: red;
}
.form-control::placeholder {
  color: sandybrown;
}
</style>
<!-- สำหรับ วันที่ แบบ พ.ศ. -->
<link rel="stylesheet" href="http://code.jquery.com/ui/1.9.1/themes/base/jquery-ui.css" />
<script src="http://code.jquery.com/jquery-1.8.2.js"></script>
<script src="/policeinnopolis/js/jquery-ui.js"></script>
</head>

<body>
	
	<div class="container">
	<div class="row">
	<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> เพิ่มข้อมูล CCTV </div>
	<form action="Save_CCTV.php" method="POST" enctype="multipart/form-data" class="body">
        
        <label hidden ="hidden">ลำดับ</label>
		<input type = "text" name = "cc_cl_aid" class="form-control"  hidden ="hidden" >
        
        <label>ประเภทกล้องวงจรปิด</label>
			<select id="cc_cl_type" name="cc_cl_type" class="form-select form-select-sm"  >
				<option value="" selected> </option>  <!-- ดึงข้อมูลมาจากตารางใหม่ -->
				<?php
					$res_gt = mysqli_query($conn, "SELECT * FROM `wm_tb_cctv_type` ORDER BY `ct_cl_aid` ASC");
                
					while($row_gt = mysqli_fetch_array($res_gt))
					{
						echo "<option value='{$row_gt['ct_cl_aid']}'>{$row_gt['ct_cl_type']}</option>";
					}
				?>
			</select>

		<label>สังกัด</label>
		<select id="cc_cl_owner" name="cc_cl_owner" class="form-select form-select-sm" >
			<option value="" selected> </option>
			<option value="รัฐ">รัฐ</option>
			<option value="เอกชน">เอกชน</option>
		</select>

        <label>จุด/สถานที่ติดตั้งกล้อง</label>
		<input type = "text" name = "cc_cl_install" class="form-control" placeholder="จุด/สถานที่ติดตั้งกล้อง" >
        
        <label>ชื่อหน่วยงาน หรือบริษัทที่ติดตั้งกล้อง</label>
		<input type = "text" name = "cc_cl_place" class="form-control" placeholder="ชื่อหน่วยงาน หรือบริษัทที่ติดตั้งกล้อง" >
        
        <!-- เลือกจังวัด อำเภอ ตำบล อัตโนมัติ -->
        <label> จังหวัด </label><br>
        <select name="cc_cl_province" id="cc_cl_province" onChange="do_province_change()" class="form-control" >
            <option value="0">&lt;&lt; เลือกจังหวัด &gt;&gt;</option>
                <?php
			  	$res_p = $conn2->query("SELECT * FROM `provinces` ORDER BY `provinces`.`pv_name_th` ASC");
			     $selected = '';
			     while($row_p = $conn2->fetch_row($res_p)) 
			     {
				  $pv_code = $row_p['pv_code'];
				  $pv_name = $row_p['pv_name_th'];
				  // set default provionce to 64 >> sukhothai
				  if($pv_code == $province) {
					  $selected = 'selected';
				  }
				  else {
					  $selected = '';
				  }
				  //
				  echo "<option value='$pv_code' $selected> $pv_name </option>\n";
			     }
			  
			  ?>
              </select>
        
        <label> อำเภอ </label><br>
        <select name="cc_cl_amphur" id="cc_cl_amphur" onChange="do_amphure_change()" class="form-control" >
        <option value="0">&lt;&lt; เลือกอำเภอ &gt;&gt;</option>
            <?php
            if($province > 0)
            {
                $res_a = $conn2->query("SELECT * FROM `amphures` WHERE province_code='$province' ORDER BY `amphures`.`ap_name_th` ASC");
                $selected = '';
              while($row_a = $conn2->fetch_row($res_a)) 
              {
                  $ap_code = $row_a['ap_code']; // 
                  $ap_name = $row_a['ap_name_th'];
                  // set default provionce to 64 >> sukhothai
                  if($ap_code == $amphure) {
                      $selected = 'selected';
                  }
                  else {
                      $selected = '';
                  }
                  //
                  echo "<option value='$ap_code' $selected> $ap_name </option>\n";
              }
            }	
            ?>
          </select>
        
         <label> ตำบล </label>
        <br>
        <select name="cc_cl_tumbon" id="cc_cl_tumbon" class="form-control" >
                <option value="0">&lt;&lt; เลือกตำบล &gt;&gt;</option>
                <?php
	           if($amphure > 0)
                {
                    $res_t = $conn2->query("SELECT * FROM `districts` WHERE amphure_code='$amphure' ORDER BY `districts`.`tb_name_th` ASC");
                    $selected = '';
                  while($row_t = $conn2->fetch_row($res_t)) 
                  {
                      $tb_code = $row_t['tb_id']; // 
                      $tb_name = $row_t['tb_name_th'];
                      // set default provionce to 64 >> sukhothai
                      if($tb_code == $tambon) {
                          $selected = 'selected';
                      }
                      else {
                          $selected = '';
                      }
                      //
                      echo "<option value='$tb_code' $selected> $tb_name </option>\n";
                  }
                }	
                ?>                                                                     
              </select>

        <label> หมู่ </label><br>
        <input type = "text" name = "cc_cl_moo" class="form-control" placeholder="ระบุหมู่บ้าน" >
        
        <label> เลขที่ </label>
        <input type = "text" name = "cc_cl_adr" class="form-control" placeholder="เลขที่" >
        
        <label> ผู้ดูแล/ผู้ประสานงาน </label>
        <input type = "text" name = "cc_cl_admin" class="form-control" placeholder="ผู้ดูแล/ผู้ประสานงาน กรณีต้องการดูข้อมูล" >
        
        <label> เบอร์ติดต่อ </label>
        <input type = "text" name = "cc_cl_phone" class="form-control" placeholder="เบอร์ติดต่อผู้ดูแลกล้อง" >
        
        <label> จำนวนวันที่สามารถบันทึกข้อมูลได้ </label>
        <input type = "text" name = "cc_cl_record" class="form-control" placeholder="จำนวนวันที่สามารถบันทึกข้อมูลได้" >
        
        <label>สถานะ</label>
		<select id="cc_cl_status" name="cc_cl_status" class="form-select form-select-sm" >
			<option value="" selected> </option>
			<option value="ปกติ">ปกติ</option>
			<option value="เสีย">เสีย</option>
		</select>
        
        <label>วันที่ตรวจสอบ <span style="color: #F90004">* จำเป็น</span></label>
        <p><input type="text" name="cc_cl_date" id="datepicker" placeholder="เพื่อข้อมูลถูกต้อง กรุณาเลือกจากปฎิทินด้านล่าง" class="form-control" autocomplete="off" required></p>
        <script>
            function set_cal(ele)//function สร้างตัวเลือกปฎิทิน
            {
              $( ele ).datepicker({
                  onSelect:(date_text)=>
                  {
                    let arr=date_text.split("/");
                    let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])+543).toString();
                    $(ele).val(new_date);
                    $(ele).css("color","");
                  },
                  beforeShow:()=>{

                    if($(ele).val()!="")
                    {
                      let arr=$(ele).val().split("/");
                      let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])-543).toString();
                      $(ele).val(new_date);

                    }

                    $(ele).css("color","white");
                  },
                  onClose:()=>{

                      $(ele).css("color","");

                      if($(ele).val()!="")
                      {
                          let arr=$(ele).val().split("-");
                          if(parseInt(arr[2])<2500)
                          {
                              let new_date=arr[0]+"-"+arr[1]+"-"+(parseInt(arr[2])).toString();
                              $(ele).val(new_date);
                          }
                      }
                  },
                  dateFormat:"yy-mm-dd", //กำหนดรูปแบบวันที่เป็น วัน/เดือน/ปี
                  changeMonth:true,//กำหนดให้เลือกเดือนได้
                  changeYear:true,//กำหนดให้เลือกปีได้
                  showOtherMonths:true,//กำหนดให้แสดงวันของเดือนก่อนหน้าได้
              });
            }
          $(document).ready(function(){
            //เรียก function set_cal เมื่อเปิดหน้าเว็บ โดยส่ง object element ที่มี id เป็น datepicker เป็นพารามิเตอร์
            set_cal( $("#datepicker") );
          })
          </script>
        <!-- สำหรับ วันที่ แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ -->
        
        <label> ละติจูด <span style="color: #F90004">* จำเป็น</span></label>
        <input type = "text" name = "cc_cl_lat" class="form-control" autocomplete="off" Required placeholder="ระบุพิกัด ละติจูด หากไม่มีให้ระบุเป็น 0"  >
        
        <label> ลองจิจูด <span style="color: #F90004">* จำเป็น</span> </label>
        <input type = "text" name = "cc_cl_lon" class="form-control" autocomplete="off" Required placeholder="ระบุพิกัด ลองจิจูด หากไม่มีให้ระบุเป็น 0"  >
        
        <label>สถานีตำรวจ <span style="color: #F90004">* จำเป็น</span></label><br>
                <!-- เลือกสถานีตำรวจ -->
        <label> บช. </label><br>
        <select name="region" id="region" onChange="do_region_change()" class="form-control" Required >
                <option value="">&lt;&lt; เลือก บช. &gt;&gt;</option>
                <?php
			  	$res_re = $conn2->query("SELECT * FROM `wm_tb_police_region` 
                                            ORDER BY `name_region` ASC");
			     $selected = '';
			     while($row_re = $conn2->fetch_row($res_re)) 
                     {
                      $code_region = $row_re['code_region'];
                      $name_region = $row_re['name_region'];
                      // set default provionce to 64 >> sukhothai
                          if($code_region == $region) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$code_region' $selected> $name_region </option>\n";
                     }
			  
			  ?>
              </select>
        
        <label> บก. </label><br>
        <select name="provincial" id="provincial" onChange="do_provincial_change()" class="form-control" >
        <option value="0">&lt;&lt; เลือก บก. &gt;&gt;</option>
                <?php
                if($region > 0)
                {
                    $res_prov = $conn2->query("SELECT * FROM `wm_tb_police_provincial` 
                                                WHERE `region_code`='$region' 
                                                ORDER BY `provincial_code` ASC");
                    $selected = '';
                  while($res_prov = $conn2->fetch_row($res_prov)) 
                  {
                      $provincial_code = $res_prov['provincial_code']; // 
                      $provincial = $res_prov['provincial'];
                      // set default provionce to 64 >> sukhothai
                          if($provincial_code == $provincial) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$provincial_code' $selected> $provincial </option>\n";
                  }
                }	
                ?>
              </select>

         <label> สน./สภ. </label>
        <br>
        <select name="station" id="station" class="form-control" >
                <option value="0">&lt;&lt; เลือก สน./สภ. &gt;&gt;</option>
                <?php
	           if($provincial > 0)
                {
                    $res_st = $conn2->query("SELECT * FROM `wm_tb_police_station2` 
                                                WHERE provincial_code='$provincial_code'
                                                ORDER BY `station_code` ASC");
                    $selected = '';
                  while($row_st = $conn2->fetch_row($res_st)) 
                  {
                      $station_code = $row_st['station_code']; // 
                      $station_name = $row_st['station_name'];
                      // set default provionce to 64 >> sukhothai
                          if($station_code == $station) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$station_code' $selected> $station_name </option>\n";
                  }
                }	
                ?>                                                                     
              </select>
		<br>
        
		<label> หมายเหตุ </label>
		<input type = "text" name = "cc_cl_remark" class="form-control" placeholder="หมายเหตุ" >
		
        <div class="mb-3">
			<label for="formFileMultiple" class="form-label">ภาพกล้อง</label>
			 <input class="form-control" type="file" id="cc_cl_cctv_picture" name="cc_cl_cctv_picture" multiple>
		</div>
        
        <div class="mb-3">
			<label for="formFileMultiple" class="form-label">ภาพมุมกล้อง</label>
			 <input class="form-control" type="file" id="cc_cl_cctv_view" name="cc_cl_cctv_view" multiple>
		</div>
        
        
		<p>
			<input type="submit" value="บันทึกข้อมูล" class="btn btn-success" >
			<td> <a href="/WatchmanData/Show_CCTV.php?rnd=<?= rand(); ?>" class="btn btn-warning" >ยกเลิก</a></td>
		</p>
		<br>
		<br>
	</form>
	</div>
	</div>
	</div>
<!-- ฟังก์ชั่น เลือก จังหวัด อำเภอ ตำบล -->
<script>

function do_province_change()
{
	var sel = document.getElementById("cc_cl_province");		
	var code = sel.options[sel.selectedIndex].value;

	$.get("../ajax/get_amphure.php?province=" + code, 
		  function(data, status){
				//alert("Data: " + data + "\nStatus: " + status);
				// jQuery style						
				$('#cc_cl_amphur').find('option').remove().end();
				var data = $.trim(data);
				if(data == '') {
					alert("ไม่พบข้อมูลอำเภอ !");
					return;
				}

					//.append('<option value="whatever">text</option>').val('whatever');
				var datas = data.split(';');
				for(var i=0; i<datas.length; i++) {
					if(datas[i] == '') continue;
					datas[i] = datas[i].split(':');
					var code = datas[i][0];
					var name = datas[i][1];
					$('#cc_cl_amphur').append('<option value="'+ code+'">' + name + '</option>');
				}
                // Trigger the change event on the amphur dropdown so the tambon dropdown will be populated     
                $('#cc_cl_amphur').trigger('change');
		});
}

function do_amphure_change()
{
	var sel = document.getElementById("cc_cl_province");		
	var code = sel.options[sel.selectedIndex].value;

	var sel2 = document.getElementById("cc_cl_amphur");		
	var code2 = sel2.options[sel2.selectedIndex].value;

	$.get("../ajax/get_district.php?province=" + code + "&amphure=" + code2, 
			  function(data, status){
					//alert("Data: " + data + "\nStatus: " + status);
					// jQuery style						
					$('#cc_cl_tumbon').find('option').remove().end();
					var data = $.trim(data);
					if(data == '') {
						alert("ไม่พบข้อมูลตำบล !");
						return;
					}

						//.append('<option value="whatever">text</option>').val('whatever');
					var datas = data.split(';');
					for(var i=0; i<datas.length; i++) {
						if(datas[i] == '') continue;
						datas[i] = datas[i].split(':');
						var code = datas[i][0];
						var name = datas[i][1];
						$('#cc_cl_tumbon').append('<option value="'+ code+'">' + name + '</option>');
					}
			});
}
// ฟังก์ชั่น เปลี่ยนภาค (บช.) เป็น ภ.จว. (บก.)
function do_region_change()
{
	var sel_region = document.getElementById("region");		
	var code_region = sel_region.options[sel_region.selectedIndex].value;

	$.get("../ajax/get_provincial.php?region=" + code_region, 
		  function(data, status){
				//alert("Data: " + data + "\nStatus: " + status);
				// jQuery style						
				$('#provincial').find('option').remove().end();
				var data = $.trim(data);
				if(data == '') {
					alert("ไม่พบข้อมูล บก. !");
					return;
				}

					//.append('<option value="whatever">text</option>').val('whatever');
				var datas = data.split(';');
				for(var i=0; i<datas.length; i++) {
					if(datas[i] == '') continue;
					datas[i] = datas[i].split(':');
					var code_region = datas[i][0];
					var name_region = datas[i][1];
					$('#provincial').append('<option value="'+ code_region+'">' + name_region + '</option>');
				}
                // Trigger the change event on the amphur dropdown so the tambon dropdown will be populated     
                $('#provincial').trigger('change');
		});
}
    //เลือก สถานี จากจังหวัด
function do_provincial_change()
{
	var sel_provincial = document.getElementById("region");		
	var provincial_code = sel_provincial.options[sel_provincial.selectedIndex].value;
    
    var sel_provincial2 = document.getElementById("provincial");		
	var station_code = sel_provincial2.options[sel_provincial2.selectedIndex].value;

	$.get("../ajax/get_station.php?region=" + provincial_code + "&provincial=" + station_code, 
			  function(data, status){
					//alert("Data: " + data + "\nStatus: " + status);
					// jQuery style						
					$('#station').find('option').remove().end();
					var data = $.trim(data);
					if(data == '') {
						alert("ไม่พบข้อมูลสถานี !");
						return;
					}

						//.append('<option value="whatever">text</option>').val('whatever');
					var datas = data.split(';');
					for(var i=0; i<datas.length; i++) {
						if(datas[i] == '') continue;
						datas[i] = datas[i].split(':');
						var station_code = datas[i][0];
						var station_name = datas[i][1];
						$('#station').append('<option value="'+ station_code+'">' + station_name + '</option>');
					}
			});
}
</script> 
</body>
</html>