<?php
include '../Condb.php';
include '../users.inc.php';
include '../right_user.php';
include '../Head_image.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

$id = isset($_GET['shoot_aid']) ? $_GET['shoot_aid'] : '';
//
////get month from select or current month
$month = isset($_GET['month']) ? $_GET['month'] : date("m");
$year = isset($_GET['year']) ? $_GET['year'] : date("Y");

$m1 = '';
$m2 = '';
$m3 = '';
$m4 = '';
$m5 = '';
$m6 = '';
$m7 = '';
$m8 = '';
$m9 = '';
$m10 = '';
$m11 = '';
$m12 = '';
switch($month)
{
    case "01": $m1 = "selected"; break;
    case "02": $m2 = "selected"; break;
    case "03": $m3 = "selected"; break;
    case "04": $m4 = "selected"; break;
    case "05": $m5 = "selected"; break;
    case "06": $m6 = "selected"; break;
    case "07": $m7 = "selected"; break;
    case "08": $m8 = "selected"; break;
    case "09": $m9 = "selected"; break;
    case "10": $m10 = "selected"; break;
    case "11": $m11 = "selected"; break;
    case "12": $m12 = "selected"; break;
}
$current_month = $global_thai_month[0+$month];
$y2 = $year+543;

?>

<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
        <base target="_top">
		<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
		<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
  		<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
 		<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js"></script>

<style type="text/css">
body{
    font-size:12px; 
}
.textAlignVer{
    display:block;
    filter: flipv fliph;
    -webkit-transform: rotate(-90deg); 
    -moz-transform: rotate(-90deg); 
    transform: rotate(-90deg); 
    position:relative;
    width:20px;
    white-space:nowrap;
    font-size:12px;
    margin-bottom:10px;
}
</style>
<!--<p><img src="../Image/Head2.jpg" width="100%" height="" alt=""/></p>-->
<div class="container-fluid" align="left">
<div class=" h3 text-center  alert alert-success mb-4 mt-4 " role="alert" >กิจกรรม 16 การเพิกถอนใบอนุญาต และทะเบียนอาวุธปืน กรณีเหตุที่เกิดขึ้น</div>

<div align="left">
<div>
    <table width="84%" border="0" cellspacing="1" cellpadding="1">
      <tbody>
        <tr>
          <td width="10%">&nbsp;<a href="/WM/index.php?rnd=<?= rand(); ?>&page=report" class="btn btn-warning btn-lg mb-4" >ย้อนกลับ</a></td>
          <!--<td width="13%">&nbsp;<a href="#Add_Activity1.php" class="btn btn-success btn-lg mb-4" >เพิ่มข้อมูล</a></td>-->
          <td width="11%"><label>เลือกปี </label>
            <select id="mi_cl_year" name="mi_cl_year" class="form-select col-8 d-lg-inline" style="background-color: khaki; font-size: 16px" onChange="dates_change()">
              <option value="">เลือก</option>
              <?php
                $cur_y = date("Y") + 2;
                for($y=2020; $y<$cur_y; $y++) {
                    $sel = ($y == $year) ? "selected" : '';
                    echo "<option value='$y' $sel> ". ($y + 543) ." </option>";
                }
            ?>
          </select></td>
            <td width="1%">&nbsp;</td>
          <td width="19%"><label>เดือน</label>
            <select id="mi_cl_month" name="mi_cl_month" class="form-select col-3 d-lg-inline" style="background-color: khaki; font-size: 16px" onChange="dates_change()">
              <option value="">เลือก</option>
              <option <?= $m1 ?> value="01">มกราคม</option>
              <option <?= $m2 ?> value="02">กุมภาพันธ์</option>
              <option <?= $m3 ?> value="03">มีนาคม</option>
              <option <?= $m4 ?> value="04">เมษายน</option>
              <option <?= $m5 ?> value="05">พฤษภาคม</option>
              <option <?= $m6 ?> value="06">มิถุนายน</option>
              <option <?= $m7 ?> value="07">กรกฎาคม</option>
              <option <?= $m8 ?> value="08">สิงหาคม</option>
              <option <?= $m9 ?> value="09">กันยายน</option>
              <option <?= $m10 ?> value="10">ตุลาคม</option>
              <option <?= $m11 ?> value="11">พฤศจิกายน</option>
              <option <?= $m12 ?> value="12">ธันวาคม</option>
          </select></td>
            <td width="46%">&nbsp;&nbsp;</td>
        </tr>
      </tbody>
    </table>
</div>
<br>
<h3 align="center">ข้อมูลการส่งอาวุธปืนที่ใช้ในการก่อเหตุเพื่อเพิกถอนทะเบียน</h3>
<h3 align="center">ประจำเดือน&nbsp; <?= $current_month ?> &nbsp;<?= $y2 ?> &nbsp;</h3>
<div class="table-responsive">
<table align="center" width="1120" height="240" border="1" cellpadding="1" cellspacing="1" class="table-bordered table-hover mb-4 mt-4">
  <tbody>
    <tr>
      <td bgcolor="#C8F96D" style="color: #00000; text-align: center; border-color: navy">ลำดับ</td>
      <td bgcolor="#C8F96D" style="color: #00000; text-align: center; border-color: navy">สถานี</td>
      <td height="90" bgcolor="#C8F96D" style="color: #00000; text-align: center; border-color: navy">จำนวนอาวุธปืนมีทะเบียน<br>
        ที่ใช้ในการก่อเหตุ<br>
        (กระบอก)</td>
      <td height="90" bgcolor="#C8F96D" style="color: #00000; text-align: center; border-color: navy">จำนวนอาวุธปืนที่ประสาน<br>
        นายอำเภอเพื่อเพิกถอนทะเบียน<br>
        (กระบอก)</td>
      <td height="90" bgcolor="#C8F96D" style="color: #00000; text-align: center; border-color: navy">คงเหลือ<br>
        อาวุธปืนที่ใช้ในการก่อเหตุ<br>
        ที่ยังไม่ได้ประสานเพิกถอน<br>
        (กระบอก)</td>
      <td height="90" bgcolor="#C8F96D" style="color: #00000; text-align: center; border-color: navy">นายอำเภอ<br>
        ทำการเพิกถอนทะเบียน<br>
        เรียบร้อย <br>
        (กระบอก)</td>
    </tr>
	  
<?php
//$sql = "SELECT *, T2.wm_station_name AS pol_st ".
//        "FROM wm_tb_gun_shooting_keep_shell AS T1 ".
//        "LEFT JOIN wm_tb_police_station AS T2 ON T1.shoot_pol_st = T2.wm_PID " .
//        "WHERE shoot_date ORDER BY shoot_aid ASC ";  //ตั้งตัวแปร sql เลือกฐานข้อมูลจากตารางที่ชื่อ wm_tb_personal
//if($id != ''){
//	$sql = $sql . " WHERE shoot_aid='$id' ";
//}
//$result = mysqli_query($conn,$sql);
//                      
//// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
//$no = 1;
//while($row = mysqli_fetch_array($result))	//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
//{
//    
//    //ฟังก์ชั่น วันที่ ดึงจาก condb
// $strDate = DateThai( $row["shoot_date"] );
//
//$total_co = $row["befor_oct2022"]+$row["oct_2022"]+$row["nov_2022"]+$row["dec_2022"];
//
//
//$total_sh = $row["sh_befor_oct2022"]+$row["sh_oct_2022"]+$row["sh_nov_2022"]+$row["sh_dec_2022"];
//
//
//if($total_co == ''){
//    $balance = '0';
//}else{
//    $balance = $total_co-$total_sh;
//}
//    
//$total_co2 = $row["befor_oct2022"]+$row["oct_2022"]+$row["nov_2022"]+$row["dec_2022"]+$balance;

?>	

    <tr>
      <td>1</td>
      <td nowrap>&nbsp;
        สภ.บ้านสวน
        &nbsp;</td>
      <td>&nbsp;&nbsp;0</td>
      <td>&nbsp;&nbsp;0</td>
      <td>&nbsp;&nbsp;0</td>
      <td>&nbsp;&nbsp;0</td>
      </tr>
<?php
//	$no++;
//}//while
?>
      
<?php

?>
<!--ตารางรวม -->
<!--    <tr>
	  <td height="52" colspan="2" style="font-size: 24px; background-color: yellow">รวม&nbsp;&nbsp;</td>
	  <td height="52" style="font-size: 24px; background-color: yellow">&nbsp;&nbsp;</td>
	  <td height="52" style="font-size: 24px; background-color: yellow">&nbsp;&nbsp;</td>
	  <td height="52" style="font-size: 24px; background-color: yellow">&nbsp;&nbsp;</td>
	  <td height="52" style="font-size: 24px; background-color: yellow">&nbsp;&nbsp;</td>
	  </tr>-->
  </tbody>
</table>
</div>
<!-------------------------------------------------------------------->
<!-------------------------------------------------------------------->
<!-------------------------------------------------------------------->
<script>

function dates_change()  // คำสั่งเปลี่ยนเดือน ปี
{
    var ms = $("#mi_cl_month option:selected").val();
    var ys = $("#mi_cl_year option:selected").val();
    window.location = "/Activity/Show_Activity16.php?&month=" + ms + "&year=" + ys + "&rnd=" + Math.random();
}
</script>
