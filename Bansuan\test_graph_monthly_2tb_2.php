<?php
// Step 1: Establish a database connection
include '../Condb.php';

// Step 2: Fetch data for goals and performance for each month
$query = "SELECT MONTH(pf_cl_dates) as month, 
                SUM(pf_cl_drug_sell) as drug_sell,
                SUM(pf_cl_drug_occupy) as drug_occupy,
                SUM(pf_cl_take_drugs) as take_drugs,
                SUM(pf_cl_drug_therapy) as drug_therapy,
                SUM(pf_cl_gun) as gun,
                SUM(pf_cl_gambling_case) as gambling,
                SUM(pf_cl_immigrant_case) as immigrant,
                SUM(pf_cl_techno_case) as techno,
                SUM(pf_cl_wanted_qty) as wanted
            FROM wm_tb_performance
            GROUP BY MONTH(pf_cl_dates)";
$result = mysqli_query($conn, $query);

// Step 3: Loop through the result set and store the values in two separate arrays
$drug_sell_array = array();
$drug_occupy_array = array();
$take_drugs_array = array();
$drug_therapy_array = array();
$gun_array = array();
$gambling_array = array();
$immigrant_array = array();
$techno_array = array();
$wanted_array = array();
while ($row = mysqli_fetch_assoc($result)) {
    $drug_sell_array[] = $row['drug_sell'];
    $drug_occupy_array[] = $row['drug_occupy'];
    $take_drugs_array[] = $row['take_drugs'];
    $drug_therapy_array[] = $row['drug_therapy'];
    $gun_array[] = $row['gun'];
    $gambling_array[] = $row['gambling'];
    $immigrant_array[] = $row['immigrant'];
    $techno_array[] = $row['techno'];
    $wanted_array[] = $row['wanted'];
}

// Step 4: Use a charting library to create a bar graph
// Here's an example using Google Charts
?>

<html>
  <head>
    <script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
    <script type="text/javascript">
      google.charts.load('current', {'packages':['corechart']});
      google.charts.setOnLoadCallback(drawChart);

      function drawChart() {
        var data = google.visualization.arrayToDataTable([
          ['Month', 'จำหน่าย', 'ครอบครอง', 'เสพ', 'ส่งคัดกรอง' ],
          ['ม.ค.', <?php echo $drug_sell_array[0]; ?>, <?php echo $drug_occupy_array[0]; ?>, <?php echo $take_drugs_array[0]; ?>, <?php echo $drug_therapy_array[0]; ?>],
          ['ก.พ.', <?php echo $drug_sell_array[1]; ?>, <?php echo $drug_occupy_array[1]; ?>, <?php echo $take_drugs_array[1]; ?>, <?php echo $drug_therapy_array[1]; ?>],
          ['มี.ค.', <?php echo $drug_sell_array[2]; ?>, <?php echo $drug_occupy_array[2]; ?>, <?php echo $take_drugs_array[2]; ?>, <?php echo $drug_therapy_array[2]; ?>],
          ['เม.ย.', <?php echo $drug_sell_array[3]; ?>, <?php echo $drug_occupy_array[3]; ?>, <?php echo $take_drugs_array[3]; ?>, <?php echo $drug_therapy_array[3]; ?>],
          // Add more months as needed
        ]);
          
        var data2 = google.visualization.arrayToDataTable([
          ['Month', 'ปืน', 'การพนัน', 'คนเข้าเมือง', 'เทคโน', 'หมายจับ' ],
          ['ม.ค.', <?php echo $gun_array[0]; ?>, <?php echo $gambling_array[0]; ?>, <?php echo $immigrant_array[0]; ?>, <?php echo $techno_array[0]; ?>, <?php echo $wanted_array[0]; ?>],
          ['ก.พ.', <?php echo $gun_array[1]; ?>, <?php echo $gambling_array[1]; ?>, <?php echo $immigrant_array[1]; ?>, <?php echo $techno_array[1]; ?>, <?php echo $wanted_array[1]; ?>],
          ['มี.ค.', <?php echo $gun_array[2]; ?>, <?php echo $gambling_array[2]; ?>, <?php echo $immigrant_array[2]; ?>, <?php echo $techno_array[2]; ?>, <?php echo $wanted_array[2]; ?>],
          ['เม.ย.', <?php echo $gun_array[3]; ?>, <?php echo $gambling_array[3]; ?>, <?php echo $immigrant_array[3]; ?>, <?php echo $techno_array[3]; ?>, <?php echo $wanted_array[3]; ?>],
          // Add more months as needed
        ]);
         
        var options = {
          title: 'Monthly Goals vs Performance',
          chartArea: {width: '50%'},
          hAxis: {
            title: 'ผลการปฏิบัติ',
            minValue: 0
          },
          vAxis: {
            title: 'เดือน'
          }
        };

        var chart = new google.visualization.BarChart(document.getElementById('chart_div'));
        chart.draw(data, options);
        var chart2 = new google.visualization.BarChart(document.getElementById('chart_div2'));
        chart2.draw(data2, options);
      }
    </script>
  </head>
  <body>
    <div id="chart_div" style="width: 100%; height: 500px;"></div>
    <div id="chart_div2" style="width: 100%; height: 500px;"></div>
  </body>
</html>

<?php
// Step 5: Close the database connection
mysqli_close($conn);
?>
