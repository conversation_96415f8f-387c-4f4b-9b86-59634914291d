<?php

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

?>

<div class="container--x">
<table class="table table-striped" > 
	<tr>	
        <th> เลขหนังสือเดินทาง </th>
		<th> ที่อยู่ </th>
		<th> หมู่ </th>
		<th> ตำบล </th>
		<th> อำเภอ </th>
		<th> จังหวัด </th>
	</tr>
<?php
$sql = "SELECT
            T1.*,
            T5.pv_name_th AS province,
            T3.ap_name_th AS amphur,
            T4.tb_name_th AS tumbon
        FROM
            wm_tb_foreigner AS T1 " . 
            "LEFT JOIN provinces AS T5 ON T1.fo_cl_province = T5.pv_code " .
            "LEFT JOIN amphures AS T3 ON T1.fo_cl_amphur = T3.ap_code " .
            "LEFT JOIN districts AS T4 ON T1.fo_cl_tumbon = T4.tb_id " ;  //ตั้งตัวแปร sql เลือกฐานข้อมูลจากตารางที่ชื่อ wm_tb_personal
    
if($pcid != ''){
	$sql = $sql . " WHERE fo_cl_passport='$pcid' " ; // ผู้ปกครอง

		
}
$resultPn = mysqli_query($conn,$sql);
// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
//$no = 1;
while($rowPn=mysqli_fetch_array($resultPn))	{//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
    
    //$age = get_personal_age_2( $row["fo_cl_age"] ); // eng    
?>
	<tr>	
        <td> <?=$rowPn["fo_cl_passport"]?> </td>
		<td align="right">&nbsp;<?=$rowPn["fo_cl_adr"]?> </td>
		<td nowrap="nowrap"> <?=$rowPn["fo_cl_moo"]?> </td>
		<td> <?=$rowPn["tumbon"]?> </td>
		<td> <?=$rowPn["amphur"]?> </td>
		<td nowrap="nowrap"> <?=$rowPn["province"]?> </td>
	</tr>
	<?php
	//	$no++;
	}
	mysqli_close($conn);	//ปิดการเชื่อมต่อฐานข้อมูล

//if($no == 1) {
	//echo "<tr><td colspan='10' style='color: red'> ไม่พบข้อมูลของ <b style='color: blue'>$people_name</b> </td></tr>";
//}
	?>

		</table>
</div>
<script>
function Del(url)
{
	if(confirm("คุณจะลบข้อมูล หรือ ไม่ ?"))
	{
		window.location = url;		
	}
}
</script>