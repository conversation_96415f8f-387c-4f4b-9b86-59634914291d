<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';


// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// สำหรับเลือก จังหวัด อำเภอ ตำบล
include("../config.inc.php");
include("../classes/class.database.inc.php");
$conn2 = get_connection(); // connect to mySQLDB

// สำหรับเลือกสถานีตำรวจทั่วประเทศ
$region = isset($_GET['region']) ? $_GET['region'] : 0;
$provincial = isset($_GET['provincial']) ? $_GET['provincial'] : 0;
//$station = isset($_GET['station']) ? $_GET['station'] : 0;

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>เพิ่มข้อมูลผู้ใช้งาน</title>
<script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/policeinnopolis/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 18px;
    }
input::placeholder {
  color: red;
}
.form-control::placeholder {
  color: sandybrown;
}
</style>
<!-- สำหรับ วันที่ แบบ พ.ศ. -->
<link rel="stylesheet" href="http://code.jquery.com/ui/1.9.1/themes/base/jquery-ui.css" />
<script src="http://code.jquery.com/jquery-1.8.2.js"></script>
<script src="/policeinnopolis/js/jquery-ui.js"></script>
</head>
    
<body>
	
	<div class="container">
	<div class="row">
	<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> เพิ่มข้อมูล </div>
	<form action="Save_members.php" method="POST" enctype="multipart/form-data" class="body">
	<span style="color: #1203F8">
        
        <label hidden="hidden">ลำดับ</label>
		<input name="MID" type = "text" class="form-control" hidden="hidden"  >
		        
        <label>Account<span style="color: #F90004">* จำเป็น</span> </label>
		<input type = "text" name = "account" class="form-control" placeholder="Account" ><br>
    
		<label>Password</label>
		<input type = "text" name = "password" class="form-control" placeholder="password" >
		
		<label>hint_pw</label>
		<input type = "text" name = "hint_pw" class="form-control" placeholder="hint_pw" >
        
        <label>ยศ ชื่อ สกุล</label>
		<input type = "text" name = "member_name" class="form-control" placeholder="ระบุชื่อ" >
				
		<label>LINE id</label>
		<input type = "text" name = "line_id" class="form-control" placeholder="LINE ID" >
		
		<label>โทรศัพท์</label>
		<input type = "text" name = "member_phone" class="form-control" placeholder="ระบุโทรศัพท์" >
		
		<label>user_level</label>
		<select id="user_level" name="user_level" class="form-select form-select-sm" placeholder="user_level">
			<option value="" selected> </option>
            <option value="1">1</option>
            <option value="2">2</option>
            <option value="100">100</option>
		</select>
		
		<label>add_data</label>
		<select id="ua_add_data" name="ua_add_data" class="form-select form-select-sm" placeholder="add_data">
			<option value="" selected> </option>
            <option value="1">1</option>
            <option value="2">2</option>
            <option value="100">100</option>
		</select>
		
		<label>ua_delete_data</label>
		<select id="ua_delete_data" name="ua_delete_data" class="form-select form-select-sm" placeholder="ua_delete_data">
			<option value="" selected> </option>
            <option value="1">1</option>
            <option value="2">2</option>
            <option value="100">100</option>
		</select>
		
		<label>ua_edit_data</label>
		<select id="ua_edit_data" name="ua_edit_data" class="form-select form-select-sm" placeholder="ua_edit_data">
			<option value="" selected> </option>
            <option value="1">1</option>
            <option value="2">2</option>
            <option value="100">100</option>
		</select>
		
		<label>ua_view_data</label>
		<select id="ua_view_data" name="ua_view_data" class="form-select form-select-sm" placeholder="ua_view_data">
			<option value="" selected> </option>
            <option value="1">1</option>
            <option value="2">2</option>
            <option value="100">100</option>
		</select>
		
		<label>สถานีตำรวจ <span style="color: #F90004">* จำเป็น</span></label><br>
                <!-- เลือกสถานีตำรวจ -->
        <label> บช. </label><br>
        <select name="region" id="region" onChange="do_region_change()" class="form-control" Required >
                <option value="">&lt;&lt; เลือก บช. &gt;&gt;</option>
                <?php
			  	$res_re = $conn2->query("SELECT * FROM `wm_tb_police_region` 
                                            ORDER BY `name_region` ASC");
			     $selected = '';
			     while($row_re = $conn2->fetch_row($res_re)) 
                     {
                      $code_region = $row_re['code_region'];
                      $name_region = $row_re['name_region'];
                      // set default provionce to 64 >> sukhothai
                          if($code_region == $region) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      echo "<option value='$code_region' $selected> $name_region </option>\n";
                     }
			  ?>
              </select>
        
        <label> บก. </label><br>
        <select name="provincial" id="provincial" onChange="do_provincial_change()" class="form-control" >
        <option value="0">&lt;&lt; เลือก บก. &gt;&gt;</option>
                <?php
                if($region > 0)
                {
                    $res_prov = $conn2->query("SELECT * FROM `wm_tb_police_provincial` 
                                                WHERE `region_code`='$region' 
                                                ORDER BY `provincial_code` ASC");
                    $selected = '';
                  while($res_prov = $conn2->fetch_row($res_prov)) 
                  {
                      $provincial_code = $res_prov['provincial_code']; // 
                      $provincial = $res_prov['provincial'];
                      // set default provionce to 64 >> sukhothai
                          if($provincial_code == $provincial) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$provincial_code' $selected> $provincial </option>\n";
                  }
                }	
                ?>
              </select>

         <label> สน./สภ. </label>
        <br>
        <select name="police_station" id="police_station" class="form-control" >
                <option value="0">&lt;&lt; เลือก สน./สภ. &gt;&gt;</option>
                <?php
	           if($provincial > 0)
                {
                    $res_st = $conn2->query("SELECT * FROM `wm_tb_police_station2` 
                                                WHERE provincial_code='$provincial_code'
                                                ORDER BY `station_code` ASC");
                    $selected = '';
                  while($row_st = $conn2->fetch_row($res_st)) 
                  {
                      $station_code = $row_st['station_code']; // 
                      $station_name = $row_st['station_name'];
                      // set default provionce to 64 >> sukhothai
                          if($station_code == $station) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$station_code' $selected> $station_name </option>\n";
                  }
                }	
                ?>                                                                     
              </select>
		<br>
			
		<label>name_position</label>
		<input type = "text" name = "name_position" class="form-control" placeholder="name_position" >
		
		<label>name_police_station</label>
		<input type = "text" name = "name_police_station" class="form-control" placeholder="name_police_station" >
        
		<label>ชื่อ บก.</label>
		<input type = "text" name = "name_provincial" class="form-control" placeholder="ชื่อ บก." >
		<!--<select id="name_provincial" name="name_provincial" class="form-select form-select-sm" placeholder="name_provincial">
			<option value="" selected> </option>
            <option value="ภ.จว.สุโขทัย">ภ.จว.สุโขทัย</option>
		</select>-->
		
        <label>ชื่อ บช.</label>
		<input type = "text" name = "name_region" class="form-control" placeholder="ชื่อ บช." >
		<!--<select id="name_region" name="name_region" class="form-select form-select-sm" placeholder="name_region">
			<option value="" selected> </option>
            <option value="ตำรวจภูธรภาค 6">ตำรวจภูธรภาค 6</option>
		</select>-->
		<br>
		<p>
			<input type="submit" value="บันทึกข้อมูล" class="btn btn-success" >
			<td> <a href="index.php?rnd=<?= rand(); ?>&page=mission" class="btn btn-warning" >ยกเลิก</a> </td>
		</p>
		<br>
		<br>
	</form>
	</div>
	</div>
	</div>
<script>
// ฟังก์ชั่น เปลี่ยนภาค (บช.) เป็น ภ.จว. (บก.)
function do_region_change()
{
	var sel_region = document.getElementById("region");		
	var code_region = sel_region.options[sel_region.selectedIndex].value;

	$.get("../ajax/get_provincial.php?region=" + code_region, 
		  function(data, status){
				//alert("Data: " + data + "\nStatus: " + status);
				// jQuery style						
				$('#provincial').find('option').remove().end();
				var data = $.trim(data);
				if(data == '') {
					alert("ไม่พบข้อมูล บก. !");
					return;
				}

					//.append('<option value="whatever">text</option>').val('whatever');
				var datas = data.split(';');
				for(var i=0; i<datas.length; i++) {
					if(datas[i] == '') continue;
					datas[i] = datas[i].split(':');
					var code_region = datas[i][0];
					var name_region = datas[i][1];
					$('#provincial').append('<option value="'+ code_region+'">' + name_region + '</option>');
				}
                // Trigger the change event on the amphur dropdown so the tambon dropdown will be populated     
                $('#provincial').trigger('change');
		});
}
    //เลือก สถานี จากจังหวัด
function do_provincial_change()
{
	var sel_provincial = document.getElementById("region");		
	var provincial_code = sel_provincial.options[sel_provincial.selectedIndex].value;
    
    var sel_provincial2 = document.getElementById("provincial");		
	var station_code = sel_provincial2.options[sel_provincial2.selectedIndex].value;

	$.get("../ajax/get_station.php?region=" + provincial_code + "&provincial=" + station_code, 
			  function(data, status){
					//alert("Data: " + data + "\nStatus: " + status);
					// jQuery style						
					$('#police_station').find('option').remove().end();
					var data = $.trim(data);
					if(data == '') {
						alert("ไม่พบข้อมูลสถานี !");
						return;
					}

						//.append('<option value="whatever">text</option>').val('whatever');
					var datas = data.split(';');
					for(var i=0; i<datas.length; i++) {
						if(datas[i] == '') continue;
						datas[i] = datas[i].split(':');
						var station_code = datas[i][0];
						var station_name = datas[i][1];
						$('#police_station').append('<option value="'+ station_code+'">' + station_name + '</option>');
					}
			});
}
</script> 
		
</body>
</html>
