<?php
include '../Condb.php';
include '../users.inc.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// เมื่อมีการใช้ ตัวแปร Session จะต้องเปิดก่อน
//session_start();  // เปิดตัวแปร session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}


$id = $_GET['id'];
$pcid = $_GET['pcid'];
$sql = "SELECT * FROM wm_tb_phone WHERE ph_cl_aid = '$id' ";
$result = mysqli_query($conn,$sql);
$row = mysqli_fetch_array($result);

$people_name = '';
if($row) 
{
	$pid2 = $row["ph_cl_idcard"];
	//
	$query1 = "SELECT ps_cl_prefix, ps_cl_name, ps_cl_surname, ps_cl_image FROM wm_tb_personal WHERE ps_cl_idcard ='$pid2' ";
	$result1 = mysqli_query($conn, $query1);
	$row1 = mysqli_fetch_array($result1);		
	$people_name = $row1['ps_cl_prefix'] . $row1['ps_cl_name'] . ' ' . $row1['ps_cl_surname'];
	$people_image = $row1["ps_cl_image"];
	if($people_image != '') {
		$people_image = "<img src='{$people_image}' height='50'> ";
	}
}

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>แก้ไขข้อมูลโทรศัพท์</title>
<script language="javascript" src="/policeinnopolis/jQuery/jquery-3.5.1.min.js"></script> 
<link rel="stylesheet" href="/policeinnopolis/bootstrap/css/bootstrap.rtl.min.css">
<script src="../SweetAlert2/dist/sweetalert2.all.min.js"></script>
<style type="text/css">

<link rel="stylesheet" href="http://code.jquery.com/ui/1.9.1/themes/base/jquery-ui.css" />
<script src="http://code.jquery.com/jquery-1.8.2.js"></script>
<script src="/policeinnopolis/js/jquery-ui.js"></script>
<script src="../TestSweetAlert2/dist/sweetalert2.all.min.js"></script>
<style type="text/css">
        body,td,th {
            color: #1203F8;
        }
            .body{
                font-size: 18px;
            }
        input::placeholder {
          color: red;
        }
        .form-control::placeholder {
          color: sandybrown;
        }
</style>
</head>

<body>
	<div class="container">
	<div class="row">
		<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> แก้ไขข้อมูลโทรศัพท์ของ <?= $people_name ?> <?= $people_image ?> </div>
	<form action="Save_all_Phone.php" method="POST" enctype="multipart/form-data" class="body">	
		<label>ลำดับ</label>
		<input name="ph_cl_aid" type = "text" class="form-control" value= "<?=$row['ph_cl_aid']?>" hidden ="hidden"  >
		<label>เลขบัตรประชาชน</label>
		<input name="ph_cl_idcard" type = "text" class="form-control" value= "<?=$row['ph_cl_idcard']?>" readonly="readonly"  >
		<label>ข้อมูลเจ้าของเบอร์ </label>		
		<input name="AA" type = "text" class="form-control" value= "<?= $people_name ?>" readonly="readonly" >
		<label>หมายเลขโทรศัพท์</label>
		<input type = "text" name = "ph_cl_phone" class="form-control" value= "<?=$row['ph_cl_phone']?>" placeholder="กรอกหมายเลขโทรศัพท์ 10 หลัก" >
		<label>เครือข่ายโทรศัพท์</label>
		<input type = "text" name = "ph_cl_operators" class="form-control" value= "<?=$row['ph_cl_operators']?>" placeholder="กรอกข้อมูลเครือข่าย เช็คโดย DTAC กด *812*เบอร์# โทรออก" >
		<label>ประเภท</label>
		<input type = "text" name = "ph_cl_type" class="form-control" value= "<?=$row['ph_cl_type']?>" placeholder="กรอกข้อมูลประเภท รายเดือน/เติมเงิน" >
		<label>วันจดทะเบียน</label>
		<input type = "text" name = "ph_cl_regist" class="form-control" value= "<?=$row['ph_cl_regist']?>" placeholder="กรอกข้อมูลวันที่จดทะเบียน จำนวนวันที่ใช้งาน"  >
		<label>การใช้งาน</label>
		<input type = "text" name = "ph_cl_status" class="form-control" value= "<?=$row['ph_cl_status']?>" placeholder="กรอกข้อมูลสถานะใช้งาน เปิด ปิด ระงับ ยกเลิก โอน การใช้บริการอื่นๆ" >
		<label>ยี่ห้อ รุ่น</label>
		<input type = "text" name = "ph_cl_brand" class="form-control" value= "<?=$row['ph_cl_brand']?>" placeholder="กรอกข้อมูลยี่ห้อโทรศัพท์ รุ่น สี" >
		<label>เลข IMEI</label>
		<input type = "text" name = "ph_cl_IMEI" class="form-control" value= "<?=$row['ph_cl_IMEI']?>" placeholder="กรอกข้อมูลเลข IMEI เช็คอีมี่โดยกด *#06#" >
		<label>เลข IMSI</label>
		<input type = "text" name = "ph_cl_IMSI" class="form-control" value= "<?=$row['ph_cl_IMSI']?>" placeholder="กรอกข้อมูลเลข IMSI ของซิมการ์ด" >
		<label>คนจดทะเบียน</label>
		<input type = "text" name = "ph_cl_register_name" class="form-control" value= "<?=$row['ph_cl_register_name']?>" placeholder="กรอกข้อมูล ชื่อคนจดทะเบียน" >
		<label>บัตรคนจด</label>
		<input type = "text" name = "ph_cl_register_idcard" class="form-control" value= "<?=$row['ph_cl_register_idcard']?>" placeholder="กรอกข้อมูล เลขบัตรคนจด" >
		<label>เบส (LAC,CI)</label>
		<input type = "text" name = "ph_cl_base" class="form-control" value= "<?=$row['ph_cl_base']?>" placeholder="กรอกข้อมูล เบส (LAC,CI)" >
		<label>ที่อยู่/ตำแหน่งเบส</label>
		<input type = "text" name = "ph_cl_base_location" class="form-control" value= "<?=$row['ph_cl_base_location']?>" placeholder="กรอกข้อมูล ที่อยู่/ตำแหน่งเบส" >
        
        <label>วันตรวจสอบ <span style="color: #F90004">* จำเป็น</span> </label>
        <p><input type="text" name="ph_ch_date_inspect" id="datepicker" class="form-control" value= "<?=$row['ph_ch_date_inspect']?>"  autocomplete="off" Required></p>
        <script>
            function set_cal(ele)//function สร้างตัวเลือกปฎิทิน
            {
              $( ele ).datepicker({
                  onSelect:(date_text)=>
                  {
                    let arr=date_text.split("/");
                    let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])+543).toString();
                    $(ele).val(new_date);
                    $(ele).css("color","");
                  },
                  beforeShow:()=>{

                    if($(ele).val()!="")
                    {
                      let arr=$(ele).val().split("/");
                      let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])-543).toString();
                      $(ele).val(new_date);

                    }

                    $(ele).css("color","white");
                  },
                  onClose:()=>{

                      $(ele).css("color","");

                      if($(ele).val()!="")
                      {
                          let arr=$(ele).val().split("-");
                          if(parseInt(arr[2])<2500)
                          {
                              let new_date=arr[0]+"-"+arr[1]+"-"+(parseInt(arr[2])).toString();
                              $(ele).val(new_date);
                          }
                      }
                  },
                  dateFormat:"yy-mm-dd", //กำหนดรูปแบบวันที่เป็น วัน/เดือน/ปี
                  changeMonth:true,//กำหนดให้เลือกเดือนได้
                  changeYear:true,//กำหนดให้เลือกปีได้
                  showOtherMonths:true,//กำหนดให้แสดงวันของเดือนก่อนหน้าได้
              });
            }
          $(document).ready(function(){
            //เรียก function set_cal เมื่อเปิดหน้าเว็บ โดยส่ง object element ที่มี id เป็น datepicker เป็นพารามิเตอร์
            set_cal( $("#datepicker") );
          })
          </script>
        <!-- สำหรับ วันที่ แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ -->
        
		<label>หมายเหตุ</label>
        <textarea class="form-control" id="ph_cl_remark" name="ph_cl_remark"  rows="5" placeholder="ระบุข้อมูลแหล่งที่มา หรืออื่น ๆที่เกี่ยวข้อง" ><? echo $row['ph_cl_remark']?></textarea>
        <br>
		<p>
    	<input type="submit" value="Update" class="btn btn-success" >
		<td> <a href="Show_All.php?pcid=<?= $pcid ?>&page=phone" class="btn btn-warning" >ยกเลิก</a> </td>
 		</p>
	</form>
	</div>
	</div>
	</div>
<script src="/js/datetimepicker2.5.1/jquery.ui.datepicker-th.js" type="text/javascript"></script>    
</body>
</html>
<!-- ให้ alert ข้างล่าง -->

<?php
if(isset($_SESSION['Success'])){  ?>
    <script>
        Swal.fire({
          icon: 'success',
          title: 'บันทึกข้อมูลเสร็จเรียบร้อย',
          text: 'Saved Successfully',
          confirmButtonText: 'OK'
        }).then((result) => {
            if (result.isConfirmed) {
                // อ่านค่า $ch_cl_idcard จาก URL แทนที่จะอ้างอิงตัวแปร
                var pcid = "<?php echo $_GET['pcid']; ?>";
                window.location.href = 'Show_All.php?pcid=' + pcid + '&page=phone';
            }
        });
    </script>

<?php
    unset($_SESSION['Success']);
}
?>


<?php
if(isset($_SESSION['error'])){  ?>   <!--//เช็คว่า ตัวแปร session เป็นค่าว่างหรือไม่ ถ้ามีข้อมูลมา ก็จะให้ทำการแจ้งเตือน -->
<!-- เขียนเป็นภาษา htmlอิสระ-->
    <script>
        Swal.fire({
          icon: 'error',
          title: 'บันทึกข้อมูลไม่สำเร็จ',
          text: '<?php echo $_SESSION['error'] ?>',
        })
    </script>

<?php
    unset($_SESSION['error']);
    
}
?>
