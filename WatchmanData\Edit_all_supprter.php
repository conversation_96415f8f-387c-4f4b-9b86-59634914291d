<?php
include '../Condb.php';
include '../users.inc.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// เมื่อมีการใช้ ตัวแปร Session จะต้องเปิดก่อน
//session_start();  // เปิดตัวแปร session
/*if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
*/

$pcid = $_GET['pcid'];
$id = $_GET['id'];

$sql = "SELECT
            *
        FROM
            wm_politician_supporter
        WHERE
            idcard_candidate='$pcid' AND `aid`='$id'";

$result = mysqli_query($conn,$sql);
$row = mysqli_fetch_array($result);


//echo $sql;
?>


<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>เลือกตั้ง 66</title>
<script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/policeinnopolis/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 18px;
    }
input::placeholder {
  color: red;
}
.form-control::placeholder {
  color: sandybrown;
}
</style>
<!-- สำหรับ วันที่ แบบ พ.ศ. -->
<link rel="stylesheet" href="http://code.jquery.com/ui/1.9.1/themes/base/jquery-ui.css" />
<script src="http://code.jquery.com/jquery-1.8.2.js"></script>
<script src="/policeinnopolis/js/jquery-ui.js"></script>
<body>
	
	<div class="container">
	<div class="row">
	<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> เพิ่มข้อมูลผู้สนับสนุน ส.ส. </div>
	<form action="Save_all_supporter.php" method="POST" enctype="multipart/form-data" class="body">
	<span style="color: #1203F8">
        
        <label hidden="hidden">ลำดับ</label>
		<input name="aid" type = "text" class="form-control" value="<?= $row['aid'] ?>" hidden="hidden"  >
		        
        <label>เลขประจำตัวประชาชน ผู้สมัคร ส.ส.</label>
		<input type = "text" name = "idcard_candidate" value="<?= $row['idcard_candidate'] ?>" class="form-control" placeholder="เลขประจำตัวประชาชน ผู้สมัคร ส.ส." required readonly>
        	
        <b>กลุ่มผู้สนับสนุนของผู้สมัครรับเลือกตั้ง ส.ส.</b>
        <label>เลขบัตร ส.จ.</label>
		<input type = "text" name = "idcard_sor_jor" value="<?= $row['idcard_sor_jor']?>" class="form-control" placeholder="ระบุเลขประจำตัวประชาชน ของ ส.จ.ที่สนับสนุน" >
        
        <label>ชื่อ-สกุล กลุ่ม ส.จ. (ตำแหน่ง)</label>
		<input type = "text" name = "sor_jor" class="form-control" value="<?= $row['sor_jor']?>" placeholder="ระบุชื่อ-สกุล (และตำแหน่ง) ของ กลุ่ม ส.จ.ที่สนับสนุน" >
        
        <label>เลขบัตร กลุ่มกำนัน/ผู้ใหญ่บ้าน</label>
		<input type = "text" name = "idcard_village_headman" value="<?= $row['idcard_village_headman']?>" class="form-control" placeholder="ระบุเลขประจำตัวประชาชน ของ กลุ่มกำนัน/ผู้ใหญ่บ้าน ที่สนับสนุน" >
        
        <label>ชื่อ-สกุล กลุ่มกำนัน/ผู้ใหญ่บ้าน (ตำแหน่ง)</label>
		<input type = "text" name = "village_headman" class="form-control" value="<?= $row['village_headman']?>" placeholder="ระบุชื่อ-สกุล (และตำแหน่ง) ของ กลุ่มกำนัน/ผู้ใหญ่บ้านที่สนับสนุน" >
        
        <label>เลขบัตร กลุ่ม อบต.</label>
		<input type = "text" name = "idcard_SAO" class="form-control" value="<?= $row['idcard_SAO']?>" placeholder="ระบุเลขประจำตัวประชาชน ของ กลุ่ม อบต. ที่สนับสนุน" >
        
        <label>ชื่อ-สกุล กลุ่ม อบต. (ตำแหน่ง)</label>
		<input type = "text" name = "SAO" class="form-control" value="<?= $row['SAO']?>" placeholder="ระบุชื่อ-สกุล (และตำแหน่ง) ของ กลุ่ม อบต. ที่สนับสนุน" >
        
        <label>เลขบัตร ผู้มีบารมีทางสังคม </label>
		<input type = "text" name = "idcard_social_prestige" value="<?= $row['idcard_social_prestige']?>" class="form-control" placeholder="ระบุเลขประจำตัวประชาชน ของ ผู้มีบารมีทางสังคม ที่สนับสนุน" >
        
        <label>ชื่อ-สกุล ผู้มีบารมีทางสังคม (ตำแหน่ง)</label>
		<input type = "text" name = "social_prestige" class="form-control" value="<?= $row['social_prestige']?>" placeholder="ระบุชื่อ-สกุล (และตำแหน่ง) ของ ผู้มีบารมีทางสังคม ที่สนับสนุน" >
        
        <label>เลขบัตร ประธานกลุ่ม อสม./สตรี/ผู้สูงอายุ </label>
		<input type = "text" name = "idcard_group_chairman" class="form-control" value="<?= $row['idcard_group_chairman']?>" placeholder="ระบุเลขประจำตัวประชาชน ของ ประธาน อสม./ประธานกลุ่มสตรี/ประธานกลุ่มผู้สูงอายุ ที่สนับสนุน" >
        
        <label>ชื่อ-สกุล ประธานกลุ่ม อสม./สตรี/ผู้สูงอายุ (ตำแหน่ง)</label>
		<input type = "text" name = "group_chairman" class="form-control" value="<?= $row['group_chairman']?>" placeholder="ระบุชื่อ-สกุล (และตำแหน่ง) ของ ประธาน อสม./ประธานกลุ่มสตรี/ประธานกลุ่มผู้สูงอายุ ที่สนับสนุน" >
        
        <label>เลขบัตร กลุ่มสถาบันการศึกษา </label>
		<input type = "text" name = "idcard_academy" class="form-control" value="<?= $row['idcard_academy']?>" placeholder="ระบุเลขประจำตัวประชาชน ของ กลุ่มสถาบันการศึกษา ที่สนับสนุน" >
        
        <label>ชื่อ-สกุล กลุ่มสถาบันการศึกษา (ตำแหน่ง)</label>
		<input type = "text" name = "academy" class="form-control" value="<?= $row['academy']?>" placeholder="ระบุชื่อ-สกุล (และตำแหน่ง) ของ กลุ่มสถาบันการศึกษา ที่สนับสนุน" >
        
        <label>เลขบัตร ผู้นำศาสนา </label>
		<input type = "text" name = "idcard_religious_leader" class="form-control" value="<?= $row['idcard_religious_leader']?>" placeholder="ระบุเลขประจำตัวประชาชน ของ ผู้นำศาสนา ที่สนับสนุน" >
        
        <label>ชื่อ-สกุล ผู้นำศาสนา (ตำแหน่ง)</label>
		<input type = "text" name = "religious_leader" class="form-control" value="<?= $row['religious_leader']?>" placeholder="ระบุชื่อ-สกุล (และตำแหน่ง) ของ ผู้นำศาสนา ที่สนับสนุน" >
        
		<br>
		<p>
			<input type="submit" value="บันทึกข้อมูล" class="btn btn-success" >
			<td> <a href="/WatchmanData/Show_All_po.php?rnd=<?= rand(); ?>&pcid=<?= $pcid ?>&page=supporter" class="btn btn-warning" >ยกเลิก</a> </td>
		</p>
		<br>
		<br>
	</form>
	</div>
	</div>
	</div>
<!--<script src="/js/datetimepicker2.5.1/jquery.ui.datepicker-th.js" type="text/javascript"></script>  -->
</body>
</html>