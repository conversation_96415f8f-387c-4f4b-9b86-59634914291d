<?php
include '../Condb.php';
include '../users.inc.php';

?>


<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>เลือกตั้ง 66</title>
<script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/policeinnopolis/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 18px;
    }
input::placeholder {
  color: red;
}
.form-control::placeholder {
  color: sandybrown;
}
</style>
<!-- สำหรับ วันที่ แบบ พ.ศ. -->
<link rel="stylesheet" href="http://code.jquery.com/ui/1.9.1/themes/base/jquery-ui.css" />
<script src="http://code.jquery.com/jquery-1.8.2.js"></script>
<script src="/policeinnopolis/js/jquery-ui.js"></script>
</head>
    
<body>
	
	<div class="container">
	<div class="row">
	<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> เพิ่มข้อมูล </div>
	<form action="Save_p_report.php" method="POST" enctype="multipart/form-data" class="body">
	<span style="color: #1203F8">
        
       <label for="tumbon">Tumbon:</label>
<select name="tumbon" id="tumbon">
  <option value="">-- Select Tumbon --</option>
  <?php
    // Fetch all the tumbons from the database and create options for the dropdown
    $query = "SELECT * FROM elect66_tumbon";
    $result = mysqli_query($conn, $query);
    while ($row = mysqli_fetch_assoc($result)) {
      echo '<option value="' . $row['tcode'] . '">' . $row['tname'] . '</option>';
    }
  ?>
</select>


<label for="unit">Unit:</label>
<select name="unit" id="unit">
  <option value="">-- Select Unit --</option>
</select>
        
        
		<br>
		<p>
			<input type="submit" value="บันทึกข้อมูล" class="btn btn-success" >
			<td> <a href="/Election_66/Show_Election_index.php?rnd=<?= rand(); ?>&page=supporter" class="btn btn-warning" >ยกเลิก</a> </td>
		</p>
		<br>
		<br>
	</form>
	</div>
	</div>
	</div>
</body>
</html>

<script>
$(document).ready(function(){
  $('#tumbon').on('change', function(){
    var tcode = $(this).val(); // Get the selected tumbon code
    if (tcode) {
      $.ajax({
        url: 'get_unit.php',
        type: 'post',
        data: {tcode: tcode},
        dataType: 'json',
        success: function(response){
          $('#unit').empty();
          $.each(response, function(key, value){
            $('#unit').append('<option value="'+ key +'">'+ value +'</option>');
          });
        }
      });
    } else {
      $('#unit').empty();
      $('#unit').append('<option value="">-- Select Unit --</option>');
    }
  });
});
</script>
    
