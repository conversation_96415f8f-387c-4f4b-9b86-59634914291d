<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../Alert.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

$ip_address = $_SERVER['REMOTE_ADDR'];
$action = 'Save law & order'; // You can set the action to a suitable value
$data = str_replace("'", "", compact_array($_POST));
log_activity($acc, $action, $data, $ip_address); // ใช้ function ในไฟล์ Condb.php

//echo '<pre>';
//print_r($_POST);
//echo '</pre>';
//
//echo '<hr>';

$lw_cl_aid=$_POST['lw_cl_aid'];
$lw_cl_type=$_POST['lw_cl_type'];
$lw_cl_no=$_POST['lw_cl_no'];
$lw_cl_heading=$_POST['lw_cl_heading'];
$lw_cl_date=$_POST['lw_cl_date'];
$lw_cl_record=$_POST['lw_cl_record'];
// save file สส.1
$file1 = $_FILES[ 'lw_cl_file' ][ 'tmp_name' ];
if ( is_uploaded_file( $file1 ) ) {
  $lw_cl_file = "uploaded/Doc/" . $_FILES[ 'lw_cl_file' ][ 'name' ];	
  move_uploaded_file( $file1, "../" . $lw_cl_file );  
} else {
  $lw_cl_file = '';
}

try{
$sql ="SELECT * FROM wm_tb_law_order WHERE lw_cl_aid = :lw_cl_aid ";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':lw_cl_aid', $lw_cl_aid);
    $stmt->execute();
    $row = $stmt->fetch(PDO::FETCH_ASSOC);

if($row) {
    $lw_cl_aid = $row['lw_cl_aid'];
    $sql = "UPDATE wm_tb_law_order SET
            lw_cl_type = :lw_cl_type,
            lw_cl_no = :lw_cl_no,
            lw_cl_heading = :lw_cl_heading,
            lw_cl_date = :lw_cl_date,
            lw_cl_record = :lw_cl_record";
        $params = [
            'lw_cl_type' => $lw_cl_type,
            'lw_cl_no' => $lw_cl_no,
            'lw_cl_heading' => $lw_cl_heading,
            'lw_cl_date' => $lw_cl_date,
            'lw_cl_record' => $lw_cl_record
        ];
    
         if ($lw_cl_file !== '') {
            $sql .= ", lw_cl_file = :lw_cl_file";
            $params['lw_cl_file'] = $lw_cl_file;
            }

            $sql .= " WHERE lw_cl_aid = :updated_lw_cl_aid";
            $params['updated_lw_cl_aid'] = $lw_cl_aid;    

}else{
        $sql = "INSERT INTO wm_tb_law_order(lw_cl_type, lw_cl_no, lw_cl_heading, lw_cl_date, lw_cl_record, lw_cl_file) 
        VALUES(:lw_cl_type, :lw_cl_no, :lw_cl_heading, :lw_cl_date, :lw_cl_record, :lw_cl_file) ";
        $params = [
            'lw_cl_type' => $lw_cl_type,
            'lw_cl_no' => $lw_cl_no,
            'lw_cl_heading' => $lw_cl_heading,
            'lw_cl_date' => $lw_cl_date,
            'lw_cl_record' => $lw_cl_record,
            'lw_cl_file' => $lw_cl_file
        ];
    }

$stmt = $pdo->prepare($sql);
$result = $stmt->execute($params);

if ($result) {
    $_SESSION['success'] = "Data has been inserted succesfully";
    showSweetAlert("Saved successfully", "บันทึกข้อมูลเรียบร้อย");
    unset($_SESSION['success']); // Clear the session variable
    header("refresh:2; url=/Bansuan/index.php?id={$lw_cl_aid}&page=law_order");
} else {
    $_SESSION['error'] = "Error";
    showSweetAlert('Error', "บันทึกข้อมูล ไม่สำเร็จ", 'error');
    unset($_SESSION['error']); // Clear the session variable
    header("refresh:2; url=/Bansuan/index.php?id={$lw_cl_aid}&page=law_order");
}
}catch(PDOException $e){
    echo 'Query Failed: ' . $e->getMessage();
}

$pdo = null;

?>