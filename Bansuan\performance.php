<script src="/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>
<script src="../SweetAlert2/dist/sweetalert2.all.min.js"></script>

<?php //PDO
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

$id = isset($_GET['pf_cl_aid']) ? $_GET['pf_cl_aid'] : '';

//get month from select or current month
$month = isset($_GET['month']) ? $_GET['month'] : date("m");
$year = isset($_GET['year']) ? $_GET['year'] : date("Y");

$m1 = '';
$m2 = '';
$m3 = '';
$m4 = '';
$m5 = '';
$m6 = '';
$m7 = '';
$m8 = '';
$m9 = '';
$m10 = '';
$m11 = '';
$m12 = '';
switch($month)
{
    case "01": $m1 = "selected"; break;
    case "02": $m2 = "selected"; break;
    case "03": $m3 = "selected"; break;
    case "04": $m4 = "selected"; break;
    case "05": $m5 = "selected"; break;
    case "06": $m6 = "selected"; break;
    case "07": $m7 = "selected"; break;
    case "08": $m8= "selected"; break;
    case "09": $m9 = "selected"; break;
    case "10": $m10 = "selected"; break;
    case "11": $m11 = "selected"; break;
    case "12": $m12 = "selected"; break;
}
$current_month = $global_thai_month[0+$month];
$y2 = $year+543;

?>
<!---->
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link rel="stylesheet" href="/bootstrap/css/bootstrap-grid.css">
<style>
    .swal2-popup {
    width: 500px !important;
    height: auto !important;
    font-size: 14px !important;
}
    @media print {
   a {
      display: none !important;
   }
}
</style>
<link rel="stylesheet" type="text/css" href="print.css" media="print">
<div class="container-fluid" align="left">
<div class=" h3 text-center  alert alert-success mb-4 mt-4 " role="alert" >ข้อมูลผลการปฏิบัติงาน ชุดสืบสวน <?= $name_station ?> ในรอบเดือน</div>
  <div>
    <table width="90%" border="0" cellspacing="1" cellpadding="1">
      <tbody>
        <tr>
          <td width="11%"><div align="left">
            &nbsp;<a href="Add_performance.php" class="btn btn-success btn-lg mb-4 <?= $add_btn ?>" >เพิ่มข้อมูล</a></td>
          <td width="17%"><label>เลือกปี </label>
            <select id="pf_cl_year" name="pf_cl_year" class="form-select col-8 d-lg-inline" style="background-color: #00D2FE; font-size: 16px" onChange="dates_change()" >
              <option value="" >เลือก</option>
              <?php
                $cur_y = date("Y") + 2;
                for($y=2020; $y<$cur_y; $y++) {
                    $sel = ($y == $year) ? "selected" : '';
                    echo "<option value='$y' $sel> ". ($y + 543) ." </option>";
                }
            ?>
            </select></td>
          <td><label>เดือน</label>
            <select id="pf_cl_month" name="pf_cl_month" class="form-select col-8 d-lg-inline" style="background-color: #00D2FE; font-size: 16px" onChange="dates_change()">
              <option value="">เลือก</option>
              <option <?= $m1 ?> value="01">มกราคม</option>
              <option <?= $m2 ?> value="02">กุมภาพันธ์</option>
              <option <?= $m3 ?> value="03">มีนาคม</option>
              <option <?= $m4 ?> value="04">เมษายน</option>
              <option <?= $m5 ?> value="05">พฤษภาคม</option>
              <option <?= $m6 ?> value="06">มิถุนายน</option>
              <option <?= $m7 ?> value="07">กรกฎาคม</option>
              <option <?= $m8 ?> value="08">สิงหาคม</option>
              <option <?= $m9 ?> value="09">กันยายน</option>
              <option <?= $m10 ?> value="10">ตุลาคม</option>
              <option <?= $m11 ?> value="11">พฤศจิกายน</option>
              <option <?= $m12 ?> value="12">ธันวาคม</option>
            </select></td>
          <td width="35%">&nbsp;<a href="grahp_performance.php" class="btn btn-primary btn-lg mb-4" >กราฟข้อมูล</a>&nbsp;</td>
			<?php
			if ($station == 6707) {
            echo '<td width="14%"><!--<img src="../images/image_img.png" width="40">--><a href="Show_worldcup2022.php" class="btn btn-danger btn-lg mb-4" >World Cup 2022</a></td>';
				} else{ 
					}
				?>
        </tr>
      </tbody>
    </table>
    </div>
<h1 style="font-size: 24px ;background-color: yellow ;padding: 5px" h1 align="center" >ตารางแสดงผลการปฏิบัติ ในการกวาดล้างประจำเดือน &nbsp;<?= $current_month ?>&nbsp;<?= $y2 ?></h1>
<table width="100%" height="160" border="3" cellpadding="1" cellspacing="1" class="table-bordered table-hover mb-4 mt-4" >
  <tbody>
    <tr >
      <td rowspan="2" bgcolor="#C9C7C7" style="text-align: center; color: #000;" nowrap="nowrap">วันที่</td>
      <td colspan="5" bgcolor="#F3F803" style="text-align: center">พ.ร.บ.ยาเสพติด</td>
      <td colspan="2" bgcolor="#FF2124" style="color: #FBF9F9; text-align: center;">พ.ร.บ.อาวุธปืน</td>
      <td colspan="2" bgcolor="#2EFC00" style="text-align: center">พ.ร.บ.การพนัน</td>
      <td colspan="2" bgcolor="#0947ED" style="color: #F9F3F3; text-align: center;">พ.ร.บ.คนเข้าเมือง</td>
      <td colspan="2" bgcolor="#EAA61C" style="text-align: center">เทคโนโลยี</td>
      <td rowspan="2" bgcolor="#0CD4FA" style="text-align: center">หมายจับ</td>
      <td rowspan="2" bgcolor="#C9C7C7" style="text-align: center">หมายเหตุ</td>
    </tr>
    <tr>
      <td bgcolor="#F3F803" style="text-align: center">&nbsp;จำหน่าย&nbsp;</td>
      <td bgcolor="#F3F803" style="text-align: center" nowrap="nowrap">&nbsp;ครอบครอง&nbsp;</td>
      <td bgcolor="#F3F803" style="text-align: center" nowrap="nowrap">&nbsp;เสพ&nbsp;</td>
      <td bgcolor="#F3F803" style="text-align: center" nowrap="nowrap">&nbsp;คัดกรอง&nbsp;</td>
      <td bgcolor="#F3F803" style="text-align: center">&nbsp;ของกลาง&nbsp;</td>
      <td bgcolor="#FF2124" style="color: #FBF9F9; text-align: center;">&nbsp;ราย&nbsp;</td>
      <td bgcolor="#FF2124" style="color: #FBF9F9; text-align: center;">ของกลาง</td>
      <td bgcolor="#2EFC00" style="text-align: center">&nbsp;ราย&nbsp;</td>
      <td bgcolor="#2EFC00" style="text-align: center">&nbsp;คน&nbsp;</td>
      <td bgcolor="#0947ED" style="color: #F9F3F3; text-align: center;">&nbsp;ราย&nbsp;</td>
      <td bgcolor="#0947ED" style="color: #F9F3F3; text-align: center;">&nbsp;คน&nbsp;</td>
      <td bgcolor="#EAA61C" style="text-align: center">&nbsp;ราย&nbsp;</td>
      <td bgcolor="#EAA61C" style="text-align: center">&nbsp;คน&nbsp;</td>
    </tr>
	  
<?php
$sql = "SELECT * FROM wm_tb_performance WHERE station='$station' AND (pf_cl_type_main='กวาดล้าง') AND (MONTH(pf_cl_dates)=:month AND YEAR(pf_cl_dates)=:year) ORDER BY pf_cl_dates"; 

if($id != ''){
    $sql = $sql . " AND pf_cl_aid = :id";
}

$stmt = $pdo->prepare($sql);
$stmt->bindParam(':month', $month);
$stmt->bindParam(':year', $year);

if($id != ''){
    $stmt->bindParam(':id', $id);
}

$stmt->execute();

$no = 1;

$pf_cl_drug_sell = 0;
$pf_cl_drug_occupy = 0;
$pf_cl_take_drugs = 0;
$pf_cl_drug_therapy = 0;
$pf_cl_gun = 0;
$pf_cl_gambling_case = 0;
$pf_cl_gambling_person = 0;
$pf_cl_immigrant_case = 0;
$pf_cl_immigrant_person = 0;
$pf_cl_techno_case = 0;
$pf_cl_techno_person = 0;
$pf_cl_wanted_qty = 0;

while($rowPf = $stmt->fetch(PDO::FETCH_ASSOC)) {
    //ฟังก์ชั่น วันที่ ดึงจาก condb
    if($rowPf["pf_cl_dates"]!=''){
        $strDate = DateThai( $rowPf["pf_cl_dates"] );
    }else{
        $strDate = "";
    }
    
  //  print_r($strDate);
?>
  
<tr>
      <td style="text-align: center" nowrap="nowrap" ><?= $strDate ?></td>
	  <td style="text-align: center; font-weight: bold"><?= (is_numeric($rowPf["pf_cl_drug_sell"]) ? $rowPf["pf_cl_drug_sell"] : "") ?></td>
      <td style="text-align: center; font-weight: bold"><?= (is_numeric($rowPf["pf_cl_drug_occupy"]) ? $rowPf["pf_cl_drug_occupy"] : "") ?></td>
      <td style="text-align: center; font-weight: bold"><?= (is_numeric($rowPf["pf_cl_take_drugs"]) ? $rowPf["pf_cl_take_drugs"] : "") ?></td>
      <td style="text-align: center; font-weight: bold"><?= (is_numeric($rowPf["pf_cl_drug_therapy"]) ? $rowPf["pf_cl_drug_therapy"] : "") ?></td>
      <td style="text-align: center"><?= $rowPf["pf_cl_items_drug"]?></td>
      <td style="text-align: center; font-weight: bold"><?= (is_numeric($rowPf["pf_cl_gun"]) ? $rowPf["pf_cl_gun"] : "") ?></td>
      <td style="text-align: center"><?= $rowPf["pf_cl_items_gun"]?></td>
      <td style="text-align: center; font-weight: bold"><?= (is_numeric($rowPf["pf_cl_gambling_case"]) ? $rowPf["pf_cl_gambling_case"] : "") ?></td>
      <td style="text-align: center; font-weight: bold"><?= (is_numeric($rowPf["pf_cl_gambling_person"]) ? $rowPf["pf_cl_gambling_person"] : "") ?></td>
      <td style="text-align: center; font-weight: bold"><?= (is_numeric($rowPf["pf_cl_immigrant_case"]) ? $rowPf["pf_cl_immigrant_case"] : "") ?></td>
      <td style="text-align: center; font-weight: bold"><?= (is_numeric($rowPf["pf_cl_immigrant_person"]) ? $rowPf["pf_cl_immigrant_person"] : "") ?></td>
      <td style="text-align: center; font-weight: bold"><?= (is_numeric($rowPf["pf_cl_techno_case"]) ? $rowPf["pf_cl_techno_case"] : "") ?></td>
      <td style="text-align: center; font-weight: bold"><?= (is_numeric($rowPf["pf_cl_techno_person"]) ? $rowPf["pf_cl_techno_person"] : "") ?></td>
      <td style="text-align: center; font-weight: bold"><?= (is_numeric($rowPf["pf_cl_wanted_qty"]) ? $rowPf["pf_cl_wanted_qty"] : "") ?></td>
      <td ><?= $rowPf["pf_cl_remark"]?></td>
</tr>  
<?php
      
    $pf_cl_drug_sell += intval($rowPf["pf_cl_drug_sell"]);
	$pf_cl_drug_occupy += intval($rowPf["pf_cl_drug_occupy"]); 
	$pf_cl_take_drugs += intval($rowPf["pf_cl_take_drugs"]);
	$pf_cl_drug_therapy += intval($rowPf["pf_cl_drug_therapy"]);
	$pf_cl_gun += intval($rowPf["pf_cl_gun"]);
	$pf_cl_gambling_case += intval($rowPf["pf_cl_gambling_case"]);
	$pf_cl_gambling_person += intval($rowPf["pf_cl_gambling_person"]);
	$pf_cl_immigrant_case += intval($rowPf["pf_cl_immigrant_case"]);
	$pf_cl_immigrant_person += intval($rowPf["pf_cl_immigrant_person"]);
	$pf_cl_techno_case += intval($rowPf["pf_cl_techno_case"]);
	$pf_cl_techno_person += intval($rowPf["pf_cl_techno_person"]);
	$pf_cl_wanted_qty += intval($rowPf["pf_cl_wanted_qty"]);

    $no++;
}//while
?>
      <tr style="font-size: 20px; border: #676767 solid">
        <td nowrap="nowrap" bgcolor="#C9C7C7" style="text-align: center" ><strong>ยอดรวม</strong></td>
        <td bgcolor="#F3F803" style="text-align: center; font-weight: bold"><?= $pf_cl_drug_sell ?></td>
        <td bgcolor="#F3F803" style="text-align: center; font-weight: bold"><?= $pf_cl_drug_occupy ?></td>
        <td bgcolor="#F3F803" style="text-align: center; font-weight: bold"><?= $pf_cl_take_drugs ?></td>
        <td bgcolor="#F3F803" style="text-align: center; font-weight: bold"><?= $pf_cl_drug_therapy ?></td>
        <td bgcolor="#F3F803" style="text-align: center">&nbsp;</td>
        <td bgcolor="#FF2124" style="color: #FBF9F9; text-align: center; font-weight: bold"><?= $pf_cl_gun ?></td>
        <td bgcolor="#FF2124" style="color: #FBF9F9; text-align: center">&nbsp;</td>
        <td bgcolor="#2EFC00" style="text-align: center; font-weight: bold"><?= $pf_cl_gambling_case ?></td>
        <td bgcolor="#2EFC00" style="text-align: center; font-weight: bold"><?= $pf_cl_gambling_person ?></td>
        <td bgcolor="#0947ED" style="color: #F9F3F3; text-align: center; font-weight: bold"><?= $pf_cl_immigrant_case ?></td>
        <td bgcolor="#0947ED" style="color: #F9F3F3; text-align: center; font-weight: bold"><?= $pf_cl_immigrant_person ?></td>
        <td bgcolor="#EAA61C" style="text-align: center; font-weight: bold"><?= $pf_cl_techno_case ?></td>
        <td bgcolor="#EAA61C" style="text-align: center; font-weight: bold"><?= $pf_cl_techno_person ?></td>
        <td bgcolor="#0CD4FA" style="text-align: center; font-weight: bold"><?= $pf_cl_wanted_qty ?></td>
        <td style="text-align: center" >&nbsp;</td>
      </tr>
  </tbody>
</table>
</div>
    <br>	
    <hr>


<?php
$sql2 = "SELECT * FROM wm_tb_performance WHERE station='$station' AND (pf_cl_type_main='กวาดล้าง2') AND (MONTH(pf_cl_dates) = :month AND YEAR(pf_cl_dates) = :year) ";
            
if($id != ''){
	$sql2 = $sql2 . " WHERE pf_cl_aid = :id ";
}
    
$stmt2 = $pdo->prepare($sql2);
$stmt2->bindParam(':month', $month);
$stmt2->bindParam(':year', $year);

if($id != ''){
    $stmt2->bindParam(':id', $id);
}

$resultPf2 = $stmt2->execute();
          
    // Check ว่า มีข้อมูล กวาดล้าง 2 หรือไม่ ถ้ามี ให้แสดงตาราง กวาดล้าง 2
if(($resultPf2) > 0) {
  // Query to retrieve data for table 2
  $sql2 = "SELECT * FROM wm_tb_performance WHERE station='$station' AND pf_cl_type_main='กวาดล้าง2' AND MONTH(pf_cl_dates) = :month AND YEAR(pf_cl_dates) = :year ORDER BY pf_cl_dates";
  // Execute the query
    $stmt2 = $pdo->prepare($sql2);
    $stmt2->bindParam(':month', $month);
    $stmt2->bindParam(':year', $year);
    $stmt2->execute();
    $resultPf2 = $stmt2->rowCount();
    
// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no2 = 1;

// อ้างอิง ไว้ใช้สำหรับคำนวณ ยอดจับกุม
$pf_cl_drug_sell2 = 0;
$pf_cl_drug_occupy2 = 0;
$pf_cl_take_drugs2 = 0;
$pf_cl_drug_therapy2 = 0;
$pf_cl_gun2 = 0;
$pf_cl_gambling_case2 = 0;
$pf_cl_gambling_person2 = 0;
$pf_cl_immigrant_case2 = 0;
$pf_cl_immigrant_person2 = 0;
$pf_cl_techno_case2 = 0;
$pf_cl_techno_person2 = 0;
$pf_cl_wanted_qty2 = 0;
    
  // Display table 2
  // ...
?>
          <!-- ตารางกวาดล้าง รอบ 2 ปิดไว้ก่อน เมื่อมีข้อมูลค่อยมาเปิดใช้งาน -->

<h1 style="font-size: 24px ;background-color:bisque ;padding: 5px" h1 align="center" >ตารางแสดงผลการปฏิบัติ ในการกวาดล้างประจำเดือน (รอบ 2)</h1>
<table width="100%" height="160" border="3" cellpadding="1" cellspacing="1" class="table-bordered table-hover mb-4 mt-4" >
  <tbody>
    <tr >
      <td rowspan="2" bgcolor="#C9C7C7" style="text-align: center; color: #000;" nowrap="nowrap">วันที่</td>
      <td colspan="5" bgcolor="#F3F803" style="text-align: center">พ.ร.บ.ยาเสพติด</td>
      <td colspan="2" bgcolor="#FF2124" style="color: #FBF9F9; text-align: center;">พ.ร.บ.อาวุธปืน</td>
      <td colspan="2" bgcolor="#2EFC00" style="text-align: center">พ.ร.บ.การพนัน</td>
      <td colspan="2" bgcolor="#0947ED" style="color: #F9F3F3; text-align: center;">พ.ร.บ.คนเข้าเมือง</td>
      <td colspan="2" bgcolor="#EAA61C" style="text-align: center">เทคโนโลยี</td>
      <td rowspan="2" bgcolor="#0CD4FA" style="text-align: center">หมายจับ</td>
      <td rowspan="2" bgcolor="#C9C7C7" style="text-align: center">หมายเหตุ</td>
    </tr>
    <tr>
      <td bgcolor="#F3F803" style="text-align: center">&nbsp;จำหน่าย&nbsp;</td>
      <td bgcolor="#F3F803" style="text-align: center" nowrap="nowrap">&nbsp;ครอบครอง&nbsp;</td>
      <td bgcolor="#F3F803" style="text-align: center" nowrap="nowrap">&nbsp;เสพ&nbsp;</td>
      <td bgcolor="#F3F803" style="text-align: center" nowrap="nowrap">&nbsp;คัดกรอง&nbsp;</td>
      <td bgcolor="#F3F803" style="text-align: center">&nbsp;ของกลาง&nbsp;</td>
      <td bgcolor="#FF2124" style="color: #FBF9F9; text-align: center;">&nbsp;ราย&nbsp;</td>
      <td bgcolor="#FF2124" style="color: #FBF9F9; text-align: center;">ของกลาง</td>
      <td bgcolor="#2EFC00" style="text-align: center">&nbsp;ราย&nbsp;</td>
      <td bgcolor="#2EFC00" style="text-align: center">&nbsp;คน&nbsp;</td>
      <td bgcolor="#0947ED" style="color: #F9F3F3; text-align: center;">&nbsp;ราย&nbsp;</td>
      <td bgcolor="#0947ED" style="color: #F9F3F3; text-align: center;">&nbsp;คน&nbsp;</td>
      <td bgcolor="#EAA61C" style="text-align: center">&nbsp;ราย&nbsp;</td>
      <td bgcolor="#EAA61C" style="text-align: center">&nbsp;คน&nbsp;</td>
    </tr>

<?php

while($rowPf2 = $stmt->fetch(PDO::FETCH_ASSOC))	//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
{

 if($rowPf2["pf_cl_dates"]!=''){
        $strDate2 = DateThai( $rowPf2["pf_cl_dates"] );
    }else{
        $strDate2 = "";
    }
?>
	  
    <tr>
      <td style="text-align: center" nowrap="nowrap" ><?= $strDate2 ?></td>
	  <td style="text-align: center; font-weight: bold"><?= (($rowPf2["pf_cl_drug_sell"] > 0) ? $rowPf2["pf_cl_drug_sell"]  : "") ?></td>
      <td style="text-align: center; font-weight: bold"><?= (($rowPf2["pf_cl_drug_occupy"] > 0) ? $rowPf2["pf_cl_drug_occupy"] : "") ?></td>
      <td style="text-align: center; font-weight: bold"><?= (($rowPf2["pf_cl_take_drugs"] > 0) ? $rowPf2["pf_cl_take_drugs"] : "") ?></td>
      <td style="text-align: center; font-weight: bold"><?= (($rowPf2["pf_cl_drug_therapy"] > 0) ? $rowPf2["pf_cl_drug_therapy"] : "") ?></td>
      <td style="text-align: center"><?= $rowPf2["pf_cl_items_drug"]?></td>
      <td style="text-align: center; font-weight: bold"><?= (($rowPf2["pf_cl_gun"] > 0) ? $rowPf2["pf_cl_gun"] : "") ?></td>
      <td style="text-align: center"><?= $rowPf2["pf_cl_items_gun"]?></td>
      <td style="text-align: center; font-weight: bold"><?= (($rowPf2["pf_cl_gambling_case"] > 0) ? $rowPf2["pf_cl_gambling_case"] : "") ?></td>
      <td style="text-align: center; font-weight: bold"><?= (($rowPf2["pf_cl_gambling_person"] > 0) ? $rowPf2["pf_cl_gambling_person"] : "") ?></td>
      <td style="text-align: center; font-weight: bold"><?= (($rowPf2["pf_cl_immigrant_case"] > 0) ? $rowPf2["pf_cl_immigrant_case"] : "") ?></td>
      <td style="text-align: center; font-weight: bold"><?= (($rowPf2["pf_cl_immigrant_person"] > 0) ? $rowPf2["pf_cl_immigrant_person"] : "") ?></td>
      <td style="text-align: center; font-weight: bold"><?= (($rowPf2["pf_cl_techno_case"] > 0) ? $rowPf2["pf_cl_techno_case"] : "") ?></td>
      <td style="text-align: center; font-weight: bold"><?= (($rowPf2["pf_cl_techno_person"] > 0) ? $rowPf2["pf_cl_techno_person"] : "") ?></td>
      <td style="text-align: center; font-weight: bold"><?= (($rowPf2["pf_cl_wanted_qty"] > 0) ? $rowPf2["pf_cl_wanted_qty"] : "") ?></td>
      <td ><?= $rowPf2["pf_cl_remark"]?></td>
    </tr>    

<?php
    $no2++;
}
?>
      
<?php
      if(($resultPf2) > 0) {
          $sql2_1 = "SELECT * FROM wm_tb_performance WHERE station='$station' AND pf_cl_type_main='กวาดล้าง2' AND MONTH(pf_cl_dates) = :month AND YEAR(pf_cl_dates) = :year ORDER BY pf_cl_dates";
            // Execute the query
            $stmt = $pdo->prepare($sql2_1);
            $stmt->bindParam(':month', $month);
            $stmt->bindParam(':year', $year);
            $stmt->execute();
            $resultPf2_1 = $stmt->rowCount();

          //echo $sql2_1;
      while($rowPf2_1 = $stmt->fetch(PDO::FETCH_ASSOC))
      {
            $pf_cl_drug_sell2_1 += intval($rowPf2_1["pf_cl_drug_sell"]);
            $pf_cl_drug_occupy2_1 += intval($rowPf2_1["pf_cl_drug_occupy"]);
            $pf_cl_take_drugs2_1 += intval($rowPf2_1["pf_cl_take_drugs"]);
            $pf_cl_drug_therapy2_1 += intval($rowPf2_1["pf_cl_drug_therapy"]);
            $pf_cl_gun2_1 += intval($rowPf2_1["pf_cl_gun"]);
            $pf_cl_gambling_case2_1 += intval($rowPf2_1["pf_cl_gambling_case"]);
            $pf_cl_gambling_person2_1 += intval($rowPf2_1["pf_cl_gambling_person"]);
            $pf_cl_immigrant_case2_1 += intval($rowPf2_1["pf_cl_immigrant_case"]);
            $pf_cl_immigrant_person2_1 += intval($rowPf2_1["pf_cl_immigrant_person"]);
            $pf_cl_techno_case2_1 += intval($rowPf2_1["pf_cl_techno_case"]);
            $pf_cl_techno_person2_1 += intval($rowPf2_1["pf_cl_techno_person"]);
            $pf_cl_wanted_qty2_1 += intval($rowPf2_1["pf_cl_wanted_qty"]);
      }        
          
    ?>
      <tr style="font-size: 20px">
        <td nowrap="nowrap" bgcolor="#C9C7C7" style="text-align: center" ><strong>ยอดรวม</strong></td>
        <td bgcolor="#F3F803" style="text-align: center; font-weight: bold"><?= $pf_cl_drug_sell2_1 ?></td>
        <td bgcolor="#F3F803" style="text-align: center; font-weight: bold"><?= $pf_cl_drug_occupy2_1 ?></td>
        <td bgcolor="#F3F803" style="text-align: center; font-weight: bold"><?= $pf_cl_take_drugs2_1 ?></td>
        <td bgcolor="#F3F803" style="text-align: center; font-weight: bold"><?= $pf_cl_drug_therapy2_1 ?></td>
        <td bgcolor="#F3F803" style="text-align: center">&nbsp;</td>
        <td bgcolor="#FF2124" style="color: #FBF9F9; text-align: center; font-weight: bold"><?= $pf_cl_gun2_1 ?></td>
        <td bgcolor="#FF2124" style="color: #FBF9F9; text-align: center">&nbsp;</td>
        <td bgcolor="#2EFC00" style="text-align: center; font-weight: bold"><?= $pf_cl_gambling_case2_1 ?></td>
        <td bgcolor="#2EFC00" style="text-align: center; font-weight: bold"><?= $pf_cl_gambling_person2_1 ?></td>
        <td bgcolor="#0947ED" style="color: #F9F3F3; text-align: center; font-weight: bold"><?= $pf_cl_immigrant_case2_1 ?></td>
        <td bgcolor="#0947ED" style="color: #F9F3F3; text-align: center; font-weight: bold"><?= $pf_cl_immigrant_person2_1 ?></td>
        <td bgcolor="#EAA61C" style="text-align: center; font-weight: bold"><?= $pf_cl_techno_case2_1 ?></td>
        <td bgcolor="#EAA61C" style="text-align: center; font-weight: bold"><?= $pf_cl_techno_person2_1 ?></td>
        <td bgcolor="#0CD4FA" style="text-align: center; font-weight: bold"><?= $pf_cl_wanted_qty2_1 ?></td>
        <td style="text-align: center" >&nbsp;</td>
      </tr>
      
<?php
      }
?>

  </tbody>
</table>
<?php
	 }
?>

<br>	
    <hr>
    
<!--  โค้ดตาราง ผลงานกวาดล้าง 2 ถึง จุดนี้ -->
			
<h1 style="font-size: 24px ;background-color: yellow ;padding: 5px " h1 align="center" >ตารางผลการปฏิบัติงานและจับกุมทั้งหมด ในรอบเดือน &nbsp;<?= $current_month ?> &nbsp;<?= $y2 ?> &nbsp;</h1>
<table width="100%" height="94" border="1" cellpadding="1" cellspacing="1" class="table-bordered table-hover mb-4 mt-4">
  <tbody>
    <tr>
	  <td height="44" bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">ลำดับ</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;" nowrap="nowrap">วันที่</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;" nowrap="nowrap">การปฏิบัติ</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">ประเภทความผิด</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">หมายจับ</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">การจับกุม</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">ชื่อผู้ต้องหา</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">เลขบัตร</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">หมายค้น</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">พบ/ไม่พบ</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">รายการที่พบ</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">ผู้บันทึกข้อมูล</td>
	  <td colspan="4" bgcolor="#995D5D"></td>
    </tr>
	  
<?php
$sql3 = "SELECT * FROM wm_tb_performance " . 
        "WHERE station='$station' AND MONTH(pf_cl_dates) = :month AND YEAR(pf_cl_dates) = :year ORDER BY pf_cl_dates ASC";  //ตั้งตัวแปร sql เลือกฐานข้อมูลจากตารางที่ชื่อ wm_tb_personal
    
    $stmt = $pdo->prepare($sql3);
    $stmt->bindParam(':month', $month);
    $stmt->bindParam(':year', $year);
    $stmt->execute();

// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no3 = 1;
while($rowPf3 = $stmt->fetch(PDO::FETCH_ASSOC))	//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
{
        //ฟังก์ชั่น วันที่ ดึงจาก condb
 $strDate3 = DateThai( $rowPf3["pf_cl_dates"] );
?>	

    <tr>
	  <td> <?= $no3 ?> </td>
      <td nowrap="nowrap"><?= $strDate3 ?></td>
      <td nowrap="nowrap">&nbsp;<?= $rowPf3["pf_cl_type_main"]?>&nbsp;</td>
      <td>&nbsp;<?= $rowPf3["pf_cl_type_sub"]?>&nbsp;</td>
      <td>&nbsp;<?= $rowPf3["pf_cl_wanted"]?>&nbsp;</td>
      <td>&nbsp;<?= $rowPf3["pf_cl_catch"]?>&nbsp;</td>
      <td style="text-align: left" nowrap>&nbsp;<?= $rowPf3["pf_cl_suspect_name"]?>&nbsp;</td>
      <td>&nbsp;<?= $rowPf3["pf_cl_suspect_idcard"]?>&nbsp;</td>
      <td>&nbsp;<?= $rowPf3["pf_cl_search_warrant"]?>&nbsp;</td>
      <td>&nbsp;<?= $rowPf3["pf_cl_find"]?>&nbsp;</td>
      <td>&nbsp;<?= $rowPf3["pf_cl_illegal"]?>&nbsp;</td>
      <td>&nbsp;<?= $rowPf3["pf_cl_record"]?>&nbsp;</td>
	  <td>&nbsp;&nbsp;<a href="Edit_performance.php?id=<?= $rowPf3["pf_cl_aid"] ?>&pcid=<?= $rowPf3["pf_cl_suspect_idcard"]?>" class="btn btn-warning mb-4 <?= $edit_btn ?>" >แก้ไข</a></td>
	  <td>&nbsp;&nbsp;<button href="#" class="btn btn-danger mb-4" onClick="deleteItem(<?= $rowPf3['pf_cl_aid'] ?>)"<?=$del_btn?>>ลบ</button></td>
      <td>&nbsp;&nbsp;<a href="https://invest.watchman1.com/WatchmanData/Show_All.php?pcid=<?=$rowPf3["pf_cl_suspect_idcard"]?> " class="btn btn-primary mb-4" target="_blank">ข้อมูล</a> </td>
        
        
    </tr>
  </tbody>
<?php
	$no3++;
}//while
?>
</table>
<script src="/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>
<script src="../SweetAlert2/dist/sweetalert2.all.min.js"></script>
<script>
function deleteItem(id) {
    Swal.fire({
        title: 'คุณต้องการลบข้อมูลนี้ใช่ไหม?',
        text: "หลังจากลบแล้ว จะไม่สามารถกู้คืนได้",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'ใช่, ยืนยันลบ',
        cancelButtonText: 'ยกเลิก'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = '/Bansuan/Del_performance.php?id=' + id;
        }
    });
}
</script>

<script>
function dates_change()  // คำสั่งเปลี่ยนเดือน ปี
{
    var ms = $("#pf_cl_month option:selected").val();
    var ys = $("#pf_cl_year option:selected").val();
    window.location = "/Bansuan/index.php?page=performance&month=" + ms + "&year=" + ys + "&rnd=" + Math.random();
}
</script>
