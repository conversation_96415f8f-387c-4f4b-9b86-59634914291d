<?php
include '../Condb.php';
include '../users.inc.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// เมื่อมีการใช้ ตัวแปร Session จะต้องเปิดก่อน
//session_start();  // เปิดตัวแปร session
/*if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
*/

$id_cl_aid = $_POST['id_cl_aid'];
$id_cl_idcard = $_POST[ 'id_cl_idcard' ];
$id_cl_no = $_POST[ 'id_cl_no' ];
$id_cl_date = $_POST[ 'id_cl_date' ];
$id_cl_expire = $_POST[ 'id_cl_expire' ];
$id_cl_phone = $_POST[ 'id_cl_phone' ];
$id_cl_detail = $_POST[ 'id_cl_detail' ];
$id_cl_remark = $_POST[ 'id_cl_remark' ];

// save Image
$file1 = $_FILES[ 'id_cl_image' ][ 'tmp_name' ];
if ( is_uploaded_file( $file1 ) ) {
  $id_cl_image = "/policeinnopolis/uploaded/Image1/" . basename( $_FILES[ 'id_cl_image' ][ 'name' ] );	
  move_uploaded_file( $file1, "../" . $id_cl_image );  
} else {
  $id_cl_image = '';
}
//php >> $[AA-z]


$sql = "UPDATE wm_tb_idcard SET " .
		"id_cl_idcard = '$id_cl_idcard', " .
		"id_cl_no = '$id_cl_no', " .
		"id_cl_date = '$id_cl_date', " .
		"id_cl_expire = '$id_cl_expire', " .	
		"id_cl_phone = '$id_cl_phone', " .
		"id_cl_detail = '$id_cl_detail', " .
		"id_cl_remark = '$id_cl_remark' " .
		(($id_cl_image == '') ? '' : (",id_cl_image = '$id_cl_image' ")) .  //ใช้ตรวจสอบไฟล์ภาพ ว่ามีอยู่หรือไม่ ถ้ามีไม่ต้องอัพเดต
		"WHERE id_cl_aid = '$id_cl_aid' ";

//ตรวจสอบข้อมูลในคอลัมภ์ เดิม
/*	if($id_cl_image !== '')
	{
		$sql =  $sql . ", id_cl_image = '$id_cl_image' ";
	}

	$sql = $sql . " WHERE id_cl_aid = '$id_cl_aid' "; */
	

$result=mysqli_query($conn,$sql);
if($result){
	echo "<script>alert('บันทึกข้อมูลเรียบร้อย');</script>";	
}else{
	echo "<script>alert(\"บันทึกข้อมูลไม่สำเร็จ\\n" . print_r(mysqli_error($conn), true) . "\");</script>";
}

// เก็บข้อมูล Action สำหรับ EDIT
$inputs = str_replace("'", "", compact_array($_POST));
$prev = str_replace("'", "", implode(',', $row_0));

$acc = $user['account'];
$sql3 = "INSERT wm_tb_user_action (at_cl_user,at_cl_date,at_cl_action,at_cl_input,at_cl_prev) VALUES('$acc',CURRENT_TIMESTAMP,'Edit idcard {$id_cl_idcard}', '$inputs', '{$prev}')";
mysqli_query($conn, $sql3);
//

mysqli_close($conn);

echo "<script>window.location='Show_All.php?pcid=" . $id_cl_idcard . "&page=idcard';</script>";
?>

