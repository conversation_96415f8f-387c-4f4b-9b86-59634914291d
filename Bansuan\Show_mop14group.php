<?php
include '../Condb.php';
include '../users.inc.php';
include '../right_user.php';
include '../Head_image.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// เมื่อมีการใช้ ตัวแปร Session จะต้องเปิดก่อน
//session_start();  // เปิดตัวแปร session
/*if (session_status() == PHP_SESSION_NONE) {
    session_start();
}*/

//print_r($user);
// เฉพาะ ยูเซอร์ บ้านเสวน = 1 เท่านั้นที่จะดูหน้านี้ได้
if($user['ua_view_data'] != 1)
{
	header("Location: /WatchmanData/main.php");
}

$sql_all = "SELECT " .
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='27' AND station='6707') AS T27, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='28' AND station='6707') AS T28, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='29' AND station='6707') AS T29, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='30' AND station='6707') AS T30, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='31' AND station='6707') AS T31, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='32' AND station='6707') AS T32, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='33' AND station='6707') AS T33, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='34' AND station='6707') AS T34, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='35' AND station='6707') AS T35, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='36' AND station='6707') AS T36, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='37' AND station='6707') AS T37, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='38' AND station='6707') AS T38, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='39' AND station='6707') AS T39, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='40' AND station='6707') AS T40, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='41' AND station='6707') AS T41, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='42' AND station='6707') AS T42, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='43' AND station='6707') AS T43, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='44' AND station='6707') AS T44, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='45' AND station='6707') AS T45, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='46' AND station='6707') AS T46, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='47' AND station='6707') AS T47, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='48' AND station='6707') AS T48, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='49' AND station='6707') AS T49, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='50' AND station='6707') AS T50, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='51' AND station='6707') AS T51, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='52' AND station='6707') AS T52, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='53' AND station='6707') AS T53, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='54' AND station='6707') AS T54, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='55' AND station='6707') AS T55, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='56' AND station='6707') AS T56, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='57' AND station='6707') AS T57, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='58' AND station='6707') AS T58, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='59' AND station='6707') AS T59, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='60' AND station='6707') AS T60, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='61' AND station='6707') AS T61, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='62' AND station='6707') AS T62, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='63' AND station='6707') AS T63, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='64' AND station='6707') AS T64, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='65' AND station='6707') AS T65, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='66' AND station='6707') AS T66, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='67' AND station='6707') AS T67, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='68' AND station='6707') AS T68, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='69' AND station='6707') AS T69, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='70' AND station='6707') AS T70, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='71' AND station='6707') AS T71, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='72' AND station='6707') AS T72, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='73' AND station='6707') AS T73, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='74' AND station='6707') AS T74, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='75' AND station='6707') AS T75, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='76' AND station='6707') AS T76, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='77' AND station='6707') AS T77, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='78' AND station='6707') AS T78, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='79' AND station='6707') AS T79, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='80' AND station='6707') AS T80, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='81' AND station='6707') AS T81, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='82' AND station='6707') AS T82, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='83' AND station='6707') AS T83, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='84' AND station='6707') AS T84, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='85' AND station='6707') AS T85, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='86' AND station='6707') AS T86, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='87' AND station='6707') AS T87, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='88' AND station='6707') AS T88, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='89' AND station='6707') AS T89, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='90' AND station='6707') AS T90, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='91' AND station='6707') AS T91, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='92' AND station='6707') AS T92, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='93' AND station='6707') AS T93, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='94' AND station='6707') AS T94, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='95' AND station='6707') AS T95, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='96' AND station='6707') AS T96, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='97' AND station='6707') AS T97, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='98' AND station='6707') AS T98, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='99' AND station='6707') AS T99, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='100' AND station='6707') AS T100, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='101' AND station='6707') AS T101, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='102' AND station='6707') AS T102, " .  //
            "(SELECT COUNT(*) FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='103' AND station='6707') AS T103 " .
            "";
$res = mysqli_query($conn, $sql_all);
$row_all = mysqli_fetch_array($res);

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
        <base target="_top">
        <title>ระบบ WatchmanDB</title>
		<link rel="stylesheet" href="../bootstrap/css/bootstrap.rtl.min.css">
		<link rel="stylesheet" href="../bootstrap-3.4.1-dist/css/bootstrap.min.css">
  		<script src="../jQuery/jquery-3.6.1.min.js"></script>
 		<script src="../bootstrap-3.4.1-dist/js/bootstrap.min.js"></script>
        <style>
            body {
              font-family: 'Courier New', Courier, monospace;
              font-size: 16px;
            }
        </style>
</head>

<body>
<div>
  <?php
    include('../users_info.php');
    ?>	
</div>

<div class=" h3 text-center  alert alert-danger mb-2 mt-2 " role="alert" >ข้อมูล 14 กลุ่มบุคคลเกี่ยวข้องกับการชุมนุม ที่ต้องเฝ้าระวัง <?= $name_station ?></div>
    <div style="flex: auto">
        &nbsp;<a href="/Bansuan/?rnd=<?= rand(); ?>&page=information" class="btn btn-primary btn-lg mb-4" >กลับ</a>&nbsp;
    </div>
    
<div class="container">
<table width="95%" border="1" cellspacing="1" cellpadding="1" class="table-striped table-hover table-bordered">
  <tbody>
    <tr>
      <td height="40" width="68%" bgcolor="#1D04F3" style="text-align: center"><strong>รายการ</strong></td>
      <td height="40" width="11%" bgcolor="#1D04F3" style="text-align: center" ><strong>จำนวน</strong></td>
      <td height="40" width="21%" bgcolor="#1D04F3" style="text-align: center"><strong>หมายเหตุ</strong></td>
      
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;1. กลุ่มผู้ปฏิบัติการ เพจ หรือ แอดมิน</td>
      <td style="text-align: center">&nbsp;0&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;2. กลุ่มแกนนำเครือข่าย</td>
      <td style="text-align: center">&nbsp;0&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;3. กลุ่มสนับสนุนแกนนำ</td>
      <td style="text-align: center">&nbsp;0&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;4. กลุ่มสนับสนุนอาวุธ</td>
      <td style="text-align: center">&nbsp;0&nbsp;</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;5. กลุ่มสนับสนุนเสบียงอาหาร</td>
      <td style="text-align: center">&nbsp;0&nbsp;</td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;6. กลุ่มสนับสนุนด้านการพยาบาล ยารักษาโรค</td>
      <td height="40" style="font-size: 18px; text-align: center">&nbsp;0&nbsp;</td>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;7. กลุ่มระดมช่วยเหลือเงินทุน</td>
      <td height="40" style="font-size: 18px; text-align: center">&nbsp;0&nbsp;</td>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;8. กลุ่มพื้นที่การชุมนุม เส้นทางหลบหนี จุดนัดพบปะ</td>
      <td height="40" style="font-size: 18px; text-align: center">&nbsp;0&nbsp;</td>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;9. กลุ่ม NGO ที่ให้การสนับสนุน</td>
      <td height="40" style="font-size: 18px; text-align: center">&nbsp;0&nbsp;</td>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;10. กลุ่มสนับสนุนงบประมาณในการต่อสู้ทางกฎหมาย และ จ้างทนายความ</td>
      <td height="40" style="font-size: 18px; text-align: center">&nbsp;0&nbsp;</td>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;11. กลุ่มสื่อออนไลน์ ที่มีความเห็นต่าง</td>
      <td height="40" style="font-size: 18px; text-align: center">&nbsp;0&nbsp;</td>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;12. กลุ่มบุคคลสาธารณะ ผู้จูงใจ เช่น เน็ตไอดอล นักแสดง</td>
      <td height="40" style="font-size: 18px; text-align: center">&nbsp;0&nbsp;</td>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;13. กลุ่มชี้นำทางความคิดทั้งในและต่างประเทศ</td>
      <td height="40" style="font-size: 18px; text-align: center">&nbsp;0&nbsp;</td>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;14. กลุ่มอื่นๆ เช่น ผู้ได้รับความเดือดร้อน , ยื่นถวายฎีกา</td>
      <td height="40" style="font-size: 18px; text-align: center">&nbsp;0&nbsp;</td>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;</td>
      </tr>
  </tbody>
</table>
    <br>
    <div style="flex: auto" align="right">
        &nbsp;&nbsp;&nbsp;<a href="/Bansuan/?rnd=<?= rand(); ?>&page=information" class="btn btn-primary btn-lg mb-4" >กลับ</a>&nbsp;
    </div>
    <hr>
</div>
    
</body>
</html>