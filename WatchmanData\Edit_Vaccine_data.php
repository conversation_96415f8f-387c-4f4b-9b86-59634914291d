<?php
include '../Condb.php';
include '../users.inc.php';
include '../right_user.php';


// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// เมื่อมีการใช้ ตัวแปร Session จะต้องเปิดก่อน
//session_start();  // เปิดตัวแปร session
/*if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
*/
$id = $_GET['id'];
$pcid = $_GET['pcid'];
$sql = "SELECT * FROM wm_tb_vaccine WHERE va_cl_aid = '$id' ";
$result = mysqli_query($conn,$sql);
$row = mysqli_fetch_array($result);

$people_name = '';
if($row) 
{
	$pid2 = $row["va_cl_idcard"];
	//
	$query1 = "SELECT ps_cl_prefix, ps_cl_name, ps_cl_surname, ps_cl_image FROM wm_tb_personal WHERE ps_cl_idcard ='$pid2' ";
	$result1 = mysqli_query($conn, $query1);
	$row1 = mysqli_fetch_array($result1);		
	$people_name = $row1['ps_cl_prefix'] . $row1['ps_cl_name'] . ' ' . $row1['ps_cl_surname'];
	$people_image = $row1["ps_cl_image"];
	if($people_image != '') {
		$people_image = "<img src='{$people_image}' height='50'> ";
	}
}

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>แก้ไขข้อมูลการรับวัคซีน</title>
<link rel="stylesheet" href="/policeinnopolis/bootstrap/css/bootstrap.rtl.min.css">
<!-- สำหรับ วันที่ แบบ พ.ศ. -->
<link rel="stylesheet" href="http://code.jquery.com/ui/1.9.1/themes/base/jquery-ui.css" />
<script src="http://code.jquery.com/jquery-1.8.2.js"></script>
<script src="/policeinnopolis/js/jquery-ui.js"></script>
</head>

<body>
	<div class="container">
	<div class="row">
		<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> แก้ไขข้อมูลการรับวัคซีน ของ <?= $people_name ?> <?= $people_image ?> </div>
	<form action="Save_Vaccine_data.php" method="POST" enctype="multipart/form-data">
        
		<label> ลำดับ </label>
		<input name="va_cl_aid" type = "text" class="form-control" value= "<?=$row['va_cl_aid']?>" hidden ="hidden"  >
		<label> เลขบัตรประชาชน </label>
		<input name="va_cl_idcard" type = "text" class="form-control" value= "<?=$row['va_cl_idcard']?>" readonly="readonly"  >
		<label> ข้อมูลเจ้าของข้อมูล </label>		
		<input name="AA" type = "text" class="form-control" value= "<?= $people_name ?>" readonly="readonly" >
		<label> โทรศัพท์ </label>
		<input type = "text" name = "va_cl_phone" class="form-control" value= "<?=$row['va_cl_phone']?>" placeholder="ระบุหมายเลขโทรศัพท์ (ถ้ามี)" >
		<label> ประเภท </label>
		<input type = "text" name = "va_cl_type" class="form-control" value= "<?=$row['va_cl_type']?>" placeholder="ระบุประเภทของบุคคล" >
		<label> กลุ่มบุคคล </label>
		<input type = "text" name = "va_cl_group" class="form-control" value= "<?=$row['va_cl_group']?>" placeholder="ระบุกลุ่มบุคคล" >
		<label> เข็มที่ </label>
		<input type = "text" name = "va_cl_syringe" class="form-control" value= "<?=$row['va_cl_syringe']?>" placeholder="ระบุเข็มที่รับวัคซีน" >
		<label> วันที่รับวัคซีน </label>
        <p><input type="text" name="va_cl_date" id="datepicker" value= "<?=$row['va_cl_date']?>" class="form-control" placeholder="ระบุวันที่รับวัคซีน" autocomplete="off" ></p>
        <script>
            function set_cal(ele)//function สร้างตัวเลือกปฎิทิน
            {
              $( ele ).datepicker({
                  onSelect:(date_text)=>
                  {
                    let arr=date_text.split("/");
                    let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])+543).toString();
                    $(ele).val(new_date);
                    $(ele).css("color","");
                  },
                  beforeShow:()=>{

                    if($(ele).val()!="")
                    {
                      let arr=$(ele).val().split("/");
                      let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])-543).toString();
                      $(ele).val(new_date);

                    }

                    $(ele).css("color","white");
                  },
                  onClose:()=>{

                      $(ele).css("color","");

                      if($(ele).val()!="")
                      {
                          let arr=$(ele).val().split("-");
                          if(parseInt(arr[2])<2500)
                          {
                              let new_date=arr[0]+"-"+arr[1]+"-"+(parseInt(arr[2])).toString();
                              $(ele).val(new_date);
                          }
                      }
                  },
                  dateFormat:"yy-mm-dd", //กำหนดรูปแบบวันที่เป็น วัน/เดือน/ปี
                  changeMonth:true,//กำหนดให้เลือกเดือนได้
                  changeYear:true,//กำหนดให้เลือกปีได้
                  showOtherMonths:true,//กำหนดให้แสดงวันของเดือนก่อนหน้าได้
              });
            }
          $(document).ready(function(){
            //เรียก function set_cal เมื่อเปิดหน้าเว็บ โดยส่ง object element ที่มี id เป็น datepicker เป็นพารามิเตอร์
            set_cal( $("#datepicker") );
          })
          </script>
        <!-- สำหรับ วันที่ แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ -->
        
		<label> ชนิดวัคซีน </label>
		<input type = "text" name = "va_cl_vaccine_type" class="form-control" value= "<?=$row['va_cl_vaccine_type']?>" placeholder="ระบุชนิดของวัคซีน" >
		<label> สถานพยาบาล </label>
		<input type = "text" name = "va_cl_hospital" class="form-control" value= "<?=$row['va_cl_hospital']?>" placeholder="ระบุสถานพยาบาลที่ฉีดวัคซีน" >
        <label> ข้อมูลอื่น ๆ</label>
        <textarea class="form-control" id="va_cl_other" name="va_cl_other" rows="5" placeholder="ระบุข้อมูลอื่น ๆที่เกี่ยวข้อง" ><? echo $row['va_cl_other']?></textarea>
		<br>
		<p>
			<input type="submit" value="Update" class="btn btn-success" >
			<td> <a href="main.php?pcid=<?= rand() ?>" class="btn btn-warning" >ยกเลิก</a> </td>
		</p>
	</form>
	</div>
	</div>
	</div>
</body>
</html>