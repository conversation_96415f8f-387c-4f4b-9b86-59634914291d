<?php
include '../Condb.php'; //PDO
include '../users.inc.php';
include '../right_user.php';


// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

$pcid = $_GET['pcid'];

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Add Progress</title>
<script src="/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 18px;
    }
input::placeholder {
  color: red;
}
.form-control::placeholder {
  color: sandybrown;
}
</style>
<!-- สำหรับ วันที่ แบบ พ.ศ. -->
<link rel="stylesheet" href="http://code.jquery.com/ui/1.9.1/themes/base/jquery-ui.css" />
<script src="http://code.jquery.com/jquery-1.8.2.js"></script>
<script src="/js/jquery-ui.js"></script>
</head>

<body>
	<div class="container">
	<div class="row">
		<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> เพิ่มข้อมูลการติดตามหมายจับ </div>
	<form action="Save_progress.php" method="POST" enctype="multipart/form-data" class="body">
        
        <label hidden="hidden">ลำดับ</label>
		<input name="pg_cl_aid" type = "text" class="form-control" hidden="hidden"  >
        
        <label> เลขบัตรประชาชนผู้ต้องหา <span style="color: #F90004">* จำเป็น</span> </label>
        <input type = "text" name="pg_cl_idcard" class="form-control" value="<?= $pcid ?>" readonly="readonly" >

        <label>วันที่ติดตามผล <span style="color: #F90004">* จำเป็น</span> </label>
        <p><input type="text" name="pg_cl_date" id="datepicker" class="form-control" placeholder="เพื่อข้อมูลถูกต้อง กรุณาเลือกจากปฎิทินด้านล่าง" autocomplete="off" required></p>
              <script>
            function set_cal(ele)//function สร้างตัวเลือกปฎิทิน
            {
              $( ele ).datepicker({
                  onSelect:(date_text)=>
                  {
                    let arr=date_text.split("/");
                    let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])+543).toString();
                    $(ele).val(new_date);
                    $(ele).css("color","");
                  },
                  beforeShow:()=>{

                    if($(ele).val()!="")
                    {
                      let arr=$(ele).val().split("/");
                      let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])-543).toString();
                      $(ele).val(new_date);

                    }

                    $(ele).css("color","white");
                  },
                  onClose:()=>{

                      $(ele).css("color","");

                      if($(ele).val()!="")
                      {
                          let arr=$(ele).val().split("-");
                          if(parseInt(arr[2])<2500)
                          {
                              let new_date=arr[0]+"-"+arr[1]+"-"+(parseInt(arr[2])).toString();
                              $(ele).val(new_date);
                          }
                      }
                  },
                  dateFormat:"yy-mm-dd", //กำหนดรูปแบบวันที่เป็น วัน/เดือน/ปี
                  changeMonth:true,//กำหนดให้เลือกเดือนได้
                  changeYear:true,//กำหนดให้เลือกปีได้
                  showOtherMonths:true,//กำหนดให้แสดงวันของเดือนก่อนหน้าได้
              });
            }
          $(document).ready(function(){
            //เรียก function set_cal เมื่อเปิดหน้าเว็บ โดยส่ง object element ที่มี id เป็น datepicker เป็นพารามิเตอร์
            set_cal( $("#datepicker") );
          })
          </script>
        <!-- สำหรับ วันที่ แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ -->

        <label> ความคืบหน้า </label>
        <input type = "text" name = "pg_cl_progress" class="form-control" placeholder="รายละเอียดความคืบหน้า" >

        <label> การดำเนินการ </label>
        <textarea class="form-control" id="pg_cl_action" name="pg_cl_action"  rows="5" placeholder="ระบุการดำเนินการ หรือสั่งการ" ></textarea>
        
        <label> ผู้สืบสวน </label>
		<input type = "text" name = "pg_cl_investigator" class="form-control" placeholder="ระบุชื่อเจ้าหน้าที่ผู้สืบสวน" >
		        
        <label> หมายเหตุ </label>
        <input type = "text" name = "pg_cl_remark" class="form-control" placeholder="หมายเหตุอื่น ๆ (ถ้ามี)" >
        
        <div class="mb-3">
			<label for="formFileMultiple" class="form-label">รายงานการสืบสวน</label>
			<input class="form-control" type="file" id="pg_cl_file" name="pg_cl_file" multiple>
		</div>
        
        <br>
        <p>
            <input type="submit" value="บันทึกข้อมูล" class="btn btn-success" >
            <td> <a href="progress.php?pcid=<?= $pcid ?> " class="btn btn-warning" >ยกเลิก</a> </td>
        </p>
		<br>
		<br>
	</form>
	</div>
	</div>
	</div>
</body>
</html>