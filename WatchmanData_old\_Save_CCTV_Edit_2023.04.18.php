<?php
include '../Condb.php';
include '../users.inc.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// เมื่อมีการใช้ ตัวแปร Session จะต้องเปิดก่อน
//session_start();  // เปิดตัวแปร session
/*if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
*/

//echo '<pre>';
//print_r($_POST);
//echo '</pre>';
//
//echo '<hr>';
//exit();

$cc_cl_aid = $_POST[ 'cc_cl_aid' ];
$cc_cl_type = $_POST[ 'cc_cl_type' ];
$cc_cl_owner = $_POST[ 'cc_cl_owner' ];
$cc_cl_install = $_POST[ 'cc_cl_install' ];
$cc_cl_place = $_POST[ 'cc_cl_place' ];
$cc_cl_province = $_POST[ 'cc_cl_province' ];
$cc_cl_amphur = $_POST[ 'cc_cl_amphur' ];
$cc_cl_tumbon = $_POST[ 'cc_cl_tumbon' ];
$cc_cl_moo = $_POST[ 'cc_cl_moo' ];
$cc_cl_adr = $_POST[ 'cc_cl_adr' ];
$cc_cl_admin = $_POST[ 'cc_cl_admin' ];
$cc_cl_phone = $_POST[ 'cc_cl_phone' ];
$cc_cl_record = $_POST[ 'cc_cl_record' ];
$cc_cl_status = $_POST[ 'cc_cl_status' ];
$cc_cl_date = $_POST[ 'cc_cl_date' ];
$cc_cl_lat = $_POST[ 'cc_cl_lat' ];
$cc_cl_lon = $_POST[ 'cc_cl_lon' ];
$region = $_POST[ 'region' ];
$provincial = $_POST[ 'provincial' ];
$station = $_POST[ 'station' ];
$cc_cl_remark = $_POST[ 'cc_cl_remark' ];

// save Image ภาพกล้อง ใหม่แทน
$file1 = $_FILES[ 'cc_cl_cctv_picture' ][ 'tmp_name' ];

if ( is_uploaded_file( $file1 ) ) {
    //ลบรูปเก่าในเซิร์ฟเวอร์ก่อน
    $sql_pic = "SELECT * FROM wm_tb_cctv WHERE cc_cl_aid='cc_cl_aid' ";
    $res_pic = mysqli_query($conn,$sql_pic);
    if($row_pic = mysqli_fetch_array($res_pic))
    {
        if(($row_pic['cc_cl_cctv_picture'] != '') && file_exists($row_pic['cc_cl_cctv_picture'])) {
            unlink( $row_pic['cc_cl_cctv_picture'] );
        }    
    }
    //เปลี่ยนชื่อไฟล์ใหม่
    $cc_cl_cctv_picture = "/policeinnopolis/uploaded/Image1/" . basename( $_FILES[ 'cc_cl_cctv_picture' ][ 'name' ] );	    
    // file + ext    
    $ext = strrchr($cc_cl_cctv_picture, ".");    
    $cc_cl_cctv_picture = "../policeinnopolis/uploaded/Image1/File_" . time() . $ext; // file+time+ext        
    move_uploaded_file( $file1, $cc_cl_cctv_picture );  
} 
else {
  $cc_cl_cctv_picture = '';
}

// save Image ภาพมุมกล้อง ใหม่แทน
$file2 = $_FILES[ 'cc_cl_cctv_view' ][ 'tmp_name' ];

if ( is_uploaded_file( $file2 ) ) {
    //ลบรูปเก่าในเซิร์ฟเวอร์ก่อน
    $sql_viw = "SELECT * FROM wm_tb_cctv WHERE cc_cl_aid='cc_cl_aid' ";
    $res_viw = mysqli_query($conn,$sql_viw);
    if($row_viw = mysqli_fetch_array($res_viw))
    {
        if(($row_viw['cc_cl_cctv_view'] != '') && file_exists($row_viw['cc_cl_cctv_view'])) {
            unlink( $row_viw['cc_cl_cctv_view'] );
        }    
    }
    //เปลี่ยนชื่อไฟล์ใหม่
    $cc_cl_cctv_view = "/policeinnopolis/uploaded/Image1/" . basename( $_FILES[ 'cc_cl_cctv_view' ][ 'name' ] );	    
    // file + ext    
    $ext = strrchr($cc_cl_cctv_view, ".");    
    $cc_cl_cctv_view = "../policeinnopolis/uploaded/Image1/File_" . time() . $ext; // file+time+ext        
    move_uploaded_file( $file2, $cc_cl_cctv_view );  
} 
else {
  $cc_cl_cctv_view = '';
}


$sql = "UPDATE wm_tb_cctv SET " .
		"cc_cl_type = '$cc_cl_type'," .
        "cc_cl_owner = '$cc_cl_owner', ".
        "cc_cl_install = '$cc_cl_install', ".
        "cc_cl_place = '$cc_cl_place', ".
        "cc_cl_province = '$cc_cl_province', ".
        "cc_cl_amphur = '$cc_cl_amphur', ".
        "cc_cl_tumbon = '$cc_cl_tumbon', ".
        "cc_cl_moo = '$cc_cl_moo', ".
        "cc_cl_adr = '$cc_cl_adr', ".
        "cc_cl_admin = '$cc_cl_admin', ".
        "cc_cl_phone = '$cc_cl_phone', ".
        "cc_cl_record = '$cc_cl_record', ".
        "cc_cl_status = '$cc_cl_status', ".
        "cc_cl_date = '$cc_cl_date', ".
        "cc_cl_lat = '$cc_cl_lat', ".
        "cc_cl_lon = '$cc_cl_lon', ".
        "cc_cl_pol_station = '$cc_cl_pol_station', ".
        "cc_cl_remark = '$cc_cl_remark' ";

        if($cc_cl_cctv_picture !== '')
            {
                $sql =  $sql . ", cc_cl_cctv_picture = '$cc_cl_cctv_picture' ";
            }

        elseif($cc_cl_cctv_view !== '')
            {
                $sql =  $sql . ", cc_cl_cctv_view = '$cc_cl_cctv_view' ";
            }
        $sql = $sql . " WHERE cc_cl_aid = '$cc_cl_aid' ";

$result=mysqli_query($conn, $sql);

if($result){
	echo "<script>alert('บันทึกข้อมูลเรียบร้อย');</script>";	
}else{
	echo "<script>alert(\"บันทึกข้อมูลไม่สำเร็จ\\n" . print_r(mysqli_error($conn), true) . "\");</script>";
}

// เก็บข้อมูล Action
$inputs = str_replace("'", "", compact_array($_POST));
$prev = "";//str_replace("'", "", implode(',', $row_0));

$acc = $user['account'];
$sql3 = "INSERT wm_tb_user_action (at_cl_user,at_cl_date,at_cl_action,at_cl_input,at_cl_prev) VALUES('$acc',CURRENT_TIMESTAMP,'Add new CCTV {$cc_cl_install}', '$inputs', '{$prev}')";
mysqli_query($conn, $sql3);
//

mysqli_close($conn);

echo "<script>window.location='Show_CCTV.php?pcid=" . $cc_cl_aid . " ';</script>";

?>

