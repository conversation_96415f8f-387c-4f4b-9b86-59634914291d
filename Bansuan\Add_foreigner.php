<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';


// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']); 

// สำหรับเลือก จังหวัด อำเภอ ตำบล
include("../config.inc.php");
include("../classes/class.database.inc.php");
$conn2 = get_connection(); // connect to mySQLDB

// สำหรับเลือกสถานีตำรวจทั่วประเทศ
$region = isset($_GET['region']) ? $_GET['region'] : 0;
$provincial = isset($_GET['provincial']) ? $_GET['provincial'] : 0;
//$station = isset($_GET['station']) ? $_GET['station'] : 0;

$province = isset($_GET['province']) ? $_GET['province'] : 64; // sukhothai
$amphure = isset($_GET['amphure']) ? $_GET['amphure'] : 0;
$tambon = isset($_GET['tambon']) ? $_GET['tambon'] : 0;

?>

<!doctype html>
<html><head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>เพิ่มข้อมูลบุคคลต่างด้าว</title>
<script src="/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 18px;
    }
input::placeholder {
  color: red;
}
.form-control::placeholder {
  color: sandybrown;
}
</style>
<!--<script src="/jquery-ui-1.12.1/jquery-ui.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/jquery-ui-1.12.1/jquery-ui.css">
<link rel="stylesheet" href="/js/datetimepicker2.5.1/jquery.datetimepicker.min.css">
<script src="/js/datetimepicker2.5.1/jquery.datetimepicker.full.js" type="text/javascript"></script>    
<script src="/js/utils.js?<?= rand() ?>" type="text/javascript"></script> -->

<title>jQuery UI Datepicker - Display month &amp; year menus</title>
<link rel="stylesheet" href="//code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">
<link rel="stylesheet" href="/resources/demos/style.css">
<script src="https://code.jquery.com/jquery-3.6.0.js"></script>
<script src="https://code.jquery.com/ui/1.13.2/jquery-ui.js"></script>

<!--script language="javascript" src="../jQuery/jquery-3.6.1.min.js"></script -->    
    
<!--  เลือกเพศอัตโนมัต -->
<script>
    function doPrefixChange()
    {
        var selected = $("#fo_cl_prefix_eng option:selected").val();
        if(selected == "Mr.") {
            $("#fo_cl_sex1").prop("checked", true);
        }
        else if(selected == "Miss"){
            $("#fo_cl_sex2").prop("checked", true);
        }
        else if(selected == "Mrs."){
            $("#fo_cl_sex2").prop("checked", true);
        }
        else {
            $("#fo_cl_sex1").prop("checked", false);
            $("#fo_cl_sex2").prop("checked", false);
            $("#fo_cl_sex3").prop("checked", false);
        }
    }
</script>

</head>

<body>
	
	<div class="container">
	<div class="row">
	<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> เพิ่มข้อมูลบุคคลต่างด้าว </div>
	<form action="Save_foreigner.php" method="POST" enctype="multipart/form-data" class="body">
	<span style="color: #1203F8">
        
        <label hidden ="hidden">ลำดับ</label>
		<input type = "text" name = "fo_cl_aid" class="form-control"  hidden ="hidden" >
        
        <label>ประเภทหนังสือเดินทาง</label>
		<input type = "text" name = "fo_cl_type_passport" class="form-control" placeholder="ระบุประเภทหนังสือเดินทาง"   >
        
        <label>เลขหนังสือเดินทาง <span style="color: #F90004">* จำเป็น</span></label>
		<input type = "text" name = "fo_cl_passport" class="form-control" placeholder="ระบุเลขหนังสือเดินทาง" Required  >
        
        <label>สัญชาติ</label>
		<input type = "text" name = "fo_cl_nationality" class="form-control" placeholder="ระบุสัญชาติ"   >
        
        <label>คำนำหน้า</label>
        <select id="fo_cl_prefix_eng" name="fo_cl_prefix_eng" class="form-select form-select-sm" aria-label=".form-select-sm example" onChange="doPrefixChange()">
            <option value="" selected> </option>
            <option value="Mr.">Mr.</option>
            <option value="Mrs.">Mrs.</option>
            <option value="Miss">Miss</option>
        </select>
        
        <label>เพศ</label><br>
              <input name="fo_cl_sex" id="fo_cl_sex1" type="radio" value="ชาย" checked="checked" />ชาย</label>
              <label><input name="fo_cl_sex" id="fo_cl_sex2" type="radio" value="หญิง" />หญิง</label>				<label><input name="fo_cl_sex" id="fo_cl_sex3" type="radio" value="LGBTQ" />LGBTQ</label><br>
        
        <label>ชื่อต้น<span style="color: #F90004">* จำเป็น</span> </label>
        <input type = "text" name = "fo_cl_name" class="form-control" placeholder="First name" Required >
        
        <label>ชื่อกลาง</label>
        <input type = "text" name = "fo_cl_midname" class="form-control" placeholder="Middle name" >
        
        <label>สกุล</label>
        <input type = "text" name = "fo_cl_surname" class="form-control" placeholder="Last name" >
        
        <label>วันเกิด</label>
               <p><input type="text" name="fo_cl_birthday" id="datepicker" placeholder="เพื่อข้อมูลถูกต้อง กรุณาเลือกจากปฎิทินด้านล่าง" class="form-control"   autocomplete="off" ></p>
              <script>
              $( function() {
                    $.datepicker.setDefaults( $.datepicker.regional[ "th" ] );

                      var currentTime = new Date();
                      var year = currentTime.getFullYear();

                      // date
                      $("#datepicker").datepicker({
                        changeMonth: true,
                        changeYear: true,
                        yearSuffix: year+543,
                        yearRange: '-100:+0',
                        dateFormat: 'yy-mm-dd'
                      });
                   } );
              </script>

        <hr>
        <b>ข้อมูลที่อยู่</b><br>
        <!-- เลือกจังวัด อำเภอ ตำบล อัตโนมัติ -->
        <label> จังหวัด </label><br>
        <select name="fo_cl_province" id="fo_cl_province" onChange="do_province_change()" class="form-control" >
            <option value="0">&lt;&lt; เลือกจังหวัด &gt;&gt;</option>
                <?php
			  	$res_p = $conn2->query("SELECT * FROM `provinces` ORDER BY `provinces`.`pv_name_th` ASC");
			     $selected = '';
			     while($row_p = $conn2->fetch_row($res_p)) 
			     {
				  $pv_code = $row_p['pv_code'];
				  $pv_name = $row_p['pv_name_th'];
				  // set default provionce to 64 >> sukhothai
				  if($pv_code == $province) {
					  $selected = 'selected';
				  }
				  else {
					  $selected = '';
				  }
				  //
				  echo "<option value='$pv_code' $selected> $pv_name </option>\n";
			     }
			  
			  ?>
              </select>
        
        <label> อำเภอ </label><br>
        <select name="fo_cl_amphur" id="fo_cl_amphur" onChange="do_amphure_change()" class="form-control" >
        <option value="0">&lt;&lt; เลือกอำเภอ &gt;&gt;</option>
            <?php
            if($province > 0)
            {
                $res_a = $conn2->query("SELECT * FROM `amphures` WHERE province_code='$province' ORDER BY `amphures`.`ap_name_th` ASC");
                $selected = '';
              while($row_a = $conn2->fetch_row($res_a)) 
              {
                  $ap_code = $row_a['ap_code']; // 
                  $ap_name = $row_a['ap_name_th'];
                  // set default provionce to 64 >> sukhothai
                  if($ap_code == $amphure) {
                      $selected = 'selected';
                  }
                  else {
                      $selected = '';
                  }
                  //
                  echo "<option value='$ap_code' $selected> $ap_name </option>\n";
              }
            }	
            ?>
          </select>
        
         <label> ตำบล </label>
        <br>
        <select name="fo_cl_tumbon" id="fo_cl_tumbon" class="form-control" >
                <option value="0">&lt;&lt; เลือกตำบล &gt;&gt;</option>
                <?php
	           if($amphure > 0)
                {
                    $res_t = $conn2->query("SELECT * FROM `districts` WHERE amphure_code='$amphure' ORDER BY `districts`.`tb_name_th` ASC");
                    $selected = '';
                  while($row_t = $conn2->fetch_row($res_t)) 
                  {
                      $tb_code = $row_t['tb_id']; // 
                      $tb_name = $row_t['tb_name_th'];
                      // set default provionce to 64 >> sukhothai
                      if($tb_code == $tambon) {
                          $selected = 'selected';
                      }
                      else {
                          $selected = '';
                      }
                      //
                      echo "<option value='$tb_code' $selected> $tb_name </option>\n";
                  }
                }	
                ?>                                                                     
              </select>

        <label> หมู่บ้าน/ชุมชน <span style="color: #F90004">* จำเป็น</span> </label><br>
        <input type = "text" name = "fo_cl_moo" class="form-control" placeholder="ระบุหมู่บ้าน/ชุมชน" Required >
        
        <label>ถนน</label>
        <input type = "text" name = "fo_cl_road" class="form-control" placeholder="ถนน" >
        
        <label>อาคาร</label>
        <input type = "text" name = "fo_cl_adr_building" class="form-control" placeholder="อาคาร" >
        
        <label>บ้านเลขที่</label>
        <input type = "text" name = "fo_cl_adr" class="form-control" placeholder="ที่อยู่ในไทย" >
        
        <hr>
        <b>ข้อมูลการเดินทาง</b><br>
        
        <label>มาจากประเทศ</label>
        <input type = "text" name = "fo_cl_country" class="form-control" placeholder="มาจากประเทศ" >
        
        <label>วันเดินทางเข้า</label>
		<input type = "text" name="fo_cl_date_in" class="form-control datepicker"  placeholder="วันเดินทางเข้า" autocomplete="off" > 
        
        		
        <label>ช่องทางเข้าประเทศ</label>
        <input type = "text" name = "fo_cl_imm_chk_in" class="form-control" placeholder="ชองทางเข้าประเทศ หรือด่าน ตม." >
        
        <label>ได้รับอนุญาตให้อยู่ถึงวันที่</label>
		<input type = "text" name="fo_cl_stay_exp" class="form-control datepicker"  placeholder="ได้รับอนุญาตให้อยู่ถึงวันที่" autocomplete="off" > 
            
        <hr>
        <b>ข้อมูลการทำงาน</b><br>
        
        <label>เลขใบอนุญาตทำงาน</label>
        <input type = "text" name = "fo_cl_workpermit_no" class="form-control" placeholder="เลขใบอนุญาตทำงาน Work permit" >
        
        <label>ตำแหน่งงาน</label>
        <input type = "text" name = "fo_cl_position" class="form-control" placeholder="ตำแหน่งงาน" >
        
        <label>ได้รับอนุญาตให้ทำงานถึงวันที่</label>
		<input type = "text" name="fo_cl_workpermit_exp" class="form-control datepicker"  placeholder="ได้รับอนุญาตให้ทำงานถึงวันที่" autocomplete="off" >
                
        <label>สถานะการขออยู่ต่อ</label>
        <select id="fo_cl_status" name="fo_cl_status" class="form-select form-select-sm" aria-label=".form-select-sm example" >
            <option value="" selected> </option>
            <option value="ถูกต้อง">ถูกต้อง</option>
            <option value="ขออยู่ต่อ">ขออยู่ต่อ</option>
            <option value="เกินกำหนด">เกินกำหนด</option>
            <option value="หลบหนี">หลบหนี</option>
        </select>
        
        <label>เหตุผลการขออยู่ต่อ</label>
        <input type = "text" name = "fo_cl_reason" class="form-control" placeholder="เหตุผลการขออยู่ต่อ" >
        
        <hr>
        <b>ข้อมูลนายจ้าง</b><br>
        <label>เลขที่นายจ้าง</label>
        <input type = "text" name = "fo_cl_employer_no" class="form-control" placeholder="เลขที่นายจ้าง" >
        
        <label>ชื่อนายจ้าง</label>
        <input type = "text" name = "fo_cl_employer_name" class="form-control" placeholder="ชื่อนายจ้าง" >
        
        <label>ประเภทนายจ้าง</label>
        <select id="fo_cl_employer_type" name="fo_cl_employer_type" class="form-select form-select-sm" aria-label=".form-select-sm example" >
            <option value="" selected> </option>
            <option value="นิติบุคคล">นิติบุคคล</option>
            <option value="บุคคลธรรมดา">บุคคลธรรมดา</option>
        </select>
        
        <label>ที่อยู่นายจ้าง</label>
        <input type = "text" name = "fo_cl_employer_address" class="form-control" placeholder="ที่อยู่นายจ้าง" >
        
        <label>เบอร์โทรนายจ้าง</label>
        <input type = "text" name = "fo_cl_employer_phone" class="form-control" placeholder="เบอร์โทรนายจ้าง" >
        
        <hr>
        
        <br>
        <label>สถานีตำรวจ <span style="color: #F90004">* จำเป็น</span></label><br>
                <!-- เลือกสถานีตำรวจ -->
        <label> บช. </label><br>
        <select name="region" id="region" onChange="do_region_change()" class="form-control" Required >
                <option value="">&lt;&lt; เลือก บช. &gt;&gt;</option>
                <?php
			  	$res_re = $conn2->query("SELECT * FROM `wm_tb_police_region` 
                                            ORDER BY `name_region` ASC");
			     $selected = '';
			     while($row_re = $conn2->fetch_row($res_re)) 
                     {
                      $code_region = $row_re['code_region'];
                      $name_region = $row_re['name_region'];
                      // set default provionce to 64 >> sukhothai
                          if($code_region == $region) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$code_region' $selected> $name_region </option>\n";
                     }
			  
			  ?>
              </select>
        
        <label> บก. </label><br>
        <select name="provincial" id="provincial" onChange="do_provincial_change()" class="form-control" >
        <option value="0">&lt;&lt; เลือก บก. &gt;&gt;</option>
                <?php
                if($region > 0)
                {
                    $res_prov = $conn2->query("SELECT * FROM `wm_tb_police_provincial` 
                                                WHERE `region_code`='$region' 
                                                ORDER BY `provincial_code` ASC");
                    $selected = '';
                  while($res_prov = $conn2->fetch_row($res_prov)) 
                  {
                      $provincial_code = $res_prov['provincial_code']; // 
                      $provincial = $res_prov['provincial'];
                      // set default provionce to 64 >> sukhothai
                          if($provincial_code == $provincial) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$provincial_code' $selected> $provincial </option>\n";
                  }
                }	
                ?>
              </select>

         <label> สน./สภ. </label>
        <br>
        <select name="station" id="station" class="form-control" >
                <option value="0">&lt;&lt; เลือก สน./สภ. &gt;&gt;</option>
                <?php
	           if($provincial > 0)
                {
                    $res_st = $conn2->query("SELECT * FROM `wm_tb_police_station2` 
                                                WHERE provincial_code='$provincial_code'
                                                ORDER BY `station_code` ASC");
                    $selected = '';
                  while($row_st = $conn2->fetch_row($res_st)) 
                  {
                      $station_code = $row_st['station_code']; // 
                      $station_name = $row_st['station_name'];
                      // set default provionce to 64 >> sukhothai
                          if($station_code == $station) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$station_code' $selected> $station_name </option>\n";
                  }
                }	
                ?>                                                                     
              </select>
		<br>
        
        <div class="mb-3">
             <label for="formFileMultiple" class="form-label">รูปภาพ</label>
             <input class="form-control" type="file" id="fo_cl_image" name="fo_cl_image" multiple>
        </div>
        
		<p>
		<br>
			<input type="submit" value="บันทึกข้อมูล" class="btn btn-success" >
			<td> <a href="index.php?rnd=<?= rand(); ?>&page=foreigner" class="btn btn-warning" >ยกเลิก</a> </td>
		</p>
		<br>
		<br>
	</form>
	</div>
	</div>
	</div>
<!-- ฟังก์ชั่น เลือก จังหวัด อำเภอ ตำบล -->
<script>

function do_province_change()
{
	var sel = document.getElementById("fo_cl_province");		
	var code = sel.options[sel.selectedIndex].value;

	$.get("../ajax/get_amphure.php?province=" + code, 
		  function(data, status){
				//alert("Data: " + data + "\nStatus: " + status);
				// jQuery style						
				$('#fo_cl_amphur').find('option').remove().end();
				var data = $.trim(data);
				if(data == '') {
					alert("ไม่พบข้อมูลอำเภอ !");
					return;
				}

					//.append('<option value="whatever">text</option>').val('whatever');
				var datas = data.split(';');
				for(var i=0; i<datas.length; i++) {
					if(datas[i] == '') continue;
					datas[i] = datas[i].split(':');
					var code = datas[i][0];
					var name = datas[i][1];
					$('#fo_cl_amphur').append('<option value="'+ code+'">' + name + '</option>');
				}
                    // Trigger the change event on the amphur dropdown so the tambon dropdown will be populated     
                $('#fo_cl_amphur').trigger('change');

		});
}

function do_amphure_change()
{
	var sel = document.getElementById("fo_cl_province");		
	var code = sel.options[sel.selectedIndex].value;

	var sel2 = document.getElementById("fo_cl_amphur");		
	var code2 = sel2.options[sel2.selectedIndex].value;

	$.get("../ajax/get_district.php?province=" + code + "&amphure=" + code2, 
			  function(data, status){
					//alert("Data: " + data + "\nStatus: " + status);
					// jQuery style						
					$('#fo_cl_tumbon').find('option').remove().end();
					var data = $.trim(data);
					if(data == '') {
						alert("ไม่พบข้อมูลตำบล !");
						return;
					}

						//.append('<option value="whatever">text</option>').val('whatever');
					var datas = data.split(';');
					for(var i=0; i<datas.length; i++) {
						if(datas[i] == '') continue;
						datas[i] = datas[i].split(':');
						var code = datas[i][0];
						var name = datas[i][1];
						$('#fo_cl_tumbon').append('<option value="'+ code+'">' + name + '</option>');
					}
			});
}

// ฟังก์ชั่น เปลี่ยนภาค (บช.) เป็น ภ.จว. (บก.)
function do_region_change()
{
	var sel_region = document.getElementById("region");		
	var code_region = sel_region.options[sel_region.selectedIndex].value;

	$.get("../ajax/get_provincial.php?region=" + code_region, 
		  function(data, status){
				//alert("Data: " + data + "\nStatus: " + status);
				// jQuery style						
				$('#provincial').find('option').remove().end();
				var data = $.trim(data);
				if(data == '') {
					alert("ไม่พบข้อมูล บก. !");
					return;
				}

					//.append('<option value="whatever">text</option>').val('whatever');
				var datas = data.split(';');
				for(var i=0; i<datas.length; i++) {
					if(datas[i] == '') continue;
					datas[i] = datas[i].split(':');
					var code_region = datas[i][0];
					var name_region = datas[i][1];
					$('#provincial').append('<option value="'+ code_region+'">' + name_region + '</option>');
				}
                // Trigger the change event on the amphur dropdown so the tambon dropdown will be populated     
                $('#provincial').trigger('change');
		});
}
    //เลือก สถานี จากจังหวัด
function do_provincial_change()
{
	var sel_provincial = document.getElementById("region");		
	var provincial_code = sel_provincial.options[sel_provincial.selectedIndex].value;
    
    var sel_provincial2 = document.getElementById("provincial");		
	var station_code = sel_provincial2.options[sel_provincial2.selectedIndex].value;

	$.get("../ajax/get_station.php?region=" + provincial_code + "&provincial=" + station_code, 
			  function(data, status){
					//alert("Data: " + data + "\nStatus: " + status);
					// jQuery style						
					$('#station').find('option').remove().end();
					var data = $.trim(data);
					if(data == '') {
						alert("ไม่พบข้อมูลสถานี !");
						return;
					}

						//.append('<option value="whatever">text</option>').val('whatever');
					var datas = data.split(';');
					for(var i=0; i<datas.length; i++) {
						if(datas[i] == '') continue;
						datas[i] = datas[i].split(':');
						var station_code = datas[i][0];
						var station_name = datas[i][1];
						$('#station').append('<option value="'+ station_code+'">' + station_name + '</option>');
					}
			});
}
</script> 
<script src="/js/datetimepicker2.5.1/jquery.ui.datepicker-th.js" type="text/javascript"></script>     
</body>
</html>