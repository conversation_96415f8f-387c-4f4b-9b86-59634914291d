<?php
include '../Condb.php';
include '../users.inc.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// เมื่อมีการใช้ ตัวแปร Session จะต้องเปิดก่อน
//session_start();  // เปิดตัวแปร session
/*if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
*/

// สำหรับเลือก จังหวัด อำเภอ ตำบล
include("../config.inc.php");
include("../classes/class.database.inc.php");
$conn2 = get_connection(); // connect to mySQLDB

// สำหรับเลือกสถานีตำรวจทั่วประเทศ
$region = isset($_GET['region']) ? $_GET['region'] : 0; // sukhothai
$provincial = isset($_GET['provincial']) ? $_GET['provincial'] : 0; // sukhothai
$station = isset($_GET['station']) ? $_GET['station'] : 0; // sukhothai

?>


<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>เพิ่มข้อมูลบุคคลจิตเวช ตาม พรบ.สุขภาพจิต</title>
<script src="/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 18px;
    }
input::placeholder {
  color: red;
}
.form-control::placeholder {
  color: sandybrown;
}
</style>
<script src="/jquery-ui-1.12.1/jquery-ui.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/jquery-ui-1.12.1/jquery-ui.css">
<link rel="stylesheet" href="/js/datetimepicker2.5.1/jquery.datetimepicker.min.css">
<script src="/js/datetimepicker2.5.1/jquery.datetimepicker.full.js" type="text/javascript"></script>    
<script src="/js/utils.js?<?= rand() ?>" type="text/javascript"></script>  
</head>

<body>
	
	<div class="container">
	<div class="row">
	<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> เพิ่มข้อมูล การยิงเก็บประวัติ อาวุธปืน</div>
	<form action="Save_history_shooting.php" method="POST" enctype="multipart/form-data" class="body">
	<span style="color: #1203F8">

		<label hidden ="hidden">ลำดับ</label>
		<input type = "text" name = "No" class="form-control"  hidden ="hidden" >
        
        <label>รหัสการจัดเก็บ <span style="color: #F90004">* จำเป็น</span> </label>
        <input type = "text" name = "code" class="form-control" placeholder="รหัสการจัดเก็บ" required >
        
        <label>วันเดือนปีที่ยิง <span style="color: #F90004">* จำเป็น</span></label>
        <p><input type="text" name="date" id="datepicker" class="form-control" autocomplete="off" required></p>
              <script>
              $( function() {
                    $.datepicker.setDefaults( $.datepicker.regional[ "th" ] );

                      var currentTime = new Date();
                      var year = currentTime.getFullYear();

                      // date
                      $("#datepicker").datepicker({
                        changeMonth: true,
                        changeYear: true,
                        yearSuffix: year+543,
                        yearRange: '-100:+0',
                        dateFormat: 'yy-mm-dd'
                      });
                   } );
              </script>
        
		<label>เลขที่ใบอนุญาต (ป4)</label>
		<input type = "text" name = "license_number" class="form-control" placeholder="เลขที่ใบอนุญาต (ป4)" >
        
        <label>ชื่อผู้ครอบครองอาวุธปืนตามใบอนุญาต</label>
		<input type = "text" name = "owner" class="form-control" placeholder="ชื่อผู้ครอบครองอาวุธปืนตามใบอนุญาต" >
        
        <label>ชนิดของปืน</label>
		<input type = "text" name = "type" class="form-control" placeholder="ชนิดของปืน" >
        
        <label>ขนาดของปืน</label>
		<input type = "text" name = "size" class="form-control" placeholder="ขนาดของปืน" >
        
        <label>ยี่ห้อปืน</label>
		<input type = "text" name = "brand" class="form-control" placeholder="ยี่ห้อปืน" >
        
        <label>รุ่นปืน</label>
		<input type = "text" name = "model" class="form-control" placeholder="รุ่นปืน" >
        
        <label>ผู้ยิงจัดเก็บ</label>
		<input type = "text" name = "shooter" class="form-control" placeholder="ผู้ยิงจัดเก็บ" >
        
        <label>ผู้บันทึก</label>
		<input type = "text" name = "recorder" class="form-control" placeholder="ผู้บันทึก" >
        
        <br>
        <label>สถานีตำรวจ <span style="color: #F90004">* จำเป็น</span></label><br>
                <!-- เลือกสถานีตำรวจ -->
        <label> บช. </label><br>
        <select name="region" id="region" onChange="do_region_change()" class="form-control" Required >
                <option value="">&lt;&lt; เลือก บช. &gt;&gt;</option>
                <?php
			  	$res_re = $conn2->query("SELECT * FROM `wm_tb_police_region` ORDER BY `wm_tb_police_region`.`id_region` ASC");
			     $selected = '';
			     while($row_re = $conn2->fetch_row($res_re)) 
			     {
				  $code_region = $row_re['code_region'];
				  $name_region = $row_re['name_region'];
				  // set default provionce to 64 >> sukhothai
				  if($code_region == $region) {
					  $selected = 'selected';
				  }
				  else {
					  $selected = '';
				  }
				  //
				  echo "<option value='$code_region' $selected> $name_region </option>\n";
			     }
			  
			  ?>
              </select>
        
        <label> บก. </label><br>
        <select name="provincial" id="provincial" onChange="do_provincial_change()" class="form-control" >
        <option value="0">&lt;&lt; เลือก บก. &gt;&gt;</option>
                <?php
                if($region > 0)
                {
                    $res_prov = $conn2->query("SELECT * FROM `wm_tb_police_provincial` WHERE `region_code`='$region' ORDER BY `wm_tb_police_provincial`.`wm_PID` ASC");
                    $selected = '';
                  while($res_prov = $conn2->fetch_row($res_prov)) 
                  {
                      $provincial_code = $res_prov['provincial_code']; // 
                      $provincial = $res_prov['provincial'];
                      // set default provionce to 64 >> sukhothai
                      if($provincial_code == $provincial) {
                          $selected = 'selected';
                      }
                      else {
                          $selected = '';
                      }
                      //
                      echo "<option value='$provincial_code' $selected> $provincial </option>\n";
                  }
                }	

                ?>
              </select>
        
        
         <label> สน./สภ. </label>
        <br>
        <select name="station" id="station" class="form-control" >
                <option value="0">&lt;&lt; เลือก สน./สภ. &gt;&gt;</option>
                <?php
	           if($provincial > 0)
                {
                    $res_st = $conn2->query("SELECT * FROM `wm_tb_police_station2` WHERE provincial_code='$provincial_code' ORDER BY `districts`.`station_name` ASC");
                    $selected = '';
                  while($row_st = $conn2->fetch_row($res_st)) 
                  {
                      $station_code = $row_st['station_code']; // 
                      $station_name = $row_st['station_name'];
                      // set default provionce to 64 >> sukhothai
                      if($station_code == $station) {
                          $selected = 'selected';
                      }
                      else {
                          $selected = '';
                      }
                      //
                      echo "<option value='$station_code' $selected> $station_name </option>\n";
                  }
                }	
                ?>                                                                     
              </select>
		<br>
        
		<p>
		<br>
			<input type="submit" value="บันทึกข้อมูล" class="btn btn-success" >
			<td> <a href="/Activity/history_shooting.php?rnd=<?= rand(); ?>" class="btn btn-warning" >ยกเลิก</a> </td>
		</p>
		<br>
		<br>
	</form>
	</div>
	</div>
	</div>
<script>
// ฟังก์ชั่น เปลี่ยนภาค เป็น จังหวัด
function do_region_change()
{
	var sel_region = document.getElementById("region");		
	var code_region = sel_region.options[sel_region.selectedIndex].value;

	$.get("../ajax/get_provincial.php?region=" + code_region, 
		  function(data, status){
				//alert("Data: " + data + "\nStatus: " + status);
				// jQuery style						
				$('#provincial').find('option').remove().end();
				var data = $.trim(data);
				if(data == '') {
					alert("ไม่พบข้อมูล บก. !");
					return;
				}

					//.append('<option value="whatever">text</option>').val('whatever');
				var datas = data.split(';');
				for(var i=0; i<datas.length; i++) {
					if(datas[i] == '') continue;
					datas[i] = datas[i].split(':');
					var code_region = datas[i][0];
					var name_region = datas[i][1];
					$('#provincial').append('<option value="'+ code_region+'">' + name_region + '</option>');
				}
		});
}
    //เลือก สถานี จากจังหวัด
function do_provincial_change()
{
	var sel_provincial = document.getElementById("region");		
	var provincial_code = sel_provincial.options[sel_provincial.selectedIndex].value;
    
    var sel_provincial2 = document.getElementById("provincial");		
	var station_code = sel_provincial2.options[sel_provincial2.selectedIndex].value;

	$.get("../ajax/get_station.php?region=" + provincial_code + "&provincial=" + station_code, 
			  function(data, status){
					//alert("Data: " + data + "\nStatus: " + status);
					// jQuery style						
					$('#station').find('option').remove().end();
					var data = $.trim(data);
					if(data == '') {
						alert("ไม่พบข้อมูลสถานี !");
						return;
					}

						//.append('<option value="whatever">text</option>').val('whatever');
					var datas = data.split(';');
					for(var i=0; i<datas.length; i++) {
						if(datas[i] == '') continue;
						datas[i] = datas[i].split(':');
						var station_code = datas[i][0];
						var station_name = datas[i][1];
						$('#station').append('<option value="'+ station_code+'">' + station_name + '</option>');
					}
			});
}
</script>
<script src="/js/datetimepicker2.5.1/jquery.ui.datepicker-th.js" type="text/javascript"></script>
</body>
</html>