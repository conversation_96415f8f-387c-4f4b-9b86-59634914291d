<?php
include '../Condb.php';
include '../users.inc.php';

//print_r($_POST);
//print_r($_FILES);

$ps_cl_aid = $_POST[ 'ps_cl_aid' ];
$ps_cl_idcard = $_POST[ 'ps_cl_idcard' ];
$ps_cl_prefix = $_POST[ 'ps_cl_prefix' ];
$ps_cl_sex = $_POST[ 'ps_cl_sex' ];
$ps_cl_name = $_POST[ 'ps_cl_name' ];
$ps_cl_surname = $_POST[ 'ps_cl_surname' ];
$ps_cl_nickname = $_POST[ 'ps_cl_nickname' ];
$ps_cl_birthday2 = $_POST[ 'ps_cl_birthday2' ];
$ps_cl_father = $_POST[ 'ps_cl_father' ];
$ps_cl_father_pid = $_POST[ 'ps_cl_father_pid' ];
$ps_cl_mother = $_POST[ 'ps_cl_mother' ];
$ps_cl_mother_pid = $_POST[ 'ps_cl_mother_pid' ];
$ps_cl_marital_status = $_POST[ 'ps_cl_marital_status' ];
$region = $_POST[ 'region' ];
$provincial = $_POST[ 'provincial' ];
$station = $_POST[ 'station' ];
$ps_cl_type = $_POST[ 'ps_cl_type' ];

// save Image
$file1 = $_FILES[ 'ps_cl_image' ][ 'tmp_name' ];
if ( is_uploaded_file( $file1 ) ) {
    /*
  $ps_cl_image = "/policeinnopolis/uploaded/Image1/" . basename( $_FILES[ 'ps_cl_image' ][ 'name' ] );
	
  move_uploaded_file( $file1, "../" . $ps_cl_image );  
  */
    //ลบรูปเก่าในเซิร์ฟเวอร์ก่อน
    $sql = "SELECT * FROM wm_tb_personal WHERE ps_cl_aid='ps_cl_aid' ";
    $res = mysqli_query($conn,$sql);
    if($row = mysqli_fetch_array($res))
    {
        if(($row['ps_cl_image'] != '') && file_exists($row['ps_cl_image'])) {
            unlink( $row['ps_cl_image'] );
        }    
    }

    //เปลี่ยนชื่อไฟล์ใหม่
    $ps_cl_image = "/policeinnopolis/uploaded/Image1/" . basename( $_FILES[ 'ps_cl_image' ][ 'name' ] );	    
    // file + ext    
    $ext = strrchr($ps_cl_image, ".");    
    $ps_cl_image = "../policeinnopolis/uploaded/Image1/File_" . time() . $ext; // file+time+ext        
    move_uploaded_file( $file1, $ps_cl_image );  
    
} 
else {
  $ps_cl_image = '';
}
//php >> $[AA-z]


$sql = "UPDATE wm_tb_personal SET " .
		"ps_cl_idcard = '$ps_cl_idcard', " .
		"ps_cl_prefix = '$ps_cl_prefix', ".
		"ps_cl_sex = '$ps_cl_sex', " .
		"ps_cl_name = '$ps_cl_name', " .
		"ps_cl_surname = '$ps_cl_surname', " .
		"ps_cl_nickname = '$ps_cl_nickname', ".
		"ps_cl_birthday2 = '$ps_cl_birthday2', ".
		"ps_cl_father = '$ps_cl_father', ".
		"ps_cl_father_pid = '$ps_cl_father_pid', ".
		"ps_cl_mother = '$ps_cl_mother', ".
		"ps_cl_mother_pid = '$ps_cl_mother_pid', ".
        "ps_cl_marital_status = '$ps_cl_marital_status', ".
        "region = '$region', ".
        "provincial = '$provincial', ".
		"station = '$station', ".
        "ps_cl_type = '$ps_cl_type' ".
		(($ps_cl_image == '') ? '' : (",ps_cl_image = '$ps_cl_image' ")) .
		"WHERE ps_cl_aid = '$ps_cl_aid' ";


$result = mysqli_query($conn, $sql);

//echo "$sql <hr>";
//print_r(mysqli_error($conn));

if($result){
	echo "<script>alert('บันทึกข้อมูลเรียบร้อย ');</script>";
	echo "<script>window.location='Show_All_SMIV_MTR.php?pcid={$ps_cl_idcard}&page_MTR';</script>";
	//echo "<a href='Show_Personal.php'>กลับหน้า Personal </a>";
}else{
	echo "<script>alert('บันทึกข้อมูลไม่สำเร็จ\n" . print_r(mysqli_error($conn), true) . "');</script>";
}

/*
// เก็บข้อมูล Action
$acc = $user['account'];
$sql3 = "INSERT wm_tb_user_action (at_cl_user,at_cl_date,at_cl_action) VALUES('$acc',CURRENT_TIMESTAMP,'Edit Personal {$ps_cl_idcard}')";
mysqli_query($conn, $sql3);
//
*/

// เก็บข้อมูล Action สำหรับ EDIT
$inputs = str_replace("'", "", compact_array($_POST));
$prev = str_replace("'", "", implode(',', $row_0));

$acc = $user['account'];
$sql3 = "INSERT wm_tb_user_action (at_cl_user,at_cl_date,at_cl_action,at_cl_input,at_cl_prev) VALUES('$acc',CURRENT_TIMESTAMP,'Edit Personal MTR {$ps_cl_idcard}', '$inputs', '{$prev}')";
mysqli_query($conn, $sql3);
//


mysqli_close($conn);

?>
