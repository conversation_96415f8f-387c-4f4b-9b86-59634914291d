<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';
include '../Head_image.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

//get month from select or current month
$month = isset($_GET['month']) ? $_GET['month'] : date("m");
$year = isset($_GET['year']) ? $_GET['year'] : date("Y");
//$station = isset($_GET['station']) ? $_GET['station'] : 6407;
//$station = 6407;   // สภ.เมืองพิจิตร

//--------------------
// last modify 2023-01-20
$limite_date = strtotime("$year-09-30"); // วันสุดท้ายของช่วง เป็น 30 ก.ย.ของปีนั้น ๆ
$current_date = time(); // now()   กำหนดวันปัจจุบัน
$check_date = strtotime("$year-10-01");   // ตัวแปร $check_date เป็นวันตั้งแต่ 1 ต.ค.ของปีนั้น ๆ เป็นต้นไป
$select_date = strtotime("$year-$month-01"); // เป็นตัวแปร สำหรับเลือก ของเดือนนั้น ๆ

//ถ้าเวลายังไม่สิ้นสุด 30 กย. ของปี
if($current_date < $check_date)   // ถ้าวันปัจจุบัน มีค่าน้อยกว่า วันที่ 1 ต.ค.
{
    $limite_date = strtotime(($year-1) . "-09-30");  // ตัวแปร $limite_date จะมีค่าเท่ากับ 30 ก.ย.ของปี ที่ลบค่าไป 1
    $check_date = strtotime(($year-1) . "-10-01");   // ตัวแปร $check_date จะมีค่าเท่ากับ 1 ต.ค. ของปี ที่ลบค่าไป 1
}

$m1 = '';
$m2 = '';
$m3 = '';
$m4 = '';
$m5 = '';
$m6 = '';
$m7 = '';
$m8 = '';
$m9 = '';
$m10 = '';
$m11 = '';
$m12 = '';
switch($month)
{
    case "01": $m1 = "selected"; break;
    case "02": $m2 = "selected"; break;
    case "03": $m3 = "selected"; break;
    case "04": $m4 = "selected"; break;
    case "05": $m5 = "selected"; break;
    case "06": $m6 = "selected"; break;
    case "07": $m7 = "selected"; break;
    case "08": $m8 = "selected"; break;
    case "09": $m9 = "selected"; break;
    case "10": $m10 = "selected"; break;
    case "11": $m11 = "selected"; break;
    case "12": $m12 = "selected"; break;
}
$current_month = $global_thai_month[0+$month];
$y2 = $year+543;

// แปลงเวลา เป็นปีไทย
function date_2_Thai( $date )
{
    global $global_thaimonth;
    $strYear = date("Y", $date) + 543;
    $strMonth = date("n",$date) - 1;
    $strDay = date("j", $date);
    $strMonthThai = $global_thaimonth[$strMonth];
    return "$strDay $strMonthThai $strYear";
}

//สร้างฟังก์ชั่น ย้อนหลัง arrest_summary_count มาใช้งาน เป็นยอดรวมการจับกุม ย้อนหลัง 1 เดือน
// dateM - datetime var
function arrest_summary_count($date)
{
    global $pdo, $station;

    $m1 = date("Y-m-1", $date);
    $m2 = date("Y-m-t", $date);

    $sql_s = "SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial` = :provincial AND `wr_cl_status` IN (2,4,5,6)";

    $stmt = $pdo->prepare($sql_s);
    $stmt->bindParam(':m1', $m1);
    $stmt->bindParam(':m2', $m2);
    $stmt->bindParam(':provincial', $provincial);
    $stmt->execute();

    $row_s = $stmt->fetch(PDO::FETCH_NUM);
    return $row_s[0];
}

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<base target="_top">
<title>รายงานหมายจับ ภ.จว.พิจิตร</title>
<link rel="stylesheet" href="/bootstrap/css/bootstrap-grid.css">
<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<style type="text/css">
body{
    font-size:12px; 
}
.textAlignVer{
    display:block;
    filter: flipv fliph;
    -webkit-transform: rotate(-90deg); 
    -moz-transform: rotate(-90deg); 
    transform: rotate(-90deg); 
    position:relative;
    width:20px;
    white-space:nowrap;
    font-size:16px;
    margin-bottom:10px;
}
.box !im{
    border: black;
    border-top-color: black !important;
    border-bottom-color: black !important;
    border-left-color: black !important;
    border-right-color: black !important;

}
</style>
    
</head>

<body>
    
<div class="container-fluid" align="left">
<div class=" h3 text-center  alert alert-success mb-4 mt-4 " role="alert" >กิจกรรม 7 หมายจับค้างเก่า <?= $name_provincial ?></div>
<div>
    <table width="80%" border="0" cellspacing="1" cellpadding="1">
      <tbody>
        <tr>
          <td width="21%">&nbsp;<a href="/WM/Show_PJ_report.php?rnd=<?= rand(); ?>" class="btn btn-warning btn-lg mb-4" >ย้อนกลับ</a><!--&nbsp;&nbsp;<a href="Add_mission.php" class="btn btn-success btn-lg mb-4" >เพิ่มข้อมูล</a>--></td>
          <td width="17%">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<b style="font-size: 25px">เลือกช่วงข้อมูล</b></td>    
          <td width="10%"><label>เลือกปี </label>
            <select id="mi_cl_year" name="mi_cl_year" class="form-select col-8 d-lg-inline" style="background-color: khaki; font-size: 16px" onChange="dates_change()">
              <option value="">เลือก</option>
              <?php
                $cur_y = date("Y") + 2;
                for($y=2020; $y<$cur_y; $y++) {
                    $sel = ($y == $year) ? "selected" : '';
                    echo "<option value='$y' $sel> ". ($y + 543) ." </option>";
                }
            ?>
          </select></td>
          <td width="3%"></td>
          <td width="16%"><label>เดือน</label>
            <select id="mi_cl_month" name="mi_cl_month" class="form-select col-3 d-lg-inline" style="background-color: khaki; font-size: 16px" onChange="dates_change()">
              <option value="">เลือก</option>
              <option <?= $m1 ?> value="01">มกราคม</option>
              <option <?= $m2 ?> value="02">กุมภาพันธ์</option>
              <option <?= $m3 ?> value="03">มีนาคม</option>
              <option <?= $m4 ?> value="04">เมษายน</option>
              <option <?= $m5 ?> value="05">พฤษภาคม</option>
              <option <?= $m6 ?> value="06">มิถุนายน</option>
              <option <?= $m7 ?> value="07">กรกฎาคม</option>
              <option <?= $m8 ?> value="08">สิงหาคม</option>
              <option <?= $m9 ?> value="09">กันยายน</option>
              <option <?= $m10 ?> value="10">ตุลาคม</option>
              <option <?= $m11 ?> value="11">พฤศจิกายน</option>
              <option <?= $m12 ?> value="12">ธันวาคม</option>
          </select></td>
          <td width="29%">&nbsp;</td>
          <td width="4%">&nbsp;</td>
        </tr>
      </tbody>
    </table><br>
</div>
<h3 align="center">ข้อมูลหมายจับค้างเก่า  <?= $name_provincial ?> </h3>
<h3 align="center">ประจำเดือน&nbsp; <?= $current_month ?> &nbsp;<?= $y2 ?> &nbsp;</h3><br>
<table width="90%" border="1" cellspacing="1" cellpadding="1" class="table table-bordered">
  <tbody>
         <tr  height=28 style='height:21.0pt'>
           <td rowspan="3" bordercolor="#0C0B0B"  style='border:solid #000000;
          height:21.0pt; background-color:aqua '>&nbsp;หน่วย</td>
           <td height="28" colspan="4" bordercolor="#000000"  style='border:solid #000000; background-color:#DDDDDD '>ยอดหมายจับ (ยกมา)</td>
           <td height=28 colspan=12 bordercolor="#0C0B0B"  style='border:solid #000000;
          height:21.0pt; background-color:aqua '><span style="height:21.0pt; background-color:aqua ">หมายจับทั่วไปของ  <?= $name_provincial ?>  ตั้งแต่ 11 ต.ค.45 - <?= date_2_Thai($limite_date) ?></span></td>
          <td colspan=14  style='border:solid #000000; background-color:#ADFDB6'>หมายจับทั่วไปของ
           <?= $name_provincial ?>  ตั้งแต่ <?= date_2_Thai($check_date) ?>  - ปัจจุบัน</td>
          <td colspan=3  style='border:solid #000000; background-color: yellow; text-align: center'>รวม (ในเดือนนั้น)</td>
         </tr>
         <tr  height=28 style='height:21.0pt'>
           <td rowspan=2  bordercolor="#000000"  style='border:solid #000000; background-color:#DDDDDD '><p>ยอดหมายจับ (01)</p></td>
           <td rowspan=2  bordercolor="#000000"  style='border:solid #000000; background-color:#DDDDDD '><p>มีคุณภาพ  (02)</p></td>
           <td rowspan=2  bordercolor="#000000"  style='border:solid #000000; background-color:#DDDDDD '>อายัด (03)</td>
          <td rowspan=2  bordercolor="#000000"  style='border:solid #000000; background-color:#51ADFC '><p>ไม่มีคุณภาพ (04)</p></td>
          <td rowspan=2  width=104 style='border:solid #000000; width:78pt; background-color:aqua ' bordercolor="#0C0B0B">หมายคงเหลือทั้งหมด (1)</td>
          <td colspan="2" align=left  style='border:solid #000000; background-color:aqua '>คุณภาพของหมาย</td>
          <td colspan=6  style='border:solid #000000; background-color:#FA757F '><p>การจำหน่ายหมายจับ
            (เพิ่มเติม)
             (<?= $current_month ?>&nbsp;<?= $y2 ?>)</td>
          <td colspan=2  style='border:solid #000000;
          border-left:none; background-color:aqua '>จำนวนหมายคงเหลือ</td>
          <td rowspan=2  width=93 style='border:solid #000000;
          border-top:none;width:70pt; background-color:aqua '>ร้อยละผลการจับกุมจากหมายจับทั้งหมด (12)</td>
          <td rowspan=2  width=87 style='border:solid #000000;
          border-top:none;width:65pt; background-color:#ADFDB6'>หมายจับคงเหลือทั้งหมดจากการประชุมที่ผ่านมา (13)</td>
          <td rowspan=2  width=81 style='border:solid #000000;
          border-top:none;width:61pt; background-color:#ADFDB6'>จำนวนหมายจับใหม่ (เพิ่มเติม) (14)</td>
          <td rowspan=2  width=65 style='border:solid #000000;
          border-top:none;width:49pt; background-color:#ADFDB6'>รวมหมายจับทั้งหมด (15)</td>
          <td colspan="2" align=left class=xl961464 style='border:solid #000000; background-color:#ADFDB6'>คุณภาพของหมาย</td>
          <td colspan=6  style='border:solid #000000; background-color:#FA757F'>การจำหน่ายหมายจับ
            (เพิ่มเติม)<br>
            (<?= $current_month ?>&nbsp;<?= $y2 ?>)</td>
          <td colspan=2  style='border:solid #000000;
          border-left:none; background-color:#ADFDB6'>จำนวนหมายคงเหลือ</td>
          <td rowspan=2  width=93 style='border:solid #000000;
          border-top:none;width:70pt; background-color:#ADFDB6'>ร้อยละผลการจับกุมจากหมายจับทั้งหมด (26)</td>
          <td rowspan=2  width=70 style='border:solid #000000;width:53pt; background-color: #51ADFC'>รวมหมายจับทั้งหมด
          1 ต.ค.45 - ปัจจุบัน (27)</td>
          <td rowspan=2  width=70 style='border:solid #000000;width:53pt; background-color: #FA757F'>รวมจำหน่ายหมายทั้งหมด (28)</td>
          <td rowspan=2  width=70 style='border:solid #000000;width:53pt; background-color: yellow'>ร้อยละผลการจับกุมจากหมายจับทั้งหมด</td>
         </tr>
         <tr  height=133>
          <td style='border:solid #000000; background-color:#7FFA75 ' align="center" valign="bottom"><span class="textAlignVer">มีคุณภาพ (2)</td>
             <td  style='border:solid #000000; background-color:aqua ' align="center" valign="bottom"><span class="textAlignVer">ไม่มีคุณภาพ (3)</td>
          <td  style='border:solid #000000; background-color:aqua '>จับกุม (4)</td>
          <td  style='border:solid #000000; background-color:aqua '>ขาดอายุความ (5)</td>
          <td  style='border:solid #000000; background-color:aqua '>ตาย (6)</td>
          <td  style='border:solid #000000; background-color:aqua '>อายัด (7)</td>
          <td  style='border:solid #000000; background-color:aqua '>อื่น ๆ (8)</td>
          <td  style='border:solid #000000; background-color:#FA757F '>รวม (9)</td>
          <td  style='border:solid #000000; background-color:#51ADFC ' align="center" valign="bottom"><span class="textAlignVer">มีคุณภาพ (10)</td>
          <td  style='border:solid #000000; background-color:aqua ' align="center" valign="bottom"><span class="textAlignVer">ไม่มีคุณภาพ (11)</td>
          <td  style='border:solid #000000; background-color:#7FFA75' align="center" valign="bottom"><span class="textAlignVer">มีคุณภาพ (16)</td>
          <td  style='border:solid #000000; background-color:#ADFDB6' align="center" valign="bottom"><span class="textAlignVer">ไม่มีคุณภาพ (17)</td>
          <td  style='border:solid #000000; background-color:#ADFDB6'>จับกุม (18)</td>
          <td  style='border:solid #000000; background-color:#ADFDB6'>ขาดอายุความ (19)</td>
          <td  style='border:solid #000000; background-color:#ADFDB6'>ตาย (20)</td>
          <td  style='border:solid #000000; background-color:#ADFDB6'>อายัด (21)</td>
          <td  style='border:solid #000000; background-color:#ADFDB6'>อื่น ๆ (22)</td>
          <td  style='border:solid #000000; background-color:#FA757F'>รวม (23)</td>
          <td  style='border:solid #000000; background-color:#51ADFC' align="center" valign="bottom"><span class="textAlignVer">มีคุณภาพ (24)</td>
          <td  style='border:solid #000000; background-color:#ADFDB6' align="center" valign="bottom"><span class="textAlignVer">ไม่มีคุณภาพ (25)</td>
         </tr>
<?php                                    
    
//$date1 = date("Y", $limite_date) . "-$month-01";        //"$year-$month-01";
$day1 = date("t", $limite_date);
$date2 = "$year-$month-$day1"; // <<<
                      
// ยอดหมายจับเก่า ที่ออกระหว่าง ปี 45 - 30 ก.ย.2565 ทั้งหมด
$date_prev = date("Y-m-d", $limite_date);
$date_next = date("Y-m-01", $check_date); // ตั้งแต่ 1 ตค เปนต้นไป
    $m1 = date("Y-m-01", $select_date);       // m1 = เดือนปัจจุบันที่เลือก
    $m2 = date("Y-m-t", $select_date);        // << end date of current month ได้ค่าวันสุดท้ายของเดือนปัจจุบันที่เลือกมา
                      
//ฟังก์ชั่น เลือกเดือน ย้อนหลัง 1 เดือน ได้ตัวแปร $sum2 ไปใช้งาน
$sum = arrest_summary_count( $select_date );
$prev_y = $year;
$prev_m = $month - 1;
if($prev_m < 0) {
    $prev_m = 12;
    $prev_y--;
}
$mp = strtotime("$prev_y-$prev_m-01");
$sum2 = arrest_summary_count( $mp );  // ยอดย้อนหลังไป 1 เดือน
     
					  
$sql = "SELECT " .
	//////// ยอดรวม ของ ภ.จว.พิจิตร  ///////////
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `wr_cl_status` IN (1,3)) AS All_01, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
    ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `wr_cl_status` IN (1,3)) AS Old_All_1, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
    ///// ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7 , " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`=6 ) AS Old_other_8, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`IN(2,4,5,6)) AS S_9, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     /////  หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `provincial`=:provincial AND `wr_cl_status` IN (1,3)) AS New_BF_13 , ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND (`wr_cl_status` IN (1,3,2))) AS New_M_14 , ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `provincial`=:provincial AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16 , ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `provincial`=:provincial AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17 , ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18 , ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`=4) AS New_Exp_19 , ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`=5) AS New_Dead_20 , " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`=3) AS New_Hold_21 , " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`=6) AS New_other_22 , " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`IN(2,4,5,6)) AS S_23, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
	
	////////// สภ.ดงเจริญ ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6401 AND `wr_cl_status` IN (1,3)) AS All_01_djr, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6401 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_djr, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6401 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_djr, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6401 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_djr, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6401 AND `wr_cl_status` IN (1,3)) AS Old_All_1_djr, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6401 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_djr, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6401 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_djr, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     ///// ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6401 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_djr, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6401 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_djr, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6401 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_djr, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6401 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_djr, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6401 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_djr, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6401 AND `wr_cl_status`=6 ) AS Old_other_8_djr, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6401 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_djr, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6401 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_djr, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     /////  หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6401 AND `wr_cl_status` IN (1,3)) AS New_BF_13_djr, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6401 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_djr, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6401 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_djr, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6401 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_djr, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6401 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_djr, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6401 AND `wr_cl_status`=4) AS New_Exp_19_djr, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6401 AND `wr_cl_status`=5) AS New_Dead_20_djr, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6401 AND `wr_cl_status`=3) AS New_Hold_21_djr, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6401 AND `wr_cl_status`=6) AS New_other_22_djr, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6401 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_djr, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6401 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_djr, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
	
	////////// สภ.ตะพานหิน ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6402 AND `wr_cl_status` IN (1,3)) AS All_01_tph, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6402 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_tph, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6402 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_tph, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6402 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_tph, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6402 AND `wr_cl_status` IN (1,3)) AS Old_All_1_tph, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6402 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_tph, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6402 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_tph, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6402 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_tph, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6402 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_tph, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6402 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_tph, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6402 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_tph, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6402 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_tph, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6402 AND `wr_cl_status`=6 ) AS Old_other_8_tph, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6402 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_tph, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6402 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_tph, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     /////  หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6402 AND `wr_cl_status` IN (1,3)) AS New_BF_13_tph, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6402 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_tph, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6402 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_tph, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6402 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_tph, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6402 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_tph, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6402 AND `wr_cl_status`=4) AS New_Exp_19_tph, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6402 AND `wr_cl_status`=5) AS New_Dead_20_tph, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6402 AND `wr_cl_status`=3) AS New_Hold_21_tph, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6402 AND `wr_cl_status`=6) AS New_other_22_tph, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6402 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_tph, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6402 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_tph, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
	
	////////// สภ.ทับคล้อ ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6403 AND `wr_cl_status` IN (1,3)) AS All_01_tko, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6403 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_tko, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6403 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_tko, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6403 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_tko, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6403 AND `wr_cl_status` IN (1,3)) AS Old_All_1_tko, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6403 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_tko, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6403 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_tko, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6403 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_tko, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6403 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_tko, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6403 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_tko, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6403 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_tko, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6403 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_tko, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6403 AND `wr_cl_status`=6 ) AS Old_other_8_tko, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6403 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_tko, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6403 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_tko, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
    //////หมายจับ ใหม่  //////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6403 AND `wr_cl_status` IN (1,3)) AS New_BF_13_tko, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6403 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_tko, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6403 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_tko, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6403 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_tko, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
    ////// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก  //////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6403 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_tko, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6403 AND `wr_cl_status`=4) AS New_Exp_19_tko, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6403 AND `wr_cl_status`=5) AS New_Dead_20_tko, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6403 AND `wr_cl_status`=3) AS New_Hold_21_tko, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6403 AND `wr_cl_status`=6) AS New_other_22_tko, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6403 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_tko, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6403 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_tko, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  6403
	
	////////// สภ.บางมูลนาก ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6404 AND `wr_cl_status` IN (1,3)) AS All_01_pmn, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6404 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_pmn, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6404 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_pmn, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6404 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_pmn, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6404 AND `wr_cl_status` IN (1,3)) AS Old_All_1_pmn, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6404 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_pmn, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6404 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_pmn, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6404 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_pmn, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6404 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_pmn, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6404 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_pmn, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6404 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_pmn, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6404 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_pmn, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6404 AND `wr_cl_status`=6 ) AS Old_other_8_pmn, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6404 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_pmn, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6404 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_pmn, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     /////  หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6404 AND `wr_cl_status` IN (1,3)) AS New_BF_13_pmn, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6404 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_pmn, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6404 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_pmn, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6404 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_pmn, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6404 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_pmn, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6404 AND `wr_cl_status`=4) AS New_Exp_19_pmn, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6404 AND `wr_cl_status`=5) AS New_Dead_20_pmn, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6404 AND `wr_cl_status`=3) AS New_Hold_21_pmn, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6404 AND `wr_cl_status`=6) AS New_other_22_pmn, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6404 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_pmn, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6404 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_pmn, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  6404 
	
	////////// สภ.บึงนาราง ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6405 AND `wr_cl_status` IN (1,3)) AS All_01_bnr, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6405 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_bnr, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6405 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_bnr, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6405 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_bnr, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
    /////////////////////////////// หมายจับเก่า  ///////////////////////////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6405 AND `wr_cl_status` IN (1,3)) AS Old_All_1_bnr, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6405 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_bnr, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6405 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_bnr, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6405 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_bnr, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6405 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_bnr, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6405 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_bnr, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6405 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_bnr, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6405 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_bnr, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6405 AND `wr_cl_status`=6 ) AS Old_other_8_bnr, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6405 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_bnr, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6405 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_bnr, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     /////  หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6405 AND `wr_cl_status` IN (1,3)) AS New_BF_13_bnr, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6405 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_bnr, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6405 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_bnr, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6405 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_bnr, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6405 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_bnr, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6405 AND `wr_cl_status`=4) AS New_Exp_19_bnr, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6405 AND `wr_cl_status`=5) AS New_Dead_20_bnr, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6405 AND `wr_cl_status`=3) AS New_Hold_21_bnr, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6405 AND `wr_cl_status`=6) AS New_other_22_bnr, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6405 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_bnr, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6405 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_bnr, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  6405
	
	////////// สภ.โพทะเล ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6406 AND `wr_cl_status` IN (1,3)) AS All_01_ptl, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6406 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_ptl, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6406 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_ptl, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6406 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_ptl, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6406 AND `wr_cl_status` IN (1,3)) AS Old_All_1_ptl, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6406 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_ptl, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6406 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_ptl, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6406 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_ptl, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6406 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_ptl, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6406 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_ptl, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6406 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_ptl, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6406 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_ptl, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6406 AND `wr_cl_status`=6 ) AS Old_other_8_ptl, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6406 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_ptl, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6406 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_ptl, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     ///// หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6406 AND `wr_cl_status` IN (1,3)) AS New_BF_13_ptl, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6406 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_ptl, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6406 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_ptl, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6406 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_ptl, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6406 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_ptl, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6406 AND `wr_cl_status`=4) AS New_Exp_19_ptl, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6406 AND `wr_cl_status`=5) AS New_Dead_20_ptl, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6406 AND `wr_cl_status`=3) AS New_Hold_21_ptl, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6406 AND `wr_cl_status`=6) AS New_other_22_ptl, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6406 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_ptl, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6406 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_ptl, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  6406
	
	////////// สภ.เมืองพิจิตร ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6407 AND `wr_cl_status` IN (1,3)) AS All_01_mpj, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6407 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_mpj, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6407 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_mpj, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6407 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_mpj, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6407 AND `wr_cl_status` IN (1,3)) AS Old_All_1_mpj, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6407 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_mpj, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6407 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_mpj, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6407 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_mpj, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6407 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_mpj, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6407 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_mpj, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6407 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_mpj, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6407 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_mpj, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6407 AND `wr_cl_status`=6 ) AS Old_other_8_mpj, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6407 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_mpj, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6407 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_mpj, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     ///// หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6407 AND `wr_cl_status` IN (1,3)) AS New_BF_13_mpj, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6407 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_mpj, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6407 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_mpj, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6407 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_mpj, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6407 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_mpj, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6407 AND `wr_cl_status`=4) AS New_Exp_19_mpj, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6407 AND `wr_cl_status`=5) AS New_Dead_20_mpj, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6407 AND `wr_cl_status`=3) AS New_Hold_21_mpj, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6407 AND `wr_cl_status`=6) AS New_other_22_mpj, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6407 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_mpj, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6407 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_mpj, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  6407
	
	////////// สภ.วชิรบารมี ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6408 AND `wr_cl_status` IN (1,3)) AS All_01_wcr, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6408 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_wcr, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6408 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_wcr, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6408 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_wcr, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6408 AND `wr_cl_status` IN (1,3)) AS Old_All_1_wcr, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6408 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_wcr, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6408 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_wcr, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6408 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_wcr, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6408 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_wcr, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6408 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_wcr, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6408 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_wcr, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6408 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_wcr, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6408 AND `wr_cl_status`=6 ) AS Old_other_8_wcr, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6408 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_wcr, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6408 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_wcr, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     ///// หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6408 AND `wr_cl_status` IN (1,3)) AS New_BF_13_wcr, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6408 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_wcr, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6408 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_wcr, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6408 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_wcr, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6408 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_wcr, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6408 AND `wr_cl_status`=4) AS New_Exp_19_wcr, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6408 AND `wr_cl_status`=5) AS New_Dead_20_wcr, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6408 AND `wr_cl_status`=3) AS New_Hold_21_wcr, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6408 AND `wr_cl_status`=6) AS New_other_22_wcr, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6408 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_wcr, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6408 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_wcr, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  6408
	
	////////// สภ.วังทรายพูน ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6409 AND `wr_cl_status` IN (1,3)) AS All_01_wsp, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6409 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_wsp, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6409 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_wsp, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6409 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_wsp, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6409 AND `wr_cl_status` IN (1,3)) AS Old_All_1_wsp, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6409 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_wsp, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6409 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_wsp, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6409 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_wsp, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6409 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_wsp, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6409 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_wsp, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6409 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_wsp, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6409 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_wsp, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6409 AND `wr_cl_status`=6 ) AS Old_other_8_wsp, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6409 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_wsp, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6409 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_wsp, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     ///// หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6409 AND `wr_cl_status` IN (1,3)) AS New_BF_13_wsp, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6409 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_wsp, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6409 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_wsp, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6409 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_wsp, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6409 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_wsp, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6409 AND `wr_cl_status`=4) AS New_Exp_19_wsp, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6409 AND `wr_cl_status`=5) AS New_Dead_20_wsp, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6409 AND `wr_cl_status`=3) AS New_Hold_21_wsp, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6409 AND `wr_cl_status`=6) AS New_other_22_wsp, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6409 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_wsp, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6409 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_wsp, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  6409
	
	////////// สภ.สากเหล็ก ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6410 AND `wr_cl_status` IN (1,3)) AS All_01_skl, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6410 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_skl, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6410 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_skl, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6410 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_skl, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6410 AND `wr_cl_status` IN (1,3)) AS Old_All_1_skl, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6410 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_skl, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6410 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_skl, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6410 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_skl, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6410 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_skl, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6410 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_skl, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6410 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_skl, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6410 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_skl, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6410 AND `wr_cl_status`=6 ) AS Old_other_8_skl, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6410 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_skl, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6410 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_skl, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     ///// หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6410 AND `wr_cl_status` IN (1,3)) AS New_BF_13_skl, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6410 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_skl, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6410 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_skl, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6410 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_skl, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6410 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_skl, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6410 AND `wr_cl_status`=4) AS New_Exp_19_skl, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6410 AND `wr_cl_status`=5) AS New_Dead_20_skl, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6410 AND `wr_cl_status`=3) AS New_Hold_21_skl, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6410 AND `wr_cl_status`=6) AS New_other_22_skl, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6410 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_skl, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6410 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_skl, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  6410
	
	////////// สภ.สามง่าม ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6411 AND `wr_cl_status` IN (1,3)) AS All_01_smk, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6411 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_smk, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6411 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_smk, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6411 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_smk, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6411 AND `wr_cl_status` IN (1,3)) AS Old_All_1_smk, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6411 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_smk, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6411 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_smk, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6411 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_smk, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6411 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_smk, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6411 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_smk, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6411 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_smk, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6411 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_smk, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6411 AND `wr_cl_status`=6 ) AS Old_other_8_smk, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6411 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_smk, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6411 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_smk, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     ///// หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6411 AND `wr_cl_status` IN (1,3)) AS New_BF_13_smk, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6411 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_smk, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6411 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_smk, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6411 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_smk, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6411 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_smk, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6411 AND `wr_cl_status`=4) AS New_Exp_19_smk, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6411 AND `wr_cl_status`=5) AS New_Dead_20_smk, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6411 AND `wr_cl_status`=3) AS New_Hold_21_smk, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6411 AND `wr_cl_status`=6) AS New_other_22_smk, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6411 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_smk, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6411 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_smk, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  6411
	
	////////// สภ.หนองโสน ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6412 AND `wr_cl_status` IN (1,3)) AS All_01_nsn, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6412 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_nsn, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6412 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_nsn, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6412 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_nsn, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6412 AND `wr_cl_status` IN (1,3)) AS Old_All_1_nsn, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6412 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_nsn, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6412 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_nsn, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6412 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_nsn, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6412 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_nsn, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6412 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_nsn, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6412 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_nsn, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6412 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_nsn, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6412 AND `wr_cl_status`=6 ) AS Old_other_8_nsn, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6412 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_nsn, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6412 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_nsn, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     ///// หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6412 AND `wr_cl_status` IN (1,3)) AS New_BF_13_nsn, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6412 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_nsn, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6412 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_nsn, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6412 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_nsn, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6412 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_nsn, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6412 AND `wr_cl_status`=4) AS New_Exp_19_nsn, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6412 AND `wr_cl_status`=5) AS New_Dead_20_nsn, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6412 AND `wr_cl_status`=3) AS New_Hold_21_nsn, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6412 AND `wr_cl_status`=6) AS New_other_22_nsn, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6412 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_nsn, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6412 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_nsn, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  6412
	
	////////// สภ.โพธิ์ประทับช้าง ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6413 AND `wr_cl_status` IN (1,3)) AS All_01_ptc, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6413 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_ptc, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6413 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_ptc, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6413 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_ptc, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6413 AND `wr_cl_status` IN (1,3)) AS Old_All_1_ptc, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6413 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_ptc, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6413 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_ptc, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6413 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_ptc, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6413 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_ptc, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6413 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_ptc, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6413 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_ptc, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6413 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_ptc, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6413 AND `wr_cl_status`=6 ) AS Old_other_8_ptc, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6413 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_ptc, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6413 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_ptc, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     ///// หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6413 AND `wr_cl_status` IN (1,3)) AS New_BF_13_ptc, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6413 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_ptc, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6413 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_ptc, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6413 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_ptc, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6413 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_ptc, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6413 AND `wr_cl_status`=4) AS New_Exp_19_ptc, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6413 AND `wr_cl_status`=5) AS New_Dead_20_ptc, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6413 AND `wr_cl_status`=3) AS New_Hold_21_ptc, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6413 AND `wr_cl_status`=6) AS New_other_22_ptc, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6413 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_ptc, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6413 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_ptc, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  6413
	
	////////// สภ.วังหว้า ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6414 AND `wr_cl_status` IN (1,3)) AS All_01_wgw, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6414 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_wgw, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6414 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_wgw, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6414 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_wgw, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6414 AND `wr_cl_status` IN (1,3)) AS Old_All_1_wgw, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6414 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_wgw, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6414 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_wgw, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6414 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_wgw, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6414 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_wgw, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6414 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_wgw, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6414 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_wgw, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6414 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_wgw, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6414 AND `wr_cl_status`=6 ) AS Old_other_8_wgw, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6414 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_wgw, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6414 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_wgw, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     ///// หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6414 AND `wr_cl_status` IN (1,3)) AS New_BF_13_wgw, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6414 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_wgw, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6414 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_wgw, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6414 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_wgw, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6414 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_wgw, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6414 AND `wr_cl_status`=4) AS New_Exp_19_wgw, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6414 AND `wr_cl_status`=5) AS New_Dead_20_wgw, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6414 AND `wr_cl_status`=3) AS New_Hold_21_wgw, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6414 AND `wr_cl_status`=6) AS New_other_22_wgw, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6414 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_wgw, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6414 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_wgw, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  6414
	
	////////// สภ.ดงป่าคำ ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6415 AND `wr_cl_status` IN (1,3)) AS All_01_dpk, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6415 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_dpk, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6415 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_dpk, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6415 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_dpk, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6415 AND `wr_cl_status` IN (1,3)) AS Old_All_1_dpk, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6415 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_dpk, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6415 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_dpk, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6415 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_dpk, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6415 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_dpk, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6415 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_dpk, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6415 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_dpk, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6415 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_dpk, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6415 AND `wr_cl_status`=6 ) AS Old_other_8_dpk, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6415 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_dpk, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6415 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_dpk, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     ///// หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6415 AND `wr_cl_status` IN (1,3)) AS New_BF_13_dpk, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6415 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_dpk, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6415 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_dpk, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6415 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_dpk, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6415 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_dpk, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6415 AND `wr_cl_status`=4) AS New_Exp_19_dpk, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6415 AND `wr_cl_status`=5) AS New_Dead_20_dpk, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6415 AND `wr_cl_status`=3) AS New_Hold_21_dpk, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6415 AND `wr_cl_status`=6) AS New_other_22_dpk, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6415 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_dpk, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6415 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_dpk, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  6415
	
	////////// สภ.บางลาย ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6416 AND `wr_cl_status` IN (1,3)) AS All_01_bgl, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6416 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_bgl, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6416 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_bgl, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6416 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_bgl, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6416 AND `wr_cl_status` IN (1,3)) AS Old_All_1_bgl, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6416 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_bgl, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6416 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_bgl, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6416 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_bgl, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6416 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_bgl, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6416 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_bgl, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6416 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_bgl, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6416 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_bgl, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6416 AND `wr_cl_status`=6 ) AS Old_other_8_bgl, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6416 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_bgl, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6416 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_bgl, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     ///// หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6416 AND `wr_cl_status` IN (1,3)) AS New_BF_13_bgl, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6416 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_bgl, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6416 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_bgl, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6416 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_bgl, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6416 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_bgl, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6416 AND `wr_cl_status`=4) AS New_Exp_19_bgl, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6416 AND `wr_cl_status`=5) AS New_Dead_20_bgl, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6416 AND `wr_cl_status`=3) AS New_Hold_21_bgl, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6416 AND `wr_cl_status`=6) AS New_other_22_bgl, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6416 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_bgl, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6416 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_bgl, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  6416
	
	////////// สภ.ย่านยาว ///////////
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6417 AND `wr_cl_status` IN (1,3)) AS All_01_yyo, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6417 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02_yyo, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6417 AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03_yyo, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=6417 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04_yyo, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
     ///// หมายจับเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6417 AND `wr_cl_status` IN (1,3)) AS Old_All_1_yyo, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6417 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2_yyo, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=6417 AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3_yyo, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
     /////  ยอดจำหน่ายหมายเก่า /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6417 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4_yyo, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6417 AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1_yyo, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6417 AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2_yyo, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6417 AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6_yyo, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6417 AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7_yyo, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6417 AND `wr_cl_status`=6 ) AS Old_other_8_yyo, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6417 AND `wr_cl_status`IN(2,4,5,6)) AS S_9_yyo, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6417 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_yyo, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
     ///// หมายจับ ใหม่ /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=6417 AND `wr_cl_status` IN (1,3)) AS New_BF_13_yyo, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=6417 AND (`wr_cl_status` IN (1,3,2))) AS New_M_14_yyo, ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6417 AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16_yyo, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=6417 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17_yyo, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
     ///// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก /////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6417 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18_yyo, ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6417 AND `wr_cl_status`=4) AS New_Exp_19_yyo, ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6417 AND `wr_cl_status`=5) AS New_Dead_20_yyo, " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6417 AND `wr_cl_status`=3) AS New_Hold_21_yyo, " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6417 AND `wr_cl_status`=6) AS New_other_22_yyo, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6417 AND `wr_cl_status`IN(2,4,5,6)) AS S_23_yyo, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=6417 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_yyo ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  6417
	
    "";
                      
try {
    $stmt = $pdo->prepare($sql);
    // Bind parameter values
    $stmt->bindParam(':m1', $m1);
    $stmt->bindParam(':m2', $m2);
    $stmt->bindParam(':provincial', $provincial);
    $stmt->bindParam(':date_next', $date_next);
    $stmt->bindParam(':date_prev', $date_prev);
    
    // Execute the prepared statement
    $stmt->execute();
    
	
    // Fetch the results if needed
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Process the results as required
    foreach ($results as $row) {
                      
	$numerator = 10;
    $denominator = 0;

    if ($denominator != 0) {
        $result = $numerator / $denominator;
		} else {
		$results = null; // Or any other appropriate value or action.
    }
//echo $sql;
///// ยอดของ ภ.จว.พิจิตร ////
$New_All_15 = $row['New_BF_13']+$row['New_M_14'];     //  
$S_27 = $row['Old_All_1']+$New_All_15;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27

// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5 = $row['Old_Exp_1']+$row['Old_Exp_2'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear = $row['Old_C_4'] + $Old_Exp_5 + $row['Old_Dead_6']+ $row['Old_other_8'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear = $row['Old_C_4'] + $row['Old_Exp_1'] + $row['Old_Dead_6']+ $row['Old_other_8'];
$Old_Q_clear_10 = $row['Old_Q_2'] - $Old_Q_clear;
$Old_NQ_clear_11 = $row['Old_NQ_3'] - $row['Old_Exp_2'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13'] >$sum2){
    $New_BF_13 = $row['New_BF_13']-$sum2;
}else{
    $New_BF_13 = $row['New_BF_13'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16'] > $sum2){
    $New_Q_16 = $row['New_Q_16']-$sum2;
}else{
    $New_Q_16 = $row['New_Q_16'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24 = $New_Q_16-$row['S_23'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24 > $sum2){
        $S_24_1 = $S_24-$sum2;
}else{
        $S_24_1 = $S_24;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23 = $row['New_C_18'] + $row['New_Exp_19'] + $row['New_Dead_20']+ $row['New_other_22'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24 = $New_Q_16 - $New_clear_23;
$S_28 = $row['S_9']+$New_clear_23; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen = 0;
$NewPersen = 0;
$AllPersen = 0;
if ($row['All_01'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen = ($row['S_9'] / $row['All_01']) * 100;
	$OldPersen = round($OldPersen,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen = ($row['S_23'] / $row['All_01']) * 100;
	$NewPersen = round($NewPersen,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27 != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen = ($S_28 / $S_27) * 100;
	$AllPersen = round($AllPersen,2);  // จุดทศนิยม 2 ตำแหน่ง  
}

	
/////  สภ.ดงเจริญ  ////
		
$New_All_15_djr = $row['New_BF_13_djr']+$row['New_M_14_djr'];     //  
$S_27_djr = $row['Old_All_1_djr']+$New_All_15_djr;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_djr = $row['Old_Exp_1_djr']+$row['Old_Exp_2_djr'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_djr = $row['Old_C_4_djr'] + $Old_Exp_5_djr + $row['Old_Dead_6_djr']+ $row['Old_other_8_djr'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_djr = $row['Old_C_4_djr'] + $row['Old_Exp_1_djr'] + $row['Old_Dead_6_djr']+ $row['Old_other_8_djr'];
$Old_Q_clear_10_djr = $row['Old_Q_2_djr'] - $Old_Q_clear_djr;
$Old_NQ_clear_11_djr = $row['Old_NQ_3_djr'] - $row['Old_Exp_2_djr'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_djr'] >$sum2){
    $New_BF_13_djr = $row['New_BF_13_djr']-$sum2;
}else{
    $New_BF_13_djr = $row['New_BF_13_djr'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_djr'] > $sum2){
    $New_Q_16_djr = $row['New_Q_16_djr']-$sum2;
}else{
    $New_Q_16_djr = $row['New_Q_16_djr'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_djr = $New_Q_16_djr - $row['S_23_djr'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_djr > $sum2){
        $S_24_1_djr = $S_24_djr - $sum2;
}else{
        $S_24_1_djr = $S_24_djr;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_djr = $row['New_C_18_djr'] + $row['New_Exp_19_djr'] + $row['New_Dead_20_djr']+ $row['New_other_22_djr'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_djr = $New_Q_16_djr - $New_clear_23_djr;
$S_28_djr = $row['S_9_djr']+$New_clear_23_djr; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_djr = 0;
$NewPersen_djr = 0;
$AllPersen_djr = 0;
if ($row['All_01_djr'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_djr = ($row['S_9_djr'] / $row['All_01_djr']) * 100;
	$OldPersen_djr = round($OldPersen_djr,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_djr = ($row['S_23_djr'] / $row['All_01_djr']) * 100;
	$NewPersen_djr = round($NewPersen_djr,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_djr != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_djr = ($S_28_djr / $S_27_djr) * 100;
	$AllPersen_djr = round($AllPersen_djr,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.ตะพานหิน  ////
		
$New_All_15_tph = $row['New_BF_13_tph']+$row['New_M_14_tph'];     //  
$S_27_tph = $row['Old_All_1_tph']+$New_All_15_tph;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_tph = $row['Old_Exp_1_tph']+$row['Old_Exp_2_tph'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_tph = $row['Old_C_4_tph'] + $Old_Exp_5_tph + $row['Old_Dead_6_tph']+ $row['Old_other_8_tph'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_tph = $row['Old_C_4_tph'] + $row['Old_Exp_1_tph'] + $row['Old_Dead_6_tph']+ $row['Old_other_8_tph'];
$Old_Q_clear_10_tph = $row['Old_Q_2_tph'] - $Old_Q_clear_tph;
$Old_NQ_clear_11_tph = $row['Old_NQ_3_tph'] - $row['Old_Exp_2_tph'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_tph'] >$sum2){
    $New_BF_13_tph = $row['New_BF_13_tph']-$sum2;
}else{
    $New_BF_13_tph = $row['New_BF_13_tph'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_tph'] > $sum2){
    $New_Q_16_tph = $row['New_Q_16_tph']-$sum2;
}else{
    $New_Q_16_tph = $row['New_Q_16_tph'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_tph = $New_Q_16_tph - $row['S_23_tph'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_tph > $sum2){
        $S_24_1_tph = $S_24_tph - $sum2;
}else{
        $S_24_1_tph = $S_24_tph;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_tph = $row['New_C_18_tph'] + $row['New_Exp_19_tph'] + $row['New_Dead_20_tph']+ $row['New_other_22_tph'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_tph = $New_Q_16_tph - $New_clear_23_tph;
$S_28_tph = $row['S_9_tph']+$New_clear_23_tph; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_tph = 0;
$NewPersen_tph = 0;
$AllPersen_tph = 0;
if ($row['All_01_tph'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_tph = ($row['S_9_tph'] / $row['All_01_tph']) * 100;
	$OldPersen_tph = round($OldPersen_tph,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_tph = ($row['S_23_tph'] / $row['All_01_tph']) * 100;
	$NewPersen_tph = round($NewPersen_tph,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_tph != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_tph = ($S_28_tph / $S_27_tph) * 100;
	$AllPersen_tph = round($AllPersen_tph,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.ทับคล้อ  ////
		
$New_All_15_tko = $row['New_BF_13_tko']+$row['New_M_14_tko'];     //  
$S_27_tko = $row['Old_All_1_tko']+$New_All_15_tko;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27

// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_tko = $row['Old_Exp_1_tko']+$row['Old_Exp_2_tko'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_tko = $row['Old_C_4_tko'] + $Old_Exp_5_tko + $row['Old_Dead_6_tko']+ $row['Old_other_8_tko'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_tko = $row['Old_C_4_tko'] + $row['Old_Exp_1_tko'] + $row['Old_Dead_6_tko']+ $row['Old_other_8_tko'];
$Old_Q_clear_10_tko = $row['Old_Q_2_tko'] - $Old_Q_clear_tko;
$Old_NQ_clear_11_tko = $row['Old_NQ_3_tko'] - $row['Old_Exp_2_tko'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_tko'] >$sum2){
    $New_BF_13_tko = $row['New_BF_13_tko']-$sum2;
}else{
    $New_BF_13_tko = $row['New_BF_13_tko'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_tko'] > $sum2){
    $New_Q_16_tko = $row['New_Q_16_tko']-$sum2;
}else{
    $New_Q_16_tko = $row['New_Q_16_tko'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_tko = $New_Q_16_tko - $row['S_23_tko'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_tko > $sum2){
        $S_24_1_tko = $S_24_tko - $sum2;
}else{
        $S_24_1_tko = $S_24_tko;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_tko = $row['New_C_18_tko'] + $row['New_Exp_19_tko'] + $row['New_Dead_20_tko']+ $row['New_other_22_tko'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_tko = $New_Q_16_tko - $New_clear_23_tko;
$S_28_tko = $row['S_9_tko']+$New_clear_23_tko; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_tko = 0;
$NewPersen_tko = 0;
$AllPersen_tko = 0;
if ($row['All_01_tko'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_tko = ($row['S_9_tko'] / $row['All_01_tko']) * 100;
	$OldPersen_tko = round($OldPersen_tko,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_tko = ($row['S_23_tko'] / $row['All_01_tko']) * 100;
	$NewPersen_tko = round($NewPersen_tko,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_tko != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_tko = ($S_28_tko / $S_27_tko) * 100;
	$AllPersen_tko = round($AllPersen_tko,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.บางมูลนาก  ////
		
$New_All_15_pmn = $row['New_BF_13_pmn']+$row['New_M_14_pmn'];     //  
$S_27_pmn = $row['Old_All_1_pmn']+$New_All_15_pmn;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_pmn = $row['Old_Exp_1_pmn']+$row['Old_Exp_2_pmn'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_pmn = $row['Old_C_4_pmn'] + $Old_Exp_5_pmn + $row['Old_Dead_6_pmn']+ $row['Old_other_8_pmn'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_pmn = $row['Old_C_4_pmn'] + $row['Old_Exp_1_pmn'] + $row['Old_Dead_6_pmn']+ $row['Old_other_8_pmn'];
$Old_Q_clear_10_pmn = $row['Old_Q_2_pmn'] - $Old_Q_clear_pmn;
$Old_NQ_clear_11_pmn = $row['Old_NQ_3_pmn'] - $row['Old_Exp_2_pmn'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_pmn'] >$sum2){
    $New_BF_13_pmn = $row['New_BF_13_pmn']-$sum2;
}else{
    $New_BF_13_pmn = $row['New_BF_13_pmn'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_pmn'] > $sum2){
    $New_Q_16_pmn = $row['New_Q_16_pmn']-$sum2;
}else{
    $New_Q_16_pmn = $row['New_Q_16_pmn'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_pmn = $New_Q_16_pmn - $row['S_23_pmn'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_pmn > $sum2){
        $S_24_1_pmn = $S_24_pmn - $sum2;
}else{
        $S_24_1_pmn = $S_24_pmn;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_pmn = $row['New_C_18_pmn'] + $row['New_Exp_19_pmn'] + $row['New_Dead_20_pmn']+ $row['New_other_22_pmn'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_pmn = $New_Q_16_pmn - $New_clear_23_pmn;
$S_28_pmn = $row['S_9_pmn']+$New_clear_23_pmn; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_pmn = 0;
$NewPersen_pmn = 0;
$AllPersen_pmn = 0;
if ($row['All_01_pmn'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_pmn = ($row['S_9_pmn'] / $row['All_01_pmn']) * 100;
	$OldPersen_pmn = round($OldPersen_pmn,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_pmn = ($row['S_23_pmn'] / $row['All_01_pmn']) * 100;
	$NewPersen_pmn = round($NewPersen_pmn,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_pmn != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_pmn = ($S_28_pmn / $S_27_pmn) * 100;
	$AllPersen_pmn = round($AllPersen_pmn,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.บึงนาราง  ////
		
$New_All_15_bnr = $row['New_BF_13_bnr']+$row['New_M_14_bnr'];     //  
$S_27_bnr = $row['Old_All_1_bnr']+$New_All_15_bnr;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_bnr = $row['Old_Exp_1_bnr']+$row['Old_Exp_2_bnr'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_bnr = $row['Old_C_4_bnr'] + $Old_Exp_5_bnr + $row['Old_Dead_6_bnr']+ $row['Old_other_8_bnr'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_bnr = $row['Old_C_4_bnr'] + $row['Old_Exp_1_bnr'] + $row['Old_Dead_6_bnr']+ $row['Old_other_8_bnr'];
$Old_Q_clear_10_bnr = $row['Old_Q_2_bnr'] - $Old_Q_clear_bnr;
$Old_NQ_clear_11_bnr = $row['Old_NQ_3_bnr'] - $row['Old_Exp_2_bnr'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_bnr'] >$sum2){
    $New_BF_13_bnr = $row['New_BF_13_bnr']-$sum2;
}else{
    $New_BF_13_bnr = $row['New_BF_13_bnr'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_bnr'] > $sum2){
    $New_Q_16_bnr = $row['New_Q_16_bnr']-$sum2;
}else{
    $New_Q_16_bnr = $row['New_Q_16_bnr'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_bnr = $New_Q_16_bnr - $row['S_23_bnr'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_bnr > $sum2){
        $S_24_1_bnr = $S_24_bnr - $sum2;
}else{
        $S_24_1_bnr = $S_24_bnr;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_bnr = $row['New_C_18_bnr'] + $row['New_Exp_19_bnr'] + $row['New_Dead_20_bnr']+ $row['New_other_22_bnr'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_bnr = $New_Q_16_bnr - $New_clear_23_bnr;
$S_28_bnr = $row['S_9_bnr']+$New_clear_23_bnr; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_bnr = 0;
$NewPersen_bnr = 0;
$AllPersen_bnr = 0;
if ($row['All_01_bnr'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_bnr = ($row['S_9_bnr'] / $row['All_01_bnr']) * 100;
	$OldPersen_bnr = round($OldPersen_bnr,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_bnr = ($row['S_23_bnr'] / $row['All_01_bnr']) * 100;
	$NewPersen_bnr = round($NewPersen_bnr,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_bnr != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_bnr = ($S_28_bnr / $S_27_bnr) * 100;
	$AllPersen_bnr = round($AllPersen_bnr,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.โพทะเล  ////
		
$New_All_15_ptl = $row['New_BF_13_ptl']+$row['New_M_14_ptl'];     //  
$S_27_ptl = $row['Old_All_1_ptl']+$New_All_15_ptl;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_ptl = $row['Old_Exp_1_ptl']+$row['Old_Exp_2_ptl'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_ptl = $row['Old_C_4_ptl'] + $Old_Exp_5_ptl + $row['Old_Dead_6_ptl']+ $row['Old_other_8_ptl'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_ptl = $row['Old_C_4_ptl'] + $row['Old_Exp_1_ptl'] + $row['Old_Dead_6_ptl']+ $row['Old_other_8_ptl'];
$Old_Q_clear_10_ptl = $row['Old_Q_2_ptl'] - $Old_Q_clear_ptl;
$Old_NQ_clear_11_ptl = $row['Old_NQ_3_ptl'] - $row['Old_Exp_2_ptl'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_ptl'] >$sum2){
    $New_BF_13_ptl = $row['New_BF_13_ptl']-$sum2;
}else{
    $New_BF_13_ptl = $row['New_BF_13_ptl'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_ptl'] > $sum2){
    $New_Q_16_ptl = $row['New_Q_16_ptl']-$sum2;
}else{
    $New_Q_16_ptl = $row['New_Q_16_ptl'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_ptl = $New_Q_16_ptl - $row['S_23_ptl'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_ptl > $sum2){
        $S_24_1_ptl = $S_24_ptl - $sum2;
}else{
        $S_24_1_ptl = $S_24_ptl;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_ptl = $row['New_C_18_ptl'] + $row['New_Exp_19_ptl'] + $row['New_Dead_20_ptl']+ $row['New_other_22_ptl'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_ptl = $New_Q_16_ptl - $New_clear_23_ptl;
$S_28_ptl = $row['S_9_ptl']+$New_clear_23_ptl; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_ptl = 0;
$NewPersen_ptl = 0;
$AllPersen_ptl = 0;
if ($row['All_01_ptl'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_ptl = ($row['S_9_ptl'] / $row['All_01_ptl']) * 100;
	$OldPersen_ptl = round($OldPersen_ptl,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_ptl = ($row['S_23_ptl'] / $row['All_01_ptl']) * 100;
	$NewPersen_ptl = round($NewPersen_ptl,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_ptl != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_ptl = ($S_28_ptl / $S_27_ptl) * 100;
	$AllPersen_ptl = round($AllPersen_ptl,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.เมืองพิจิตร  ////
		
$New_All_15_mpj = $row['New_BF_13_mpj']+$row['New_M_14_mpj'];     //  
$S_27_mpj = $row['Old_All_1_mpj']+$New_All_15_mpj;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_mpj = $row['Old_Exp_1_mpj']+$row['Old_Exp_2_mpj'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_mpj = $row['Old_C_4_mpj'] + $Old_Exp_5_mpj + $row['Old_Dead_6_mpj']+ $row['Old_other_8_mpj'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_mpj = $row['Old_C_4_mpj'] + $row['Old_Exp_1_mpj'] + $row['Old_Dead_6_mpj']+ $row['Old_other_8_mpj'];
$Old_Q_clear_10_mpj = $row['Old_Q_2_mpj'] - $Old_Q_clear_mpj;
$Old_NQ_clear_11_mpj = $row['Old_NQ_3_mpj'] - $row['Old_Exp_2_mpj'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_mpj'] >$sum2){
    $New_BF_13_mpj = $row['New_BF_13_mpj']-$sum2;
}else{
    $New_BF_13_mpj = $row['New_BF_13_mpj'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_mpj'] > $sum2){
    $New_Q_16_mpj = $row['New_Q_16_mpj']-$sum2;
}else{
    $New_Q_16_mpj = $row['New_Q_16_mpj'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_mpj = $New_Q_16_mpj - $row['S_23_mpj'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_mpj > $sum2){
        $S_24_1_mpj = $S_24_mpj - $sum2;
}else{
        $S_24_1_mpj = $S_24_mpj;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_mpj = $row['New_C_18_mpj'] + $row['New_Exp_19_mpj'] + $row['New_Dead_20_mpj']+ $row['New_other_22_mpj'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_mpj = $New_Q_16_mpj - $New_clear_23_mpj;
$S_28_mpj = $row['S_9_mpj']+$New_clear_23_mpj; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_mpj = 0;
$NewPersen_mpj = 0;
$AllPersen_mpj = 0;
if ($row['All_01_mpj'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_mpj = ($row['S_9_mpj'] / $row['All_01_mpj']) * 100;
	$OldPersen_mpj = round($OldPersen_mpj,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_mpj = ($row['S_23_mpj'] / $row['All_01_mpj']) * 100;
	$NewPersen_mpj = round($NewPersen_mpj,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_mpj != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_mpj = ($S_28_mpj / $S_27_mpj) * 100;
	$AllPersen_mpj = round($AllPersen_mpj,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.วชิรบารมี  ////
		
$New_All_15_wcr = $row['New_BF_13_wcr']+$row['New_M_14_wcr'];     //  
$S_27_wcr = $row['Old_All_1_wcr']+$New_All_15_wcr;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_wcr = $row['Old_Exp_1_wcr']+$row['Old_Exp_2_wcr'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_wcr = $row['Old_C_4_wcr'] + $Old_Exp_5_wcr + $row['Old_Dead_6_wcr']+ $row['Old_other_8_wcr'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_wcr = $row['Old_C_4_wcr'] + $row['Old_Exp_1_wcr'] + $row['Old_Dead_6_wcr']+ $row['Old_other_8_wcr'];
$Old_Q_clear_10_wcr = $row['Old_Q_2_wcr'] - $Old_Q_clear_wcr;
$Old_NQ_clear_11_wcr = $row['Old_NQ_3_wcr'] - $row['Old_Exp_2_wcr'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_wcr'] >$sum2){
    $New_BF_13_wcr = $row['New_BF_13_wcr']-$sum2;
}else{
    $New_BF_13_wcr = $row['New_BF_13_wcr'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_wcr'] > $sum2){
    $New_Q_16_wcr = $row['New_Q_16_wcr']-$sum2;
}else{
    $New_Q_16_wcr = $row['New_Q_16_wcr'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_wcr = $New_Q_16_wcr - $row['S_23_wcr'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_wcr > $sum2){
        $S_24_1_wcr = $S_24_wcr - $sum2;
}else{
        $S_24_1_wcr = $S_24_wcr;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_wcr = $row['New_C_18_wcr'] + $row['New_Exp_19_wcr'] + $row['New_Dead_20_wcr']+ $row['New_other_22_wcr'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_wcr = $New_Q_16_wcr - $New_clear_23_wcr;
$S_28_wcr = $row['S_9_wcr']+$New_clear_23_wcr; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_wcr = 0;
$NewPersen_wcr = 0;
$AllPersen_wcr = 0;
if ($row['All_01_wcr'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_wcr = ($row['S_9_wcr'] / $row['All_01_wcr']) * 100;
	$OldPersen_wcr = round($OldPersen_wcr,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_wcr = ($row['S_23_wcr'] / $row['All_01_wcr']) * 100;
	$NewPersen_wcr = round($NewPersen_wcr,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_wcr != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_wcr = ($S_28_wcr / $S_27_wcr) * 100;
	$AllPersen_wcr = round($AllPersen_wcr,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.วังทรายพูน  ////
		
$New_All_15_wsp = $row['New_BF_13_wsp']+$row['New_M_14_wsp'];     //  
$S_27_wsp = $row['Old_All_1_wsp']+$New_All_15_wsp;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_wsp = $row['Old_Exp_1_wsp']+$row['Old_Exp_2_wsp'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_wsp = $row['Old_C_4_wsp'] + $Old_Exp_5_wsp + $row['Old_Dead_6_wsp']+ $row['Old_other_8_wsp'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_wsp = $row['Old_C_4_wsp'] + $row['Old_Exp_1_wsp'] + $row['Old_Dead_6_wsp']+ $row['Old_other_8_wsp'];
$Old_Q_clear_10_wsp = $row['Old_Q_2_wsp'] - $Old_Q_clear_wsp;
$Old_NQ_clear_11_wsp = $row['Old_NQ_3_wsp'] - $row['Old_Exp_2_wsp'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_wsp'] >$sum2){
    $New_BF_13_wsp = $row['New_BF_13_wsp']-$sum2;
}else{
    $New_BF_13_wsp = $row['New_BF_13_wsp'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_wsp'] > $sum2){
    $New_Q_16_wsp = $row['New_Q_16_wsp']-$sum2;
}else{
    $New_Q_16_wsp = $row['New_Q_16_wsp'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_wsp = $New_Q_16_wsp - $row['S_23_wsp'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_wsp > $sum2){
        $S_24_1_wsp = $S_24_wsp - $sum2;
}else{
        $S_24_1_wsp = $S_24_wsp;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_wsp = $row['New_C_18_wsp'] + $row['New_Exp_19_wsp'] + $row['New_Dead_20_wsp']+ $row['New_other_22_wsp'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_wsp = $New_Q_16_wsp - $New_clear_23_wsp;
$S_28_wsp = $row['S_9_wsp']+$New_clear_23_wsp; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_wsp = 0;
$NewPersen_wsp = 0;
$AllPersen_wsp = 0;
if ($row['All_01_wsp'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_wsp = ($row['S_9_wsp'] / $row['All_01_wsp']) * 100;
	$OldPersen_wsp = round($OldPersen_wsp,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_wsp = ($row['S_23_wsp'] / $row['All_01_wsp']) * 100;
	$NewPersen_wsp = round($NewPersen_wsp,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_wsp != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_wsp = ($S_28_wsp / $S_27_wsp) * 100;
	$AllPersen_wsp = round($AllPersen_wsp,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.สากเหล็ก  ////
		
$New_All_15_skl = $row['New_BF_13_skl']+$row['New_M_14_skl'];     //  
$S_27_skl = $row['Old_All_1_skl']+$New_All_15_skl;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_skl = $row['Old_Exp_1_skl']+$row['Old_Exp_2_skl'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_skl = $row['Old_C_4_skl'] + $Old_Exp_5_skl + $row['Old_Dead_6_skl']+ $row['Old_other_8_skl'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_skl = $row['Old_C_4_skl'] + $row['Old_Exp_1_skl'] + $row['Old_Dead_6_skl']+ $row['Old_other_8_skl'];
$Old_Q_clear_10_skl = $row['Old_Q_2_skl'] - $Old_Q_clear_skl;
$Old_NQ_clear_11_skl = $row['Old_NQ_3_skl'] - $row['Old_Exp_2_skl'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_skl'] >$sum2){
    $New_BF_13_skl = $row['New_BF_13_skl']-$sum2;
}else{
    $New_BF_13_skl = $row['New_BF_13_skl'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_skl'] > $sum2){
    $New_Q_16_skl = $row['New_Q_16_skl']-$sum2;
}else{
    $New_Q_16_skl = $row['New_Q_16_skl'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_skl = $New_Q_16_skl - $row['S_23_skl'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_skl > $sum2){
        $S_24_1_skl = $S_24_skl - $sum2;
}else{
        $S_24_1_skl = $S_24_skl;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_skl = $row['New_C_18_skl'] + $row['New_Exp_19_skl'] + $row['New_Dead_20_skl']+ $row['New_other_22_skl'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_skl = $New_Q_16_skl - $New_clear_23_skl;
$S_28_skl = $row['S_9_skl']+$New_clear_23_skl; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_skl = 0;
$NewPersen_skl = 0;
$AllPersen_skl = 0;
if ($row['All_01_skl'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_skl = ($row['S_9_skl'] / $row['All_01_skl']) * 100;
	$OldPersen_skl = round($OldPersen_skl,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_skl = ($row['S_23_skl'] / $row['All_01_skl']) * 100;
	$NewPersen_skl = round($NewPersen_skl,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_skl != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_skl = ($S_28_skl / $S_27_skl) * 100;
	$AllPersen_skl = round($AllPersen_skl,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.สามง่าม  ////
		
$New_All_15_smk = $row['New_BF_13_smk']+$row['New_M_14_smk'];     //  
$S_27_smk = $row['Old_All_1_smk']+$New_All_15_smk;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_smk = $row['Old_Exp_1_smk']+$row['Old_Exp_2_smk'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_smk = $row['Old_C_4_smk'] + $Old_Exp_5_smk + $row['Old_Dead_6_smk']+ $row['Old_other_8_smk'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_smk = $row['Old_C_4_smk'] + $row['Old_Exp_1_smk'] + $row['Old_Dead_6_smk']+ $row['Old_other_8_smk'];
$Old_Q_clear_10_smk = $row['Old_Q_2_smk'] - $Old_Q_clear_smk;
$Old_NQ_clear_11_smk = $row['Old_NQ_3_smk'] - $row['Old_Exp_2_smk'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_smk'] >$sum2){
    $New_BF_13_smk = $row['New_BF_13_smk']-$sum2;
}else{
    $New_BF_13_smk = $row['New_BF_13_smk'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_smk'] > $sum2){
    $New_Q_16_smk = $row['New_Q_16_smk']-$sum2;
}else{
    $New_Q_16_smk = $row['New_Q_16_smk'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_smk = $New_Q_16_smk - $row['S_23_smk'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_smk > $sum2){
        $S_24_1_smk = $S_24_smk - $sum2;
}else{
        $S_24_1_smk = $S_24_smk;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_smk = $row['New_C_18_smk'] + $row['New_Exp_19_smk'] + $row['New_Dead_20_smk']+ $row['New_other_22_smk'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_smk = $New_Q_16_smk - $New_clear_23_smk;
$S_28_smk = $row['S_9_smk']+$New_clear_23_smk; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_smk = 0;
$NewPersen_smk = 0;
$AllPersen_smk = 0;
if ($row['All_01_smk'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_smk = ($row['S_9_smk'] / $row['All_01_smk']) * 100;
	$OldPersen_smk = round($OldPersen_smk,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_smk = ($row['S_23_smk'] / $row['All_01_smk']) * 100;
	$NewPersen_smk = round($NewPersen_smk,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_smk != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_smk = ($S_28_smk / $S_27_smk) * 100;
	$AllPersen_smk = round($AllPersen_smk,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.หนองโสน  ////
		
$New_All_15_nsn = $row['New_BF_13_nsn']+$row['New_M_14_nsn'];     //  
$S_27_nsn = $row['Old_All_1_nsn']+$New_All_15_nsn;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_nsn = $row['Old_Exp_1_nsn']+$row['Old_Exp_2_nsn'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_nsn = $row['Old_C_4_nsn'] + $Old_Exp_5_nsn + $row['Old_Dead_6_nsn']+ $row['Old_other_8_nsn'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_nsn = $row['Old_C_4_nsn'] + $row['Old_Exp_1_nsn'] + $row['Old_Dead_6_nsn']+ $row['Old_other_8_nsn'];
$Old_Q_clear_10_nsn = $row['Old_Q_2_nsn'] - $Old_Q_clear_nsn;
$Old_NQ_clear_11_nsn = $row['Old_NQ_3_nsn'] - $row['Old_Exp_2_nsn'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_nsn'] >$sum2){
    $New_BF_13_nsn = $row['New_BF_13_nsn']-$sum2;
}else{
    $New_BF_13_nsn = $row['New_BF_13_nsn'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_nsn'] > $sum2){
    $New_Q_16_nsn = $row['New_Q_16_nsn']-$sum2;
}else{
    $New_Q_16_nsn = $row['New_Q_16_nsn'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_nsn = $New_Q_16_nsn - $row['S_23_nsn'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_nsn > $sum2){
        $S_24_1_nsn = $S_24_nsn - $sum2;
}else{
        $S_24_1_nsn = $S_24_nsn;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_nsn = $row['New_C_18_nsn'] + $row['New_Exp_19_nsn'] + $row['New_Dead_20_nsn']+ $row['New_other_22_nsn'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_nsn = $New_Q_16_nsn - $New_clear_23_nsn;
$S_28_nsn = $row['S_9_nsn']+$New_clear_23_nsn; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_nsn = 0;
$NewPersen_nsn = 0;
$AllPersen_nsn = 0;
if ($row['All_01_nsn'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_nsn = ($row['S_9_nsn'] / $row['All_01_nsn']) * 100;
	$OldPersen_nsn = round($OldPersen_nsn,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_nsn = ($row['S_23_nsn'] / $row['All_01_nsn']) * 100;
	$NewPersen_nsn = round($NewPersen_nsn,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_nsn != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_nsn = ($S_28_nsn / $S_27_nsn) * 100;
	$AllPersen_nsn = round($AllPersen_nsn,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.โพธิ์ประทับช้าง  ////
		
$New_All_15_ptc = $row['New_BF_13_ptc']+$row['New_M_14_ptc'];     //  
$S_27_ptc = $row['Old_All_1_ptc']+$New_All_15_ptc;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_ptc = $row['Old_Exp_1_ptc']+$row['Old_Exp_2_ptc'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_ptc = $row['Old_C_4_ptc'] + $Old_Exp_5_ptc + $row['Old_Dead_6_ptc']+ $row['Old_other_8_ptc'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_ptc = $row['Old_C_4_ptc'] + $row['Old_Exp_1_ptc'] + $row['Old_Dead_6_ptc']+ $row['Old_other_8_ptc'];
$Old_Q_clear_10_ptc = $row['Old_Q_2_ptc'] - $Old_Q_clear_ptc;
$Old_NQ_clear_11_ptc = $row['Old_NQ_3_ptc'] - $row['Old_Exp_2_ptc'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_ptc'] >$sum2){
    $New_BF_13_ptc = $row['New_BF_13_ptc']-$sum2;
}else{
    $New_BF_13_ptc = $row['New_BF_13_ptc'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_ptc'] > $sum2){
    $New_Q_16_ptc = $row['New_Q_16_ptc']-$sum2;
}else{
    $New_Q_16_ptc = $row['New_Q_16_ptc'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_ptc = $New_Q_16_ptc - $row['S_23_ptc'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_ptc > $sum2){
        $S_24_1_ptc = $S_24_ptc - $sum2;
}else{
        $S_24_1_ptc = $S_24_ptc;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_ptc = $row['New_C_18_ptc'] + $row['New_Exp_19_ptc'] + $row['New_Dead_20_ptc']+ $row['New_other_22_ptc'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_ptc = $New_Q_16_ptc - $New_clear_23_ptc;
$S_28_ptc = $row['S_9_ptc']+$New_clear_23_ptc; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_ptc = 0;
$NewPersen_ptc = 0;
$AllPersen_ptc = 0;
if ($row['All_01_ptc'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_ptc = ($row['S_9_ptc'] / $row['All_01_ptc']) * 100;
	$OldPersen_ptc = round($OldPersen_ptc,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_ptc = ($row['S_23_ptc'] / $row['All_01_ptc']) * 100;
	$NewPersen_ptc = round($NewPersen_ptc,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_ptc != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_ptc = ($S_28_ptc / $S_27_ptc) * 100;
	$AllPersen_ptc = round($AllPersen_ptc,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.วังหว้า  ////
		
$New_All_15_wgw = $row['New_BF_13_wgw']+$row['New_M_14_wgw'];     //  
$S_27_wgw = $row['Old_All_1_wgw']+$New_All_15_wgw;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_wgw = $row['Old_Exp_1_wgw']+$row['Old_Exp_2_wgw'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_wgw = $row['Old_C_4_wgw'] + $Old_Exp_5_wgw + $row['Old_Dead_6_wgw']+ $row['Old_other_8_wgw'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_wgw = $row['Old_C_4_wgw'] + $row['Old_Exp_1_wgw'] + $row['Old_Dead_6_wgw']+ $row['Old_other_8_wgw'];
$Old_Q_clear_10_wgw = $row['Old_Q_2_wgw'] - $Old_Q_clear_wgw;
$Old_NQ_clear_11_wgw = $row['Old_NQ_3_wgw'] - $row['Old_Exp_2_wgw'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_wgw'] >$sum2){
    $New_BF_13_wgw = $row['New_BF_13_wgw']-$sum2;
}else{
    $New_BF_13_wgw = $row['New_BF_13_wgw'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_wgw'] > $sum2){
    $New_Q_16_wgw = $row['New_Q_16_wgw']-$sum2;
}else{
    $New_Q_16_wgw = $row['New_Q_16_wgw'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_wgw = $New_Q_16_wgw - $row['S_23_wgw'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_wgw > $sum2){
        $S_24_1_wgw = $S_24_wgw - $sum2;
}else{
        $S_24_1_wgw = $S_24_wgw;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_wgw = $row['New_C_18_wgw'] + $row['New_Exp_19_wgw'] + $row['New_Dead_20_wgw']+ $row['New_other_22_wgw'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_wgw = $New_Q_16_wgw - $New_clear_23_wgw;
$S_28_wgw = $row['S_9_wgw']+$New_clear_23_wgw; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_wgw = 0;
$NewPersen_wgw = 0;
$AllPersen_wgw = 0;
if ($row['All_01_wgw'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_wgw = ($row['S_9_wgw'] / $row['All_01_wgw']) * 100;
	$OldPersen_wgw = round($OldPersen_wgw,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_wgw = ($row['S_23_wgw'] / $row['All_01_wgw']) * 100;
	$NewPersen_wgw = round($NewPersen_wgw,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_wgw != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_wgw = ($S_28_wgw / $S_27_wgw) * 100;
	$AllPersen_wgw = round($AllPersen_wgw,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.ดงป่าคำ  ////
		
$New_All_15_dpk = $row['New_BF_13_dpk']+$row['New_M_14_dpk'];     //  
$S_27_dpk = $row['Old_All_1_dpk']+$New_All_15_dpk;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_dpk = $row['Old_Exp_1_dpk']+$row['Old_Exp_2_dpk'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_dpk = $row['Old_C_4_dpk'] + $Old_Exp_5_dpk + $row['Old_Dead_6_dpk']+ $row['Old_other_8_dpk'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_dpk = $row['Old_C_4_dpk'] + $row['Old_Exp_1_dpk'] + $row['Old_Dead_6_dpk']+ $row['Old_other_8_dpk'];
$Old_Q_clear_10_dpk = $row['Old_Q_2_dpk'] - $Old_Q_clear_dpk;
$Old_NQ_clear_11_dpk = $row['Old_NQ_3_dpk'] - $row['Old_Exp_2_dpk'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_dpk'] >$sum2){
    $New_BF_13_dpk = $row['New_BF_13_dpk']-$sum2;
}else{
    $New_BF_13_dpk = $row['New_BF_13_dpk'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_dpk'] > $sum2){
    $New_Q_16_dpk = $row['New_Q_16_dpk']-$sum2;
}else{
    $New_Q_16_dpk = $row['New_Q_16_dpk'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_dpk = $New_Q_16_dpk - $row['S_23_dpk'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_dpk > $sum2){
        $S_24_1_dpk = $S_24_dpk - $sum2;
}else{
        $S_24_1_dpk = $S_24_dpk;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_dpk = $row['New_C_18_dpk'] + $row['New_Exp_19_dpk'] + $row['New_Dead_20_dpk']+ $row['New_other_22_dpk'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_dpk = $New_Q_16_dpk - $New_clear_23_dpk;
$S_28_dpk = $row['S_9_dpk']+$New_clear_23_dpk; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_dpk = 0;
$NewPersen_dpk = 0;
$AllPersen_dpk = 0;
if ($row['All_01_dpk'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_dpk = ($row['S_9_dpk'] / $row['All_01_dpk']) * 100;
	$OldPersen_dpk = round($OldPersen_dpk,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_dpk = ($row['S_23_dpk'] / $row['All_01_dpk']) * 100;
	$NewPersen_dpk = round($NewPersen_dpk,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_dpk != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_dpk = ($S_28_dpk / $S_27_dpk) * 100;
	$AllPersen_dpk = round($AllPersen_dpk,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.บางลาย  ////
		
$New_All_15_bgl = $row['New_BF_13_bgl']+$row['New_M_14_bgl'];     //  
$S_27_bgl = $row['Old_All_1_bgl']+$New_All_15_bgl;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_bgl = $row['Old_Exp_1_bgl']+$row['Old_Exp_2_bgl'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_bgl = $row['Old_C_4_bgl'] + $Old_Exp_5_bgl + $row['Old_Dead_6_bgl']+ $row['Old_other_8_bgl'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_bgl = $row['Old_C_4_bgl'] + $row['Old_Exp_1_bgl'] + $row['Old_Dead_6_bgl']+ $row['Old_other_8_bgl'];
$Old_Q_clear_10_bgl = $row['Old_Q_2_bgl'] - $Old_Q_clear_bgl;
$Old_NQ_clear_11_bgl = $row['Old_NQ_3_bgl'] - $row['Old_Exp_2_bgl'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_bgl'] >$sum2){
    $New_BF_13_bgl = $row['New_BF_13_bgl']-$sum2;
}else{
    $New_BF_13_bgl = $row['New_BF_13_bgl'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_bgl'] > $sum2){
    $New_Q_16_bgl = $row['New_Q_16_bgl']-$sum2;
}else{
    $New_Q_16_bgl = $row['New_Q_16_bgl'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_bgl = $New_Q_16_bgl - $row['S_23_bgl'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_bgl > $sum2){
        $S_24_1_bgl = $S_24_bgl - $sum2;
}else{
        $S_24_1_bgl = $S_24_bgl;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_bgl = $row['New_C_18_bgl'] + $row['New_Exp_19_bgl'] + $row['New_Dead_20_bgl']+ $row['New_other_22_bgl'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_bgl = $New_Q_16_bgl - $New_clear_23_bgl;
$S_28_bgl = $row['S_9_bgl']+$New_clear_23_bgl; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_bgl = 0;
$NewPersen_bgl = 0;
$AllPersen_bgl = 0;
if ($row['All_01_bgl'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_bgl = ($row['S_9_bgl'] / $row['All_01_bgl']) * 100;
	$OldPersen_bgl = round($OldPersen_bgl,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_bgl = ($row['S_23_bgl'] / $row['All_01_bgl']) * 100;
	$NewPersen_bgl = round($NewPersen_bgl,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_bgl != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_bgl = ($S_28_bgl / $S_27_bgl) * 100;
	$AllPersen_bgl = round($AllPersen_bgl,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
/////  สภ.ย่านยาว  ////
		
$New_All_15_yyo = $row['New_BF_13_yyo']+$row['New_M_14_yyo'];     //  
$S_27_yyo = $row['Old_All_1_yyo']+$New_All_15_yyo;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5_yyo = $row['Old_Exp_1_yyo']+$row['Old_Exp_2_yyo'];
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear_yyo = $row['Old_C_4_yyo'] + $Old_Exp_5_yyo + $row['Old_Dead_6_yyo']+ $row['Old_other_8_yyo'];		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
$Old_Q_clear_yyo = $row['Old_C_4_yyo'] + $row['Old_Exp_1_yyo'] + $row['Old_Dead_6_yyo']+ $row['Old_other_8_yyo'];
$Old_Q_clear_10_yyo = $row['Old_Q_2_yyo'] - $Old_Q_clear_yyo;
$Old_NQ_clear_11_yyo = $row['Old_NQ_3_yyo'] - $row['Old_Exp_2_yyo'];
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13_yyo'] >$sum2){
    $New_BF_13_yyo = $row['New_BF_13_yyo']-$sum2;
}else{
    $New_BF_13_yyo = $row['New_BF_13_yyo'];
}
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16_yyo'] > $sum2){
    $New_Q_16_yyo = $row['New_Q_16_yyo']-$sum2;
}else{
    $New_Q_16_yyo = $row['New_Q_16_yyo'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
$S_24_yyo = $New_Q_16_yyo - $row['S_23_yyo'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_yyo > $sum2){
        $S_24_1_yyo = $S_24_yyo - $sum2;
}else{
        $S_24_1_yyo = $S_24_yyo;
    }
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23_yyo = $row['New_C_18_yyo'] + $row['New_Exp_19_yyo'] + $row['New_Dead_20_yyo']+ $row['New_other_22_yyo'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24_yyo = $New_Q_16_yyo - $New_clear_23_yyo;
$S_28_yyo = $row['S_9_yyo']+$New_clear_23_yyo; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
$OldPersen_yyo = 0;
$NewPersen_yyo = 0;
$AllPersen_yyo = 0;
if ($row['All_01_yyo'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_yyo = ($row['S_9_yyo'] / $row['All_01_yyo']) * 100;
	$OldPersen_yyo = round($OldPersen_yyo,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_yyo = ($row['S_23_yyo'] / $row['All_01_yyo']) * 100;
	$NewPersen_yyo = round($NewPersen_yyo,2);  // จุดทศนิยม 2 ตำแหน่ง
}
if ($S_27_yyo != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_yyo = ($S_28_yyo / $S_27_yyo) * 100;
	$AllPersen_yyo = round($AllPersen_yyo,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
		
?>
<!-- ภ.จว.พิจิตร -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left; align-content: center; align-items: center; font-size: 20px; font-weight: bold' nowrap><span style="height:22.8pt; background-color:aqua; align-content: center; align-items: center" ><?= $name_provincial ?></span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: bold'>
     <?= $row['All_01'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: bold'>
     <?= $row['All_Q_02'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: bold'>&nbsp;<?= $row['All_Hold_03'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: bold'>
    <?= $row['All_NQ_04'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: bold'>&nbsp;<?= $row['Old_All_1'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: bold'>&nbsp;<?= $row['Old_Q_2'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: bold'><?= $row['Old_NQ_3'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: bold'>&nbsp;<?php echo (( $row['Old_C_4'] > 0) ? $row['Old_C_4'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: bold'>&nbsp;<?php echo (($Old_Exp_5 > 0) ? $Old_Exp_5 : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: bold'>&nbsp;<?php echo (($row['Old_Dead_6'] > 0) ? $row['Old_Dead_6'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: bold'><?php echo (($row['Old_Hold_7'] > 0) ? $row['Old_Hold_7'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: bold'>&nbsp;<?php echo (($row['Old_other_8'] > 0) ? $row['Old_other_8'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: bold'>&nbsp;<?php echo (($Old_clear > 0) ? $Old_clear : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: bold'>&nbsp;<?php echo (($Old_Q_clear_10 > 0) ? ($Old_Q_clear_10)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: bold'>&nbsp;<?php echo (($row['Old_NQ_3'] > 0) ? ($row['Old_NQ_3'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: bold'>&nbsp;<?= $OldPersen ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: bold'>&nbsp;<?php echo (($New_BF_13 > 0) ? ($New_BF_13)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: bold'>&nbsp;<?php echo (($row['New_M_14'] > 0) ? $row['New_M_14']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: bold'>&nbsp;<?php echo (($New_All_15 > 0) ? ($New_All_15)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: bold'><?php echo (($New_Q_16 > 0) ? ($New_Q_16)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: bold'><?php echo (($row['New_NQ_17'] > 0) ? $row['New_NQ_17'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: bold'><?php echo (($row['New_C_18'] > 0) ? $row['New_C_18'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: bold'><?php echo (($row['New_Exp_19'] > 0) ? $row['New_Exp_19'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: bold'><?php echo (($row['New_Dead_20'] > 0) ? $row['New_Dead_20'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: bold'><?php echo (($row['New_Hold_21'] > 0) ? $row['New_Hold_21'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: bold'><?php echo (($row['New_other_22'] > 0) ? $row['New_other_22'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: bold'><?php echo (($New_clear_23 > 0) ? $New_clear_23 : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: bold'><?php echo (($New_Q_bl_24 > 0) ? ($New_Q_bl_24)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: bold'><?php echo (($row['S_25'] > 0) ? ($row['S_25'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: bold'><?= $NewPersen ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: bold'><?= $S_27 ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: bold'><?= $S_28 ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: bold'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.ดงเจริญ -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.ดงเจริญ</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_djr'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_djr'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_djr'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_djr'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_djr'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_djr'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_djr'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_djr'] > 0) ? $row['Old_C_4_djr'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_djr > 0) ? $Old_Exp_5_djr : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_djr'] > 0) ? $row['Old_Dead_6_djr'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_djr'] > 0) ? $row['Old_Hold_7_djr'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_djr'] > 0) ? $row['Old_other_8_djr'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_djr > 0) ? $Old_clear_djr : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_djr > 0) ? ($Old_Q_clear_10_djr)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_djr'] > 0) ? ($row['Old_NQ_3_djr'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_djr ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_djr > 0) ? ($New_BF_13_djr)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_djr'] > 0) ? $row['New_M_14_djr']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_djr > 0) ? ($New_All_15_djr)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_djr > 0) ? ($New_Q_16_djr)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_djr'] > 0) ? $row['New_NQ_17_djr'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_djr'] > 0) ? $row['New_C_18_djr'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_djr'] > 0) ? $row['New_Exp_19_djr'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_djr'] > 0) ? $row['New_Dead_20_djr'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_djr'] > 0) ? $row['New_Hold_21_djr'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_djr'] > 0) ? $row['New_other_22_djr'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_djr > 0) ? $New_clear_23_djr : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_djr > 0) ? ($New_Q_bl_24_djr)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_djr'] > 0) ? ($row['S_25_djr'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_djr ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_djr ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_djr ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_djr > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_djr < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_djr ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.ตะพานหิน -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.ตะพานหิน</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_tph'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_tph'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_tph'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->

  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_tph'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_tph'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_tph'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_tph'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_tph'] > 0) ? $row['Old_C_4_tph'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_tph > 0) ? $Old_Exp_5_tph : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_tph'] > 0) ? $row['Old_Dead_6_tph'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_tph'] > 0) ? $row['Old_Hold_7_tph'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_tph'] > 0) ? $row['Old_other_8_tph'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_tph > 0) ? $Old_clear_tph : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_tph > 0) ? ($Old_Q_clear_10_tph)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_tph'] > 0) ? ($row['Old_NQ_3_tph'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_tph ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_tph > 0) ? ($New_BF_13_tph)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_tph'] > 0) ? $row['New_M_14_tph']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_tph > 0) ? ($New_All_15_tph)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_tph > 0) ? ($New_Q_16_tph)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_tph'] > 0) ? $row['New_NQ_17_tph'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_tph'] > 0) ? $row['New_C_18_tph'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_tph'] > 0) ? $row['New_Exp_19_tph'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_tph'] > 0) ? $row['New_Dead_20_tph'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_tph'] > 0) ? $row['New_Hold_21_tph'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_tph'] > 0) ? $row['New_other_22_tph'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_tph > 0) ? $New_clear_23_tph : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_tph > 0) ? ($New_Q_bl_24_tph)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_tph'] > 0) ? ($row['S_25_tph'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_tph ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_tph ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_tph ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_tph > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_tph < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_tph ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.่ทับคล้อ -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.ทับคล้อ</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_tko'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_tko'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_tko'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_tko'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_tko'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_tko'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_tko'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_tko'] > 0) ? $row['Old_C_4_tko'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_tko > 0) ? $Old_Exp_5_tko : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_tko'] > 0) ? $row['Old_Dead_6_tko'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_tko'] > 0) ? $row['Old_Hold_7_tko'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_tko'] > 0) ? $row['Old_other_8_tko'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_tko > 0) ? $Old_clear_tko : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_tko > 0) ? ($Old_Q_clear_10_tko)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_tko'] > 0) ? ($row['Old_NQ_3_tko'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_tko ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_tko > 0) ? ($New_BF_13_tko)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_tko'] > 0) ? $row['New_M_14_tko']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_tko > 0) ? ($New_All_15_tko)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_tko > 0) ? ($New_Q_16_tko)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_tko'] > 0) ? $row['New_NQ_17_tko'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_tko'] > 0) ? $row['New_C_18_tko'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_tko'] > 0) ? $row['New_Exp_19_tko'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_tko'] > 0) ? $row['New_Dead_20_tko'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_tko'] > 0) ? $row['New_Hold_21_tko'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_tko'] > 0) ? $row['New_other_22_tko'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_tko > 0) ? $New_clear_23_tko : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_tko > 0) ? ($New_Q_bl_24_tko)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_tko'] > 0) ? ($row['S_25_tko'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_tko ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_tko ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_tko ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_tko > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_tko < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_tko ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.่บางมูลนาก -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.บางมูลนาก</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_pmn'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_pmn'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_pmn'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_pmn'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_pmn'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_pmn'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_pmn'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_pmn'] > 0) ? $row['Old_C_4_pmn'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_pmn > 0) ? $Old_Exp_5_pmn : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_pmn'] > 0) ? $row['Old_Dead_6_pmn'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_pmn'] > 0) ? $row['Old_Hold_7_pmn'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_pmn'] > 0) ? $row['Old_other_8_pmn'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_pmn > 0) ? $Old_clear_pmn : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_pmn > 0) ? ($Old_Q_clear_10_pmn)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_pmn'] > 0) ? ($row['Old_NQ_3_pmn'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_pmn ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_pmn > 0) ? ($New_BF_13_pmn)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_pmn'] > 0) ? $row['New_M_14_pmn']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_pmn > 0) ? ($New_All_15_pmn)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_pmn > 0) ? ($New_Q_16_pmn)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_pmn'] > 0) ? $row['New_NQ_17_pmn'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_pmn'] > 0) ? $row['New_C_18_pmn'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_pmn'] > 0) ? $row['New_Exp_19_pmn'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_pmn'] > 0) ? $row['New_Dead_20_pmn'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_pmn'] > 0) ? $row['New_Hold_21_pmn'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_pmn'] > 0) ? $row['New_other_22_pmn'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_pmn > 0) ? $New_clear_23_pmn : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_pmn > 0) ? ($New_Q_bl_24_pmn)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_pmn'] > 0) ? ($row['S_25_pmn'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_pmn ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_pmn ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_pmn ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_pmn > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_pmn < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_pmn ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.บึงนาราง -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.บึงนาราง</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_bnr'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_bnr'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_bnr'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_bnr'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_bnr'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_bnr'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_bnr'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_bnr'] > 0) ? $row['Old_C_4_bnr'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_bnr > 0) ? $Old_Exp_5_bnr : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_bnr'] > 0) ? $row['Old_Dead_6_bnr'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_bnr'] > 0) ? $row['Old_Hold_7_bnr'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_bnr'] > 0) ? $row['Old_other_8_bnr'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_bnr > 0) ? $Old_clear_bnr : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_bnr > 0) ? ($Old_Q_clear_10_bnr)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_bnr'] > 0) ? ($row['Old_NQ_3_bnr'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_bnr ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_bnr > 0) ? ($New_BF_13_bnr)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_bnr'] > 0) ? $row['New_M_14_bnr']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_bnr > 0) ? ($New_All_15_bnr)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_bnr > 0) ? ($New_Q_16_bnr)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_bnr'] > 0) ? $row['New_NQ_17_bnr'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_bnr'] > 0) ? $row['New_C_18_bnr'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_bnr'] > 0) ? $row['New_Exp_19_bnr'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_bnr'] > 0) ? $row['New_Dead_20_bnr'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_bnr'] > 0) ? $row['New_Hold_21_bnr'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_bnr'] > 0) ? $row['New_other_22_bnr'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_bnr > 0) ? $New_clear_23_bnr : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_bnr > 0) ? ($New_Q_bl_24_bnr)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_bnr'] > 0) ? ($row['S_25_bnr'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_bnr ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_bnr ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_bnr ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_bnr > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_bnr < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_bnr ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.โพทะเล -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.โพทะเล</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_ptl'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_ptl'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_ptl'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_ptl'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_ptl'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_ptl'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_ptl'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_ptl'] > 0) ? $row['Old_C_4_ptl'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_ptl > 0) ? $Old_Exp_5_ptl : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_ptl'] > 0) ? $row['Old_Dead_6_ptl'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_ptl'] > 0) ? $row['Old_Hold_7_ptl'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_ptl'] > 0) ? $row['Old_other_8_ptl'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_ptl > 0) ? $Old_clear_ptl : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_ptl > 0) ? ($Old_Q_clear_10_ptl)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_ptl'] > 0) ? ($row['Old_NQ_3_ptl'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_ptl ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_ptl > 0) ? ($New_BF_13_ptl)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_ptl'] > 0) ? $row['New_M_14_ptl']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_ptl > 0) ? ($New_All_15_ptl)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_ptl > 0) ? ($New_Q_16_ptl)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_ptl'] > 0) ? $row['New_NQ_17_ptl'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_ptl'] > 0) ? $row['New_C_18_ptl'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_ptl'] > 0) ? $row['New_Exp_19_ptl'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_ptl'] > 0) ? $row['New_Dead_20_ptl'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_ptl'] > 0) ? $row['New_Hold_21_ptl'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_ptl'] > 0) ? $row['New_other_22_ptl'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_ptl > 0) ? $New_clear_23_ptl : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_ptl > 0) ? ($New_Q_bl_24_ptl)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_ptl'] > 0) ? ($row['S_25_ptl'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_ptl ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_ptl ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_ptl ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_ptl > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_ptl < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_ptl ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.เมืองพิจิตร -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.เมืองพิจิตร</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_mpj'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_mpj'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_mpj'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_mpj'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_mpj'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_mpj'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_mpj'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_mpj'] > 0) ? $row['Old_C_4_mpj'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_mpj > 0) ? $Old_Exp_5_mpj : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_mpj'] > 0) ? $row['Old_Dead_6_mpj'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_mpj'] > 0) ? $row['Old_Hold_7_mpj'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_mpj'] > 0) ? $row['Old_other_8_mpj'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_mpj > 0) ? $Old_clear_mpj : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_mpj > 0) ? ($Old_Q_clear_10_mpj)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_mpj'] > 0) ? ($row['Old_NQ_3_mpj'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_mpj ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_mpj > 0) ? ($New_BF_13_mpj)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_mpj'] > 0) ? $row['New_M_14_mpj']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_mpj > 0) ? ($New_All_15_mpj)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_mpj > 0) ? ($New_Q_16_mpj)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_mpj'] > 0) ? $row['New_NQ_17_mpj'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_mpj'] > 0) ? $row['New_C_18_mpj'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_mpj'] > 0) ? $row['New_Exp_19_mpj'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_mpj'] > 0) ? $row['New_Dead_20_mpj'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_mpj'] > 0) ? $row['New_Hold_21_mpj'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_mpj'] > 0) ? $row['New_other_22_mpj'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_mpj > 0) ? $New_clear_23_mpj : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_mpj > 0) ? ($New_Q_bl_24_mpj)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_mpj'] > 0) ? ($row['S_25_mpj'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_mpj ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_mpj ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_mpj ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_mpj > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_mpj < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_mpj ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.วชิรบารมี -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.วชิรบารมี</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_wcr'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_wcr'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_wcr'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_wcr'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_wcr'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_wcr'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_wcr'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_wcr'] > 0) ? $row['Old_C_4_wcr'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_wcr > 0) ? $Old_Exp_5_wcr : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_wcr'] > 0) ? $row['Old_Dead_6_wcr'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_wcr'] > 0) ? $row['Old_Hold_7_wcr'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_wcr'] > 0) ? $row['Old_other_8_wcr'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_wcr > 0) ? $Old_clear_wcr : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_wcr > 0) ? ($Old_Q_clear_10_wcr)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_wcr'] > 0) ? ($row['Old_NQ_3_wcr'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_wcr ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_wcr > 0) ? ($New_BF_13_wcr)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_wcr'] > 0) ? $row['New_M_14_wcr']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_wcr > 0) ? ($New_All_15_wcr)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_wcr > 0) ? ($New_Q_16_wcr)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_wcr'] > 0) ? $row['New_NQ_17_wcr'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_wcr'] > 0) ? $row['New_C_18_wcr'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_wcr'] > 0) ? $row['New_Exp_19_wcr'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_wcr'] > 0) ? $row['New_Dead_20_wcr'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_wcr'] > 0) ? $row['New_Hold_21_wcr'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_wcr'] > 0) ? $row['New_other_22_wcr'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_wcr > 0) ? $New_clear_23_wcr : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_wcr > 0) ? ($New_Q_bl_24_wcr)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_wcr'] > 0) ? ($row['S_25_wcr'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_wcr ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_wcr ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_wcr ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_wcr > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_wcr < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_wcr ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.วังทรายพูน -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.วังทรายพูน</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_wsp'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_wsp'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_wsp'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_wsp'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_wsp'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_wsp'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_wsp'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_wsp'] > 0) ? $row['Old_C_4_wsp'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_wsp > 0) ? $Old_Exp_5_wsp : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_wsp'] > 0) ? $row['Old_Dead_6_wsp'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_wsp'] > 0) ? $row['Old_Hold_7_wsp'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_wsp'] > 0) ? $row['Old_other_8_wsp'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_wsp > 0) ? $Old_clear_wsp : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_wsp > 0) ? ($Old_Q_clear_10_wsp)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_wsp'] > 0) ? ($row['Old_NQ_3_wsp'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_wsp ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_wsp > 0) ? ($New_BF_13_wsp)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_wsp'] > 0) ? $row['New_M_14_wsp']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_wsp > 0) ? ($New_All_15_wsp)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_wsp > 0) ? ($New_Q_16_wsp)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_wsp'] > 0) ? $row['New_NQ_17_wsp'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_wsp'] > 0) ? $row['New_C_18_wsp'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_wsp'] > 0) ? $row['New_Exp_19_wsp'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_wsp'] > 0) ? $row['New_Dead_20_wsp'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_wsp'] > 0) ? $row['New_Hold_21_wsp'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_wsp'] > 0) ? $row['New_other_22_wsp'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_wsp > 0) ? $New_clear_23_wsp : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_wsp > 0) ? ($New_Q_bl_24_wsp)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_wsp'] > 0) ? ($row['S_25_wsp'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_wsp ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_wsp ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_wsp ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_wsp > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_wsp < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_wsp ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.สากเหล็ก -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.สากเหล็ก</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_skl'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_skl'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_skl'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_skl'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_skl'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_skl'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_skl'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_skl'] > 0) ? $row['Old_C_4_skl'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_skl > 0) ? $Old_Exp_5_skl : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_skl'] > 0) ? $row['Old_Dead_6_skl'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_skl'] > 0) ? $row['Old_Hold_7_skl'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_skl'] > 0) ? $row['Old_other_8_skl'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_skl > 0) ? $Old_clear_skl : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_skl > 0) ? ($Old_Q_clear_10_skl)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_skl'] > 0) ? ($row['Old_NQ_3_skl'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_skl ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_skl > 0) ? ($New_BF_13_skl)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_skl'] > 0) ? $row['New_M_14_skl']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_skl > 0) ? ($New_All_15_skl)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_skl > 0) ? ($New_Q_16_skl)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_skl'] > 0) ? $row['New_NQ_17_skl'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_skl'] > 0) ? $row['New_C_18_skl'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_skl'] > 0) ? $row['New_Exp_19_skl'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_skl'] > 0) ? $row['New_Dead_20_skl'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_skl'] > 0) ? $row['New_Hold_21_skl'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_skl'] > 0) ? $row['New_other_22_skl'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_skl > 0) ? $New_clear_23_skl : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_skl > 0) ? ($New_Q_bl_24_skl)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_skl'] > 0) ? ($row['S_25_skl'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_skl ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_skl ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_skl ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_skl > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_skl < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_skl ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.สามง่าม -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.สามง่าม</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_smk'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_smk'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_smk'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_smk'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_smk'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_smk'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_smk'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_smk'] > 0) ? $row['Old_C_4_smk'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_smk > 0) ? $Old_Exp_5_smk : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_smk'] > 0) ? $row['Old_Dead_6_smk'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_smk'] > 0) ? $row['Old_Hold_7_smk'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_smk'] > 0) ? $row['Old_other_8_smk'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_smk > 0) ? $Old_clear_smk : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_smk > 0) ? ($Old_Q_clear_10_smk)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_smk'] > 0) ? ($row['Old_NQ_3_smk'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_smk ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_smk > 0) ? ($New_BF_13_smk)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_smk'] > 0) ? $row['New_M_14_smk']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_smk > 0) ? ($New_All_15_smk)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_smk > 0) ? ($New_Q_16_smk)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_smk'] > 0) ? $row['New_NQ_17_smk'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_smk'] > 0) ? $row['New_C_18_smk'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_smk'] > 0) ? $row['New_Exp_19_smk'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_smk'] > 0) ? $row['New_Dead_20_smk'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_smk'] > 0) ? $row['New_Hold_21_smk'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_smk'] > 0) ? $row['New_other_22_smk'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_smk > 0) ? $New_clear_23_smk : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_smk > 0) ? ($New_Q_bl_24_smk)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_smk'] > 0) ? ($row['S_25_smk'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_smk ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_smk ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_smk ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_smk > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_smk < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_smk ?>
</div>
	 </td> <!--  -->
 </tr>
	    
<!-- สภ.หนองโสน -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.หนองโสน</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_nsn'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_nsn'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_nsn'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_nsn'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_nsn'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_nsn'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_nsn'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_nsn'] > 0) ? $row['Old_C_4_nsn'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_nsn > 0) ? $Old_Exp_5_nsn : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_nsn'] > 0) ? $row['Old_Dead_6_nsn'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_nsn'] > 0) ? $row['Old_Hold_7_nsn'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_nsn'] > 0) ? $row['Old_other_8_nsn'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_nsn > 0) ? $Old_clear_nsn : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_nsn > 0) ? ($Old_Q_clear_10_nsn)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_nsn'] > 0) ? ($row['Old_NQ_3_nsn'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_nsn ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_nsn > 0) ? ($New_BF_13_nsn)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_nsn'] > 0) ? $row['New_M_14_nsn']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_nsn > 0) ? ($New_All_15_nsn)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_nsn > 0) ? ($New_Q_16_nsn)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_nsn'] > 0) ? $row['New_NQ_17_nsn'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_nsn'] > 0) ? $row['New_C_18_nsn'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_nsn'] > 0) ? $row['New_Exp_19_nsn'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_nsn'] > 0) ? $row['New_Dead_20_nsn'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_nsn'] > 0) ? $row['New_Hold_21_nsn'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_nsn'] > 0) ? $row['New_other_22_nsn'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_nsn > 0) ? $New_clear_23_nsn : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_nsn > 0) ? ($New_Q_bl_24_nsn)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_nsn'] > 0) ? ($row['S_25_nsn'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_nsn ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_nsn ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_nsn ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_nsn > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_nsn < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_nsn ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.โพธิ์ประทับช้าง -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.โพธิ์ประทับช้าง</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_ptc'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_ptc'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_ptc'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_ptc'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_ptc'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_ptc'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_ptc'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_ptc'] > 0) ? $row['Old_C_4_ptc'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_ptc > 0) ? $Old_Exp_5_ptc : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_ptc'] > 0) ? $row['Old_Dead_6_ptc'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_ptc'] > 0) ? $row['Old_Hold_7_ptc'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_ptc'] > 0) ? $row['Old_other_8_ptc'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_ptc > 0) ? $Old_clear_ptc : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_ptc > 0) ? ($Old_Q_clear_10_ptc)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_ptc'] > 0) ? ($row['Old_NQ_3_ptc'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_ptc ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_ptc > 0) ? ($New_BF_13_ptc)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_ptc'] > 0) ? $row['New_M_14_ptc']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_ptc > 0) ? ($New_All_15_ptc)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_ptc > 0) ? ($New_Q_16_ptc)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_ptc'] > 0) ? $row['New_NQ_17_ptc'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_ptc'] > 0) ? $row['New_C_18_ptc'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_ptc'] > 0) ? $row['New_Exp_19_ptc'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_ptc'] > 0) ? $row['New_Dead_20_ptc'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_ptc'] > 0) ? $row['New_Hold_21_ptc'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_ptc'] > 0) ? $row['New_other_22_ptc'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_ptc > 0) ? $New_clear_23_ptc : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_ptc > 0) ? ($New_Q_bl_24_ptc)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_ptc'] > 0) ? ($row['S_25_ptc'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_ptc ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_ptc ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_ptc ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_ptc > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_ptc < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_ptc ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.วังหว้า -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.วังหว้า</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_wgw'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_wgw'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_wgw'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_wgw'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_wgw'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_wgw'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_wgw'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_wgw'] > 0) ? $row['Old_C_4_wgw'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_wgw > 0) ? $Old_Exp_5_wgw : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_wgw'] > 0) ? $row['Old_Dead_6_wgw'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_wgw'] > 0) ? $row['Old_Hold_7_wgw'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_wgw'] > 0) ? $row['Old_other_8_wgw'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_wgw > 0) ? $Old_clear_wgw : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_wgw > 0) ? ($Old_Q_clear_10_wgw)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_wgw'] > 0) ? ($row['Old_NQ_3_wgw'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_wgw ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_wgw > 0) ? ($New_BF_13_wgw)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_wgw'] > 0) ? $row['New_M_14_wgw']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_wgw > 0) ? ($New_All_15_wgw)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_wgw > 0) ? ($New_Q_16_wgw)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_wgw'] > 0) ? $row['New_NQ_17_wgw'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_wgw'] > 0) ? $row['New_C_18_wgw'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_wgw'] > 0) ? $row['New_Exp_19_wgw'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_wgw'] > 0) ? $row['New_Dead_20_wgw'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_wgw'] > 0) ? $row['New_Hold_21_wgw'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_wgw'] > 0) ? $row['New_other_22_wgw'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_wgw > 0) ? $New_clear_23_wgw : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_wgw > 0) ? ($New_Q_bl_24_wgw)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_wgw'] > 0) ? ($row['S_25_wgw'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_wgw ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_wgw ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_wgw ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_wgw > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_wgw < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_wgw ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.ดงป่าคำ -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.ดงป่าคำ</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_dpk'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_dpk'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_dpk'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_dpk'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_dpk'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_dpk'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_dpk'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_dpk'] > 0) ? $row['Old_C_4_dpk'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_dpk > 0) ? $Old_Exp_5_dpk : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_dpk'] > 0) ? $row['Old_Dead_6_dpk'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_dpk'] > 0) ? $row['Old_Hold_7_dpk'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_dpk'] > 0) ? $row['Old_other_8_dpk'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_dpk > 0) ? $Old_clear_dpk : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_dpk > 0) ? ($Old_Q_clear_10_dpk)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_dpk'] > 0) ? ($row['Old_NQ_3_dpk'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_dpk ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_dpk > 0) ? ($New_BF_13_dpk)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_dpk'] > 0) ? $row['New_M_14_dpk']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_dpk > 0) ? ($New_All_15_dpk)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_dpk > 0) ? ($New_Q_16_dpk)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_dpk'] > 0) ? $row['New_NQ_17_dpk'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_dpk'] > 0) ? $row['New_C_18_dpk'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_dpk'] > 0) ? $row['New_Exp_19_dpk'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_dpk'] > 0) ? $row['New_Dead_20_dpk'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_dpk'] > 0) ? $row['New_Hold_21_dpk'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_dpk'] > 0) ? $row['New_other_22_dpk'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_dpk > 0) ? $New_clear_23_dpk : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_dpk > 0) ? ($New_Q_bl_24_dpk)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_dpk'] > 0) ? ($row['S_25_dpk'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_dpk ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_dpk ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_dpk ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_dpk > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_dpk < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_dpk ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.บางลาย -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.บางลาย</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_bgl'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_bgl'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_bgl'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_bgl'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_bgl'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_bgl'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_bgl'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_bgl'] > 0) ? $row['Old_C_4_bgl'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_bgl > 0) ? $Old_Exp_5_bgl : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_bgl'] > 0) ? $row['Old_Dead_6_bgl'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_bgl'] > 0) ? $row['Old_Hold_7_bgl'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_bgl'] > 0) ? $row['Old_other_8_bgl'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_bgl > 0) ? $Old_clear_bgl : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_bgl > 0) ? ($Old_Q_clear_10_bgl)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_bgl'] > 0) ? ($row['Old_NQ_3_bgl'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_bgl ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_bgl > 0) ? ($New_BF_13_bgl)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_bgl'] > 0) ? $row['New_M_14_bgl']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_bgl > 0) ? ($New_All_15_bgl)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_bgl > 0) ? ($New_Q_16_bgl)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_bgl'] > 0) ? $row['New_NQ_17_bgl'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_bgl'] > 0) ? $row['New_C_18_bgl'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_bgl'] > 0) ? $row['New_Exp_19_bgl'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_bgl'] > 0) ? $row['New_Dead_20_bgl'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_bgl'] > 0) ? $row['New_Hold_21_bgl'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_bgl'] > 0) ? $row['New_other_22_bgl'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_bgl > 0) ? $New_clear_23_bgl : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_bgl > 0) ? ($New_Q_bl_24_bgl)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_bgl'] > 0) ? ($row['S_25_bgl'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_bgl ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_bgl ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_bgl ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_bgl > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_bgl < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_bgl ?>
</div>
	 </td> <!--  -->
 </tr>
	  
<!-- สภ.ย่านยาว -->
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.ย่านยาว</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01_yyo'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02_yyo'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03_yyo'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04_yyo'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1_yyo'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2_yyo'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3_yyo'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4_yyo'] > 0) ? $row['Old_C_4_yyo'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5_yyo > 0) ? $Old_Exp_5_yyo : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6_yyo'] > 0) ? $row['Old_Dead_6_yyo'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7_yyo'] > 0) ? $row['Old_Hold_7_yyo'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8_yyo'] > 0) ? $row['Old_other_8_yyo'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear_yyo > 0) ? $Old_clear_yyo : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10_yyo > 0) ? ($Old_Q_clear_10_yyo)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3_yyo'] > 0) ? ($row['Old_NQ_3_yyo'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_yyo ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13_yyo > 0) ? ($New_BF_13_yyo)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14_yyo'] > 0) ? $row['New_M_14_yyo']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15_yyo > 0) ? ($New_All_15_yyo)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->

  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16_yyo > 0) ? ($New_Q_16_yyo)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17_yyo'] > 0) ? $row['New_NQ_17_yyo'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18_yyo'] > 0) ? $row['New_C_18_yyo'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19_yyo'] > 0) ? $row['New_Exp_19_yyo'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20_yyo'] > 0) ? $row['New_Dead_20_yyo'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21_yyo'] > 0) ? $row['New_Hold_21_yyo'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22_yyo'] > 0) ? $row['New_other_22_yyo'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23_yyo > 0) ? $New_clear_23_yyo : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24_yyo > 0) ? ($New_Q_bl_24_yyo)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_yyo'] > 0) ? ($row['S_25_yyo'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_yyo ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_yyo ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_yyo ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen_yyo > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen_yyo < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen_yyo ?>
</div>
	 </td> <!--  -->
 </tr>
	    
	  
<?php
    }
} catch (PDOException $e) {
    // Handle errors
    echo 'Query failed: ' . $e->getMessage();
    exit;
}
?>
  </tbody>
</table>

</div>    
&nbsp;&nbsp;<button class="btn btn-primary btn-lg mb-4" onClick="do_print()"> พิมพ์รายงาน </button>
<p style="font-size: 18px">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;เป้าหมาย ตร. ให้จับกุม หมายเก่า 3 % &nbsp;&nbsp;&nbsp;&nbsp; รวมหมายเก่า+หมายใหม่ 6.5 %</p>
    <hr>
<!--<p>&nbsp;&nbsp;<a href="#catch_old_w.php" class="btn btn-secondary btn-lg mb-4" >จับหมายเก่า</a>&nbsp;&nbsp;&nbsp;&nbsp;<a href="#catch_new_w.php" class="btn btn-secondary btn-lg mb-4" >จับหมายใหม่</a>&nbsp;&nbsp;</p>-->
<p>&nbsp;</p>
<script>
function dates_change()  // คำสั่งเปลี่ยนเดือน ปี
{
    var ms = $("#mi_cl_month option:selected").val();
    var ys = $("#mi_cl_year option:selected").val();
    window.location = "/Activity/Show_Activity7_PJ_station.php?&month=" + ms + "&year=" + ys + "&rnd=" + Math.random();
}
  </script>
</body>

</html>
<!-- script สำหรับส่งไปพิมพ์รายงาน  -->
<script>
    
    function do_print()
    {
        var ms = $("#mi_cl_month option:selected").val();
        var ys = $("#mi_cl_year option:selected").val();
        var newW = window.open("/Activity/Show_Activity7_PJ_station_print.php?&month=" + ms + "&year=" + ys + "&rnd=" + Math.random(), "_print", "menubar=no,titlebar=no,toolbar=no,status=no");
        newW.print();
        //window.print();
    }
    
</script>
