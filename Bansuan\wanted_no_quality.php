<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';
include '../Head_image.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']); 

 $sqls = "SELECT
            (SELECT COUNT(*) FROM wm_tb_warrant WHERE station=:station AND wr_cl_status='1' AND NOT wr_cl_qty=2) AS Total1,
            (SELECT COUNT(*) FROM wm_tb_warrant WHERE station=:station AND wr_cl_status='3') AS Total2,
            (SELECT COUNT(*) FROM wm_tb_warrant WHERE station=:station AND wr_cl_qty=2 AND wr_cl_status='1') AS Total3";

    $stmt = $pdo->prepare($sqls);
	$stmt->bindParam(':station',$station);
    $stmt->execute();
    $row5 = $stmt->fetch(PDO::FETCH_ASSOC);

    //print_r($row5);
    $total = $row5['Total1'];
    $total2 = $row5['Total2'];
    $total3 = $row5['Total3'];

//ยอดคงเหลือทั้งหมด
    $tatal_all = $total + $total2 + $total3;

?>
<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
        <base target="_top">
		<link rel="stylesheet" href="../bootstrap/css/bootstrap.rtl.min.css">
		<link rel="stylesheet" href="../bootstrap-3.4.1-dist/css/bootstrap.min.css">
  		<script src="../jQuery/jquery-3.6.1.min.js"></script>
 		<script src="../bootstrap-3.4.1-dist/js/bootstrap.min.js"></script>
        <link rel="stylesheet" href="../bootstrap/css/bootstrap-grid.css">

<div>
	<?php
	include('../users_info.php');
	?>	
</div>
<div class="align-self-start">
	<div class=" h3 text-center  alert alert-danger mb-4 mt-4 " role="alert" >ข้อมูลหมายจับไม่มีคุณภาพ (ไม่ทราบตัว)  <?= $name_station ?></div>
		<div align="left">
			<!--&nbsp;&nbsp;<a href="Add_wanted_no_quality.php" class="btn btn-primary btn-lg mb-4" >เพิ่มข้อมูล</a>-->   
            &nbsp;&nbsp;<a href="/Bansuan/?rnd=<?= rand(); ?>&page=wanted" class="btn btn-warning btn-lg mb-4" >ย้อนกลับ</a> 
			&nbsp;&nbsp;
		</div>
		<div align="left" style="font-size: 20px ">
			&nbsp;<a style="background-color: yellow ; padding: 10px">หมายจับ ณ ปัจจุบัน มี : <b style="color: crimson; font-size: 26px"> <?= $tatal_all ?> </b>หมาย แยกเป็น 
            <span style="color: #FC0408"> หมายที่ต้องดำเนินการจับกุม จำนวน</span> <span style="color: #FC0408"><b style="color: crimson ;font-size: 26px"> <?= $total ?> </b> หมาย </span>
			หมายไม่มีคุณภาพ จำนวน <b style="color: crimson; font-size: 26px"><?= $total3 ?></b>  หมาย  และ อายัดตัว จำนวน <b style="color: crimson; font-size: 26px">  <?= $total2 ?> </b> หมาย </span>
            &nbsp;&nbsp;</a> <br>
		</div>
  <table class="table table-striped table-hover table-bordered table-sm mb-4 mt-4" >
    <tbody>
    <tr class="container">
      <th>ลำดับ</th>
	  <th>ชื่อ-สกุล</th>
	  <th>เลขบัตร</th>
	  <th>ศาล</th>
      <th nowrap="nowrap">เลขที่หมาย</th>
      <th>วันที่ออกหมาย</th>
      <th>ขาดอายุความ</th>
      <th>เลขคดี</th>
	  <th>ฐานความผิด</th>
	  <th>สถานะ</th>
	  <th>พงส.</th>
      <th>ผู้รับผิดชอบ</th>
      <th> </th>
    </tr>
	  

<?php
$sql = "SELECT T1.*, T2.ps_cl_name, T2.ps_cl_surname , T3.sw_cl_name  " .		//เงื่อนไข การเลือกข้อมูล จาก 2 ตาราง
		"FROM wm_tb_warrant AS T1 " .
		"LEFT JOIN wm_tb_personal AS T2 ON T1.wr_cl_idcard = T2.ps_cl_idcard " .
        "LEFT JOIN wm_tb_status_warrent AS T3 ON T1.wr_cl_status = T3.sw_cl_aid " .
		"WHERE (T1.station=:station) AND (T1.`wr_cl_qty`=2) AND (T1.wr_cl_status='1') " .			//เพิ่มเงื่อนไข เลือกสถานี และสถานะมีผลใช้
		"ORDER BY T1.wr_cl_date ASC ";
	
    $stmt = $pdo->prepare($sql);
	$stmt->bindParam(':station',$station);
    $stmt->execute();
    
// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no = 1;
while($rowWd2 = $stmt->fetch(PDO::FETCH_ASSOC))	{
    
    //ฟังก์ชั่น วันที่ ดึงจาก condb
    if($rowWd2['wr_cl_date']!=''){
        $strDate = DateThai( $rowWd2['wr_cl_date'] );
    }else{
        $strDate = '';
    }
    
     if($rowWd2['wr_cl_expire']!=''){
        $strDate2 = DateThai( $rowWd2['wr_cl_expire'] );
    }else{
        $strDate2 = '';
    }
    
?>
	  
    <tr class="container">
      <td>&nbsp;<?= $no ?></td>
	  <td nowrap="nowrap"><?= $rowWd2['ps_cl_name'] .' ' . $rowWd2['ps_cl_surname'] ?></td>
	  <td><?= $rowWd2['wr_cl_idcard'] ?></td>
	  <td ><?= $rowWd2['wr_cl_court'] ?></td>
      <td nowrap="nowrap"><?= $rowWd2['wr_cl_no'] ?></td>
      <td nowrap="nowrap"><?= $strDate ?></td>
      <td>&nbsp;<?= $strDate2 ?>&nbsp;</td>
      <td><?= $rowWd2['wr_cl_crimes_no'] ?></td>
	  <td><?= $rowWd2['wr_cl_crimes_type'] ?></td>
	  <td nowrap="nowrap"><?= $rowWd2['sw_cl_name'] ?></td>
	  <td><?= $rowWd2['wr_cl_officer'] ?></td>
      <td nowrap="nowrap"><?= $rowWd2['wr_cl_police'] ?></td>

      <td><button onClick="go_detail('<?=$rowWd2['wr_cl_idcard'] ?>')" class="btn btn-success">รายละเอียด</button>
        <!--<p>&nbsp;</p> <button onClick="go_progress('<?=$rowWd2['wr_cl_idcard'] ?>')" class="btn btn-warning">ติดตามหมาย</button>--></td>   
    </tr>
<?php
	$no++;
}//while
?>
  </tbody>
</table>
</div>
<hr>

<script>
function go_detail(pcid)	
{
	window.location = "/WatchmanData/Show_All.php?pcid=" +pcid + "&rnd=" +"&page=warrant";
}
function go_progress(pcid)	
{
	window.location = "progress.php?pcid=" +pcid + "&rnd=" +"&page=wanted";
}
</script>
