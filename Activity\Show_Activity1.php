<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';
include '../Head_image.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// เฉพาะ ยูเซอร์ บ้านเสวน = 1 เท่านั้นที่จะดูหน้านี้ได้
/*if($user['ua_view_data'] != 1)
{
	header("Location: /WatchmanData/main.php");
}*/

$id = isset($_GET['mi_cl_aid']) ? $_GET['mi_cl_aid'] : '';

?>

<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
        <base target="_top">
		<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
		<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
  		<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
 		<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js"></script>

<style type="text/css">
body{
    font-size:12px; 
}
.textAlignVer{
    display:block;
    filter: flipv fliph;
    -webkit-transform: rotate(-90deg); 
    -moz-transform: rotate(-90deg); 
    transform: rotate(-90deg); 
    position:relative;
    width:20px;
    white-space:nowrap;
    font-size:12px;
    margin-bottom:10px;
}
</style>
<div class="container-fluid" align="left">
<div class=" h3 text-center  alert alert-success mb-4 mt-4 " role="alert" >กิจกรรม 1 ข้อมูลท้องถิ่น ระบบ CRIMES</div>

<div align="left">
<div>
    <table width="84%" border="0" cellspacing="1" cellpadding="1">
      <tbody>
        <tr>
          <td width="10%">&nbsp;<a href="/WM/index.php?rnd=<?= rand(); ?>&page=report" class="btn btn-warning btn-lg mb-4" >ย้อนกลับ</a></td>

            <td width="1%">&nbsp;&nbsp;</td>
          <td width="19%" style="margin-right: auto">&nbsp;&nbsp;<a href="/WM/Show_Gen_person_tb.php" class="btn btn-primary  btn-lg mb-4" >บัญชีข้อมูลบุคคลทั่วไป</a>&nbsp;&nbsp;&nbsp;</td>
        <td width="1%">&nbsp;&nbsp;</td>
          <td width="69%">&nbsp;&nbsp;<a href="/WM/Show_crimes_person_tb.php" class="btn btn-primary  btn-lg mb-4" >บัญชีข้อมูลบุคคลเกี่ยวข้องกับอาชญากรรม</a>&nbsp;</td>
        </tr>
      </tbody>
    </table>
</div>
<br>
<div align="center"><b>บุคคลท้องถิ่น <?= $name_station ?></b></div>
<div class="table-responsive">
<a href="#Add_person_data.php" class="btn btn-success btn-lg mb-4" >เพิ่มข้อมูล</a>
<table width="100%" height="171" border="1" cellpadding="1" cellspacing="1" class="table-bordered table-hover mb-4 mt-4">
  <tbody>
    <tr>
      <td width="3%" rowspan="2" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">ลำดับ</td>
      <td width="3%" rowspan="2" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">สถานี</td>
      <td height="46" colspan="3" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">ข้อมูลรายงาน</td>
      <td colspan="3" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">ข้อมูลจากระบบ CRIMES</td>
      <td width="22%" rowspan="2" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">ความสมบูรณ์ในการบันทึกข้อมูลในระบบ CRIMES (%)</td>
      <td width="7%" rowspan="2" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">วันที่ตรวจสอบ</td>
      <td colspan="3" rowspan="2" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy"></td>
    </tr>
    <tr>
	  <td width="9%" height="58" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">บุคคลทั่วไป (คน)</td>
      <td width="17%" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">บุคคลที่เกี่ยวข้องกับอาชญากรรม (คน)</td>
      <td width="3%" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">รวม (คน)</td>
      <td width="7%" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">บุคคลทั่วไป (คน)</td>
      <td width="17%" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">บุคคลที่เกี่ยวข้องกับอาชญากรรม (คน)</td>
      <td width="3%" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">รวม (คน)</td>
      </tr>
	  
<?php
$sql = "SELECT *, T2.station_name AS pol_st ".
        "FROM wm_tb_crimes_record AS T1 ".
        "LEFT JOIN wm_tb_police_station2 AS T2 ON T1.station = T2.station_code " .
        "WHERE station = :station " . // Add the WHERE clause for station=:station
        "ORDER BY aid_crimes_record ASC ";

if ($id != '') {
    $sql .= "AND aid_crimes_record = :id "; // Use ".=" to append to the existing query
}

$stmt = $pdo->prepare($sql);

if ($id != '') {
    $stmt->bindParam(':id', $id);
}

$stmt->bindParam(':station', $station);

try {
    $stmt->execute();
} catch (PDOException $e) {
    echo '$sql Failed: '. $e->getMessage();
}
              
// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no = 1;
while($row_mi = $stmt->fetch(PDO::FETCH_ASSOC))	//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
{
    
    //ฟังก์ชั่น วันที่ ดึงจาก condb
 $strDate = DateThai( $row_mi["date_record"] );
    
$sum_person_report = $row_mi["person_gen_report"]+$row_mi["person_crimes_report"];
$sum_person_record = $row_mi["person_gen_record"]+$row_mi["person_crimes_record"];
$sum_place_report = $row_mi["place_gen_report"]+$row_mi["place_crimes_report"];
$sum_place_record = $row_mi["place_gen_record"]+$row_mi["place_crimes_record"];
    
$image1 = $row_mi["image1"];
if($image1 != '') {
	$image1 = "<img src='{$image1}' width='100%'> ";
}
    
$image2 = $row_mi["image2"];
if($image2 != '') {
	$image2 = "<img src='{$image2}' width='100%'> ";
}
   
$image3 = $row_mi["image3"];
if($image3 != '') {
	$image3 = "<img src='{$image3}' width='100%'> ";
}
   
?>	

    <tr>
      <td><?= $row_mi["aid_crimes_record"]?></td>  
      <td nowrap>&nbsp;<?= $row_mi["pol_st"]?>&nbsp;</td>
      <td>&nbsp;<?= $row_mi["person_gen_report"] ?>&nbsp;</td>
      <td>&nbsp;<?= $row_mi["person_crimes_report"]?>&nbsp;</td>
      <td>&nbsp;<?= $sum_person_report ?>&nbsp;</td> <!-- ใช้การคำนวน -->
      <td>&nbsp;<?= $row_mi["person_gen_record"]?>&nbsp;</td>
      <td>&nbsp;<?= $row_mi["person_crimes_record"]?>&nbsp;</td>
      <td>&nbsp;<?= $sum_person_record ?>&nbsp;</td>  <!-- ใช้การคำนวน -->
      <td>&nbsp;<?= $row_mi["record_complete"]?>&nbsp;%</td>
      <td nowrap="nowrap">&nbsp;<?= $strDate ?>&nbsp;</td>
	  
	  <td width="5%"><a href="Edit_person_crimes_record.php?id=<?= $row_mi["aid_crimes_record"] ?>" class="btn btn-warning mb-4" >แก้ไข</a> </td>
    </tr>
  </tbody>

</table>
</div>
<hr><br>
<?php
	   if ($station == 6707) {
		   echo '<div align="center"><a href="https://docs.google.com/spreadsheets/d/1AXvtnzCt3bIOKGeY3EUz0ArqyaVXEBCq/edit?usp=share_link&ouid=116660768149609913852&rtpof=true&sd=true" class="btn btn-success btn-lg mb-4" target="_blank" >ไฟล์ Excel</a>
		   &nbsp;&nbsp;<img src="<?= $row_mi["image1"] ?>&nbsp;&nbsp;</div><br>';
	   }
?>
<hr>
    
<div align="center"><b>สถานที่ท้องถิ่น <?= $name_station ?></b></div>
<div class="table-responsive">
<a href="Add_place_data.php" class="btn btn-success btn-lg mb-4" >เพิ่มข้อมูล</a>
<table width="100%" height="171" border="1" cellpadding="1" cellspacing="1" class="table-bordered table-hover mb-4 mt-4">
  <tbody>
    <tr>
      <td width="3%" rowspan="2" bgcolor="#5FFF00" style="color: #00000; text-align: center; border-color: navy">ลำดับ</td>
      <td width="3%" rowspan="2" bgcolor="#5FFF00" style="color: #00000; text-align: center; border-color: navy">สถานี</td>
      <td height="46" colspan="3" bgcolor="#5FFF00" style="color: #00000; text-align: center; border-color: navy">ข้อมูลรายงาน</td>
      <td colspan="3" bgcolor="#5FFF00" style="color: #00000; text-align: center; border-color: navy">ข้อมูลจากระบบ CRIMES</td>
      <td width="22%" rowspan="2" bgcolor="#5FFF00" style="color: #00000; text-align: center; border-color: navy">ความสมบูรณ์ในการบันทึกข้อมูลในระบบ CRIMES (%)</td>
      <td width="7%" rowspan="2" bgcolor="#5FFF00" style="color: #00000; text-align: center; border-color: navy">วันที่ตรวจสอบ</td>
      <td colspan="3" rowspan="2" bgcolor="#5FFF00" style="color: #00000; text-align: center; border-color: navy"></td>
    </tr>
    <tr>
	  <td width="9%" height="58" bgcolor="#5FFF00" style="color: #00000; text-align: center; border-color: navy">สถานที่ทั่วไป (แห่ง)</td>
      <td width="17%" bgcolor="#5FFF00" style="color: #00000; text-align: center; border-color: navy">สถานที่ที่เกี่ยวข้องกับอาชญากรรม (แห่ง)</td>
      <td width="3%" bgcolor="#5FFF00" style="color: #00000; text-align: center; border-color: navy">รวม (แห่ง)</td>
      <td width="7%" bgcolor="#5FFF00" style="color: #00000; text-align: center; border-color: navy">สถานที่ทั่วไป (แห่ง)</td>
      <td width="17%" bgcolor="#5FFF00" style="color: #00000; text-align: center; border-color: navy">สถานที่ที่เกี่ยวข้องกับอาชญากรรม (แห่ง)</td>
      <td width="3%" bgcolor="#5FFF00" style="color: #00000; text-align: center; border-color: navy">รวม (แห่ง)</td>
      </tr>

    <tr>
	  <td><?= $row_mi["aid_crimes_record"]?></td>  
      <td nowrap>&nbsp;<?= $row_mi["pol_st"]?>&nbsp;</td>
      <td>&nbsp;<?= $row_mi["place_gen_report"] ?>&nbsp;</td>
      <td>&nbsp;<?= $row_mi["place_crimes_report"]?>&nbsp;</td>
      <td>&nbsp;<?= $sum_place_report ?>&nbsp;</td> <!-- ใช้การคำนวน -->
      <td>&nbsp;<?= $row_mi["place_gen_record"]?>&nbsp;</td>
      <td>&nbsp;<?= $row_mi["place_crimes_record"]?>&nbsp;</td>
      <td>&nbsp;<?= $sum_place_record ?>&nbsp;</td> <!-- ใช้การคำนวน -->
      <td>&nbsp;<?= $row_mi["record_complete_p"]?>&nbsp;%</td>
      <td nowrap="nowrap">&nbsp;<?= $strDate ?>&nbsp;</td>
	  
	  <td width="5%"><a href="Edit_place_crimes_record.php?id=<?= $row_mi["aid_crimes_record"] ?>" class="btn btn-warning mb-4" >แก้ไข</a> </td>
    </tr>
  </tbody>

</table>
</div>
<hr>
<?php
	   if ($station == 6707) {
		   echo '<div align="center"><a href="https://docs.google.com/spreadsheets/d/1kEpgQqZv2rLa4FVn-qGreGEtIix67TXT/edit?usp=share_link&ouid=116660768149609913852&rtpof=true&sd=true" class="btn btn-success btn-lg mb-4" target="_blank" >ไฟล์ Excel</a>&nbsp;&nbsp;<img src="<?= $row_mi["image2"] ?></div><br>';
	   }
?>
<hr>
    
<div align="center"><b>กล้องวงจรปิด <?= $name_station ?></b></div>
<div class="table-responsive">
<a href="Add_cctv_data.php" class="btn btn-success btn-lg mb-4" >เพิ่มข้อมูล</a>
<table width="100%" height="171" border="1" cellpadding="1" cellspacing="1" class="table-bordered table-hover mb-4 mt-4">
  <tbody>
    <tr>
      <td width="3%" bgcolor="#FFD900" style="color: #00000; text-align: center; border-color: navy">ลำดับ</td>
      <td width="3%" bgcolor="#FFD900" style="color: #00000; text-align: center; border-color: navy">สถานี</td>
      <td bgcolor="#FFD900" style="color: #00000; text-align: center; border-color: navy">ข้อมูลรายงาน</td>
      <td bgcolor="#FFD900" style="color: #00000; text-align: center; border-color: navy">ข้อมูลจากระบบ CRIMES</td>
      <td width="22%" bgcolor="#FFD900" style="color: #00000; text-align: center; border-color: navy">ความสมบูรณ์ในการบันทึกข้อมูลในระบบ CRIMES (%)</td>
      <td width="7%" bgcolor="#FFD900" style="color: #00000; text-align: center; border-color: navy">วันที่ตรวจสอบ</td>
      <td colspan="3" bgcolor="#FFD900" style="color: #00000; text-align: center; border-color: navy"></td>
    </tr>

    <tr>
	  <td><?= $row_mi["aid_crimes_record"]?></td>  
      <td nowrap>&nbsp;<?= $row_mi["pol_st"]?>&nbsp;</td>
      <td>&nbsp;<?= $row_mi["cctv_report"] ?>&nbsp;</td>
      <td>&nbsp;<?= $row_mi["cctv_record"]?>&nbsp;</td>
      <td>&nbsp;<?= $row_mi["record_complete_c"]?>&nbsp;%</td>
      <td nowrap="nowrap">&nbsp;<?= $strDate ?>&nbsp;</td>
	  
	  <td width="5%"><a href="Edit_cctv_on_crimes.php?id=<?= $row_mi["aid_crimes_record"] ?>" class="btn btn-warning mb-4" >แก้ไข</a> </td>
    </tr>
  </tbody>

</table>
</div>
<hr>
<?php
	   if ($station == 6707) {
		   echo '<div align="center"><a href="https://docs.google.com/spreadsheets/d/1PuB9g8nLvEKzpJJ4TYhgo24HA5Rczxow/edit?usp=share_link&ouid=116660768149609913852&rtpof=true&sd=true" class="btn btn-success btn-lg mb-4" target="_blank" >ไฟล์ Excel</a>&nbsp;&nbsp;<img src="<?= $row_mi["image3"] ?></div><br>';
	   }
?>
<hr>
<?php
	$no++;
}//while
?>
    
<script>
function Del(url)
{
	if(confirm("คุณจะลบข้อมูล หรือ ไม่ ?"))
	{
		window.location = url;		
	}
}
</script>

<br>
<hr>
<br>

    

