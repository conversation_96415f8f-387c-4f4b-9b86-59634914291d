<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);


// สำหรับเลือก จังหวัด อำเภอ ตำบล
include("../config.inc.php");
include("../classes/class.database.inc.php");
// สำหรับเลือก จังหวัด อำเภอ ตำบล
$conn2 = get_connection(); // connect to mySQLDB

// สำหรับเลือกสถานีตำรวจทั่วประเทศ
$region = isset($_GET['region']) ? $_GET['region'] : 0;
$provincial = isset($_GET['provincial']) ? $_GET['provincial'] : 0;
//$station = isset($_GET['station']) ? $_GET['station'] : 0;

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Add Data Personal for General</title>
<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 20px;
    }
    .form-control::placeholder {
  color: sandybrown;
}
</style>
    <!--  เลือกเพศอัตโนมัต -->
<script language="javascript" src="/jQuery/jquery-3.5.1.min.js"></script>   
<script>
    function doPrefixChange()
    {
        var selected = $("#ps_cl_prefix option:selected").val();
        if(selected == "นาย") {
            $("#ps_cl_sex1").prop("checked", true);
        }
        else if(selected == "นาง"){
            $("#ps_cl_sex2").prop("checked", true);
        }
        else if(selected == "น.ส."){
            $("#ps_cl_sex2").prop("checked", true);
        }
        else if(selected == "ด.ช."){
            $("#ps_cl_sex1").prop("checked", true);
        }
        else if(selected == "ด.ญ."){
            $("#ps_cl_sex2").prop("checked", true);
        }
        else {
            $("#ps_cl_sex1").prop("checked", false);
            $("#ps_cl_sex2").prop("checked", false);
            $("#ps_cl_sex3").prop("checked", false);
        }
    }
</script>

<!-- สำหรับวันเกิด แบบใหม่  -->
<link rel="stylesheet" href="../datepicker_th/jquery.datetimepicker.css" type="text/css">
<script src="../datepicker_th/jquery-1.8.3.min.js"></script>  
<script src="../datepicker_th/jquery.datetimepicker.full.js"></script>
<script type="text/javascript"> 
$(function(){
	
	$.datetimepicker.setLocale('th'); // ต้องกำหนดเสมอถ้าใช้ภาษาไทย และ เป็นปี พ.ศ.
	// กรณีใช้แบบ input
    $("#testdate5").datetimepicker({
        timepicker:false,
        format:'Y-m-d',  // กำหนดรูปแบบวันที่ ที่ใช้ เป็น 00-00-0000			
        lang:'th',  // ต้องกำหนดเสมอถ้าใช้ภาษาไทย และ เป็นปี พ.ศ.
		onSelectDate:function(dp,$input){
			var yearT=new Date(dp).getFullYear()-0;  
			var yearTH=yearT+543;
			var fulldate=$input.val();
			var fulldateTH=fulldate.replace(yearT,yearTH);
			$input.val(fulldateTH);
		},
    });       
	// กรณีใช้กับ input ต้องกำหนดส่วนนี้ด้วยเสมอ เพื่อปรับปีให้เป็น ค.ศ. ก่อนแสดงปฏิทิน
	$("#testdate5").on("mouseenter mouseleave",function(e){
		var dateValue=$(this).val();
		if(dateValue!=""){
				var arr_date=dateValue.split("-"); // ถ้าใช้ตัวแบ่งรูปแบบอื่น ให้เปลี่ยนเป็นตามรูปแบบนั้น
				// ในที่นี้อยู่ในรูปแบบ 00-00-0000 เป็น d-m-Y  แบ่งด่วย - ดังนั้น ตัวแปรที่เป็นปี จะอยู่ใน array
				//  ตัวที่สอง arr_date[2] โดยเริ่มนับจาก 0 
				if(e.type=="mouseenter"){
					var yearT=arr_date[2]-543;
				}		
				if(e.type=="mouseleave"){
					var yearT=parseInt(arr_date[2])+543;
				}	
				dateValue=dateValue.replace(arr_date[2],yearT);
				$(this).val(dateValue);													
		}		
	});
    
    
});
</script>
</head>

<body>
	
	<div class="container">
	<div class="row">
	<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> กรอกข้อมูลบุคคล </div>
	<form action="Save_Personal_for_bansuan.php" method="POST" enctype="multipart/form-data" class="body">
                
        <label>ประเภทบุคคล</label><br>
    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <label><input name="ps_cl_type" id="ps_cl_type1" type="radio" value="1" onclick="type_change()" /> บุคคลทั่วไป </label>
    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
        <label><input name="ps_cl_type" id="ps_cl_type2" type="radio" value="2" onclick="type_change()" /> บุคคลเกี่ยวข้องอาชญากรรม </label>
            <br><br>
            <script>
                function type_change() {
                    var ps_cl_type1 = $("#ps_cl_type1");
                    if (ps_cl_type1.is(":checked")) {
                        $('#ps_cl_gen_detail').prop("disabled", false);
                        //$('#ps_cl_gen_detail').val("");
                        $('#ps_cl_gen_detail').val("33"); // set default value to 33
                        $('#ps_cl_CrimeType2').prop("disabled", true);
                        $('#ps_cl_CrimeType2').val("");
                    }
                    var ps_cl_type2 = $("#ps_cl_type2");
                    if (ps_cl_type2.is(":checked")) {
                        $('#ps_cl_gen_detail').prop("disabled", true);
                        $('#ps_cl_gen_detail').val("");
                        $('#ps_cl_CrimeType2').prop("disabled", false);
                    }
                }
            </script>

            <label>บุคคลทั่วไป 32 ประเภท</label><br>
            <input type="hidden" name="ps_cl_gen_detail" id="ps_cl_gen_detail_hidden" value="">
            <select name="ps_cl_gen_detail" id="ps_cl_gen_detail" class="form-control" Required >
            <option value="">เลือก</option>
                <?php
                    $res_ty = $pdo->prepare("SELECT * FROM wm_tb_gen_detail ORDER BY sort ASC");
                    try{
                        $res_ty->execute();
                    }catch(PDOException $e){
                        echo 'Query $res_ty Failed: '. $e->getMessage();
                    }
                
                    while($row_ty = $res_ty->fetch(PDO::FETCH_ASSOC))
                    {
                        if($ps_type == $row_ty['gd_cl_aid']) {
                            $selected = "selected";
                        }
                        else {
                            $selected = "";
                        }
                        $gd_cl_aid = htmlspecialchars($row_ty['gd_cl_aid'], ENT_QUOTES, 'UTF-8');
                        $gd_cl_name = htmlspecialchars($row_ty['gd_cl_name'], ENT_QUOTES, 'UTF-8');
                        
                        echo "<option value='$gd_cl_aid' {$selected}> $gd_cl_name </option>";
                    }
                ?>
            </select>

            <label>บุคคลอาชญากรรม 23 ประเภท</label>
            <input type="hidden" name="ps_cl_CrimeType2" id="ps_cl_CrimeType2_hidden" value="">
            <select id="ps_cl_CrimeType2" name="ps_cl_CrimeType2" class="form-select form-select-sm" >
            <option value="">เลือก</option>
                <?php
                    $res4 = $pdo->prepare("SELECT * FROM wm_tb_crimes_type order by cm_cl_aid ASC");
                    try{
                        $res4->execute();
                    }catch(PDOException $e){
                        echo 'Query $res4 Failed: '. $e->getMessage();
                    }
                
                    while($row4 = $res4->fetch(PDO::FETCH_ASSOC))
                    {
                        if($crime4 == $row4['cm_cl_aid']) {
                            $selected = "selected";
                        }
                        else {
                            $selected = "";
                        }
                        $cm_cl_aid = htmlspecialchars($row4['cm_cl_aid'], ENT_QUOTES, 'UTF-8');
                        $cm_cl_name = htmlspecialchars($row4['cm_cl_name'], ENT_QUOTES, 'UTF-8');
                        
                        echo "<option value='$cm_cl_aid' {$selected}> $cm_cl_name </option>";
                    }
                ?>
            </select>
        <br>
        
        <span style="color: #1203F8">
        <label>เลขบัตรประชาชน <span style="color: #F90004">* จำเป็น</span> </label>
        </span>
        <input type = "text" name="ps_cl_idcard" class="form-control" autocomplete="off" placeholder="กรอกเลขบัตรประชาชน 13 หลัก"  Required >

        <label>คำนำหน้า</label>
        <select id="ps_cl_prefix" name="ps_cl_prefix" class="form-select form-select-sm" aria-label=".form-select-sm example" onChange="doPrefixChange()">
            <option value="" selected> </option>
            <option value="นาย">นาย</option>
            <option value="นาง">นาง</option>
            <option value="น.ส.">น.ส.</option>
            <option value="ด.ช.">ด.ช.</option>
            <option value="ด.ญ.">ด.ญ.</option>
        </select>

        <label>เพศ</label><br>
            <input name="ps_cl_sex" id="ps_cl_sex1" type="radio" value="ชาย" checked="checked" />ชาย</label>
            <label><input name="ps_cl_sex" id="ps_cl_sex2" type="radio" value="หญิง" />หญิง</label>					<label><input name="ps_cl_sex" id="ps_cl_sex3" type="radio" value="LGBTQ" />LGBTQ</label><br>

        <label>ชื่อ <span style="color: #F90004">* จำเป็น</span> </label>
        <input type = "text" name = "ps_cl_name" class="form-control" placeholder="กรอกชื่อจริง"   Required  >
        <label>นามสกุล <span style="color: #F90004">* จำเป็น</span> </label>
        <input type = "text" name = "ps_cl_surname" class="form-control" placeholder="กรอกนามสกุลจริง"  Required >
        <label>ชื่อเล่น</label>
        <input type = "text" name = "ps_cl_nickname" class="form-control" placeholder="กรอกชื่อเล่น" >
        
        <label>วันเดือนปีเกิด</label>
        <input type="text" name="ps_cl_birthday2" id="datepicker" value="" placeholder="เพื่อข้อมูลถูกต้อง กรุณาเลือกจากปฎิทินด้านล่าง" class="form-control" style="width:350px;" autocomplete="off">
        <script type="text/javascript"> 
            $(function(){

                $.datetimepicker.setLocale('th'); // ต้องกำหนดเสมอถ้าใช้ภาษาไทย และ เป็นปี พ.ศ.
                // กรณีใช้แบบ input
                $("#datepicker").datetimepicker({
                    timepicker:false,
                    format:'Y-m-d',  // กำหนดรูปแบบวันที่ ที่ใช้ เป็น 00-00-0000			
                    lang:'th',  // ต้องกำหนดเสมอถ้าใช้ภาษาไทย และ เป็นปี พ.ศ.
                    onSelectDate:function(dp,$input){
                        var yearT=new Date(dp).getFullYear()-0;  
                        var yearTH=yearT;  // ถ้าจะให้แสดงเป็น พ.ศ. ให้เป็น yearT+543
                        var fulldate=$input.val();
                        var fulldateTH=fulldate.replace(yearT,yearTH);
                        $input.val(fulldateTH);
                    },
                });       
                // กรณีใช้กับ input ต้องกำหนดส่วนนี้ด้วยเสมอ เพื่อปรับปีให้เป็น ค.ศ. ก่อนแสดงปฏิทิน
                $("#datepicker").on(function(e){
                    var dateValue=$(this).val();
                    if(dateValue!=""){
                            var arr_date=dateValue.split("-"); // ถ้าใช้ตัวแบ่งรูปแบบอื่น ให้เปลี่ยนเป็นตามรูปแบบนั้น
                            // ในที่นี้อยู่ในรูปแบบ 00-00-0000 เป็น d-m-Y  แบ่งด่วย - ดังนั้น ตัวแปรที่เป็นปี จะอยู่ใน array
                            //  ตัวที่สอง arr_date[2] โดยเริ่มนับจาก 0 
                            dateValue=dateValue.replace(arr_date[0],yearT);
                            $(this).val(dateValue);													
                    }		
                });


            });
            </script>
        <!-- สำหรับ วันที่ แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ -->
        
        <label>บิดา</label>
        <input type = "text" name = "ps_cl_father" class="form-control" placeholder="ชื่อบิดา" >
        <label>เลขบัตรประชาชน บิดา</label>
        <input type = "text" name = "ps_cl_father_pid" class="form-control" placeholder="เลขบัตรประชาชน บิดา" >
        <label>มารดา</label>
        <input type = "text" name = "ps_cl_mother" class="form-control" placeholder="ชื่อมารดา" >
        <label>เลขบัตรประชาชน มารดา</label>
        <input type = "text" name = "ps_cl_mother_pid" class="form-control" placeholder="เลขบัตรประชาชน มารดา" >

        <label>สถานะสมรส</label>
		<select id="ps_cl_marital_status" name="ps_cl_marital_status" class="form-select form-select-sm" >
			<option value="" selected> </option>
			<option value="โสด">โสด</option>
			<option value="สมรส">สมรส</option>
			<option value="หย่า">หย่า</option>
			<option value="หม้าย">หม้าย</option>
			<option value="อื่น ๆ">อื่น ๆ</option>
		</select>
        <br>
        
        <label>สถานะการมีชีวิตอยู่</label><br>
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            <label><input name="ps_cl_death" id="ps_cl_death1" type="radio" value="1" onclick="death_change()" /> ยังมีชีวิตอยู่ </label>
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
            <label><input name="ps_cl_death" id="ps_cl_death2" type="radio" value="2" onclick="death_change()" /> เสียชีวิตแล้ว </label>
        <br><br>
        
        <label>วันเดือนปีที่เสียชีวิต</label>
        <input type="hidden" name="date_of_death" >
        <input type="text" name="date_of_death" id="datepicker2" value="" placeholder="เพื่อข้อมูลถูกต้อง กรุณาเลือกจากปฎิทินด้านล่าง" class="form-control" style="width:350px;" autocomplete="off">
        <script>
            function death_change()
            {
                var rad1 = $("#ps_cl_death1");
                if(rad1.is(":checked")) 
                {
                    $('#datepicker2').val("");
                    $('#datepicker2').prop("disabled", true);                    
                }
                var rad2 = $("#ps_cl_death2");
                if(rad2.is(":checked")) 
                {
                    $('#datepicker2').prop("disabled", false);
                }
                console.log("rad1:", rad1.is(":checked"), "rad2:", rad2.is(":checked"))
            }
        </script>
        
        <script type="text/javascript"> 
            $(function(){

                $.datetimepicker.setLocale('th'); // ต้องกำหนดเสมอถ้าใช้ภาษาไทย และ เป็นปี พ.ศ.
                // กรณีใช้แบบ input
                $("#datepicker2").datetimepicker({
                    timepicker:false,
                    format:'Y-m-d',  // กำหนดรูปแบบวันที่ ที่ใช้ เป็น 00-00-0000			
                    lang:'th',  // ต้องกำหนดเสมอถ้าใช้ภาษาไทย และ เป็นปี พ.ศ.
                    onSelectDate:function(dp,$input){
                        var yearT=new Date(dp).getFullYear()-0;  
                        var yearTH=yearT;  // ถ้าจะให้แสดงเป็น พ.ศ. ให้เป็น yearT+543
                        var fulldate=$input.val();
                        var fulldateTH=fulldate.replace(yearT,yearTH);
                        $input.val(fulldateTH);
                    },
                });       
                // กรณีใช้กับ input ต้องกำหนดส่วนนี้ด้วยเสมอ เพื่อปรับปีให้เป็น ค.ศ. ก่อนแสดงปฏิทิน
                $("#datepicker2").on(function(e){
                    var dateValue=$(this).val();
                    if(dateValue!=""){
                            var arr_date=dateValue.split("-"); // ถ้าใช้ตัวแบ่งรูปแบบอื่น ให้เปลี่ยนเป็นตามรูปแบบนั้น
                            // ในที่นี้อยู่ในรูปแบบ 00-00-0000 เป็น d-m-Y  แบ่งด่วย - ดังนั้น ตัวแปรที่เป็นปี จะอยู่ใน array
                            //  ตัวที่สอง arr_date[2] โดยเริ่มนับจาก 0 
                            dateValue=dateValue.replace(arr_date[0],yearT);
                            $(this).val(dateValue);													
                    }		
                });
            });
            </script>
        <!-- สำหรับ วันที่ แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ -->
        <br>
        
        <label>สถานีตำรวจ (รับผิดชอบตามที่อยู่) <span style="color: #F90004">* จำเป็น</span></label><br>
                <!-- เลือกสถานีตำรวจ -->
        <label> บช. </label><br>
        <select name="region" id="region" onChange="do_region_change()" class="form-control" Required >
                <option value="">&lt;&lt; เลือก บช. &gt;&gt;</option>
                <?php
			  	$res_re = $conn2->query("SELECT * FROM `wm_tb_police_region` 
                                            ORDER BY `name_region` ASC");
			     $selected = '';
			     while($row_re = $conn2->fetch_row($res_re)) 
                     {
                      $code_region = $row_re['code_region'];
                      $name_region = $row_re['name_region'];
                      // set default provionce to 64 >> sukhothai
                          if($code_region == $region) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$code_region' $selected> $name_region </option>\n";
                     }
			  
			  ?>
              </select>
        
        <label> บก. </label><br>
        <select name="provincial" id="provincial" onChange="do_provincial_change()" class="form-control" >
        <option value="0">&lt;&lt; เลือก บก. &gt;&gt;</option>
                <?php
                if($region > 0)
                {
                    $res_prov = $conn2->query("SELECT * FROM `wm_tb_police_provincial` 
                                                WHERE `region_code`='$region' 
                                                ORDER BY `provincial_code` ASC");
                    $selected = '';
                  while($res_prov = $conn2->fetch_row($res_prov)) 
                  {
                      $provincial_code = $res_prov['provincial_code']; // 
                      $provincial = $res_prov['provincial'];
                      // set default provionce to 64 >> sukhothai
                          if($provincial_code == $provincial) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$provincial_code' $selected> $provincial </option>\n";
                  }
                }	
                ?>
              </select>

         <label> สน./สภ. </label>
        <br>
        <select name="station" id="station" class="form-control" >
                <option value="0">&lt;&lt; เลือก สน./สภ. &gt;&gt;</option>
                <?php
	           if($provincial > 0)
                {
                    $res_st = $conn2->query("SELECT * FROM `wm_tb_police_station2` 
                                                WHERE provincial_code='$provincial_code'
                                                ORDER BY `station_code` ASC");
                    $selected = '';
                  while($row_st = $conn2->fetch_row($res_st)) 
                  {
                      $station_code = $row_st['station_code']; // 
                      $station_name = $row_st['station_name'];
                      // set default provionce to 64 >> sukhothai
                          if($station_code == $station) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$station_code' $selected> $station_name </option>\n";
                  }
                }	
                ?>                                                                     
              </select>
		<br>

        <label>ผู้บันทึก <span style="color: #F90004">* จำเป็น</span> </label>
		<select id="ps_cl_recorder" name="ps_cl_recorder" class="form-select form-select-sm"  placeholder="ระบุชื่อผู้บันทึก" Required >
			<option value="" selected> </option>
            <?php
                    $bsdp = $pdo->prepare("SELECT * FROM `police_name_bsdetective` order by `aid_bsdp` DESC");
                    try{
                        $bsdp->execute();
                    }catch(PDOException $e){
                        echo 'Query $bsdp Failed: '. $e->getMessage();
                    }
            
                    while($row_bsdp = $bsdp->fetch(PDO::FETCH_ASSOC))
                    {
                        $aid_bsdp = htmlspecialchars($row_bsdp['aid_bsdp'], ENT_QUOTES, 'UTF-8');
                        $bsdp_name = htmlspecialchars($row_bsdp['bsdp_name'], ENT_QUOTES, 'UTF-8');
                        
                        echo "<option value='$aid_bsdp'>$bsdp_name</option>";
                    }
                ?>
		</select>
        <br>
        <label>วันที่บันทึกข้อมูล <span style="color: #F90004">* จำเป็น</span> </label>
        <input type="text" name="ps_cl_date_rec" id="datepicker3" value="" placeholder="เพื่อข้อมูลถูกต้อง กรุณาเลือกจากปฎิทินด้านล่าง" class="form-control" style="width:350px;" autocomplete="off" Required >
        <script type="text/javascript"> 
            $(function(){

                $.datetimepicker.setLocale('th'); // ต้องกำหนดเสมอถ้าใช้ภาษาไทย และ เป็นปี พ.ศ.
                // กรณีใช้แบบ input
                $("#datepicker3").datetimepicker({
                    timepicker:false,
                    format:'Y-m-d',  // กำหนดรูปแบบวันที่ ที่ใช้ เป็น 00-00-0000			
                    lang:'th',  // ต้องกำหนดเสมอถ้าใช้ภาษาไทย และ เป็นปี พ.ศ.
                    onSelectDate:function(dp,$input){
                        var yearT=new Date(dp).getFullYear()-0;  
                        var yearTH=yearT;  // ถ้าจะให้แสดงเป็น พ.ศ. ให้เป็น yearT+543
                        var fulldate=$input.val();
                        var fulldateTH=fulldate.replace(yearT,yearTH);
                        $input.val(fulldateTH);
                    },
                });       
                // กรณีใช้กับ input ต้องกำหนดส่วนนี้ด้วยเสมอ เพื่อปรับปีให้เป็น ค.ศ. ก่อนแสดงปฏิทิน
                $("#datepicker3").on(function(e){
                    var dateValue=$(this).val();
                    if(dateValue!=""){
                            var arr_date=dateValue.split("-"); // ถ้าใช้ตัวแบ่งรูปแบบอื่น ให้เปลี่ยนเป็นตามรูปแบบนั้น
                            // ในที่นี้อยู่ในรูปแบบ 00-00-0000 เป็น d-m-Y  แบ่งด่วย - ดังนั้น ตัวแปรที่เป็นปี จะอยู่ใน array
                            //  ตัวที่สอง arr_date[2] โดยเริ่มนับจาก 0 
                            dateValue=dateValue.replace(arr_date[0],yearT);
                            $(this).val(dateValue);													
                    }		
                });


            });
            </script>
        <br>
        <div class="mb-3">
            <label for="formFileMultiple" class="form-label">รูปภาพ</label>
             <input class="form-control" type="file" id="ps_cl_image" name="ps_cl_image" multiple>
        </div>
        <p>
            <input type="submit" value="บันทึกข้อมูล" class="btn btn-success" >
            <td> <a href="Show_Gen_person.php" class="btn btn-warning" >ยกเลิก</a> </td>
        </p>
            <br>
            <br>
            <br>
            <br>
        </form>
        </div>
        </div>
        </div>
<script>
// ฟังก์ชั่น เปลี่ยนภาค (บช.) เป็น ภ.จว. (บก.)
function do_region_change()
{
	var sel_region = document.getElementById("region");		
	var code_region = sel_region.options[sel_region.selectedIndex].value;

	$.get("../ajax/get_provincial.php?region=" + code_region, 
		  function(data, status){
				//alert("Data: " + data + "\nStatus: " + status);
				// jQuery style						
				$('#provincial').find('option').remove().end();
				var data = $.trim(data);
				if(data == '') {
					alert("ไม่พบข้อมูล บก. !");
					return;
				}

					//.append('<option value="whatever">text</option>').val('whatever');
				var datas = data.split(';');
				for(var i=0; i<datas.length; i++) {
					if(datas[i] == '') continue;
					datas[i] = datas[i].split(':');
					var code_region = datas[i][0];
					var name_region = datas[i][1];
					$('#provincial').append('<option value="'+ code_region+'">' + name_region + '</option>');
				}
                // Trigger the change event on the amphur dropdown so the tambon dropdown will be populated     
                $('#provincial').trigger('change');
		});
}
    //เลือก สถานี จากจังหวัด
function do_provincial_change()
{
	var sel_provincial = document.getElementById("region");		
	var provincial_code = sel_provincial.options[sel_provincial.selectedIndex].value;
    
    var sel_provincial2 = document.getElementById("provincial");		
	var station_code = sel_provincial2.options[sel_provincial2.selectedIndex].value;

	$.get("../ajax/get_station.php?region=" + provincial_code + "&provincial=" + station_code, 
			  function(data, status){
					//alert("Data: " + data + "\nStatus: " + status);
					// jQuery style						
					$('#station').find('option').remove().end();
					var data = $.trim(data);
					if(data == '') {
						alert("ไม่พบข้อมูลสถานี !");
						return;
					}

						//.append('<option value="whatever">text</option>').val('whatever');
					var datas = data.split(';');
					for(var i=0; i<datas.length; i++) {
						if(datas[i] == '') continue;
						datas[i] = datas[i].split(':');
						var station_code = datas[i][0];
						var station_name = datas[i][1];
						$('#station').append('<option value="'+ station_code+'">' + station_name + '</option>');
					}
			});
}    
</script>
</body>
</html>
