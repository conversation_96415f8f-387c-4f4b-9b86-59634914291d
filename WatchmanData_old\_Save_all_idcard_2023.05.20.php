<?php
include '../Condb.php';
include '../users.inc.php';
include '../Alert.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);
$ip_address = $_SERVER['REMOTE_ADDR'];
//Log the user activity
$action = 'Save All Crimes'; // You can set the action to a suitable value
//$data = json_encode($_POST); // You can store the $_POST data as a JSON string
$data = str_replace("'", "", compact_array($_POST));
log_activity($acc, $action, $data, $ip_address); // ใช้ function ในไฟล์ Condb.php


$id_cl_aid = $_POST['id_cl_aid'];
$id_cl_idcard = $_POST[ 'id_cl_idcard' ];
$id_cl_no = $_POST[ 'id_cl_no' ];
$id_cl_date = $_POST[ 'id_cl_date' ];
$id_cl_expire = $_POST[ 'id_cl_expire' ];
$id_cl_phone = $_POST[ 'id_cl_phone' ];
$id_cl_detail = $_POST[ 'id_cl_detail' ];
$id_cl_remark = $_POST[ 'id_cl_remark' ];

// save Image
$file1 = $_FILES[ 'id_cl_image' ][ 'tmp_name' ];
if ( is_uploaded_file( $file1 ) ) {
    $id_cl_image = "/policeinnopolis/uploaded/Image1/" . basename( $_FILES[ 'id_cl_image' ][ 'name' ] );	    
    // file + ext    
    $ext = strrchr($id_cl_image, ".");    
    $id_cl_image = "../policeinnopolis/uploaded/Image1/File_" . time() . $ext; // file+time+ext        
    move_uploaded_file( $file1, $id_cl_image ); 
    
} else {
  $id_cl_image = '';
}

$sql ="SELECT * FROM `wm_tb_idcard` WHERE id_cl_aid='$id_cl_aid' ";
$result = mysqli_query($conn,$sql);
$row = mysqli_fetch_array($result);
if($row) {
        $id_cl_aid = $row['id_cl_aid'];
        $sql = "UPDATE wm_tb_idcard SET " .
        "id_cl_idcard = '$id_cl_idcard', " .
		"id_cl_no = '$id_cl_no', " .
		"id_cl_date = '$id_cl_date', " .
		"id_cl_expire = '$id_cl_expire', " .	
		"id_cl_phone = '$id_cl_phone', " .
		"id_cl_detail = '$id_cl_detail', " .
		"id_cl_remark = '$id_cl_remark' " .
		(($id_cl_image == '') ? '' : (",id_cl_image = '$id_cl_image' ")) .  //ใช้ตรวจสอบไฟล์ภาพ ว่ามีอยู่หรือไม่ ถ้ามีไม่ต้องอัพเดต
		"WHERE id_cl_aid = '$id_cl_aid' ";
}else{
        $sql = "INSERT INTO wm_tb_idcard(id_cl_idcard, id_cl_no, id_cl_date, id_cl_expire, id_cl_phone, id_cl_detail, id_cl_remark, id_cl_image) VALUES('$id_cl_idcard', '$id_cl_no', '$id_cl_date', '$id_cl_expire', '$id_cl_phone', '$id_cl_detail', '$id_cl_remark', '$id_cl_image') ";
    }

$result=mysqli_query($conn,$sql);

mysqli_report(MYSQLI_REPORT_ERROR | MYSQLI_REPORT_STRICT);
if ($result) {
    $_SESSION['success'] = "Data has been inserted succesfully";
    showSweetAlert("Saved successfully", "บันทึกข้อมูลเรียบร้อย");
    unset($_SESSION['success']); // Clear the session variable
    header("refresh:2; url=/WatchmanData/Show_All.php?pcid=" . $id_cl_idcard . "&page=idcard");
} else {
    $_SESSION['error'] = "Error";
    showSweetAlert('Error', "บันทึกข้อมูล ไม่สำเร็จ", 'error');
    unset($_SESSION['error']); // Clear the session variable
    header("refresh:2; url=/WatchmanData/Show_All.php?pcid=" . $id_cl_idcard . "&page=idcard");
}

mysqli_close($conn);

?>

