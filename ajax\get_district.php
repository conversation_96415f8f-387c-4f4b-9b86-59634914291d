<?php
//include '../Condb.php';
include("../config.inc.php");
include("../classes/class.database.inc.php");


$province = isset($_GET['province']) ? $_GET['province'] : 64; // sukhothai
$amphure = isset($_GET['amphure']) ? $_GET['amphure'] : 0; // sukhothai



if($amphure > 0)
{
	$conn = get_connection();
	$res = $conn->query("SELECT * FROM `districts` WHERE amphure_code='$amphure' ORDER BY `districts`.`tb_name_th` ASC");
	
	 while($row = $conn->fetch_row($res)) 
	 {
		 $tb_code = $row['tb_id']; // 
		 $tb_name = $row['tb_name_th'];
		 echo "$tb_code:$tb_name;";
	  }			  
}	

?>