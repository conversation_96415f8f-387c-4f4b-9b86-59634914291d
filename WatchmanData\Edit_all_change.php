<?php
include "../Condb.php"; //PDO
include '../users.inc.php';
include '../right_user.php';


// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

$id = $_GET['id'];

$sql = "SELECT * FROM wm_tb_change WHERE cg_cl_aid=:id ";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':id', $id);
    $stmt->execute();
    $row = $stmt->fetch(PDO::FETCH_ASSOC);

$people_name = '';
if($row) 
{
	$pcid = $row["cg_cl_idcard"];		
	//
	$query1 = "SELECT * FROM wm_tb_personal WHERE ps_cl_idcard = :pcid";
    $stmt = $pdo->prepare($query1);
    $stmt->bindParam(':pcid', $pcid);
    $stmt->execute();
    $row1 = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $people_name = $row1['ps_cl_prefix'] . ' ' . $row1['ps_cl_name'] . ' ' . $row1['ps_cl_surname'];
    $people_image = $row1["ps_cl_image"];
    
    if ($people_image != '') {
        $people_image = "<img src='{$people_image}' height='50'> ";
    }
	else {
		die("ไม่พบข้อมูล pcid: $pcid");
	}
}
?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>แก้ไขข้อมูลการเปลี่ยนชื่อ-สกุล</title>
<script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/policeinnopolis/bootstrap/css/bootstrap.rtl.min.css">
<!-- สำหรับ วันที่ แบบ พ.ศ. -->
<link rel="stylesheet" href="http://code.jquery.com/ui/1.9.1/themes/base/jquery-ui.css" />
<script src="http://code.jquery.com/jquery-1.8.2.js"></script>
<script src="/policeinnopolis/js/jquery-ui.js"></script>
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 18px;
    }
input::placeholder {
  color: red;
}
.form-control::placeholder {
  color: sandybrown;
}
</style>
</head>

<body>
	<div class="container" >
	<div class="row">
		<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> แก้ไขการเปลี่ยนแปลงข้อมูล ของ <?= $people_name ?> <?= $people_image ?> </div>
	<form action="Save_all_change.php" method="POST" enctype="multipart/form-data" class="body">	
		<label>ลำดับ</label>
		<input name="cg_cl_aid" type = "text" class="form-control" value= "<?=$row['cg_cl_aid']?>" hidden ="hidden"  >
		<label>เลขบัตรประชาชน</label>
		<input name="cg_cl_idcard" type = "text" class="form-control" value= "<?= $row['cg_cl_idcard']?>" readonly="readonly"  >

        <label>วันที่</label>
        <p><input type="text" name="cg_cl_date" id="datepicker" class="form-control" value= "<?=$row['cg_cl_date']?>" autocomplete="off" ></p>
        <script>
                function set_cal(ele)//function สร้างตัวเลือกปฎิทิน
                {
                  $( ele ).datepicker({
                      onSelect:(date_text)=>
                      {
                        let arr=date_text.split("/");
                        let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])+543).toString();
                        $(ele).val(new_date);
                        $(ele).css("color","");
                      },
                      beforeShow:()=>{

                        if($(ele).val()!="")
                        {
                          let arr=$(ele).val().split("/");
                          let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])-543).toString();
                          $(ele).val(new_date);

                        }

                        $(ele).css("color","white");
                      },
                      onClose:()=>{

                          $(ele).css("color","");

                          if($(ele).val()!="")
                          {
                              let arr=$(ele).val().split("-");
                              if(parseInt(arr[2])<2500)
                              {
                                  let new_date=arr[0]+"-"+arr[1]+"-"+(parseInt(arr[2])).toString();
                                  $(ele).val(new_date);
                              }
                          }
                      },
                      dateFormat:"yy-mm-dd", //กำหนดรูปแบบวันที่เป็น วัน/เดือน/ปี
                      changeMonth:true,//กำหนดให้เลือกเดือนได้
                      changeYear:true,//กำหนดให้เลือกปีได้
                      showOtherMonths:true,//กำหนดให้แสดงวันของเดือนก่อนหน้าได้
                  });
                }
              $(document).ready(function(){
                //เรียก function set_cal เมื่อเปิดหน้าเว็บ โดยส่ง object element ที่มี id เป็น datepicker เป็นพารามิเตอร์
                set_cal( $("#datepicker") );
              })
              </script>
            <!-- สำหรับ วันที่ แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ -->
        
		<label>ชื่อเดิม</label>
		<input type = "text" name = "cg_cl_name_old" class="form-control" value= "<?=$row['cg_cl_name_old']?>" placeholder="กรอกข้อมูลชื่อเดิม" >
		<label>ชื่อใหม่</label>
		<input type = "text" name = "cg_cl_name_new" class="form-control" value= "<?=$row['cg_cl_name_new']?>" placeholder="กรอกข้อมูลชื่อใหม่" >
		<label>นามสกุลเดิม</label>
		<input type = "text" name = "cg_cl_surname_old" class="form-control" value= "<?=$row['cg_cl_surname_old']?>" placeholder="กรอกข้อมูลนามสกุลเดิม"  >
		<label>นามสกุลใหม่</label>
		<input type = "text" name = "cg_cl_surname_new" class="form-control" value= "<?=$row['cg_cl_surname_new']?>" placeholder="กรอกข้อมูลนามสกุลใหม่" ><br>
		<p>
			<input type="submit" value="Update" class="btn btn-success" >
			<td> <a href="Show_All.php?pcid=<?= $pcid ?>&page=change" class="btn btn-warning" >ยกเลิก</a> </td>
 	</p>
	</form>
	</div>
	</div>
	</div>
</body>
</html>