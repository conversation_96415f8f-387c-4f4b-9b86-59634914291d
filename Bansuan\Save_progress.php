<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../Alert.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);


/*echo '<pre>';
print_r($_POST);
echo '</pre>';

echo '<hr';

print_r($_FILES);
exit();*/

$pg_cl_aid = $_POST[ 'pg_cl_aid' ];
$pg_cl_idcard = $_POST[ 'pg_cl_idcard' ];
$pg_cl_date = $_POST[ 'pg_cl_date' ];
$pg_cl_progress = $_POST[ 'pg_cl_progress' ];
$pg_cl_action = $_POST[ 'pg_cl_action' ];
$pg_cl_investigator = $_POST[ 'pg_cl_investigator' ];
$pg_cl_remark = $_POST[ 'pg_cl_remark' ];

// save file สส.1
$file1 = $_FILES[ 'pg_cl_file' ][ 'tmp_name' ];
if ( is_uploaded_file( $file1 ) ) {
  $pg_cl_file = "uploaded/Doc/" . $_FILES[ 'pg_cl_file' ][ 'name' ];	
  move_uploaded_file( $file1, "../" . $pg_cl_file );  
} else {
  $pg_cl_file = '';
}

$sql = "SELECT * FROM wm_tb_wanted_progress WHERE pg_cl_aid = :pg_cl_aid ";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':pg_cl_aid', $pg_cl_aid);
try{
    $stmt->execute();
}catch (PDOException $e) {
    echo 'Query $sql Failed: '. $e->getMessage();
}
$row = $stmt->fetch(PDO::FETCH_ASSOC);

if($row){
    $sql = "UPDATE wm_tb_wanted_progress SET " .
		"pg_cl_idcard = :pg_cl_idcard, " .
		"pg_cl_date = :pg_cl_date, ".
		"pg_cl_progress = :pg_cl_progress, " .
		"pg_cl_action = :pg_cl_action, " .
		"pg_cl_investigator = :pg_cl_investigator, " .
		"pg_cl_remark = :pg_cl_remark "  ;
        $params = [
            'pg_cl_idcard' => $pg_cl_idcard,
            'pg_cl_date' => $pg_cl_date,
            'pg_cl_progress' => $pg_cl_progress,
            'pg_cl_action' => $pg_cl_action,
            'pg_cl_investigator' => $pg_cl_investigator,
            'pg_cl_remark' => $pg_cl_remark
            ];
    
            // ไฟล์รายงานสืบสวน
            if ($pg_cl_file !== '') {
            $sql .= ", pg_cl_file = :pg_cl_file";
            $params['pg_cl_file'] = $pg_cl_file;
            }

            $sql .= " WHERE pg_cl_aid = :updated_pg_cl_aid";
            $params['updated_pg_cl_aid'] = $pg_cl_aid;
    
}else{
    $sql = "INSERT INTO wm_tb_wanted_progress (pg_cl_idcard, pg_cl_date, pg_cl_progress, pg_cl_action, pg_cl_investigator, pg_cl_remark, pg_cl_file) 
    VALUES(:pg_cl_idcard, :pg_cl_date, :pg_cl_progress, :pg_cl_action, :pg_cl_investigator, :pg_cl_remark, :pg_cl_file) ";
            $params = [
            'pg_cl_idcard' => $pg_cl_idcard,
            'pg_cl_date' => $pg_cl_date,
            'pg_cl_progress' => $pg_cl_progress,
            'pg_cl_action' => $pg_cl_action,
            'pg_cl_investigator' => $pg_cl_investigator,
            'pg_cl_remark' => $pg_cl_remark,
            'pg_cl_file' => $pg_cl_file
            ];
}

$stmt = $pdo->prepare($sql);

try{
    $result = $stmt->execute($params);
}catch(PDOException $e){
    echo 'Query $result Failed: '. $e->getMessage();
}

$ip_address = $_SERVER['REMOTE_ADDR'];
$action = "Save progress: $pg_cl_idcard"; // You can set the action to a suitable value
$data = str_replace("'", "", compact_array($_POST));
log_activity($acc, $action, $data, $ip_address); // ใช้ function ในไฟล์ Condb.php

if ($result) {
    $_SESSION['success'] = "Data has been inserted succesfully";
    showSweetAlert("Saved successfully", "บันทึกข้อมูลเรียบร้อย");
    unset($_SESSION['success']); // Clear the session variable
    header("refresh:2; url=/Bansuan/progress.php?pcid={$pg_cl_idcard}");
} else {
    $_SESSION['error'] = "Error";
    showSweetAlert('Error', "บันทึกข้อมูล ไม่สำเร็จ", 'error');
    unset($_SESSION['error']); // Clear the session variable
    header("refresh:2; url=/Bansuan/progress.php?pcid={$pg_cl_idcard}");
}

$pdo = null;

?>
