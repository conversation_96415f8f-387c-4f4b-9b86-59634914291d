<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';
include '../Head_image.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

 // ดึงข้อมูล
$sql_sum = "SELECT T1.*, T2.sco_cl_data AS data, T3.cs_cl_status AS status FROM `wm_tb_Complaints` AS T1 " .
              "LEFT JOIN wm_tb_source_complaints AS T2 ON T1.cp_cl_source = T2.sco_cl_aid " .
              "LEFT JOIN wm_tb_Complaints_status AS T3 ON T1.cp_cl_status = T3.cs_cl_aid " .
              "WHERE (`cp_cl_source`='1' OR `cp_cl_source`='2' OR `cp_cl_source`='3' OR `cp_cl_source`='4' OR `cp_cl_source`='5' OR `cp_cl_source`='6' OR `cp_cl_source`='7' OR `cp_cl_source`='12' OR `cp_cl_source`='13' ) AND T1.station='$station' ";

	$stmt = $pdo->prepare($sql_sum);
try{
	$stmt->execute();
}catch (PDOException $e) {
	echo '$sql_sum Failed: '. $e->getMessage();
}

    $total = $stmt->rowCount();

//echo '<pre>';
//print_r($res_sum);
//echo '</pre>';

?>

<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js"></script>
<div class="container-fluid" align="left">
<div>
    <?php
    include('../users_info.php');
    ?>	
</div>
    
<div class=" h3 text-center  alert alert-success mb-4 mt-4 " role="alert" >บัญชีเรื่องที่รับแจ้งเหตุ <?= $name_station ?> </div>
<div align="left">
	&nbsp;&nbsp; <a href="/Bansuan/index.php?rnd=<?= rand(); ?>&page=complaints" class="btn btn-warning btn-lg mb-4" >ย้อนกลับ</a> 
    &nbsp;&nbsp;<a style="background-color: yellow ; padding: 20px">รับแจ้งเหตุ : <b style="color: crimson; font-size: 26px"><?= $total ?></b> เรื่อง</a>
<div class="table-responsive">
<table width="100%" height="94" border="1" cellpadding="1" cellspacing="1" class="table-bordered table-hover mb-4 mt-4">
  <tbody>
    <tr>
	  <td height="44" bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">ลำดับ</td>		
	  <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">รหัสรับแจ้งเหตุ</td>   
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">เหตุ</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">ที่มา</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">วันเวลา รับแจ้งเหตุ</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">วันที่เกิดเหตุ</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">เวลา</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">สถานที่เกิดเหตุ</td>      
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">ร้อยเวรสืบสวน</td>
	  <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">สถานะการดำเนินการ</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">ไฟล์ สส.1</td>
      <td colspan="3" bgcolor="#995D5D"></td>
      <td colspan="8" bgcolor="#995D5D"></td>
    </tr>
	  
<?php

// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no = 1;
while($row_sum = $stmt->fetch(PDO::FETCH_ASSOC))	
      //เพิ่มเข้ามาใหม่ 12/09/2022 จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
{
    //ฟังก์ชั่น วันที่ ดึงจาก condb
    $strDate = DateThai( $row_sum["cp_cl_date_complaints"] );
    $strDate2 = DateThai( $row_sum["cp_cl_date_incident"] );
    
     $pcid = $row_sum['cp_cl_case_id'];  
    //ตรวจสอบไฟล์ สส.1 ก่อนว่า มีไฟล์ในตารางไหม หากมี ให้ส่งเป็นลิงค์
	$pdf = $row_sum["cp_cl_complaints_file"];
	if($pdf !== '')
	{
		$links = '<a href="/' . $row_sum["cp_cl_complaints_file"] .'" target="_blank">Download</a>';
	}
	else {
		$links = 'ไม่มีไฟล์';
	}
    
    
?>	

    <tr>
      <td> <?= $no ?> </td>
      <td><?= $row_sum["cp_cl_case_id"]?></td>
      <td style="text-align: left">&nbsp;<?= $row_sum["cp_cl_case"]?></td>
      <td><?= $row_sum["data"]?></td>
      <td nowrap="nowrap"><?= $strDate ?> / <?= $row_sum["cp_cl_time_complaints"]?></td>
      <td nowrap="nowrap"><?= $strDate2 ?></td>
      <td><?= $row_sum["cp_cl_time_start_incident"]?> - <?= $row_sum["cp_cl_time_end_incident"]?></td>
   	  <td><?= $row_sum["cp_cl_place"]?></td>
	<!--  <td><?= $row_sum["cp_cl_sufferer_quantity"]?></td>
	  <td>1. <?= $row_sum["cp_cl_sufferer_name1"]?> อายุ <?= $row_sum["cp_cl_age1"]?> ปี <?= $row_sum["cp_cl_address1"]?> โทร <?= $row_sum["cp_cl_phone1"]?> /2. <?= $row_sum["cp_cl_sufferer_name2"]?> อายุ <?= $row_sum["cp_cl_age2"]?> ปี <?= $row_sum["cp_cl_address2"]?> โทร <?= $row_sum["cp_cl_phone2"]?> /3. <?= $row_sum["cp_cl_sufferer_name3"]?> อายุ <?= $row_sum["cp_cl_age3"]?> ปี <?= $row_sum["cp_cl_address3"]?> โทร <?= $row_sum["cp_cl_phone3"]?></td>
	  <td>1. <?= $row_sum["cp_cl_suspect1"]?> /2. <?= $row_sum["cp_cl_suspect2"]?> /3. <?= $row_sum["cp_cl_suspect3"]?></td>
	  <td><?= $row_sum["cp_cl_vehicle"]?></td>
	  <td><?= $row_sum["cp_cl_property"]?></td>
	  <td><?= $row_sum["cp_cl_case_behavior"]?></td> -->
	  <td><?= $row_sum["cp_cl_investigative_sergeant"]?></td>
	  <td><?= $row_sum["status"]?></td>
      <td><?= $links ?></td>

        <td><a href="show_detail_complaints.php?id=<?= $row_sum["cp_cl_aid"] ?>&pcid=<?= $pcid ?>" class="btn btn-success mb-4" >รายละเอียด</a></td>
        <td><a href="Edit_complaints.php?id=<?= $row_sum["cp_cl_aid"] ?>" class="btn btn-warning mb-4 <?= $edit_btn ?>" >แก้ไข</a></td>
        <td><a href="Del_complaints.php?id=<?= $row_sum["cp_cl_aid"] ?>" class="btn btn-danger mb-4 <?= $del_btn ?>" onClick="Del('Del_complaints.php?id=<?= $row_sum["cp_cl_aid"]?>')" >ลบ</a></td>
        
        
	  </tr>
  </tbody>
<?php
	$no++;
}//while
?>
</table>
        </div>

<script>
function Del(url)
{
	if(confirm("คุณจะลบข้อมูล หรือ ไม่ ?"))
	{
		window.location = url;		
	}
}
</script>
