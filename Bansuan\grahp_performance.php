<?php
// Step 1: Establish a database connection
include '../Condb.php'; //PDO
include '../users.inc.php';
include '../right_user.php';
include '../Head_image.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

//get month from select or current month
$month = isset($_GET['month']) ? $_GET['month'] : date("m");
$year = isset($_GET['year']) ? $_GET['year'] : date("Y");

$m1 = '';
$m2 = '';
$m3 = '';
$m4 = '';
$m5 = '';
$m6 = '';
$m7 = '';
$m8 = '';
$m9 = '';
$m10 = '';
$m11 = '';
$m12 = '';
switch($month)
{
    case "01": $m1 = "selected"; break;
    case "02": $m2 = "selected"; break;
    case "03": $m3 = "selected"; break;
    case "04": $m4 = "selected"; break;
    case "05": $m5 = "selected"; break;
    case "06": $m6 = "selected"; break;
    case "07": $m7 = "selected"; break;
    case "08": $m8= "selected"; break;
    case "09": $m9 = "selected"; break;
    case "10": $m10 = "selected"; break;
    case "11": $m11 = "selected"; break;
    case "12": $m12 = "selected"; break;
}
$current_month = $global_thai_month[0+$month];
$y2 = $year+543;

?>
<html>
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
    
  </head>
  <body>
      <p></p>
    <div class="container-fluid" align="left">
        <div mb-4 mt-4><a href="/Bansuan/index.php?rnd=<?= rand(); ?>&page=report" class="btn btn-warning" >ย้อนกลับ</a></div>

<?php
// Step 2: Fetch data for goals and performance for each month
$query = "SELECT MONTH(pf_cl_dates) as month, 
                SUM(pf_cl_drug_sell) as drug_sell,
                SUM(pf_cl_drug_occupy) as drug_occupy,
                SUM(pf_cl_take_drugs) as take_drugs,
                SUM(pf_cl_drug_therapy) as drug_therapy,
                SUM(pf_cl_gun) as gun,
                SUM(pf_cl_gambling_case) as gambling,
                SUM(pf_cl_immigrant_case) as immigrant,
                SUM(pf_cl_techno_case) as techno,
                SUM(pf_cl_wanted_qty) as wanted
            FROM wm_tb_performance
			WHERE station='$station'
            GROUP BY MONTH(pf_cl_dates)";
    $stmt = $pdo->prepare($query);
try{
    $stmt->execute();
}catch (PDOException $e) {
    echo '$query Failed: '. $e->getMessage();
}

//echo $query;  WHERE MONTH(pf_cl_dates)='$month' ส่วนนี้ ยังไม่สำเร็จ
// Step 3: Loop through the result set and store the values in two separate arrays
$drug_sell_array = array();
$drug_occupy_array = array();
$take_drugs_array = array();
$drug_therapy_array = array();
$gun_array = array();
$gambling_array = array();
$immigrant_array = array();
$techno_array = array();
$wanted_array = array();
        
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $drug_sell_array[] = $row['drug_sell'];
    $drug_occupy_array[] = $row['drug_occupy'];
    $take_drugs_array[] = $row['take_drugs'];
    $drug_therapy_array[] = $row['drug_therapy'];
    $gun_array[] = $row['gun'];
    $gambling_array[] = $row['gambling'];
    $immigrant_array[] = $row['immigrant'];
    $techno_array[] = $row['techno'];
    $wanted_array[] = $row['wanted'];
}

// Step 4: Use a charting library to create a bar graph
?>
        <!-- แสดงกราฟ -->
        <div id="chart_div" style="width: 100%; height: 500px;"></div>
        <div id="chart_div2" style="width: 100%; height: 500px;"></div>
    </div>
  </body>
</html>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
    <script type="text/javascript">
      google.charts.load('current', {'packages':['corechart']});
      google.charts.setOnLoadCallback(drawChart);

      function drawChart() {
        var data = google.visualization.arrayToDataTable([
          ['Month', 'จำหน่าย', 'ครอบครอง', 'เสพ(ดำเนินคดี)', 'ส่งคัดกรอง' ],
          ['ม.ค.', <?php echo $drug_sell_array[0]; ?>, <?php echo $drug_occupy_array[0]; ?>, <?php echo $take_drugs_array[0]; ?>, <?php echo $drug_therapy_array[0]; ?>],
          ['ก.พ.', <?php echo $drug_sell_array[1]; ?>, <?php echo $drug_occupy_array[1]; ?>, <?php echo $take_drugs_array[1]; ?>, <?php echo $drug_therapy_array[1]; ?>],
          ['มี.ค.', <?php echo $drug_sell_array[2]; ?>, <?php echo $drug_occupy_array[2]; ?>, <?php echo $take_drugs_array[2]; ?>, <?php echo $drug_therapy_array[2]; ?>],
          ['เม.ย.', <?php echo $drug_sell_array[3]; ?>, <?php echo $drug_occupy_array[3]; ?>, <?php echo $take_drugs_array[3]; ?>, <?php echo $drug_therapy_array[3]; ?>],
          ['พ.ค.', <?php echo $drug_sell_array[4]; ?>, <?php echo $drug_occupy_array[4]; ?>, <?php echo $take_drugs_array[4]; ?>, <?php echo $drug_therapy_array[4]; ?>],
		  ['มิ.ย.', <?php echo $drug_sell_array[5]; ?>, <?php echo $drug_occupy_array[5]; ?>, <?php echo $take_drugs_array[5]; ?>, <?php echo $drug_therapy_array[5]; ?>],
		  ['ก.ค.', <?php echo $drug_sell_array[6]; ?>, <?php echo $drug_occupy_array[6]; ?>, <?php echo $take_drugs_array[6]; ?>, <?php echo $drug_therapy_array[6]; ?>],
		  ['ส.ค.', <?php echo $drug_sell_array[7]; ?>, <?php echo $drug_occupy_array[7]; ?>, <?php echo $take_drugs_array[7]; ?>, <?php echo $drug_therapy_array[7]; ?>],
          // Add more months as needed
        ]);
          
        var data2 = google.visualization.arrayToDataTable([
          ['Month', 'ปืน', 'การพนัน', 'คนเข้าเมือง', 'เทคโน', 'หมายจับ' ],
          ['ม.ค.', <?php echo $gun_array[0]; ?>, <?php echo $gambling_array[0]; ?>, <?php echo $immigrant_array[0]; ?>, <?php echo $techno_array[0]; ?>, <?php echo $wanted_array[0]; ?>],
          ['ก.พ.', <?php echo $gun_array[1]; ?>, <?php echo $gambling_array[1]; ?>, <?php echo $immigrant_array[1]; ?>, <?php echo $techno_array[1]; ?>, <?php echo $wanted_array[1]; ?>],
          ['มี.ค.', <?php echo $gun_array[2]; ?>, <?php echo $gambling_array[2]; ?>, <?php echo $immigrant_array[2]; ?>, <?php echo $techno_array[2]; ?>, <?php echo $wanted_array[2]; ?>],
          ['เม.ย.', <?php echo $gun_array[3]; ?>, <?php echo $gambling_array[3]; ?>, <?php echo $immigrant_array[3]; ?>, <?php echo $techno_array[3]; ?>, <?php echo $wanted_array[3]; ?>],
          ['พ.ค.', <?php echo $gun_array[4]; ?>, <?php echo $gambling_array[4]; ?>, <?php echo $immigrant_array[4]; ?>, <?php echo $techno_array[4]; ?>, <?php echo $wanted_array[4]; ?>],
		  ['มิ.ย.', <?php echo $gun_array[5]; ?>, <?php echo $gambling_array[5]; ?>, <?php echo $immigrant_array[5]; ?>, <?php echo $techno_array[5]; ?>, <?php echo $wanted_array[5]; ?>],
		  ['ก.ค.', <?php echo $gun_array[6]; ?>, <?php echo $gambling_array[6]; ?>, <?php echo $immigrant_array[6]; ?>, <?php echo $techno_array[6]; ?>, <?php echo $wanted_array[6]; ?>],
		  ['ส.ค.', <?php echo $gun_array[7]; ?>, <?php echo $gambling_array[7]; ?>, <?php echo $immigrant_array[7]; ?>, <?php echo $techno_array[7]; ?>, <?php echo $wanted_array[7]; ?>],
          // Add more months as needed
        ]);
         
        var options = {
          title: 'แผนภูมิแสดงข้อมูล ผลการปฏิบัติกวาดล้าง ประจำเดือน <?= $name_station ?>',
          chartArea: {width: '50%'},
          hAxis: {
            title: '',
            minValue: 0
          },
          vAxis: {
            title: 'จำนวนผลการปฏิบัติ'
          }
        };

        /*var chart = new google.visualization.BarChart(document.getElementById('chart_div'));
        chart.draw(data, options);
        var chart2 = new google.visualization.BarChart(document.getElementById('chart_div2'));
        chart2.draw(data2, options);*/
        var chart = new google.visualization.ColumnChart(document.getElementById('chart_div'));
        chart.draw(data, options);
        var chart2 = new google.visualization.ColumnChart(document.getElementById('chart_div2'));
        chart2.draw(data2, options);
      }
    </script>

<script>
function dates_change()  // คำสั่งเปลี่ยนเดือน ปี
{
    var ms = $("#pf_cl_month option:selected").val();
    var ys = $("#pf_cl_year option:selected").val();
    window.location = "/Bansuan/grahp_performance.php&month=" + ms + "&year=" + ys + "&rnd=" + Math.random();
}
</script>