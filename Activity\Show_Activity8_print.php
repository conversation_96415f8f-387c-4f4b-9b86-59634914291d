<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);


$id = isset($_GET['hv_cl_aid']) ? $_GET['hv_cl_aid'] : '';

//get month from select or current month
$month = isset($_GET['month']) ? $_GET['month'] : date("m");
$year = isset($_GET['year']) ? $_GET['year'] : date("Y");

$m1 = '';
$m2 = '';
$m3 = '';
$m4 = '';
$m5 = '';
$m6 = '';
$m7 = '';
$m8 = '';
$m9 = '';
$m10 = '';
$m11 = '';
$m12 = '';
switch($month)
{
    case "01": $m1 = "selected"; break;
    case "02": $m2 = "selected"; break;
    case "03": $m3 = "selected"; break;
    case "04": $m4 = "selected"; break;
    case "05": $m5 = "selected"; break;
    case "06": $m6 = "selected"; break;
    case "07": $m7 = "selected"; break;
    case "08": $m8 = "selected"; break;
    case "09": $m9 = "selected"; break;
    case "10": $m10 = "selected"; break;
    case "11": $m11 = "selected"; break;
    case "12": $m12 = "selected"; break;
}
$current_month = $global_thai_month[0+$month];
$y2 = $year+543;

$currentDate = date('d F Y');

// สำหรับ พิมพ์หน้า ข้อมูฃ ประกาศ บรรทัด ที่ต้องการพิมพ์
//-----------------
$MAX_ROW = 18;
//-----------------

// สร้างฟังก์ชั่น เพื่อดึง หัวตาราง ไปแสดง ในหน้าต่อไป
function print_header()
{
    echo '
<div class="container body_text" align="left" style="margin-left:auto">
<table width="99%" border="1" cellspacing="1" cellpadding="1" class="table-striped table-hover table-bordered">
  <tbody>
    <tr>
      <td rowspan="3" style="text-align: center">ลำดับ</td>
      <td rowspan="3" style="text-align: center">สถานี</td>
      <td rowspan="3" style="text-align: center">จำนวนครั้ง<br>(รวมทั้งหมด)</td>
      <td height="46" colspan="5" style="text-align: center">เยี่ยมพบ</td>
      <td colspan="9" style="text-align: center">เยี่ยมไม่พบ</td>
      <td rowspan="3" style="text-align: center">มีผลการปฏิบัติ</td>
      <td rowspan="3" style="text-align: center">ไม่มีผลการปฏิบัติ</td>
    </tr>
    <tr>
      <td rowspan="2" style="text-align: center">จำนวน (คน)</td>
      <td rowspan="2" style="text-align: center">ผลใน Police4.0</td>
      <td rowspan="2" style="text-align: center">ร้อยละความสำเร็จ</td>
      <td rowspan="2"style="text-align: center">เก็บ DNA</td>
      <td rowspan="2"style="text-align: center">ยังไม่เก็บ DNA</td>
      <td colspan="3">ทราบที่อยู่</td>
      <td colspan="3">ไม่ทราบที่อยู่</td>
      <td colspan="3">กระทำผิดอีก/อยู่เรือนจำ</td>
      </tr>
    <tr>
	  <td>จำนวน</td>
	  <td>แจ้งท้องที่ใหม่</td>
	  <td>ยังไม่แจ้ง</td>
	  <td>จำนวน</td>
	  <td>แจ้ง รจ.และทว.</td>
	  <td>ยังไม่แจ้ง</td>
	  <td>จำนวน</td>
	  <td>แจ้ง ทว.</td>
	  <td>ยังไม่แจ้ง</td>
      </tr>
    ';
}

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<base target="_top">
<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js"></script>
<!-- สไตล์ สำหรับ สั่งพิมพ์ข้อมูล -->
<style>
@media only print
{
   
}
    
@media print {
 /* Set the page size to A4 */
  @page {
    size: A4;
  }
body{ padding:  0; margin:  0; }
  .printId{visibility: visible;}
    
  .footer { page-break-after: always;}
     /* Avoid breaking content across pages */
  .content {
    page-break-inside: avoid;
  }
}
.body_text{
    font-size: 16px;
    color: black;
}
td{
    color: black;
}
tr{
    color: black;
}
</style>
</head>
<body>
    <!-- นำสไตล์ สำหรับ สั่งพิมพ์ข้อมูล มาใช้งาน ภายใต้ <dvi></div>-->
<div class="printId">
<div class="container-fluid" align="left">
<div class=" h3 text-center body_text mb-4 mt-4 " >กิจกรรม 8 การตรวจเยี่ยมบุคคลพ้นโทษ/พักโทษ</div>

<h3 align="center" class="body_text">ข้อมูลการติดตามพฤติการณ์และความเคลื่อนไหวของบุคคลพ้นโทษ พักโทษ</h3>
<h3 align="center" class="body_text">ประจำเดือน&nbsp; <?= $current_month ?> &nbsp;<?= $y2 ?> &nbsp;</h3>
    
<div class="container body_text; content" align="left" style="margin-left:auto">
<table width="99%" border="1" cellspacing="1" cellpadding="1" class="table-striped table-hover table-bordered">
  <tbody>
    <tr>
      <td rowspan="3" style="text-align: center">ลำดับ</td>
      <td rowspan="3" style="text-align: center">สถานี</td>
      <td rowspan="3" style="text-align: center">จำนวนครั้ง<br>(รวมทั้งหมด)</td>
      <td height="46" colspan="5" style="text-align: center">เยี่ยมพบ</td>
      <td colspan="9" style="text-align: center">เยี่ยมไม่พบ</td>
      <td rowspan="3" style="text-align: center">มีผลการปฏิบัติ</td>
      <td rowspan="3" style="text-align: center">ไม่มีผลการปฏิบัติ</td>
    </tr>
    <tr>
      <td rowspan="2" style="text-align: center">จำนวน (คน)</td>
      <td rowspan="2" style="text-align: center">ผลใน Police4.0</td>
      <td rowspan="2" style="text-align: center">ร้อยละความสำเร็จ</td>
      <td rowspan="2"style="text-align: center">เก็บ DNA</td>
      <td rowspan="2"style="text-align: center">ยังไม่เก็บ DNA</td>
      <td colspan="3">ทราบที่อยู่</td>
      <td colspan="3">ไม่ทราบที่อยู่</td>
      <td colspan="3">กระทำผิดอีก/อยู่เรือนจำ</td>
      </tr>
    <tr>
	  <td>จำนวน</td>
	  <td>แจ้งท้องที่ใหม่</td>
	  <td>ยังไม่แจ้ง</td>
	  <td>จำนวน</td>
	  <td>แจ้ง รจ.และทว.</td>
	  <td>ยังไม่แจ้ง</td>
	  <td>จำนวน</td>
	  <td>แจ้ง ทว.</td>
	  <td>ยังไม่แจ้ง</td>
      </tr>
	  
<?php     
                      
$sql = "SELECT " .
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND station=:station) AS T1_1, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND station=:station) AS T1, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND station=:station) AS T2, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND station=:station) AS T3, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND station=:station) AS T4, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND station=:station) AS T5, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND station=:station) AS T6, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND station=:station) AS T7, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND station=:station) AS T8, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND station=:station) AS T9, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND station=:station) AS T10, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND station=:station) AS T11, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND station=:station) AS T12, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND station=:station) AS T13 " .
        " ";

$stmt = $pdo->prepare($sql);
$stmt->bindParam(':month', $month);
$stmt->bindParam(':year', $year);
$stmt->bindParam(':station', $station); // Add station parameter
try {
    $stmt->execute();
} catch (PDOException $e) {
    echo '$sql Failed: '. $e->getMessage();
}
    
// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no = 1;
while($row = $stmt->fetch(PDO::FETCH_ASSOC))	//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
{
//ฟังก์ชั่น วันที่ ดึงจาก condb
//$strDate = DateThai( $row_mi["date_record"] );
    
if($row["T1"] > 0) {   
    $persen = ($row["T2"]*100)/$row["T1"];
}
else {
    $persen=0;
    }
    $sum1 = $row["T1"] + $row["T5"] + $row["T8"] + $row["T11"];
    $sum2 = $row["T1_1"] - $sum1;

?>	

    <tr>
	  <td height="52" style="font-size: 24px"><?= $no ?> </td> 
      <td nowrap>&nbsp;<?= $name_station ?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo $row["T1_1"] ?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo $row["T1"]?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo $row["T2"]?>&nbsp;</td>
      <td style="font-size: 24px"><?= $persen ?>&nbsp;%</td>
      <td style="font-size: 24px">&nbsp;<?php echo $row["T3"]?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo $row["T4"]?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T5"] > 0) ? $row["T5"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T6"] > 0) ? $row["T6"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T7"] > 0) ? $row["T7"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T8"] > 0) ? $row["T8"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T9"] > 0) ? $row["T9"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T10"] > 0) ? $row["T10"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T11"] > 0) ? $row["T11"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T12"] > 0) ? $row["T12"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T13"] > 0) ? $row["T13"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?= $sum1 ?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?= $sum2 ?>&nbsp;</td>
      <!--<td><a href="#Edit_mission.php?id=<?= $row[""] ?>" class="btn btn-warning mb-4" >แก้ไข</a> </td>
	  <td><a href="#Del_mission.php?id=<?= $row[""] ?>" class="btn btn-danger mb-4" onClick="Del('#Del_mission.php?id=<?= $row[""]?>')">ลบ</a>-->
    </tr>
  </tbody>
<?php
        
	$no++;
    // กำหนด คุณสมบัติของหน้าที่จะส่งไปพิมพ์
         // if(($no % ($row_per_page + 1)) == 0) {
         if(($no % ($MAX_ROW)) == 0) {
            echo "</tbody></table></div>";
            
              echo "<div class='footer'>&nbsp;</div> "; // สิ้นสุดการพิม 1 หน้า
            
              print_header();
        }
}//while
?>
</table>
</div>
    </div>
    </div>
<br>
    <div align="center">
       <?php
       if ($station == 6707) {
		   echo '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ตรวจแล้วรับรองว่าถูกต้อง<br>
        <br>
        	&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;พ.ต.ท.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br>
		   &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br>
			';
	   } else {
		   echo '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ตรวจแล้วรับรองว่าถูกต้อง<br>
        <br>
        	&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;พ.ต.ท.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br><br>
		   &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br>
			';
	   }
		
	?>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;หน.งานสืบสวน <?= $name_station ?> <br><br>
        
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<?php echo "พิมพ์เอกสารเมื่อ " . DateThaiFull($currentDate) . "<br>"; ?>&nbsp;&nbsp;<br>
    </div>
</body>
</html>
    