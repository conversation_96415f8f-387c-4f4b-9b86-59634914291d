<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';
include '../Head_image.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

$sql2 = "SELECT COUNT(*) AS Total, 
               COUNT(CASE WHEN on_cl_status_case = 'สิ้นสุด' THEN 1 END) AS Total2 
        FROM wm_tb_online_case 
        WHERE station = '$station'";
    $stmt2 = $pdo->prepare($sql2);
try{
    $stmt2->execute();
}catch (PDOException $e) {
    echo '$sql Failed: '. $e->getMessage();
}

    $row2 = $stmt2->fetch(PDO::FETCH_ASSOC);
    $total = $row2['Total'];
    $total3 = $row2['Total2'];

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
<link rel="stylesheet" href="/bootstrap/css/bootstrap-grid.css">
<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js"></script>

<title>ระบบ WatchmanDB</title>
</head>
    
<body>
<div class="container-fluid">
<div>
    <?php
    include('../users_info.php');
    ?>	
</div>
    
		<div class=" h3 text-center  alert alert-secondary mb-4 mt-4 " role="alert" >ข้อมูลคดีรับแจ้งความออนไลน์ <?= $name_station ?> ที่สิ้นสุดแล้ว</div>&nbsp;
		<div align="left">
		&nbsp;<a href="/Bansuan/index.php?rnd=<?= rand(); ?>&page=online" class="btn btn-primary btn-lg mb-4" >กลับ</a>&nbsp;<a style="background-color: yellow ;padding: 10px">จำนวนคดีรับแจ้งความออนไลน์ <?= $name_station ?> ณ ปัจจุบัน มีทั้งหมดจำนวน : <span style="color: #FC0408"><b style="color: crimson; font-size: 24px">
            <?= $total ?> </b> คดี </a> <a style="background-color: yellow ;padding: 10px">สิ้นสุดแล้ว จำนวน : <span style="color: #FC0408"><b style="color: crimson; font-size: 24px">
            <?= $total3 ?> </b> คดี </a>
            <a style="background-color: yellow ;padding: 10px">คงเหลือ จำนวน : <span style="color: #FC0408"><b style="color: crimson; font-size: 24px">
            <?= $total - $total3 ?> </b> คดี </a>
		</div>
    </div>

<table class="table table-striped table-hover table-bordered table-sm mb-4 mt-4 " >
      <tbody>
            <tr class="container-fluid ">
              <th nowrap="nowrap">ลำดับ</th>
              <th>เลขรับแจ้ง</th>
              <th>ผู้เสียหาย</th>
              <th>รายละเอียดโดยย่อ</th>
              <th>ค่าเสียหาย</th>
           <!--   <th>หน่วยรับผิดชอบ</th>   -->
              <th>พนักงานสอบสวน</th>
              <th>วันรับแจ้ง</th> 
              <th>สถานะ</th>
              <th>ผู้สืบสวน</th>
              <th>รายงานสืบสวน</th>
              <th></th>
              <th></th>
            </tr>


<?php
$sql = "SELECT
            T1.*,
            T2.station_name as station,
            T_BSDP.`bsdp_name` AS BSDP,
            T_BSINQ.`bs_inq_name` AS INQ_name " .		//เงื่อนไข การเลือกข้อมูล จาก 2 ตาราง
        "FROM
            wm_tb_online_case AS T1 " .
            "LEFT JOIN `police_name_bsdetective` AS T_BSDP ON T1.`on_cl_detective` = T_BSDP.`aid_bsdp` " .  // ชุดสืบสวน
            "LEFT JOIN `police_name_bs_inqinquiry` AS T_BSINQ ON T1.`on_cl_inquiry_official` = T_BSINQ.`aid_bs_inq` " .  // พนักงานสอบสวน
            "LEFT JOIN wm_tb_police_station2 AS T2 ON T1.station = T2.station_code " .
        "WHERE
            T1.station='$station' AND on_cl_status_case='สิ้นสุด' " .			//เพิ่มเงื่อนไข เลือกสถานี และสถานะมีผลใช้
        "ORDER BY
            T1.on_cl_aid ASC";  //ตั้งตัวแปร sql เลือกฐานข้อมูลจากตารางที่ชื่อ wm_tb_personal

    $stmt = $pdo->prepare($sql);
try{
    $stmt->execute();
}catch (PDOException $e) {
    echo '$sql Failed: '. $e->getMessage();
}

// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no = 1;
while($row_on = $stmt->fetch(PDO::FETCH_ASSOC))	{//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
    $pcid = $row_on['on_cl_no']; //เลขบัตร ผู้พ้นโทษ

    //ฟังก์ชั่น วันที่ ดึงจาก condb
    $strDate = DateThai( $row_on['on_cl_date'] );

//ตรวจสอบรายงานสืบสวนก่อนว่า มีไฟล์ในตารางไหม หากมี ให้ส่งเป็นลิงค์
$doc = $row_on["on_cl_file"];
if($doc !== '')
{
    $link = '<a href="/' . $row_on["on_cl_file"] .'" target="_blank">Download</a>';
}
else {
    $link = ' ';
}
?>                                                                  

            <tr class="container-fluid">
              <td>&nbsp; <?= $no ?></td>    
            <!--  <td><?= $row_on['on_cl_aid'] ?></td>  -->
              <td><?= $row_on['on_cl_no'] ?></td>
              <td><?= $row_on['on_cl_name_sufferer'] ?></td>
              <td><?= $row_on['on_cl_detail'] ?></td> 
              <td><?= $row_on['on_cl_damages'] ?></td> 
          <!--    <td><?= $row_on['station'] ?></td>    -->
              <td><?= $row_on['INQ_name'] ?></td>
              <td nowrap><?= $strDate ?></td>
              <td><?= $row_on['on_cl_status_case'] ?></td>
              <td><?= $row_on['BSDP'] ?></td>
              <td><?= $link ?></td>	
                
              <td><a href="show_detail_online.php?id=<?= $row_on['on_cl_aid'] ?>&pcid=<?= $pcid ?>" class="btn btn-success mb-4" >รายละเอียด</a></td>
            </tr>
                <?php
                    $no++;
                }//while
                ?>
      </tbody>
</table>
</div>
</body>
</html>