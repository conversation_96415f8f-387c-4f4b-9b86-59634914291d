<!DOCTYPE html>
<html>
<head>
 <title>Dynamic Checklist Example</title>
</head>
<body>
 <form action="" method="post">
  <label for="dropdown">Select an item:</label>
  <select name="dropdown" id="dropdown" onchange="showChecklist()">
   <option value="">--Select--</option>
   <option value="item1">Item 1</option>
   <option value="item2">Item 2</option>
   <option value="item3">Item 3</option>
  </select>

  <!--<div id="checklist" style="display:none;">
   <label>Checklist for selected item:</label><br>
   <input type="checkbox" name="checklist[]" value="check1">Check 1<br>
   <input type="checkbox" name="checklist[]" value="check2">Check 2<br>
   <input type="checkbox" name="checklist[]" value="check3">Check 3<br>
  </div>-->
     <br>
 <label for="dropdown2" >Select an item 2:</label>
  <select name="dropdown2" id="dropdown2" style="display:none;">
   <option value="">--Select--</option>
   <option value="item1">Item 12</option>
   <option value="item2">Item 22</option>
   <option value="item3">Item 32</option>
  </select>
     
  <input type="submit" value="Submit">
 </form>

 <script>
  function showChecklist() {
   var dropdown = document.getElementById("dropdown");
   var dropdown2 = document.getElementById("dropdown2");

   if (dropdown.value != "") {
    dropdown2.style.display = "block";
   } else {
    dropdown2.style.display = "none";
   }
  }
 </script>
<!--    <label for="dropdown2">Select an item2:</label>
  <select name="dropdown2" id="dropdown2" onchange="showChecklist2()">
   <option value="">--Select--</option>
   <option value="item1">Item 11</option>
   <option value="item2">Item 22</option>
   <option value="item3">Item 33</option>
  </select>
    
    <div id="checklist2" style="display:none;">
   <label>Checklist for selected item:</label><br>
   <input type="checkbox2" name="checklist2[]" value="check11">Check 11<br>
   <input type="checkbox2" name="checklist2[]" value="check22">Check 22<br>
   <input type="checkbox2" name="checklist2[]" value="check33">Check 33<br>
  </div>
<script>-->
<!--//  function showChecklist2() {
//   var dropdown2 = document.getElementById("dropdown2");
//   var checklist2 = document.getElementById("checklist2");
//
//   if (dropdown2.value != "") {
//    checklist2.style.display = "block";
//   } else {
//    checklist2.style.display = "none";
//   }
//  }
// </script>-->
</body>
</html>