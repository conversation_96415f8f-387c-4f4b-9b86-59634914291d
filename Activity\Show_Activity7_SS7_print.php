<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

//get month from select or current month
$month = isset($_GET['month']) ? $_GET['month'] : date("m");
$year = isset($_GET['year']) ? $_GET['year'] : date("Y");
//$station = isset($_GET['station']) ? $_GET['station'] : 6707;
//$station = 6707;   // สภ.บ้านสวน

//--------------------
// last modify 2023-01-20
$limite_date = strtotime("$year-09-30"); // วันสุดท้ายของช่วง เป็น 30 ก.ย.ของปีนั้น ๆ
$current_date = time(); // now()   กำหนดวันปัจจุบัน
$check_date = strtotime("$year-10-01");   // ตัวแปร $check_date เป็นวันตั้งแต่ 1 ต.ค.ของปีนั้น ๆ เป็นต้นไป
$select_date = strtotime("$year-$month-01"); // เป็นตัวแปร สำหรับเลือก ของเดือนนั้น ๆ

//ถ้าเวลายังไม่สิ้นสุด 30 กย. ของปี
if($current_date < $check_date)   // ถ้าวันปัจจุบัน มีค่าน้อยกว่า วันที่ 1 ต.ค.
{
    $limite_date = strtotime(($year-1) . "-09-30");  // ตัวแปร $limite_date จะมีค่าเท่ากับ 30 ก.ย.ของปี ที่ลบค่าไป 1
    $check_date = strtotime(($year-1) . "-10-01");   // ตัวแปร $check_date จะมีค่าเท่ากับ 1 ต.ค. ของปี ที่ลบค่าไป 1
}


$m1 = '';
$m2 = '';
$m3 = '';
$m4 = '';
$m5 = '';
$m6 = '';
$m7 = '';
$m8 = '';
$m9 = '';
$m10 = '';
$m11 = '';
$m12 = '';
switch($month)
{
    case "01": $m1 = "selected"; break;
    case "02": $m2 = "selected"; break;
    case "03": $m3 = "selected"; break;
    case "04": $m4 = "selected"; break;
    case "05": $m5 = "selected"; break;
    case "06": $m6 = "selected"; break;
    case "07": $m7 = "selected"; break;
    case "08": $m8 = "selected"; break;
    case "09": $m9 = "selected"; break;
    case "10": $m10 = "selected"; break;
    case "11": $m11 = "selected"; break;
    case "12": $m12 = "selected"; break;
}
$current_month = $global_thai_month[0+$month];
$y2 = $year+543;

$currentDate = date('d F Y');

// แปลงเวลา เป็นปีไทย
function date_2_Thai( $date )
{
    global $global_thaimonth;
    $strYear = date("Y", $date) + 543;
    $strMonth = date("n",$date) - 1;
    $strDay = date("j", $date);
    $strMonthThai = $global_thaimonth[$strMonth];
    return "$strDay $strMonthThai $strYear";
}

//สร้างฟังก์ชั่น ย้อนหลัง arrest_summary_count มาใช้งาน เป็นยอดรวมการจับกุม ย้อนหลัง 1 เดือน
// dateM - datetime var
function arrest_summary_count($date)
{
    global $pdo, $station;

    $m1 = date("Y-m-1", $date);
    $m2 = date("Y-m-t", $date);

    $sql_s = "SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station` = :station AND `wr_cl_status` IN ('2', '5', '6', '7')";

    $stmt = $pdo->prepare($sql_s);
    $stmt->bindParam(':m1', $m1);
    $stmt->bindParam(':m2', $m2);
    $stmt->bindParam(':station', $station);
    $stmt->execute();

    $row_s = $stmt->fetch(PDO::FETCH_NUM);
    return $row_s[0];
}

// สำหรับ พิมพ์หน้า ข้อมูฃ ประกาศ บรรทัด ที่ต้องการพิมพ์
//-----------------
$MAX_ROW = 18;
//-----------------

// สร้างฟังก์ชั่น เพื่อดึง หัวตาราง ไปแสดง ในหน้าต่อไป
function print_header()
{
    echo '
<div class="container body_text" align="left" style="margin-left:auto">
<table width="99%" border="1" cellspacing="1" cellpadding="1" class="table-striped table-hover table-bordered">
  <tbody>
    <tr style="font-size: 16px">
      <td>&nbsp;ปี \ เดือน&nbsp;</td>
      <td colspan="2">&nbsp;ม.ค.&nbsp;</td>
      <td colspan="2">&nbsp;ก.พ.&nbsp;</td>
      <td colspan="2">&nbsp;มี.ค.&nbsp;</td>
      <td colspan="2">&nbsp;เม.ย.&nbsp;</td>
      <td colspan="2">&nbsp;พ.ค.&nbsp;</td>
      <td colspan="2">&nbsp;มิ.ย.&nbsp;</td>
      <td colspan="2">&nbsp;ก.ค.&nbsp;</td>
      <td colspan="2">&nbsp;ส.ค.&nbsp;</td>
      <td colspan="2">&nbsp;ก.ย.&nbsp;</td>
      <td colspan="2">&nbsp;ต.ค.&nbsp;</td>
      <td colspan="2">&nbsp;พ.ย.&nbsp;</td>
      <td colspan="2">&nbsp;ธ.ค.&nbsp;</td>
    </tr>
    <tr style="font-size: 16px">
      <td>&nbsp;&nbsp;</td>
      <td>&nbsp;จับ&nbsp;</td>
      <td>&nbsp;ขาดอายุ&nbsp;</td>
      <td>&nbsp;จับ&nbsp;</td>
      <td>&nbsp;ขาดอายุ&nbsp;</td>
      <td>&nbsp;จับ&nbsp;</td>
      <td>&nbsp;ขาดอายุ&nbsp;</td>
      <td>&nbsp;จับ&nbsp;</td>
      <td>&nbsp;ขาดอายุ&nbsp;</td>
      <td>&nbsp;จับ&nbsp;</td>
      <td>&nbsp;ขาดอายุ&nbsp;</td>
      <td>&nbsp;จับ&nbsp;</td>
      <td>&nbsp;ขาดอายุ&nbsp;</td>
      <td>&nbsp;จับ&nbsp;</td>
      <td>&nbsp;ขาดอายุ&nbsp;</td>
      <td>&nbsp;จับ&nbsp;</td>
      <td>&nbsp;ขาดอายุ&nbsp;</td>
      <td>&nbsp;จับ&nbsp;</td>
      <td>&nbsp;ขาดอายุ&nbsp;</td>
      <td>&nbsp;จับ&nbsp;</td>
      <td>&nbsp;ขาดอายุ&nbsp;</td>
      <td>&nbsp;จับ&nbsp;</td>
      <td>&nbsp;ขาดอายุ&nbsp;</td>
      <td>&nbsp;จับ&nbsp;</td>
      <td>&nbsp;ขาดอายุ&nbsp;</td>
    </tr>
    ';
}

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<base target="_top">
<title>แบบ สส.7</title>
<link rel="stylesheet" href="/bootstrap/css/bootstrap-grid.css">
<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<!-- สไตล์ สำหรับ สั่งพิมพ์ข้อมูล -->
<style>
@media only print
{
   
}
    
@media print {
body{ padding:  0; margin:  0; }
  .printId{visibility: visible;}
    
.footer { page-break-after: always;}
}
.body_text{
    font-size: 16px;
    color: black;
}
td{
    color: black;
}
tr{
    color: black;
}
</style>
</head>
    
<body>
    <!-- นำสไตล์ สำหรับ สั่งพิมพ์ข้อมูล มาใช้งาน ภายใต้ <dvi></div>-->
<div class="printId">
<div class=" h3 text-center body_text mb-4 mt-4 " >ผลการจับกุมตามหมายจับ ภาพรวม (รายปี) &nbsp;&nbsp;(แบบ สส.7) <br><br> เดือน <?= $current_month ?> &nbsp;<?= $y2 ?> &nbsp; <?= $name_station ?></div>


<tbody>
<?php
      
$sql = "SELECT  
            CONCAT(YEAR(`wr_cl_date_finish`)+543) AS `year`, 
            SUM(IF(MONTH(`wr_cl_date_finish`) = 1, 1, 0)) AS `m1`, 
            SUM(IF(MONTH(`wr_cl_date_finish`) = 2, 1, 0)) AS `m2`, 
            SUM(IF(MONTH(`wr_cl_date_finish`) = 3, 1, 0)) AS `m3`, 
            SUM(IF(MONTH(`wr_cl_date_finish`) = 4, 1, 0)) AS `m4`, 
            SUM(IF(MONTH(`wr_cl_date_finish`) = 5, 1, 0)) AS `m5`, 
            SUM(IF(MONTH(`wr_cl_date_finish`) = 6, 1, 0)) AS `m6`, 
            SUM(IF(MONTH(`wr_cl_date_finish`) = 7, 1, 0)) AS `m7`, 
            SUM(IF(MONTH(`wr_cl_date_finish`) = 8, 1, 0)) AS `m8`, 
            SUM(IF(MONTH(`wr_cl_date_finish`) = 9, 1, 0)) AS `m9`, 
            SUM(IF(MONTH(`wr_cl_date_finish`) = 10, 1, 0)) AS `m10`, 
            SUM(IF(MONTH(`wr_cl_date_finish`) = 11, 1, 0)) AS `m11`, 
            SUM(IF(MONTH(`wr_cl_date_finish`) = 12, 1, 0)) AS `m12`, 
            SUM(IF(MONTH(`wr_cl_date_finish`) = 1 AND wr_cl_status = 5, 1, 0)) AS `f1`, 
            SUM(IF(MONTH(`wr_cl_date_finish`) = 2 AND wr_cl_status = 5, 1, 0)) AS `f2`, 
            SUM(IF(MONTH(`wr_cl_date_finish`) = 3 AND wr_cl_status = 5, 1, 0)) AS `f3`, 
            SUM(IF(MONTH(`wr_cl_date_finish`) = 4 AND wr_cl_status = 5, 1, 0)) AS `f4`, 
            SUM(IF(MONTH(`wr_cl_date_finish`) = 5 AND wr_cl_status = 5, 1, 0)) AS `f5`, 
            SUM(IF(MONTH(`wr_cl_date_finish`) = 6 AND wr_cl_status = 5, 1, 0)) AS `f6`, 
            SUM(IF(MONTH(`wr_cl_date_finish`) = 7 AND wr_cl_status = 5, 1, 0)) AS `f7`, 
            SUM(IF(MONTH(`wr_cl_date_finish`) = 8 AND wr_cl_status = 5, 1, 0)) AS `f8`, 
            SUM(IF(MONTH(`wr_cl_date_finish`) = 9 AND wr_cl_status = 5, 1, 0)) AS `f9`,
            SUM(IF(MONTH(`wr_cl_date_finish`) = 10 AND wr_cl_status = 5, 1, 0)) AS `f10`,
            SUM(IF(MONTH(`wr_cl_date_finish`) = 11 AND wr_cl_status = 5, 1, 0)) AS `f11`,
            SUM(IF(MONTH(`wr_cl_date_finish`) = 12 AND wr_cl_status = 5, 1, 0)) AS `f12`
        FROM 
            wm_tb_warrant AS T1 
        WHERE 
            T1.`station`=:station AND YEAR(T1.wr_cl_date_finish)=:year
        GROUP BY 
            `year`";

    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':station', $station);
    $stmt->bindParam(':year', $year);
try{
    $stmt->execute();
}catch (PDOException $e) {
    echo '$sql Failed: '. $e->getMessage();
}
    $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
$no = 1; 
      //echo $sql;
?>

<?php foreach ($result as $row): ?>
    
<div class="container body_text" align="left" style="margin-left:auto">
<table width="99%" border="1" cellspacing="1" cellpadding="1" class="table-striped table-hover table-bordered">
  <tbody>
    <tr style="font-size: 16px">
      <td>&nbsp;ปี \ เดือน&nbsp;</td>
      <td colspan="2">&nbsp;ม.ค.&nbsp;</td>
      <td colspan="2">&nbsp;ก.พ.&nbsp;</td>
      <td colspan="2">&nbsp;มี.ค.&nbsp;</td>
      <td colspan="2">&nbsp;เม.ย.&nbsp;</td>
      <td colspan="2">&nbsp;พ.ค.&nbsp;</td>
      <td colspan="2">&nbsp;มิ.ย.&nbsp;</td>
      <td colspan="2">&nbsp;ก.ค.&nbsp;</td>
      <td colspan="2">&nbsp;ส.ค.&nbsp;</td>
      <td colspan="2">&nbsp;ก.ย.&nbsp;</td>
      <td colspan="2">&nbsp;ต.ค.&nbsp;</td>
      <td colspan="2">&nbsp;พ.ย.&nbsp;</td>
      <td colspan="2">&nbsp;ธ.ค.&nbsp;</td>
    </tr>
    <tr style="font-size: 16px">
      <td>&nbsp;&nbsp;</td>
      <td>&nbsp;จับ&nbsp;</td>
      <td>&nbsp;ขาดอายุ&nbsp;</td>
      <td>&nbsp;จับ&nbsp;</td>
      <td>&nbsp;ขาดอายุ&nbsp;</td>
      <td>&nbsp;จับ&nbsp;</td>
      <td>&nbsp;ขาดอายุ&nbsp;</td>
      <td>&nbsp;จับ&nbsp;</td>
      <td>&nbsp;ขาดอายุ&nbsp;</td>
      <td>&nbsp;จับ&nbsp;</td>
      <td>&nbsp;ขาดอายุ&nbsp;</td>
      <td>&nbsp;จับ&nbsp;</td>
      <td>&nbsp;ขาดอายุ&nbsp;</td>
      <td>&nbsp;จับ&nbsp;</td>
      <td>&nbsp;ขาดอายุ&nbsp;</td>
      <td>&nbsp;จับ&nbsp;</td>
      <td>&nbsp;ขาดอายุ&nbsp;</td>
      <td>&nbsp;จับ&nbsp;</td>
      <td>&nbsp;ขาดอายุ&nbsp;</td>
      <td>&nbsp;จับ&nbsp;</td>
      <td>&nbsp;ขาดอายุ&nbsp;</td>
      <td>&nbsp;จับ&nbsp;</td>
      <td>&nbsp;ขาดอายุ&nbsp;</td>
      <td>&nbsp;จับ&nbsp;</td>
      <td>&nbsp;ขาดอายุ&nbsp;</td>
    </tr>
    <tr style="font-size: 24px">
      <td>&nbsp;<?= $y2 ?>&nbsp;</td>
      <td>&nbsp;<?php echo (($row['m1'] > 0) ? $row['m1']  : "-") ?>&nbsp;</td>
      <td style="color: red">&nbsp;<?php echo (($row['f1'] > 0) ? $row['f1']  : "-") ?>&nbsp;</td>
      <td>&nbsp;<?php echo (($row['m2'] > 0) ? $row['m2']  : "-") ?>&nbsp;</td>
      <td style="color: red">&nbsp;<?php echo (($row['f2'] > 0) ? $row['f2']  : "-") ?>&nbsp;</td>
      <td>&nbsp;<?php echo (($row['m3'] > 0) ? $row['m3']  : "-") ?>&nbsp;</td>
      <td style="color: red">&nbsp;<?php echo (($row['f3'] > 0) ? $row['f3']  : "-") ?>&nbsp;</td>
      <td>&nbsp;<?php echo (($row['m4'] > 0) ? $row['m4']  : "-") ?>&nbsp;</td>
      <td style="color: red">&nbsp;<?php echo (($row['f4'] > 0) ? $row['f4']  : "-") ?>&nbsp;</td>
      <td>&nbsp;<?php echo (($row['m5'] > 0) ? $row['m5']  : "-") ?>&nbsp;</td>
      <td style="color: red">&nbsp;<?php echo (($row['f5'] > 0) ? $row['f5']  : "-") ?>&nbsp;</td>
      <td>&nbsp;<?php echo (($row['m6'] > 0) ? $row['m6']  : "-") ?>&nbsp;</td>
      <td style="color: red">&nbsp;<?php echo (($row['f6'] > 0) ? $row['f6']  : "-") ?>&nbsp;</td>
      <td>&nbsp;<?php echo (($row['m7'] > 0) ? $row['m7']  : "-") ?>&nbsp;</td>
      <td style="color: red">&nbsp;<?php echo (($row['f7'] > 0) ? $row['f7']  : "-") ?>&nbsp;</td>
      <td>&nbsp;<?php echo (($row['m8'] > 0) ? $row['m8']  : "-") ?>&nbsp;</td>
      <td style="color: red">&nbsp;<?php echo (($row['f8'] > 0) ? $row['f8']  : "-") ?>&nbsp;</td>
      <td>&nbsp;<?php echo (($row['m9'] > 0) ? $row['m9']  : "-") ?>&nbsp;</td>
      <td style="color: red">&nbsp;<?php echo (($row['f9'] > 0) ? $row['f9']  : "-") ?>&nbsp;</td>
      <td>&nbsp;<?php echo (($row['m10'] > 0) ? $row['m10']  : "-") ?>&nbsp;</td>
      <td style="color: red">&nbsp;<?php echo (($row['f10'] > 0) ? $row['f10']  : "-") ?>&nbsp;</td>
      <td>&nbsp;<?php echo (($row['m11'] > 0) ? $row['m11']  : "-") ?>&nbsp;</td>
      <td style="color: red">&nbsp;<?php echo (($row['f11'] > 0) ? $row['f11']  : "-") ?>&nbsp;</td>
      <td>&nbsp;<?php echo (($row['m12'] > 0) ? $row['m12']  : "-") ?>&nbsp;</td>
      <td style="color: red">&nbsp;<?php echo (($row['f12'] > 0) ? $row['f12']  : "-") ?>&nbsp;</td>
        
    </tr>
  </tbody>
<?php
    $no++;
          
        // กำหนด คุณสมบัติของหน้าที่จะส่งไปพิมพ์
         // if(($no % ($row_per_page + 1)) == 0) {
         if(($no % ($MAX_ROW)) == 0) {
            echo "</tbody></table></div>";
            
              echo "<div class='footer'>&nbsp;</div> "; // สิ้นสุดการพิม 1 หน้า
            
              print_header();
        }
?>
<?php endforeach; ?>
</table>
</div>
    <br>
    <div align="center">
       <?php
       if ($station == 6707) {
		   echo '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ตรวจแล้วรับรองว่าถูกต้อง<br>
        <br>
        	&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;พ.ต.ท.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br>
		   &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br>
			';
	   } else {
		   echo '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ตรวจแล้วรับรองว่าถูกต้อง<br>
        <br>
        	&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;พ.ต.ท.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br><br>
		   &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<br>
			';
	   }
		
	?>
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;หน.งานสืบสวน <?= $name_station ?> <br><br>
        
        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<?php echo "พิมพ์เอกสารเมื่อ " . DateThaiFull($currentDate) . "<br>"; ?>&nbsp;&nbsp;<br>
    </div>
    
</body>
</html>