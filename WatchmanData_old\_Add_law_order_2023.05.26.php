<?php
include '../Condb.php';
include '../users.inc.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

?>


<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>เพิ่มข้อมูล กฎหมาย ระเบียบ คำสั่ง</title>
<script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/policeinnopolis/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 18px;
    }
input::placeholder {
  color: red;
}
.form-control::placeholder {
  color: sandybrown;
}
</style>
<!-- สำหรับ วันเกิด แบบ พ.ศ. -->
<link rel="stylesheet" href="http://code.jquery.com/ui/1.9.1/themes/base/jquery-ui.css" />
<script src="http://code.jquery.com/jquery-1.8.2.js"></script>
<script src="/policeinnopolis/js/jquery-ui.js"></script>
</head>

<body>
	
	<div class="container">
	<div class="row">
	<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> เพิ่มข้อมูล กฎหมาย ระเบียบ คำสั่ง </div>
	<form action="Save_law_order.php" method="POST" enctype="multipart/form-data" class="body">
	<span style="color: #1203F8">
        
		<label hidden ="hidden">ลำดับ</label>
		<input type = "text" name = "lw_cl_aid" class="form-control"  hidden ="hidden" >
        
        <label>ประเภท</label>
                <select id="lw_cl_type" name="lw_cl_type" class="form-select form-select-sm" aria-label=".form-select-sm example" >
                    <option value="0" selected> </option>  <!-- ดึงข้อมูลมาจากตารางใหม่ -->
                    <?php
                        $res_lt = mysqli_query($conn, "SELECT * FROM `wm_tb_law_order_type` WHERE `lwt_aid` ");
                        while($row_lt = mysqli_fetch_array($res_lt))
                        {
                            echo "<option value='{$row_lt['lwt_aid']}'>{$row_lt['lwt_type']}</option>";
                        }
                    ?>
                </select>
        
		<label>เลขที่</label>
		<input type = "text" name = "lw_cl_no" class="form-control"  placeholder="เลขที่คำสั่ง" >
        
		<label>เรื่อง</label>
		<input type = "text" name="lw_cl_heading" class="form-control"  placeholder="เรื่อง" >
                
		<label>วันที่มีผลบังคับใช้</label>
        <p><input type="text" name="lw_cl_date" id="datepicker" class="form-control" autocomplete="off" ></p>
              <script>
            function set_cal(ele)//function สร้างตัวเลือกปฎิทิน
            {
              $( ele ).datepicker({
                  onSelect:(date_text)=>
                  {
                    let arr=date_text.split("/");
                    let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])+543).toString();
                    $(ele).val(new_date);
                    $(ele).css("color","");
                  },
                  beforeShow:()=>{

                    if($(ele).val()!="")
                    {
                      let arr=$(ele).val().split("/");
                      let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])-543).toString();
                      $(ele).val(new_date);

                    }

                    $(ele).css("color","white");
                  },
                  onClose:()=>{

                      $(ele).css("color","");

                      if($(ele).val()!="")
                      {
                          let arr=$(ele).val().split("-");
                          if(parseInt(arr[2])<2500)
                          {
                              let new_date=arr[0]+"-"+arr[1]+"-"+(parseInt(arr[2])).toString();
                              $(ele).val(new_date);
                          }
                      }
                  },
                  dateFormat:"yy-mm-dd", //กำหนดรูปแบบวันที่เป็น วัน/เดือน/ปี
                  changeMonth:true,//กำหนดให้เลือกเดือนได้
                  changeYear:true,//กำหนดให้เลือกปีได้
                  showOtherMonths:true,//กำหนดให้แสดงวันของเดือนก่อนหน้าได้
              });
            }
          $(document).ready(function(){
            //เรียก function set_cal เมื่อเปิดหน้าเว็บ โดยส่ง object element ที่มี id เป็น datepicker เป็นพารามิเตอร์
            set_cal( $("#datepicker") );
          })
          </script>
        <!-- สำหรับ วันที่ แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ -->
		
		<label>ผู้บันทึก</label>
		<select id="lw_cl_record" name="lw_cl_record" class="form-select form-select-sm"  placeholder="ระบุเจ้าหน้าที่ผู้บันทึก">
			<option value="" selected> </option>
            <?php
                    $bsdp = mysqli_query($conn, "SELECT * FROM `police_name_bsdetective` order by `aid_bsdp` DESC");
                    while($row_bsdp = mysqli_fetch_array($bsdp))
                    {
                        echo "<option value='{$row_bsdp['aid_bsdp']}'>{$row_bsdp['bsdp_name']}</option>";
                    }
                ?>
		</select>
        
		<br>
        <div class="mb-3">
			<label for="formFileMultiple" class="form-label">ไฟล์</label>
			<input class="form-control" type="file" id="lw_cl_file" name="lw_cl_file" multiple>
		</div>
		
		<p>
		<br>
			<input type="submit" value="บันทึกข้อมูล" class="btn btn-success" >
			<td> <a href="index.php?rnd=<?= rand(); ?>&page=law_order" class="btn btn-warning" >ยกเลิก</a> </td>
		</p>
		<br>
		<br>
	</form>
	</div>
	</div>
	</div>
<!--<script src="/js/datetimepicker2.5.1/jquery.ui.datepicker-th.js" type="text/javascript"></script> -->
</body>
</html>