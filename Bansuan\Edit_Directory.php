<?php  //PDO
include "../Condb.php";
include '../users.inc.php';
include '../right_user.php';


// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// สำหรับเลือก จังหวัด อำเภอ ตำบล /และ สถานีตำรวจ
include("../config.inc.php");
include("../classes/class.database.inc.php");
$conn2 = get_connection(); // connect to mySQLDB

$province = isset($_GET['province']) ? $_GET['province'] : 64; // sukhothai
$amphure = isset($_GET['amphure']) ? $_GET['amphure'] : 0;
$tambon = isset($_GET['tambon']) ? $_GET['tambon'] : 0;

// สำหรับเลือกสถานีตำรวจทั่วประเทศ
$region = isset($_GET['region']) ? $_GET['region'] : 0;
$provincial = isset($_GET['provincial']) ? $_GET['provincial'] : 0;
//$station = isset($_GET['station']) ? $_GET['station'] : 0;

$id = $_GET['id'];
$pcid = $_GET['pcid'];

$sql = "SELECT * FROM DirectoryDetention22 WHERE id=:id AND idcard=:pcid ";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':id', $id);
    $stmt->bindParam(':pcid', $pcid);
    $stmt->execute();
    $row = $stmt->fetch(PDO::FETCH_ASSOC);

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>แก้ไข ข้อมูลการรายงาน ม.22 ม.23</title>
<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 18px;
    }
input::placeholder {
  color: red;
}
.form-control::placeholder {
  color: sandybrown;
}
.form-control::value {
  color: sandybrown;
}
</style>
<!-- สำหรับวันที่ เดือนไทย ปี พ.ศ. และ เลือนเลือกปี พ.ศ.ได้  -->
<link rel="stylesheet" href="../datepicker_th/jquery.datetimepicker.css" type="text/css">
<script src="../datepicker_th/jquery-1.8.3.min.js"></script>  
<script src="../datepicker_th/jquery.datetimepicker.full.js"></script>
</head>

<body>
	
	<div class="container">
	<div class="row">
	<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> แก้ไข ข้อมูลการรายงาน ม.22 ม.23</div>
	<form action="Save_directory_edit.php" method="POST" enctype="multipart/form-data" class="body">
        
		<label hidden ="hidden">ลำดับ</label>
		<input type="text" name="id" id="id" value="<?= $row['id']?>" class="form-control"  hidden ="hidden" >
        	
		<label>เลขบัตรประชาชนผู้ถูกควบคุม <span style="color: #F90004">* จำเป็น</span> </label>
		<input type="text" name="idcard" id="idcard" value="<?= $row['idcard']?>" class="form-control"  placeholder="เลขบัตรประชาชน" readonly>
		
		<label for="charge">ข้อหา</label>
		<input type="text" name="charge" id="charge" value="<?= $row['charge']?>" class="form-control" >
        
        <label for="pol_arrest">ชื่อผู้ควบคุม <span style="color: #F90004">* จำเป็น</span> </label>
		<input type="text" name="pol_arrest" id="pol_arrest" value="<?= $row['pol_arrest']?>" class="form-control"  placeholder="ชื่อผู้ควบคุม" required>
		
		<label for="position_arrest">ตำแหน่งผู้ควบคุม</label>
		<input type="text" name="position_arrest" id="position_arrest" value="<?= $row['position_arrest']?>" class="form-control"  placeholder="ตำแหน่งผู้ควบคุม" >
		
		<label for="phone_arrest">โทรศัพท์</label>
		<input type="text" name="phone_arrest" id="phone_arrest" value="<?= $row['phone_arrest']?>" class="form-control"  placeholder="โทรศัพท์ผู้ควบคุม" >
		
		<label>สถานีตำรวจ <span style="color: #F90004">* จำเป็น</span></label><br>
                <!-- เลือกสถานีตำรวจ -->
        <label> บช. </label><br>
        <select name="region" id="region" onChange="do_region_change()" class="form-control" Required >
                <option value="">&lt;&lt; เลือก บช. &gt;&gt;</option>
                <?php
                $region = $row['region']; // <<ดึงข้อมูลจาก ตารางมา row ซึ่งเป็นคนละตัวกับ row_p
                //------------------------------------
			  	$res_re = $conn2->query("SELECT * FROM `wm_tb_police_region` 
                                            ORDER BY `name_region` ASC");
			     $selected = '';
			     while($row_re = $conn2->fetch_row($res_re)) 
                     {
                      $code_region = $row_re['code_region'];
                      $name_region = $row_re['name_region'];
                      // set default provionce to 64 >> sukhothai
                          if($code_region == $region) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$code_region' $selected> $name_region </option>\n";
                     }
			  
			  ?>
              </select>
        
        
        <label> บก. </label><br>        
        <select name="provincial" id="provincial" onChange="do_provincial_change()" value="<?php echo $provincial; ?>" class="form-control" >
        <option value="0">&lt;&lt; เลือก บก. &gt;&gt;</option>
                <?php
                if($region > 0)
                {
                    $provincial = $row['provincial']; // <<
                    //-------------------------------------
                    $res_prov = $conn2->query("SELECT * FROM `wm_tb_police_provincial` 
                                                WHERE `region_code`='$region' 
                                                ORDER BY `provincial_code` ASC");
                    $selected = '';
                  while($res_prov = $conn2->fetch_row($res_prov)) 
                  {
                      $provincial_code = $res_prov['provincial_code']; // 
                      $provincial = $res_prov['provincial'];
                      // set default provionce to 64 >> sukhothai
                          if($provincial_code == $provincial) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$provincial_code' $selected> $provincial </option>\n";
                  }
                }	
                ?>
              </select>
        
        
         <label> สน./สภ. </label>
        <br>
                
        <select name="station" id="station" class="form-control" >
                <option value="0">&lt;&lt; เลือก สน./สภ. &gt;&gt;</option>
                <?php
	           if($provincial > 0)
                {
                   $station = $row['station'];
                   //------------------------------------------
                    $res_st = $conn2->query("SELECT * FROM `wm_tb_police_station2` 
                                                WHERE provincial_code='$provincial_code'
                                                ORDER BY `station_code` ASC");
                    $selected = '';
                  while($row_st = $conn2->fetch_row($res_st)) 
                  {
                      $station_code = $row_st['station_code']; // 
                      $station_name = $row_st['station_name'];
                      // set default provionce to 64 >> sukhothai
                          if($station_code == $station) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$station_code' $selected> $station_name </option>\n";
                  }

                }	
                ?>                                                                     
              </select>
		<br>
        
        <label>ชื่อฝ่ายปกครอง</label>
		<input type="text" name="gov_name" id="gov_name" value= "<?= $row['gov_name']?>" class="form-control"  placeholder="ชื่อฝ่ายปกครอง" >
        
        <label>โทรศัพท์/ช่องทางที่แจ้ง</label>
		<select name="gov_contact" id="gov_contact" class="form-select form-select-sm" placeholder="โทรศัพท์/ช่องทางที่แจ้ง">
			<option value="" selected> </option>
			<option value="เว็บไซต์ https://arrest.dopa.go.th/">เว็บไซต์ https://arrest.dopa.go.th/</option>
			<option value="Line ปกครองป้องกันและปราบปรามการทรมานฯ">Line ปกครองป้องกันและปราบปรามการทรมานฯ</option>
			<option value="โทรศัพท์">โทรศัพท์</option>
		</select>
        
        <label>วัน/เดือน/ปี/เวลา ที่แจ้ง <span style="color: #F90004">* จำเป็น</span> </label>
        <input type="text" name="gov_date" id="gov_date" value="<?= $row['gov_date']?>" class="form-control" style="width:250px;" autocomplete="off" >
        <!-- สำหรับวันที่ เดือนไทย ปี พ.ศ. และ เลือนเลือกปี พ.ศ.ได้  -->
        <script type="text/javascript"> 
            $(function(){
                $.datetimepicker.setLocale('th'); // ต้องกำหนดเสมอถ้าใช้ภาษาไทย และ เป็นปี พ.ศ.
                // กรณีใช้แบบ input
                $("#gov_date").datetimepicker({
                    timepicker:true,
                    format:'Y-m-d H:i',  // กำหนดรูปแบบวันที่ ที่ใช้ เป็น 00-00-0000			
                    lang:'th',  // ต้องกำหนดเสมอถ้าใช้ภาษาไทย และ เป็นปี พ.ศ.
                    onSelectDate:function(dp,$input){
                        var yearT=new Date(dp).getFullYear()-0;  
                        var yearTH=yearT;  // ถ้าจะให้แสดงเป็น พ.ศ. ให้เป็น yearT+543
                        var fulldate=$input.val();
                        var fulldateTH=fulldate.replace(yearT,yearTH);
                        $input.val(fulldateTH);
                    },
                });       
                // กรณีใช้กับ input ต้องกำหนดส่วนนี้ด้วยเสมอ เพื่อปรับปีให้เป็น ค.ศ. ก่อนแสดงปฏิทิน
                $("#gov_date").on(function(e){
                    var dateValue=$(this).val();
                    if(dateValue!=""){
                            var arr_date=dateValue.split("-"); // ถ้าใช้ตัวแบ่งรูปแบบอื่น ให้เปลี่ยนเป็นตามรูปแบบนั้น
                            // ในที่นี้อยู่ในรูปแบบ 00-00-0000 เป็น d-m-Y  แบ่งด่วย - ดังนั้น ตัวแปรที่เป็นปี จะอยู่ใน array
                            //  ตัวที่สอง arr_date[2] โดยเริ่มนับจาก 0 
                            dateValue=dateValue.replace(arr_date[0],yearT);
                            $(this).val(dateValue);													
                    }		
                });
            });
            </script>

        <div class="mb-3">
			<label for="formFileMultiple" class="form-label">ไฟล์แจ้งฝ่ายปกครอง</label>
			<input class="form-control" type="file" id="gov_pdf" name="gov_pdf" value="<?= $row['gov_pdf']?>" multiple>
		</div>
        
        <label>ชื่ออัยการ</label>
		<input type="text" name="attorney_name" id="attorney_name" value="<?= $row['attorney_name']?>" class="form-control" placeholder="ชื่ออัยการ" >
        
        <label>โทรศัพท์/ช่องทางที่แจ้ง</label>
		<select name="att_contact" id="att_contact" class="form-select form-select-sm" placeholder="โทรศัพท์/ช่องทางที่แจ้ง">
			<option value="" selected> </option>
			<option value="เว็บไซต์ https://arrest.dopa.go.th/">เว็บไซต์ https://arrest.dopa.go.th/</option>
			<option value="Line ประสานงานพรบ.อุ้มหาย,ทรมานฯ">Line ประสานงานพรบ.อุ้มหาย,ทรมานฯ</option>
			<option value="โทรศัพท์ 055-651708">โทรศัพท์ 055-651708</option>
			<option value="Email : <EMAIL>">Email : <EMAIL></option>
		</select>
        
        <label>วัน/เดือน/ปี/เวลา ที่แจ้ง <span style="color: #F90004">* จำเป็น</span> </label>
        <input type="text" name="att_date" id="att_date" value="<?= $row['att_date']?>" class="form-control" style="width:250px;" autocomplete="off" >
        <!-- สำหรับวันที่ เดือนไทย ปี พ.ศ. และ เลือนเลือกปี พ.ศ.ได้  -->
        <script type="text/javascript"> 
            $(function(){
                $.datetimepicker.setLocale('th'); // ต้องกำหนดเสมอถ้าใช้ภาษาไทย และ เป็นปี พ.ศ.
                // กรณีใช้แบบ input
                $("#att_date").datetimepicker({
                    timepicker:true,
                    format:'Y-m-d H:i',  // กำหนดรูปแบบวันที่ ที่ใช้ เป็น 00-00-0000			
                    lang:'th',  // ต้องกำหนดเสมอถ้าใช้ภาษาไทย และ เป็นปี พ.ศ.
                    onSelectDate:function(dp,$input){
                        var yearT=new Date(dp).getFullYear()-0;  
                        var yearTH=yearT;  // ถ้าจะให้แสดงเป็น พ.ศ. ให้เป็น yearT+543
                        var fulldate=$input.val();
                        var fulldateTH=fulldate.replace(yearT,yearTH);
                        $input.val(fulldateTH);
                    },
                });       
                // กรณีใช้กับ input ต้องกำหนดส่วนนี้ด้วยเสมอ เพื่อปรับปีให้เป็น ค.ศ. ก่อนแสดงปฏิทิน
                $("#att_date").on(function(e){
                    var dateValue=$(this).val();
                    if(dateValue!=""){
                            var arr_date=dateValue.split("-"); // ถ้าใช้ตัวแบ่งรูปแบบอื่น ให้เปลี่ยนเป็นตามรูปแบบนั้น
                            // ในที่นี้อยู่ในรูปแบบ 00-00-0000 เป็น d-m-Y  แบ่งด่วย - ดังนั้น ตัวแปรที่เป็นปี จะอยู่ใน array
                            //  ตัวที่สอง arr_date[2] โดยเริ่มนับจาก 0 
                            dateValue=dateValue.replace(arr_date[0],yearT);
                            $(this).val(dateValue);													
                    }		
                });
            });
            </script>

        <div class="mb-3">
			<label for="formFileMultiple" class="form-label">ไฟล์แจ้งอัยการ</label>
			<input class="form-control" type="file" id="att_pdf" name="att_pdf" value="<?= $row['att_pdf']?>" multiple>
		</div>
        <hr>
        <div class="mb-3">
			<label for="formFileMultiple" class="form-label">ภาพด้านหน้า</label>
			<input class="form-control" type="file" id="image1" name="image1" value="<?= $row['image1']?>" multiple>
		</div>
        
        <div class="mb-3">
			<label for="formFileMultiple" class="form-label">ภาพด้านหลัง</label>
			<input class="form-control" type="file" id="image2" name="image2" value="<?= $row['image2']?>" multiple>
		</div>
        
        <div class="mb-3">
			<label for="formFileMultiple" class="form-label">ภาพข้างขวา</label>
			<input class="form-control" type="file" id="image3" name="image3" value="<?= $row['image3']?>" multiple>
		</div>
        
        <div class="mb-3">
			<label for="formFileMultiple" class="form-label">ภาพข้างซ้าย</label>
			<input class="form-control" type="file" id="image4" name="image4" value="<?= $row['image4']?>" multiple>
		</div>
        
        <label>หมายเหตุ บันทึกเหตุสุดวิสัย</label>
		<input type="text" name="remark" id="remark" value="<?= $row['remark']?>" class="form-control" placeholder="หมายเหตุ" ><br>

		<p>
		<br>
			<input type="submit" value="บันทึกข้อมูล" class="btn btn-success" >
			<td> <a href="index.php?rnd=<?= rand(); ?>&page=Directory" class="btn btn-warning" >ยกเลิก</a> </td>
		</p>
		<br>
		<br>
	</form>
	</div>
	</div>
	</div>
<!-- ฟังก์ชั่น เลือก จังหวัด อำเภอ ตำบล -->
<script>

function do_province_change()
{
	var sel = document.getElementById("bl_cl_province");		
	var code = sel.options[sel.selectedIndex].value;

	$.get("../ajax/get_amphure.php?province=" + code, 
		  function(data, status){
				//alert("Data: " + data + "\nStatus: " + status);
				// jQuery style						
				$('#bl_cl_amphur').find('option').remove().end();
				var data = $.trim(data);
				if(data == '') {
					alert("ไม่พบข้อมูลอำเภอ !");
					return;
				}

					//.append('<option value="whatever">text</option>').val('whatever');
				var datas = data.split(';');
				for(var i=0; i<datas.length; i++) {
					if(datas[i] == '') continue;
					datas[i] = datas[i].split(':');
					var code = datas[i][0];
					var name = datas[i][1];
					$('#bl_cl_amphur').append('<option value="'+ code+'">' + name + '</option>');
				}
                // Trigger the change event on the amphur dropdown so the tambon dropdown will be populated     
                $('#bl_cl_amphur').trigger('change');
		});
}

function do_amphure_change()
{
	var sel = document.getElementById("bl_cl_province");		
	var code = sel.options[sel.selectedIndex].value;

	var sel2 = document.getElementById("bl_cl_amphur");		
	var code2 = sel2.options[sel2.selectedIndex].value;

	$.get("../ajax/get_district.php?province=" + code + "&amphure=" + code2, 
			  function(data, status){
					//alert("Data: " + data + "\nStatus: " + status);
					// jQuery style						
					$('#bl_cl_tumbon').find('option').remove().end();
					var data = $.trim(data);
					if(data == '') {
						alert("ไม่พบข้อมูลตำบล !");
						return;
					}

						//.append('<option value="whatever">text</option>').val('whatever');
					var datas = data.split(';');
					for(var i=0; i<datas.length; i++) {
						if(datas[i] == '') continue;
						datas[i] = datas[i].split(':');
						var code = datas[i][0];
						var name = datas[i][1];
						$('#bl_cl_tumbon').append('<option value="'+ code+'">' + name + '</option>');
					}
			});
}

// ฟังก์ชั่น เปลี่ยนภาค เป็น จังหวัด
function do_region_change(current_value)
{
    current_value = (typeof current_value == 'undefined') ? 0 : current_value;
	var sel_region = document.getElementById("region");		
	var code_region = sel_region.options[sel_region.selectedIndex].value;

	$.get("../ajax/get_provincial.php?region=" + code_region, 
		  function(data, status){
				//alert("Data: " + data + "\nStatus: " + status);
				// jQuery style						
				$('#provincial').find('option').remove().end();
				var data = $.trim(data);
				if(data == '') {
					alert("ไม่พบข้อมูล บก. !");
					return;
				}

					//.append('<option value="whatever">text</option>').val('whatever');
				var datas = data.split(';');
				for(var i=0; i<datas.length; i++) {
					if(datas[i] == '') continue;
					datas[i] = datas[i].split(':');
					var code_region = datas[i][0];
					var name_region = datas[i][1];
					//$('#provincial').append('<option value="'+ code_region+'">' + name_region + '</option>');
                    if(current_value == code_region) {
                        $('#provincial').append('<option value="'+ code_region+'" selected>' + name_region + '</option>'); 
                    }
                    else {
                        $('#provincial').append('<option value="'+ code_region+'">' + name_region + '</option>');
                    }
				}
                // Trigger the change event on the amphur dropdown so the tambon dropdown will be populated     
                $('#provincial').trigger('change');
		});
}
    //เลือก สถานี จากจังหวัด
function do_provincial_change(current_value)
{
    current_value = (typeof current_value == 'undefined') ? 0 : current_value;
    
	var sel_region = document.getElementById("region");		
	var provincial_code = sel_region.options[sel_region.selectedIndex].value;
    
    var sel_provincial = document.getElementById("provincial");		
	var station_code = sel_provincial.options[sel_provincial.selectedIndex].value;

	$.get("../ajax/get_station.php?region=" + provincial_code + "&provincial=" + station_code, 
			  function(data, status){
					//alert("Data: " + data + "\nStatus: " + status);
					// jQuery style						
					$('#station').find('option').remove().end();
					var data = $.trim(data);
					if(data == '') {
						alert("ไม่พบข้อมูลสถานี !");
						return;
					}

						//.append('<option value="whatever">text</option>').val('whatever');
					var datas = data.split(';');
					for(var i=0; i<datas.length; i++) {
						if(datas[i] == '') continue;
						datas[i] = datas[i].split(':');
						var station_code = datas[i][0];
						var station_name = datas[i][1];
                        if(current_value == station_code) {
                            $('#station').append('<option value="'+ station_code+'" selected>' + station_name + '</option>'); 
                        }
                        else {
                            $('#station').append('<option value="'+ station_code+'">' + station_name + '</option>');   
                        }						
					}
			});
}
</script> 
<!-- สำหรับ ดึงข้อมูลดรอปดาวน์ มาแก้ไข-->
<script>
function auto_select(sid, value)
{
    $('#' + sid).val(value);
}
$(document).ready(function() {
<?php
    echo "auto_select('charge', '{$row['charge']}');\n";
	//echo "auto_select('pol_name', '{$row['pol_name']}');\n";
	echo "auto_select('gov_contact', '{$row['gov_contact']}');\n";
	echo "auto_select('att_contact', '{$row['att_contact']}');\n";
?>
	setTimeout("do_region_change('<?= $row['provincial'] ?>')", 1000);
    setTimeout("do_provincial_change('<?= $row['station'] ?>')", 2000);
    });
</script>
</body>
</html>

