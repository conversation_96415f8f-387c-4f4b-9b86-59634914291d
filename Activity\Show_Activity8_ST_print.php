<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);


$id = isset($_GET['hv_cl_aid']) ? $_GET['hv_cl_aid'] : '';

//get month from select or current month
$month = isset($_GET['month']) ? $_GET['month'] : date("m");
$year = isset($_GET['year']) ? $_GET['year'] : date("Y");

$m1 = '';
$m2 = '';
$m3 = '';
$m4 = '';
$m5 = '';
$m6 = '';
$m7 = '';
$m8 = '';
$m9 = '';
$m10 = '';
$m11 = '';
$m12 = '';
switch($month)
{
    case "01": $m1 = "selected"; break;
    case "02": $m2 = "selected"; break;
    case "03": $m3 = "selected"; break;
    case "04": $m4 = "selected"; break;
    case "05": $m5 = "selected"; break;
    case "06": $m6 = "selected"; break;
    case "07": $m7 = "selected"; break;
    case "08": $m8 = "selected"; break;
    case "09": $m9 = "selected"; break;
    case "10": $m10 = "selected"; break;
    case "11": $m11 = "selected"; break;
    case "12": $m12 = "selected"; break;
}
$current_month = $global_thai_month[0+$month];
$y2 = $year+543;

// สำหรับ พิมพ์หน้า ข้อมูฃ ประกาศ บรรทัด ที่ต้องการพิมพ์
//-----------------
$MAX_ROW = 18;
//-----------------

// สร้างฟังก์ชั่น เพื่อดึง หัวตาราง ไปแสดง ในหน้าต่อไป
function print_header()
{
    echo '
	<h3 align="center">ข้อมูลการติดตามพฤติการณ์และความเคลื่อนไหวของบุคคลพ้นโทษ พักโทษ  <?= $name_provincial ?></h3>
<h3 align="center">ประจำเดือน&nbsp; <?= $current_month ?> &nbsp;<?= $y2 ?> &nbsp;</h3>
<div class="table-responsive">
<table height="221" border="1" cellpadding="1" cellspacing="1" class="table-bordered table-hover mb-4 mt-4">
  <tbody>
    <tr>
      <td rowspan="3" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">&nbsp;สถานี&nbsp;</td>
      <td rowspan="3" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">จำนวนครั้ง<br>(รวมทั้งหมด)</td>
      <td height="46" colspan="5" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">เยี่ยมพบ</td>
      <td colspan="9" bgcolor="#FD5F2F" style="color: #00000; text-align: center; border-color: navy">เยี่ยมไม่พบ</td>
      <td rowspan="3" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">มีผลการปฏิบัติ</td>
      <td rowspan="3" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">ไม่มีผลการปฏิบัติ</td>
    </tr>
    <tr>
      <td rowspan="2" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">&nbsp;จำนวน (คน)&nbsp;</td>
      <td rowspan="2" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">&nbsp;ผลใน Police4.0&nbsp;</td>
      <td rowspan="2" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">ร้อยละความสำเร็จ</td>
      <td rowspan="2" bgcolor="#48CF1A" style="color: #00000; text-align: center; border-color: navy">เก็บ DNA</td>
      <td rowspan="2" bgcolor="#48CF1A" style="color: #00000; text-align: center; border-color: navy">ยังไม่เก็บ DNA</td>
      <td height="58" colspan="3" bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">ทราบที่อยู่</td>
      <td colspan="3" bgcolor="#FDBD62" style="color: #00000; text-align: center; border-color: navy">ไม่ทราบที่อยู่</td>
      <td colspan="3" bgcolor="#FA9779" style="color: #00000; text-align: center; border-color: navy">กระทำผิดอีก/อยู่เรือนจำ</td>
      </tr>
    <tr>
	  <td height="58" bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">&nbsp;จำนวน&nbsp;</td>
	  <td bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">&nbsp;แจ้งท้องที่ใหม่&nbsp;</td>
	  <td bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">&nbsp;ยังไม่แจ้ง&nbsp;</td>
	  <td bgcolor="#FDBD62" style="color: #00000; text-align: center; border-color: navy">&nbsp;จำนวน&nbsp;</td>
	  <td bgcolor="#FDBD62" style="color: #00000; text-align: center; border-color: navy">&nbsp;แจ้ง รจ.และทว.&nbsp;</td>
	  <td bgcolor="#FDBD62" style="color: #00000; text-align: center; border-color: navy">&nbsp;ยังไม่แจ้ง&nbsp;</td>
	  <td bgcolor="#FA9779" style="color: #00000; text-align: center; border-color: navy">&nbsp;จำนวน&nbsp;</td>
	  <td bgcolor="#FA9779" style="color: #00000; text-align: center; border-color: navy">&nbsp;แจ้ง ทว.&nbsp;</td>
	  <td bgcolor="#FA9779" style="color: #00000; text-align: center; border-color: navy">&nbsp;ยังไม่แจ้ง&nbsp;</td>
      </tr>
	';
}

?>

<meta charset="utf-8">
<title>รายงานบุคคลพ้นโทษ/พักโทษ ภ.จว.สุโขทัย</title>
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
        <base target="_top">
		<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
		<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
  		<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
 		<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js"></script>

<style type="text/css">
body{
    font-size:12px; 
}
.textAlignVer{
    display:block;
    filter: flipv fliph;
    -webkit-transform: rotate(-90deg); 
    -moz-transform: rotate(-90deg); 
    transform: rotate(-90deg); 
    position:relative;
    width:20px;
    white-space:nowrap;
    font-size:12px;
    margin-bottom:10px;
}
	td {
		height: 50px;
	}
</style>
<!-- สไตล์ สำหรับ สั่งพิมพ์ข้อมูล -->
<style>
@media only print
{
   
}
    
@media print {
    /* Set the page size to A4 landscape */
    @page {
    size: A4 landscape;
  }
    /* Set the margins */
  body {
    margin: 0;
    padding: 0;
  }
td{
    color: black;
}
tr{
    color: black;
}
  .printId{visibility: visible;}
    
.footer { page-break-after: always;}
}
.body_text{
    font-size: 16px;
    color: black;
}
/* Avoid breaking content across pages */
  .content {
    page-break-inside: avoid;
  }
/* Hide elements that shouldn't be printed */
  .no-print {
    display: none;
  }

</style>
<!--<p><img src="../Image/Head2.jpg" width="100%" height="" alt=""/></p>-->
<body>
<div class="container-fluid" align="left">
<div class="printId">
<h3 align="center">ข้อมูลการติดตามพฤติการณ์และความเคลื่อนไหวของบุคคลพ้นโทษ พักโทษ  <?= $name_provincial ?></h3>
<h3 align="center">ประจำเดือน&nbsp; <?= $current_month ?> &nbsp;<?= $y2 ?> &nbsp;</h3>
<div class="table-responsive">
<table height="221" border="1" cellpadding="1" cellspacing="1" class="table-bordered table-hover mb-4 mt-4">
  <tbody>
    <tr>
      <td rowspan="3" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">&nbsp;สถานี&nbsp;</td>
      <td rowspan="3" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">จำนวนครั้ง<br>(รวมทั้งหมด)</td>
      <td height="46" colspan="5" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">เยี่ยมพบ</td>
      <td colspan="9" bgcolor="#FD5F2F" style="color: #00000; text-align: center; border-color: navy">เยี่ยมไม่พบ</td>
      <td rowspan="3" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">มีผลการปฏิบัติ</td>
      <td rowspan="3" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">ไม่มีผลการปฏิบัติ</td>
    </tr>
    <tr>
      <td rowspan="2" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">&nbsp;จำนวน (คน)&nbsp;</td>
      <td rowspan="2" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">&nbsp;ผลใน Police4.0&nbsp;</td>
      <td rowspan="2" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">ร้อยละความสำเร็จ</td>
      <td rowspan="2" bgcolor="#48CF1A" style="color: #00000; text-align: center; border-color: navy">เก็บ DNA</td>
      <td rowspan="2" bgcolor="#48CF1A" style="color: #00000; text-align: center; border-color: navy">ยังไม่เก็บ DNA</td>
      <td height="58" colspan="3" bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">ทราบที่อยู่</td>
      <td colspan="3" bgcolor="#FDBD62" style="color: #00000; text-align: center; border-color: navy">ไม่ทราบที่อยู่</td>
      <td colspan="3" bgcolor="#FA9779" style="color: #00000; text-align: center; border-color: navy">กระทำผิดอีก/อยู่เรือนจำ</td>
      </tr>
    <tr>
	  <td height="58" bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">&nbsp;จำนวน&nbsp;</td>
	  <td bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">&nbsp;แจ้งท้องที่ใหม่&nbsp;</td>
	  <td bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">&nbsp;ยังไม่แจ้ง&nbsp;</td>
	  <td bgcolor="#FDBD62" style="color: #00000; text-align: center; border-color: navy">&nbsp;จำนวน&nbsp;</td>
	  <td bgcolor="#FDBD62" style="color: #00000; text-align: center; border-color: navy">&nbsp;แจ้ง รจ.และทว.&nbsp;</td>
	  <td bgcolor="#FDBD62" style="color: #00000; text-align: center; border-color: navy">&nbsp;ยังไม่แจ้ง&nbsp;</td>
	  <td bgcolor="#FA9779" style="color: #00000; text-align: center; border-color: navy">&nbsp;จำนวน&nbsp;</td>
	  <td bgcolor="#FA9779" style="color: #00000; text-align: center; border-color: navy">&nbsp;แจ้ง ทว.&nbsp;</td>
	  <td bgcolor="#FA9779" style="color: #00000; text-align: center; border-color: navy">&nbsp;ยังไม่แจ้ง&nbsp;</td>
      </tr>
	  
<?php     
                      
$sql = "SELECT " .
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T1_1, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T1, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T2, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T3, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T4, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T5, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T6, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T7, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T8, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T9, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T10, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T11, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T12, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial) AS T13, " .
	
	// กงไกรลาศ
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6701) AS T1_1_kkl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6701) AS T1_kkl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6701) AS T2_kkl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6701) AS T3_kkl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6701) AS T4_kkl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6701) AS T5_kkl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6701) AS T6_kkl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6701) AS T7_kkl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6701) AS T8_kkl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6701) AS T9_kkl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6701) AS T10_kkl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6701) AS T11_kkl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6701) AS T12_kkl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6701) AS T13_kkl, " .
	// คีรีมาศ
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6702) AS T1_1_krm, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6702) AS T1_krm, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6702) AS T2_krm, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6702) AS T3_krm, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6702) AS T4_krm, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6702) AS T5_krm, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6702) AS T6_krm, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6702) AS T7_krm, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6702) AS T8_krm, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6702) AS T9_krm, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6702) AS T10_krm, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6702) AS T11_krm, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6702) AS T12_krm, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6702) AS T13_krm, " .
	// ทุ่งเสลี่ยม
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6703) AS T1_1_tsl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6703) AS T1_tsl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6703) AS T2_tsl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6703) AS T3_tsl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6703) AS T4_tsl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6703) AS T5_tsl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6703) AS T6_tsl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6703) AS T7_tsl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6703) AS T8_tsl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6703) AS T9_tsl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6703) AS T10_tsl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6703) AS T11_tsl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6703) AS T12_tsl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6703) AS T13_tsl, " .
	// บ้านแก่ง
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6704) AS T1_1_bng, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6704) AS T1_bng, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6704) AS T2_bng, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6704) AS T3_bng, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6704) AS T4_bng, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6704) AS T5_bng, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6704) AS T6_bng, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6704) AS T7_bng, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6704) AS T8_bng, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6704) AS T9_bng, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6704) AS T10_bng, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6704) AS T11_bng, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6704) AS T12_bng, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6704) AS T13_bng, " .
	// บ้านด่านลานหอย
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6705) AS T1_1_bdh, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6705) AS T1_bdh, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6705) AS T2_bdh, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6705) AS T3_bdh, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6705) AS T4_bdh, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6705) AS T5_bdh, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6705) AS T6_bdh, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6705) AS T7_bdh, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6705) AS T8_bdh, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6705) AS T9_bdh, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6705) AS T10_bdh, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6705) AS T11_bdh, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6705) AS T12_bdh, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6705) AS T13_bdh, " .
	// บ้านไร่
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6706) AS T1_1_bnr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6706) AS T1_bnr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6706) AS T2_bnr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6706) AS T3_bnr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6706) AS T4_bnr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6706) AS T5_bnr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6706) AS T6_bnr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6706) AS T7_bnr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6706) AS T8_bnr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6706) AS T9_bnr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6706) AS T10_bnr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6706) AS T11_bnr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6706) AS T12_bnr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6706) AS T13_bnr, " .
	// บ้านสวน
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6707) AS T1_1_bs, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6707) AS T1_bs, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6707) AS T2_bs, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6707) AS T3_bs, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6707) AS T4_bs, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6707) AS T5_bs, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6707) AS T6_bs, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6707) AS T7_bs, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6707) AS T8_bs, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6707) AS T9_bs, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6707) AS T10_bs, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6707) AS T11_bs, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6707) AS T12_bs, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6707) AS T13_bs, " .
	// เมืองเก่า
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6708) AS T1_1_mgk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6708) AS T1_mgk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6708) AS T2_mgk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6708) AS T3_mgk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6708) AS T4_mgk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6708) AS T5_mgk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6708) AS T6_mgk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6708) AS T7_mgk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6708) AS T8_mgk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6708) AS T9_mgk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6708) AS T10_mgk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6708) AS T11_mgk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6708) AS T12_mgk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6708) AS T13_mgk, " .
	// เมืองสุโขทัย
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6709) AS T1_1_mst, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6709) AS T1_mst, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6709) AS T2_mst, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6709) AS T3_mst, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6709) AS T4_mst, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6709) AS T5_mst, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6709) AS T6_mst, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6709) AS T7_mst, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6709) AS T8_mst, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6709) AS T9_mst, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6709) AS T10_mst, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6709) AS T11_mst, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6709) AS T12_mst, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6709) AS T13_mst, " .
	// ศรีนคร
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6710) AS T1_1_snk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6710) AS T1_snk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6710) AS T2_snk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6710) AS T3_snk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6710) AS T4_snk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6710) AS T5_snk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6710) AS T6_snk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6710) AS T7_snk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6710) AS T8_snk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6710) AS T9_snk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6710) AS T10_snk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6710) AS T11_snk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6710) AS T12_snk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6710) AS T13_snk, " .
	// ศรีสัชนาลัย
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6711) AS T1_1_snl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6711) AS T1_snl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6711) AS T2_snl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6711) AS T3_snl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6711) AS T4_snl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6711) AS T5_snl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6711) AS T6_snl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6711) AS T7_snl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6711) AS T8_snl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6711) AS T9_snl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6711) AS T10_snl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6711) AS T11_snl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6711) AS T12_snl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6711) AS T13_snl, " .
	// ศรีสำโรง
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6712) AS T1_1_ssr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6712) AS T1_ssr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6712) AS T2_ssr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6712) AS T3_ssr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6712) AS T4_ssr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6712) AS T5_ssr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6712) AS T6_ssr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6712) AS T7_ssr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6712) AS T8_ssr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6712) AS T9_ssr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6712) AS T10_ssr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6712) AS T11_ssr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6712) AS T12_ssr, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6712) AS T13_ssr, " .
	// สวรรคโลก
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6713) AS T1_1_swl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6713) AS T1_swl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6713) AS T2_swl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6713) AS T3_swl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6713) AS T4_swl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6713) AS T5_swl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6713) AS T6_swl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6713) AS T7_swl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6713) AS T8_swl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6713) AS T9_swl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6713) AS T10_swl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6713) AS T11_swl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6713) AS T12_swl, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6713) AS T13_swl, " .
	// ท่าฉนวน
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6714) AS T1_1_tcn, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6714) AS T1_tcn, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6714) AS T2_tcn, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6714) AS T3_tcn, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6714) AS T4_tcn, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6714) AS T5_tcn, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6714) AS T6_tcn, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6714) AS T7_tcn, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6714) AS T8_tcn, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6714) AS T9_tcn, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6714) AS T10_tcn, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6714) AS T11_tcn, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6714) AS T12_tcn, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6714) AS T13_tcn, " .
	// เมืองบางขลัง
		"(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6715) AS T1_1_mbk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6715) AS T1_mbk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6715) AS T2_mbk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6715) AS T3_mbk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6715) AS T4_mbk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6715) AS T5_mbk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6715) AS T6_mbk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6715) AS T7_mbk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6715) AS T8_mbk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6715) AS T9_mbk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6715) AS T10_mbk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6715) AS T11_mbk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6715) AS T12_mbk, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND `provincial`=:provincial AND `station`=6715) AS T13_mbk " .
	
        " ";

$stmt = $pdo->prepare($sql);
$stmt->bindParam(':month', $month);
$stmt->bindParam(':year', $year);
$stmt->bindParam(':provincial', $provincial); // Add station parameter
try {
    $stmt->execute();
} catch (PDOException $e) {
    echo '$sql Failed: '. $e->getMessage();
}

          
// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no = 1;
while($row = $stmt->fetch(PDO::FETCH_ASSOC))	//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
{
//ฟังก์ชั่น วันที่ ดึงจาก condb
//$strDate = DateThai( $row_mi["date_record"] );
    
if($row["T1"] > 0) {   
    $persen = ($row["T2"]*100)/$row["T1"];
}
else {
    $persen=0;
    }
    $sum1 = $row["T1"] + $row["T5"] + $row["T8"] + $row["T11"];
    $sum2 = $row["T1_1"] - $sum1;

// กงไกรลาศ
if($row["T1_kkl"] > 0) {   
    $persen_kkl = ($row["T2_kkl"]*100)/$row["T1_kkl"];
}
else {
    $persen_kkl=0;
    }
    $sum1_kkl = $row["T1_kkl"] + $row["T5_kkl"] + $row["T8_kkl"] + $row["T11_kkl"];
    $sum2_kkl = $row["T1_1_kkl"] - $sum1_kkl;
	
// คีรีมาศ
if($row["T1_krm"] > 0) {   
    $persen_krm = ($row["T2_krm"]*100)/$row["T1_krm"];
}
else {
    $persen_krm=0;
    }
    $sum1_krm = $row["T1_krm"] + $row["T5_krm"] + $row["T8_krm"] + $row["T11_krm"];
    $sum2_krm = $row["T1_1_krm"] - $sum1_krm;
	
// ทุ่งเสลี่ยม
if($row["T1_tsl"] > 0) {   
    $persen_tsl = ($row["T2_tsl"]*100)/$row["T1_tsl"];
}
else {
    $persen_tsl=0;
    }
    $sum1_tsl = $row["T1_tsl"] + $row["T5_tsl"] + $row["T8_tsl"] + $row["T11_tsl"];
    $sum2_tsl = $row["T1_1_tsl"] - $sum1_tsl;
	
// บ้านแก่ง
if($row["T1_bng"] > 0) {   
    $persen_bng = ($row["T2_bng"]*100)/$row["T1_bng"];
}
else {
    $persen_bng=0;
    }
    $sum1_bng = $row["T1_bng"] + $row["T5_bng"] + $row["T8_bng"] + $row["T11_bng"];
    $sum2_bng = $row["T1_1_bng"] - $sum1_bng;
	
// บ้านด่านลานหอย
if($row["T1_bdh"] > 0) {   
    $persen_bdh = ($row["T2_bdh"]*100)/$row["T1_bdh"];
}
else {
    $persen_bdh=0;
    }
    $sum1_bdh = $row["T1_bdh"] + $row["T5_bdh"] + $row["T8_bdh"] + $row["T11_bdh"];
    $sum2_bdh = $row["T1_1_bdh"] - $sum1_bdh;
	
// บ้านไร่
if($row["T1_bnr"] > 0) {   
    $persen_bnr = ($row["T2_bnr"]*100)/$row["T1_bnr"];
}
else {
    $persen_bnr=0;
    }
    $sum1_bnr = $row["T1_bnr"] + $row["T5_bnr"] + $row["T8_bnr"] + $row["T11_bnr"];
    $sum2_bnr = $row["T1_1_bnr"] - $sum1_bnr;
	
// บ้านสวน
if($row["T1_bs"] > 0) {   
    $persen_bs = ($row["T2_bs"]*100)/$row["T1_bs"];
}
else {
    $persen_bs=0;
    }
    $sum1_bs = $row["T1_bs"] + $row["T5_bs"] + $row["T8_bs"] + $row["T11_bs"];
    $sum2_bs = $row["T1_1_bs"] - $sum1_bs;
	
// เมืองเก่า
if($row["T1_mgk"] > 0) {   
    $persen_mgk = ($row["T2_mgk"]*100)/$row["T1_mgk"];
}
else {
    $persen_mgk=0;
    }
    $sum1_mgk = $row["T1_mgk"] + $row["T5_mgk"] + $row["T8_mgk"] + $row["T11_mgk"];
    $sum2_mgk = $row["T1_1_mgk"] - $sum1_mgk;
	
// เมืองสุโขทัย
if($row["T1_mst"] > 0) {   
    $persen_mst = ($row["T2_mst"]*100)/$row["T1_mst"];
}
else {
    $persen_mst=0;
    }
    $sum1_mst = $row["T1_mst"] + $row["T5_mst"] + $row["T8_mst"] + $row["T11_mst"];
    $sum2_mst = $row["T1_1_mst"] - $sum1_mst;
	
// ศรีนคร
if($row["T1_snk"] > 0) {   
    $persen_snk = ($row["T2_snk"]*100)/$row["T1_snk"];
}
else {
    $persen_snk=0;
    }
    $sum1_snk = $row["T1_snk"] + $row["T5_snk"] + $row["T8_snk"] + $row["T11_snk"];
    $sum2_snk = $row["T1_1_snk"] - $sum1_snk;
	
// ศรีสัชนาลัย
if($row["T1_snl"] > 0) {   
    $persen_snl = ($row["T2_snl"]*100)/$row["T1_snl"];
}
else {
    $persen_snl=0;
    }
    $sum1_snl = $row["T1_snl"] + $row["T5_snl"] + $row["T8_snl"] + $row["T11_snl"];
    $sum2_snl = $row["T1_1_snl"] - $sum1_snl;
	
// ศรีสำโรง
if($row["T1_ssr"] > 0) {   
    $persen_ssr = ($row["T2_ssr"]*100)/$row["T1_ssr"];
}
else {
    $persen_ssr=0;
    }
    $sum1_ssr = $row["T1_ssr"] + $row["T5_ssr"] + $row["T8_ssr"] + $row["T11_ssr"];
    $sum2_ssr = $row["T1_1_ssr"] - $sum1_ssr;
	
// สวรรคโลก
if($row["T1_swl"] > 0) {   
    $persen_swl = ($row["T2_swl"]*100)/$row["T1_swl"];
}
else {
    $persen_swl=0;
    }
    $sum1_swl = $row["T1_swl"] + $row["T5_swl"] + $row["T8_swl"] + $row["T11_swl"];
    $sum2_swl = $row["T1_1_swl"] - $sum1_swl;
	
// ท่าฉนวน
if($row["T1_tcn"] > 0) {   
    $persen_tcn = ($row["T2_tcn"]*100)/$row["T1_tcn"];
}
else {
    $persen_tcn=0;
    }
    $sum1_tcn = $row["T1_tcn"] + $row["T5_tcn"] + $row["T8_tcn"] + $row["T11_tcn"];
    $sum2_tcn = $row["T1_1_tcn"] - $sum1_tcn;
	
// เมืองบางขลัง
if($row["T1_mbk"] > 0) {   
    $persen_mbk = ($row["T2_mbk"]*100)/$row["T1_mbk"];
}
else {
    $persen_mbk=0;
    }
    $sum1_mbk = $row["T1_mbk"] + $row["T5_mbk"] + $row["T8_mbk"] + $row["T11_mbk"];
    $sum2_mbk = $row["T1_1_mbk"] - $sum1_mbk;
?>	

    <tr>
      <td nowrap style="text-align: left; font-size: 22px; font-weight: bold">&nbsp;ภ.จว.สุโขทัย&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T1_1"] > 0) ? $row["T1_1"]  : "") ?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T1"] > 0) ? $row["T1"]  : "") ?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T2"] > 0) ? $row["T2"]  : "") ?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold"><?php echo $persen ?>&nbsp;%</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T3"] > 0) ? $row["T3"]  : "") ?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T4"] > 0) ? $row["T4"]  : "") ?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T5"] > 0) ? $row["T5"]  : "")?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T6"] > 0) ? $row["T6"]  : "")?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T7"] > 0) ? $row["T7"]  : "")?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T8"] > 0) ? $row["T8"]  : "")?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T9"] > 0) ? $row["T9"]  : "")?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T10"] > 0) ? $row["T10"]  : "")?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T11"] > 0) ? $row["T11"]  : "")?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T12"] > 0) ? $row["T12"]  : "")?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($row["T13"] > 0) ? $row["T13"]  : "")?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($sum1 > 0) ? $sum1  : "") ?>&nbsp;</td>
      <td style="font-size: 22px; font-weight: bold">&nbsp;<?php echo (($sum2 > 0) ? $sum2  : "") ?>&nbsp;</td>
    </tr>
	<!-- กงไกรลาศ -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.กงไกรลาศ&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_kkl"] > 0) ? $row["T1_1_kkl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_kkl"] > 0) ? $row["T1_kkl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_kkl"] > 0) ? $row["T2_kkl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?php echo $persen_kkl ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_kkl"] > 0) ? $row["T3_kkl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_kkl"] > 0) ? $row["T4_kkl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_kkl"] > 0) ? $row["T5_kkl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_kkl"] > 0) ? $row["T6_kkl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_kkl"] > 0) ? $row["T7_kkl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_kkl"] > 0) ? $row["T8_kkl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_kkl"] > 0) ? $row["T9_kkl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_kkl"] > 0) ? $row["T10_kkl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_kkl"] > 0) ? $row["T11_kkl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_kkl"] > 0) ? $row["T12_kkl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_kkl"] > 0) ? $row["T13_kkl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_kkl > 0) ? $sum1_kkl : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_kkl > 0) ? $sum2_kkl : "") ?>&nbsp;</td>
    </tr>
	<!-- คีรีมาศ -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.คีรีมาศ&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_krm"] > 0) ? $row["T1_1_krm"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_krm"] > 0) ? $row["T1_krm"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_krm"] > 0) ? $row["T2_krm"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?= $persen_krm ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_krm"] > 0) ? $row["T3_krm"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_krm"] > 0) ? $row["T4_krm"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_krm"] > 0) ? $row["T5_krm"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_krm"] > 0) ? $row["T6_krm"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_krm"] > 0) ? $row["T7_krm"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_krm"] > 0) ? $row["T8_krm"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_krm"] > 0) ? $row["T9_krm"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_krm"] > 0) ? $row["T10_krm"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_krm"] > 0) ? $row["T11_krm"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_krm"] > 0) ? $row["T12_krm"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_krm"] > 0) ? $row["T13_krm"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_krm > 0) ? $sum1_krm : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_krm > 0) ? $sum2_krm : "")?>&nbsp;</td>
    </tr> 
	<!-- ทุ่งเสลี่ยม -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.ทุ่งเสลี่ยม&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_tsl"] > 0) ? $row["T1_1_tsl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_tsl"] > 0) ? $row["T1_tsl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_tsl"] > 0) ? $row["T2_tsl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?= $persen_tsl ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_tsl"] > 0) ? $row["T3_tsl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_tsl"] > 0) ? $row["T4_tsl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_tsl"] > 0) ? $row["T5_tsl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_tsl"] > 0) ? $row["T6_tsl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_tsl"] > 0) ? $row["T7_tsl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_tsl"] > 0) ? $row["T8_tsl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_tsl"] > 0) ? $row["T9_tsl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_tsl"] > 0) ? $row["T10_tsl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_tsl"] > 0) ? $row["T11_tsl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_tsl"] > 0) ? $row["T12_tsl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_tsl"] > 0) ? $row["T13_tsl"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_tsl > 0) ? $sum1_tsl : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_tsl > 0) ? $sum2_tsl : "")?>&nbsp;</td>
    </tr> 
	<!-- บ้านแก่ง -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.บ้านแก่ง&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_bng"] > 0) ? $row["T1_1_bng"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_bng"] > 0) ? $row["T1_bng"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_bng"] > 0) ? $row["T2_bng"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?= $persen_bng ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_bng"] > 0) ? $row["T3_bng"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_bng"] > 0) ? $row["T4_bng"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_bng"] > 0) ? $row["T5_bng"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_bng"] > 0) ? $row["T6_bng"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_bng"] > 0) ? $row["T7_bng"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_bng"] > 0) ? $row["T8_bng"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_bng"] > 0) ? $row["T9_bng"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_bng"] > 0) ? $row["T10_bng"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_bng"] > 0) ? $row["T11_bng"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_bng"] > 0) ? $row["T12_bng"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_bng"] > 0) ? $row["T13_bng"]  : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_bng > 0) ? $sum1_bng : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_bng > 0) ? $sum2_bng : "")?>&nbsp;</td>
    </tr> 
	<!-- บ้านด่านลานหอย -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.บ้านด่านลานหอย&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_bdh"] > 0) ? $row["T1_1_bdh"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_bdh"] > 0) ? $row["T1_bdh"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_bdh"] > 0) ? $row["T2_bdh"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?= $persen_bdh ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_bdh"] > 0) ? $row["T3_bdh"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_bdh"] > 0) ? $row["T4_bdh"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_bdh"] > 0) ? $row["T5_bdh"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_bdh"] > 0) ? $row["T6_bdh"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_bdh"] > 0) ? $row["T7_bdh"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_bdh"] > 0) ? $row["T8_bdh"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_bdh"] > 0) ? $row["T9_bdh"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_bdh"] > 0) ? $row["T10_bdh"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_bdh"] > 0) ? $row["T11_bdh"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_bdh"] > 0) ? $row["T12_bdh"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_bdh"] > 0) ? $row["T13_bdh"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_bdh > 0) ? $sum1_bdh : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_bdh > 0) ? $sum2_bdh : "")?>&nbsp;</td>
    </tr> 
	<!-- บ้านไร่ -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.บ้านไร่&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_bnr"] > 0) ? $row["T1_1_bnr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_bnr"] > 0) ? $row["T1_bnr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_bnr"] > 0) ? $row["T2_bnr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?= $persen_bnr ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_bnr"] > 0) ? $row["T3_bnr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_bnr"] > 0) ? $row["T4_bnr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_bnr"] > 0) ? $row["T5_bnr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_bnr"] > 0) ? $row["T6_bnr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_bnr"] > 0) ? $row["T7_bnr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_bnr"] > 0) ? $row["T8_bnr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_bnr"] > 0) ? $row["T9_bnr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_bnr"] > 0) ? $row["T10_bnr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_bnr"] > 0) ? $row["T11_bnr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_bnr"] > 0) ? $row["T12_bnr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_bnr"] > 0) ? $row["T13_bnr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_bnr > 0) ? $sum1_bnr : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_bnr > 0) ? $sum2_bnr : "")?>&nbsp;</td>
    </tr> 
	<!-- บ้านสวน -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.บ้านสวน&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_bs"] > 0) ? $row["T1_1_bs"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_bs"] > 0) ? $row["T1_bs"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_bs"] > 0) ? $row["T2_bs"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?= $persen_bs ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_bs"] > 0) ? $row["T3_bs"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_bs"] > 0) ? $row["T4_bs"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_bs"] > 0) ? $row["T5_bs"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_bs"] > 0) ? $row["T6_bs"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_bs"] > 0) ? $row["T7_bs"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_bs"] > 0) ? $row["T8_bs"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_bs"] > 0) ? $row["T9_bs"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_bs"] > 0) ? $row["T10_bs"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_bs"] > 0) ? $row["T11_bs"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_bs"] > 0) ? $row["T12_bs"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_bs"] > 0) ? $row["T13_bs"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_bs > 0) ? $sum1_bs : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_bs > 0) ? $sum2_bs : "")?>&nbsp;</td>
    </tr> 
	<!-- เมืองเก่า -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.เมืองเก่า&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_mgk"] > 0) ? $row["T1_1_mgk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_mgk"] > 0) ? $row["T1_mgk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_mgk"] > 0) ? $row["T2_mgk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?= $persen_mgk ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_mgk"] > 0) ? $row["T3_mgk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_mgk"] > 0) ? $row["T4_mgk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_mgk"] > 0) ? $row["T5_mgk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_mgk"] > 0) ? $row["T6_mgk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_mgk"] > 0) ? $row["T7_mgk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_mgk"] > 0) ? $row["T8_mgk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_mgk"] > 0) ? $row["T9_mgk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_mgk"] > 0) ? $row["T10_mgk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_mgk"] > 0) ? $row["T11_mgk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_mgk"] > 0) ? $row["T12_mgk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_mgk"] > 0) ? $row["T13_mgk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_mgk > 0) ? $sum1_mgk : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_mgk > 0) ? $sum2_mgk : "")?>&nbsp;</td>
    </tr>
	<!-- เมืองสุโขทัย -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.เมืองสุโขทัย&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_mst"] > 0) ? $row["T1_1_mst"] : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_mst"] > 0) ? $row["T1_mst"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_mst"] > 0) ? $row["T2_mst"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?= $persen_mst ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_mst"] > 0) ? $row["T3_mst"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_mst"] > 0) ? $row["T4_mst"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_mst"] > 0) ? $row["T5_mst"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_mst"] > 0) ? $row["T6_mst"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_mst"] > 0) ? $row["T7_mst"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_mst"] > 0) ? $row["T8_mst"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_mst"] > 0) ? $row["T9_mst"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_mst"] > 0) ? $row["T10_mst"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_mst"] > 0) ? $row["T11_mst"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_mst"] > 0) ? $row["T12_mst"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_mst"] > 0) ? $row["T13_mst"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_mst > 0) ? $sum1_mst : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_mst > 0) ? $sum2_mst : "") ?>&nbsp;</td>
    </tr> 
	<!-- ศรีนคร -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.ศรีนคร&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_snk"] > 0) ? $row["T1_1_snk"] : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_snk"] > 0) ? $row["T1_snk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_snk"] > 0) ? $row["T2_snk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?php echo $persen_snk ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_snk"] > 0) ? $row["T3_snk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_snk"] > 0) ? $row["T4_snk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_snk"] > 0) ? $row["T5_snk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_snk"] > 0) ? $row["T6_snk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_snk"] > 0) ? $row["T7_snk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_snk"] > 0) ? $row["T8_snk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_snk"] > 0) ? $row["T9_snk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_snk"] > 0) ? $row["T10_snk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_snk"] > 0) ? $row["T11_snk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_snk"] > 0) ? $row["T12_snk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_snk"] > 0) ? $row["T13_snk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_snk > 0) ? $sum1_snk : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_snk > 0) ? $sum2_snk : "") ?>&nbsp;</td>
    </tr> 
	<!-- ศรีสัชนาลัย -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.ศรีสัชนาลัย&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_snl"] > 0) ? $row["T1_1_snl"] : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_snl"] > 0) ? $row["T1_snl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_snl"] > 0) ? $row["T2_snl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?php echo $persen_snl ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_snl"] > 0) ? $row["T3_snl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_snl"] > 0) ? $row["T4_snl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_snl"] > 0) ? $row["T5_snl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_snl"] > 0) ? $row["T6_snl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_snl"] > 0) ? $row["T7_snl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_snl"] > 0) ? $row["T8_snl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_snl"] > 0) ? $row["T9_snl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_snl"] > 0) ? $row["T10_snl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_snl"] > 0) ? $row["T11_snl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_snl"] > 0) ? $row["T12_snl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_snl"] > 0) ? $row["T13_snl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_snl > 0) ? $sum1_snl : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_snl > 0) ? $sum2_snl : "") ?>&nbsp;</td>
    </tr> 
	<!-- ศรีสำโรง -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.ศรีสำโรง&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_ssr"] > 0) ? $row["T1_1_ssr"] : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_ssr"] > 0) ? $row["T1_ssr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_ssr"] > 0) ? $row["T2_ssr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?php echo $persen_ssr ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_ssr"] > 0) ? $row["T3_ssr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_ssr"] > 0) ? $row["T4_ssr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_ssr"] > 0) ? $row["T5_ssr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_ssr"] > 0) ? $row["T6_ssr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_ssr"] > 0) ? $row["T7_ssr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_ssr"] > 0) ? $row["T8_ssr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_ssr"] > 0) ? $row["T9_ssr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_ssr"] > 0) ? $row["T10_ssr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_ssr"] > 0) ? $row["T11_ssr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_ssr"] > 0) ? $row["T12_ssr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_ssr"] > 0) ? $row["T13_ssr"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_ssr > 0) ? $sum1_ssr : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_ssr > 0) ? $sum2_ssr : "") ?>&nbsp;</td>
    </tr> 
	<!-- สวรรคโลก -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.สวรรคโลก&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_swl"] > 0) ? $row["T1_1_swl"] : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_swl"] > 0) ? $row["T1_swl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_swl"] > 0) ? $row["T2_swl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?php echo $persen_swl ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_swl"] > 0) ? $row["T3_swl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_swl"] > 0) ? $row["T4_swl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_swl"] > 0) ? $row["T5_swl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_swl"] > 0) ? $row["T6_swl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_swl"] > 0) ? $row["T7_swl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_swl"] > 0) ? $row["T8_swl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_swl"] > 0) ? $row["T9_swl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_swl"] > 0) ? $row["T10_swl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_swl"] > 0) ? $row["T11_swl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_swl"] > 0) ? $row["T12_swl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_swl"] > 0) ? $row["T13_swl"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_swl > 0) ? $sum1_swl : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_swl > 0) ? $sum2_swl : "") ?>&nbsp;</td>
    </tr> 
	<!-- ท่าฉนวน -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.ท่าฉนวน&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_tcn"] > 0) ? $row["T1_1_tcn"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_tcn"] > 0) ? $row["T1_tcn"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_tcn"] > 0) ? $row["T2_tcn"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?php echo $persen_tcn ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_tcn"] > 0) ? $row["T3_tcn"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_tcn"] > 0) ? $row["T4_tcn"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_tcn"] > 0) ? $row["T5_tcn"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_tcn"] > 0) ? $row["T6_tcn"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_tcn"] > 0) ? $row["T7_tcn"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_tcn"] > 0) ? $row["T8_tcn"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_tcn"] > 0) ? $row["T9_tcn"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_tcn"] > 0) ? $row["T10_tcn"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_tcn"] > 0) ? $row["T11_tcn"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_tcn"] > 0) ? $row["T12_tcn"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_tcn"] > 0) ? $row["T13_tcn"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_tcn > 0) ? $sum1_tcn : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_tcn > 0) ? $sum2_tcn : "") ?>&nbsp;</td>
    </tr> 
	<!-- เมืองบางขลัง -->
	 <tr>
      <td nowrap style="text-align: left">&nbsp;สภ.เมืองบางขลัง&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_1_mbk"] > 0) ? $row["T1_1_mbk"] : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T1_mbk"] > 0) ? $row["T1_mbk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T2_mbk"] > 0) ? $row["T2_mbk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px"><?php echo $persen_mbk ?>&nbsp;%</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T3_mbk"] > 0) ? $row["T3_mbk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T4_mbk"] > 0) ? $row["T4_mbk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T5_mbk"] > 0) ? $row["T5_mbk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T6_mbk"] > 0) ? $row["T6_mbk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T7_mbk"] > 0) ? $row["T7_mbk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T8_mbk"] > 0) ? $row["T8_mbk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T9_mbk"] > 0) ? $row["T9_mbk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T10_mbk"] > 0) ? $row["T10_mbk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T11_mbk"] > 0) ? $row["T11_mbk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T12_mbk"] > 0) ? $row["T12_mbk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($row["T13_mbk"] > 0) ? $row["T13_mbk"] : "")?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum1_mbk > 0) ? $sum1_mbk : "") ?>&nbsp;</td>
      <td style="font-size: 20px">&nbsp;<?php echo (($sum2_mbk > 0) ? $sum2_mbk : "") ?>&nbsp;</td>
    </tr> 
  </tbody>
<?php
        
	$no++;
}//while
?>
</table>
</div>
</div>
</div>
</body>

