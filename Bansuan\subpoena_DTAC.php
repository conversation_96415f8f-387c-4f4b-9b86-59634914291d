<?php /*?><?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';


// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

//$pcid = $_GET['pcid'];

// Set the timezone to your desired location
//date_default_timezone_set('Asia');

// Get the current date
$currentDate = date('d F Y');

?><?php */?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link rel="stylesheet" href="/bootstrap/css/bootstrap-grid.css">
<title>DTAC</title>
<style>
    @media print {
          @page {
            size: A4;
            margin-top: 10mm;
            margin-left: 15mm;
            margin-right: 15mm;
/*			a[href]:after { content: none !important; }
  			img[src]:after { content: none !important; }
			  body::after {
				content: none !important;*/
			  }
			/*@page { margin: 0; }
  			body  { margin: 1.6cm; }*/
          }
	
	 	  a {
			  display: none !important;
			  content: none !important;
		   }
	
          body {
            margin: 0;
            padding: 0;
			font-family : "TH SarabunIT๙","TH Sarabun New", "TH SarabunPSK" ;
          }

			body::after {
				content: none !important;
				display: none !important;
			}
		
          .header {
            text-align: center;
            margin-bottom: 0;
          }

            .date {
                float: right;
                font-family : "TH SarabunIT๙","TH Sarabun New", "TH SarabunPSK" ;
                font-size: 16.0pt;
                line-height: normal;
              }
           .footer {
            text-align: left;
            margin-bottom: 1cm;
          }
        
         .page-break {
            page-break-after: always;
          }
         .no-print {
                display: none;
              }
            }

            @media screen {
              .no-screen {
                display: none;
              }
    }
    
    h2{
        font-family : "TH SarabunIT๙","TH Sarabun New", "TH SarabunPSK" ;
        font-size: 24.0pt;
    }
    h3{
        font-family : "TH SarabunIT๙","TH Sarabun New", "TH SarabunPSK" ;
        font-size: 22.0pt;
    }
    h4{
        font-family : "TH SarabunIT๙","TH Sarabun New", "TH SarabunPSK" ;
        font-size: 20.0pt;
    }
    h5{
        font-family : "TH SarabunIT๙","TH Sarabun New", "TH SarabunPSK" ;
        font-size: 18.0pt;
    }
    p {
        font-family : "TH SarabunIT๙","TH Sarabun New", "TH SarabunPSK" ;
        font-size: 16.0pt;
        line-height: normal;
    }
	#p {
        font-family : "TH SarabunIT๙","TH Sarabun New", "TH SarabunPSK" ;
        font-size: 12.0pt;
        line-height: normal;
    }
	.rtp {
		margin: 0;
	}
	.namedoc {
		padding: 70px 0;
	}
    
    .p2 {
        font-family : "TH SarabunIT๙","TH Sarabun New", "TH SarabunPSK" ;
        font-size: 12.0pt;
        line-height: normal;
    }
    
.u-underline {
      text-decoration: underline;
      text-decoration-color: gray;
      text-decoration-style: dotted;
      text-decoration-thickness: 1px;
    }
.head1 {
	background: #E7EF98;
	  /*width: 250px; /* adjust to your desired width */
	  height: 150px; /* adjust to your desired height */
	  margin: 0;
	  /*border: .5px solid black; /* add a border to see the frame */
	  text-align: center; /* center text inside the container */
	  display: flex; /* center content vertically */
	  justify-content:space-between; /* center content horizontally */
	  /*align-items: center; /* center content vertically */
	}
.head1name {
	background: #F3A5A6;
	display: flex;
	align-items: flex-start;
	align-content: center;
        font-family: "TH SarabunIT๙","TH Sarabun New", "TH SarabunPSK";
        font-size: 15.0pt;
        padding-left: .5rem;
        margin: 0;
    }
.head1logo {
	background: #BDF0C5;
	display:flex;
	align-content: center;
	align-items: center;
	text-align: center;
        font-family: "TH SarabunIT๙","TH Sarabun New", "TH SarabunPSK";
        font-size: 15.0pt;
        padding-left: .5rem;
        margin: 0;
    }
.head1adr {
	display: flex;
	background: #B2C9F4;
		font-family: "TH SarabunIT๙","TH Sarabun New", "TH SarabunPSK";
        font-size: 15.0pt;
        padding-left: .5rem;
        margin: 0;
	}
.lbox4 {
        font-family: "TH SarabunIT๙","TH Sarabun New", "TH SarabunPSK";
        font-size: 5.0pt;
        padding-left: .3rem;
        margin: 0;
    }
    
.mainbody {
        border: #060606 solid 1.5px;
        width: 210mm;
        height: 297mm;
    }
    .boxleft {
        border: #060606 solid 1px;
        width: 50%;
    }
    .boxleftflex {
        border: #060606 solid 1px;
        display: flex;
    }
    
.image-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 10px;
  justify-content: center;
  align-items: center;
  /*display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh; /* Adjust this value if needed */*/
} 
.head-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-gap: 10px;
	}

.row {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px;
  margin: 0px;
}
.row_img {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px;
  margin: 0px;
}

.column {
  position: relative;
  overflow: hidden;
  padding-bottom: 60%; /* Adjust this percentage to control the visible portion of the image */
  transition: 0.3s;
}

    .column img:hover{
        border: 2px solid red;
    }
.column img {
  /*position: absolute;*/
  top: 10px;
  left: 10px;
  width: 50%;
  object-fit: cover;
  padding: 10px;
  transition: 0.3s;
}

.container1 {
                  width: 250px; /* adjust to your desired width */
                  height: 150px; /* adjust to your desired height */
                  margin: 0;
                  border: .5px solid black; /* add a border to see the frame */
                  text-align: center; /* center text inside the container */
                  display: flex; /* center content vertically */
                  justify-content: center; /* center content horizontally */
                  /*align-items: center; /* center content vertically */
}
.container2 {
                  width: 200px; /* adjust to your desired width */
                  height: 100px; /* adjust to your desired height */
                  margin: 0;
                  /*border: 1px solid black; /* add a border to see the frame */
                  text-align: center; /* center text inside the container */
                  display: flex; /* center content vertically */
                  justify-content: center; /* center content horizontally */
                  /*align-items: center; /* center content vertically */
                }
.container_img {
                  width: auto; /* adjust to your desired width */
                  height: 280px; /* adjust to your desired height */
				  border-radius: 25px;
                  margin: 0;
                  border: 1px solid black; /* add a border to see the frame */
                  text-align: center; /* center text inside the container */
                  display: flex; /* center content vertically */
                  justify-content: center; /* center content horizontally */
                  align-items: center; /* center content vertically */
                }
	#sign {
		margin-bottom: 0;
		padding-bottom: 0;
	}
	p #namesign {
		margin: 0;
		padding-top: 0;
	}
	
</style>
</head>

<body>
<?php /*?><?php
$sql = "SELECT *,
            PS.ps_cl_prefix, PS.ps_cl_name, PS.ps_cl_surname, PS.station, PS.ps_cl_image,
            Adr.ad_cl_num, Adr.ad_cl_moo, Adr.ad_cl_tumbon, Adr.ad_cl_amphur, Adr.ad_cl_province,
            Tb.tb_name_th, 
            Ap.ap_name_th, 
            Pv.pv_name_th,
            St.station_name,
            Ch.ch_cl_crimes_detail, Ch.ch_cl_pol_remark, Ch.ch_cl_Moo, Ch.ch_cl_Tumbon, Ch.ch_cl_Amphur, Ch.ch_cl_Province, Ch.station ST,
            St2.station_name ST2, St2.tumbon_st stTb, St2.ampher_st stAp, St2.prov_st stPv,
            Tb2.tb_name_th tb2,
            Ap2.ap_name_th ap2,
            Pv2.pv_name_th pv2
        FROM DirectoryDetention22 Dir
            LEFT JOIN wm_tb_personal PS ON PS.ps_cl_idcard = Dir.idcard
            LEFT JOIN wm_tb_address Adr ON Adr.ad_cl_idcard = Dir.idcard
            LEFT JOIN provinces AS Pv ON Pv.pv_code = Adr.ad_cl_province
            LEFT JOIN amphures AS Ap ON Ap.ap_code = Adr.ad_cl_amphur
            LEFT JOIN districts AS Tb ON Tb.tb_id = Adr.ad_cl_tumbon
            LEFT JOIN wm_tb_police_station2 AS St ON St.station_code = PS.station
            LEFT JOIN wm_tb_crimes_history AS Ch ON Ch.ch_cl_idcard = Dir.idcard
            LEFT JOIN provinces AS Pv2 ON Pv2.pv_code = Ch.ch_cl_Province
            LEFT JOIN amphures AS Ap2 ON Ap2.ap_code = Ch.ch_cl_Amphur
            LEFT JOIN districts AS Tb2 ON Tb2.tb_id = Ch.ch_cl_Tumbon
            LEFT JOIN wm_tb_police_station2 AS St2 ON St2.station_code = Ch.station
        WHERE Dir.station=:station AND Dir.idcard = :pcid 
        ORDER BY Ch.ch_cl_aid DESC
        LIMIT 1;
        ";
    
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':pcid', $pcid);
	$stmt->bindParam(':station', $station);
try{
    $stmt->execute();
}catch (PDOException $e) {
    echo '$sql Failed: '. $e->getMessage();
}
$no = 1;
while($row = $stmt->fetch(PDO::FETCH_ASSOC))	//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
{
 
//วันแจ้งฝ่ายปกครอง
if(!empty($row["gov_date"])){
    $govDate = DateThai( $row["gov_date"] );
}else{
    $govDate = '';
}
    
// วันแจ้งอัยการ
if(!empty($row["att_date"])){
    $attDate = DateThai( $row["att_date"] );
}else{
    $attDate = '';
}
    
// วันควบคุมตัว
if(!empty($row["ch_cl_date"])){
    $ChDate = DateThai( $row["ch_cl_date"] );
}else{
    $ChDate = '';
}
// อายุ
if($row["ps_cl_birthday2"] > 0){
        $age = get_personal_age_2( $row["ps_cl_birthday2"] );
        $age_display = $age . " ปี";
    }else{
        $age_display = '-';
    }
//ตรวจสอบไฟล์ สส.1 ก่อนว่า มีไฟล์ในตารางไหม หากมี ให้ส่งเป็นลิงค์
	$govPdf = $row["gov_pdf"];
	if($govPdf !== '')
	{
		$links_gov = '<a href="/' . $row["gov_pdf"] .'" target="_blank">Download</a>';
	}
	else {
		$links_gov = 'ไม่มีไฟล์';
	}
    
//ตรวจสอบไฟล์ สส.1 ก่อนว่า มีไฟล์ในตารางไหม หากมี ให้ส่งเป็นลิงค์
	$attPdf = $row["att_pdf"];
	if($attPdf !== '')
	{
		$links_att = '<a href="/' . $row["att_pdf"] .'" target="_blank">Download</a>';
	}
	else {
		$links_att = 'ไม่มีไฟล์';
	}
 
//ที่อยู่
    $adr = $row["ad_cl_num"] . ' หมู่ '.  $row["ad_cl_moo"]. ' ต.'. $row["tb_name_th"]. ' อ.'. $row["ap_name_th"]. ' จ.'. $row["pv_name_th"];
    
//ที่อยู่ จับกุม
    $adr_catch = $row["ch_cl_number"]. ' หมู่ '.  $row["ch_cl_Moo"];
    
// เปลี่ยนรูปแบบ เลขบัตรประชาชน
$id_number = $row['ps_cl_idcard'];
$formatted_id = $id_number[0] . "-" . substr($id_number, 1, 4) . "-" . substr($id_number, 5, 5) . "-" . substr($id_number, 10, 2) . "-" . $id_number[12];
    
// ภาพด้านหน้า
$image1 = $row["image1"];
if($image1 != '') {
	$image1 = "<img src='{$image1}' width='80%'> ";
}
// ภาพด้านหลัง
$image2 = $row["image2"];
if($image2 != '') {
	$image2 = "<img src='{$image2}' width='80%'> ";
}
// ภาพด้านหน้า
$image3 = $row["image3"];
if($image3 != '') {
	$image3 = "<img src='{$image3}' width='80%'> ";
}
// ภาพด้านหน้า
$image4 = $row["image4"];
if($image4 != '') {
	$image4 = "<img src='{$image4}' width='80%'> ";
}
	
// กำหนดรูปแบบเวลาใหม่เป็น 00:00
// Assuming $row["ch_cl_time"] contains the time value in "00:00:00" format
$originalTime = $row["ch_cl_time"];

// Create a DateTime object using the original time value
$dateTime = new DateTime($originalTime);

// Format the DateTime object to display only the hours and minutes part
$formattedTime = $dateTime->format('H:i');

// Output the formatted time
//echo $formattedTime;
    
?><?php */?>
    <div class="head-grid">
		<!--<div class="head1">-->
			<div class="head1name">
				<h4 class="namedoc">หมายเรียกพยานเอกสาร</h4>
			</div>
			<div class="head1logo">
				<div class="header" align="center">
					<img src="../images/krut-3-cm.png" alt="Logo" style="width: 3cm;">
					<h4 class="rtp">สำนักงานตำรวจแห่งชาติ</h4>
				</div>
         	</div>
			<div class="head1adr">
				<p id="p" align="left"> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ผู้รับแจ้ง<br>
				ชื่อ-สกุล &nbsp;&nbsp; <u class="u-underline"> <b> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </u></b><br>
				</b>
            	</p>
			</div>
		<!-- </div> ปิด Div Head1 -->
        
		 
       
    </div> <!-- row  -->
    
<div class="lbox3">
    <p>ตามมาตรา ๒๒ แห่งพระราชบัญญัติป้องกันและปราบปรามการทรมานและการกระทำให้บุคคลสูญหาย พ.ศ.๒๕๖๕</p>
      <div class="content">
        <p>&nbsp;&nbsp;&nbsp;&nbsp;๑. ผู้ถูกควบคุมตัว <u class="u-underline"> <b>&nbsp;&nbsp;</u></p>
        <p>เลขประจำตัวประชาชน/หนังสือเดินทาง/บัตรประจำตัวคนต่างด้าว <u class="u-underline"> <b>&nbsp;&nbsp;&nbsp;</b>&nbsp;&nbsp;</u></p>
        <p>ในข้อหา&nbsp;&nbsp;<u class="u-underline"> <b></b>&nbsp;&nbsp;</u><br><input type="checkbox"> และพวก (ในกรณีมากกว่า ๑ คน)</p>
          
        <p>&nbsp;&nbsp;&nbsp;&nbsp;๒. เจ้าหน้าที่ของรัฐผู้รับผิดชอบ (ผู้จับ/ควบคุมตัว) </p>
        <p>ชื่อ &nbsp;&nbsp;<u class="u-underline"> <b>&nbsp;&nbsp;</b>&nbsp;&nbsp;</u>&nbsp;&nbsp;ตำแหน่ง&nbsp;&nbsp;<u class="u-underline"> <b>&nbsp;&nbsp;&nbsp;&nbsp;</b>&nbsp;&nbsp;</u>&nbsp;&nbsp;หน่วยงาน&nbsp;&nbsp;<u class="u-underline"> <b>&nbsp;&nbsp;</b>&nbsp;&nbsp;</u></p>
        <p>&nbsp;&nbsp; กรม&nbsp;&nbsp;<u class="u-underline"> <b></b>&nbsp;&nbsp;</u>&nbsp;&nbsp;กระทรวง&nbsp;&nbsp; <u class="u-underline"> <b>สำนักงานตำรวจแห่งชาติ</b>&nbsp;&nbsp;</u>&nbsp;&nbsp;หมายเลขโทรศัพท์&nbsp;&nbsp;<u class="u-underline"> <b>&nbsp;&nbsp;</b>&nbsp;&nbsp;</u></p> <!-- โทร. -->
        <p>&nbsp;&nbsp;&nbsp;&nbsp;๓. ท้องที่ที่ใช้ควบคุมตัว&nbsp;&nbsp;<u class="u-underline"> <b>&nbsp;&nbsp;&nbsp;&nbsp;</u></b>&nbsp;&nbsp;ตำบล&nbsp;<u class="u-underline"> <b>&nbsp;&nbsp;</u></b> &nbsp; อำเภอ&nbsp;<u class="u-underline"> <b>&nbsp;&nbsp;</u></b> &nbsp; จังหวัด&nbsp;<u class="u-underline"> <b>&nbsp;&nbsp;</u></b></p>
        <!-- Add more paragraphs here -->

          <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ลงชื่อ.................................. เจ้าหน้าที่ผู้รับแจ้ง </p> <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(..................................) </p> <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ตำแหน่ง.......................................... <br> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;รักษาราชการแทนผู้อำนวยการสำนักการสอบสวนและนิติการ<br>
          &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;กรมการปกครอง/นายอำเภอ <b>&nbsp;&nbsp;</b></p>
        </div>
    
</div>  <!-- box3 -->
    
<div class="page-break"></div>  <!-- Insert page break after a certain number of paragraphs -->

    <!-- หน้า 2 -->
    

        <p></p>
        <p></p>
        <p></p>
    </div>

      <div>
        <p></p>
      </div>
</body>
    
<div class="no-print">
        <p> </p>
      </div>

      <a href="https://invest.watchman1.com/Bansuan/pagePdf22gov.php?pcid=5671100029864" class="no-screen"> </a>
    
</html>

<?php /*?><?php
    }
    // Generate the document
function generateDocument() {
  ob_start(); // Start output buffering
    
  return ob_get_clean(); // Return the buffered output
}

// Generate the document and output it
echo generateDocument();
?><?php */?>