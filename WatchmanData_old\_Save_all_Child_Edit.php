<?php
include '../Condb.php';
include '../users.inc.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// เมื่อมีการใช้ ตัวแปร Session จะต้องเปิดก่อน
//session_start();  // เปิดตัวแปร session
/*if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
*/

//echo '<pre>';
//print_r($_POST);
//echo '</pre>';
//
//echo '<hr';
//
//echo '<pre>';
//var_dump($_POST);
//echo '</pre>';

//exit();

// EDIT > AID >> to update
$cd_cl_aid = $_POST[ 'cd_cl_aid' ];

$cd_cl_idcard = $_POST[ 'cd_cl_idcard' ];
$cd_cl_idcard_child = $_POST[ 'cd_cl_idcard_child' ];
$cd_cl_prefix = $_POST[ 'cd_cl_prefix' ];
$cd_cl_sex = $_POST[ 'cd_cl_sex' ];
$cd_cl_name = $_POST[ 'cd_cl_name' ];
$cd_cl_surname = $_POST[ 'cd_cl_surname' ];
$cd_cl_nickname = $_POST[ 'cd_cl_nickname' ];
$cd_cl_birthday2 = $_POST[ 'cd_cl_birthday2' ];
$cd_cl_father = $_POST[ 'cd_cl_father' ];
$cd_cl_father_pid = $_POST[ 'cd_cl_father_pid' ];
$cd_cl_mother = $_POST[ 'cd_cl_mother' ];
$cd_cl_mother_pid = $_POST[ 'cd_cl_mother_pid' ];
$cd_cl_spouse = $_POST[ 'cd_cl_spouse' ];

//$cd_cl_birthday2 = thai_date_2_eng($cd_cl_birthday);

// save Image ใหม่แทน
$file1 = $_FILES[ 'cd_cl_image' ][ 'tmp_name' ];

if ( is_uploaded_file( $file1 ) ) {


    //ลบรูปเก่าในเซิร์ฟเวอร์ก่อน
    $sql = "SELECT * FROM wm_tb_personal WHERE ps_cl_aid='ps_cl_aid' ";
    $res = mysqli_query($conn,$sql);
    if($row = mysqli_fetch_array($res))
    {
        if(($row['cd_cl_image'] != '') && file_exists($row['cd_cl_image'])) {
            unlink( $row['cd_cl_image'] );
        }    
    }

    //เปลี่ยนชื่อไฟล์ใหม่
    $cd_cl_image = "/policeinnopolis/uploaded/Image1/" . basename( $_FILES[ 'cd_cl_image' ][ 'name' ] );	    
    // file + ext    
    $ext = strrchr($cd_cl_image, ".");    
    $cd_cl_image = "../policeinnopolis/uploaded/Image1/File_" . time() . $ext; // file+time+ext        
    move_uploaded_file( $file1, $cd_cl_image );  
} 
else {
  $cd_cl_image = '';
}

// $sql2 >> insert ไปลงตาราง >> personal
// check exists data in personal เช็คว่ามีข้อมูลในตาราง Personal หรือยัง ถ้ามีแล้ว ไม่ต้องทำอะไร
$sql2 = "SELECT * FROM wm_tb_personal WHERE ps_cl_idcard='$cd_cl_idcard_child' ";
//echo $sql3;
$res2 = mysqli_query($conn, $sql2);
// มีข้อมูลแล้ว
if($row2 = mysqli_fetch_array($res2)) {
   
}
// เปนข้อมูลใหม่ >>
else {
    $sql2 = "Udate wm_tb_personal SET " .
        "ps_cl_idcard = '$cd_cl_idcard_child', " .
        "ps_cl_prefix = '$cd_cl_prefix', " .
        "ps_cl_sex = '$cd_cl_sex', " .
        "ps_cl_name = '$cd_cl_name', " .
        "ps_cl_surname = '$cd_cl_surname', " .
        "ps_cl_nickname = '$cd_cl_nickname', " .
        "ps_cl_birthday2 = '$cd_cl_birthday2', " .
        "ps_cl_father = '$cd_cl_father', " .
        "ps_cl_father_pid = '$cd_cl_father_pid', " .
        "ps_cl_mother = '$cd_cl_mother', " .
        "ps_cl_mother_pid = '$cd_cl_mother_pid' ";
    
        if($cd_cl_image !== '')			//เช็คว่า มีไฟล์ภาพอยู่หรือเปล่า ถ้ามี ไม่ต้องอัพเดต
	{
		$sql2 =  $sql2 . ", ps_cl_image = '$cd_cl_image' ";
	}
	$sql2 = $sql2 . " WHERE cd_cl_aid = '$cd_cl_aid' ";
    mysqli_query($conn, $sql2);
    
    //echo( $sql2 );
    // echo mysqli_error($conn);
}



$sql = "UPDATE wm_tb_child SET " .
		"cd_cl_idcard = '$cd_cl_idcard', ".
		"cd_cl_idcard_child = '$cd_cl_idcard_child', " .
		"cd_cl_prefix = '$cd_cl_prefix', " .
		"cd_cl_sex = '$cd_cl_sex', " .
		"cd_cl_name = '$cd_cl_name', " .
		"cd_cl_surname = '$cd_cl_surname', " .
		"cd_cl_nickname = '$cd_cl_nickname', " .
        "cd_cl_birthday2 = '$cd_cl_birthday2', " .
		"cd_cl_father = '$cd_cl_father', " .
		"cd_cl_father_pid = '$cd_cl_father_pid', " .
		"cd_cl_mother = '$cd_cl_mother', " .
		"cd_cl_mother_pid = '$cd_cl_mother_pid', " .
		"cd_cl_spouse = '$cd_cl_spouse' ";

	if($cd_cl_image !== '')			//เช็คว่า มีไฟล์ภาพอยู่หรือเปล่า ถ้ามี ไม่ต้องอัพเดต
	{
		$sql =  $sql . ", cd_cl_image = '$cd_cl_image' ";
	}
	$sql = $sql . " WHERE cd_cl_aid = '$cd_cl_aid' ";
	


$result=mysqli_query($conn,$sql);
if($result){
	echo "<script>alert('บันทึกข้อมูลเรียบร้อย');</script>";	
}else{
	echo "<script>alert(\"บันทึกข้อมูลไม่สำเร็จ\\n" . print_r(mysqli_error($conn), true) . "\");</script>";
}

// เก็บข้อมูล Action
$inputs = str_replace("'", "", compact_array($_POST));
//$prev = str_replace("'", "", implode(',', $row_0));   // , '{$prev}'

$acc = $user['account'];
$sql3 = "INSERT wm_tb_user_action (at_cl_user,at_cl_date,at_cl_action,at_cl_input,at_cl_prev) VALUES('$acc',CURRENT_TIMESTAMP,'Edit child {$cd_cl_idcard}', '$inputs')";
mysqli_query($conn, $sql3);
//

mysqli_close($conn);
echo "<script>window.location='Show_All.php?pcid=" . $cd_cl_idcard . "&page=child';</script>";
?>

