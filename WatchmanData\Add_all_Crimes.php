<?php
include "../Condb.php"; //PDO
include '../users.inc.php';
include '../right_user.php';


// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// สำหรับเลือก จังหวัด อำเภอ ตำบล/ ภ., จว. , สภ.
include("../config.inc.php");
include("../classes/class.database.inc.php");

$pcid = $_GET['pcid'];
$people_name = '';

if ($pcid != '') {
    $query1 = "SELECT * FROM wm_tb_personal WHERE ps_cl_idcard = :pcid";
    $stmt = $pdo->prepare($query1);
    $stmt->bindParam(':pcid', $pcid);
    $stmt->execute();
    $row1 = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $people_name = $row1['ps_cl_prefix'] . ' ' . $row1['ps_cl_name'] . ' ' . $row1['ps_cl_surname'];
    $people_image = $row1["ps_cl_image"];
    
    if ($people_image != '') {
        $people_image = "<img src='{$people_image}' height='80'> ";
    }
}

// สำหรับเลือก จังหวัด อำเภอ ตำบล
$conn2 = get_connection(); // connect to mySQLDB

$province = isset($_GET['province']) ? $_GET['province'] : 0;
if ($provincial == 67) {
    $province = isset($_GET['province']) ? $_GET['province'] : 64; // สุโขทัย
} elseif ($provincial == 68) {
    $province = isset($_GET['province']) ? $_GET['province'] : 53; // อุตรดิตถ์
} elseif ($provincial == 69) {
    $province = isset($_GET['province']) ? $_GET['province'] : 61; // อุทัยธานี
} elseif ($provincial == 65) {
    $province = isset($_GET['province']) ? $_GET['province'] : 65; // พิษณุโลก
} elseif ($provincial == 61) {
    $province = isset($_GET['province']) ? $_GET['province'] : 62; // กำแพงเพชร
} elseif ($provincial == 62) {
    $province = isset($_GET['province']) ? $_GET['province'] : 63; // ตาก
} elseif ($provincial == 63) {
    $province = isset($_GET['province']) ? $_GET['province'] : 60; // นครสวรรค์
} elseif ($provincial == 64) {
    $province = isset($_GET['province']) ? $_GET['province'] : 66; // พิจิตร
} elseif ($provincial == 66) {
    $province = isset($_GET['province']) ? $_GET['province'] : 67; // เพชรบูรณ์
}

$amphure = isset($_GET['amphure']) ? $_GET['amphure'] : 0;
$tambon = isset($_GET['tambon']) ? $_GET['tambon'] : 0;

// สำหรับเลือกสถานีตำรวจทั่วประเทศ
/*if ($region == 6) {
	$region = isset($_GET['region']) ? $_GET['region'] : 6; // ภาค6
}else{
	$region = isset($_GET['region']) ? $_GET['region'] : 0; // ภาคอื่น
}*/
$region = isset($_GET['region']) ? $_GET['region'] : 0; // ภาคอื่น
$provincial = isset($_GET['provincial']) ? $_GET['provincial'] : 0; // sukhothai
//$station = isset($_GET['station']) ? $_GET['station'] : 0; // sukhothai

?>

<!doctype html>
<html><head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>เพิ่มข้อมูลประวัติอาชญากรรม</title>
<!--<script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script> -->   
<link rel="stylesheet" href="/policeinnopolis/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 18px;
    }
input::placeholder {
  color: red;
}
.form-control::placeholder {
  color: sandybrown;
}
</style>
<!-- สำหรับวันที่ เดือนไทย ปี พ.ศ. และ เลือนเลือกปี พ.ศ.ได้  -->
<link rel="stylesheet" href="../datepicker_th/jquery.datetimepicker.css" type="text/css">
<script src="../datepicker_th/jquery-1.8.3.min.js"></script>  
<script src="../datepicker_th/jquery.datetimepicker.full.js"></script>

<script src="/policeinnopolis/js/jquery-ui.js"></script>
<script src="../SweetAlert2/dist/sweetalert2.all.min.js"></script>

<!-- ทำเมนู New กะพริบ -->
<style>
.blink-new {
  color: red;
  font-weight: bold;
  animation: blinker 1s linear infinite;
}
@keyframes blinker {
  50% { opacity: 0; }
}
</style>
	
</head>

<body>
	<div class="container">
	<div class="row">
		<div class="col-sm-10">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> เพิ่มข้อมูลประวัติอาชญากรรม ของ <?= $people_image ?> <?=$people_name?> <?= $pcid ?>  </div>
	<form action="Save_all_Crimes.php" method="POST" enctype="multipart/form-data" class="body">
        
        <label hidden ="hidden">ลำดับ</label>
		<input type = "text" name = "ch_cl_aid" class="form-control"  hidden ="hidden" >
        
		<label> เลขบัตรประชาชน </label>
		<input type = "text" name="ch_cl_idcard" class="form-control"  value="<?= $pcid ?>"  readonly="readonly">
        
        <label>วันที่ทำผิด/ทำประวัติ/ควบคุม/X-ray</label>
        <p><input type="text" name="ch_cl_date" id="datepicker" placeholder="เพื่อข้อมูลถูกต้อง กรุณาเลือกจากปฎิทินด้านล่าง" class="form-control" autocomplete="off" ></p>
        <script type="text/javascript"> 
            $(function(){
                $.datetimepicker.setLocale('th');
                $("#datepicker").datetimepicker({
                    timepicker: false,
                    format:'Y-m-d',
                    lang:'th',
                    onSelectDate:function(dp,$input){
                        var yearT=new Date(dp).getFullYear()-0;  
                        var yearTH=yearT;
                        var fulldate=$input.val();
                        var fulldateTH=fulldate.replace(yearT,yearTH);
                        $input.val(fulldateTH);
                    },
                });
                $("#datepicker").on(function(e){
                    var dateValue=$(this).val();
                    if(dateValue!=""){
                            var arr_date=dateValue.split("-");
                            dateValue=dateValue.replace(arr_date[0],yearT);
                            $(this).val(dateValue);													
                    }		
                });
            });
            </script>
        
        <label> เวลาควบคุมตัว </label>
        <p><input type="text" name="ch_cl_time" id="timepicker" placeholder="ระบุเวลา" class="form-control" autocomplete="off" ></p>
        <script type="text/javascript"> 
            $(function(){
                $.datetimepicker.setLocale('th');
                $("#timepicker").datetimepicker({
                    timepicker:true,
                    format:'H:i',
                    lang:'th',
                    onSelectDate:function(dp,$input){
                        var yearT=new Date(dp).getFullYear()-0;  
                        var yearTH=yearT;
                        var fulldate=$input.val();
                        var fulldateTH=fulldate.replace(yearT,yearTH);
                        $input.val(fulldateTH);
                    },
                });       
                $("#timepicker").on(function(e){
                    var dateValue=$(this).val();
                    if(dateValue!=""){
                            var arr_date=dateValue.split("-");
                            dateValue=dateValue.replace(arr_date[0],yearT);
                            $(this).val(dateValue);													
                    }		
                });
            });
            </script>
        
		<label> ประเภทความผิด </label>
            <select id="ch_cl_crimes_type" name="ch_cl_crimes_type" class="form-select form-select-sm" placeholder="ระบุประเภทความผิด" required>
                <option value="" selected> </option> <!-- ดึงข้อมูลมาจากตารางใหม่ -->
                <?php
                $stmt_type = $pdo->query("SELECT * FROM tbCrimeType ORDER BY ctID ASC");
                while ($row_type = $stmt_type->fetch(PDO::FETCH_ASSOC)) {
                    echo "<option value='{$row_type['ctID']}'>{$row_type['ctName']}</option>";
                }
                ?>
            </select>

            <label> ประเภทความผิด ตามแบบ ศขส.สภ. </label>
            <select id="ch_cl_crimes_type2" name="ch_cl_crimes_type2" class="form-select form-select-sm" placeholder="ระบุประเภทความผิด ตามแบบ ศขส.สภ." required>
                <option value="" selected> </option> <!-- ดึงข้อมูลมาจากตารางใหม่ -->
                <?php
                $stmt_type2 = $pdo->query("SELECT * FROM wm_tb_crimes_type ORDER BY cm_cl_aid ASC");
                while ($row_type2 = $stmt_type2->fetch(PDO::FETCH_ASSOC)) {
                    echo "<option value='{$row_type2['cm_cl_aid']}'>{$row_type2['cm_cl_name']}</option>";
                }
                ?>
            </select>
        <br>
        
        <label> รายละเอียดการทำความผิด หรือพฤติการณ์การถูกควบคุมตัว โดยย่อ <span style="color: #F90004">* จำเป็น</span> </label>
        <textarea name = "ch_cl_crimes_detail" class="form-control" placeholder="ระบุรายละเอียดการทำความผิด หรือพฤติการณ์การถูกควบคุมตัว โดยย่อ"   Required></textarea>
        
        <br>
        <b>กรณีก่อเหตุ โปรดระบุ แผนประทุษกรรม</b><br>
        <label> อาวุธที่ใช้ก่อเหตุ </label>
		<input type = "text" name="ch_cl_weapon" class="form-control"  value="" placeholder="ระบุอาวุธที่ใช้ก่อเหตุ" >
        
        <label> ยานพาหนะที่ใช้ก่อเหตุ </label>
		<input type = "text" name="ch_cl_vehicle" class="form-control"  value="" placeholder="ระบุยานพาหนะที่ใช้ก่อเหตุ" >
        
        <label> ผู้ร่วมก่อเหตุ </label>
		<input type = "text" name="ch_cl_participant" class="form-control"  value="" placeholder="ระบุผู้ร่วมในการก่อเหตุ" >
        
        <label> วิธีการก่อเหตุ </label>
		<input type = "text" name="ch_cl_method" class="form-control"  value="" placeholder="ระบุวิธีการก่อเหตุ หรือแผนประทุษกรรม" >
        
        <br>
        <b>สถานที่จับกุม/ควบคุมตัว/หรือที่เกิดเหตุ</b><br>
        <label> จังหวัด </label><br>
        <select name="ch_cl_Province" id="ch_cl_Province" onChange="do_province_change()" class="form-control" >
            <option value="0">&lt;&lt; เลือกจังหวัด &gt;&gt;</option>
                <?php
			  	$res_p = $conn2->query("SELECT * FROM `provinces` ORDER BY `provinces`.`pv_name_th` ASC");
			     $selected = '';
			     while($row_p = $conn2->fetch_row($res_p)) 
			     {
				  $pv_code = $row_p['pv_code'];
				  $pv_name = $row_p['pv_name_th'];
				  // set default provionce to 64 >> sukhothai
				  if($pv_code == $province) {
					  $selected = 'selected';
				  }
				  else {
					  $selected = '';
				  }
				  //
				  echo "<option value='$pv_code' $selected> $pv_name </option>\n";
			     }
			  
			  ?>
              </select>
        
        <label> อำเภอ </label><br>
        <select name="ch_cl_Amphur" id="ch_cl_Amphur" onChange="do_amphure_change()" class="form-control" >
        <option value="0">&lt;&lt; เลือกอำเภอ &gt;&gt;</option>
            <?php
            if($province > 0)
            {
                $res_a = $conn2->query("SELECT * FROM `amphures` WHERE province_code='$province' ORDER BY `amphures`.`ap_name_th` ASC");
                $selected = '';
              while($row_a = $conn2->fetch_row($res_a)) 
              {
                  $ap_code = $row_a['ap_code']; // 
                  $ap_name = $row_a['ap_name_th'];
                  // set default provionce to 64 >> sukhothai
                  if($ap_code == $amphure) {
                      $selected = 'selected';
                  }
                  else {
                      $selected = '';
                  }
                  //
                  echo "<option value='$ap_code' $selected> $ap_name </option>\n";
              }
            }	
            ?>
          </select>
        
         <label> ตำบล </label>
        <br>
        <select name="ch_cl_Tumbon" id="ch_cl_Tumbon" class="form-control" >
            <option value="0">&lt;&lt; เลือกตำบล &gt;&gt;</option>
            <?php
	           if($amphure > 0)
                {
                    $res_t = $conn2->query("SELECT * FROM `districts` WHERE amphure_code='$amphure' ORDER BY `districts`.`tb_name_th` ASC");
                    $selected = '';
                  while($row_t = $conn2->fetch_row($res_t)) 
                  {
                      $tb_code = $row_t['tb_id']; // 
                      $tb_name = $row_t['tb_name_th'];
                      // set default provionce to 64 >> sukhothai
                      if($tb_code == $tambon) {
                          $selected = 'selected';
                      }
                      else {
                          $selected = '';
                      }
                      //
                      echo "<option value='$tb_code' $selected> $tb_name </option>\n";
                  }
                }	
                ?>                                                                     
              </select>

        <label> หมู่บ้าน/ชุมชน <span style="color: #F90004">* จำเป็น</span> </label><br>
        <input type = "text" name = "ch_cl_Moo" class="form-control" placeholder="ระบุหมู่บ้าน/ชุมชน" Required >
        
        <label> บ้านเลขที่ หรือสถานที่จับกุม </label><br>
        <input type = "text" name = "ch_cl_number" class="form-control" placeholder="ระบุ บ้านเลขที่ หรือสถานที่จับกุม " >
        <br>
        
        <label>สถานีตำรวจ (รับผิดชอบคดี หรือจับกุม/ควบคุมตัว) <span style="color: #F90004">* จำเป็น</span></label><br>
                <!-- เลือกสถานีตำรวจ -->
        <label> บช. </label><br>
        <select name="region" id="region" onChange="do_region_change()" class="form-control" Required >
                <option value="">&lt;&lt; เลือก บช. &gt;&gt;</option>
                <?php
			  	$res_re = $conn2->query("SELECT * FROM `wm_tb_police_region` 
                                            ORDER BY `name_region` ASC");
			     $selected = '';
			     while($row_re = $conn2->fetch_row($res_re)) 
                     {
                      $code_region = $row_re['code_region'];
                      $name_region = $row_re['name_region'];
                      // set default provionce to 64 >> sukhothai
                          if($code_region == $region) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$code_region' $selected> $name_region </option>\n";
                     }
			  
			  ?>
              </select>
        
        <label> บก. </label><br>
        <select name="provincial" id="provincial" onChange="do_provincial_change()" class="form-control" >
        <option value="0">&lt;&lt; เลือก บก. &gt;&gt;</option>
                <?php
                if($region > 0)
                {
                    $res_prov = $conn2->query("SELECT * FROM `wm_tb_police_provincial` 
                                                WHERE `region_code`='$region' 
                                                ORDER BY `provincial_code` ASC");
                    $selected = '';
                  while($res_prov = $conn2->fetch_row($res_prov)) 
                  {
                      $provincial_code = $res_prov['provincial_code']; // 
                      $provincial = $res_prov['provincial'];
                      // set default provionce to 64 >> sukhothai
                          if($provincial_code == $provincial) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$provincial_code' $selected> $provincial </option>\n";
                  }
                }	
                ?>
              </select>

         <label> สน./สภ. </label>
        <br>
        <select name="station" id="station" class="form-control" >
                <option value="0">&lt;&lt; เลือก สน./สภ. &gt;&gt;</option>
                <?php
	           if($provincial > 0)
                {
                    $res_st = $conn2->query("SELECT * FROM `wm_tb_police_station2` 
                                                WHERE provincial_code='$provincial_code'
                                                ORDER BY `station_code` ASC");
                    $selected = '';
                  while($row_st = $conn2->fetch_row($res_st)) 
                  {
                      $station_code = $row_st['station_code']; // 
                      $station_name = $row_st['station_name'];
                      // set default provionce to 64 >> sukhothai
                          if($station_code == $station) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$station_code' $selected> $station_name </option>\n";
                  }
                }	
                ?>                                                                     
              </select>
		<br>
        
        <hr>
		<b>การประชาคม X-ray ผู้ค้าผู้เสพ</b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<span class="blink-new">**NEW**</span>
		<br>
		<label>มีรายชื่อในการประชาคม X-ray หรือไม่</label><br>
		&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
		<label><input name="ch_cl_crimes_Xray" id="ch_cl_crimes_Xray1" type="radio" value="1" onclick="Xray_change()" /> ไม่มีชื่อ </label>
		&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
		<label><input name="ch_cl_crimes_Xray" id="ch_cl_crimes_Xray2" type="radio" value="2" onclick="Xray_change()" /> มีชื่อ </label>
		
		<br><br>
		<label>แยกข้อมูล</label><br>
		&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
		<label><input name="report" id="report1" type="radio" value="1" /> ข้อมูลตามจริง </label>
		&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
		<label><input name="report" id="report2" type="radio" value="2" /> ข้อมูลรายงาน </label>
		
		<br><br>
		<label> ผลการ X-ray </label>
            <select id="ch_cl_crimes_Xray_result" name="ch_cl_crimes_Xray_result" class="form-select form-select-sm" placeholder="ระบุผลการ X-ray">
                <option value="" selected> </option> <!-- ดึงข้อมูลมาจากตารางใหม่ -->
                <?php
                $stmt_xray = $pdo->query("SELECT * FROM Xray_drug_data ORDER BY code_xray ASC");
                while ($row_xray = $stmt_xray->fetch(PDO::FETCH_ASSOC)) {
                    echo "<option value='{$row_xray['code_xray']}'>{$row_xray['name_xray']}</option>";
                }
                ?>
            </select>
		
		<br>
		<label for="ch_cl_date_action">วันเวลาดำเนินการ </label>
		<input type="text" name="ch_cl_date_action" id="ch_cl_date_action" value="" class="form-control" placeholder="วันเวลาดำเนินการ" style="width:250px;" autocomplete="off" >
		<script>
		$(function(){
			$.datetimepicker.setLocale('th');
			$("#ch_cl_date_action").datetimepicker({
				timepicker: true,
				format: 'Y-m-d H:i',
				lang: 'th',
				onSelectDate: function(dp, $input) {
					var yearT = new Date(dp).getFullYear() - 0;
					var yearTH = yearT;
					var fulldate = $input.val();
					var fulldateTH = fulldate.replace(yearT, yearTH);
					$input.val(fulldateTH);
				},
			});
		});
		</script>
		<br>
        
        <hr>
        <b>พฤติกรรมก่อเหตุ หากเป็นแก๊ง/ขบวนการ/เครือข่าย (ระบุเพิ่มเติม)</b><br>
        <label>เลือกประเภท </label>
		<select id="ch_cl_gang" name="ch_cl_gang" class="form-select form-select-sm" >
			<option value="" selected> </option>  <!-- ดึงข้อมูลมาจากตารางใหม่ -->
			<?php
				$stmt_g = $pdo->query("SELECT * FROM wm_tb_crimes_gang order by cg_cl_aid ASC");
                while ($row_g = $stmt_g->fetch(PDO::FETCH_ASSOC)) {
					echo "<option value='{$row_g['cg_cl_aid']}'>{$row_g['cg_cl_gang']}</option>";
				}
			?>
		</select>
        
        <label> ระบุชื่อแก๊ง/ขบวนการ/เครือข่าย </label>
		<input type = "text" name = "ch_cl_name_gang" class="form-control" placeholder="ชื่อแก๊ง/ขบวนการ/เครือข่าย"  >
        
        <label> ทำหน้าที่อะไรใน แก๊ง/ขบวนการ/เครือข่าย </label>
		<input type = "text" name = "ch_cl_duty" class="form-control" placeholder="ทำหน้าที่อะไรใน แก๊ง/ขบวนการ/เครือข่าย"  >

        <div class="mb-3">
			<label for="formFileMultiple" class="form-label">ผัง แก๊ง/ขบวนการ/เครือข่าย (ถ้ามี)</label>
			<input class="form-control" type="file" id="ch_cl_network" name="ch_cl_network" multiple>
		</div>
        
        <label> หมายเหตุ</label>
		<input type = "text" name = "ch_cl_pol_remark" class="form-control" placeholder="หมายเหตุ"  >
        
	 	<br>
			<p>
			<input type="submit" value="บันทึกข้อมูล" class="btn btn-success" >
			<td> <a href="Show_All.php?pcid=<?= $pcid ?>&page=crimes" class="btn btn-warning" >ยกเลิก</a> </td>
			</p>
		<br>
		<br>
	</form>
	</div>
	</div>
	</div>
    
<script>


function do_province_change()
{
	var sel = document.getElementById("ch_cl_Province");		
	var code = sel.options[sel.selectedIndex].value;

	$.get("../ajax/get_amphure.php?province=" + code, 
		  function(data, status){
				//alert("Data: " + data + "\nStatus: " + status);
				// jQuery style						
				$('#ch_cl_Amphur').find('option').remove().end();
				var data = $.trim(data);
				if(data == '') {
					alert("ไม่พบข้อมูลอำเภอ !");
					return;
				}

					//.append('<option value="whatever">text</option>').val('whatever');
				var datas = data.split(';');
				for(var i=0; i<datas.length; i++) {
					if(datas[i] == '') continue;
					datas[i] = datas[i].split(':');
					var code = datas[i][0];
					var name = datas[i][1];
					$('#ch_cl_Amphur').append('<option value="'+ code+'">' + name + '</option>');
				}
// Trigger the change event on the amphur dropdown so the tambon dropdown will be populated     
                $('#ch_cl_Amphur').trigger('change');
		});
}

function do_amphure_change()
{
	var sel = document.getElementById("ch_cl_Province");		
	var code = sel.options[sel.selectedIndex].value;

	var sel2 = document.getElementById("ch_cl_Amphur");		
	var code2 = sel2.options[sel2.selectedIndex].value;

	$.get("../ajax/get_district.php?province=" + code + "&amphure=" + code2, 
			  function(data, status){
					//alert("Data: " + data + "\nStatus: " + status);
					// jQuery style						
					$('#ch_cl_Tumbon').find('option').remove().end();
					var data = $.trim(data);
					if(data == '') {
						alert("ไม่พบข้อมูลตำบล !");
						return;
					}

						//.append('<option value="whatever">text</option>').val('whatever');
					var datas = data.split(';');
					for(var i=0; i<datas.length; i++) {
						if(datas[i] == '') continue;
						datas[i] = datas[i].split(':');
						var code = datas[i][0];
						var name = datas[i][1];
						$('#ch_cl_Tumbon').append('<option value="'+ code+'">' + name + '</option>');
					}
                $('#ch_cl_Tumbon').trigger('change');
			});
}

// ฟังก์ชั่น เปลี่ยนภาค (บช.) เป็น ภ.จว. (บก.)
function do_region_change()
{
	var sel_region = document.getElementById("region");		
	var code_region = sel_region.options[sel_region.selectedIndex].value;

	$.get("../ajax/get_provincial.php?region=" + code_region, 
		  function(data, status){
				//alert("Data: " + data + "\nStatus: " + status);
				// jQuery style						
				$('#provincial').find('option').remove().end();
				var data = $.trim(data);
				if(data == '') {
					alert("ไม่พบข้อมูล บก. !");
					return;
				}

					//.append('<option value="whatever">text</option>').val('whatever');
				var datas = data.split(';');
				for(var i=0; i<datas.length; i++) {
					if(datas[i] == '') continue;
					datas[i] = datas[i].split(':');
					var code_region = datas[i][0];
					var name_region = datas[i][1];
					$('#provincial').append('<option value="'+ code_region+'">' + name_region + '</option>');
				}
                // Trigger the change event on the amphur dropdown so the tambon dropdown will be populated     
                $('#provincial').trigger('change');
		});
}
    //เลือก สถานี จากจังหวัด
function do_provincial_change()
{
	var sel_provincial = document.getElementById("region");		
	var provincial_code = sel_provincial.options[sel_provincial.selectedIndex].value;
    
    var sel_provincial2 = document.getElementById("provincial");		
	var station_code = sel_provincial2.options[sel_provincial2.selectedIndex].value;

	$.get("../ajax/get_station.php?region=" + provincial_code + "&provincial=" + station_code, 
			  function(data, status){
					//alert("Data: " + data + "\nStatus: " + status);
					// jQuery style						
					$('#station').find('option').remove().end();
					var data = $.trim(data);
					if(data == '') {
						alert("ไม่พบข้อมูลสถานี !");
						return;
					}

						//.append('<option value="whatever">text</option>').val('whatever');
					var datas = data.split(';');
					for(var i=0; i<datas.length; i++) {
						if(datas[i] == '') continue;
						datas[i] = datas[i].split(':');
						var station_code = datas[i][0];
						var station_name = datas[i][1];
						$('#station').append('<option value="'+ station_code+'">' + station_name + '</option>');
					}
			});
}
</script>  
</body>
</html>
