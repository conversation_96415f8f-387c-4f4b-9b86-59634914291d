<?php
include '../Condb.php';
include '../users.inc.php';

//echo '<pre>';
//print_r($_POST);
//echo '</pre>';
//exit();

$fr_cl_aid = $_POST[ 'fr_cl_aid' ];
$fr_cl_idcard = $_POST[ 'fr_cl_idcard' ];
$fr_cl_status = $_POST[ 'fr_cl_status' ];
$fr_cl_offense = $_POST[ 'fr_cl_offense' ];
$fr_cl_in_prison = $_POST[ 'fr_cl_in_prison' ];
$fr_cl_start = $_POST[ 'fr_cl_start' ];
$fr_cl_freed = $_POST[ 'fr_cl_freed' ];
$fr_cl_adrress1 = $_POST[ 'fr_cl_adrress1' ];
$fr_cl_adrress2 = $_POST[ 'fr_cl_adrress2' ];
$fr_cl_location = $_POST[ 'fr_cl_location' ];
$fr_cl_police = $_POST[ 'fr_cl_police' ];
$fr_cl_pol_station = $_POST[ 'fr_cl_pol_station' ];
$fr_cl_check_date = $_POST[ 'fr_cl_check_date' ];
$fr_cl_dna = $_POST[ 'fr_cl_dna' ];
$fr_cl_cause_no_keepdna = $_POST[ 'fr_cl_cause_no_keepdna' ];
$fr_cl_tracking = $_POST[ 'fr_cl_tracking' ];
$fr_cl_remark = $_POST[ 'fr_cl_remark' ];

// save Image
$file1 = $_FILES[ 'fr_cl_image' ][ 'tmp_name' ];
if ( is_uploaded_file( $file1 ) ) {
  $fr_cl_image = "/policeinnopolis/uploaded/Image1/" . basename( $_FILES[ 'fr_cl_image' ][ 'name' ] );	
  move_uploaded_file( $file1, "../" . $fr_cl_image );  
} else {
  $fr_cl_image = '';
}
//php >> $[AA-z]

// save file
$file2 = $_FILES[ 'fr_cl_home_image' ][ 'tmp_name' ];
if ( is_uploaded_file( $file2 ) ) {
  $fr_cl_home_image = "/policeinnopolis/uploaded/Image1/" . $_FILES[ 'fr_cl_home_image' ][ 'name' ];	
  move_uploaded_file( $file2, "../" . $fr_cl_home_image );  
} else {
  $fr_cl_home_image = '';
}


// convert Thai dates to eng
if($fr_cl_freed != '') {
	if(strpos($fr_cl_freed, '/') > 0) {
    	$dates = explode('/', $fr_cl_freed);	// d/m/y
	}
	elseif(strpos($fr_cl_freed, '-') > 0) {
		$date = explode('-', $fr_cl_freed);	// y-m-d
		$dates = array($date[2], $date[1], $date[0]);	// d/m/Y
	}
	// thai dates
	if(substr(''.$dates[2],0,2) ==='25') {
		$fr_cl_freed = ($dates[2]-543) . '-' . $dates[1] . '-' . $dates[0];
	}
	// eng dates
	else {
    	$fr_cl_freed = $dates[2] . '-' . $dates[1] . '-' . $dates[0];
	}
}

// convert Thai dates to eng
if($fr_cl_check_date != '') {
	if(strpos($fr_cl_check_date, '/') > 0) {
    	$dates = explode('/', $fr_cl_check_date);	// d/m/y
	}
	elseif(strpos($fr_cl_check_date, '-') > 0) {
		$date = explode('-', $fr_cl_check_date);	// y-m-d
		$dates = array($date[2], $date[1], $date[0]);	// d/m/Y
	}
	// thai dates
	if(substr(''.$dates[2],0,2) ==='25') {
		$fr_cl_check_date = ($dates[2]-543) . '-' . $dates[1] . '-' . $dates[0];
	}
	// eng dates
	else {
    	$fr_cl_check_date = $dates[2] . '-' . $dates[1] . '-' . $dates[0];
	}
}

//print_r($_POST);

$sql = "UPDATE wm_tb_freed SET " .
		"fr_cl_idcard = '$fr_cl_idcard'," .
		"fr_cl_status = '$fr_cl_status'," .
		"fr_cl_offense = '$fr_cl_offense', " . 
		"fr_cl_in_prison = '$fr_cl_in_prison'," .
		"fr_cl_start = '$fr_cl_start',".
		"fr_cl_freed = '$fr_cl_freed', ".
		"fr_cl_adrress1 = '$fr_cl_adrress1', " .
		"fr_cl_adrress2 = '$fr_cl_adrress2', " .
		"fr_cl_location = '$fr_cl_location', " .
		"fr_cl_police = '$fr_cl_police', " .
	  	"fr_cl_pol_station = '$fr_cl_pol_station', " .
		"fr_cl_check_date = '$fr_cl_check_date', " .
		"fr_cl_dna = '$fr_cl_dna', " .
		"fr_cl_cause_no_keepdna = '$fr_cl_cause_no_keepdna', " .
        "fr_cl_tracking = '$fr_cl_tracking', " .
		"fr_cl_remark = '$fr_cl_remark' " ;
	
	if($fr_cl_image !== '')
	{
		$sql =  $sql . ", fr_cl_image = '$fr_cl_image' ";
	}
	
	elseif ($fr_cl_home_image !== ''){
		$sql =  $sql . ", fr_cl_home_image = '$fr_cl_home_image' ";
	}

	$sql = $sql . " WHERE fr_cl_aid = '$fr_cl_aid' ";


$result=mysqli_query($conn,$sql);
if($result){
	echo "<script>alert('บันทึกข้อมูลเรียบร้อย');</script>";	
}else{
	echo "<script>alert(\"บันทึกข้อมูลไม่สำเร็จ\\n" . print_r(mysqli_error($conn), true) . "\");</script>";
}

// เก็บข้อมูล Action สำหรับ EDIT
$inputs = str_replace("'", "", compact_array($_POST));
$prev = str_replace("'", "", implode(',', $row_0));

$acc = $user['account'];
$sql3 = "INSERT wm_tb_user_action (at_cl_user,at_cl_date,at_cl_action,at_cl_input,at_cl_prev) VALUES('$acc',CURRENT_TIMESTAMP,'Edit freed {$fr_cl_idcard}', '$inputs', '{$prev}')";
mysqli_query($conn, $sql3);
//

mysqli_close($conn);

	echo "<script>window.location='Show_All.php?pcid=" . $fr_cl_idcard . "&page=freed ';</script>";
?>
