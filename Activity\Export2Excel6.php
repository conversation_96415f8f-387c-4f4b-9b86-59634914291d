<?php 
// ที่มา  https://www.codexworld.com/export-data-to-excel-in-php/
// Load the database configuration file 
include '../Condb.php';
 
// Filter the excel data 
function filterData(&$str){ 
    $str = preg_replace("/\t/", "\\t", $str); 
    $str = preg_replace("/\r?\n/", "\\n", $str); 
    if(strstr($str, '"')) $str = '"' . str_replace('"', '""', $str) . '"'; 
} 
 
// Excel file name for download 
$fileName = "Activity1_ss12_" . date('Y-m-d') . ".xls"; 
 
// Column names 
$fields = array('เดือนปี', 'เสพ', 'ครอบครอง', 'จำหน่าย', 'ผลิต'); 
 
// Display column names as first row 
$excelData = implode("\t", array_values($fields)) . "\n"; 
 
// Fetch records from database 
//$query = $conn->query("SELECT * FROM test_graph_table"); 
$sql = "SELECT
            CONCAT(YEAR(`pf_cl_dates`)+543) AS `year`,
            MONTH(`pf_cl_dates`) AS month,
            COUNT(`pf_cl_dates`) AS count,
            SUM(`pf_cl_drug`IN('4','5','6')) AS take,
            Sum(`pf_cl_drug`='3') AS have, 
            Sum(`pf_cl_drug`='2') AS sell, 
            Sum(`pf_cl_drug`='1') AS make
        FROM
            `wm_tb_performance`
        WHERE 
            station='$station' AND `pf_cl_type_sub`='พ.ร.บ.ยาเสพติด' AND pf_cl_dates >= :start_date AND pf_cl_dates <= :end_date
        GROUP BY 
            MONTH(`pf_cl_dates`), YEAR(`pf_cl_dates`)
        ORDER BY
            pf_cl_dates ASC ";
      
    $params = array(
    'start_date' => $start_date,
    'end_date' => $end_date
);
      
// Execute the query
try {
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    echo "Query failed: " . $e->getMessage();
    exit();
}

// Check if there are any rows returned by the query
if (count($result) == 0) {
    echo "No data for the selected time period.";
} else {
    // Create an array of all the months between the start date and end date
    $months = array();
    $start = new DateTime($start_date);
    $end = new DateTime($end_date);
    $interval = DateInterval::createFromDateString('1 month');
    $period = new DatePeriod($start, $interval, $end);

    foreach ($period as $dt) {
        $months[] = array(
            'year' => $dt->format('Y') + 543,
            'month' => $dt->format('m'),
            'count' => '0',
            'f' => '0'
        );
    }

    // Left join the result set with the months array
    $data = array_replace($months, $result);
}

if($sql->num_rows > 0){ 
	
    // Output each row of the data 
    foreach ($result as $row){
        $lineData = array($months, $row['take'], $row['have'], $row['sell'], $row['make']); 
        array_walk($lineData, 'filterData'); 
        $excelData .= implode("\t", array_values($lineData)) . "\n"; 
    } 
}else{ 
    $excelData .= 'No records found...'. "\n"; 
} 
 
// Headers for download 
header("Content-Type: application/vnd.ms-excel"); 
header("Content-Disposition: attachment; filename=\"$fileName\""); 
 
// Render excel data 
echo $excelData; 
 
exit;