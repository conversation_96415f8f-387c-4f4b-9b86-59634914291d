<?php
include '../Condb.php';

// สำหรับเลือก จังหวัด อำเภอ ตำบล
include("../config.inc.php");
include("../classes/class.database.inc.php");

$id = isset($_GET['id']) ? ($_GET['id']) : 0;

$sql = "SELECT * FROM wm_tb_crimes_history WHERE ch_cl_aid = '$id' ";
$result = mysqli_query($conn,$sql);
$row = mysqli_fetch_array($result);

//echo $row['provincial'];
//echo $row['station'];
//echo $sql;
//print_r($row);

$people_name = '';
if($row) 
{
	$pcid = $row["ch_cl_idcard"];		
	//
	$query1 = "SELECT ps_cl_prefix, ps_cl_name, ps_cl_surname, ps_cl_image FROM wm_tb_personal WHERE ps_cl_idcard ='$pcid' ";
	$result1 = mysqli_query($conn, $query1);
	if($row1 = mysqli_fetch_array($result1))
	{
		$people_name = $row1['ps_cl_prefix'] . $row1['ps_cl_name'] . ' ' . $row1['ps_cl_surname'];
		$people_image = $row1["ps_cl_image"];
		if($people_image != '') {
			$people_image = "<img src='{$people_image}' height='50'> ";
		}
	}
	else {
		die("ไม่พบข้อมูล pcid: $pcid");
	}
}

// สำหรับเลือก จังหวัด อำเภอ ตำบล
$conn2 = get_connection(); // connect to mySQLDB

$province = isset($_GET['province']) ? $_GET['province'] : 64; // sukhothai
$amphure = isset($_GET['amphure']) ? $_GET['amphure'] : 0; // sukhothai
$tambon = isset($_GET['tambon']) ? $_GET['tambon'] : 0; // sukhothai

// สำหรับเลือกสถานีตำรวจทั่วประเทศ
$region = isset($_GET['region']) ? $_GET['region'] : 0; // sukhothai
$provincial = isset($_GET['provincial']) ? $_GET['provincial'] : 0; // sukhothai
$station = isset($_GET['station']) ? $_GET['station'] : 0; // sukhothai

//print_r($station);
//print_r($row);
?>


<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>แก้ไขข้อมูลประวัติอาชญากรรม</title>
<!--<script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>  -->  
<link rel="stylesheet" href="/policeinnopolis/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 18px;
    }
input::placeholder {
  color: red;
}
.form-control::placeholder {
  color: sandybrown;
}
</style>
<!-- สำหรับ วันเกิด แบบ พ.ศ. -->
<link rel="stylesheet" href="http://code.jquery.com/ui/1.9.1/themes/base/jquery-ui.css" />
<script src="http://code.jquery.com/jquery-1.8.2.js"></script>
<script src="/policeinnopolis/js/jquery-ui.js"></script>
    
</head>

<body>
	<div class="container" >
	<div class="row">
		<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> แก้ไขข้อมูลประวัติอาชญากรรม ของ <?= $people_name ?> <?= $people_image ?> </div>
	<form action="Save_all_Crimes.php" method="POST" enctype="multipart/form-data" class="body">	
        
		<label>ลำดับ</label>
		<input name="ch_cl_aid" type = "text" class="form-control" value= "<?= $row['ch_cl_aid'] ?>" hidden ="hidden"  >
        
		<label>เลขบัตรประชาชน</label>
		<input name="ch_cl_idcard" type = "text" class="form-control" value= "<?= $row['ch_cl_idcard']?>" readonly="readonly"  >
        
		<label>ชื่อ </label>		
		<input name="AA" type = "text" class="form-control" value= "<?= $people_name ?>" readonly="readonly" >

        <label>วันที่ทำผิดหรือทำประวัติ</label>
        <p><input type="text" name="ch_cl_date" id="datepicker" class="form-control" value= "<?=$row['ch_cl_date']?>" autocomplete="off" ></p>
            <script>
            function set_cal(ele)//function สร้างตัวเลือกปฎิทิน
            {
              $( ele ).datepicker({
                  onSelect:(date_text)=>
                  {
                    let arr=date_text.split("/");
                    let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])+543).toString();
                    $(ele).val(new_date);
                    $(ele).css("color","");
                  },
                  beforeShow:()=>{

                    if($(ele).val()!="")
                    {
                      let arr=$(ele).val().split("/");
                      let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])-543).toString();
                      $(ele).val(new_date);

                    }

                    $(ele).css("color","white");
                  },
                  onClose:()=>{

                      $(ele).css("color","");

                      if($(ele).val()!="")
                      {
                          let arr=$(ele).val().split("-");
                          if(parseInt(arr[2])<2500)
                          {
                              let new_date=arr[0]+"-"+arr[1]+"-"+(parseInt(arr[2])).toString();
                              $(ele).val(new_date);
                          }
                      }
                  },
                  dateFormat:"yy-mm-dd", //กำหนดรูปแบบวันที่เป็น วัน/เดือน/ปี
                  changeMonth:true,//กำหนดให้เลือกเดือนได้
                  changeYear:true,//กำหนดให้เลือกปีได้
                  showOtherMonths:true,//กำหนดให้แสดงวันของเดือนก่อนหน้าได้
              });
            }
          $(document).ready(function(){
            //เรียก function set_cal เมื่อเปิดหน้าเว็บ โดยส่ง object element ที่มี id เป็น datepicker เป็นพารามิเตอร์
            set_cal( $("#datepicker") );
          })
          </script>
        <!-- สำหรับ วันเกิด แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ -->
        
        <label> ประเภทความผิด </label>
        <select id="ch_cl_crimes_type" name="ch_cl_crimes_type" class="form-select form-select-sm" placeholder="ระบุประเภทความผิด" Required >
			<option value="" selected>เลือก</option>  <!-- ดึงข้อมูลมาจากตารางใหม่ -->
			<?php
				$res_type = mysqli_query($conn, "SELECT * FROM tbCrimeType ORDER BY ctID ASC");
				while($row_type = mysqli_fetch_array($res_type))
				{
					echo "<option value='{$row_type['ctID']}'>{$row_type['ctName']}</option>";
				}
			?>
		</select>
        
        <label> ประเภทความผิด ตามแบบ ศขส.สภ. </label>
        <select id="ch_cl_crimes_type2" name="ch_cl_crimes_type2" class="form-select form-select-sm" placeholder="ระบุประเภทความผิด ตามแบบ ศขส.สภ." Required >
			<option value="" selected> </option>  <!-- ดึงข้อมูลมาจากตารางใหม่ -->
			<?php
				$res_type2 = mysqli_query($conn, "SELECT * FROM wm_tb_crimes_type ORDER BY cm_cl_aid ASC");
				while($row_type2 = mysqli_fetch_array($res_type2))
				{
					echo "<option value='{$row_type2['cm_cl_aid']}'>{$row_type2['cm_cl_name']}</option>";
				}
			?>
		</select>

        <label> รายละเอียดการทำความผิด <span style="color: #F90004">* จำเป็น</span> </label>
        <textarea name = "ch_cl_crimes_detail" class="form-control" placeholder="ระบุรายละเอียดการทำความผิด" Required><?= $row['ch_cl_crimes_detail']?>
        </textarea>
		
        <b>ข้อมูลที่อยู่ หรือสถานที่เกิดเหตุ</b><br>
        <label> จังหวัด </label><br>
        <select name="ch_cl_Province" id="ch_cl_Province" onChange="do_province_change()" class="form-control" >
            <option value="0">&lt;&lt; เลือกจังหวัด &gt;&gt;</option>
                <?php
                $province = $row['ch_cl_Province']; // <<
                //------------------------------------
			  	$res_p = $conn2->query("SELECT * FROM `provinces` ORDER BY `provinces`.`name_th` ASC");			    
                $selected = '';
			     while($row_p = $conn2->fetch_row($res_p)) 
			     {
				  $code = $row_p['code'];
				  $name = $row_p['name_th'];
				  // set default provionce to 64 >> sukhothai
				  if($code == $province) {
					  $selected = 'selected';
				  }
				  else {
					  $selected = '';
				  }
				  //
				  echo "<option value='$code' $selected> $name </option>\n";
			     }
			  
			  ?>
              </select>

        <label> อำเภอ </label><br>
        <select name="ch_cl_Amphur" id="ch_cl_Amphur" onChange="do_amphure_change()" class="form-control" >
        <option value="0">&lt;&lt; เลือกอำเภอ &gt;&gt;</option>
                <?php
                if($province > 0)
                {
                    $amphure = $row['ch_cl_Amphur']; // <<
                    //-------------------------------------
                    $res_a = $conn2->query("SELECT * FROM `amphures` WHERE province_code='$province' ORDER BY `amphures`.`name_th` ASC");
                    $selected = '';
                  while($row_a = $conn2->fetch_row($res_a)) 
                  {
                      $code = $row_a['code']; // 
                      $name = $row_a['name_th'];
                      // set default provionce to 64 >> sukhothai
                      if($code == $amphure) {
                          $selected = 'selected';
                      }
                      else {
                          $selected = '';
                      }
                      //
                      echo "<option value='$code' $selected> $name </option>\n";
                  }
                }	
                ?>
              </select>
        
         <label> ตำบล </label>
        <br>
        <select name="ch_cl_Tumbon" id="ch_cl_Tumbon" class="form-control" >
                <option value="0">&lt;&lt; เลือกตำบล &gt;&gt;</option>
                <?php
	           if($amphure > 0)
                {
                   $tambon = $row['ch_cl_Tumbon'];
                   //------------------------------------------
                    $res_t = $conn2->query("SELECT * FROM `districts` WHERE amphure_code='$amphure' ORDER BY `districts`.`name_th` ASC");
                    $selected = '';
                      while($row_t = $conn2->fetch_row($res_t)) 
                      {
                          $code = $row_t['id']; // 
                          $name = $row_t['name_th'];
                          // set default provionce to 64 >> sukhothai
                          if($code == $tambon) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                          //
                          echo "<option value='$code' $selected> $name </option>\n";
                      }

                }	
                ?>                                                                     
              </select>

        <label> หมู่บ้าน/ชุมชน <span style="color: #F90004">* จำเป็น</span> </label><br>
        <input type = "text" name = "ch_cl_Moo" class="form-control" value= "<?=$row['ch_cl_Moo']?>" placeholder="ระบุหมู่บ้าน/ชุมชน" Required  >
        <br>
        <label>สถานีตำรวจ (รับผิดชอบคดี) <span style="color: #F90004">* จำเป็น</span></label><br>
                <!-- เลือกสถานีตำรวจ -->
        <label> บช. </label><br>
        <select name="region" id="region" onChange="do_region_change()" class="form-control" Required >
                <option value="">&lt;&lt; เลือก บช. &gt;&gt;</option>
                <?php
                $region = $row['region']; // <<ดึงข้อมูลจาก ตารางมา row ซึ่งเป็นคนละตัวกับ row_p
                //------------------------------------
			  	$res_re = $conn2->query("SELECT * FROM `wm_tb_police_region` ORDER BY `wm_tb_police_region`.`id_region` ASC");
			     $selected = '';
			     while($row_re = $conn2->fetch_row($res_re)) 
                     {
                      $code_region = $row_re['code_region'];
                      $name_region = $row_re['name_region'];
                      // set default provionce to 64 >> sukhothai
                          if($code_region == $region) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$code_region' $selected> $name_region </option>\n";
                     }
			  
			  ?>
              </select>
        
        
        <label> บก. </label><br>        
        <select name="provincial" id="provincial" onChange="do_provincial_change()" value="<?php echo $provincial; ?>" class="form-control" >
        <option value="0">&lt;&lt; เลือก บก. &gt;&gt;</option>
                <?php
                if($region > 0)
                {
                    $provincial = $row['provincial']; // <<
                    //-------------------------------------
                    $res_prov = $conn2->query("SELECT * FROM `wm_tb_police_provincial` WHERE `region_code`='$region' ORDER BY `wm_tb_police_provincial`.`wm_PID` ASC");
                    $selected = '';
                  while($res_prov = $conn2->fetch_row($res_prov)) 
                  {
                      $provincial_code = $res_prov['provincial_code']; // 
                      $provincial = $res_prov['provincial'];
                      // set default provionce to 64 >> sukhothai
                          if($provincial_code == $provincial) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$provincial_code' $selected> $provincial </option>\n";
                  }
                }	
                ?>
              </select>
        
        
         <label> สน./สภ. </label>
        <br>
                
        <select name="station" id="station" class="form-control" >
                <option value="0">&lt;&lt; เลือก สน./สภ. &gt;&gt;</option>
                <?php
	           if($provincial > 0)
                {
                   $station = $row['station'];
                   //------------------------------------------
                    $res_st = $conn2->query("SELECT * FROM `wm_tb_police_station2` WHERE provincial_code='$provincial_code' ORDER BY `districts`.`station_name` ASC");
                    $selected = '';
                  while($row_st = $conn2->fetch_row($res_st)) 
                  {
                      $station_code = $row_st['station_code']; // 
                      $station_name = $row_st['station_name'];
                      // set default provionce to 64 >> sukhothai
                          if($station_code == $station) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$station_code' $selected> $station_name </option>\n";
                  }
                }	
                ?>                                                                     
              </select>
		<br>
        
        <b>พฤติกรรมก่อเหตุ หากเป็นแก๊ง/ขบวนการ/เครือข่าย (ระบุเพิ่มเติม)</b><br>
        
        <label> เลือกประเภทแก๊ง </label>
        <select id="ch_cl_gang" name="ch_cl_gang" class="form-select form-select-sm" placeholder="ระบุแก๊ง/ขบวนการ/เครือข่าย" >
			<option value="" selected> </option>  <!-- ดึงข้อมูลมาจากตารางใหม่ -->
			<?php
				$res_ga = mysqli_query($conn, "SELECT * FROM wm_tb_crimes_gang ORDER BY cg_cl_aid ASC");
				while($row_ga = mysqli_fetch_array($res_ga))
				{
					echo "<option value='{$row_ga['cg_cl_aid']}'>{$row_ga['cg_cl_gang']}</option>";
				}
			?>
		</select>
        
        <label> ระบุชื่อแก๊ง/ขบวนการ/เครือข่าย </label>
		<input type = "text" name = "ch_cl_name_gang" class="form-control" value= "<?=$row['ch_cl_name_gang']?>" placeholder="ชื่อแก๊ง/ขบวนการ/เครือข่าย"  >
        
        <label> ทำหน้าที่อะไรใน แก๊ง/ขบวนการ/เครือข่าย </label>
		<input type = "text" name = "ch_cl_duty" class="form-control" value= "<?=$row['ch_cl_duty']?>" placeholder="ทำหน้าที่อะไรใน แก๊ง/ขบวนการ/เครือข่าย"  >
        
        <div class="mb-3">
			<label for="formFileMultiple" class="form-label">ผัง แก๊ง/ขบวนการ/เครือข่าย (ถ้ามี)</label>
			<input class="form-control" type="file" id="ch_cl_network" name="ch_cl_network" multiple value=<?=$row['ch_cl_network']?> >
		</div>
        
        <label> หมายเหตุ </label>
		<input type = "text" name = "ch_cl_pol_remark" class="form-control" value= "<?=$row['ch_cl_pol_remark']?>" placeholder="หมายเหตุ"  >
		<br>
		<p>
			<input type="submit" value="Update" class="btn btn-success" >
			<td> <a href="Show_All.php?pcid=<?= $pcid ?>&page=crimes" class="btn btn-warning" >ยกเลิก </a></td>
		</p>
	</form>
	</div>
	</div>
	</div>
<script>
function do_province_change()
{
	var sel = document.getElementById("ch_cl_Province");		
	var code = sel.options[sel.selectedIndex].value;

	$.get("../ajax/get_amphure.php?province=" + code, 
		  function(data, status){
				//alert("Data: " + data + "\nStatus: " + status);
				// jQuery style						
				$('#ch_cl_Amphur').find('option').remove().end();
				var data = $.trim(data);
				if(data == '') {
					alert("ไม่พบข้อมูลอำเภอ !");
					return;
				}

					//.append('<option value="whatever">text</option>').val('whatever');
				var datas = data.split(';');
				for(var i=0; i<datas.length; i++) {
					if(datas[i] == '') continue;
					datas[i] = datas[i].split(':');
					var code = datas[i][0];
					var name = datas[i][1];
					$('#ch_cl_Amphur').append('<option value="'+ code+'">' + name + '</option>');
				}

                // Trigger the change event on the amphur dropdown so the tambon dropdown will be populated     
                $('#ch_cl_Amphur').trigger('change');   
        }); 
}

function do_amphure_change()
{
	var sel = document.getElementById("ch_cl_Province");		
	var code = sel.options[sel.selectedIndex].value;

	var sel2 = document.getElementById("ch_cl_Amphur");		
	var code2 = sel2.options[sel2.selectedIndex].value;

	$.get("../ajax/get_district.php?province=" + code + "&amphure=" + code2, 
			  function(data, status){
					//alert("Data: " + data + "\nStatus: " + status);
					// jQuery style						
					$('#ch_cl_Tumbon').find('option').remove().end();
					var data = $.trim(data);
					if(data == '') {
						alert("ไม่พบข้อมูลตำบล !");
						return;
					}

						//.append('<option value="whatever">text</option>').val('whatever');
					var datas = data.split(';');
					for(var i=0; i<datas.length; i++) {
						if(datas[i] == '') continue;
						datas[i] = datas[i].split(':');
						var code = datas[i][0];
						var name = datas[i][1];
						$('#ch_cl_Tumbon').append('<option value="'+ code+'">' + name + '</option>');
					}


			});
}
// ฟังก์ชั่น เปลี่ยนภาค เป็น จังหวัด
function do_region_change(current_value)
{
    current_value = (typeof current_value == 'undefined') ? 0 : current_value;
	var sel_region = document.getElementById("region");		
	var code_region = sel_region.options[sel_region.selectedIndex].value;

	$.get("../ajax/get_provincial.php?region=" + code_region, 
		  function(data, status){
				//alert("Data: " + data + "\nStatus: " + status);
				// jQuery style						
				$('#provincial').find('option').remove().end();
				var data = $.trim(data);
				if(data == '') {
					//alert("ไม่พบข้อมูล บก. !");
					return;
				}

					//.append('<option value="whatever">text</option>').val('whatever');
				var datas = data.split(';');
				for(var i=0; i<datas.length; i++) {
					if(datas[i] == '') continue;
					datas[i] = datas[i].split(':');
					var code_region = datas[i][0];
					var name_region = datas[i][1];
					//$('#provincial').append('<option value="'+ code_region+'">' + name_region + '</option>');
                    if(current_value == code_region) {
                        $('#provincial').append('<option value="'+ code_region+'" selected>' + name_region + '</option>'); 
                    }
                    else {
                        $('#provincial').append('<option value="'+ code_region+'">' + name_region + '</option>');
                    }
				}
		});
}
    //เลือก สถานี จากจังหวัด
function do_provincial_change(current_value)
{
    current_value = (typeof current_value == 'undefined') ? 0 : current_value;
    
	var sel_provincial = document.getElementById("region");		
	var provincial_code = sel_provincial.options[sel_provincial.selectedIndex].value;
    
    var sel_provincial2 = document.getElementById("provincial");		
	var station_code = sel_provincial2.options[sel_provincial2.selectedIndex].value;

	$.get("../ajax/get_station.php?region=" + provincial_code + "&provincial=" + station_code, 
			  function(data, status){
					//alert("Data: " + data + "\nStatus: " + status);
					// jQuery style						
					$('#station').find('option').remove().end();
					var data = $.trim(data);
					if(data == '') {
						//alert("ไม่พบข้อมูลสถานี !");
						return;
					}

						//.append('<option value="whatever">text</option>').val('whatever');
					var datas = data.split(';');
					for(var i=0; i<datas.length; i++) {
						if(datas[i] == '') continue;
						datas[i] = datas[i].split(':');
						var station_code = datas[i][0];
						var station_name = datas[i][1];
                        if(current_value == station_code) {
                            $('#station').append('<option value="'+ station_code+'" selected>' + station_name + '</option>'); 
                        }
                        else {
                            $('#station').append('<option value="'+ station_code+'">' + station_name + '</option>');   
                        }						
					}
			});
}
</script>    
<!--<script src="/js/datetimepicker2.5.1/jquery.ui.datepicker-th.js" type="text/javascript"></script>-->
<script>
// ชื่อ >> value >> set selct auto (สคลิปตรวจสอบ Selected) จำเป็นต้องก็อปปี้ <script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js" type="text/javascript">
function auto_select(sid, value)
{
    $('#' + sid).val(value);
}

$(document).ready(function() {
<?php
    echo "auto_select('ch_cl_crimes_type', '{$row['ch_cl_crimes_type']}');\n";
    echo "auto_select('ch_cl_crimes_type2', '{$row['ch_cl_crimes_type2']}');\n";
    echo "auto_select('ch_cl_gang', '{$row['ch_cl_gang']}');\n";

?>
    setTimeout("do_region_change('<?= $row['provincial'] ?>')", 500);
    setTimeout("do_provincial_change('<?= $row['station'] ?>')", 1000);
});   
</script>
</body>
</html>