<script src="/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>
<script src="../SweetAlert2/dist/sweetalert2.all.min.js"></script>

<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);


$pcid = $_GET['pcid']; // เลขบัตรผู้พ้นโทษ ถูกส่งมาจากการ click

$people_name = '';
if($pcid != '') {
	$query1 = "SELECT * FROM wm_tb_personal WHERE ps_cl_idcard = :pcid ";
        $stmt = $pdo->prepare($query1);
        $stmt->bindParam(':pcid', $pcid);
        $stmt->execute();
        $row1 = $stmt->fetch(PDO::FETCH_ASSOC);
    }

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
<style>
    .swal2-popup {
    width: 500px !important;
    height: auto !important;
    font-size: 14px !important;
}
</style>
<title>ระบบ WatchmanDB</title>
</head>

<body>
	<div class="container-fluid">
		<div class=" h3 text-center  alert alert-success mb-2 mt-2 " role="alert" >ข้อมูลตรวจเยี่ยมบุคคลพ้นโทษ</div>
		<div> &nbsp;&nbsp;<a href="/Bansuan/index.php?rnd=<?= rand(); ?>&page=freed" class="btn btn-warning btn-lg mb-4" >ย้อนกลับ</a>   
        &nbsp;&nbsp;<a href="add_home_visit.php?pcid=<?= $pcid ?>" class="btn btn-success btn-lg mb-4 <?= $add_btn ?>" >เพิ่มข้อมูล </a>
        &nbsp;&nbsp;<button class="btn btn-primary btn-lg mb-4" onClick="do_print()"> พิมพ์ตารางตรวจเยี่ยม </button></div>
	<table align="center" class="table table-striped table-hover table-bordered" >
		<tr bgcolor=#FB0307 >
			<th><a style="font: bold; color: aliceblue"> ลำดับ</a></th>
		<!--	<th> เลขบัตร </th>   -->
			<th><a style="font: bold; color: aliceblue"> วันที่</a></th>
			<th><a style="font: bold; color: aliceblue"> ผู้ตรวจเยี่ยม</a></th>
			<th><a style="font: bold; color: aliceblue"> พบ/ไม่พบ</a></th>
			<th><a style="font: bold; color: aliceblue"> เหตุไม่พบตัว</a></th>
			<th><a style="font: bold; color: aliceblue"> ที่อยู่/ที่ทำงานใหม่</a></th>
			<th><a style="font: bold; color: aliceblue"> การดำเนินการ</a></th>
			<th><a style="font: bold; color: aliceblue"> หมายเหตุ</a></th>
			<th colspan="3"><a style="font: bold; color: aliceblue">  </th>
        </tr>  

<?php
try{
$sql = "SELECT * FROM wm_tb_homevisit";  //ตั้งตัวแปร sql เลือกฐานข้อมูลจากตารางที่ชื่อ wm_tb_personal
if($pcid != ''){
	$sql = $sql . " WHERE hv_cl_idcard = :pcid ORDER BY `hv_cl_datevisit` ASC";
        $stmt2 = $pdo->prepare($sql);
        $stmt2->bindParam(':pcid', $pcid);
        $stmt2->execute();
}

// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no = 1;
while($rowHv = $stmt2->fetch(PDO::FETCH_ASSOC))	//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
{
	//ฟังก์ชั่น วันที่ ดึงจาก condb
    $strDate = DateThai( $rowHv["hv_cl_datevisit"] );
    
?>

    <tr>
        <td> <?= $no ?> </td>
    <!--	<td> <?= $pcid ?> </td>  -->
        <td nowrap> <?= $strDate ?> </td>
        <td> <?=$rowHv["hv_cl_pol_visit"]?> </td>
        <td> <?=$rowHv["hv_cl_find_or_not"]?> </td>
        <td> <?=$rowHv["hv_cl_cause"]?> </td>
        <td> <?=$rowHv["hv_cl_new_place"]?> </td>
        <td> <?=$rowHv["hv_cl_action"]?> </td>
        <td> <?=$rowHv["hv_cl_remark"]?> </td>
		<td><a href="report_home_visit.php?id=<?= $rowHv["hv_cl_aid"]?>&pcid=<?=$pcid ?>" class="btn btn-primary mb-4" >รายงาน</a></td>
        <td><a href="Edit_home_visit.php?id=<?= $rowHv["hv_cl_aid"]?>&pcid=<?=$pcid ?>" class="btn btn-warning mb-4 <?= $edit_btn ?>" >แก้ไข</a></td>
        <td>&nbsp;<button href="#" class="btn btn-danger mb-4" onClick="deleteItem(<?= $rowHv["hv_cl_aid"] ?>, '<?= $pcid ?>')"<?=$del_btn?>>ลบ</button>&nbsp;</td>
    </tr>
<?php
	$no++;
	}
}catch(Exception $e){
    echo 'Query Failed: '. $e->getMessage();
    exit;
}

if($no == 1) {
	echo "<tr><td colspan='10' style='color: red'> ไม่พบข้อมูลการตรวจเยี่ยมบุคคลพ้นโทษ ของ <b style='color: blue'>$people_name</b> </td></tr>";
}
	?>
	
</table>
<p>&nbsp;</p>
</div>

<script src="/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>
<script src="../SweetAlert2/dist/sweetalert2.all.min.js"></script>
<script>
function deleteItem(id, pcid) {
    Swal.fire({
        title: 'คุณต้องการลบข้อมูลนี้ใช่ไหม?',
        text: "หลังจากลบแล้ว จะไม่สามารถกู้คืนได้",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'ใช่, ยืนยันลบ',
        cancelButtonText: 'ยกเลิก'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = '/Bansuan/Del_home_visit.php?id=' + id + '&pcid=' + pcid;
        }
    });
}
</script>

</body>
</html>
<!-- script สำหรับส่งไปพิมพ์รายงาน  -->
<script>
    
    function do_print()
    {
        var newW = window.open("/Bansuan/home_visit_print.php?pcid=<?= $pcid ?>", "_print", "menubar=no,titlebar=no,toolbar=no,status=no");
        newW.print();
        //window.print();
    }
    
</script>