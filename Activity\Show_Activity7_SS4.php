<?php
include '../Condb.php';
include '../users.inc.php';
include '../right_user.php';
include '../Head_image.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// เมื่อมีการใช้ ตัวแปร Session จะต้องเปิดก่อน
//session_start();  // เปิดตัวแปร session
/*if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
*/

//get month from select or current month
$month = isset($_GET['month']) ? $_GET['month'] : date("m");
$year = isset($_GET['year']) ? $_GET['year'] : date("Y");
//$station = isset($_GET['station']) ? $_GET['station'] : 6707;
//$station = 6707;   // สภ.บ้านสวน

//--------------------
// last modify 2023-01-20
$limite_date = strtotime("$year-09-30"); // วันสุดท้ายของช่วง เป็น 30 ก.ย.ของปีนั้น ๆ
$current_date = time(); // now()   กำหนดวันปัจจุบัน
$check_date = strtotime("$year-10-01");   // ตัวแปร $check_date เป็นวันตั้งแต่ 1 ต.ค.ของปีนั้น ๆ เป็นต้นไป
$select_date = strtotime("$year-$month-01"); // เป็นตัวแปร สำหรับเลือก ของเดือนนั้น ๆ

//ถ้าเวลายังไม่สิ้นสุด 30 กย. ของปี
if($current_date < $check_date)   // ถ้าวันปัจจุบัน มีค่าน้อยกว่า วันที่ 1 ต.ค.
{
    $limite_date = strtotime(($year-1) . "-09-30");  // ตัวแปร $limite_date จะมีค่าเท่ากับ 30 ก.ย.ของปี ที่ลบค่าไป 1
    $check_date = strtotime(($year-1) . "-10-01");   // ตัวแปร $check_date จะมีค่าเท่ากับ 1 ต.ค. ของปี ที่ลบค่าไป 1
}


$m1 = '';
$m2 = '';
$m3 = '';
$m4 = '';
$m5 = '';
$m6 = '';
$m7 = '';
$m8 = '';
$m9 = '';
$m10 = '';
$m11 = '';
$m12 = '';
switch($month)
{
    case "01": $m1 = "selected"; break;
    case "02": $m2 = "selected"; break;
    case "03": $m3 = "selected"; break;
    case "04": $m4 = "selected"; break;
    case "05": $m5 = "selected"; break;
    case "06": $m6 = "selected"; break;
    case "07": $m7 = "selected"; break;
    case "08": $m8 = "selected"; break;
    case "09": $m9 = "selected"; break;
    case "10": $m10 = "selected"; break;
    case "11": $m11 = "selected"; break;
    case "12": $m12 = "selected"; break;
}
$current_month = $global_thai_month[0+$month];
$y2 = $year+543;

// แปลงเวลา เป็นปีไทย
function date_2_Thai( $date )
{
    global $global_thaimonth;
    $strYear = date("Y", $date) + 543;
    $strMonth = date("n",$date) - 1;
    $strDay = date("j", $date);
    $strMonthThai = $global_thaimonth[$strMonth];
    return "$strDay $strMonthThai $strYear";
}

//สร้างฟังก์ชั่น ย้อนหลัง arrest_summary_count มาใช้งาน เป็นยอดรวมการจับกุม ย้อนหลัง 1 เดือน
// dateM - datetime var
function arrest_summary_count($date)
{
    global $pdo, $station;

    $m1 = date("Y-m-1", $date);
    $m2 = date("Y-m-t", $date);

    $sql_s = "SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station` = :station AND `wr_cl_status` IN ('2', '5', '6', '7')";

    $stmt = $pdo->prepare($sql_s);
    $stmt->bindParam(':m1', $m1);
    $stmt->bindParam(':m2', $m2);
    $stmt->bindParam(':station', $station);
    $stmt->execute();

    $row_s = $stmt->fetch(PDO::FETCH_NUM);
    return $row_s[0];
}

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<base target="_top">
<title>แบบ สส.4</title>
<link rel="stylesheet" href="/bootstrap/css/bootstrap-grid.css">
<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
</head>

<body>
<!--<p><img src="../Image/WatchmanDB3.jpg" width="100%" height="" alt=""/></p>-->
<div class=" h3 text-center  alert alert-success mb-4 mt-4 " role="alert" >แบบ สส.4 บัญชีหมายจับภาพรวม รายปี ตั้งแต่ 11 ต.ค.2545 - ปัจจุบัน ( <?= $y2 ?> )&nbsp; <?= $name_station ?> </div>
    
&nbsp;&nbsp;&nbsp;&nbsp;<a href="/WM/index.php?rnd=<?= rand(); ?>&page=report" class="btn btn-warning btn-lg mb-4" >ย้อนกลับ</a>
    &nbsp;&nbsp;<button class="btn btn-primary btn-lg mb-4" onClick="do_print()"> พิมพ์รายงาน </button> 
	<!--<button class="btn btn-primary btn-lg mb-4" onClick="if ($station === 6707) { do_print(); }">พิมพ์รายงาน</button>-->
    <br>
    
<div class="container" align="left" style="margin-left:auto">
<table width="99%" border="1" cellspacing="1" cellpadding="1" class="table-striped table-hover table-bordered">
  <tbody>
    <tr>
      <td rowspan="2" style="background: #42FF00">&nbsp;ลำดับ&nbsp;</td>
      <td rowspan="2" nowrap style="background: #42FF00; text-align: center">&nbsp;ปี พ.ศ.&nbsp;</td>
      <td rowspan="2" nowrap style="background: #42FF00; text-align: center">&nbsp;จำนวนหมายจับ<br>(ทั้งหมดที่ออก)</td>
      <td colspan="2" nowrap style="background: #42FF00; text-align: center">&nbsp;คงเหลือ&nbsp;</td>
      <td rowspan="2" nowrap style="background: #42FF00; text-align: center">&nbsp;มีคุณภาพ<br>(ทั้งหมดที่ออก)</td>
      <td rowspan="2" style="background: #42FF00; text-align: center">&nbsp;ไม่มีคุณภาพ<br>(ทั้งหมดที่ออก)&nbsp;</td>
      <td rowspan="2" nowrap style="background: #42FF00; text-align: center">&nbsp;ไม่มีหมายจับ&nbsp;</td>
    </tr>
    <tr>
      <td nowrap style="background: #42FF00">มีคุณภาพ</td>
      <td nowrap style="background: #42FF00">ไม่มีคุณภาพ</td>
      </tr>
<?php
  //$date1 = date("Y", $limite_date) . "-$month-01";        //"$year-$month-01";
$day1 = date("t", $limite_date);
$date2 = "$year-$month-$day1"; // <<<
                      
// ยอดหมายจับเก่า ที่ออกระหว่าง ปี 45 - 30 ก.ย.2565 ทั้งหมด
$date_prev = date("Y-m-d", $limite_date);
$date_next = date("Y-m-01", $check_date); // ตั้งแต่ 1 ตค เปนต้นไป
    $m1 = date("Y-m-01", $select_date);       // m1 = เดือนปัจจุบันที่เลือก
    $m2 = date("Y-m-t", $select_date);        // << end date of current month ได้ค่าวันสุดท้ายของเดือนปัจจุบันที่เลือกมา
      
      
$sql = "SELECT 
            CONCAT(YEAR(`wr_cl_date`)+543) AS year, 
            COUNT(`wr_cl_date`) AS count,
            SUM(`wr_cl_qty`=2 AND wr_cl_status='1') AS NoQty,
            SUM(`wr_cl_qty`=1 AND wr_cl_status IN ('1', '3')) AS BL, 
            SUM(`wr_cl_qty`=1) AS QTY, 
            SUM(`wr_cl_qty`=2) AS NQTY
        FROM 
            wm_tb_warrant 
        WHERE 
            `station`='$station' 
        GROUP BY 
            YEAR(`wr_cl_date`)";
      
    $stmt = $pdo->prepare($sql);
try{
    // Execute the prepared statement
    $stmt->execute();
}catch (PDOException $e) {
    echo '$sql Failed: '. $e->getMessage();
}
    // Fetch the results if needed
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);

$no = 1;
      
      // ประกาศตัวแปร เพื่อคำนวนยอดรวมของแต่ละคอลัมน์
      $total_wanted = 0;
      $total_quality = 0;
      $total_NoQty = 0;
      $total_QTY = 0;
      $total_NQTY = 0;
?>
      
<?php foreach ($results as $row): ?>
      
    <tr>
      <td>&nbsp; <?= $no ?> &nbsp;</td>
      <td nowrap>&nbsp;<?php echo $row['year']; ?>&nbsp;</td>
      <td nowrap>&nbsp;<?php echo (($row['count'] > 0) ? $row['count']  : "-") ?>&nbsp;</td>
      <td nowrap style="background: #FBFD95">&nbsp;<?php echo (($row['BL'] > 0) ? $row['BL']  : "-") ?>&nbsp;</td>
      <td style="background: #FBFD95">&nbsp;<?php echo (($row['NoQty'] > 0) ? $row['NoQty']  : "-") ?>&nbsp;</td>
      <td>&nbsp;<?php echo (($row['QTY'] > 0) ? $row['QTY']  : "-") ?>&nbsp;</td>
      <td>&nbsp;<?php echo (($row['NQTY'] > 0) ? $row['NQTY']  : "-") ?>&nbsp;</td>
      <td nowrap>&nbsp;</td>
    </tr>
<?php
    // ยอดรว  ของข้อมูลแต่ละคอลัมน์
  $total_wanted += $row['count'];
  $total_quality += $row['BL'];
  $total_NoQty += $row['NoQty'];
  $total_QTY += $row['QTY'];
  $total_NQTY += $row['NQTY'];

$no++;
?>

<?php endforeach; ?>
    <tr>
      <td colspan="2" style="background-color: lawngreen">&nbsp;&nbsp;&nbsp;&nbsp;ยอดรวม</td>
      <td style="background-color: lawngreen">&nbsp;<?= number_format($total_wanted, 0, '.', ','); ?>&nbsp;</td>
      <td style="background-color: lawngreen">&nbsp;<?= number_format($total_quality, 0, '.', ','); ?>&nbsp;</td>
      <td style="background-color: lawngreen">&nbsp;<?= number_format($total_NoQty, 0, '.', ','); ?>&nbsp;</td>
      <td style="background-color: lawngreen">&nbsp;<?= number_format($total_QTY, 0, '.' , ','); ?>&nbsp;</td>
      <td style="background-color: lawngreen">&nbsp;<?= number_format($total_NQTY, 0, '.', ','); ?>&nbsp;</td>
      <td style="background-color: lawngreen">&nbsp;&nbsp;</td>
        
    </tr>
  </tbody>
</table>
</div>
<br>
<br>
<br>
</body>
</html>
<!-- script สำหรับส่งไปพิมพ์รายงาน  -->
<script>
    
    function do_print()
    {
        var newW = window.open("/Activity/Show_Activity7_SS4_print.php", "_print", "menubar=no,titlebar=no,toolbar=no,status=no");
        newW.print();
        //window.print();
    }
    
</script>