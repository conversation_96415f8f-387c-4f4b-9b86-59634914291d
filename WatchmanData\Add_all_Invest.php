<?php
include "../Condb.php"; //PDO
include '../users.inc.php';
include '../right_user.php';


// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

$pcid = $_GET['pcid'];
$people_name = '';

if ($pcid != '') {
    $query1 = "SELECT * FROM wm_tb_personal WHERE ps_cl_idcard = :pcid";
    $stmt = $pdo->prepare($query1);
    $stmt->bindParam(':pcid', $pcid);
    $stmt->execute();
    $row1 = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $people_name = $row1['ps_cl_prefix'] . ' ' . $row1['ps_cl_name'] . ' ' . $row1['ps_cl_surname'];
    $people_image = $row1["ps_cl_image"];
    
    if ($people_image != '') {
        $people_image = "<img src='{$people_image}' height='80'> ";
    }
}
?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link rel="icon" type="image/x-icon" href="/favicon.ico">
<title>เพิ่มข้อมูลการสืบสวนจากโทรศัพท์</title>
<link rel="stylesheet" href="/policeinnopolis/bootstrap/css/bootstrap.rtl.min.css">
<!-- สำหรับ วันเกิด แบบ พ.ศ. -->
<link rel="stylesheet" href="http://code.jquery.com/ui/1.9.1/themes/base/jquery-ui.css" />
<script src="http://code.jquery.com/jquery-1.8.2.js"></script>
<script src="/policeinnopolis/js/jquery-ui.js"></script>
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 18px;
    }
input::placeholder {
  color: red;
}
.form-control::placeholder {
  color: sandybrown;
}
</style>
</head>

<body>
	<div class="container">
	<div class="row">
		<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> เพิ่มข้อมูลการสืบสวนโทรศัพท์ ของ <?= $people_image ?> <?= $people_name ?> <?= $pcid ?> </div>
	<form action="Save_all_Invest.php" method="POST" enctype="multipart/form-data" class="body">
        
        <input type="hidden" name="iv_cl_aid" id="iv_cl_aid" value="0">
        
		<label>เลขบัตรประชาชน</label>
		<input type = "text" name="iv_cl_idcard" class="form-control" placeholder="กรอกเลขบัตรประชาชน 13 หลัก"  value="<?= $pcid ?>"  readonly="readonly" >
		<label>หมายเลขโทรศัพท์ <span style="color: #F90004">* จำเป็น</span></label>
		<input type = "text" name = "iv_cl_phone" class="form-control" placeholder="ระบุหมายเลขโทรศัพท์ 10 หลัก เชื่อมกับข้อมูลสืบสวน"   Required  >
		<label>ข้อมูล วัคซีน</label>
		<input type = "text" name = "iv_cl_vaccine" class="form-control" placeholder="เบอร์ที่ผูกกับวัคซีน"  >
		<label>ข้อมูล Get Contact</label>
		<input type = "text" name = "iv_cl_get" class="form-control" placeholder="ข้อมูลจาก Get Contact"  >
		<label>ข้อมูล Promt pay</label>
		<input type = "text" name = "iv_cl_promt" class="form-control" placeholder="ข้อมูลจาก Promt Pay"  >
		<label>ข้อมูล All Member</label>
		<input type = "text" name = "iv_cl_allmember" class="form-control" placeholder="ข้อมูลจาก All Member"  >
		<label>ข้อมูล True Money Wallet</label>
		<input type = "text" name = "iv_cl_truemoney" class="form-control" placeholder="ข้อมูลจาก True Money Wallet"  >
		<label>ข้อมูล Line</label>
		<input type = "text" name = "iv_cl_line" class="form-control" placeholder="ข้อมูลจาก Line"  >
		<label>ข้อมูล พัสดุ</label>
		<input type = "text" name = "iv_cl_logistic" class="form-control" placeholder="ข้อมูลรับส่งพัสดุ"  >
		<label>ข้อมูล สั่งสินค้าออนไลน์</label>
		<input type = "text" name = "iv_cl_shopping" class="form-control" placeholder="ข้อมูลการสั่งสินค้าและอาหาร ออนไลน์"  >

        <label>วันตรวจสอบ <span style="color: #F90004">* จำเป็น</span> </label>
         <p><input type="text" id="datepicker" name="iv_cl_inspect" placeholder="เพื่อข้อมูลถูกต้อง กรุณาเลือกจากปฎิทินด้านล่าง" class="form-control" autocomplete="off" Required ></p>
        <script>
            function set_cal(ele)//function สร้างตัวเลือกปฎิทิน
            {
              $( ele ).datepicker({
                  onSelect:(date_text)=>
                  {
                    let arr=date_text.split("/");
                    let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])+543).toString();
                    $(ele).val(new_date);
                    $(ele).css("color","");
                  },
                  beforeShow:()=>{

                    if($(ele).val()!="")
                    {
                      let arr=$(ele).val().split("/");
                      let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])-543).toString();
                      $(ele).val(new_date);

                    }

                    $(ele).css("color","white");
                  },
                  onClose:()=>{

                      $(ele).css("color","");

                      if($(ele).val()!="")
                      {
                          let arr=$(ele).val().split("-");
                          if(parseInt(arr[2])<2500)
                          {
                              let new_date=arr[0]+"-"+arr[1]+"-"+(parseInt(arr[2])).toString();
                              $(ele).val(new_date);
                          }
                      }
                  },
                  dateFormat:"yy-mm-dd", //กำหนดรูปแบบวันที่เป็น วัน/เดือน/ปี
                  changeMonth:true,//กำหนดให้เลือกเดือนได้
                  changeYear:true,//กำหนดให้เลือกปีได้
                  showOtherMonths:true,//กำหนดให้แสดงวันของเดือนก่อนหน้าได้
              });
            }
          $(document).ready(function(){
            //เรียก function set_cal เมื่อเปิดหน้าเว็บ โดยส่ง object element ที่มี id เป็น datepicker เป็นพารามิเตอร์
            set_cal( $("#datepicker") );
          })
          </script>
        <!-- สำหรับ วันที่ แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ -->
        
		<label>หมายเหตุ</label>
		<input type = "text" name = "iv_cl_remark" class="form-control" placeholder="หมายเหตุ"  >
		<br>
			<p>
			<input type="submit" value="บันทึกข้อมูล" class="btn btn-success" >
			<td> <a href="Show_All.php?pcid=<?= $pcid ?>&page=invest" class="btn btn-warning" >ยกเลิก</a> </td>
		</p>
		<br>
		<br>
	</form>
	</div>
	</div>
	</div>
</body>
</html>