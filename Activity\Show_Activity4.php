<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';
include '../Head_image.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

//get month from select or current month
$month = isset($_GET['month']) ? $_GET['month'] : date("m");
$year = isset($_GET['year']) ? $_GET['year'] : date("Y");

$m1 = '';
$m2 = '';
$m3 = '';
$m4 = '';
$m5 = '';
$m6 = '';
$m7 = '';
$m8 = '';
$m9 = '';
$m10 = '';
$m11 = '';
$m12 = '';
switch($month)
{
    case "01": $m1 = "selected"; break;
    case "02": $m2 = "selected"; break;
    case "03": $m3 = "selected"; break;
    case "04": $m4 = "selected"; break;
    case "05": $m5 = "selected"; break;
    case "06": $m6 = "selected"; break;
    case "07": $m7 = "selected"; break;
    case "08": $m8 = "selected"; break;
    case "09": $m9 = "selected"; break;
    case "10": $m10 = "selected"; break;
    case "11": $m11 = "selected"; break;
    case "12": $m12 = "selected"; break;
}
$current_month = $global_thai_month[0+$month];
$y2 = $year+543;

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<base target="_top">
<title>รายงานการขับเคลื่อนงานสืบสวน</title>
<link rel="stylesheet" href="/bootstrap/css/bootstrap-grid.css">
<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    
<script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.1.1/chart.min.js" integrity="sha512-MC1YbhseV2uYKljGJb7icPOjzF2k6mihfApPyPhEAo3NsLUW0bpgtL4xYWK1B+1OuSrUkfOTfhxrRKCz/Jp3rQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>

<style type="text/css">
body{
    font-size:12px; 
}
.textAlignVer{
    display:block;
    filter: flipv fliph;
    -webkit-transform: rotate(-90deg); 
    -moz-transform: rotate(-90deg); 
    transform: rotate(-90deg); 
    position:relative;
    width:20px;
    white-space:nowrap;
    font-size:16px;
    margin-bottom:10px;
}
</style>
    
</head>
<!--<p><img src="../Image/Head2.jpg" width="100%" height="" alt=""/></p>-->
<div class="container-fluid" align="left">
<div class=" h3 text-center  alert alert-success mb-4 mt-4 " role="alert" >กิจกรรม 4 การสืบสวนจับกุมคดีอุกฉกรรจ์ย้อนหลังและที่เกิดขึ้นใหม่</div>
    
<body>
<div align="left">
<div>
    <table width="80%" border="0" cellspacing="1" cellpadding="1">
      <tbody>
        <tr>
          <td width="11%">&nbsp;<a href="/WM/index.php?rnd=<?= rand(); ?>&page=report" class="btn btn-warning btn-lg mb-4" >ย้อนกลับ</a></td>
          
          <td width="9%"><label>เลือกปี </label>
            <select id="mi_cl_year" name="mi_cl_year" class="form-select col-8 d-lg-inline" style="background-color: khaki; font-size: 16px" onChange="dates_change()">
              <option value="">เลือก</option>
              <?php
                $cur_y = date("Y") + 2;
                for($y=2020; $y<$cur_y; $y++) {
                    $sel = ($y == $year) ? "selected" : '';
                    echo "<option value='$y' $sel> ". ($y + 543) ." </option>";
                }
            ?>
          </select></td>
          <td width="1%"></td>
          <td width="12%">&nbsp;&nbsp;<label>เดือน</label>
            <select id="mi_cl_month" name="mi_cl_month" class="form-select col-3 d-lg-inline" style="background-color: khaki; font-size: 16px" onChange="dates_change()">
              <option value="">เลือก</option>
              <option <?= $m1 ?> value="01">มกราคม</option>
              <option <?= $m2 ?> value="02">กุมภาพันธ์</option>
              <option <?= $m3 ?> value="03">มีนาคม</option>
              <option <?= $m4 ?> value="04">เมษายน</option>
              <option <?= $m5 ?> value="05">พฤษภาคม</option>
              <option <?= $m6 ?> value="06">มิถุนายน</option>
              <option <?= $m7 ?> value="07">กรกฎาคม</option>
              <option <?= $m8 ?> value="08">สิงหาคม</option>
              <option <?= $m9 ?> value="09">กันยายน</option>
              <option <?= $m10 ?> value="10">ตุลาคม</option>
              <option <?= $m11 ?> value="11">พฤศจิกายน</option>
              <option <?= $m12 ?> value="12">ธันวาคม</option>
          </select></td>
          <td width="61%">&nbsp;</td>
          <td width="6%">&nbsp;</td>
        </tr>
      </tbody>
    </table>
</div><br>
<!-------------------------------------------------------->   
<!--------------------------------------------------------> 
<!--------------------------------------------------------> 
<h3 align="center">สถิติการจับกุมคดีอุกฉกรรจ์เกิดขึ้นใหม่ <?= $name_station ?> </h3>
<h3 align="center">ประจำเดือน&nbsp; <?= $current_month ?> &nbsp;<?= $y2 ?> &nbsp;</h3><br>
<table border="1" cellspacing="1" cellpadding="1" class="table table-bordered">
  <tbody>
    <td width="4%"></tr>
    <tr class=xl8920918 height=33 style='height:24.6pt'>
      <td rowspan="2" class=xl8720918 style='height:24.6pt; background: Yellow; border: solid black '>ลำดับ</td>
      <td height="33" colspan="6" class=xl8720918 style='border-left:none; background: yellow; border: solid black '>คดีคงเหลือที่ยังจับกุมไม่ได้<br>จากการปะชุมที่ผ่านมา</td>
      <td colspan="6" class=xl8720918 style='border-left:none; background: yellow; border: solid black '>คดีอุกฉกรรจ์เกิดใหม่<br>เดือน &nbsp; <?= $current_month ?> &nbsp;<?= $y2 ?> &nbsp;</td>
      <td colspan="6" class=xl8720918 style='border-left:none; background: yellow; border: solid black '>คดีอุกฉกรรจ์ค้างเก่า จากการประชุมที่แล้ว + <br>เกิดใหม่เดือน&nbsp; <?= $current_month ?> &nbsp;<?= $y2 ?> &nbsp;</td>
      <td colspan="4" class=xl8720918 style='border-left:none; background: yellow; border: solid black '>คดีอุฉกรรจ์คงเหลือ</td>
      <td width="6%" rowspan="2" class=xl8720918 style='border-left:none; background: yellow; border: solid black '>หมายเหตุ</td>
    </tr>
    <tr class=xl8920918 height=33 style='height:24.6pt'>
  <td width="3%" height="33" class=xl8720918 style='border-left:none; background: yellow; border: solid black '>ทั้งหมด (คดี)</td>
  <td width="6%" class=xl8720918 style='border-left:none; background: yellow; border: solid black '>รู้ตัว<br>
    (คดี)</td>
  <td width="4%" class=xl8720918 style='border-left:none; background: yellow; border: solid black '>หมายจับ (หมาย)</td>
  <td width="4%" class=xl8720918 style='border-left:none; background: yellow; border: solid black '>ไม่รู้ตัว<br>
    (คดี)</td>
  <td width="7%" class=xl8720918 style='border-left:none; background: yellow; border: solid black '>จำหน่าย (คดี)</td>
  <td width="8%" class=xl8720918 style='border-left:none; background: yellow; border: solid black '>จำหน่าย(หมายจับ)</td>
  <td width="17%" height="33" class=xl8720918 style='border-left:none; background: yellow; border: solid black '>ทั้งหมด (คดี)</td>
  <td width="17%" class=xl8720918 style='border-left:none; background: yellow; border: solid black '>รู้ตัว<br>
    (คดี)</td>
  <td width="17%" class=xl8720918 style='border-left:none; background: yellow; border: solid black '>หมายจับ (หมาย)</td>
  <td width="17%" class=xl8720918 style='border-left:none; background: yellow; border: solid black '>ไม่รู้ตัว<br>
    (คดี)</td>
  <td width="17%" class=xl8720918 style='border-left:none; background: yellow; border: solid black '>จำหน่าย (คดี)</td>
  <td width="11%" class=xl8720918 style='border-left:none; background: yellow; border: solid black '>จำหน่าย(หมายจับ)</td>
  <td width="10%" height="33" class=xl8720918 style='border-left:none; background: yellow; border: solid black '>ทั้งหมด (คดี)</td>
  <td width="10%" class=xl8720918 style='border-left:none; background: yellow; border: solid black '>รู้ตัว<br>
    (คดี)</td>
  <td width="10%" class=xl8720918 style='border-left:none; background: yellow; border: solid black '>หมายจับ (หมาย)</td>
  <td width="10%" class=xl8720918 style='border-left:none; background: yellow; border: solid black '>ไม่รู้ตัว<br>
    (คดี)</td>
  <td width="10%" class=xl8720918 style='border-left:none; background: yellow; border: solid black '>จำหน่าย (คดี)</td>
  <td width="10%" class=xl8720918 style='border-left:none; background: yellow; border: solid black '>จำหน่าย(หมายจับ)</td>
  <td width="10%" height="33" class=xl8720918 style='border-left:none; background: yellow; border: solid black '>ทั้งหมด (คดี)</td>
  <td width="10%" class=xl8720918 style='border-left:none; background: yellow; border: solid black '>รู้ตัว<br>
    (คดี)</td>
  <td width="10%" class=xl8720918 style='border-left:none; background: yellow; border: solid black '>หมายจับ (หมาย)</td>
  <td width="10%" class=xl8720918 style='border-left:none; background: yellow; border: solid black '>ไม่รู้ตัว<br>
    (คดี)</td>
  </tr>
    
<?php
                          // ยังทำข้อมูลย้อนหลัง เป็นรายเดือนไม่สำเร็จ
//$m_now = "SELECT COUNT(*) FROM wm_tb_investigating_case WHERE (`ivc_cl_date` BETWEEN '$m1' AND '$m2') ";
//$m_1 = "SELECT COUNT(*) FROM wm_tb_investigating_case WHERE (`ivc_cl_date` <= '$m1' AND `ivc_cl_date` > $now ) ";
 
    
//                      echo $m_now;
//                      echo $m_1;
?>
    
<tr height=33 style='height:24.6pt'>
  <td height=33 class=xl8820918 style='height:24.6pt;border-left: solid black'>&nbsp; &nbsp;</td>
  <td class=xl8820918 style='border-top:none;border-left:none'>&nbsp;&nbsp;</td>
  <td class=xl8820918 style='border-top:none;border-left:none'>&nbsp;&nbsp;</td>
  <td class=xl8820918 style='border-top:none;border-left:none'>&nbsp;&nbsp;</td>
  <td class=xl8820918 style='border-top:none;border-left:none'>&nbsp;&nbsp;</td> 
  <!-- ivc_cl_case -->
  <td class=xl8820918 style='border-top:none;border-left:none'>&nbsp;&nbsp;</td> <!-- ivc_cl_date -->
  <td class=xl8820918 style='border-top:none;border-left:none'>&nbsp;&nbsp;</td>
  <td class=xl8820918 style='border-top:none;border-left:none'>&nbsp;&nbsp;</td>
  <td class=xl8820918 style='border-top:none;border-left:none'>&nbsp;&nbsp;</td>
  <td class=xl8820918 style='border-top:none;border-left:none'>&nbsp;&nbsp;</td>
  <td class=xl8820918 style='border-top:none;border-left:none'>&nbsp;&nbsp;</td> 
  <!-- ivc_cl_crimes_no -->
  <td class=xl8820918 style='border-top:none;border-left:none'>&nbsp;&nbsp;</td> <!-- ivc_cl_name -->
  <td class=xl8820918 style='border-top:none;border-left:none'>&nbsp;&nbsp;</td>
  <td class=xl8820918 style='border-top:none;border-left:none'>&nbsp;&nbsp;</td>
  <td class=xl8820918 style='border-top:none;border-left:none'>&nbsp;&nbsp;</td>
  <td class=xl8820918 style='border-top:none;border-left:none'>&nbsp;&nbsp;</td>
  <td class=xl8820918 style='border-top:none;border-left:none'>&nbsp;&nbsp;</td> 
  <!-- ivc_cl_detail -->
  <td class=xl8820918 style='border-top:none;border-left:none'>&nbsp;&nbsp;</td> <!-- ivc_cl_inquiry_official -->
  <td class=xl8820918 style='border-top:none;border-left:none'>&nbsp;&nbsp;</td>
  <td class=xl8820918 style='border-top:none;border-left:none'>&nbsp;&nbsp;</td>
  <td class=xl8820918 style='border-top:none;border-left:none'>&nbsp;&nbsp;</td>
  <td class=xl8820918 style='border-top:none;border-left:none'>&nbsp;&nbsp;</td> 
  <!-- ivc_cl_detective -->
  <td class=xl8820918 style='border-top:none;border-left:none'>&nbsp;&nbsp;</td> <!-- ivc_cl_catch -->
  <td class=xl8820918 style='border-top:none;border-right: solid black'>&nbsp;&nbsp;</td> <!-- ivc_cl_remark -->
 </tr>
<?php
//    $no3++;
//}
?>
  </tbody>
</table>
    <br>
    <hr>
<!-------------------------------------------------------->   
<!--------------------------------------------------------> 
<!--------------------------------------------------------> 
<?php
    
// แปลงเวลา เปนปีไทย
function date_2_Thai( $date )
{
    global $global_thaimonth;
    $strYear = date("Y", $date) + 543;
    $strMonth = date("n",$date) - 1;
    //$strDay = date("j", $date);
    $strMonthThai = $global_thaimonth[$strMonth];
    return "$strMonthThai $strYear";
}

                      
$select_date = new DateTime("$year-$month-01");
$select_date->modify('-1 month');
$prev1 = $select_date->format("Y-m-01");
$select_date->modify('-1 month');
$prev2 = $select_date->format("Y-m-01");
$select_date->modify('-1 month');
$prev3 = $select_date->format("Y-m-01");
    
?>
<h3 align="center">ข้อมูลคดีอุกฉกรรจ์เกิดขึ้นใหม่ <?= $name_station ?></h3>
<h3 align="center">ประจำเดือน&nbsp; <?= $current_month ?> &nbsp;<?= $y2 ?> &nbsp;</h3><br>
<table width="90%" border="1" cellspacing="1" cellpadding="1" class="table table-bordered">
  <tbody>
    </tr>
 <tr class=xl8920918 height=33 style='height:24.6pt'>
  <td height=33 class=xl8720918 style='height:24.6pt; background: #F07072; border: solid black '>ลำดับ</td>
  <td class=xl8720918 style='border-left:none; background: #F07072; border: solid black '>เหตุ/ข้อหา</td>
  <td class=xl8720918 style='border-left:none; background: #F07072; border: solid black '>วันที่เกิดเหตุ</td>
  <td class=xl8720918 style='border-left:none; background: #F07072; border: solid black '>เลขคดีอาญา</td>
  <td class=xl8720918 style='border-left:none; background: #F07072; border: solid black '>ชื่อผู้ต้องหา</td>
  <td class=xl8720918 style='border-left:none; background: #F07072; border: solid black '>ข้อมูลคดี (โดยย่อ)</td>
  <td class=xl8720918 style='border-left:none; background: #F07072; border: solid black '>พนักงานสอบสวน</td>
  <td class=xl8720918 style='border-left:none; background: #F07072; border: solid black '>เจ้าหน้าที่สืบสวน</td>
  <td class=xl8720918 style='border-left:none; background: #F07072; border: solid black '>สถานะการจับกุม</td>
  <td class=xl8720918 style='border-left:none; background: #F07072; border: solid black '>หมายเหตุ</td>
 </tr>
    
<?php
$sql = "SELECT * FROM wm_tb_investigating_case WHERE MONTH(ivc_cl_date)='$month' AND YEAR(ivc_cl_date)='$year' AND ivc_cl_type='คดีอุกฉกรรจ์' ";
$res = mysqli_query($conn, $sql);

$no = 1;
while($row = mysqli_fetch_array($res))	//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
{
                      
?>
    
 <tr height=33 style='height:24.6pt'>
  <td height=33 class=xl8820918 style='height:24.6pt;border-left: solid black'>&nbsp;<?= $no ?>&nbsp;</td>
  <td class=xl8820918 style='border-top:none;border-left:none'>&nbsp;<?= $row["ivc_cl_case"] ?>&nbsp;</td> <!-- ivc_cl_case -->
  <td class=xl8820918 style='border-top:none;border-left:none'>&nbsp;<?= $row["ivc_cl_date"] ?>&nbsp;</td> <!-- ivc_cl_date -->
  <td class=xl8820918 style='border-top:none;border-left:none'>&nbsp;<?= $row["ivc_cl_crimes_no"] ?>&nbsp;</td> <!-- ivc_cl_crimes_no -->
  <td class=xl8820918 style='border-top:none;border-left:none'>&nbsp;<?= $row["ivc_cl_name"] ?>&nbsp;</td> <!-- ivc_cl_name -->
  <td class=xl8820918 style='border-top:none;border-left:none'>&nbsp;<?= $row["ivc_cl_detail"] ?>&nbsp;</td> <!-- ivc_cl_detail -->
  <td class=xl8820918 style='border-top:none;border-left:none'>&nbsp;<?= $row["ivc_cl_inquiry_official"] ?>&nbsp;</td> <!-- ivc_cl_inquiry_official -->
  <td class=xl8820918 style='border-top:none;border-left:none'>&nbsp;<?= $row["ivc_cl_detective"] ?>&nbsp;</td> <!-- ivc_cl_detective -->
  <td class=xl8820918 style='border-top:none;border-left:none'>&nbsp;<?= $row["ivc_cl_catch"] ?>&nbsp;</td> <!-- ivc_cl_catch -->
  <td class=xl8820918 style='border-top:none;border-right: solid black'>&nbsp;<?= $row["ivc_cl_remark"] ?>&nbsp;</td> <!-- ivc_cl_remark -->
 </tr>
<?php
    $no++;
}
?>
  </tbody>
</table>


<!-------------------------------------------------------->   
<!--------------------------------------------------------> 
<!--------------------------------------------------------> 
<br>
<hr>

<h3 align="center">ข้อมูลคดีอุกฉกรรจ์ย้อนหลัง 3 เดือน <?= $name_station ?></h3><br>
    <table width="90%" border="1" cellspacing="1" cellpadding="1" class="table table-bordered">
      <tbody>
        </tr>
     <tr class=xl8920918 height=33 style='height:24.6pt'>
      <td height=33 class=xl8720918 style='height:24.6pt; background: #F07072; border: solid black'>ลำดับ</td>
      <td class=xl8720918 style='border-left:none; background: #F07072; border: solid black '>เดือน</td>
      <td class=xl8720918 style='border-left:none; background: #F07072; border: solid black '>จำนวนเหตุเกิด</td>
      <td class=xl8720918 style='border-left:none; background: #F07072; border: solid black '>จำนวนจับกุม</td>
      <td class=xl8720918 style='border-left:none; background: #F07072; border: solid black '>คงเหลือ</td>
     </tr>
    
<?php
//ย้อนหลัง เดือน 1
    $d1 = date("Y-m-01", strtotime($prev1));
    $d2 = date("Y-m-t", strtotime($prev1));
    
$sql1 = "SELECT " .
        "(SELECT COUNT(*) FROM wm_tb_investigating_case WHERE station='$station' AND ivc_cl_type='คดีอุกฉกรรจ์' AND (ivc_cl_date BETWEEN '$d1' AND '$d2')) AS M1, ".
        "(SELECT COUNT(*) FROM wm_tb_investigating_case WHERE station='$station' AND ivc_cl_type='คดีอุกฉกรรจ์' AND ivc_cl_catch='จับได้' AND (ivc_cl_date BETWEEN '$d1' AND '$d2')) AS M1_c ".
        "";
$res1 = mysqli_query($conn, $sql1);

$no1 = 1;
while($row1 = mysqli_fetch_array($res1))	//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
{
$sum_m1 = $row1["M1"] - $row1["M1_c"];
?>
    
     <tr height=33 style='height:24.6pt'>
      <td height=33 class=xl8820918 style='height:24.6pt;border-left: solid black'>&nbsp;1&nbsp;</td>
      <td class=xl8820918 style='border-top:none;border-left:none'>&nbsp;<?= date_2_Thai(strtotime($prev1)) ?>&nbsp;</td>
      <td class=xl8820918 style='border-top:none;border-left:none'>&nbsp;<?= $row1["M1"] ?>&nbsp;</td>
      <td class=xl8820918 style='border-top:none;border-left:none'>&nbsp;<?= $row1["M1_c"] ?>&nbsp;</td>
      <td class=xl8820918 style='border-top:none;border-right: solid black'>&nbsp;<?= $sum_m1 ?>&nbsp;</td>
     </tr>
<?php
    $no1++;
}
?>

<?php
//ย้อนหลัง เดือน 2
    $d3 = date("Y-m-01", strtotime($prev2));
    $d4 = date("Y-m-t", strtotime($prev2));
    
$sql2 = "SELECT " .
        "(SELECT COUNT(*) FROM wm_tb_investigating_case WHERE station='$station' AND ivc_cl_type='คดีอุกฉกรรจ์' AND (ivc_cl_date BETWEEN '$d3' AND '$d4')) AS M2, ".
        "(SELECT COUNT(*) FROM wm_tb_investigating_case WHERE station='$station' AND ivc_cl_type='คดีอุกฉกรรจ์' AND ivc_cl_catch='จับได้' AND (ivc_cl_date BETWEEN '$d3' AND '$d4')) AS M2_c ".
        "";
$res2 = mysqli_query($conn, $sql2);

$no2 = 1;
while($row2 = mysqli_fetch_array($res2))	//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
{
$sum_m2 = $row2["M2"] - $row2["M2_c"];
    
?>
    <tr height=33 style='height:24.6pt'>
      <td height=33 class=xl8820918 style='height:24.6pt;border-left: solid black'>&nbsp;2&nbsp;</td>
      <td class=xl8820918 style='border-top:none;border-left:none'>&nbsp;<?= date_2_Thai(strtotime($prev2)) ?>&nbsp;</td>
      <td class=xl8820918 style='border-top:none;border-left:none'>&nbsp;<?= $row2["M2"] ?>&nbsp;</td>
      <td class=xl8820918 style='border-top:none;border-left:none'>&nbsp;<?= $row2["M2_c"] ?>&nbsp;</td>
      <td class=xl8820918 style='border-top:none;border-right: solid black'>&nbsp;<?= $sum_m2 ?>&nbsp;</td>
     </tr>
<?php
    $no2++;
}
?>

<?php
//ย้อนหลัง เดือน 3
    $d5 = date("Y-m-01", strtotime($prev3));
    $d6 = date("Y-m-t", strtotime($prev3));
    
$sql3 = "SELECT " .
        "(SELECT COUNT(*) FROM wm_tb_investigating_case WHERE station='$station' AND ivc_cl_type='คดีอุกฉกรรจ์' AND (ivc_cl_date BETWEEN '$d5' AND '$d6')) AS M3, ".
        "(SELECT COUNT(*) FROM wm_tb_investigating_case WHERE station='$station' AND ivc_cl_type='คดีอุกฉกรรจ์' AND ivc_cl_catch='จับได้' AND (ivc_cl_date BETWEEN '$d5' AND '$d6')) AS M3_c ".
        "";
$res3 = mysqli_query($conn, $sql3);

$no3 = 1;
while($row3 = mysqli_fetch_array($res3))	//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
{
$sum_m3 = $row3["M3"] - $row3["M3_c"];
  
?>
     <tr height=33 style='height:24.6pt'>
      <td height=33 class=xl8820918 style='height:24.6pt;border-left: solid black'>&nbsp;3&nbsp;</td>
      <td class=xl8820918 style='border-top:none;border-left:none'>&nbsp;<?= date_2_Thai(strtotime($prev3)) ?>&nbsp;</td>
      <td class=xl8820918 style='border-top:none;border-left:none'>&nbsp;<?= $row3["M3"] ?>&nbsp;</td>
      <td class=xl8820918 style='border-top:none;border-left:none'>&nbsp;<?= $row3["M3_c"] ?>&nbsp;</td>
      <td class=xl8820918 style='border-top:none;border-right: solid black'>&nbsp;<?= $sum_m3 ?>&nbsp;</td>
     </tr>
<?php
    $no3++;
}
?>
      </tbody>

    </table>
</diV> 
</diV>  
</diV> 
</div>    <br>

</body>

</html>
<script>
function dates_change()  // คำสั่งเปลี่ยนเดือน ปี
{
    var ms = $("#mi_cl_month option:selected").val();
    var ys = $("#mi_cl_year option:selected").val();
    window.location = "/Activity/Show_Activity4.php?&month=" + ms + "&year=" + ys + "&rnd=" + Math.random();
}
</script>