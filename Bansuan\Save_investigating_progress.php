<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../Alert.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);


$ivg_cl_aid = $_POST[ 'ivg_cl_aid' ];
$ivg_cl_case_no = $_POST[ 'ivg_cl_case_no' ];	
$ivg_cl_date = $_POST[ 'ivg_cl_date' ];
$ivg_cl_invest = $_POST[ 'ivg_cl_invest' ];
$ivg_cl_invest_res = $_POST[ 'ivg_cl_invest_res' ];					
$ivg_cl_detective = $_POST[ 'ivg_cl_detective' ];

// save file รายงานสืบสวน
$file1 = $_FILES[ 'ivg_cl_file' ][ 'tmp_name' ];
if ( is_uploaded_file( $file1 ) ) {
  $ivg_cl_file = "uploaded/Doc/" . $_FILES[ 'ivg_cl_file' ][ 'name' ];	
  move_uploaded_file( $file1, "../" . $ivg_cl_file );  
} else {
  $ivg_cl_file = '';
}

$sql ="SELECT * FROM `wm_tb_investigating_progress` WHERE ivg_cl_aid = :ivg_cl_aid ";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':ivg_cl_aid', $ivg_cl_aid);
try{
    $stmt->execute();
}catch(PDOException $e){
    echo 'Sql Failed: ' .$e->getMessage();
}
    $row = $stmt->fetch(PDO::FETCH_ASSOC);

if($row) {
    $ivg_cl_aid = $row['ivg_cl_aid'];
    $sql = "UPDATE wm_tb_investigating_progress SET 
            ivg_cl_case_no = :ivg_cl_case_no,
            ivg_cl_date = :ivg_cl_date,
            ivg_cl_invest = :ivg_cl_invest,
            ivg_cl_invest_res = :ivg_cl_invest_res,
            ivg_cl_detective = :ivg_cl_detective";
    $params = [
        'ivg_cl_case_no' => $ivg_cl_case_no,
        'ivg_cl_date' => $ivg_cl_date,
        'ivg_cl_invest' => $ivg_cl_invest,
        'ivg_cl_invest_res' => $ivg_cl_invest_res,
        'ivg_cl_detective' => $ivg_cl_detective
    ];

    if ($ivg_cl_file !== '') {
        $sql .= ", ivg_cl_file = :ivg_cl_file";
        $params['ivg_cl_file'] = $ivg_cl_file;
    }

    $sql .= " WHERE ivg_cl_aid = :updated_ivg_cl_aid";
    $params['updated_ivg_cl_aid'] = $ivg_cl_aid;
    
} else {
    $sql = "INSERT INTO wm_tb_investigating_progress (ivg_cl_case_no, ivg_cl_date, ivg_cl_invest, ivg_cl_invest_res, ivg_cl_detective, ivg_cl_file) 
                VALUES (:ivg_cl_case_no, :ivg_cl_date, :ivg_cl_invest, :ivg_cl_invest_res, :ivg_cl_detective, :ivg_cl_file)";
    $params = [
        'ivg_cl_case_no' => $ivg_cl_case_no,
        'ivg_cl_date' => $ivg_cl_date,
        'ivg_cl_invest' => $ivg_cl_invest,
        'ivg_cl_invest_res' => $ivg_cl_invest_res,
        'ivg_cl_detective' => $ivg_cl_detective,
        'ivg_cl_file' => $ivg_cl_file
    ];
}

$stmt = $pdo->prepare($sql);

try{
    
$result = $stmt->execute($params);
    
}catch(PDOException $e){
    echo 'Sql2 Failed: ' . $e->getMessage();
}

$ip_address = $_SERVER['REMOTE_ADDR'];
$action = "Save investigating progress : $ivg_cl_case_no"; // You can set the action to a suitable value
$data = str_replace("'", "", compact_array($_POST));
log_activity($acc, $action, $data, $ip_address);

if ($result) {
    $_SESSION['success'] = "Data has been inserted succesfully";
    showSweetAlert("Saved successfully", "บันทึกข้อมูลเรียบร้อย");
    unset($_SESSION['success']); // Clear the session variable
    header("refresh:2; url=/Bansuan/show_detail_investigating_case.php?pcid={$ivg_cl_case_no}");
} else {
    $_SESSION['error'] = "Error";
    showSweetAlert('Error', "บันทึกข้อมูล ไม่สำเร็จ", 'error');
    unset($_SESSION['error']); // Clear the session variable
    header("refresh:2; url=/Bansuan/show_detail_investigating_case.php?pcid={$ivg_cl_case_no}");
}

$pdo = null;

?>