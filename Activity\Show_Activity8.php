<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';
include '../Head_image.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

$id = isset($_GET['hv_cl_aid']) ? $_GET['hv_cl_aid'] : '';

//get month from select or current month
$month = isset($_GET['month']) ? $_GET['month'] : date("m");
$year = isset($_GET['year']) ? $_GET['year'] : date("Y");

$m1 = '';
$m2 = '';
$m3 = '';
$m4 = '';
$m5 = '';
$m6 = '';
$m7 = '';
$m8 = '';
$m9 = '';
$m10 = '';
$m11 = '';
$m12 = '';
switch($month)
{
    case "01": $m1 = "selected"; break;
    case "02": $m2 = "selected"; break;
    case "03": $m3 = "selected"; break;
    case "04": $m4 = "selected"; break;
    case "05": $m5 = "selected"; break;
    case "06": $m6 = "selected"; break;
    case "07": $m7 = "selected"; break;
    case "08": $m8 = "selected"; break;
    case "09": $m9 = "selected"; break;
    case "10": $m10 = "selected"; break;
    case "11": $m11 = "selected"; break;
    case "12": $m12 = "selected"; break;
}
$current_month = $global_thai_month[0+$month];
$y2 = $year+543;

?>

<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
        <base target="_top">
		<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
		<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
  		<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
 		<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js"></script>

<style type="text/css">
body{
    font-size:12px; 
}
.textAlignVer{
    display:block;
    filter: flipv fliph;
    -webkit-transform: rotate(-90deg); 
    -moz-transform: rotate(-90deg); 
    transform: rotate(-90deg); 
    position:relative;
    width:20px;
    white-space:nowrap;
    font-size:12px;
    margin-bottom:10px;
}
</style>
<!--<p><img src="../Image/Head2.jpg" width="100%" height="" alt=""/></p>-->
<div class="container-fluid" align="left">
<div class=" h3 text-center  alert alert-success mb-4 mt-4 " role="alert" >กิจกรรม 8 การตรวจเยี่ยมบุคคลพ้นโทษ/พักโทษ</div>

<div align="left">
<div>
    <table width="80%" border="0" cellspacing="1" cellpadding="1">
      <tbody>
        <tr>
          <td width="12%">&nbsp;<a href="/WM/index.php?rnd=<?= rand(); ?>&page=report" class="btn btn-warning btn-lg mb-4" >ย้อนกลับ</a></td>
          <!--<td width="11%">&nbsp;<a href="Add_Activity1.php" class="btn btn-success btn-lg mb-4" >เพิ่มข้อมูล</a></td>-->
          <td width="16%"><label>เลือกปี </label>
            <select id="mi_cl_year" name="mi_cl_year" class="form-select col-8 d-lg-inline" style="background-color: khaki; font-size: 16px" onChange="dates_change()">
              <option value="">เลือก</option>
              <?php
                $cur_y = date("Y") + 2;
                for($y=2020; $y<$cur_y; $y++) {
                    $sel = ($y == $year) ? "selected" : '';
                    echo "<option value='$y' $sel> ". ($y + 543) ." </option>";
                }
            ?>
          </select></td>
            <td width="2%">&nbsp;</td>
          <td width="25%"><label>เดือน</label>
            <select id="mi_cl_month" name="mi_cl_month" class="form-select col-3 d-lg-inline" style="background-color: khaki; font-size: 16px" onChange="dates_change()">
              <option value="">เลือก</option>
              <option <?= $m1 ?> value="01">มกราคม</option>
              <option <?= $m2 ?> value="02">กุมภาพันธ์</option>
              <option <?= $m3 ?> value="03">มีนาคม</option>
              <option <?= $m4 ?> value="04">เมษายน</option>
              <option <?= $m5 ?> value="05">พฤษภาคม</option>
              <option <?= $m6 ?> value="06">มิถุนายน</option>
              <option <?= $m7 ?> value="07">กรกฎาคม</option>
              <option <?= $m8 ?> value="08">สิงหาคม</option>
              <option <?= $m9 ?> value="09">กันยายน</option>
              <option <?= $m10 ?> value="10">ตุลาคม</option>
              <option <?= $m11 ?> value="11">พฤศจิกายน</option>
              <option <?= $m12 ?> value="12">ธันวาคม</option>
          </select></td>
          <td width="40%">&nbsp;</td>
          <td width="5%">&nbsp;</td>
        </tr>
      </tbody>
    </table>
</div>

<h3 align="center">ข้อมูลการติดตามพฤติการณ์และความเคลื่อนไหวของบุคคลพ้นโทษ พักโทษ</h3>
<h3 align="center">ประจำเดือน&nbsp; <?= $current_month ?> &nbsp;<?= $y2 ?> &nbsp;</h3>
<div class="table-responsive">
<table height="221" border="1" cellpadding="1" cellspacing="1" class="table-bordered table-hover mb-4 mt-4">
  <tbody>
    <tr>
      <td rowspan="3" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">ลำดับ</td>
      <td rowspan="3" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">สถานี</td>
      <td rowspan="3" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">จำนวนครั้ง<br>(รวมทั้งหมด)</td>
      <td height="46" colspan="5" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">เยี่ยมพบ</td>
      <td colspan="9" bgcolor="#FD5F2F" style="color: #00000; text-align: center; border-color: navy">เยี่ยมไม่พบ</td>
      <td rowspan="3" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">มีผลการปฏิบัติ</td>
      <td rowspan="3" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy">ไม่มีผลการปฏิบัติ</td>
      <!--<td colspan="3" rowspan="3" bgcolor="#0CE5F9" style="color: #00000; text-align: center; border-color: navy"></td>-->
    </tr>
    <tr>
      <td rowspan="2" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">จำนวน (คน)</td>
      <td rowspan="2" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">ผลใน Police4.0</td>
      <td rowspan="2" bgcolor="#8EF58C" style="color: #00000; text-align: center; border-color: navy">ร้อยละความสำเร็จ</td>
      <td rowspan="2" bgcolor="#48CF1A" style="color: #00000; text-align: center; border-color: navy">เก็บ DNA</td>
      <td rowspan="2" bgcolor="#48CF1A" style="color: #00000; text-align: center; border-color: navy">ยังไม่เก็บ DNA</td>
      <td height="58" colspan="3" bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">ทราบที่อยู่</td>
      <td colspan="3" bgcolor="#FDBD62" style="color: #00000; text-align: center; border-color: navy">ไม่ทราบที่อยู่</td>
      <td colspan="3" bgcolor="#FA9779" style="color: #00000; text-align: center; border-color: navy">กระทำผิดอีก/อยู่เรือนจำ</td>
      </tr>
    <tr>
	  <td height="58" bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">จำนวน</td>
	  <td bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">แจ้งท้องที่ใหม่</td>
	  <td bgcolor="#FDE862" style="color: #00000; text-align: center; border-color: navy">ยังไม่แจ้ง</td>
	  <td bgcolor="#FDBD62" style="color: #00000; text-align: center; border-color: navy">จำนวน</td>
	  <td bgcolor="#FDBD62" style="color: #00000; text-align: center; border-color: navy">แจ้ง รจ.และทว.</td>
	  <td bgcolor="#FDBD62" style="color: #00000; text-align: center; border-color: navy">ยังไม่แจ้ง</td>
	  <td bgcolor="#FA9779" style="color: #00000; text-align: center; border-color: navy">จำนวน</td>
	  <td bgcolor="#FA9779" style="color: #00000; text-align: center; border-color: navy">แจ้ง ทว.</td>
	  <td bgcolor="#FA9779" style="color: #00000; text-align: center; border-color: navy">ยังไม่แจ้ง</td>
      </tr>
	  
<?php     
                      
$sql = "SELECT " .
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE (`hv_cl_find_or_not`='1' OR `hv_cl_find_or_not`='2') AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND station=:station) AS T1_1, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND station=:station) AS T1, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_find40='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND station=:station) AS T2, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND station=:station) AS T3, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_dna='2' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND station=:station) AS T4, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND station=:station) AS T5, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND station=:station) AS T6, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_action='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND station=:station) AS T7, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_adr='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND station=:station) AS T8, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND station=:station) AS T9, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND station=:station) AS T10, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_prison='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND station=:station) AS T11, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND station=:station) AS T12, ".
        "(SELECT COUNT(*) FROM wm_tb_homevisit WHERE hv_cl_no_torvor2='1' AND MONTH(hv_cl_datevisit)=:month AND YEAR(hv_cl_datevisit)=:year AND station=:station) AS T13 " .
        " ";

$stmt = $pdo->prepare($sql);
$stmt->bindParam(':month', $month);
$stmt->bindParam(':year', $year);
$stmt->bindParam(':station', $station); // Add station parameter
try {
    $stmt->execute();
} catch (PDOException $e) {
    echo '$sql Failed: '. $e->getMessage();
}

          
// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no = 1;
while($row = $stmt->fetch(PDO::FETCH_ASSOC))	//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
{
//ฟังก์ชั่น วันที่ ดึงจาก condb
//$strDate = DateThai( $row_mi["date_record"] );
    
if($row["T1"] > 0) {   
    $persen = ($row["T2"]*100)/$row["T1"];
}
else {
    $persen=0;
    }
    $sum1 = $row["T1"] + $row["T5"] + $row["T8"] + $row["T11"];
    $sum2 = $row["T1_1"] - $sum1;

?>	

    <tr>
	  <td height="52" style="font-size: 24px"><?= $no ?> </td> 
      <td nowrap>&nbsp;<?= $name_station ?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo $row["T1_1"] ?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo $row["T1"]?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo $row["T2"]?>&nbsp;</td>
      <td style="font-size: 24px"><?= $persen ?>&nbsp;%</td>
      <td style="font-size: 24px">&nbsp;<?php echo $row["T3"]?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo $row["T4"]?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T5"] > 0) ? $row["T5"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T6"] > 0) ? $row["T6"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T7"] > 0) ? $row["T7"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T8"] > 0) ? $row["T8"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T9"] > 0) ? $row["T9"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T10"] > 0) ? $row["T10"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T11"] > 0) ? $row["T11"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T12"] > 0) ? $row["T12"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?php echo (($row["T13"] > 0) ? $row["T13"]  : "")?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?= $sum1 ?>&nbsp;</td>
      <td style="font-size: 24px">&nbsp;<?= $sum2 ?>&nbsp;</td>
      <!--<td><a href="#Edit_mission.php?id=<?= $row[""] ?>" class="btn btn-warning mb-4" >แก้ไข</a> </td>
	  <td><a href="#Del_mission.php?id=<?= $row[""] ?>" class="btn btn-danger mb-4" onClick="Del('#Del_mission.php?id=<?= $row[""]?>')">ลบ</a>-->
    </tr>
  </tbody>
<?php
        
	$no++;
}//while
?>
</table>
</div>
&nbsp;&nbsp;<button class="btn btn-primary btn-lg mb-4" onClick="do_print()"> พิมพ์รายงาน </button>
<script>
function Del(url)
{
	if(confirm("คุณจะลบข้อมูล หรือ ไม่ ?"))
	{
		window.location = url;		
	}
}
function dates_change()  // คำสั่งเปลี่ยนเดือน ปี
{
    var ms = $("#mi_cl_month option:selected").val();
    var ys = $("#mi_cl_year option:selected").val();
    window.location = "/Activity/Show_Activity8.php?&month=" + ms + "&year=" + ys + "&rnd=" + Math.random();
}
</script>

<!-- script สำหรับส่งไปพิมพ์รายงาน  -->
<script>
    
    function do_print()
    {
        var ms = $("#mi_cl_month option:selected").val();
        var ys = $("#mi_cl_year option:selected").val();
        var newW = window.open("/Activity/Show_Activity8_print.php?&month=" + ms + "&year=" + ys + "&rnd=" + Math.random(), "_print", "menubar=no,titlebar=no,toolbar=no,status=no,width=100,resizable=yes");
        newW.print();
        //window.print();
    }
    
</script>
