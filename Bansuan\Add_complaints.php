<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// เลขคดี
$sql_no = "SELECT * FROM wm_tb_Complaints WHERE `cp_cl_case_id` ORDER BY `cp_cl_aid` DESC LIMIT 1";
    $stmt = $pdo->prepare($sql_no);
    $stmt->execute();
    $row_no = $stmt->fetch(PDO::FETCH_ASSOC);

    $running_no = $row_no['cp_cl_case_id'];   

// สำหรับเลือก จังหวัด อำเภอ ตำบล
include("../config.inc.php");
include("../classes/class.database.inc.php");
$conn2 = get_connection(); // connect to mySQLDB

// สำหรับเลือกสถานีตำรวจทั่วประเทศ
$region = isset($_GET['region']) ? $_GET['region'] : 0; // sukhothai
$provincial = isset($_GET['provincial']) ? $_GET['provincial'] : 0; // sukhothai
//$station = isset($_GET['station']) ? $_GET['station'] : 0; // sukhothai

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>เพิ่มข้อมูลการรับแจ้งเหตุ</title>
<script src="/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 18px;
    }
input::placeholder {
  color: red;
}
.form-control::placeholder {
  color: sandybrown;
}
</style>
<!-- สำหรับวันที่ เดือนไทย ปี พ.ศ. และ เลือนเลือกปี พ.ศ.ได้  -->
<link rel="stylesheet" href="../datepicker_th/jquery.datetimepicker.css" type="text/css">
<script src="../datepicker_th/jquery-1.8.3.min.js"></script>  
<script src="../datepicker_th/jquery.datetimepicker.full.js"></script>
</head>

<body>
	
	<div class="container">
	<div class="row">
	<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> เพิ่มข้อมูลการรับแจ้งเหตุ ฝ่ายสืบสวน </div>
	<form action="Save_complaints.php" method="POST" enctype="multipart/form-data" class="body">
	<span style="color: #1203F8">
        <div align="right"> <a>รหัสรับแจ้งเหตุ ที่เพิ่งรับล่าสุด</a>&nbsp;<b style="color: crimson; font-size: 20px ; background-color: yellow ;padding: 10px"><?= $running_no ?></b></div>
        
		<label hidden ="hidden">ลำดับ</label>
		<input type = "text" name = "cp_cl_aid" class="form-control"  hidden ="hidden" >
        
		<label>รหัสรับแจ้งเหตุ <span style="color: #F90004">* จำเป็น</span></label>
		<input type = "text" name = "cp_cl_case_id" class="form-control" placeholder="รหัสรับแจ้งเหตุ รันนิ่ง/ปี" Required >
        
		<label>เหตุ <span style="color: #F90004">* จำเป็น</span> </label>
		<input type = "text" name="cp_cl_case" class="form-control" placeholder="ระบุเหตุที่รับแจ้ง"  Required >
                
        <label>ที่มา <span style="color: #F90004">* จำเป็น</span></label>
            <select id="cp_cl_source" name="cp_cl_source" class="form-select form-select-sm" aria-label=".form-select-sm example" Required>
                <option value="" selected> </option>  <!-- ดึงข้อมูลมาจากตารางใหม่ -->
                <?php
                    $res1 = $pdo->prepare("SELECT * FROM `wm_tb_source_complaints` WHERE `sco_cl_aid` ");
                        $res1->execute();

                    while($row1 = $res1->fetch(PDO::FETCH_ASSOC))
                    {
                        $sco_cl_aid = htmlspecialchars($row1['sco_cl_aid'], ENT_QUOTES, 'UTF-8');
                        $sco_cl_data = htmlspecialchars($row1['sco_cl_data'], ENT_QUOTES, 'UTF-8');

                        echo "<option value='$sco_cl_aid'>$sco_cl_data</option>";
                    }
                ?>
            </select>
        
		<label>วัน ที่รับแจ้งเหตุ <span style="color: #F90004">* จำเป็น</span> </label> 
        <p><input type="text" name="cp_cl_date_complaints" id="datepicker" value="" placeholder="เพื่อข้อมูลถูกต้อง กรุณาเลือกจากปฎิทินด้านล่าง" class="form-control" style="width:250px;" autocomplete="off" required></p>
        <!-- สำหรับวันที่ เดือนไทย ปี พ.ศ. และ เลือนเลือกปี พ.ศ.ได้  -->
        <script type="text/javascript"> 
            $(function(){

                $.datetimepicker.setLocale('th'); // ต้องกำหนดเสมอถ้าใช้ภาษาไทย และ เป็นปี พ.ศ.
                // กรณีใช้แบบ input
                $("#datepicker").datetimepicker({
                    timepicker:false,
                    format:'Y-m-d',  // กำหนดรูปแบบวันที่ ที่ใช้ เป็น 00-00-0000			
                    lang:'th',  // ต้องกำหนดเสมอถ้าใช้ภาษาไทย และ เป็นปี พ.ศ.
                    onSelectDate:function(dp,$input){
                        var yearT=new Date(dp).getFullYear()-0;  
                        var yearTH=yearT;  // ถ้าจะให้แสดงเป็น พ.ศ. ให้เป็น yearT+543
                        var fulldate=$input.val();
                        var fulldateTH=fulldate.replace(yearT,yearTH);
                        $input.val(fulldateTH);
                    },
                });       
                // กรณีใช้กับ input ต้องกำหนดส่วนนี้ด้วยเสมอ เพื่อปรับปีให้เป็น ค.ศ. ก่อนแสดงปฏิทิน
                $("#datepicker").on(function(e){
                    var dateValue=$(this).val();
                    if(dateValue!=""){
                            var arr_date=dateValue.split("-"); // ถ้าใช้ตัวแบ่งรูปแบบอื่น ให้เปลี่ยนเป็นตามรูปแบบนั้น
                            // ในที่นี้อยู่ในรูปแบบ 00-00-0000 เป็น d-m-Y  แบ่งด่วย - ดังนั้น ตัวแปรที่เป็นปี จะอยู่ใน array
                            //  ตัวที่สอง arr_date[2] โดยเริ่มนับจาก 0 
                            dateValue=dateValue.replace(arr_date[0],yearT);
                            $(this).val(dateValue);													
                    }		
                });


            });
            </script>
        <!-- สำหรับ วันที่ แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ -->
        
		<label>เวลา รับแจ้งเหตุ</label>
		<input type = "text" name = "cp_cl_time_complaints" class="form-control" placeholder="ระบุเวลา ที่รับแจ้งเหตุ" >
        
        <label>เลขหนังสือต้นเรื่องร้องเรียน</label>
		<input type = "text" name = "cp_cl_number" class="form-control" placeholder="ระบุเลขหนังสือต้นเรื่องร้องเรียน" >
           
        <label>วันที่เกิดเหตุ <span style="color: #F90004">* จำเป็น</span> </label>
        <p><input type="text" name="cp_cl_date_incident" id="datepicker2" value="" placeholder="เพื่อข้อมูลถูกต้อง กรุณาเลือกจากปฎิทินด้านล่าง" class="form-control" style="width:250px;" autocomplete="off" required></p>
        <script type="text/javascript"> 
            $(function(){

                $.datetimepicker.setLocale('th'); // ต้องกำหนดเสมอถ้าใช้ภาษาไทย และ เป็นปี พ.ศ.
                // กรณีใช้แบบ input
                $("#datepicker2").datetimepicker({
                    timepicker:false,
                    format:'Y-m-d',  // กำหนดรูปแบบวันที่ ที่ใช้ เป็น 00-00-0000			
                    lang:'th',  // ต้องกำหนดเสมอถ้าใช้ภาษาไทย และ เป็นปี พ.ศ.
                    onSelectDate:function(dp,$input){
                        var yearT=new Date(dp).getFullYear()-0;  
                        var yearTH=yearT;  // ถ้าจะให้แสดงเป็น พ.ศ. ให้เป็น yearT+543
                        var fulldate=$input.val();
                        var fulldateTH=fulldate.replace(yearT,yearTH);
                        $input.val(fulldateTH);
                    },
                });       
                // กรณีใช้กับ input ต้องกำหนดส่วนนี้ด้วยเสมอ เพื่อปรับปีให้เป็น ค.ศ. ก่อนแสดงปฏิทิน
                $("#datepicker2").on(function(e){
                    var dateValue=$(this).val();
                    if(dateValue!=""){
                            var arr_date=dateValue.split("-"); // ถ้าใช้ตัวแบ่งรูปแบบอื่น ให้เปลี่ยนเป็นตามรูปแบบนั้น
                            // ในที่นี้อยู่ในรูปแบบ 00-00-0000 เป็น d-m-Y  แบ่งด่วย - ดังนั้น ตัวแปรที่เป็นปี จะอยู่ใน array
                            //  ตัวที่สอง arr_date[2] โดยเริ่มนับจาก 0 
                            dateValue=dateValue.replace(arr_date[0],yearT);
                            $(this).val(dateValue);													
                    }		
                });


            });
            </script>
        
		<label>ตั้งแต่เวลา</label>
		<input type = "text" name = "cp_cl_time_start_incident" class="form-control" placeholder="ระบุเหตุเกิดตั้งแต่เวลาใด"  >
		<label>ถึงเวลา</label>
		<input type = "text" name = "cp_cl_time_end_incident" class="form-control" placeholder="ระบุเวลาสิ้นสุดเหตุ" >

		<br>
		
		<label>สถานที่เกิดเหตุ</label>
		<input type = "text" name = "cp_cl_place" class="form-control" placeholder="ระบุสถานที่เกิดเหตุ" >
		
		<hr>
		
		<label>จำนวนผู้เสียหาย</label>
		<select id="cp_cl_sufferer_quantity" name="cp_cl_sufferer_quantity" class="form-select form-select-sm"  placeholder="ระบุจำนวนผู้เสียหาย" >
			<option value="" selected>เลือก</option>
			<option value="1">1</option>
			<option value="2">2</option>
			<option value="3">3</option>
			<option value="4">4</option>
			<option value="5">5</option>
			<option value="6">6</option>
			<option value="7">7</option>
			<option value="8">8</option>
			<option value="9">9</option>
			<option value="10">10</option>
		</select>
		
		<label>ชื่อผู้เสียหาย 1</label>
		<input type = "text" name = "cp_cl_sufferer_name1" class="form-control" placeholder="ระบุชื่อผู้เสียหาย 1" >
		<label>อายุผู้เสียหาย 1</label>
		<input type = "text" name = "cp_cl_age1" class="form-control" placeholder="ระบุอายุผู้เสียหาย 1 (ระบุเฉพาะตัวเลขอายุ)" >
		<label>ที่อยู่ผู้เสียหาย 1</label>
		<input type = "text" name = "cp_cl_address1" class="form-control" placeholder="ระบุที่อยู่ผู้เสียหาย 1" >
		<label>โทรศัพท์ผู้เสียหาย 1</label>
		<input type = "text" name = "cp_cl_phone1" class="form-control" placeholder="ระบุโทรศัพท์ผู้เสียหาย 1" >
		<label>ชื่อผู้เสียหาย 2 (ถ้ามี) </label>
		<input type = "text" name = "cp_cl_sufferer_name2" class="form-control" placeholder="ระบุชื่อผู้เสียหาย 2 (ถ้ามี)"   >
		<label>อายุผู้เสียหาย 2</label>
		<input type = "text" name = "cp_cl_age2" class="form-control" placeholder="ระบุอายุผู้เสียหาย 2 (ระบุเฉพาะตัวเลขอายุ)" >
		<label>ที่อยู่ผู้เสียหาย 2</label>
		<input type = "text" name = "cp_cl_address2" class="form-control" placeholder="ระบุที่อยู่ผู้เสียหาย 2" >
		<label>โทรศัพท์ผู้เสียหาย 2</label>
		<input type = "text" name = "cp_cl_phone2" class="form-control" placeholder="ระบุโทรศัพท์ผู้เสียหาย 2" >
		<label>ชื่อผู้เสียหาย 3 (ถ้ามี)</label>
		<input type = "text" name = "cp_cl_sufferer_name3" class="form-control" placeholder="ระบุชื่อผู้เสียหาย 3 (ถ้ามี) (ระบุเฉพาะตัวเลขอายุ)"   >
		<label>อายุผู้เสียหาย 3</label>
		<input type = "text" name = "cp_cl_age3" class="form-control" placeholder="ระบุอายุผู้เสียหาย 3" >
		<label>ที่อยู่ผู้เสียหาย 3</label>
		<input type = "text" name = "cp_cl_address3" class="form-control" placeholder="ระบุที่อยู่ผู้เสียหาย 3" >
		<label>โทรศัพท์ผู้เสียหาย 3</label>
		<input type = "text" name = "cp_cl_phone3" class="form-control" placeholder="ระบุโทรศัพท์ผู้เสียหาย 3" >
		
		<br>
		<hr color="#F8060A">
		<br>
		
		<label>ผู้ก่อเหตุ/ผู้ต้องสงสัย 1</label>
		<input type = "text" name = "cp_cl_suspect1" class="form-control" placeholder="ระบุผู้ต้องสงสัย 1" >
		<label>ผู้ก่อเหตุ/ผู้ต้องสงสัย 2</label>
		<input type = "text" name = "cp_cl_suspect2" class="form-control" placeholder="ระบุผู้ต้องสงสัย 2" >
		<label>ผู้ก่อเหตุ/ผู้ต้องสงสัย 3</label>
		<input type = "text" name = "cp_cl_suspect3" class="form-control" placeholder="ระบุผู้ต้องสงสัย 3" >
		<label>ตำหนิรูปพรรณ</label>
		<input type = "text" name = "cp_cl_description" class="form-control" placeholder="ระบุตำหนิรูปพรรณผู้ต้องสงสัย"   >
		<label>ยานพาหนะที่ใช้ก่อเหตุ</label>
		<input type = "text" name = "cp_cl_vehicle" class="form-control" placeholder="ระบุยานพาหนะที่ใช้ก่อเหตุ" >
		<label>ทรัพย์สินที่ถูกประทุษร้าย</label>
		<input type = "text" name = "cp_cl_property" class="form-control" placeholder="ระบุทรัพย์สินที่ถูกประทุษร้าย" >
		
		<br>
		<hr color="#F8060A">
		<br>
		
		<label>พฤติการณ์</label>
		<input type = "text" name = "cp_cl_case_behavior" class="form-control" placeholder="ระบุพฤติการณ์" >
		<label>แผนที่สถานที่เกิดเหตุ (ระบุพิกัด)</label>
		<input type = "text" name = "cp_cl_map" class="form-control" placeholder="แผนที่สถานที่เกิดเหตุ" >
		
		<br>
		<hr color="#F8060A">
		<br>
		
		<label>ร้อยเวรสืบสวน</label>
		<?php
		if ($station == 6707) {
			echo '<select id="cp_cl_investigative_sergeant" name="cp_cl_investigative_sergeant"  class="form-select form-select-sm"  placeholder="ระบุร้อยเวรสืบสวน">';
			echo '<option value="" selected> </option>
				<option value="ร.ต.อ.พิทยา อ่วมเหล็ง">ร.ต.อ.พิทยา อ่วมเหล็ง</option>
				<option value="ร.ต.อ.วุฒิไกร สายทอง">ร.ต.อ.วุฒิไกร สายทอง</option>
				<option value="ร.ต.อ.ณัฐพงษ์ ภู่ทอง">ร.ต.อ.ณัฐพงษ์ ภู่ทอง</option>
			';
			echo '</select>';
		} else {
			echo '<input type="text" id="cp_cl_investigative_sergeant" name="cp_cl_investigative_sergeant" class="form-control" placeholder="ระบุร้อยเวรสืบสวน">';
		}
		?>

		<label>เจ้าหน้าที่สืบสวน/บันทึก</label>
		<?php
			if ($station == 6707) {
				// User's station is 6707, display the dropdown select
				echo '<select id="cp_cl_investigative_officer" name="cp_cl_investigative_officer" class="form-select form-select-sm" placeholder="ระบุชื่อผู้บันทึก" required>';
				echo '<option value="" selected> </option>';
				$bsdp = $pdo->prepare("SELECT * FROM `police_name_bsdetective` ORDER BY `police_name_bsdetective`.`aid_bsdp` DESC");
				$bsdp->execute();
				while ($row_bsdp = $bsdp->fetch(PDO::FETCH_ASSOC)) {
					$aid_bsdp = htmlspecialchars($row_bsdp['aid_bsdp'], ENT_QUOTES, 'UTF-8');
					$bsdp_name = htmlspecialchars($row_bsdp['bsdp_name'], ENT_QUOTES, 'UTF-8');

					echo "<option value='$bsdp_name'>$bsdp_name</option>";
				}
				echo '</select>';
			} else {
				// User's station is not 6707, display a text input
				echo '<input type="text" id="cp_cl_investigative_officer" name="cp_cl_investigative_officer" class="form-control" placeholder="ระบุ ยศชื่อ-สกุล ผู้บันทึก" required>';
			}
		?>

        <label hidden ="hidden">การดำเนินการ</label>
		<input type = "text" name = "cp_cl_action" class="form-control"  placeholder="กรอกรายละเอียดการดำเนินการ" hidden ="hidden" >
        
        <label>สถานะการดำเนินการ</label>
        <select id="cp_cl_status" name="cp_cl_status" class="form-select form-select-sm"placeholder="สถานะการดำเนินการ">
                    <option value="" selected> </option>  <!-- ดึงข้อมูลมาจากตารางใหม่ -->
                    <?php
                        $res2 = $pdo->prepare("SELECT * FROM wm_tb_Complaints_status order by cs_cl_aid ASC");
                        $res2->execute();
            
                        while($row2 = $res2->fetch(PDO::FETCH_ASSOC))
                        {
                            $cs_cl_aid = htmlspecialchars($row2['cs_cl_aid'], ENT_QUOTES, 'UTF-8');
                            $cs_cl_status = htmlspecialchars($row2['cs_cl_status'], ENT_QUOTES, 'UTF-8');
                            
                            
                            echo "<option value='$cs_cl_aid'>$cs_cl_status</option>";
                        }
                    ?>
                </select>
        
        <br>
        <label>สถานีตำรวจ <span style="color: #F90004">* จำเป็น</span></label><br>
                <!-- เลือกสถานีตำรวจ -->
        <label> บช. </label><br>
        <select name="region" id="region" onChange="do_region_change()" class="form-control" Required >
                <option value="">&lt;&lt; เลือก บช. &gt;&gt;</option>
                <?php
			  	$res_re = $conn2->query("SELECT * FROM `wm_tb_police_region` 
                                            ORDER BY `name_region` ASC");
			     $selected = '';
			     while($row_re = $conn2->fetch_row($res_re)) 
                     {
                      $code_region = $row_re['code_region'];
                      $name_region = $row_re['name_region'];
                      // set default provionce to 64 >> sukhothai
                          if($code_region == $region) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$code_region' $selected> $name_region </option>\n";
                     }
			  
			  ?>
              </select>
        
        <label> บก. </label><br>
        <select name="provincial" id="provincial" onChange="do_provincial_change()" class="form-control" >
        <option value="0">&lt;&lt; เลือก บก. &gt;&gt;</option>
                <?php
                if($region > 0)
                {
                    $res_prov = $conn2->query("SELECT * FROM `wm_tb_police_provincial` 
                                                WHERE `region_code`='$region' 
                                                ORDER BY `provincial_code` ASC");
                    $selected = '';
                  while($res_prov = $conn2->fetch_row($res_prov)) 
                  {
                      $provincial_code = $res_prov['provincial_code']; // 
                      $provincial = $res_prov['provincial'];
                      // set default provionce to 64 >> sukhothai
                          if($provincial_code == $provincial) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$provincial_code' $selected> $provincial </option>\n";
                  }
                }	
                ?>
              </select>

         <label> สน./สภ. </label>
        <br>
        <select name="station" id="station" class="form-control" >
                <option value="0">&lt;&lt; เลือก สน./สภ. &gt;&gt;</option>
                <?php
	           if($provincial > 0)
                {
                    $res_st = $conn2->query("SELECT * FROM `wm_tb_police_station2` 
                                                WHERE provincial_code='$provincial_code'
                                                ORDER BY `station_code` ASC");
                    $selected = '';
                  while($row_st = $conn2->fetch_row($res_st)) 
                  {
                      $station_code = $row_st['station_code']; // 
                      $station_name = $row_st['station_name'];
                      // set default provionce to 64 >> sukhothai
                          if($station_code == $station) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$station_code' $selected> $station_name </option>\n";
                  }
                }	
                ?>                                                                     
              </select>
		<br>
        
		<br>
        <div class="mb-3">
			<label for="formFileMultiple" class="form-label">ไฟล์เอกสาร</label>
			<input class="form-control" type="file" id="cp_cl_complaints_file" name="cp_cl_complaints_file" multiple>
		</div>
		
		<p>
		<br>
			<input type="submit" value="บันทึกข้อมูล" class="btn btn-success" >
			<td> <a href="index.php?rnd=<?= rand(); ?>&page=complaints" class="btn btn-warning" >ยกเลิก</a> </td>
		</p>
		<br>
		<br>
	</form>
	</div>
	</div>
	</div>
<script>
// ฟังก์ชั่น เปลี่ยนภาค (บช.) เป็น ภ.จว. (บก.)
function do_region_change()
{
	var sel_region = document.getElementById("region");		
	var code_region = sel_region.options[sel_region.selectedIndex].value;

	$.get("../ajax/get_provincial.php?region=" + code_region, 
		  function(data, status){
				//alert("Data: " + data + "\nStatus: " + status);
				// jQuery style						
				$('#provincial').find('option').remove().end();
				var data = $.trim(data);
				if(data == '') {
					alert("ไม่พบข้อมูล บก. !");
					return;
				}

					//.append('<option value="whatever">text</option>').val('whatever');
				var datas = data.split(';');
				for(var i=0; i<datas.length; i++) {
					if(datas[i] == '') continue;
					datas[i] = datas[i].split(':');
					var code_region = datas[i][0];
					var name_region = datas[i][1];
					$('#provincial').append('<option value="'+ code_region+'">' + name_region + '</option>');
				}
                // Trigger the change event on the amphur dropdown so the tambon dropdown will be populated     
                $('#provincial').trigger('change');
		});
}
    //เลือก สถานี จากจังหวัด
function do_provincial_change()
{
	var sel_provincial = document.getElementById("region");		
	var provincial_code = sel_provincial.options[sel_provincial.selectedIndex].value;
    
    var sel_provincial2 = document.getElementById("provincial");		
	var station_code = sel_provincial2.options[sel_provincial2.selectedIndex].value;

	$.get("../ajax/get_station.php?region=" + provincial_code + "&provincial=" + station_code, 
			  function(data, status){
					//alert("Data: " + data + "\nStatus: " + status);
					// jQuery style						
					$('#station').find('option').remove().end();
					var data = $.trim(data);
					if(data == '') {
						alert("ไม่พบข้อมูลสถานี !");
						return;
					}

						//.append('<option value="whatever">text</option>').val('whatever');
					var datas = data.split(';');
					for(var i=0; i<datas.length; i++) {
						if(datas[i] == '') continue;
						datas[i] = datas[i].split(':');
						var station_code = datas[i][0];
						var station_name = datas[i][1];
						$('#station').append('<option value="'+ station_code+'">' + station_name + '</option>');
					}
			});
}
</script>

</body>
</html>
