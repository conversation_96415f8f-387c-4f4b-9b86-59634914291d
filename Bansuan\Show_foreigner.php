<script src="/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>
<script src="../SweetAlert2/dist/sweetalert2.all.min.js"></script>

<?php //PDO
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

 // ดึงข้อมูล
$sql_fo = "SELECT
                T1.*,
                T2.station_name as station,
                T5.pv_name_th AS province,
                T3.ap_name_th AS amphur,
                T4.tb_name_th AS tumbon
            FROM
                `wm_tb_foreigner` AS T1 " .
                "LEFT JOIN wm_tb_police_station2 AS T2 ON T1.station = T2.station_code " .
                "LEFT JOIN provinces AS T5 ON T1.fo_cl_province = T5.pv_code " .
                "LEFT JOIN amphures AS T3 ON T1.fo_cl_amphur = T3.ap_code " .
                "LEFT JOIN districts AS T4 ON T1.fo_cl_tumbon = T4.tb_id " .
            "WHERE
                T1.station='$station' AND T1.fo_cl_aid
            ORDER BY
                T1.fo_cl_aid ASC ";

    $stmt = $pdo->prepare($sql_fo);
    $stmt->execute();
    $total = $stmt->rowCount();

//echo '<pre>';
//print_r($res_fo);
//echo '</pre>';
//
//echo '<hr>';

//echo '<pre>';
//var_dump($res_fo);
//echo '</pre>';

//echo '<pre>';
//print_r($res_fo);
//echo '</pre>';

?>
<!--Add CSS styles to position and style the enlarged image:-->
        <style>
          .enlarged-image {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
          }

          .enlarged-image img {
            max-width: 90%;
            max-height: 90%;
          }
        </style>
<link rel="stylesheet" href="../bootstrap/css/bootstrap-grid.css">
<style>
    .swal2-popup {
    width: 500px !important;
    height: auto !important;
    font-size: 14px !important;
}
</style>
<div class="align-self-start">
<div class=" h3 text-center  alert alert-success mb-4 mt-4 " role="alert" >บัญชีบุคคลต่างด้าว <?= $name_station ?> </div>
<div align="left">
    &nbsp;<a href="Add_foreigner.php" class="btn btn-success btn-lg mb-4 <?= $add_btn ?>" >เพิ่มข้อมูล</a>
	&nbsp;&nbsp; <!--<a href="/Bansuan/index.php?rnd=<?= rand(); ?>&page=information" class="btn btn-primary btn-lg mb-4" >กลับ</a> -->
    <a style="background-color: yellow ; padding: 20px">จำนวนบุคคลต่างด้าว : <b style="color: crimson; font-size: 26px"><?= $total ?></b></a>
	<?php
			if ($station == 6707) {
            echo '&nbsp;&nbsp;<a href="https://drive.google.com/file/d/1JggC-hr74tyKfpSWvZRWQsU0Cd1alBq0/view?usp=share_link" class="btn btn-primary btn-lg mb-4" target="_blank">ข้อมูลต่างด้าว จาก ตม.</a>';
        } else{
				
			}
		?>
		
<div class="table-responsive">
<table height="94" border="1" cellpadding="1" cellspacing="1" class="table-bordered table-hover mb-4 mt-4">
  <tbody>
    <tr>
	  <td height="44" bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">ลำดับ</td>		
	  <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">เลขพาสปอร์ต</td>   
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">สัญชาติ</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">คำนำหน้า</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">ชื่อ</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">อายุ</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">วันที่เข้าประเทศ</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">อนุญาตให้อยู่ถึงวันที่</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">ที่อยู่</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">ชื่อนายจ้าง</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">เบอร์นายจ้าง</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">สถานะ</td>
      <!--<td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">พื้นที่</td>-->
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">ภาพ</td>
      <td colspan="3" bgcolor="#995D5D"></td>
      <td colspan="8" bgcolor="#995D5D"></td>
    </tr>
	  
<?php

// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no = 1;
while($row_fo = $stmt->fetch(PDO::FETCH_ASSOC))	
      //เพิ่มเข้ามาใหม่ 12/09/2022 จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
{
     $pcid = $row_fo['fo_cl_passport'];  
    //ฟังก์ชั่น วันที่ ดึงจาก condb
    if($row_fo["fo_cl_date_in"]  != "" ){
    $strDate = DateThai( $row_fo["fo_cl_date_in"] );
        }else{
        $strDate = "";
    }
    if($row_fo["fo_cl_stay_exp"]  != "" ){
    $strDate2 = DateThai( $row_fo["fo_cl_stay_exp"] );
        }else{
        $strDate2 = "";
    }
    
//echo '<hr>';
//
//echo '<pre>';
//var_dump($row_fo);
//echo '</pre>'
    //คำนวณอายุ โดยนำข้อมูลวันเกิด อังกฤษ มาคำนวนฟังก์ชั่น get_personal_age_2
    $birthday = $row_fo["fo_cl_birthday"];
    if($birthday != "") 
    {
        $age = get_personal_age_2($birthday);
    }
    else {
        $age = $row_fo["fo_cl_age"];
    }
?>	

    <tr>
          <td><?= $no ?> </td>
          <td>&nbsp;<?= $row_fo["fo_cl_passport"]?>&nbsp;</td>
          <td nowrap>&nbsp;<?= $row_fo["fo_cl_nationality"]?>&nbsp;</td>
          <td>&nbsp;<?= $row_fo["fo_cl_prefix_eng"]?> </td>
   	      <td nowrap> <?= $row_fo["fo_cl_name"]?> <?= $row_fo["fo_cl_midname"]?> <?= $row_fo["fo_cl_surname"]?> </td>
	      <td>&nbsp;<?= $age ?>&nbsp;</td>
	      <td nowrap>&nbsp;<?= $strDate ?>&nbsp;</td>
          <td nowrap>&nbsp;<?= $strDate2 ?>&nbsp;</td>
          <td><?= $row_fo["fo_cl_adr"]?> หมู่ <?= $row_fo["fo_cl_moo"]?> ต.<?= $row_fo["tumbon"]?> อ.<?= $row_fo["amphur"]?> จ.<?= $row_fo["province"]?>&nbsp;</td>
          <td>&nbsp;<?= $row_fo["fo_cl_employer_name"]?>&nbsp;</td>
          <td>&nbsp;<?= $row_fo["fo_cl_employer_phone"]?>&nbsp;</td>
          <td>&nbsp;<?= $row_fo["fo_cl_status"]?>&nbsp;</td>
	      <!--<td><?= $row_fo["station"]?></td>-->
          <td><img class="enlarge-image" src="<?= $row_fo["fo_cl_image"] ?>" height="80"></td>

        <td>&nbsp;<a href="Show_all_foreigner.php?id=<?= $row_fo["fo_cl_aid"] ?>&pcid=<?= $pcid ?>" class="btn btn-success mb-4" >รายละเอียด</a>&nbsp;</td>
        <td>&nbsp;<a href="Edit_foreigner.php?id=<?= $row_fo["fo_cl_aid"] ?>&pcid=<?= $row_fo["fo_cl_passport"] ?>" class="btn btn-warning mb-4 <?= $edit_btn ?>" >แก้ไข</a>&nbsp;</td>
        
        <td>&nbsp;<button href="#" class="btn btn-danger mb-4" onClick="deleteItem(<?= $row_fo["fo_cl_aid"] ?>)"<?=$del_btn?>>ลบ</button>&nbsp;</td>

	  </tr>
  </tbody>
<?php
	$no++;
}
?>
</table>
        </div>

<script src="/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>
<script src="../SweetAlert2/dist/sweetalert2.all.min.js"></script>
<script>
function deleteItem(id) {
    Swal.fire({
        title: 'คุณต้องการลบข้อมูลนี้ใช่ไหม?',
        text: "หลังจากลบแล้ว จะไม่สามารถกู้คืนได้",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'ใช่, ยืนยันลบ',
        cancelButtonText: 'ยกเลิก'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = '/Bansuan/Del_foreigner.php?id=' + id;
        }
    });
}
</script>
    
<!--Add a JavaScript code to handle the click event:-->

<script>
  // Get all the images with class "enlarge-image"
  const images = document.querySelectorAll(".enlarge-image");

  // Attach a click event listener to each image
  images.forEach(image => {
    image.addEventListener("click", event => {
      // Create a new <div> element to display the enlarged image
      const enlargedImage = document.createElement("div");
      enlargedImage.classList.add("enlarged-image");

      // Create a new <img> element and set its src attribute to the clicked image's src
      const img = document.createElement("img");
      img.src = event.target.src;

      // Append the <img> element to the <div> element
      enlargedImage.appendChild(img);

      // Add the <div> element to the body
      document.body.appendChild(enlargedImage);

      // Attach a click event listener to the <div> element to remove it when clicked
      enlargedImage.addEventListener("click", event => {
        enlargedImage.remove();
      });
    });
  });
</script>

