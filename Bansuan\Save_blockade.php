<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../Alert.php';

$acc = $user['account'];
$ip_address = $_SERVER['REMOTE_ADDR'];
$action = 'Save blockade'; // You can set the action to a suitable value
$data = str_replace("'", "", compact_array($_POST));
log_activity($acc, $action, $data, $ip_address); // ใช้ function ในไฟล์ Condb.php

/*echo '<pre>';
print_r($_POST);
echo '</pre>';

echo '<hr';

print_r($_FILES);
exit();*/

$bl_cl_aid = $_POST[ 'bl_cl_aid' ];
$bl_cl_num = $_POST[ 'bl_cl_num' ];
$bl_cl_date = $_POST[ 'bl_cl_date' ];
$bl_cl_method = $_POST[ 'bl_cl_method' ];
$bl_cl_search_warrant = $_POST[ 'bl_cl_search_warrant' ];
$bl_cl_province = $_POST[ 'bl_cl_province' ];
$bl_cl_amphur = $_POST[ 'bl_cl_amphur' ];
$bl_cl_tumbon = $_POST[ 'bl_cl_tumbon' ];
$bl_cl_moo = $_POST[ 'bl_cl_moo' ];
$bl_cl_adr = $_POST[ 'bl_cl_adr' ];
$bl_cl_conduct = $_POST[ 'bl_cl_conduct' ];
$bl_cl_find_illegal = $_POST[ 'bl_cl_find_illegal' ];
$bl_cl_find_person = $_POST[ 'bl_cl_find_person' ];
$bl_cl_illegal_things = $_POST[ 'bl_cl_illegal_things' ];
$bl_cl_remark = $_POST[ 'bl_cl_remark' ];
$bl_cl_group = $_POST[ 'bl_cl_group' ];
$region = $_POST[ 'region' ];
$provincial = $_POST[ 'provincial' ];
$station = $_POST[ 'station' ];
$bl_cl_leader = $_POST[ 'bl_cl_leader' ];
$bl_cl_record = $_POST[ 'bl_cl_record' ];

// save file
$file1 = $_FILES[ 'bl_cl_file' ][ 'tmp_name' ];
if ( is_uploaded_file( $file1 ) ) {
  $bl_cl_file = "uploaded/Doc/" . $_FILES[ 'bl_cl_file' ][ 'name' ];	
  move_uploaded_file( $file1, "../" . $bl_cl_file );  
} else {
  $bl_cl_file = '';
}

$sql_bl = "SELECT * FROM `wm_tb_blockade` WHERE bl_cl_aid=:bl_cl_aid ";
    $stmt = $pdo->prepare($sql_bl);
    $stmt->bindParam(':bl_cl_aid', $bl_cl_aid);
try{
    $stmt->execute();
}catch (PDOException $e) {
    echo '$sql_bl Failed: '. $e->getMessage();
}
    $row = $stmt->fetch(PDO::FETCH_ASSOC);

if($row) {
    $bl_cl_aid = $row['bl_cl_aid'];

    $sql = "UPDATE wm_tb_blockade SET " .
            "bl_cl_num = :bl_cl_num," .
            "bl_cl_date = :bl_cl_date," .
            "bl_cl_method = :bl_cl_method," .
            "bl_cl_search_warrant = :bl_cl_search_warrant," .
            "bl_cl_province = :bl_cl_province," .
            "bl_cl_amphur = :bl_cl_amphur," .
            "bl_cl_tumbon = :bl_cl_tumbon," .
            "bl_cl_moo = :bl_cl_moo," .
            "bl_cl_adr = :bl_cl_adr," .
            "bl_cl_conduct = :bl_cl_conduct," .
            "bl_cl_find_illegal = :bl_cl_find_illegal," .
            "bl_cl_find_person = :bl_cl_find_person," .
            "bl_cl_illegal_things = :bl_cl_illegal_things," .
            "bl_cl_remark = :bl_cl_remark," .
            "bl_cl_group = :bl_cl_group," .
            "region = :region," .
            "provincial = :provincial," .
            "station = :station," .
            "bl_cl_leader = :bl_cl_leader," .
            "bl_cl_record = :bl_cl_record " ;
        
        $params = [
            'bl_cl_num' => $bl_cl_num,
            'bl_cl_date' => $bl_cl_date,
            'bl_cl_method' => $bl_cl_method,
            'bl_cl_search_warrant' => $bl_cl_search_warrant,
            'bl_cl_province' => $bl_cl_province,
            'bl_cl_amphur' => $bl_cl_amphur,
            'bl_cl_tumbon' => $bl_cl_tumbon,
            'bl_cl_moo' => $bl_cl_moo,
            'bl_cl_adr' => $bl_cl_adr,
            'bl_cl_conduct' => $bl_cl_conduct,
            'bl_cl_find_illegal' => $bl_cl_find_illegal,
            'bl_cl_find_person' => $bl_cl_find_person,
            'bl_cl_illegal_things' => $bl_cl_illegal_things,
            'bl_cl_remark' => $bl_cl_remark,
            'bl_cl_group' => $bl_cl_group,
            'region' => $region,
            'provincial' => $provincial,
            'station' => $station,
            'bl_cl_leader' => $bl_cl_leader,
            'bl_cl_record' => $bl_cl_record
            ];
    
            if ($bl_cl_file !== '') {
            $sql .= ", bl_cl_file = :bl_cl_file";
            $params['bl_cl_file'] = $bl_cl_file;
            }

            $sql .= " WHERE bl_cl_aid = :updated_bl_cl_aid";
            $params['updated_bl_cl_aid'] = $bl_cl_aid;
    
}else{
        $sql = "INSERT INTO wm_tb_blockade(bl_cl_num, bl_cl_date, bl_cl_method, bl_cl_search_warrant, bl_cl_province, bl_cl_amphur, bl_cl_tumbon, bl_cl_moo, bl_cl_adr, bl_cl_conduct, bl_cl_find_illegal, bl_cl_find_person, bl_cl_illegal_things, bl_cl_remark, bl_cl_group, region, provincial, station, bl_cl_leader, bl_cl_record, bl_cl_file) VALUES(:bl_cl_num, :bl_cl_date, :bl_cl_method, :bl_cl_search_warrant, :bl_cl_province, :bl_cl_amphur, :bl_cl_tumbon, :bl_cl_moo, :bl_cl_adr, :bl_cl_conduct, :bl_cl_find_illegal, :bl_cl_find_person, :bl_cl_illegal_things, :bl_cl_remark, :bl_cl_group, :region, :provincial, :station, :bl_cl_leader, :bl_cl_record, :bl_cl_file) ";
    
            $params = [
            'bl_cl_num' => $bl_cl_num,
            'bl_cl_date' => $bl_cl_date,
            'bl_cl_method' => $bl_cl_method,
            'bl_cl_search_warrant' => $bl_cl_search_warrant,
            'bl_cl_province' => $bl_cl_province,
            'bl_cl_amphur' => $bl_cl_amphur,
            'bl_cl_tumbon' => $bl_cl_tumbon,
            'bl_cl_moo' => $bl_cl_moo,
            'bl_cl_adr' => $bl_cl_adr,
            'bl_cl_conduct' => $bl_cl_conduct,
            'bl_cl_find_illegal' => $bl_cl_find_illegal,
            'bl_cl_find_person' => $bl_cl_find_person,
            'bl_cl_illegal_things' => $bl_cl_illegal_things,
            'bl_cl_remark' => $bl_cl_remark,
            'bl_cl_group' => $bl_cl_group,
            'region' => $region,
            'provincial' => $provincial,
            'station' => $station,
            'bl_cl_leader' => $bl_cl_leader,
            'bl_cl_record' => $bl_cl_record,
            'bl_cl_file' => $bl_cl_file
            ];
}

$stmt = $pdo->prepare($sql);

try{
    $result = $stmt->execute($params);
}catch(PDOException $e){
    echo 'Query $result Failed: '. $e->getMessage();
}

if ($result) {
    $_SESSION['success'] = "Data has been inserted succesfully";
    showSweetAlert("Saved successfully", "บันทึกข้อมูลเรียบร้อย");
    unset($_SESSION['success']); // Clear the session variable
    header("refresh:2; url=/Bansuan/index.php?pcid={$bl_cl_aid}&page=blockade");
} else {
    $_SESSION['error'] = "Error";
    showSweetAlert('Error', "บันทึกข้อมูล ไม่สำเร็จ", 'error');
    unset($_SESSION['error']); // Clear the session variable
    header("refresh:2; url=/Bansuan/index.php?pcid={$bl_cl_aid}&page=blockade");
}

$pdo = null;

?>