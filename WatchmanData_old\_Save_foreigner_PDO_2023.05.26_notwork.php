<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../Alert.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);
$ip_address = $_SERVER['REMOTE_ADDR'];
$action = 'Save foreigner'; // You can set the action to a suitable value
$data = str_replace("'", "", compact_array($_POST));
log_activity($acc, $action, $data, $ip_address); // ใช้ function ในไฟล์ Condb.php

/*echo '<pre>';
print_r($_POST);
echo '</pre>';

echo '<hr>';

echo '<pre>';
var_dump($_POST);
echo '</pre>';

exit();*/

$fo_cl_aid = $_POST['fo_cl_aid'];
$fo_cl_type_passport = $_POST['fo_cl_type_passport'];
$fo_cl_passport = $_POST['fo_cl_passport'];
$fo_cl_nationality = $_POST['fo_cl_nationality'];
$fo_cl_prefix_eng = $_POST['fo_cl_prefix_eng'];
$fo_cl_sex = $_POST['fo_cl_sex'];
$fo_cl_name = $_POST['fo_cl_name'];
$fo_cl_midname = $_POST['fo_cl_midname'];
$fo_cl_surname = $_POST['fo_cl_surname'];
$fo_cl_birthday = $_POST['fo_cl_birthday'];
$fo_cl_province = $_POST['fo_cl_province'];
$fo_cl_amphur = $_POST['fo_cl_amphur'];
$fo_cl_tumbon = $_POST['fo_cl_tumbon'];
$fo_cl_moo = $_POST['fo_cl_moo'];
$fo_cl_road = $_POST['fo_cl_road'];
$fo_cl_adr_building = $_POST['fo_cl_adr_building'];
$fo_cl_adr = $_POST['fo_cl_adr'];
$fo_cl_country = $_POST['fo_cl_country'];
$fo_cl_date_in = $_POST['fo_cl_date_in'];
$fo_cl_imm_chk_in = $_POST['fo_cl_imm_chk_in'];
$fo_cl_stay_exp = $_POST['fo_cl_stay_exp'];
$fo_cl_workpermit_no = $_POST['fo_cl_workpermit_no'];
$fo_cl_position = $_POST['fo_cl_position'];
$fo_cl_workpermit_exp = $_POST['fo_cl_workpermit_exp'];
$fo_cl_status = $_POST['fo_cl_status'];
$fo_cl_reason = $_POST['fo_cl_reason'];
$fo_cl_employer_no = $_POST['fo_cl_employer_no'];
$fo_cl_employer_name = $_POST['fo_cl_employer_name'];
$fo_cl_employer_type = $_POST['fo_cl_employer_type'];
$fo_cl_employer_address = $_POST['fo_cl_employer_address'];
$fo_cl_employer_phone = $_POST['fo_cl_employer_phone'];
$region = $_POST['region'];
$provincial = $_POST['provincial'];
$station = $_POST['station'];

// save Image ใหม่แทน
$file1 = $_FILES[ 'fo_cl_image' ][ 'tmp_name' ];

if ( is_uploaded_file( $file1 ) ) {

    //ลบรูปเก่าในเซิร์ฟเวอร์ก่อน
    $sql = "SELECT * FROM wm_tb_foreigner WHERE fo_cl_aid = :fo_cl_aid ";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':fo_cl_aid', $fo_cl_aid);
        $stmt->execute();
    if($row = $stmt->fetch(PDO::FETCH_ASSOC))
    {
        if(($row['fo_cl_image'] != '') && file_exists($row['fo_cl_image'])) {
            unlink( $row['fo_cl_image'] );
        }    
    }

    //เปลี่ยนชื่อไฟล์ใหม่
    $fo_cl_image = "/policeinnopolis/uploaded/Image1/" . basename( $_FILES[ 'fo_cl_image' ][ 'name' ] );	    
    // file + ext    
    $ext = strrchr($fo_cl_image, ".");    
    $fo_cl_image = "../policeinnopolis/uploaded/Image1/File_" . time() . $ext; // file+time+ext        
    move_uploaded_file( $file1, $fo_cl_image );  
} 
else {
  $fo_cl_image = '';
}

$fo_cl_age = get_personal_age_2($fo_cl_birthday);

//แปลงปี จากปีไทย เป็นปีอังกฤษ
//$fo_cl_birthday = thai_date_2_eng($fo_cl_birthday);
//เมื่อได้วันเกิดอังกฤษแล้ว ใช้ฟังก์ชั่นแปลงวันที่ เป็นอายุ
//$fo_cl_age = get_personal_age_2($fo_cl_birthday);

try{
// check exists data in personal เช็คว่ามีข้อมูลในตาราง Personal หรือยัง ถ้ามีแล้ว ไม่ต้องทำอะไร
$sql1 = "SELECT * FROM wm_tb_foreigner WHERE fo_cl_passport = :fo_cl_passport ";
    $stmt = $pdo->prepare($sql1);
    $stmt->bindParam(':fo_cl_passport', $fo_cl_passport);
    $stmt->execute();
    $row1 = $stmt->fetch(PDO::FETCH_ASSOC);

// มีข้อมูลแล้ว
if($row1 > 0 ){
    // วันเกิด เป็นค่า่ว่าง + มีภาพ
    if($fo_cl_birthday == "" && $fo_cl_image !== ''){
        $sql1 =  "UPDATE wm_tb_foreigner SET " .
                "fo_cl_type_passport = :fo_cl_type_passport, " .
                "fo_cl_passport = :fo_cl_passport, " .
                "fo_cl_nationality = :fo_cl_nationality, " .
                "fo_cl_prefix_eng = :fo_cl_prefix_eng, " .
                "fo_cl_sex = :fo_cl_sex, " .
                "fo_cl_name = :fo_cl_name, " .
                "fo_cl_midname = :fo_cl_midname, " .
                "fo_cl_surname = :fo_cl_surname, " .
                "fo_cl_province = :fo_cl_province, " .
                "fo_cl_amphur = :fo_cl_amphur, " .
                "fo_cl_tumbon = :fo_cl_tumbon, " .
                "fo_cl_moo = :fo_cl_moo, " .
                "fo_cl_road = :fo_cl_road, " .
                "fo_cl_adr_building = :fo_cl_adr_building, " .
                "fo_cl_adr = :fo_cl_adr, " .
                "fo_cl_country = :fo_cl_country, " .
                "fo_cl_date_in = :fo_cl_date_in, " .
                "fo_cl_imm_chk_in = :fo_cl_imm_chk_in, " .
                "fo_cl_stay_exp = :fo_cl_stay_exp, " .
                "fo_cl_workpermit_no = :fo_cl_workpermit_no, " .
                "fo_cl_position = :fo_cl_position, " .
                "fo_cl_workpermit_exp = :fo_cl_workpermit_exp, " .
                "fo_cl_status = :fo_cl_status, " .
                "fo_cl_reason = :fo_cl_reason, " .
                "fo_cl_employer_no = :fo_cl_employer_no, " .
                "fo_cl_employer_name = :fo_cl_employer_name, " .
                "fo_cl_employer_type = :fo_cl_employer_type, " .
                "fo_cl_employer_address = :fo_cl_employer_address, " .
                "fo_cl_employer_phone = :fo_cl_employer_phone, " .
                "region = :region, " .
                "provincial = :provincial, " .
                "station = :station, " .
                "fo_cl_image = :fo_cl_image " .
                "WHERE fo_cl_aid = :fo_cl_aid ";
                $stmt = $pdo->prepare($sql1);
                $stmt->bindParam(':fo_cl_aid', $fo_cl_aid);
                $stmt->bindParam(':fo_cl_type_passport', $fo_cl_type_passport);
                $stmt->bindParam(':fo_cl_passport', $fo_cl_passport);
                $stmt->bindParam(':fo_cl_nationality', $fo_cl_nationality);
                $stmt->bindParam(':fo_cl_prefix_eng', $fo_cl_prefix_eng);
                $stmt->bindParam(':fo_cl_sex', $fo_cl_sex);
                $stmt->bindParam(':fo_cl_name', $fo_cl_name);
                $stmt->bindParam(':fo_cl_midname', $fo_cl_midname);
                $stmt->bindParam(':fo_cl_surname', $fo_cl_surname);
                $stmt->bindParam(':fo_cl_province', $fo_cl_province);
                $stmt->bindParam(':fo_cl_amphur', $fo_cl_amphur);
                $stmt->bindParam(':fo_cl_tumbon', $fo_cl_tumbon);
                $stmt->bindParam(':fo_cl_moo', $fo_cl_moo);
                $stmt->bindParam(':fo_cl_road', $fo_cl_road);
                $stmt->bindParam(':fo_cl_adr_building', $fo_cl_adr_building);
                $stmt->bindParam(':fo_cl_adr', $fo_cl_adr);
                $stmt->bindParam(':fo_cl_country', $fo_cl_country);
                $stmt->bindParam(':fo_cl_date_in', $fo_cl_date_in);
                $stmt->bindParam(':fo_cl_imm_chk_in', $fo_cl_imm_chk_in);
                $stmt->bindParam(':fo_cl_stay_exp', $fo_cl_stay_exp);
                $stmt->bindParam(':fo_cl_workpermit_no', $fo_cl_workpermit_no);
                $stmt->bindParam(':fo_cl_position', $fo_cl_position);
                $stmt->bindParam(':fo_cl_workpermit_exp', $fo_cl_workpermit_exp);
                $stmt->bindParam(':fo_cl_status', $fo_cl_status);
                $stmt->bindParam(':fo_cl_reason', $fo_cl_reason);
                $stmt->bindParam(':fo_cl_employer_no', $fo_cl_employer_no);
                $stmt->bindParam(':fo_cl_employer_name', $fo_cl_employer_name);
                $stmt->bindParam(':fo_cl_employer_type', $fo_cl_employer_type);
                $stmt->bindParam(':fo_cl_employer_address', $fo_cl_employer_address);
                $stmt->bindParam(':fo_cl_employer_phone', $fo_cl_employer_phone);
                $stmt->bindParam(':region', $region);
                $stmt->bindParam(':provincial', $provincial);
                $stmt->bindParam(':station', $station);
                $stmt->bindParam(':fo_cl_image', $fo_cl_image);
        // วันเกิด เป็นค่า่ว่าง + ไม่มีภาพ
    }elseif($fo_cl_birthday == "" && $fo_cl_image == ''){
            $sql1 =  "UPDATE wm_tb_foreigner SET " .
                "fo_cl_type_passport = :fo_cl_type_passport, " .
                "fo_cl_passport = :fo_cl_passport, " .
                "fo_cl_nationality = :fo_cl_nationality, " .
                "fo_cl_prefix_eng = :fo_cl_prefix_eng, " .
                "fo_cl_sex = :fo_cl_sex, " .
                "fo_cl_name = :fo_cl_name, " .
                "fo_cl_midname = :fo_cl_midname, " .
                "fo_cl_surname = :fo_cl_surname, " .
                "fo_cl_province = :fo_cl_province, " .
                "fo_cl_amphur = :fo_cl_amphur, " .
                "fo_cl_tumbon = :fo_cl_tumbon, " .
                "fo_cl_moo = :fo_cl_moo, " .
                "fo_cl_road = :fo_cl_road, " .
                "fo_cl_adr_building = :fo_cl_adr_building, " .
                "fo_cl_adr = :fo_cl_adr, " .
                "fo_cl_country = :fo_cl_country, " .
                "fo_cl_date_in = :fo_cl_date_in, " .
                "fo_cl_imm_chk_in = :fo_cl_imm_chk_in, " .
                "fo_cl_stay_exp = :fo_cl_stay_exp, " .
                "fo_cl_workpermit_no = :fo_cl_workpermit_no, " .
                "fo_cl_position = :fo_cl_position, " .
                "fo_cl_workpermit_exp = :fo_cl_workpermit_exp, " .
                "fo_cl_status = :fo_cl_status, " .
                "fo_cl_reason = :fo_cl_reason, " .
                "fo_cl_employer_no = :fo_cl_employer_no, " .
                "fo_cl_employer_name = :fo_cl_employer_name, " .
                "fo_cl_employer_type = :fo_cl_employer_type, " .
                "fo_cl_employer_address = :fo_cl_employer_address, " .
                "fo_cl_employer_phone = :fo_cl_employer_phone, " .
                "region = :region, " .
                "provincial = :provincial, " .
                "station = :station " .
                "WHERE fo_cl_aid = :fo_cl_aid ";
                $stmt = $pdo->prepare($sql1);
                $stmt->bindParam(':fo_cl_aid', $fo_cl_aid);
                $stmt->bindParam(':fo_cl_type_passport', $fo_cl_type_passport);
                $stmt->bindParam(':fo_cl_passport', $fo_cl_passport);
                $stmt->bindParam(':fo_cl_nationality', $fo_cl_nationality);
                $stmt->bindParam(':fo_cl_prefix_eng', $fo_cl_prefix_eng);
                $stmt->bindParam(':fo_cl_sex', $fo_cl_sex);
                $stmt->bindParam(':fo_cl_name', $fo_cl_name);
                $stmt->bindParam(':fo_cl_midname', $fo_cl_midname);
                $stmt->bindParam(':fo_cl_surname', $fo_cl_surname);
                $stmt->bindParam(':fo_cl_province', $fo_cl_province);
                $stmt->bindParam(':fo_cl_amphur', $fo_cl_amphur);
                $stmt->bindParam(':fo_cl_tumbon', $fo_cl_tumbon);
                $stmt->bindParam(':fo_cl_moo', $fo_cl_moo);
                $stmt->bindParam(':fo_cl_road', $fo_cl_road);
                $stmt->bindParam(':fo_cl_adr_building', $fo_cl_adr_building);
                $stmt->bindParam(':fo_cl_adr', $fo_cl_adr);
                $stmt->bindParam(':fo_cl_country', $fo_cl_country);
                $stmt->bindParam(':fo_cl_date_in', $fo_cl_date_in);
                $stmt->bindParam(':fo_cl_imm_chk_in', $fo_cl_imm_chk_in);
                $stmt->bindParam(':fo_cl_stay_exp', $fo_cl_stay_exp);
                $stmt->bindParam(':fo_cl_workpermit_no', $fo_cl_workpermit_no);
                $stmt->bindParam(':fo_cl_position', $fo_cl_position);
                $stmt->bindParam(':fo_cl_workpermit_exp', $fo_cl_workpermit_exp);
                $stmt->bindParam(':fo_cl_status', $fo_cl_status);
                $stmt->bindParam(':fo_cl_reason', $fo_cl_reason);
                $stmt->bindParam(':fo_cl_employer_no', $fo_cl_employer_no);
                $stmt->bindParam(':fo_cl_employer_name', $fo_cl_employer_name);
                $stmt->bindParam(':fo_cl_employer_type', $fo_cl_employer_type);
                $stmt->bindParam(':fo_cl_employer_address', $fo_cl_employer_address);
                $stmt->bindParam(':fo_cl_employer_phone', $fo_cl_employer_phone);
                $stmt->bindParam(':region', $region);
                $stmt->bindParam(':provincial', $provincial);
                $stmt->bindParam(':station', $station);
           // มีวันเกิด + มีภาพ
        }elseif($fo_cl_birthday !== "" && $fo_cl_image !== ''){
            $sql1 =  "UPDATE wm_tb_foreigner SET " .
                "fo_cl_type_passport = :fo_cl_type_passport, " .
                "fo_cl_passport = :fo_cl_passport, " .
                "fo_cl_nationality = :fo_cl_nationality, " .
                "fo_cl_prefix_eng = :fo_cl_prefix_eng, " .
                "fo_cl_sex = :fo_cl_sex, " .
                "fo_cl_name = :fo_cl_name, " .
                "fo_cl_midname = :fo_cl_midname, " .
                "fo_cl_surname = :fo_cl_surname, " .
                "fo_cl_birthday = :fo_cl_birthday, " .
                "fo_cl_province = :fo_cl_province, " .
                "fo_cl_amphur = :fo_cl_amphur, " .
                "fo_cl_tumbon = :fo_cl_tumbon, " .
                "fo_cl_moo = :fo_cl_moo, " .
                "fo_cl_road = :fo_cl_road, " .
                "fo_cl_adr_building = :fo_cl_adr_building, " .
                "fo_cl_adr = :fo_cl_adr, " .
                "fo_cl_country = :fo_cl_country, " .
                "fo_cl_date_in = :fo_cl_date_in, " .
                "fo_cl_imm_chk_in = :fo_cl_imm_chk_in, " .
                "fo_cl_stay_exp = :fo_cl_stay_exp, " .
                "fo_cl_workpermit_no = :fo_cl_workpermit_no, " .
                "fo_cl_position = :fo_cl_position, " .
                "fo_cl_workpermit_exp = :fo_cl_workpermit_exp, " .
                "fo_cl_status = :fo_cl_status, " .
                "fo_cl_reason = :fo_cl_reason, " .
                "fo_cl_employer_no = :fo_cl_employer_no, " .
                "fo_cl_employer_name = :fo_cl_employer_name, " .
                "fo_cl_employer_type = :fo_cl_employer_type, " .
                "fo_cl_employer_address = :fo_cl_employer_address, " .
                "fo_cl_employer_phone = :fo_cl_employer_phone, " .
                "region = :region, " .
                "provincial = :provincial, " .
                "station = :station, " .
                "fo_cl_image = :fo_cl_image " .
                "WHERE fo_cl_aid = :fo_cl_aid ";
                $stmt = $pdo->prepare($sql1);
                $stmt->bindParam(':fo_cl_aid', $fo_cl_aid);
                $stmt->bindParam(':fo_cl_type_passport', $fo_cl_type_passport);
                $stmt->bindParam(':fo_cl_passport', $fo_cl_passport);
                $stmt->bindParam(':fo_cl_nationality', $fo_cl_nationality);
                $stmt->bindParam(':fo_cl_prefix_eng', $fo_cl_prefix_eng);
                $stmt->bindParam(':fo_cl_sex', $fo_cl_sex);
                $stmt->bindParam(':fo_cl_name', $fo_cl_name);
                $stmt->bindParam(':fo_cl_midname', $fo_cl_midname);
                $stmt->bindParam(':fo_cl_surname', $fo_cl_surname);
                $stmt->bindParam(':fo_cl_birthday', $fo_cl_birthday);
                $stmt->bindParam(':fo_cl_province', $fo_cl_province);
                $stmt->bindParam(':fo_cl_amphur', $fo_cl_amphur);
                $stmt->bindParam(':fo_cl_tumbon', $fo_cl_tumbon);
                $stmt->bindParam(':fo_cl_moo', $fo_cl_moo);
                $stmt->bindParam(':fo_cl_road', $fo_cl_road);
                $stmt->bindParam(':fo_cl_adr_building', $fo_cl_adr_building);
                $stmt->bindParam(':fo_cl_adr', $fo_cl_adr);
                $stmt->bindParam(':fo_cl_country', $fo_cl_country);
                $stmt->bindParam(':fo_cl_date_in', $fo_cl_date_in);
                $stmt->bindParam(':fo_cl_imm_chk_in', $fo_cl_imm_chk_in);
                $stmt->bindParam(':fo_cl_stay_exp', $fo_cl_stay_exp);
                $stmt->bindParam(':fo_cl_workpermit_no', $fo_cl_workpermit_no);
                $stmt->bindParam(':fo_cl_position', $fo_cl_position);
                $stmt->bindParam(':fo_cl_workpermit_exp', $fo_cl_workpermit_exp);
                $stmt->bindParam(':fo_cl_status', $fo_cl_status);
                $stmt->bindParam(':fo_cl_reason', $fo_cl_reason);
                $stmt->bindParam(':fo_cl_employer_no', $fo_cl_employer_no);
                $stmt->bindParam(':fo_cl_employer_name', $fo_cl_employer_name);
                $stmt->bindParam(':fo_cl_employer_type', $fo_cl_employer_type);
                $stmt->bindParam(':fo_cl_employer_address', $fo_cl_employer_address);
                $stmt->bindParam(':fo_cl_employer_phone', $fo_cl_employer_phone);
                $stmt->bindParam(':region', $region);
                $stmt->bindParam(':provincial', $provincial);
                $stmt->bindParam(':station', $station);
                $stmt->bindParam(':fo_cl_image', $fo_cl_image);
        
            }elseif($fo_cl_birthday !== "" && $fo_cl_image == ''){
                // มีวันเกิด + ไม่มีภาพ
                $sql1 =  "UPDATE wm_tb_foreigner SET " .
                "fo_cl_type_passport = :fo_cl_type_passport, " .
                "fo_cl_passport = :fo_cl_passport, " .
                "fo_cl_nationality = :fo_cl_nationality, " .
                "fo_cl_prefix_eng = :fo_cl_prefix_eng, " .
                "fo_cl_sex = :fo_cl_sex, " .
                "fo_cl_name = :fo_cl_name, " .
                "fo_cl_midname = :fo_cl_midname, " .
                "fo_cl_surname = :fo_cl_surname, " .
                "fo_cl_birthday = :fo_cl_birthday, " .
                "fo_cl_province = :fo_cl_province, " .
                "fo_cl_amphur = :fo_cl_amphur, " .
                "fo_cl_tumbon = :fo_cl_tumbon, " .
                "fo_cl_moo = :fo_cl_moo, " .
                "fo_cl_road = :fo_cl_road, " .
                "fo_cl_adr_building = :fo_cl_adr_building, " .
                "fo_cl_adr = :fo_cl_adr, " .
                "fo_cl_country = :fo_cl_country, " .
                "fo_cl_date_in = :fo_cl_date_in, " .
                "fo_cl_imm_chk_in = :fo_cl_imm_chk_in, " .
                "fo_cl_stay_exp = :fo_cl_stay_exp, " .
                "fo_cl_workpermit_no = :fo_cl_workpermit_no, " .
                "fo_cl_position = :fo_cl_position, " .
                "fo_cl_workpermit_exp = :fo_cl_workpermit_exp, " .
                "fo_cl_status = :fo_cl_status, " .
                "fo_cl_reason = :fo_cl_reason, " .
                "fo_cl_employer_no = :fo_cl_employer_no, " .
                "fo_cl_employer_name = :fo_cl_employer_name, " .
                "fo_cl_employer_type = :fo_cl_employer_type, " .
                "fo_cl_employer_address = :fo_cl_employer_address, " .
                "fo_cl_employer_phone = :fo_cl_employer_phone, " .
                "region = :region, " .
                "provincial = :provincial, " .
                "station = :station " .
                "WHERE fo_cl_aid = :fo_cl_aid ";
                $stmt = $pdo->prepare($sql1);
                $stmt->bindParam(':fo_cl_aid', $fo_cl_aid);
                $stmt->bindParam(':fo_cl_type_passport', $fo_cl_type_passport);
                $stmt->bindParam(':fo_cl_passport', $fo_cl_passport);
                $stmt->bindParam(':fo_cl_nationality', $fo_cl_nationality);
                $stmt->bindParam(':fo_cl_prefix_eng', $fo_cl_prefix_eng);
                $stmt->bindParam(':fo_cl_sex', $fo_cl_sex);
                $stmt->bindParam(':fo_cl_name', $fo_cl_name);
                $stmt->bindParam(':fo_cl_midname', $fo_cl_midname);
                $stmt->bindParam(':fo_cl_surname', $fo_cl_surname);
                $stmt->bindParam(':fo_cl_birthday', $fo_cl_birthday);
                $stmt->bindParam(':fo_cl_province', $fo_cl_province);
                $stmt->bindParam(':fo_cl_amphur', $fo_cl_amphur);
                $stmt->bindParam(':fo_cl_tumbon', $fo_cl_tumbon);
                $stmt->bindParam(':fo_cl_moo', $fo_cl_moo);
                $stmt->bindParam(':fo_cl_road', $fo_cl_road);
                $stmt->bindParam(':fo_cl_adr_building', $fo_cl_adr_building);
                $stmt->bindParam(':fo_cl_adr', $fo_cl_adr);
                $stmt->bindParam(':fo_cl_country', $fo_cl_country);
                $stmt->bindParam(':fo_cl_date_in', $fo_cl_date_in);
                $stmt->bindParam(':fo_cl_imm_chk_in', $fo_cl_imm_chk_in);
                $stmt->bindParam(':fo_cl_stay_exp', $fo_cl_stay_exp);
                $stmt->bindParam(':fo_cl_workpermit_no', $fo_cl_workpermit_no);
                $stmt->bindParam(':fo_cl_position', $fo_cl_position);
                $stmt->bindParam(':fo_cl_workpermit_exp', $fo_cl_workpermit_exp);
                $stmt->bindParam(':fo_cl_status', $fo_cl_status);
                $stmt->bindParam(':fo_cl_reason', $fo_cl_reason);
                $stmt->bindParam(':fo_cl_employer_no', $fo_cl_employer_no);
                $stmt->bindParam(':fo_cl_employer_name', $fo_cl_employer_name);
                $stmt->bindParam(':fo_cl_employer_type', $fo_cl_employer_type);
                $stmt->bindParam(':fo_cl_employer_address', $fo_cl_employer_address);
                $stmt->bindParam(':fo_cl_employer_phone', $fo_cl_employer_phone);
                $stmt->bindParam(':region', $region);
                $stmt->bindParam(':provincial', $provincial);
                $stmt->bindParam(':station', $station);
            }

} else {
    // บันทึกข้อมูลใหม่  ไม่มีวันเกิด + มีภาพ
    if($fo_cl_birthday == "" && $fo_cl_image !== ''){
        $sql = "INSERT INTO wm_tb_foreigner (fo_cl_type_passport, fo_cl_passport, fo_cl_nationality, fo_cl_prefix_eng, fo_cl_sex, fo_cl_name, fo_cl_midname, fo_cl_surname, fo_cl_province, fo_cl_amphur, fo_cl_tumbon, fo_cl_moo, fo_cl_road, fo_cl_adr_building, fo_cl_adr, fo_cl_country, fo_cl_date_in, fo_cl_imm_chk_in, fo_cl_stay_exp, fo_cl_workpermit_no, fo_cl_position, fo_cl_workpermit_exp, fo_cl_status, fo_cl_reason, fo_cl_employer_no, fo_cl_employer_name, fo_cl_employer_type, fo_cl_employer_address, fo_cl_employer_phone, region, provincial, station, fo_cl_image) VALUES(:fo_cl_type_passport, :fo_cl_passport, :fo_cl_nationality, :fo_cl_prefix_eng, :fo_cl_sex, :fo_cl_name, :fo_cl_midname, :fo_cl_surname, :fo_cl_province, :fo_cl_amphur, :fo_cl_tumbon, :fo_cl_moo, :fo_cl_road, :fo_cl_adr_building, :fo_cl_adr, :fo_cl_country, :fo_cl_date_in, :fo_cl_imm_chk_in, :fo_cl_stay_exp, :fo_cl_workpermit_no, :fo_cl_position, :fo_cl_workpermit_exp, :fo_cl_status, :fo_cl_reason, :fo_cl_employer_no, :fo_cl_employer_name, :fo_cl_employer_type, :fo_cl_employer_address, :fo_cl_employer_phone, :region, :provincial, :station, :fo_cl_image) ";
                $stmt = $pdo->prepare($sql1);
                $stmt->bindParam(':fo_cl_aid', $fo_cl_aid);
                $stmt->bindParam(':fo_cl_type_passport', $fo_cl_type_passport);
                $stmt->bindParam(':fo_cl_passport', $fo_cl_passport);
                $stmt->bindParam(':fo_cl_nationality', $fo_cl_nationality);
                $stmt->bindParam(':fo_cl_prefix_eng', $fo_cl_prefix_eng);
                $stmt->bindParam(':fo_cl_sex', $fo_cl_sex);
                $stmt->bindParam(':fo_cl_name', $fo_cl_name);
                $stmt->bindParam(':fo_cl_midname', $fo_cl_midname);
                $stmt->bindParam(':fo_cl_surname', $fo_cl_surname);
                $stmt->bindParam(':fo_cl_province', $fo_cl_province);
                $stmt->bindParam(':fo_cl_amphur', $fo_cl_amphur);
                $stmt->bindParam(':fo_cl_tumbon', $fo_cl_tumbon);
                $stmt->bindParam(':fo_cl_moo', $fo_cl_moo);
                $stmt->bindParam(':fo_cl_road', $fo_cl_road);
                $stmt->bindParam(':fo_cl_adr_building', $fo_cl_adr_building);
                $stmt->bindParam(':fo_cl_adr', $fo_cl_adr);
                $stmt->bindParam(':fo_cl_country', $fo_cl_country);
                $stmt->bindParam(':fo_cl_date_in', $fo_cl_date_in);
                $stmt->bindParam(':fo_cl_imm_chk_in', $fo_cl_imm_chk_in);
                $stmt->bindParam(':fo_cl_stay_exp', $fo_cl_stay_exp);
                $stmt->bindParam(':fo_cl_workpermit_no', $fo_cl_workpermit_no);
                $stmt->bindParam(':fo_cl_position', $fo_cl_position);
                $stmt->bindParam(':fo_cl_workpermit_exp', $fo_cl_workpermit_exp);
                $stmt->bindParam(':fo_cl_status', $fo_cl_status);
                $stmt->bindParam(':fo_cl_reason', $fo_cl_reason);
                $stmt->bindParam(':fo_cl_employer_no', $fo_cl_employer_no);
                $stmt->bindParam(':fo_cl_employer_name', $fo_cl_employer_name);
                $stmt->bindParam(':fo_cl_employer_type', $fo_cl_employer_type);
                $stmt->bindParam(':fo_cl_employer_address', $fo_cl_employer_address);
                $stmt->bindParam(':fo_cl_employer_phone', $fo_cl_employer_phone);
                $stmt->bindParam(':region', $region);
                $stmt->bindParam(':provincial', $provincial);
                $stmt->bindParam(':station', $station);
                $stmt->bindParam(':fo_cl_image', $fo_cl_image);
        
    } elseif($fo_cl_birthday == "" && $fo_cl_image == ''){
        // ไม่มีวันเกิด + ไม่มีภาพ
        $sql = "INSERT INTO wm_tb_foreigner (fo_cl_type_passport, fo_cl_passport, fo_cl_nationality, fo_cl_prefix_eng, fo_cl_sex, fo_cl_name, fo_cl_midname, fo_cl_surname, fo_cl_province, fo_cl_amphur, fo_cl_tumbon, fo_cl_moo, fo_cl_road, fo_cl_adr_building, fo_cl_adr, fo_cl_country, fo_cl_date_in, fo_cl_imm_chk_in, fo_cl_stay_exp, fo_cl_workpermit_no, fo_cl_position, fo_cl_workpermit_exp, fo_cl_status, fo_cl_reason, fo_cl_employer_no, fo_cl_employer_name, fo_cl_employer_type, fo_cl_employer_address, fo_cl_employer_phone, region, provincial, station) VALUES(:fo_cl_type_passport, :fo_cl_passport, :fo_cl_nationality, :fo_cl_prefix_eng, :fo_cl_sex, :fo_cl_name, :fo_cl_midname, :fo_cl_surname, :fo_cl_province, :fo_cl_amphur, :fo_cl_tumbon, :fo_cl_moo, :fo_cl_road, :fo_cl_adr_building, :fo_cl_adr, :fo_cl_country, :fo_cl_date_in, :fo_cl_imm_chk_in, :fo_cl_stay_exp, :fo_cl_workpermit_no, :fo_cl_position, :fo_cl_workpermit_exp, :fo_cl_status, :fo_cl_reason, :fo_cl_employer_no, :fo_cl_employer_name, :fo_cl_employer_type, :fo_cl_employer_address, :fo_cl_employer_phone, :region, :provincial, :station) ";
                $stmt = $pdo->prepare($sql1);
                $stmt->bindParam(':fo_cl_aid', $fo_cl_aid);
                $stmt->bindParam(':fo_cl_type_passport', $fo_cl_type_passport);
                $stmt->bindParam(':fo_cl_passport', $fo_cl_passport);
                $stmt->bindParam(':fo_cl_nationality', $fo_cl_nationality);
                $stmt->bindParam(':fo_cl_prefix_eng', $fo_cl_prefix_eng);
                $stmt->bindParam(':fo_cl_sex', $fo_cl_sex);
                $stmt->bindParam(':fo_cl_name', $fo_cl_name);
                $stmt->bindParam(':fo_cl_midname', $fo_cl_midname);
                $stmt->bindParam(':fo_cl_surname', $fo_cl_surname);
                $stmt->bindParam(':fo_cl_province', $fo_cl_province);
                $stmt->bindParam(':fo_cl_amphur', $fo_cl_amphur);
                $stmt->bindParam(':fo_cl_tumbon', $fo_cl_tumbon);
                $stmt->bindParam(':fo_cl_moo', $fo_cl_moo);
                $stmt->bindParam(':fo_cl_road', $fo_cl_road);
                $stmt->bindParam(':fo_cl_adr_building', $fo_cl_adr_building);
                $stmt->bindParam(':fo_cl_adr', $fo_cl_adr);
                $stmt->bindParam(':fo_cl_country', $fo_cl_country);
                $stmt->bindParam(':fo_cl_date_in', $fo_cl_date_in);
                $stmt->bindParam(':fo_cl_imm_chk_in', $fo_cl_imm_chk_in);
                $stmt->bindParam(':fo_cl_stay_exp', $fo_cl_stay_exp);
                $stmt->bindParam(':fo_cl_workpermit_no', $fo_cl_workpermit_no);
                $stmt->bindParam(':fo_cl_position', $fo_cl_position);
                $stmt->bindParam(':fo_cl_workpermit_exp', $fo_cl_workpermit_exp);
                $stmt->bindParam(':fo_cl_status', $fo_cl_status);
                $stmt->bindParam(':fo_cl_reason', $fo_cl_reason);
                $stmt->bindParam(':fo_cl_employer_no', $fo_cl_employer_no);
                $stmt->bindParam(':fo_cl_employer_name', $fo_cl_employer_name);
                $stmt->bindParam(':fo_cl_employer_type', $fo_cl_employer_type);
                $stmt->bindParam(':fo_cl_employer_address', $fo_cl_employer_address);
                $stmt->bindParam(':fo_cl_employer_phone', $fo_cl_employer_phone);
                $stmt->bindParam(':region', $region);
                $stmt->bindParam(':provincial', $provincial);
                $stmt->bindParam(':station', $station);
        // มีวันเกิด + มีภาพ
    }elseif($fo_cl_birthday !== "" && $fo_cl_image !== ''){
        $sql = "INSERT INTO wm_tb_foreigner (fo_cl_type_passport, fo_cl_passport, fo_cl_nationality, fo_cl_prefix_eng, fo_cl_sex, fo_cl_name, fo_cl_midname, fo_cl_surname, fo_cl_birthday, fo_cl_province, fo_cl_amphur, fo_cl_tumbon, fo_cl_moo, fo_cl_road, fo_cl_adr_building, fo_cl_adr, fo_cl_country, fo_cl_date_in, fo_cl_imm_chk_in, fo_cl_stay_exp, fo_cl_workpermit_no, fo_cl_position, fo_cl_workpermit_exp, fo_cl_status, fo_cl_reason, fo_cl_employer_no, fo_cl_employer_name, fo_cl_employer_type, fo_cl_employer_address, fo_cl_employer_phone, region, provincial, station, fo_cl_image) VALUES(:fo_cl_type_passport, :fo_cl_passport, :fo_cl_nationality, :fo_cl_prefix_eng, :fo_cl_sex, :fo_cl_name, :fo_cl_midname, :fo_cl_surname, :fo_cl_birthday, :fo_cl_province, :fo_cl_amphur, :fo_cl_tumbon, :fo_cl_moo, :fo_cl_road, :fo_cl_adr_building, :fo_cl_adr, :fo_cl_country, :fo_cl_date_in, :fo_cl_imm_chk_in, :fo_cl_stay_exp, :fo_cl_workpermit_no, :fo_cl_position, :fo_cl_workpermit_exp, :fo_cl_status, :fo_cl_reason, :fo_cl_employer_no, :fo_cl_employer_name, :fo_cl_employer_type, :fo_cl_employer_address, :fo_cl_employer_phone, :region, :provincial, :station, :fo_cl_image) ";
                $stmt = $pdo->prepare($sql1);
                $stmt->bindParam(':fo_cl_aid', $fo_cl_aid);
                $stmt->bindParam(':fo_cl_type_passport', $fo_cl_type_passport);
                $stmt->bindParam(':fo_cl_passport', $fo_cl_passport);
                $stmt->bindParam(':fo_cl_nationality', $fo_cl_nationality);
                $stmt->bindParam(':fo_cl_prefix_eng', $fo_cl_prefix_eng);
                $stmt->bindParam(':fo_cl_sex', $fo_cl_sex);
                $stmt->bindParam(':fo_cl_name', $fo_cl_name);
                $stmt->bindParam(':fo_cl_midname', $fo_cl_midname);
                $stmt->bindParam(':fo_cl_surname', $fo_cl_surname);
                $stmt->bindParam(':fo_cl_birthday', $fo_cl_birthday);
                $stmt->bindParam(':fo_cl_province', $fo_cl_province);
                $stmt->bindParam(':fo_cl_amphur', $fo_cl_amphur);
                $stmt->bindParam(':fo_cl_tumbon', $fo_cl_tumbon);
                $stmt->bindParam(':fo_cl_moo', $fo_cl_moo);
                $stmt->bindParam(':fo_cl_road', $fo_cl_road);
                $stmt->bindParam(':fo_cl_adr_building', $fo_cl_adr_building);
                $stmt->bindParam(':fo_cl_adr', $fo_cl_adr);
                $stmt->bindParam(':fo_cl_country', $fo_cl_country);
                $stmt->bindParam(':fo_cl_date_in', $fo_cl_date_in);
                $stmt->bindParam(':fo_cl_imm_chk_in', $fo_cl_imm_chk_in);
                $stmt->bindParam(':fo_cl_stay_exp', $fo_cl_stay_exp);
                $stmt->bindParam(':fo_cl_workpermit_no', $fo_cl_workpermit_no);
                $stmt->bindParam(':fo_cl_position', $fo_cl_position);
                $stmt->bindParam(':fo_cl_workpermit_exp', $fo_cl_workpermit_exp);
                $stmt->bindParam(':fo_cl_status', $fo_cl_status);
                $stmt->bindParam(':fo_cl_reason', $fo_cl_reason);
                $stmt->bindParam(':fo_cl_employer_no', $fo_cl_employer_no);
                $stmt->bindParam(':fo_cl_employer_name', $fo_cl_employer_name);
                $stmt->bindParam(':fo_cl_employer_type', $fo_cl_employer_type);
                $stmt->bindParam(':fo_cl_employer_address', $fo_cl_employer_address);
                $stmt->bindParam(':fo_cl_employer_phone', $fo_cl_employer_phone);
                $stmt->bindParam(':region', $region);
                $stmt->bindParam(':provincial', $provincial);
                $stmt->bindParam(':station', $station);
                $stmt->bindParam(':fo_cl_image', $fo_cl_image);
        // มีวันเกิด + ไม่มีภาพ
        }elseif($fo_cl_birthday !== "" && $fo_cl_image == ''){
        $sql = "INSERT INTO wm_tb_foreigner (fo_cl_type_passport, fo_cl_passport, fo_cl_nationality, fo_cl_prefix_eng, fo_cl_sex, fo_cl_name, fo_cl_midname, fo_cl_surname, fo_cl_birthday, fo_cl_province, fo_cl_amphur, fo_cl_tumbon, fo_cl_moo, fo_cl_road, fo_cl_adr_building, fo_cl_adr, fo_cl_country, fo_cl_date_in, fo_cl_imm_chk_in, fo_cl_stay_exp, fo_cl_workpermit_no, fo_cl_position, fo_cl_workpermit_exp, fo_cl_status, fo_cl_reason, fo_cl_employer_no, fo_cl_employer_name, fo_cl_employer_type, fo_cl_employer_address, fo_cl_employer_phone, region, provincial, station) VALUES(:fo_cl_type_passport, :fo_cl_passport, :fo_cl_nationality, :fo_cl_prefix_eng, :fo_cl_sex, :fo_cl_name, :fo_cl_midname, :fo_cl_surname, :fo_cl_birthday, :fo_cl_province, :fo_cl_amphur, :fo_cl_tumbon, :fo_cl_moo, :fo_cl_road, :fo_cl_adr_building, :fo_cl_adr, :fo_cl_country, :fo_cl_date_in, :fo_cl_imm_chk_in, :fo_cl_stay_exp, :fo_cl_workpermit_no, :fo_cl_position, :fo_cl_workpermit_exp, :fo_cl_status, :fo_cl_reason, :fo_cl_employer_no, :fo_cl_employer_name, :fo_cl_employer_type, :fo_cl_employer_address, :fo_cl_employer_phone, :region, :provincial, :station) ";
                $stmt = $pdo->prepare($sql1);
                $stmt->bindParam(':fo_cl_aid', $fo_cl_aid);
                $stmt->bindParam(':fo_cl_type_passport', $fo_cl_type_passport);
                $stmt->bindParam(':fo_cl_passport', $fo_cl_passport);
                $stmt->bindParam(':fo_cl_nationality', $fo_cl_nationality);
                $stmt->bindParam(':fo_cl_prefix_eng', $fo_cl_prefix_eng);
                $stmt->bindParam(':fo_cl_sex', $fo_cl_sex);
                $stmt->bindParam(':fo_cl_name', $fo_cl_name);
                $stmt->bindParam(':fo_cl_midname', $fo_cl_midname);
                $stmt->bindParam(':fo_cl_surname', $fo_cl_surname);
                $stmt->bindParam(':fo_cl_birthday', $fo_cl_birthday);
                $stmt->bindParam(':fo_cl_province', $fo_cl_province);
                $stmt->bindParam(':fo_cl_amphur', $fo_cl_amphur);
                $stmt->bindParam(':fo_cl_tumbon', $fo_cl_tumbon);
                $stmt->bindParam(':fo_cl_moo', $fo_cl_moo);
                $stmt->bindParam(':fo_cl_road', $fo_cl_road);
                $stmt->bindParam(':fo_cl_adr_building', $fo_cl_adr_building);
                $stmt->bindParam(':fo_cl_adr', $fo_cl_adr);
                $stmt->bindParam(':fo_cl_country', $fo_cl_country);
                $stmt->bindParam(':fo_cl_date_in', $fo_cl_date_in);
                $stmt->bindParam(':fo_cl_imm_chk_in', $fo_cl_imm_chk_in);
                $stmt->bindParam(':fo_cl_stay_exp', $fo_cl_stay_exp);
                $stmt->bindParam(':fo_cl_workpermit_no', $fo_cl_workpermit_no);
                $stmt->bindParam(':fo_cl_position', $fo_cl_position);
                $stmt->bindParam(':fo_cl_workpermit_exp', $fo_cl_workpermit_exp);
                $stmt->bindParam(':fo_cl_status', $fo_cl_status);
                $stmt->bindParam(':fo_cl_reason', $fo_cl_reason);
                $stmt->bindParam(':fo_cl_employer_no', $fo_cl_employer_no);
                $stmt->bindParam(':fo_cl_employer_name', $fo_cl_employer_name);
                $stmt->bindParam(':fo_cl_employer_type', $fo_cl_employer_type);
                $stmt->bindParam(':fo_cl_employer_address', $fo_cl_employer_address);
                $stmt->bindParam(':fo_cl_employer_phone', $fo_cl_employer_phone);
                $stmt->bindParam(':region', $region);
                $stmt->bindParam(':provincial', $provincial);
                $stmt->bindParam(':station', $station);
    }
}
    
//echo( $sql1 );
$result = $stmt->execute();

    if ($result) {
        $_SESSION['success'] = "Data has been inserted succesfully";
        showSweetAlert("Saved successfully", "บันทึกข้อมูลเรียบร้อย");
        unset($_SESSION['success']); // Clear the session variable
        header("refresh:2; url=/Bansuan/index.php?pcid={$fo_cl_aid}&page=foreigner");
    } else {
        $_SESSION['error'] = "Error";
        showSweetAlert('Error', "บันทึกข้อมูล ไม่สำเร็จ", 'error');
        unset($_SESSION['error']); // Clear the session variable
        header("refresh:2; url=/Bansuan/index.php?pcid={$fo_cl_aid}&page=foreigner");
    }

}catch(Exception $e){
    echo 'Query Failed: '. $e->getMessage();
}

?>