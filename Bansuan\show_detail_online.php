<script src="/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>
<script src="../SweetAlert2/dist/sweetalert2.all.min.js"></script>

<?php
include '../Condb.php';
include '../users.inc.php';
include '../right_user.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);


//$id = $_GET['id'];
$pcid = $_GET['pcid'];

//echo $pcid;

?>


<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1">
<link rel="stylesheet" href="../bootstrap/css/bootstrap.rtl.min.css">
<title>ระบบ WatchmanDB</title>
	<style>
		.td {
			 text-align: start;
		}
	</style>
</head>

<body>
	<div class="container-fluid">
		<div class=" h3 text-center  alert alert-success mb-2 mt-2 " role="alert" >ข้อมูลสืบสวน คดีรับแจ้งความออนไลน์ <?= $name_station ?></div>
		<div style="flex: auto"> &nbsp;<a href="/Bansuan/?pcid=<?=$pcid?>&rnd=<?= rand(); ?>&page=online" class="btn btn-primary mb-4" >กลับ</a> &nbsp;&nbsp;</div>
            
<?php

$sql = "SELECT
            T1.*,
            T2.station_name as station " .
        "FROM
            wm_tb_online_case AS T1  " .
            "LEFT JOIN wm_tb_police_station2 AS T2 ON T1.station = T2.station_code " ;
        
if($pcid != ''){
	$sql = $sql . " WHERE on_cl_no='$pcid' ";
}


$result_on = mysqli_query($conn,$sql);
// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no = 1;
while($row_on=mysqli_fetch_array($result_on))	//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
{
    $pcid = $row_on['on_cl_no'];  
    
    //ฟังก์ชั่น วันที่ ดึงจาก condb
    if($row_on["on_cl_date"]!=''){
        $strDate = DateThai( $row_on["on_cl_date"] );
    }else{
        $strDate = '';
    }
    
        
    //ตรวจสอบไฟล์ สส.1 ก่อนว่า มีไฟล์ในตารางไหม หากมี ให้ส่งเป็นลิงค์
	$pdf = $row_on["on_cl_file"];
	if($pdf !== '')
	{
		$links = '<a href="/' . $row_on["on_cl_file"] .'" target="_blank">Download</a>';
	}
	else {
		$links = 'ไม่มีไฟล์';
	}
    
    
?>	
            
 
<div class="container">
<table width="92%" border="1" cellspacing="1" cellpadding="1" class="table-striped table-hover table-bordered">
  <tbody>
    <tr align="center" >
      <td width="15%" align="center" bgcolor="#1D04F3" height="40px"><b style="color: white" >รายการ</b></td>
      <td width="85%" align="center" bgcolor="#1D04F3" height="40px"><b style="color: white" >รายละเอียด</b></td>
    </tr>
    <tr>
      <td style="text-align: start">&nbsp;&nbsp;เลขรับแจ้ง</td>
      <td style="text-align: start">&nbsp;&nbsp;<?= $row_on["on_cl_no"]?></td>
    </tr>
    <tr>
      <td style="text-align: start">&nbsp;&nbsp;ผู้เสียหาย</td>
      <td style="text-align: start">&nbsp;&nbsp;<?= $row_on["on_cl_name_sufferer"]?></td>
    </tr>
    <tr>
      <td style="text-align: start">&nbsp;&nbsp;รายละเอียดโดยย่อ</td>
      <td style="text-align: start">&nbsp;&nbsp;<?= $row_on["on_cl_detail"]?></td>
    </tr>
    <tr>
      <td style="text-align: start">&nbsp;&nbsp;หน่วยรับผิดชอบ</td>
      <td style="text-align: start">&nbsp;&nbsp;<?= $row_on["station"]?></td>
    </tr>
    <tr>
      <td style="text-align: start">&nbsp;&nbsp;พนักงานสอบสวน</td>
      <td style="text-align: start">&nbsp;&nbsp;<?= $row_on["on_cl_inquiry_official"]?></td>
    </tr>
    <tr>
      <td style="text-align: start">&nbsp;&nbsp;วันรับแจ้ง</td>
      <td style="text-align: start">&nbsp;&nbsp;<?= $strDate ?></td>
    </tr>
    <tr>
      <td style="text-align: start">&nbsp;&nbsp;สถานะคดี</td>
      <td style="text-align: start">&nbsp;&nbsp;<?= $row_on["on_cl_status_case"]?></td>
    </tr>
    <tr>
      <td style="text-align: start">&nbsp;&nbsp;ผู้สืบสวน</td>
      <td style="text-align: start">&nbsp;&nbsp;<?= $row_on["on_cl_detective"]?></td>
    </tr>
 <!--   <tr>
      <td>สรุปการสืบสวน</td>
      <td><?= $row_on["on_cl_detective_detail"]?></td>
    </tr>
    <tr>
      <td>รายงานสืบสวน</td>
      <td><?= $links ?></td>
    </tr> -->
  </tbody>
</table>
 <!--   <div class="alert alert-warning table-hover table-bordered" >
        <div class="col-8"> การดำเนินการ : <?= $row_on["#cp_cl_action"]?>  </div>
    
    </div> -->
</div>
            	
</div>

        
        <?php
	$no++;
	}
//	mysqli_close($conn);	//ปิดการเชื่อมต่อฐานข้อมูล

	?>  
<hr>

<div class="container-fluid">
<br>
<div>
    <a href="Add_online_invest.php?pcid=<?= $pcid ?>" class="btn btn-success mb-4 <?= $add_btn ?>" >เพิ่มรายการสืบสวน</a>
</div>
<table class="table table-striped table-hover table-bordered table-sm mb-4 mt-4">
    <tbody>
        <tr class="container">
            <th>ลำดับ</th>
       <!--     <th>เลขรับแจ้ง</th> -->
            <th>วันที่</th>
            <th>รายการสืบสวน</th>
            <th>สรุปผล</th>
            <th>ผู้สืบสวน</th>
            <th>แนบไฟล์</th>
            <th></th>
            <th></th>
        </tr>
<?php
    
$sql2 = "SELECT *, T4.bsdp_name AS pol_name FROM wm_tb_online_invest AS T1
            LEFT JOIN police_name_bsdetective AS T4 ON T1.oi_cl_detective = T4.aid_bsdp
        ";  //ตั้งตัวแปร sql เลือกฐานข้อมูลจากตารางที่ชื่อ wm_tb_personal
if($pcid != ''){
	$sql2 = $sql2 . " WHERE oi_cl_case_no='$pcid' ";
}
    $result_oi = mysqli_query($conn,$sql2);

       // echo $sql2;
// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no2 = 1;
while($row_oi = mysqli_fetch_array($result_oi))	{
    
    //ฟังก์ชั่น วันที่ ดึงจาก condb
    $strDate2 = DateThai( $row_oi['oi_cl_date'] );
    
    //ตรวจสอบไฟล์ สส.1 ก่อนว่า มีไฟล์ในตารางไหม หากมี ให้ส่งเป็นลิงค์
	$pdf = $row_oi["oi_cl_file"];
	if($pdf !== '')
	{
		$links = '<a href="/' . $row_oi["oi_cl_file"] .'" target="_blank">Download</a>';
	}
	else {
		$links = 'ไม่มีไฟล์';
	}
    //จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
    
?>
        <tr class="container">
            <td>&nbsp;<?= $no2 ?></td>
       <!--     <td><?= $row_oi['oi_cl_case_no'] ?></td>    -->
            <td><?= $strDate2 ?></td>
            <td style="text-align: start"><?= $row_oi['oi_cl_invest'] ?></td>
            <td style="text-align: start"><?= $row_oi['oi_cl_invest_res'] ?></td>
            <td style="text-align: start"><?= $row_oi['pol_name'] ?></td>
            <td><?= $links ?></td>
            <td>&nbsp;<a href="Edit_online_invest.php?pcid=<?= $pcid ?>&id=<?= $row_oi['oi_cl_aid'] ?> " class="btn btn-warning mb-4 <?= $edit_btn ?>" >แก้ไข</a>&nbsp;</td>
            
            <td>&nbsp;<button href="#" class="btn btn-danger mb-4" onClick="deleteItem(<?= $row_oi['oi_cl_aid'] ?>, '<?= $pcid ?>')"<?=$del_btn?>>ลบ</button>&nbsp;</td>

<?php
	$no2++;
}//while
?>
    </tbody>
</table>    
</div>
<script>
function deleteItem(id, pcid) {
    Swal.fire({
        title: 'คุณต้องการลบข้อมูลนี้ใช่ไหม?',
        text: "หลังจากลบแล้ว จะไม่สามารถกู้คืนได้",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'ใช่, ยืนยันลบ',
        cancelButtonText: 'ยกเลิก'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = '/Bansuan/Del_online_invest.php?id=' + id + '&pcid=' + pcid;
        }
    });
}
</script>
</body>
</html>