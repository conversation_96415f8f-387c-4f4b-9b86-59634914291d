<?php
include '../Condb.php';

$sql = "SELECT * FROM wm_tb_personal_backup WHERE ps_cl_birthday2 IS NULL";
$result = mysqli_query($conn,$sql);

//print_r($result);
//exit();
while($row = mysqli_fetch_array($result))
{
    $aid = $row['ps_cl_aid']; // << auto run
    $thai = $row['ps_cl_birthday'];
    $eng = thai_date_2_eng($thai); // จำชื่อไม่ได้
    $sql2 = "update wm_tb_personal_backup set ps_cl_birthday2='$eng' WHERE ps_cl_birthday2 IS NULL ";
    mysqli_query($sql2);
}

?>


<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>แก้ไขวันที่</title>
<script src="/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
</style>
<script src="/jquery-ui-1.12.1/jquery-ui.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/jquery-ui-1.12.1/jquery-ui.css">
<link rel="stylesheet" href="/js/datetimepicker2.5.1/jquery.datetimepicker.min.css">
<script src="/js/datetimepicker2.5.1/jquery.datetimepicker.full.js" type="text/javascript"></script>    
<script src="/js/utils.js?<?= rand() ?>" type="text/javascript"></script> 
</head>

<body>
	
	<div class="container">
	<div class="row">
	<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> แก้ไขข้อมูลการปิดล้อมตรวจค้น </div>
	<form action="Save_blockade_Edit.php" method="POST" enctype="multipart/form-data">
	<span style="color: #1203F8">
        
        
        
		<p>
		<br>
			<input type="submit" value="บันทึกข้อมูล" class="btn btn-success" >
			<td> <a href="index.php?rnd=<?= rand(); ?>&page=blockade" class="btn btn-warning" >ยกเลิก</a> </td>
		</p>
		<br>
		<br>
	</form>
	</div>
	</div>
	</div>
<!-- ฟังก์ชั่น เลือก จังหวัด อำเภอ ตำบล -->
<script>

function do_province_change()
{
	var sel = document.getElementById("bl_cl_province");		
	var code = sel.options[sel.selectedIndex].value;

	$.get("../ajax/get_amphure.php?province=" + code, 
		  function(data, status){
				//alert("Data: " + data + "\nStatus: " + status);
				// jQuery style						
				$('#bl_cl_amphur').find('option').remove().end();
				var data = $.trim(data);
				if(data == '') {
					alert("ไม่พบข้อมูลอำเภอ !");
					return;
				}

					//.append('<option value="whatever">text</option>').val('whatever');
				var datas = data.split(';');
				for(var i=0; i<datas.length; i++) {
					if(datas[i] == '') continue;
					datas[i] = datas[i].split(':');
					var code = datas[i][0];
					var name = datas[i][1];
					$('#bl_cl_amphur').append('<option value="'+ code+'">' + name + '</option>');
				}
		});
}

function do_amphure_change()
{
	var sel = document.getElementById("bl_cl_province");		
	var code = sel.options[sel.selectedIndex].value;

	var sel2 = document.getElementById("bl_cl_amphur");		
	var code2 = sel2.options[sel2.selectedIndex].value;

	$.get("../ajax/get_district.php?province=" + code + "&amphure=" + code2, 
			  function(data, status){
					//alert("Data: " + data + "\nStatus: " + status);
					// jQuery style						
					$('#bl_cl_tumbon').find('option').remove().end();
					var data = $.trim(data);
					if(data == '') {
						alert("ไม่พบข้อมูลตำบล !");
						return;
					}

						//.append('<option value="whatever">text</option>').val('whatever');
					var datas = data.split(';');
					for(var i=0; i<datas.length; i++) {
						if(datas[i] == '') continue;
						datas[i] = datas[i].split(':');
						var code = datas[i][0];
						var name = datas[i][1];
						$('#bl_cl_tumbon').append('<option value="'+ code+'">' + name + '</option>');
					}
			});
}

</script> 
<script src="/js/datetimepicker2.5.1/jquery.ui.datepicker-th.js" type="text/javascript"></script>
<script>
// ชื่อ >> value >> set selct auto (สคลิปตรวจสอบ Selected) จำเป็นต้องก็อปปี้ <script src="/jQuery/jquery-3.5.1.min.js" type="text/javascript">
function auto_select(sid, value)
{
    $('#' + sid).val(value);
}

function auto_thaidate(sid, value)
{
	if(value != '') {
		var dates = value.split('-');
		dates = value.split('-');
		if(dates.length > 2) {
			var ys = dates[0];
			var ms = dates[1];
			var ds = dates[2];
			if(ys.substr(0,2) == '20') {
				ys = parseInt(ys) + 543;
			}
			value = $.digit(ds,2) + '/' + $.digit(ms,2) + '/' +ys;
			$('#' + sid).val(value);
		}
	}
}

$(document).ready(function() {
<?php
    echo "auto_select('bl_cl_num', '{$row['bl_cl_num']}');\n";
    echo "auto_select('bl_cl_method', '{$row['bl_cl_method']}');\n";
    echo "auto_select('bl_cl_find_illegal', '{$row['bl_cl_find_illegal']}');\n"; 
    echo "auto_select('bl_cl_pol_station', '{$row['bl_cl_pol_station']}');\n";
    echo "auto_select('bl_cl_record', '{$row['bl_cl_record']}');\n";
    // convert db date to thai dates
	echo "auto_thaidate('bl_cl_date', '{$row['bl_cl_date']}');\n";
?>
    });
</script>
</body>
</html>