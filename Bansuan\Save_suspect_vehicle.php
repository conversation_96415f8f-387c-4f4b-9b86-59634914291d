<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../Alert.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

$ip_address = $_SERVER['REMOTE_ADDR'];
$action = 'Save suspect vehicle'; // You can set the action to a suitable value
$data = str_replace("'", "", compact_array($_POST));
log_activity($acc, $action, $data, $ip_address); // ใช้ function ในไฟล์ Condb.php

/*echo '<pre>';
print_r($_POST);
echo '</pre>';

echo '<hr>';

echo '<pre>';
var_dump($_POST);
echo '</pre>';

exit();*/

$sv_cl_aid = $_POST['sv_cl_aid'];
$sv_cl_date = $_POST[ 'sv_cl_date' ];
$sv_cl_vehicle_type = $_POST[ 'sv_cl_vehicle_type' ];
$sv_cl_veh_plate = $_POST[ 'sv_cl_veh_plate' ];
$sv_cl_brand = $_POST[ 'sv_cl_brand' ];
$sv_cl_model = $_POST[ 'sv_cl_model' ];
$sv_cl_color = $_POST[ 'sv_cl_color' ];
$sv_cl_suspect = $_POST[ 'sv_cl_suspect' ];
$sv_cl_place = $_POST[ 'sv_cl_place' ];
$sv_cl_driver = $_POST[ 'sv_cl_driver' ];
$sv_cl_phone_driver = $_POST[ 'sv_cl_phone_driver' ];
$sv_cl_police = $_POST[ 'sv_cl_police' ];
$checking = $_POST[ 'checking' ]; //checking
$action = $_POST[ 'action' ]; //action
$Send_inquiry_official = $_POST[ 'Send_inquiry_official' ]; //Send_inquiry_official
$sv_cl_inquiry_official = $_POST[ 'sv_cl_inquiry_official' ];
$region = $_POST[ 'region' ];
$provincial = $_POST[ 'provincial' ];
$station = $_POST[ 'station' ];
$sv_cl_action = $_POST[ 'sv_cl_action' ];
$sv_cl_owner = $_POST[ 'sv_cl_owner' ];
$sv_cl_return = $_POST[ 'sv_cl_return' ];
$sv_cl_phone_return = $_POST[ 'sv_cl_phone_return' ];
$sv_cl_action_date = $_POST[ 'sv_cl_action_date' ];
$sv_cl_status = $_POST[ 'sv_cl_status' ];

$sql ="SELECT * FROM `wm_tb_suspect_vehicle` WHERE sv_cl_aid = :sv_cl_aid ";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':sv_cl_aid', $sv_cl_aid);
try{
    $stmt->execute();
}catch(PDOException $e){
    echo $e->getMessage();
}

$row = $stmt->fetch(PDO::FETCH_ASSOC);
if($row) {
    $sv_cl_aid = $row['sv_cl_aid'];
    
    $sql = "UPDATE wm_tb_suspect_vehicle SET " .
		"sv_cl_date = :sv_cl_date," .
		"sv_cl_vehicle_type = :sv_cl_vehicle_type," .
        "sv_cl_veh_plate = :sv_cl_veh_plate," .
        "sv_cl_brand = :sv_cl_brand," .
    	"sv_cl_model = :sv_cl_model," .
        "sv_cl_color = :sv_cl_color," .
    	"sv_cl_suspect = :sv_cl_suspect," .
        "sv_cl_place = :sv_cl_place," .
        "sv_cl_driver = :sv_cl_driver," .
        "sv_cl_phone_driver = :sv_cl_phone_driver," .
    	"sv_cl_police = :sv_cl_police," .
        "checking = :checking," .
        "action = :action," .
        "Send_inquiry_official = :Send_inquiry_official," .
		"sv_cl_inquiry_official = :sv_cl_inquiry_official," .
		"region = :region," .
        "provincial = :provincial," .
        "station = :station," .
        "sv_cl_action = :sv_cl_action," .
        "sv_cl_owner = :sv_cl_owner," .
        "sv_cl_return = :sv_cl_return," .
        "sv_cl_phone_return = :sv_cl_phone_return," .
        "sv_cl_action_date = :sv_cl_action_date," .
        "sv_cl_status = :sv_cl_status " .
        "WHERE sv_cl_aid = :sv_cl_aid ";
            $stmt = $pdo->prepare($sql);
            $stmt->bindParam(':sv_cl_aid', $sv_cl_aid);
            $stmt->bindParam(':sv_cl_date', $sv_cl_date);
}else{
        $sql = "INSERT INTO wm_tb_suspect_vehicle (sv_cl_date, sv_cl_vehicle_type, sv_cl_veh_plate, sv_cl_brand, sv_cl_model, sv_cl_color, sv_cl_suspect, sv_cl_place, sv_cl_driver, sv_cl_phone_driver, sv_cl_police, checking, action, Send_inquiry_official, sv_cl_inquiry_official, region, provincial, station, sv_cl_action, sv_cl_owner, sv_cl_return, sv_cl_phone_return, sv_cl_action_date, sv_cl_status) 
        VALUES(:sv_cl_date, :sv_cl_vehicle_type, :sv_cl_veh_plate, :sv_cl_brand, :sv_cl_model, :sv_cl_color, :sv_cl_suspect, :sv_cl_place, :sv_cl_driver, :sv_cl_phone_driver, :sv_cl_police, :checking, :action, :Send_inquiry_official, :sv_cl_inquiry_official, :region, :provincial, :station, :sv_cl_action, :sv_cl_owner, :sv_cl_return, :sv_cl_phone_return, :sv_cl_action_date, :sv_cl_status) ";
            $stmt = $pdo->prepare($sql);
            $stmt->bindParam(':sv_cl_date', $sv_cl_date);
    }
$stmt->bindParam(':sv_cl_vehicle_type', $sv_cl_vehicle_type);
$stmt->bindParam(':sv_cl_veh_plate', $sv_cl_veh_plate);
$stmt->bindParam(':sv_cl_brand', $sv_cl_brand);
$stmt->bindParam(':sv_cl_model', $sv_cl_model);
$stmt->bindParam(':sv_cl_color', $sv_cl_color);
$stmt->bindParam(':sv_cl_suspect', $sv_cl_suspect);
$stmt->bindParam(':sv_cl_place', $sv_cl_place);
$stmt->bindParam(':sv_cl_driver', $sv_cl_driver);
$stmt->bindParam(':sv_cl_phone_driver', $sv_cl_phone_driver);
$stmt->bindParam(':sv_cl_police', $sv_cl_police);
$stmt->bindParam(':checking', $checking);
$stmt->bindParam(':action', $action);
$stmt->bindParam(':Send_inquiry_official', $Send_inquiry_official);
$stmt->bindParam(':sv_cl_inquiry_official', $sv_cl_inquiry_official);
$stmt->bindParam(':region', $region);
$stmt->bindParam(':provincial', $provincial);
$stmt->bindParam(':station', $station);
$stmt->bindParam(':sv_cl_action', $sv_cl_action);
$stmt->bindParam(':sv_cl_owner', $sv_cl_owner);
$stmt->bindParam(':sv_cl_return', $sv_cl_return);
$stmt->bindParam(':sv_cl_phone_return', $sv_cl_phone_return);
$stmt->bindParam(':sv_cl_action_date', $sv_cl_action_date);
$stmt->bindParam(':sv_cl_status', $sv_cl_status);
try{
    $result = $stmt->execute();
}catch (PDOException $e) {
    echo $e->getMessage();
}

if ($result) {
    $_SESSION['success'] = "Data has been inserted succesfully";
    showSweetAlert("Saved successfully", "บันทึกข้อมูลเรียบร้อย");
    unset($_SESSION['success']); // Clear the session variable
    header("refresh:2; url=/Bansuan/index.php?id={$sv_cl_veh_plate}&page=suspect");
} else {
    $_SESSION['error'] = "Error";
    showSweetAlert('Error', "บันทึกข้อมูล ไม่สำเร็จ", 'error');
    unset($_SESSION['error']); // Clear the session variable
    header("refresh:2; url=/Bansuan/index.php?id={$sv_cl_veh_plate}&page=suspect");
}

$pdo = null;

?>