<?php
include '../Condb.php';
include '../users.inc.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// เมื่อมีการใช้ ตัวแปร Session จะต้องเปิดก่อน
//session_start();  // เปิดตัวแปร session
/*if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
*/
$id = $_GET['id'];

$sql = "SELECT * FROM wm_tb_child WHERE cd_cl_aid = '$id' ";
$result = mysqli_query($conn,$sql);
$row = mysqli_fetch_array($result);

$people_name = '';
if($row) 
{
	$pcid = $row["cd_cl_idcard"];		
	//
	$query1 = "SELECT ps_cl_prefix, ps_cl_name, ps_cl_surname, ps_cl_image FROM wm_tb_personal WHERE ps_cl_idcard ='$pcid' ";
	$result1 = mysqli_query($conn, $query1);
	if($row1 = mysqli_fetch_array($result1))
	{
		$people_name = $row1['ps_cl_prefix'] . $row1['ps_cl_name'] . ' ' . $row1['ps_cl_surname'];
		$people_image = $row1["ps_cl_image"];
		if($people_image != '') {
			$people_image = "<img src='{$people_image}' height='50'> ";
		}
	}
	else {
		die("ไม่พบข้อมูล pcid: $pcid");
	}
}

?>


<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>แก้ไขข้อมูลบุตร</title>
<script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>
<link rel="stylesheet" href="/policeinnopolis/bootstrap/css/bootstrap.rtl.min.css">
<!-- สำหรับ วันเกิด แบบ พ.ศ. -->
<link rel="stylesheet" href="http://code.jquery.com/ui/1.9.1/themes/base/jquery-ui.css" />
<script src="http://code.jquery.com/jquery-1.8.2.js"></script>
<script src="/policeinnopolis/js/jquery-ui.js"></script>
    
<script>
    function doPrefixChange()
    {
        var selected = $("#cd_cl_prefix option:selected").val();
        if(selected == "นาย") {
            $("#cd_cl_sex1").prop("checked", true);
        }
        else if(selected == "นาง"){
            $("#cd_cl_sex2").prop("checked", true);
        }
        else if(selected == "น.ส."){
            $("#cd_cl_sex2").prop("checked", true);
        }
        else if(selected == "ด.ช."){
            $("#cd_cl_sex1").prop("checked", true);
        }
        else if(selected == "ด.ญ."){
            $("#cd_cl_sex2").prop("checked", true);
        }
        else {
            $("#cd_cl_sex1").prop("checked", false);
            $("#cd_cl_sex2").prop("checked", false);
            $("#cd_cl_sex3").prop("checked", false);
        }
    }
</script>
    
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 18px;
    }
input::placeholder {
  color: red;
}
.form-control::placeholder {
  color: sandybrown;
}
</style>
</head>

<body>
	<div class="container" >
	<div class="row">
		<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> แก้ไขข้อมูลบุตร ของ <?= $people_name ?> <?= $people_image ?> </div>
	<form action="Save_all_Child_Edit.php" method="POST" enctype="multipart/form-data" class="body">	
		<label>ลำดับ</label>
		<input name="cd_cl_aid" type = "text" class="form-control" value= "<?=$row['cd_cl_aid']?>" hidden ="hidden"  >
		<label>เลขบัตรประชาชน</label>
		<input name="cd_cl_idcard" type = "text" class="form-control" value= "<?= $row['cd_cl_idcard']?>" readonly="readonly"  >
		<label>เลขบัตรประชาชนบุตร</label>
		<input type = "text" name="cd_cl_idcard_child" class="form-control" value= "<?=$row['cd_cl_idcard_child']?>" placeholder="กรอกเลขบัตรประชาชน 13 หลักของบุตร" >

		<label>คำนำหน้า</label>
		<select id="cd_cl_prefix" name="cd_cl_prefix" class="form-select form-select-sm" onChange="doPrefixChange()" >
			<option value="">เลือกคำนำหน้า</option>
			<option value="นาย" <? if($row['cd_cl_prefix'] == 'นาย') echo 'selected'; ?> > นาย</option>
			<option value="นาง" <? if($row['cd_cl_prefix'] == 'นาง') echo 'selected'; ?> >นาง</option>
			<option value="น.ส." <? if($row['cd_cl_prefix'] == 'น.ส.') echo 'selected'; ?> >น.ส.</option>
			<option value="ด.ช." <? if($row['cd_cl_prefix'] == 'ด.ช.') echo 'selected'; ?> >ด.ช.</option>
			<option value="ด.ญ." <? if($row['cd_cl_prefix'] == 'ด.ญ.') echo 'selected'; ?> >ด.ญ.</option>
		</select>

		<label>เพศ</label> : 
		      <input name="cd_cl_sex" id="cd_cl_sex1" type="radio" value="ชาย" <? if($row['cd_cl_sex'] == "ชาย") echo 'checked="checked" '; ?> />ชาย</label>
		      <label><input name="cd_cl_sex" id="cd_cl_sex2" type="radio" value="หญิง" <? if($row['cd_cl_sex'] == "หญิง") echo 'checked="checked" '; ?> />หญิง</label>													<label><input name="cd_cl_sex" id="cd_cl_sex3" type="radio" value="LGBTQ" <? if($row['cd_cl_sex'] == "LGBTQ") echo 'checked="checked" '; ?> />LGBTQ</label><br>

		<label>ชื่อ</label>
		<input type = "text" name = "cd_cl_name" class="form-control" value= "<?=$row['cd_cl_name']?>" placeholder="กรอกชื่อจริง" >
		<label>นามสกุล</label>
		<input type = "text" name = "cd_cl_surname" class="form-control" value= "<?=$row['cd_cl_surname']?>" placeholder="กรอกนามสกุลจริง" >
		<label>ชื่อเล่น</label>
		<input type = "text" name = "cd_cl_nickname" class="form-control" value= "<?=$row['cd_cl_nickname']?>" placeholder="กรอกชื่อเล่น"  >

        <label>วันเดือนปีเกิด</label>
        <p><input type="text" name="cd_cl_birthday2" id="datepicker" value= "<?=$row['cd_cl_birthday2']?>" class="form-control" style="width:250px;" autocomplete="off" required></p>
        <!-- สำหรับวันที่ เดือนไทย ปี พ.ศ. และ เลือนเลือกปี พ.ศ.ได้  -->
        <script type="text/javascript"> 
            $(function(){

                $.datetimepicker.setLocale('th'); // ต้องกำหนดเสมอถ้าใช้ภาษาไทย และ เป็นปี พ.ศ.
                // กรณีใช้แบบ input
                $("#datepicker").datetimepicker({
                    timepicker:false,
                    format:'Y-m-d',  // กำหนดรูปแบบวันที่ ที่ใช้ เป็น 00-00-0000			
                    lang:'th',  // ต้องกำหนดเสมอถ้าใช้ภาษาไทย และ เป็นปี พ.ศ.
                    onSelectDate:function(dp,$input){
                        var yearT=new Date(dp).getFullYear()-0;  
                        var yearTH=yearT;  // ถ้าจะให้แสดงเป็น พ.ศ. ให้เป็น yearT+543
                        var fulldate=$input.val();
                        var fulldateTH=fulldate.replace(yearT,yearTH);
                        $input.val(fulldateTH);
                    },
                });       
                // กรณีใช้กับ input ต้องกำหนดส่วนนี้ด้วยเสมอ เพื่อปรับปีให้เป็น ค.ศ. ก่อนแสดงปฏิทิน
                $("#datepicker").on(function(e){
                    var dateValue=$(this).val();
                    if(dateValue!=""){
                            var arr_date=dateValue.split("-"); // ถ้าใช้ตัวแบ่งรูปแบบอื่น ให้เปลี่ยนเป็นตามรูปแบบนั้น
                            // ในที่นี้อยู่ในรูปแบบ 00-00-0000 เป็น d-m-Y  แบ่งด่วย - ดังนั้น ตัวแปรที่เป็นปี จะอยู่ใน array
                            //  ตัวที่สอง arr_date[2] โดยเริ่มนับจาก 0 
                            dateValue=dateValue.replace(arr_date[0],yearT);
                            $(this).val(dateValue);													
                    }		
                });
            });
            </script>
        <!-- สำหรับ วันที่ แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ -->
            
		<label>บิดา</label>
		<input type = "text" name = "cd_cl_father" class="form-control" value= "<?=$row['cd_cl_father']?>" placeholder="ชื่อบิดา" >
		<label>เลขบัตรบิดา</label>
		<input type = "text" name = "cd_cl_father_pid" class="form-control" value= "<?=$row['cd_cl_father_pid']?>" placeholder="เลขบัตรประชาชน บิดา" >
		<label>มารดา</label>
		<input type = "text" name = "cd_cl_mother" class="form-control" value= "<?=$row['cd_cl_mother']?>" placeholder="ชื่อมารดา" >
		<label>เลขบัตรมารดา</label>
		<input type = "text" name = "cd_cl_mother_pid" class="form-control" value= "<?=$row['cd_cl_mother_pid']?>" placeholder= "เลขบัตรประชาชน มารดา" >
		<label>คู่สมรส</label>
		<input type = "text" name = "cd_cl_spouse" class="form-control" value= "<?=$row['cd_cl_spouse']?>" placeholder="ชื่อคู่สมรส หรือแฟน" >
		<div class="mb-3">
			<label for="formFileMultiple" class="form-label">รูปภาพ</label> <img src= "<?= $row["cd_cl_image"] ?>" height="50">
			 <input class="form-control" type="file" id="cd_cl_image" name="cd_cl_image" multiple  value=<?=$row['cd_cl_image']?> >
		</div>
  		<p>
    	<input type="submit" value="Update" class="btn btn-success" >
		<td> <a href="Show_All.php?pcid=<?= $pcid ?>&page=child" class="btn btn-warning" >ยกเลิก</a> </td>
 		</p>
	</form>
	</div>
	</div>
	</div>
    

<script>
// ชื่อ >> value >> set selct auto (สคลิปตรวจสอบ Selected) จำเป็นต้องก็อปปี้ <script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js" type="text/javascript">
function auto_select(sid, value)
{
    $('#' + sid).val(value);
}

$(document).ready(function() {
<?php
  //  echo "auto_select('pf_cl_dates', '{$row['pf_cl_dates']}');\n";
    echo "auto_select('cd_cl_prefix', '{$row['cd_cl_prefix']}');\n";
            
?>
    });
</script>
    
</body>
</html>