<?php
include '../Condb.php';

$pcid = $_GET['pcid'];

$people_name = '';
if($pcid != '') {
	$query1 = "SELECT * FROM wm_tb_personal WHERE ps_cl_idcard ='$pcid' ";
	$result1 = mysqli_query($conn, $query1);
	$row1 = mysqli_fetch_array($result1);	
	
	$people_name = $row1['ps_cl_prefix'] . ' ' . $row1['ps_cl_name'] . ' ' . $row1['ps_cl_surname'];
	$people_image = $row1["ps_cl_image"];
	if($people_image != '') {
		$people_image = "<img src='{$people_image}' height='80'> ";
	}
}

$sql = "SELECT * FROM wm_tb_warrant_checklist WHERE ck_idcard = '$pcid' ";
$result = mysqli_query($conn,$sql);
$row = mysqli_fetch_array($result);

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Check List</title>
<!-- สำหรับ วันที่ แบบ พ.ศ. -->
<link rel="stylesheet" href="http://code.jquery.com/ui/1.9.1/themes/base/jquery-ui.css" />
<script src="http://code.jquery.com/jquery-1.8.2.js"></script>
<script src="/policeinnopolis/js/jquery-ui.js"></script>
</head>

<body>
    
<h1 align="center">Check List หมายจับค้างเก่า</h1>
<button style="bottom: auto"><a href="../policeinnopolis/WatchmanData/Show_All.php?pcid=<?= $pcid ?>&page=warrant" class="btn btn-warning" >ย้อนกลับ</a></button>
    <div class=" h4 text-center alert alert-success mb-4 mt-4 " role="alert"><?= $people_image ?> <b style="font-size: 25px ; color: blue"><?=$people_name?> <?= $pcid ?> </b></div>
    <form action="Save_checklist.php" method="POST" enctype="multipart/form-data" >
    <table width="95%" border="2" cellspacing="1" cellpadding="1" class="table-borderless">
  <tbody>
      <label hidden ="hidden">ลำดับ</label>
		<input type = "text" name = "ck_aid" class="form-control"  hidden ="hidden" >
      <label  hidden ="hidden">เลขบัตร</label>
		<input type = "text" name = "ck_idcard" class="form-control" value= "<?=$row1['ps_cl_idcard']?>"  hidden ="hidden">
      
    <tr bgcolor="#F30509" align="center" style="font-size: 20px; color: aliceblue">
      <td>ลำดับ</td>
      <td>หัวข้อ</td>
      <td colspan="2">รายละเอียด</td>
      <td>ลำดับ</td>
      <td>หัวข้อ</td>
      <td>รายละเอียด</td>
    </tr>
    <tr>
      <td rowspan="11" style="font-size: 50px" align="center">1</td>
      <td rowspan="11" bgcolor="#FA08F4" style="font-size: 20px">ทะเบียนราษฎร์</td>
      <td>1.ผู้ต้องหา</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="11"> 
        รายละเอียดบัตรประชาชนล่าสุด ย้อนหลัง รูปถ่ายแบบ 1,2</td>
      <td rowspan="14" style="font-size: 50px" align="center">6</td>
      <td rowspan="14" bgcolor="#8EF8E4" style="font-size: 20px">การสื่อสาร<br>
        โทรศัพท์<br>
      อินเตอร์เน็ต</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="61">
      ตรวจสอบการจดทะเบียนมือถือผู้ต้องหาเครือข่าย AIS,DTAC,TRUE,CAT,TOT</td>
    </tr>
    <tr>
      <td>2.คู่สมรส/แฟน</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="12"> 
      รายละเอียดทะเบียนบ้านปัจจุบัน การย้ายที่อยู่</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="62">
      ตรวจสอบวันจดทะเบียน ประเภทรายเดือน/เติมเงิน ที่อยู่ ที่จัดส่งบิล</td>
    </tr>
    <tr>
      <td>3.บุตร</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="13">
      รายละเอียดการเปลี่ยนแปลงชื่อ นามสกุล รูปทำบัตรทุกครั้ง</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="63">
      ตรวจสอบสถานะการใช้งาน ระงับ ยกเลิก โอน การขอให้บริการพิเศษ SMS ฯ</td>
    </tr>
    <tr>
      <td>4.พ่อแม่</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="14">
      รายละเอียดทะเบียนราษฎร์คู่สมรส บุตร ที่อยู่อดีต/ปัจจุบัน</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="64">
      ตรวจสอบหมายเลข IMEI ,IMSI และหมายเลขซิมการ์ด เบอร์มือถือผู้ต้องหา</td>
    </tr>
    <tr>
      <td>5.พี่น้อง</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="15">
      รายละเอียดการทำบัตร ปชช. สถานที่ วันเวลา วันหมดอายุ</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="65">
      ตรวจสอบมือถือ ยี่ห้อ รุ่น สี จากหมายเลข IMEI เบอร์มือถือของผู้ต้องหา</td>
    </tr>
    <tr>
      <td>6.ปู่/ย่า/ตา/ยาย</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="16">
      รายละเอียดการรับรองการทำบัตร ปชข. สถานะ คนรับรอง</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="66">
      ตรวจสอบขอรายการใช้โทรศัพท์มือถือ การโทรเข้ารับสาย การใช้ข้อมูลฯ</td>
    </tr>
    <tr>
      <td>7.ลุง/ป้า/น้า/อา</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="17">
      รายละเอียดเบอร์โทรศัพท์ที่ให้ไว้ขณะทำบัตรประชาชน</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="67">
      ตรวจสอบพิกัดเสาสัญญาณโทรศัพท์ผู้ต้องหา จากตำแหน่ง LAC,CID</td>
    </tr>
    <tr>
      <td>8.ลูกพี่ลูกน้อง/หลาน</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="18">
      พิสูจน์ทราบบ้านตามทะเบียนบ้าน ถ่ายภาพ แผนที่</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="68">
      ตรวจสอบเครือข่ายบุคคลที่ผู้ต้องหาติดต่อด้วย โทรเข้า/รับสาย ใช้อินเตอร์เน็ต</td>
    </tr>
    <tr>
      <td>9.ญาติคู่สมรส/แฟน</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="19">
      พิสูจน์ทราบบ้านพักอาศัยปัจจุบัน ถ่ายภาพ แผนที่</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="69">
      ตรวจสอบความถี่และช่วงเวลาการใช้สาย การโทร.ติด รับสาย ไม่ได้รับสาย</td>
    </tr>
    <tr>
      <td>10.เพื่อนสนิท</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="110">
      พิสูจน์ทราบสถานที่ทำงาน รายละเอียด ประวัติการทำงาน</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="610">
      ตรวจสอบรายละเอียดการโทรศัพท์ติดต่อหน่วยงานรัฐ เอกชน ผู้ให้บริการต่างๆ</td>
    </tr>
    <tr>
      <td>11.เพื่อนร่วมงาน<br></td>
      <td bgcolor="#EFFA03"><input type="checkbox" name="ck_checkbox[]" value="111">
      จัดทำผัง Link Chart ความสัมพันธ์ 1.-11 พร้อมรายละเอียด</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="611">
      ตรวจสอบการจดทะเบียนโทรศัพท์ประจำที่เบอร์บ้านผู้ต้องหาจาก TOT,TRUE</td>
    </tr>
    <tr>
      <td rowspan="12" style="font-size: 50px" align="center">2</td>
      <td rowspan="12" bgcolor="#F1B007" style="font-size: 20px">ประวัติ<br>
        หมายจับ<br>
        ต้องโทษ<br>
      การกระทาผิด</td>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="21">
      ตรวจสอบหมายจับของผู้ต้องหาจากระบบ polis,crime,pdc จัดพิมพ์ทุกหมายใส่แฟ้ม</td>
      <td><input type="checkbox" name="checkbox612" id="checkbox612">
      ตรวจสอบวันจด ที่อยู่ ที่ส่งบิล ขอรายการใช้โทรศัพท์เบอร์บ้านของผู้ต้องหา</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="22">
      ตรวจสอบว่าหมายจับแต่ละหมายจับยังต้องการตัวมาดำเนินคดีอยู่หรือไม่จากระบบฯ</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="613">
      ตรวจสอบวิเคราะห์พฤติกรรมผู้ต้องหา เพื่อหาที่อยู่จาก แฟน พ่อแม่ ลูก ญาติฯ</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="23">
      ตรวจสอบท้องที่เจ้าของหมายจับ อัยการ และศาล ว่ายังต้องการตัว ดาเนินคดีหรือไม่</td>
      <td bgcolor="#EFFA03"><input type="checkbox" name="ck_checkbox[]" value="614">
      จัดทาผัง I2 และ Link Chart วิเคราะห์หาตาแหน่งที่อยู่ พฤติกรรมผู้ต้องหา</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="24">
      ตรวจสอบจากระบบทะเบียนราษฎร์อย่างต่อเนื่องว่า มีการแจ้งตายหรือไม่ รายละเอียด</td>
      <td rowspan="15" style="font-size: 50px" align="center">7</td>
      <td rowspan="15" bgcolor="#28EE0A" style="font-size: 20px">การเงิน<br>
        ธนาคาร<br>
        ATM<br>
      Internet Banking</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="71">
      ตรวจสอบการเปิดบัญชีธนาคารผู้ต้องหาทุกธนาคาร มีกี่บัญชี ธนาคารใดบ้าง</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="25">
      ตรวจสอบการต้องโทษ จาคุก จากระบบ DXC ว่าผู้ต้องหาจำคุกหรือไม่ ที่ไหน อย่างไร</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="72">
      ตรวจสอบรายละเอียดเลขบัญชีธนาคาร คาขอเปิดบัญชี ประเภทบัญชี สาขา</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="26">
      ตรวจสอบรายละเอียดการถูกดำเนินคดี ผู้ร่วมกระทำผิด/คู่คดี จากระบบ DXC,ศาล</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="73">
      ตรวจสอบรายละเอียดข้อมูลผู้ต้องหาที่ให้ไว้กับธนาคาร ที่อยู่ เบอร์โทร.ติดต่อ</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="27">
      ตรวจสอบอัยการสั่งฟ้อง/ไม่ฟ้องคดี ศาลตัดสินจาคุก/ปรับ รอการลงโทษ หรือยกฟ้อง</td>
      <td><input type="checkbox" name="checkbox74" id="checkbox74">
      ตรวจสอบรายละเอียดการผูกบัญชีธนาคารผู้ต้องหากับโทรศัพท์มือถือรับ OTP</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="28">
      ตรวจสอบสถานที่คุมขัง/เรือนจา ข้อมูลที่ให้ไว้กับเรือนจำ ญาติ/ทนาย ที่มาเยี่ยม</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="75">
      ตรวจสอบรายละเอียดบัญชีธนาคารผู้ต้องหาผูกโทรศัพท์มือถือรับ SMS ALERT</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="29">
      ตรวจสอบประวัติการต้องโทษ รายละเอียดวันเวลาสถานที่ ข้อหา พฤติการณ์ที่ถูกจับกุม</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="76">
      ตรวจสอบรายละเอียดการผูกบัญชีธนาคารกับระบบพร้อมเพย์หาเบอร์มือถือ</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="210">
      ตรวจสอบรายละเอียดแผนประทุษกรรม วิธีการ อุปกรณ์ ยานพาหนะในการกระทำผิด</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="77">
      ตรวจสอบข้อมูล Statment รายการเดินบัญชีธนาคาร การฝาก ถอน โอนเงิน</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="211">
      ตรวจสอบรายละเอียดตกเป็นผู้ต้องหาคดีอื่นๆ นอกจากหมายนี้จากระบบรับแจ้งความฯ</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="78">
      ตรวจสอบรายละเอียดการทำบัตร ATM ของบัญชีผู้ต้องหามีหรือไม่ อย่างไร</td>
    </tr>
    <tr>
      <td colspan="2" bgcolor="#EFFA03"><input type="checkbox" name="ck_checkbox[]" value="212">
      รวบรวมสรุปจัดทาผัง Time line เชื่อมโยงประวัติการกระทำผิดผู้ต้องหาตามลำดับเวลา</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="79">
      ตรวจสอบตำแหน่งการใช้ ATM ฝากเงิน ถอนเงินที่ตู้ ATM วันเวลา สถานที่ใด</td>
    </tr>
    <tr>
      <td rowspan="12" style="font-size: 50px" align="center">3</td>
      <td rowspan="12" bgcolor="#A50AE0" style="font-size: 20px">ใบขับขี่<br>
         ยานพาหนะ<br>
      ประกันภัย</td>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="31">
      ตรวจสอบผู้ต้องหาได้รับใบอนุญาตขับขี่หรือไม่จากระบบ polis,crime,กรมขนส่งฯ</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="710">
      ตรวจสอบการติดต่อธนาคาร ขอภาพกล้อง CCTV และกล้อง CCTV ตู้ ATM</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="32">
      ตรวจสอบประเภทใบอนุญาตขับขี่ที่ผู้ต้องหาได้รับอนุญาต ชั่วคราว/ตลอดชีพ</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="711">
      ตรวจสอบการเปิดใช้บริการ Internet banking บัญชีธนาคารของผู้ต้องหา</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="33">
      ตรวจสอบวันอนุญาต/วันหมดอายุใบอนุญาตขับขี่ ครบกำหนดเมื่อใด ต้องต่ออายุฯที่ใด</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="712">
      ตรวจสอบรายละเอียดข้อมูลผู้ต้องหาที่ให้กับธนาคาร ที่อยู่ อีเมลล์ มือถือ</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="34">
      ตรวจสอบและคัดสาเนารายละเอียดใบอนุญาตขับขี่ของผู้ต้องหาจากระบบฯ</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="713">
      ตรวจสอบหาที่อยู่ของผู้ต้องหาจาก IP Adress ที่เข้าใช้ Internet Banking</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="35">
      ตรวจสอบตาแหน่งผู้ต้องหาจากระบบ GIS รถรับจ้างฯ กรณีมีใบอนุญาตขับรถสาธารณะ</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="714">
      ตรวจสอบข้อมูลการติดต่อผู้ต้องหากับ Call Center ธนาคาร</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="36">
      ตรวจสอบจำนวน/ประเภท/ทะเบียนรถที่ผู้ต้องหาครอบครอง/ถือกรรมสิทธิ์จากระบบฯ</td>
      <td bgcolor="#F3F248"><input type="checkbox" name="ck_checkbox[]" value="715">
      รวบรวมสรุปจัดทำผัง Link Chart เชื่อมโยงข้อมูลบัญชีธนาคารของผู้ต้องหา</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="37">
      ตรวจสอบยี่ห้อ/สี/รุ่น/เลขเครื่อง/เลขตัวถังรถดังกล่าวและหารูป ตย.รถผู้ต้องหา</td>
      <td rowspan="12" style="font-size: 50px" align="center">8</td>
      <td rowspan="12" bgcolor="#1BAF3F" style="font-size: 20px">บัตรเดบิต<br>
        บัตรเครดิต<br>
        E-MONEY<br>
      สวัสดิการแห่งรัฐ</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="81">
      ตรวจสอบการเปิดใช้งานบัตรเครดิตของผู้ต้องหาจากผู้ให้บริการบัตรเครดิต</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="38">
      ตรวจสอบวันจดทะเบียน/การเปลี่ยนแปลง ย้าย โอนกรรมสิทธิ์ ซื้อจากผู้ใด ที่ใด</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="82">
      ตรวจสอบรายละเอียดบัตรเครดิต ข้อมูลผู้ต้องหาที่ให้ไว้ ที่อยู่ เบอร์โทร.ติดต่อ</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="39">
      ตรวจสอบวันสิ้นอายุภาษี ตาหนิรูปพิเศษรถ การประสบอุบัติเหตุ จากระบบประกันภัย</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="83">
      ตรวจสอบรายละเอียดการใช้งานบัตรเครดิต รายการใช้ สถานที่ใช้บัตรเครดิต</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="310">
      ตรวจสอบหาเบอร์ติดต่อ ที่อยู่ แผนที่บ้านผู้ต้องหาจาก บ.ไฟแนนซ์/ บ.ประภันภัยรถ</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="84">
      ตรวจสอบการเปิดใช้งานบัตรเดบิตของผู้ต้องหาจากธนาคารที่เปิดบัญชี</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="311">
      ตรวจสอบชื่อนามสกุล ทะเบียนรถ ผู้ต้องหาในระบบใบสั่งจราจร เพื่อนาข้อมูลไปสืบจับ</td>
      <td>ตรวจสอบรายละเอียดการใช้งานบัตรเดบิต ชำระค่าสินค้า วันเวลาสถานที่ใช้</td>
    </tr>
    <tr>
      <td colspan="2" bgcolor="#FAF202"><input type="checkbox" name="ck_checkbox[]" value="312">
      รวบรวมสรุปจัดทาผัง link Chart ใบอนุญาตขับขี่/ยานพาหนะ/ใบสั่ง รูป ตย.รถผู้ต้องหา</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="85">
      ตรวจสอบการเปิดใช้งาน TRUE Wallet ,Line Pay ระบบ E-MONEY ผู้ต้องหา</td>
    </tr>
    <tr>
      <td rowspan="12" style="font-size: 50px" align="center">4</td>
      <td rowspan="12" bgcolor="#0EF2F4" style="font-size: 20px">การเดินทาง<br>
        หนังสือเดินทาง<br>
      ด่านสกัด</td>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="41">
      ตรวจสอบรายละเอียดหนังสือเดินทางผู้ต้องหา ประเภท เลขที่ ชื่อภาษาอังกฤษ รูปถ่าย</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="86">
      ตรวจสอบข้อมูลผู้ต้องหาที่ให้ไว้กับผู้ให้บริการ ที่อยู่ เบอร์โทรศัพท์ที่ผูกกับระบบ</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="42">
      ตรวจสอบประวัติเดินทางเข้า/ออกประเทศ วันเวลา ช่องทาง จากระบบ PIBIC สตม.</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="87">
      ตรวจสอบรายละเอียดการใช้งาน ชาระค่าสินค้า ตำแหน่งที่ใช้ ขอกล้อง CCTV</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="43">
      ตรวจสอบการเดินทาง/ต้นทาง/ปลายทาง ยานพาหนะ การซื้อตั่ว การชำระเงิน บัญชี ธ.</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="88">
      ตรวจสอบการขึ้นทะเบียนบัตรสวัสดิการแห่งรัฐของผู้ต้องหาจากระบบ กท.คลัง</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="44">
      ตรวจสอบตาหนิรูปพรรณปัจจุบันผู้ต้องหาจากระบบ CCTV ท่ารถ ท่าเรือ ท่าอากาศยาน</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="89">
      ตรวจสอบรายละเอียดใช้บัตรสวัสดิการแห่งรัฐกดเงินสดตู้ ATM วันเวลา ที่ใด</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="45">
      ตรวจสอบบุคคล/เครือข่ายร่วมเดินทาง มารับ/ไปส่ง ยานพาหนะมารับ/ไปส่ง</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="810">
      ตรวจสอบรายละเอียดการใช้บัตรสวัสดิการแห่งรัฐลดหย่อนค่าใช้จ่าย อย่างไร</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="46">
      ตรวจสอบพฤติกรรมการเดินทาง รถเมล์ รถรับจ้าง/สาธารณะ รถส่วนตัว สัมภาระฯลฯ</td>
      <td bgcolor="#D3F217"><input type="checkbox" name="ck_checkbox[]" value="811">
      รวบรวมสรุปจัดทำผัง Link Chart เชื่อมโยงข้อมูลบัตรเครดิตฯ ของผู้ต้องหา</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="47">
      ตรวจสอบการเข้าพักโรงแรม บ้านเช่า อพาร์ทเม้นท์ผู้ต้องหา จากระบบ ตม.30,โรงแรม</td>
      <td rowspan="12" style="font-size: 50px" align="center">9</td>
      <td rowspan="12" bgcolor="#EE0EEE" style="font-size: 20px">SOCIAL<br>
      NETWORK</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="91">
      ตรวจสอบการเปิดใช้บัญชี Facebook,Instagram,Twitter,Line,Wechatฯลฯ</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="48">
      ตรวจสอบข้อมูลการได้รับบัตรสวัสดิการรัฐผู้ต้องหาจากระบบบัตรสวัสดิการแห่งรัฐ</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="92">
      ตรวจสอบชื่อนามสกุล ชื่อเล่น ไทย/ภาษาอังกฤษผู้ต้องหาจาก Google ฯลฯ</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="49">
      ตรวจสอบการใช้บัตรสวัสดิการแห่งรัฐของผู้ต้องหาว่า นำมาใช้ลดค่าเดินทางที่ใด เมื่อใด</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="93">
      ตรวจสอบเบอร์โทรศัพท์ของผู้ต้องหาว่ามีการผูกกับบัญชี Social ใดบ้าง</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="410">
      ตรวจสอบทะเบียนรถผู้ต้องหา ยี่ห้อ/รุ่น/สี/ตาหนิพิเศษ ผ่านด่านสกัดกั้น ปส. หรือไม่</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="94">
      ตรวจสอบความเคลื่อนไหว Social โพสต์ ไลค์ กิจวัตรประจาวันของผู้ต้องหา</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="411">
      ตรวจสอบการเดินทางผ่านด่านสกัดกั้น วันเวลาที่ใด วิเคราะห์เส้นทาง ต้น/ปลายทาง</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="95">
      ตรวจสอบการอัพโหลด รูปถ่าย รูปภาพ วีดีโอ ภาพผู้ต้องหา ตำแหน่งสถานที่</td>
    </tr>
    <tr>
      <td colspan="2" bgcolor="#EAF403"><input type="checkbox" name="ck_checkbox[]" value="412">
      รวบรวมสรุปจัดทำผัง Time line เชื่อมโยงการเดินทางของผู้ต้องหาตามลำดับเวลา</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="96">
      ตรวจสอบรายละเอียดเครือข่ายบุคคลที่เกี่ยวข้องกับบัญชี Social ของผู้ต้องหา</td>
    </tr>
    <tr>
      <td rowspan="18" style="font-size: 50px" align="center">5</td>
      <td rowspan="18" bgcolor="#E28633" style="font-size: 20px">ประกันสังคม<br>
        ประกันสุขภาพ<br>
        ประกันชีวิต<br>
      ภาษี</td>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="51">
      ตรวจสอบประวัติการทำงานผู้ต้องหาจากระบบประกันสังคม ทำงานอยู่/ลาออก/ส่งเงินฯ</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="97">
      ตรวจติดตามวิเคราะห์บทสนทนาระหว่างเครือข่าย Social หาที่อยู่ผู้ต้องหา</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="52">
      ตรวจสอบชื่อบริษัทที่ทำงาน ที่ตั้ง แผนที่ ประเภทธุรกิจ จำนวนพนักงาน เบอร์ติดต่อ</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="98">
      ตรวจสอบการเข้าใข้ Social ของผู้ต้องหาผ่านช่องทางใด มือถือ คอมพิวเตอร์</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="53">
      ตรวจสอบประวัติผู้ต้องหาที่ให้ไว้กับบริษัทที่ทำงาน รายละเอียดมือถือ ที่พัก แผนที่บ้าน</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="99">
      ตรวจสอบ IP Adress จากการใช้อุปกรณ์มือถือ คอมฯ เข้าใช้งาน Social</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="54">
      ตรวจสอบรายละเอียดผู้ค้ำประกันฯ ชื่อนามสกุล ที่อยู่ เบอร์มือถือ ความสัมพันธ์ฯ</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="910">
      ตรวจสอบ IP Adress หาที่อยู่ของผู้ต้องหาจากผู้ให้บริการอินเตอร์เน็ต</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="55">
      ตรวจสอบรายละเอียดประกันสุขภาพ บัตรทอง/30 บาท ของผู้ต้องหาจากระบบ สปสช.</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="911">
      ดำเนินการใช้ Social Network ติดต่อกับผู้ต้องหา/เพื่อน เพื่อหาที่อยู่ผู้ต้องหา</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="56">
      ตรวจสอบรายละเอียดประเภทสิทธิการรักษาพยาบาล สถานพยาบาลปฐมภูมิ/ประจำ</td>
      <td bgcolor="#E7F804"><input type="checkbox" name="ck_checkbox[]" value="912">
      รวบรวมสรุปจัดทำผัง Link Chart รายละเอียด Social Network ผู้ต้องหา</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="57">
      ตรวจสอบที่ตั้งสถานพยาบาลที่ผู้ต้องหาขึ้นทะเบียน วันเดือนปีทำบัตร ที่อยู่ที่ให้ไว้</td>
      <td rowspan="12" style="font-size: 50px" align="center">10</td>
      <td rowspan="12" bgcolor="#F9BA2B" style="font-size: 20px">DELIVERY<br>
        SHOPPING<br>
      SHIPPING</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="101">
      ตรวจสอบชื่อนามสกุล มือถือของผู้ต้องหากับผู้ให้บริการ DELIVERY ส่งอาหาร</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="58">
      ตรวจสอบเบอร์โทรศัพท์ผู้ต้องหาที่ให้ไว้ ประวัติการรักษา การนัดหมายมารักษา</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="102">
      ตรวจสอบรายละเอียดการสั่ง DELIVERY ผู้ต้องหา ว่าให้ไปส่ง วันเวลา สถานที่</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="59">
      ตรวจสอบข้อมูลรายละเอียดของญาติที่ติดต่อได้ในฐานข้อมูล ชื่อนามสกุลที่อยู่ เลขบัตร</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="103">
      ตรวจสอบผู้ใช้มือถือผู้ต้องหาโทรสั่ง อายุ สาเนียง ตาหนิรูปพรรณผู้ที่มาอาหาร</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="510">
      ตรวจสอบชื่อนามสกุล เลขบัตร ปชช. ผู้ต้องหาทาประกันชีวิตหรือไม่ จากระบบ คปภ.</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="104">
      ตรวจสอบช่องทางการใช้บริการ DELIVERY ผ่านโทรศัพท์/อินเตอร์เน็ต หา IP</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="511">
      ตรวจสอบบริษัทที่รับทาประกันชีวิต ที่ตั้ง เบอร์ติดต่อ ประเภทกรมธรรม์/เลขที่</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="105">
      ตรวจสอบการใช้บริการของผู้ต้องหากับ บ.สั่งซื้อสินค้าออนไลน์ LAZADA ฯลฯ</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="512">
      ตรวจสอบอายุกรมธรรม์/ทุนประกัน ผู้รับสิทธิ/ผลประโยชน์ ช่องทางชำระเบี้ยประกัน</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="106">
      ตรวจสอบข้อมูลผู้ต้องหาที่ให้ไว้กับบริษัท อีเมลล์ มือถือ ที่อยู่จัดสิ่งสินค้า</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="513">
      ตรวจสอบรายละเอียดส่วนตัวผู้ต้องหาที่ให้ไว้กับบริษัทประกันชีวิต ที่อยู่ แผนที่ มือถือ</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="107">
      ตรวจสอบข้อมูลผู้ต้องหา ช่องทางการชาระเงิน บัตรเครดิต บัญชีธนาคาร</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="514">
      ตรวจสอบการเคลมประกันชีวิต กรณีกรมธรรม์ประเภทสุขภาพ โรงพยาบาล/นัดรักษา</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="108">
      ตรวจสอบข้อมูลผู้ต้องหากับบริษัทไปรษณ๊ย์ไทยมีการส่ง/รับ จดหมาย/พัสดุ</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="515">
      ตรวจสอบบัตรประจาตัวผู้เสียภาษีของผู้ต้องหาจากระบบฐานข้อมูลกรมสรรพากร</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="109">
      ตรวจสอบรายละเอียดที่อยู่ต้นทาง/ปลายทาง ที่มีชื่อผู้ต้องหาเกี่ยวข้อง</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="516">
      ตรวจสอบรายละเอียดเลขประจำตัวผู้เสียภาษี ประเภท การลดหย่อน หลักฐานประกอบ</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="1010">
      ตรวจสอบการใช้บริการผู้ต้องหา/ญาติกับ บ.รับส่งพัสดุ KERRY,Line Man ฯลฯ</td>
    </tr>
    <tr>
      <td colspan="2"><input type="checkbox" name="ck_checkbox[]" value="517">
      ตรวจสอบที่อยู่ช่องทางการการรับคืนภาษี เช็ค โอนผ่านระบบพร้อมเพย์ บัญชี ธ. มือถือ</td>
      <td><input type="checkbox" name="ck_checkbox[]" value="1011">
      ตรวจสอบรายละเอียดที่อยู่ต้นทาง/ปลายทาง ที่มีชื่อผู้ต้องหาเกี่ยวข้อง</td>
    </tr>
    <tr>
      <td colspan="2" bgcolor="#ECF706"><input type="checkbox" name="ck_checkbox[]" value="518">
      รวบรวมสรุปจัดทาผัง Link Chart เชื่อมโยงประกันสังคม/สุขภาพ/ชีวิตฯ ของผู้ต้องหา</td>
      <td bgcolor="#E9F305"><input type="checkbox" name="ck_checkbox[]" value="1012">
      รวบรวมสรุปจัดทำผัง Link Chart ข้อมูลการใช้บริการ DELIVERYฯ ผู้ต้องหา</td>
    </tr>
  </tbody>
</table>
    <div>
    <label>เจ้าหน้าที่สืบสวน/บันทึก</label>
		<select id="ck_detective" name="ck_detective" placeholder="ระบุเจ้าหน้าที่สืบสวน/บันทึก">
			<option value="" selected> </option>
			<?php
                    $bsdp = mysqli_query($conn, "SELECT * FROM `police_name_bsdetective` order by `aid_bsdp` DESC");
                    while($row_bsdp = mysqli_fetch_array($bsdp))
                    {
                        echo "<option value='{$row_bsdp['aid_bsdp']}'>{$row_bsdp['bsdp_name']}</option>";
                    }
                ?>
		</select><br>
        
        <label>วันที่</label>
               <p><input type="text" name="ck_date" id="datepicker" autocomplete="off" class="form-control"></p>
              <script>
            function set_cal(ele)//function สร้างตัวเลือกปฎิทิน
            {
              $( ele ).datepicker({
                  onSelect:(date_text)=>
                  {
                    let arr=date_text.split("/");
                    let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])+543).toString();
                    $(ele).val(new_date);
                    $(ele).css("color","");
                  },
                  beforeShow:()=>{

                    if($(ele).val()!="")
                    {
                      let arr=$(ele).val().split("/");
                      let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])-543).toString();
                      $(ele).val(new_date);

                    }

                    $(ele).css("color","white");
                  },
                  onClose:()=>{

                      $(ele).css("color","");

                      if($(ele).val()!="")
                      {
                          let arr=$(ele).val().split("-");
                          if(parseInt(arr[2])<2500)
                          {
                              let new_date=arr[0]+"-"+arr[1]+"-"+(parseInt(arr[2])).toString();
                              $(ele).val(new_date);
                          }
                      }
                  },
                  dateFormat:"yy-mm-dd", //กำหนดรูปแบบวันที่เป็น วัน/เดือน/ปี
                  changeMonth:true,//กำหนดให้เลือกเดือนได้
                  changeYear:true,//กำหนดให้เลือกปีได้
                  showOtherMonths:true,//กำหนดให้แสดงวันของเดือนก่อนหน้าได้
              });
            }
          $(document).ready(function(){
            //เรียก function set_cal เมื่อเปิดหน้าเว็บ โดยส่ง object element ที่มี id เป็น datepicker เป็นพารามิเตอร์
            set_cal( $("#datepicker") );
          })
          </script>
        <!-- สำหรับ วันเกิด แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ -->
    </div>
    <p>
    <input type="submit" value="บันทึกข้อมูล" class="btn btn-success" >
    <td> <a href="../policeinnopolis/WatchmanData/Show_All.php?pcid=<?= $pcid ?>&page=warrant" class="btn btn-warning" >ยกเลิก</a> </td>
    </p>
    <br>
</form>
    
<!--<script src="/js/datetimepicker2.5.1/jquery.ui.datepicker-th.js" type="text/javascript"></script>-->
<script>
// ชื่อ >> value >> set selct auto (สคลิปตรวจสอบ Selected) จำเป็นต้องก็อปปี้ <script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js" type="text/javascript">
function auto_select(sid, value)
{
    $('#' + sid).val(value);
}
    
$(document).ready(function() {
<?php
    echo "auto_select('ck_checkbox', '{$row['ck_checkbox']}');\n";
    // convert db date to thai dates
	//echo "auto_thaidate('fun_cl_date', '{$row['fun_cl_date']}');\n";
?>
    });
</script>
</body>
</html>