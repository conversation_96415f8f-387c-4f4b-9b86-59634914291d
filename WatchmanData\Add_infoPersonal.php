<link rel="stylesheet" href="/policeinnopolis/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 20px;
    }
.form-control::placeholder {
  color: sandybrown;
}
</style>
    <!--  เลือกเพศอัตโนมัต -->
<script language="javascript" src="/policeinnopolis/jQuery/jquery-3.5.1.min.js"></script>  
<script>
    function doPrefixChange()
    {
        var selected = $("#ps_cl_prefix option:selected").val();
        if(selected == "นาย") {
            $("#ps_cl_sex1").prop("checked", true);
        }
        else if(selected == "นาง"){
            $("#ps_cl_sex2").prop("checked", true);
        }
        else if(selected == "น.ส."){
            $("#ps_cl_sex2").prop("checked", true);
        }
        else if(selected == "ด.ช."){
            $("#ps_cl_sex1").prop("checked", true);
        }
        else if(selected == "ด.ญ."){
            $("#ps_cl_sex2").prop("checked", true);
        }
        else {
            $("#ps_cl_sex1").prop("checked", false);
            $("#ps_cl_sex2").prop("checked", false);
            $("#ps_cl_sex3").prop("checked", false);
        }
    }
</script>
   
<!-- สำหรับวันที่ เดือนไทย ปี พ.ศ. และ เลือนเลือกปี พ.ศ.ได้  -->
<link rel="stylesheet" href="http://code.jquery.com/ui/1.9.1/themes/base/jquery-ui.css" />
<script src="http://code.jquery.com/jquery-1.8.2.js"></script>
<script src="/policeinnopolis/js/jquery-ui.js"></script>
<script src="../SweetAlert2/dist/sweetalert2.all.min.js"></script>
</head>

<body>
		<label>คำนำหน้า</label>
		<select id="ps_cl_prefix" name="ps_cl_prefix" class="form-select form-select-sm"  onChange="doPrefixChange()">
			<option value="" selected> </option>
			<option value="นาย">นาย</option>
			<option value="นาง">นาง</option>
			<option value="น.ส.">น.ส.</option>
			<option value="ด.ช.">ด.ช.</option>
			<option value="ด.ญ.">ด.ญ.</option>
		</select>

		<label>เพศ</label><br>
		<label><input name="ps_cl_sex" id="ps_cl_sex1" type="radio" value="ชาย" checked="checked" /> ชาย </label>
		<label><input name="ps_cl_sex" id="ps_cl_sex2" type="radio" value="หญิง" /> หญิง </label>					<label><input name="ps_cl_sex" id="ps_cl_sex3" type="radio" value="LGBTQ" /> LGBTQ </label><br>

		<label>ชื่อ <span style="color: #F90004">* จำเป็น</span> </label>
		<input type = "text" name = "ps_cl_name" class="form-control" placeholder="ระบุ เฉพาะชื่อจริง"   Required  >
		<label>นามสกุล <span style="color: #F90004">* จำเป็น</span> </label>
		<input type = "text" name = "ps_cl_surname" class="form-control" placeholder="ระบุ นามสกุลจริง"  Required >
		<label>ชื่อเล่น</label>
		<input type = "text" name = "ps_cl_nickname" class="form-control" placeholder="ระบุ ชื่อเล่น หรือฉายา" >
		<br>
		<label>วันเดือนปีเกิด <span style="color: #F90004">* รูปแบบ ปี ค.ศ.-เดือน-วัน</span></label>
		<input type="text" name="ps_cl_birthday2" id="datepicker" value="" placeholder="ใช้รูปแบบ 2023-09-25 เท่านั้น หรือเลือกจากปฎิทิน" class="form-control" style="width:350px;" autocomplete="off">
			<p id="result" style="color: red"></p>
        <script>
            function set_cal(ele)//function สร้างตัวเลือกปฎิทิน
            {
              $( ele ).datepicker({
                  onSelect:(date_text)=>
                  {
                    let arr=date_text.split("/");
                    let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])+543).toString();
                    $(ele).val(new_date);
                    $(ele).css("color","");
                  },
                  beforeShow:()=>{

                    if($(ele).val()!="")
                    {
                      let arr=$(ele).val().split("/");
                      let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])-543).toString();
                      $(ele).val(new_date);

                    }

                    $(ele).css("color","white");
                  },
                  onClose:()=>{

                      $(ele).css("color","");

                      if($(ele).val()!="")
                      {
                          let arr=$(ele).val().split("-");
                          if(parseInt(arr[2])<2500)
                          {
                              let new_date=arr[0]+"-"+arr[1]+"-"+(parseInt(arr[2])).toString();
                              $(ele).val(new_date);
                          }
                      }
                  },
                  dateFormat:"yy-mm-dd", //กำหนดรูปแบบวันที่เป็น วัน/เดือน/ปี
                  changeMonth:true,//กำหนดให้เลือกเดือนได้
                  changeYear:true,//กำหนดให้เลือกปีได้
                  showOtherMonths:true,//กำหนดให้แสดงวันของเดือนก่อนหน้าได้
              });
            }
          $(document).ready(function(){
            //เรียก function set_cal เมื่อเปิดหน้าเว็บ โดยส่ง object element ที่มี id เป็น datepicker เป็นพารามิเตอร์
            set_cal( $("#datepicker") );
          })
          </script>
        <!-- สำหรับ วันที่ แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ -->
	
                
		<label>บิดา</label>
		<input type = "text" name = "ps_cl_father" class="form-control" placeholder="ชื่อบิดา" >
		<label>เลขบัตรประชาชน บิดา</label>
		<input type = "text" name = "ps_cl_father_pid" class="form-control" placeholder="เลขบัตรประชาชน บิดา" >
		<label>มารดา</label>
		<input type = "text" name = "ps_cl_mother" class="form-control" placeholder="ชื่อมารดา" >
		<label>เลขบัตรประชาชน มารดา</label>
		<input type = "text" name = "ps_cl_mother_pid" class="form-control" placeholder="เลขบัตรประชาชน มารดา" >

        <label>สถานภาพสมรส</label>
		<select id="ps_cl_marital_status" name="ps_cl_marital_status" class="form-select form-select-sm" >
			<option value="" selected> </option>
			<option value="โสด">โสด</option>
			<option value="สมรส">สมรส</option>
			<option value="หย่า">หย่า</option>
			<option value="หม้าย">หม้าย</option>
			<option value="อื่น ๆ">อื่น ๆ</option>
		</select>
        <br>
        
        <label>สถานะการมีชีวิตอยู่  <span style="color: #F90004">* จำเป็นต้องเลือก</span> </label><br>
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
			<label><input name="ps_cl_death" id="ps_cl_death1" type="radio" value="1" onclick="deathChange()" required /> ยังมีชีวิตอยู่ </label>
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
			<label><input name="ps_cl_death" id="ps_cl_death2" type="radio" value="2" onclick="deathChange()" /> เสียชีวิตแล้ว </label>
		
        <br><br>
        
        <label>วันเดือนปีที่เสียชีวิต</label>
        <input type="hidden" name="date_of_death" >
        <input type="text" name="date_of_death" id="datepicker2" value="" placeholder="เฉพาะผู้เสียชีวิตแล้ว" class="form-control" style="width:250px;" autocomplete="off">
        
        <script type="text/javascript">
            // สำหรับ เปลี่ยนวันตาย ให้ disable หรือ enable
            function deathChange()
            {
                var rad1 = $("#ps_cl_death1");
                if(rad1.is(":checked")) 
                {
                    $('#datepicker2').val("");
                    $('#datepicker2').prop("disabled", true);                    
                }
                var rad2 = $("#ps_cl_death2");
                if(rad2.is(":checked")) 
                {
                    $('#datepicker2').prop("disabled", false);
                }
                //console.log("rad1:", rad1.is(":checked"), "rad2:", rad2.is(":checked"))
            }
            
            // datepicker
            $(function(){

                $.datetimepicker.setLocale('th'); // ต้องกำหนดเสมอถ้าใช้ภาษาไทย และ เป็นปี พ.ศ.
                // กรณีใช้แบบ input
                $("#datepicker2").datetimepicker({
                    timepicker:false,
                    format:'Y-m-d',  // กำหนดรูปแบบวันที่ ที่ใช้ เป็น 00-00-0000			
                    lang:'th',  // ต้องกำหนดเสมอถ้าใช้ภาษาไทย และ เป็นปี พ.ศ.
                    onSelectDate:function(dp,$input){
                        var yearT=new Date(dp).getFullYear()-0;  
                        var yearTH=yearT;  // ถ้าจะให้แสดงเป็น พ.ศ. ให้เป็น yearT+543
                        var fulldate=$input.val();
                        var fulldateTH=fulldate.replace(yearT,yearTH);
                        $input.val(fulldateTH);
                    },
                });       
                // กรณีใช้กับ input ต้องกำหนดส่วนนี้ด้วยเสมอ เพื่อปรับปีให้เป็น ค.ศ. ก่อนแสดงปฏิทิน
                $("#datepicker2").on(function(e){
                    var dateValue=$(this).val();
                    if(dateValue!=""){
                            var arr_date=dateValue.split("-"); // ถ้าใช้ตัวแบ่งรูปแบบอื่น ให้เปลี่ยนเป็นตามรูปแบบนั้น
                            // ในที่นี้อยู่ในรูปแบบ 00-00-0000 เป็น d-m-Y  แบ่งด่วย - ดังนั้น ตัวแปรที่เป็นปี จะอยู่ใน array
                            //  ตัวที่สอง arr_date[2] โดยเริ่มนับจาก 0 
                            dateValue=dateValue.replace(arr_date[0],yearT);
                            $(this).val(dateValue);													
                    }		
                });
            });
            </script>
        <!-- สำหรับ วันที่ แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ -->
        
       
		<div class="mb-3">
			<label for="formFileMultiple" class="form-label">รูปภาพหน้าตรงของบุคคล</label>
			 <input class="form-control" type="file" id="ps_cl_image" name="ps_cl_image" multiple>
		</div>
