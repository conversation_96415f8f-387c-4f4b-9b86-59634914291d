<?php
include '../Condb.php';
include '../users.inc.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// เมื่อมีการใช้ ตัวแปร Session จะต้องเปิดก่อน
//session_start();  // เปิดตัวแปร session
/*if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
*/

//echo '<pre>';
//print_r($_POST);
//echo '</pre>';
//
//echo '<hr';
//
//echo '<pre>';
//var_dump($_POST);
//echo '</pre>';
//
//exit();

$fr_cl_aid = $_POST[ 'fr_cl_aid' ];
$fr_cl_idcard = $_POST[ 'fr_cl_idcard' ];
$fr_cl_status = $_POST[ 'fr_cl_status' ];
$fr_cl_offense = $_POST[ 'fr_cl_offense' ];
$fr_cl_in_prison = $_POST[ 'fr_cl_in_prison' ];
$fr_cl_start = $_POST[ 'fr_cl_start' ];
$fr_cl_freed = $_POST[ 'fr_cl_freed' ];
$fr_cl_adrress1 = $_POST[ 'fr_cl_adrress1' ];
$fr_cl_adrress2 = $_POST[ 'fr_cl_adrress2' ];
$fr_cl_location = $_POST[ 'fr_cl_location' ];
$fr_cl_police = $_POST[ 'fr_cl_police' ];
$region = $_POST[ 'region' ];
$provincial = $_POST[ 'provincial' ];
$station = $_POST[ 'station' ];
$fr_cl_check_date = $_POST[ 'fr_cl_check_date' ];
$fr_cl_dna = $_POST[ 'fr_cl_dna' ];
$fr_cl_cause_no_keepdna = $_POST[ 'fr_cl_cause_no_keepdna' ];
$fr_cl_tracking = $_POST[ 'fr_cl_tracking' ];
$fr_cl_remark = $_POST[ 'fr_cl_remark' ];

// save Image
$file1 = $_FILES[ 'fr_cl_image' ][ 'tmp_name' ];
if ( is_uploaded_file( $file1 ) ) {
    // เปลี่ยนชื่อไฟล์รูปภาพก่อน
    $fr_cl_image = "/policeinnopolis/uploaded/Image1/" . basename( $_FILES[ 'fr_cl_image' ][ 'name' ] );	    
    // file + ext    
    $ext = strrchr($fr_cl_image, ".");    
    $fr_cl_image = "../policeinnopolis/uploaded/Image1/File_" . time() . $ext; // file+time+ext        
    move_uploaded_file( $file1, $fr_cl_image ); 
    
} else {
  $fr_cl_image = '';
}

// save file
$file2 = $_FILES[ 'fr_cl_home_image' ][ 'tmp_name' ];
if ( is_uploaded_file( $file2 ) ) {
  $fr_cl_home_image = "/policeinnopolis/uploaded/Image1/" . $_FILES[ 'fr_cl_home_image' ][ 'name' ];	
  move_uploaded_file( $file2, "../" . $fr_cl_home_image );  
} else {
  $fr_cl_home_image = '';
}

$sql ="SELECT * FROM `wm_tb_freed` WHERE fr_cl_aid='$fr_cl_aid' ";
$result = mysqli_query($conn,$sql);
$row = mysqli_fetch_array($result);
if($row) {
        $fr_cl_aid = $row['fr_cl_aid'];
    
        $sql = "UPDATE wm_tb_freed SET " .
		"fr_cl_idcard = '$fr_cl_idcard'," .
		"fr_cl_status = '$fr_cl_status'," .
		"fr_cl_offense = '$fr_cl_offense', " . 
		"fr_cl_in_prison = '$fr_cl_in_prison'," .
		"fr_cl_start = '$fr_cl_start',".
		"fr_cl_freed = '$fr_cl_freed', ".
		"fr_cl_adrress1 = '$fr_cl_adrress1', " .
		"fr_cl_adrress2 = '$fr_cl_adrress2', " .
		"fr_cl_location = '$fr_cl_location', " .
		"fr_cl_police = '$fr_cl_police', " .
	  	"region = '$region', " .
        "provincial = '$provincial', " .
        "station = '$station', " .
		"fr_cl_check_date = '$fr_cl_check_date', " .
		"fr_cl_dna = '$fr_cl_dna', " .
		"fr_cl_cause_no_keepdna = '$fr_cl_cause_no_keepdna', " .
        "fr_cl_tracking = '$fr_cl_tracking', " .
		"fr_cl_remark = '$fr_cl_remark' " ;
	
	if($fr_cl_image !== '')
	{
		$sql =  $sql . ", fr_cl_image = '$fr_cl_image' ";
	}
	
	elseif ($fr_cl_home_image !== ''){
		$sql =  $sql . ", fr_cl_home_image = '$fr_cl_home_image' ";
	}

	$sql = $sql . " WHERE fr_cl_aid = '$fr_cl_aid' ";
    
}else{
        $sql = "INSERT INTO wm_tb_freed (fr_cl_idcard, fr_cl_status, fr_cl_offense, fr_cl_in_prison, fr_cl_start, fr_cl_freed, fr_cl_adrress1, fr_cl_adrress2, fr_cl_location, fr_cl_police, region, provincial, station, fr_cl_check_date, fr_cl_dna, fr_cl_cause_no_keepdna, fr_cl_tracking, fr_cl_remark, fr_cl_image, fr_cl_home_image) VALUES('$fr_cl_idcard', '$fr_cl_status', '$fr_cl_offense', '$fr_cl_in_prison', '$fr_cl_start', '$fr_cl_freed', '$fr_cl_adrress1', '$fr_cl_adrress2', '$fr_cl_location', '$fr_cl_police', '$region', '$provincial', '$station', '$fr_cl_check_date', '$fr_cl_dna', '$fr_cl_cause_no_keepdna', '$fr_cl_tracking', '$fr_cl_remark', '$fr_cl_image', '$fr_cl_home_image') ";
    }

$result=mysqli_query($conn,$sql);
if($result){
	echo "<script>alert('บันทึกข้อมูลเรียบร้อย');</script>";	
}else{
	echo "<script>alert(\"บันทึกข้อมูลไม่สำเร็จ\\n" . print_r(mysqli_error($conn), true) . "\");</script>";
}


//บันทึกประวัติการแก้ไข เพิ่ม ข้อมูล
$acc = $user['account'];
$inputs = str_replace("'", "", compact_array($_POST));
$prev1 = "";
$prev2 = str_replace("'", "", implode(',', $row_0));

if($row){
    $prev2 = str_replace("'", "", implode(',', $row_0));
    $sql3 = "INSERT wm_tb_user_action (at_cl_user,at_cl_date,at_cl_action,at_cl_input,at_cl_prev) VALUES('$acc',CURRENT_TIMESTAMP,'Edit freed {$fr_cl_idcard}', '$inputs', '{$prev2}')";
}else{
    $prev1 = "";//str_replace("'", "", implode(',', $row_0));
    $sql3 = "INSERT wm_tb_user_action (at_cl_user,at_cl_date,at_cl_action,at_cl_input,at_cl_prev) VALUES('$acc',CURRENT_TIMESTAMP,'Add new freed {$fr_cl_idcard}', '$inputs', '{$prev1}')";
}
mysqli_query($conn, $sql3);
//

mysqli_close($conn);

	echo "<script>window.location='Show_All.php?pcid=" . $fr_cl_idcard . "&page=freed';</script>";
?>
