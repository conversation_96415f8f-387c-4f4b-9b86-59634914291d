<script src="/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>
<script src="../SweetAlert2/dist/sweetalert2.all.min.js"></script>

<?php //PDO
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);


$id = isset($_GET['mi_cl_aid']) ? $_GET['mi_cl_aid'] : '';

//get month from select or current month
$month = isset($_GET['month']) ? $_GET['month'] : date("m");
$year = isset($_GET['year']) ? $_GET['year'] : date("Y");

$m1 = '';
$m2 = '';
$m3 = '';
$m4 = '';
$m5 = '';
$m6 = '';
$m7 = '';
$m8 = '';
$m9 = '';
$m10 = '';
$m11 = '';
$m12 = '';
switch($month)
{
    case "01": $m1 = "selected"; break;
    case "02": $m2 = "selected"; break;
    case "03": $m3 = "selected"; break;
    case "04": $m4 = "selected"; break;
    case "05": $m5 = "selected"; break;
    case "06": $m6 = "selected"; break;
    case "07": $m7 = "selected"; break;
    case "08": $m8 = "selected"; break;
    case "09": $m9 = "selected"; break;
    case "10": $m10 = "selected"; break;
    case "11": $m11 = "selected"; break;
    case "12": $m12 = "selected"; break;
}

?>

<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link rel="stylesheet" href="/bootstrap/css/bootstrap-grid.css">
<div class="container-fluid" align="left">
<div class=" h3 text-center  alert alert-success mb-4 mt-4 " role="alert" >ภารกิจ/คำสั่ง/ข่าวสาร ฝ่ายสืบสวน <?= $name_station ?> </div>
<style>
    .swal2-popup {
    width: 500px !important;
    height: auto !important;
    font-size: 14px !important;
}
</style>
<div align="left">
    <table width="80%" border="0" cellspacing="1" cellpadding="1">
      <tbody>
        <tr>
          <td width="12%">&nbsp;<a href="Add_mission.php" class="btn btn-success btn-lg mb-4 <?= $add_btn ?>" >เพิ่มข้อมูล</a></td>
            
          <td width="19%"><label>เลือกปี </label>
            <select id="mi_cl_year" name="mi_cl_year" class="form-select col-8 d-lg-inline" style="background-color: khaki; font-size: 16px" onChange="dates_change()">
              <option value="">เลือก</option>
              <?php
                $cur_y = date("Y") + 2;
                for($y=2020; $y<$cur_y; $y++) {
                    $sel = ($y == $year) ? "selected" : '';
                    echo "<option value='$y' $sel> ". ($y + 543) ." </option>";
                }
            ?>
          </select></td>
          <td width="65%"><label>เดือน</label>
            <select id="mi_cl_month" name="mi_cl_month" class="form-select col-3 d-lg-inline" style="background-color: khaki; font-size: 16px" onChange="dates_change()">
              <option value="">เลือก</option>
              <option <?= $m1 ?> value="01">มกราคม</option>
              <option <?= $m2 ?> value="02">กุมภาพันธ์</option>
              <option <?= $m3 ?> value="03">มีนาคม</option>
              <option <?= $m4 ?> value="04">เมษายน</option>
              <option <?= $m5 ?> value="05">พฤษภาคม</option>
              <option <?= $m6 ?> value="06">มิถุนายน</option>
              <option <?= $m7 ?> value="07">กรกฎาคม</option>
              <option <?= $m8 ?> value="08">สิงหาคม</option>
              <option <?= $m9 ?> value="09">กันยายน</option>
              <option <?= $m10 ?> value="10">ตุลาคม</option>
              <option <?= $m11 ?> value="11">พฤศจิกายน</option>
              <option <?= $m12 ?> value="12">ธันวาคม</option>
          </select></td>
          <td width="2%">&nbsp;</td>
          <td width="2%">&nbsp;</td>
        </tr>
      </tbody>
    </table>
</div>


<div class="table-responsive">
<table width="100%" height="94" border="1" cellpadding="1" cellspacing="1" class="table-bordered table-hover mb-4 mt-4">
  <tbody>
    <tr>
	  <td bgcolor="#94FC60" style="color: #00000; text-align: center; border-color: navy">ลำดับ</td>   
      <td bgcolor="#94FC60" style="color: #00000; text-align: center; border-color: navy">วันที่</td>
      <td bgcolor="#94FC60" style="color: #00000; text-align: center; border-color: navy">เวลา</td>
      <td bgcolor="#94FC60" style="color: #00000; text-align: center; border-color: navy">หัวข้อ</td>
      <td bgcolor="#94FC60" style="color: #00000; text-align: center; border-color: navy">ภารกิจ/คำสั่ง/ข่าวสาร</td>
      <td bgcolor="#94FC60" style="color: #00000; text-align: center; border-color: navy">ผู้ปฏิบัติ</td>
      <td bgcolor="#94FC60" style="color: #00000; text-align: center; border-color: navy">ผู้บันทึก</td>
      <td bgcolor="#94FC60" style="color: #00000; text-align: center; border-color: navy">ไฟล์แนบ</td>
      <td colspan="3" bgcolor="#94FC60" style="color: #00000; text-align: center; border-color: navy"></td>
    </tr>
	  
<?php
$sql = "SELECT * FROM wm_tb_mission ".
       "WHERE station='$station' AND MONTH(mi_cl_date) = :month AND YEAR(mi_cl_date) = :year ORDER BY mi_cl_date ASC ";  //ตั้งตัวแปร sql เลือกฐานข้อมูลจากตารางที่ชื่อ wm_tb_personal
if($id != ''){
	$sql = $sql . " WHERE mi_cl_aid = :id ";
}

$stmt = $pdo->prepare($sql);
$stmt->bindParam(':month', $month);
$stmt->bindParam(':year', $year);

if($id != ''){
    $stmt->bindParam(':id', $id);
}
try{
    $stmt->execute();
}catch (PDOException $e) {
    echo '$sql Failed: '. $e->getMessage();
}
                  
// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no = 1;
while($row_mi = $stmt->fetch(PDO::FETCH_ASSOC))	//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
{
    //ฟังก์ชั่น วันที่ ดึงจาก condb
 $strDate = DateThai( $row_mi["mi_cl_date"] );
    
    //ตรวจสอบไฟล์ สส.1 ก่อนว่า มีไฟล์ในตารางไหม หากมี ให้ส่งเป็นลิงค์
	$pdf = $row_mi["mi_cl_file"];
	if($pdf !== '')
	{
		$links = '<a href="/' . $row_mi["mi_cl_file"] .'" target="_blank">Download</a>';
	}
	else {
		$links = 'ไม่มีไฟล์';
	}
    
$time = $row_mi["mi_cl_time"];
    
    if($time==''){
        $time = '';
    }else{
        $time = $row_mi["mi_cl_time"];
    }
?>	

    <tr>
	  <td><?= $no ?> </td>
	<!--   <td><?= $row_mi["mi_cl_aid"]?></td>   -->
      <td nowrap="nowrap">&nbsp;<?= $strDate ?>&nbsp;</td>
      <td nowrap="nowrap">&nbsp;<?= $row_mi["mi_cl_time"]?>&nbsp;</td>
      <td>&nbsp;<?= $row_mi["mi_cl_mission"]?>&nbsp;</td>
      <td style="text-align: left" >&nbsp;<?= $row_mi["mi_cl_mission_details"]?>&nbsp;</td>
      <td style="text-align: left" >&nbsp;<?= $row_mi["mi_cl_worker"]?>&nbsp;</td>
      <td style="text-align: left" >&nbsp;<?= $row_mi["mi_cl_record"]?>&nbsp;</td>
      <td>&nbsp;<?= $links ?>&nbsp;</td>
	  
	  <td>&nbsp;<a href="Edit_mission.php?id=<?= $row_mi["mi_cl_aid"] ?>" class="btn btn-warning mb-4 <?= $edit_btn ?>" >แก้ไข</a>&nbsp; </td>
	  <td>&nbsp;<button href="#" class="btn btn-danger mb-4" onClick="deleteItem(<?= $row_mi["mi_cl_aid"] ?>)"<?=$del_btn?>>ลบ</button>&nbsp; </td>
    </tr>
  </tbody>
<?php
	$no++;
}//while
?>
</table>
</div>
<script src="/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>
<script src="../SweetAlert2/dist/sweetalert2.all.min.js"></script>
<script>
function deleteItem(id) {
    Swal.fire({
        title: 'คุณต้องการลบข้อมูลนี้ใช่ไหม?',
        text: "หลังจากลบแล้ว จะไม่สามารถกู้คืนได้",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'ใช่, ยืนยันลบ',
        cancelButtonText: 'ยกเลิก'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = '/Bansuan/Del_mission.php?id=' + id;
        }
    });
}
</script>
            
<script>
function dates_change()  // คำสั่งเปลี่ยนเดือน ปี
{
    var ms = $("#mi_cl_month option:selected").val();
    var ys = $("#mi_cl_year option:selected").val();
    window.location = "/Bansuan/index.php?page=mission&month=" + ms + "&year=" + ys + "&rnd=" + Math.random();
}
</script>
