<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';
include '../Head_image.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

//get month from select or current month
$month = isset($_GET['month']) ? $_GET['month'] : date("m");
$year = isset($_GET['year']) ? $_GET['year'] : date("Y");

$m1 = '';
$m2 = '';
$m3 = '';
$m4 = '';
$m5 = '';
$m6 = '';
$m7 = '';
$m8 = '';
$m9 = '';
$m10 = '';
$m11 = '';
$m12 = '';
switch($month)
{
    case "01": $m1 = "selected"; break;
    case "02": $m2 = "selected"; break;
    case "03": $m3 = "selected"; break;
    case "04": $m4 = "selected"; break;
    case "05": $m5 = "selected"; break;
    case "06": $m6 = "selected"; break;
    case "07": $m7 = "selected"; break;
    case "08": $m8 = "selected"; break;
    case "09": $m9 = "selected"; break;
    case "10": $m10 = "selected"; break;
    case "11": $m11 = "selected"; break;
    case "12": $m12 = "selected"; break;
}
$current_month = $global_thai_month[0+$month];
$y2 = $year+543;

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<base target="_top">
<title>รายงานการขับเคลื่อนงานสืบสวน</title>
<link rel="stylesheet" href="/bootstrap/css/bootstrap-grid.css">
<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    
<style type="text/css">
body{
    font-size:12px; 
}
.textAlignVer{
    display:block;
    filter: flipv fliph;
    -webkit-transform: rotate(-90deg); 
    -moz-transform: rotate(-90deg); 
    transform: rotate(-90deg); 
    position:relative;
    width:20px;
    white-space:nowrap;
    font-size:16px;
    margin-bottom:10px;
}
</style>
    
</head>

<body>
<div class="container-fluid" align="left">
<div class=" h3 text-center  alert alert-success mb-4 mt-4 " role="alert" >กิจกรรม 6 เรื่องร้องเรียน</div>
<div>
    <a href="/WM/index.php?rnd=<?= rand(); ?>&page=report" class="btn btn-warning btn-lg mb-4" >ย้อนกลับ</a>
    <table width="80%" border="0" cellspacing="1" cellpadding="1">
      <tbody>
        <tr>
          <td width="15%">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<b style="font-size: 25px">เลือกช่วงข้อมูล</b></td>    
          <td width="17%"><label>เลือกปี </label>
            <select id="mi_cl_year" name="mi_cl_year" class="form-select col-8 d-lg-inline" style="background-color: khaki; font-size: 16px" onChange="dates_change()">
              <option value=""> </option>
              <?php
                $cur_y = date("Y") + 2;
                for($y=2020; $y<$cur_y; $y++) {
                    $sel = ($y == $year) ? "selected" : '';
                    echo "<option value='$y' $sel> ". ($y + 543) ." </option>";
                }
            ?>
          </select></td>
          <td width="2%"></td>
          <td width="18%"><label>เดือน</label>
            <select id="mi_cl_month" name="mi_cl_month" class="form-select col-3 d-lg-inline" style="background-color: khaki; font-size: 16px" onChange="dates_change()">
              <option value=""> </option>
              <option <?= $m1 ?> value="01">มกราคม</option>
              <option <?= $m2 ?> value="02">กุมภาพันธ์</option>
              <option <?= $m3 ?> value="03">มีนาคม</option>
              <option <?= $m4 ?> value="04">เมษายน</option>
              <option <?= $m5 ?> value="05">พฤษภาคม</option>
              <option <?= $m6 ?> value="06">มิถุนายน</option>
              <option <?= $m7 ?> value="07">กรกฎาคม</option>
              <option <?= $m8 ?> value="08">สิงหาคม</option>
              <option <?= $m9 ?> value="09">กันยายน</option>
              <option <?= $m10 ?> value="10">ตุลาคม</option>
              <option <?= $m11 ?> value="11">พฤศจิกายน</option>
              <option <?= $m12 ?> value="12">ธันวาคม</option>
          </select></td>
          <td width="38%">&nbsp;</td>
          <td width="1%">&nbsp;</td>
        </tr>
      </tbody>
    </table><br>
</div>
<?php
 $m1 = "$year-$month-01";    // m1 = เดือนปัจจุบันที่เลือก
    $dtt = date("t", strtotime($m1));
    $m2 = "$year-$month-$dtt"; // << end date of current month ได้ค่าวันสุดท้ายของเดือนปัจจุบันที่เลือกมา
    // NOW()
    $now = $m2;   // ตัวแปร now จะเป็นค่าของเดือนปัจจบัน
                     
?>
    
<h3 align="center">ข้อมูลเรื่องร้องเรียน ของ  <?= $name_station ?></h3>
<h3 align="center">ประจำเดือน&nbsp; <?= $current_month ?> &nbsp;<?= $y2 ?> &nbsp;</h3><br>
    
<div class="container-fluid" align="left">
<table width="90%" border="1" cellspacing="1" cellpadding="1" class="table table-bordered">
  <tbody>
     <tr class=xl8827171 height=30 style='height:22.8pt'>
      <td height=30 class=xl8927171 style='height:22.8pt; background-color: cyan; border:solid #000000'>ลำดับ</td>
      <td class=xl8927171 style='border-left:none; background-color: cyan; border:solid #000000'>วัน/เดือน/ปี ที่รับเรื่อง</td>
      <td class=xl8927171 style='border-left:none; background-color: cyan; border:solid #000000'>จากหน่วยงาน</td>
      <td class=xl8927171 style='border-left:none; background-color: cyan; border:solid #000000'>เรื่อง</td>
      <td class=xl8927171 style='border-left:none; background-color: cyan; border:solid #000000'>เลขที่หนังสือ</td>
      <td class=xl8927171 style='border-left:none; background-color: cyan; border:solid #000000'>ผู้ที่ได้รับมอบหมายให้ดำเนินการ</td>
      <td class=xl8927171 style='border-left:none; background-color: cyan; border:solid #000000'>ผลการดำเนินการ</td>
      <td class=xl8927171 style='border-left:none; background-color: cyan; border:solid #000000'>หมายเหตุ</td>
     </tr>
<?php
$sql = "SELECT
            T1.*,
            T2.sco_cl_data AS data,
            T3.cs_cl_status AS status
        FROM
            `wm_tb_Complaints` AS T1 " .
            "LEFT JOIN wm_tb_source_complaints AS T2 ON T1.cp_cl_source = T2.sco_cl_aid " .
            "LEFT JOIN wm_tb_Complaints_status AS T3 ON T1.cp_cl_status = T3.cs_cl_aid
        WHERE
            (station='$station') AND (`cp_cl_date_complaints` BETWEEN :m1 AND :m2) AND (`cp_cl_source` IN ('8','9','10','11','13','14'))";
    
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':m1', $m1);
    $stmt->bindParam(':m2', $m2);
try{
    $stmt->execute();
}catch (PDOException $e) {
    echo '$sql Failed: '. $e->getMessage();
}

$no = 1;            
while($row = $stmt->fetch(PDO::FETCH_ASSOC))	//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
{

    //ฟังก์ชั่น วันที่ ดึงจาก condb
 $strDate = DateThai( $row["cp_cl_date_complaints"] );  
    
?>
    
 <tr height=30 style='height:22.8pt'>
  <td height=30 class=xl8727171 style='height:22.8pt; border:solid #000000'>&nbsp;<?= $no ?></td>
  <td class=xl8727171 style='border-top:none; border:solid #000000'>&nbsp;<?= $strDate ?></td>
  <td class=xl8727171 style='border-top:none; border:solid #000000'>&nbsp;<?= $row["data"]?></td>
  <td class=xl8727171 style='border-top:none; border:solid #000000'>&nbsp;<?= $row["cp_cl_case"]?></td>
  <td class=xl8727171 style='border-top:none; border:solid #000000'>&nbsp;<?= $row["cp_cl_number"]?></td>
  <td class=xl8727171 style='border-top:none; border:solid #000000'>&nbsp;<?= $row["cp_cl_investigative_sergeant"]?></td>
  <td class=xl8727171 style='border-top:none; border:solid #000000'>&nbsp;<?= $row["cp_cl_action"]?></td>
  <td class=xl8727171 style='border-top:none; border:solid #000000'>&nbsp;<?= $row["status"]?></td>
 </tr>
  </tbody>
<?php
     $no++;
}
?>
</table>
</div>  
&nbsp;&nbsp;<button class="btn btn-primary btn-lg mb-4" onClick="do_print()"> พิมพ์รายงาน </button>
<br>
<script>
function dates_change()  // คำสั่งเปลี่ยนเดือน ปี
{
    var ms = $("#mi_cl_month option:selected").val();
    var ys = $("#mi_cl_year option:selected").val();
    window.location = "Show_Activity6.php?&month=" + ms + "&year=" + ys + "&rnd=" + Math.random();
}
  </script>
</body>

</html>
<!-- script สำหรับส่งไปพิมพ์รายงาน  -->
<script>
    
    function do_print()
    {
        var ms = $("#mi_cl_month option:selected").val();
        var ys = $("#mi_cl_year option:selected").val();
        var newW = window.open("/Activity/Show_Activity6_print.php?&month=" + ms + "&year=" + ys + "&rnd=" + Math.random(), "_print", "menubar=no,titlebar=no,toolbar=no,status=no");
        newW.print();
        //window.print();
    }
    
</script>