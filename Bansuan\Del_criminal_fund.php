<?php
include '../Condb.php'; //PDO
include '../users.inc.php';
include '../Alert.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);
$ip_address = $_SERVER['REMOTE_ADDR'];
$action = 'Del criminal fund'; // You can set the action to a suitable value
$data = str_replace("'", "", compact_array($_POST));
log_activity($acc, $action, $data, $ip_address); // ใช้ function ในไฟล์ Condb.php

$id = $_GET['id'];

try{
$sql="DELETE FROM wm_tb_criminalfund WHERE fun_cl_aid = :id ";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':id', $id);
    $stmt->execute();

    if ($stmt->rowCount() > 0) {
        $_SESSION['success'] = "Data has been deleted successfully";
        showSweetAlert("Delete successfully", "ลบข้อมูลเรียบร้อย");
        unset($_SESSION['success']); // Clear the session variable
        header("refresh:2; url=/Bansuan/index.php?rnd=' + Math.random() + '&page=criminalfund");
    } else {
        $_SESSION['error'] = "ลบข้อมูลไม่สำเร็จ";
        showSweetAlert("Failed to delete", "ลบข้อมูลไม่สำเร็จ");
        unset($_SESSION['success']); // Clear the session variable
        header("refresh:2; url=/Bansuan/index.php?rnd=' + Math.random() + '&page=criminalfund");
    }
}catch(Exception $e){
    echo 'Query Failed: '. $e->getMessage();
}

?>