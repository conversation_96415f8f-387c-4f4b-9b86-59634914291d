<!-- insert_update_data.php -->
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Add/Update Data</title>
    <link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
    <!-- สำหรับวันเกิด แบบใหม่  -->
    <link rel="stylesheet" href="../datepicker_th/jquery.datetimepicker.css" type="text/css">
    <script src="../datepicker_th/jquery-1.8.3.min.js"></script>  
    <script src="../datepicker_th/jquery.datetimepicker.full.js"></script>
</head>
<body>
    <h2>Add/Update Data</h2>
    <form action="save_data.php" method="POST" enctype="multipart/form-data">
        <!-- Input fields for data -->
        <label hidden ="hidden">ลำดับ</label>
		<input type="text" name="id" id="id" class="form-control"  hidden ="hidden" >
        
        <label>เลขบัตรประชาชน</label>
		<input type="text" name="idcard" id="idcard" class="form-control"  placeholder="เลขบัตรประชาชน" >
        
        <label>ชื่อ-นามสกุล</label>
		<input type="text" name="name" id="name" class="form-control"  placeholder="ชื่อ-นามสกุล" >
        
        <label>ข้อหา</label>
		<input type="text" name="charge" id="charge" class="form-control"  placeholder="ข้อหา" >
        
        <label>ชื่อผู้แจ้ง</label>
		<input type="text" name="pol_name" id="pol_name" class="form-control"  placeholder="ชื่อผู้แจ้ง" >
        
        <label>ตำแหน่ง</label>
		<input type="text" name="pol_position" id="pol_position" class="form-control"  placeholder="ตำแหน่ง" >
        
        <label>ชื่อฝ่ายปกครอง</label>
		<input type="text" name="gov_name" id="gov_name" class="form-control"  placeholder="ชื่อฝ่ายปกครอง" >
        
        <label>โทรศัพท์/ช่องทางที่แจ้ง</label>
		<input type="text" name="gov_contact" id="gov_contact" class="form-control"  placeholder="โทรศัพท์/ช่องทางที่แจ้ง" >
        
        <label>วันที่แจ้ง</label>
        <input type="text" name="gov_date" id="gov_date" value="" class="form-control" style="width:250px;" autocomplete="off" required>
        <!-- สำหรับวันที่ เดือนไทย ปี พ.ศ. และ เลือนเลือกปี พ.ศ.ได้  -->
        <script type="text/javascript"> 
            $(function(){
                $.datetimepicker.setLocale('th'); // ต้องกำหนดเสมอถ้าใช้ภาษาไทย และ เป็นปี พ.ศ.
                // กรณีใช้แบบ input
                $("#gov_date").datetimepicker({
                    timepicker:false,
                    format:'Y-m-d',  // กำหนดรูปแบบวันที่ ที่ใช้ เป็น 00-00-0000			
                    lang:'th',  // ต้องกำหนดเสมอถ้าใช้ภาษาไทย และ เป็นปี พ.ศ.
                    onSelectDate:function(dp,$input){
                        var yearT=new Date(dp).getFullYear()-0;  
                        var yearTH=yearT;  // ถ้าจะให้แสดงเป็น พ.ศ. ให้เป็น yearT+543
                        var fulldate=$input.val();
                        var fulldateTH=fulldate.replace(yearT,yearTH);
                        $input.val(fulldateTH);
                    },
                });       
                // กรณีใช้กับ input ต้องกำหนดส่วนนี้ด้วยเสมอ เพื่อปรับปีให้เป็น ค.ศ. ก่อนแสดงปฏิทิน
                $("#gov_date").on(function(e){
                    var dateValue=$(this).val();
                    if(dateValue!=""){
                            var arr_date=dateValue.split("-"); // ถ้าใช้ตัวแบ่งรูปแบบอื่น ให้เปลี่ยนเป็นตามรูปแบบนั้น
                            // ในที่นี้อยู่ในรูปแบบ 00-00-0000 เป็น d-m-Y  แบ่งด่วย - ดังนั้น ตัวแปรที่เป็นปี จะอยู่ใน array
                            //  ตัวที่สอง arr_date[2] โดยเริ่มนับจาก 0 
                            dateValue=dateValue.replace(arr_date[0],yearT);
                            $(this).val(dateValue);													
                    }		
                });
            });
            </script>
        
        <label>ชื่ออัยการ</label>
		<input type="text" name="attorney_name" id="attorney_name" class="form-control"  placeholder="ชื่ออัยการ" >
        
        <label>โทรศัพท์/ช่องทางที่แจ้ง</label>
		<input type="text" name="att_contact" id="att_contact" class="form-control"  placeholder="โทรศัพท์/ช่องทางที่แจ้ง" >
        
        <label>วันที่แจ้ง</label>
        <input type="text" name="att_date" id="att_date" value="" class="form-control" style="width:250px;" autocomplete="off" required>
        <!-- สำหรับวันที่ เดือนไทย ปี พ.ศ. และ เลือนเลือกปี พ.ศ.ได้  -->
        <script type="text/javascript"> 
            $(function(){
                $.datetimepicker.setLocale('th'); // ต้องกำหนดเสมอถ้าใช้ภาษาไทย และ เป็นปี พ.ศ.
                // กรณีใช้แบบ input
                $("#att_date").datetimepicker({
                    timepicker:false,
                    format:'Y-m-d',  // กำหนดรูปแบบวันที่ ที่ใช้ เป็น 00-00-0000			
                    lang:'th',  // ต้องกำหนดเสมอถ้าใช้ภาษาไทย และ เป็นปี พ.ศ.
                    onSelectDate:function(dp,$input){
                        var yearT=new Date(dp).getFullYear()-0;  
                        var yearTH=yearT;  // ถ้าจะให้แสดงเป็น พ.ศ. ให้เป็น yearT+543
                        var fulldate=$input.val();
                        var fulldateTH=fulldate.replace(yearT,yearTH);
                        $input.val(fulldateTH);
                    },
                });       
                // กรณีใช้กับ input ต้องกำหนดส่วนนี้ด้วยเสมอ เพื่อปรับปีให้เป็น ค.ศ. ก่อนแสดงปฏิทิน
                $("#att_date").on(function(e){
                    var dateValue=$(this).val();
                    if(dateValue!=""){
                            var arr_date=dateValue.split("-"); // ถ้าใช้ตัวแบ่งรูปแบบอื่น ให้เปลี่ยนเป็นตามรูปแบบนั้น
                            // ในที่นี้อยู่ในรูปแบบ 00-00-0000 เป็น d-m-Y  แบ่งด่วย - ดังนั้น ตัวแปรที่เป็นปี จะอยู่ใน array
                            //  ตัวที่สอง arr_date[2] โดยเริ่มนับจาก 0 
                            dateValue=dateValue.replace(arr_date[0],yearT);
                            $(this).val(dateValue);													
                    }		
                });
            });
            </script>
        
        <label>หมายเหตุ</label>
		<input type="text" name="remark" id="remark" class="form-control"  placeholder="หมายเหตุ" ><br>

        <!-- Add more input fields for other data columns -->

        <input type="submit" name="submit" value="Submit">
    </form>
</body>
</html>