<?php
include '../Condb.php';
include '../users.inc.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

//get month from select or current month
$month = isset($_GET['month']) ? $_GET['month'] : '10';
//$year = isset($_GET['year']) ? $_GET['year'] : (date("m") < 10 ? date("Y")-1 : date("Y"));
$station = isset($_GET['station']) ? $_GET['station'] : 6707;
//$station = 6707;   // สภ.บ้านสวน

// Get the year from the query string
$year = isset($_GET['year']) ? $_GET['year'] : date('Y');
// Calculate the start and end dates
$start_date = ($year - 1) . '-10-01';
$end_date = $year . '-09-30';

$limite_date = strtotime("$year-09-30");
$check_date = strtotime("$year-10-01");
$select_date = strtotime("$year-$month-01");


$m1 = '';
$m2 = '';
$m3 = '';
$m4 = '';
$m5 = '';
$m6 = '';
$m7 = '';
$m8 = '';
$m9 = '';
$m10 = '';
$m11 = '';
$m12 = '';
switch($month)
{
    case "01": $m1 = "selected"; break;
    case "02": $m2 = "selected"; break;
    case "03": $m3 = "selected"; break;
    case "04": $m4 = "selected"; break;
    case "05": $m5 = "selected"; break;
    case "06": $m6 = "selected"; break;
    case "07": $m7 = "selected"; break;
    case "08": $m8 = "selected"; break;
    case "09": $m9 = "selected"; break;
    case "10": $m10 = "selected"; break;
    case "11": $m11 = "selected"; break;
    case "12": $m12 = "selected"; break;
}
$current_month = $global_thai_month[0+$month];
$y2 = $year+543;

// Include the PHPExcel library
require '../PHPExcel/Classes/PHPExcel.php';

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<base target="_top">
<title>แบบ สส.12</title>
<link rel="stylesheet" href="/bootstrap/css/bootstrap-grid.css">
<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
</head>
    
<body>
<p><img src="../Image/Head2.jpg" width="100%" height="" alt=""/></p>
<div class=" h3 text-center  alert alert-success mb-4 mt-4 " role="alert" >แบบ สส.12 สถิติการจับกุมผู้ต้องหาคดียาเสพติด ประจำปีงบประมาณ พ.ศ.&nbsp;<?= $y2 ?> &nbsp; สภ.บ้านสวน</div>

<div class="container-fluid" align="left">
    <table width="90%" border="0" cellspacing="1" cellpadding="1">
      <tbody>
        <tr>
          <td width="14%"><div align="left">
            &nbsp;&nbsp;<a href="/WM/index.php?rnd=<?= rand(); ?>&page=report" class="btn btn-warning btn-lg mb-4" >ย้อนกลับ</a></td>
          <td width="16%"><label>เลือกปีงบประมาณ </label>
            <select id="changeyear" name="changeyear" class="form-select col-8 d-lg-inline" style="background-color: #00D2FE; font-size: 16px" onChange="dates_change()" >
              <option value="" >เลือก</option>
              <?php
                    $cur_y = date("Y") + 2;
                    for($y=2019; $y<$cur_y; $y++) {
                        $sel = ($y == $year) ? "selected" : '';
                        $start_month = ($y == date("Y")) ? "10" : "01"; // October for current year, January otherwise
                        echo "<option value='$y' $sel> ". ($y + 543) ." </option>";
                    }
                ?>
            </select></td>
          <td width="39%">&nbsp;</td>
        </tr>
      </tbody>
    </table>
</div>
    <br>
    
<div class="container" align="left" style="margin-left:auto">
<table width="99%" border="1" cellspacing="1" cellpadding="1" class="table-striped table-hover table-bordered">
  <tbody>

      
<?php
      
// Query to fetch data
$query = "SELECT
            CONCAT(YEAR(`pf_cl_dates`)+543) AS `year`,
            MONTH(`pf_cl_dates`) AS month,
            COUNT(`pf_cl_dates`) AS count,
            SUM(`pf_cl_drug`IN('4','5','6')) AS take,
            Sum(`pf_cl_drug`='3') AS have, 
            Sum(`pf_cl_drug`='2') AS sell, 
            Sum(`pf_cl_drug`='1') AS make
        FROM
            `wm_tb_performance`
        WHERE 
            `pf_cl_type_sub`='พ.ร.บ.ยาเสพติด' AND pf_cl_dates >= '$start_date' AND pf_cl_dates <= '$end_date'
        GROUP BY 
            MONTH(`pf_cl_dates`), YEAR(`pf_cl_dates`)
        ORDER BY
            pf_cl_dates ASC ";
      
$result = $conn->query($query);

// Check if there are any rows returned by the query
if (mysqli_num_rows($result) == 0) {
    echo "No data for the selected time period.";
} else {
    // Create an array of all the months between the start date and end date
    $months = array();
    $start = new DateTime($start_date);
    $end = new DateTime($end_date);
    $interval = DateInterval::createFromDateString('1 month');
    $period = new DatePeriod($start, $interval, $end);

    foreach ($period as $dt) {
        $months[] = array(
            'year' => $dt->format('Y') + 543,
            'month' => $dt->format('m'),
            'count' => '0',
            'f' => '0'
        );
    }

    // Left join the result set with the months array
    $data = array();
    while ($row = mysqli_fetch_assoc($result)) {
        $data[] = $row;
    }

    $result = array_replace($months, $data);
}

switch($row['month'])
{
    case "01": $row['month'] = "มกราคม"; break;
    case "02": $row['month'] = "กุมภาพันธ์"; break;
    case "03": $row['month'] = "มีนาคม"; break;
    case "04": $row['month'] = "เมษายน"; break;
    case "05": $row['month'] = "พฤษภาคม"; break;
    case "06": $row['month'] = "มิถุนายน"; break;
    case "07": $row['month'] = "กรกฎาคม"; break;
    case "08": $row['month'] = "สิงหาคม"; break;
    case "09": $row['month'] = "กันยายน"; break;
    case "10": $row['month'] = "ตุลาคม"; break;
    case "11": $row['month'] = "พฤศจิกายน"; break;
    case "12": $row['month'] = "ธันวาคม"; break;
}
    
if(!empty($row['take'])) {
    $take = $row['take'];
}else{
    $take = '';
}
      
if(!empty($row['have'])) {
    $have = $row['have'];
}else{
    $have = '';
}
     
if(!empty($row['sell'])) {
    $sell = $row['sell'];
}else{
    $sell = '';
}
      
if(!empty($row['make'])) {
    $make = $row['make'];
}else{
    $make = '';
}
      
if(!empty($row['count'])) {
    $count = $row['count'];
}else{
    $count = '';
}
      
      
// Create a new PHPExcel object
$objPHPExcel = new PHPExcel();
   
// Set the column headers
$objPHPExcel->getActiveSheet()->setCellValue('A1', 'take');
$objPHPExcel->getActiveSheet()->setCellValue('B1', 'have');
$objPHPExcel->getActiveSheet()->setCellValue('C1', 'sell');

$row = 2; // Start from the second row

// Loop through the data and populate the cells
if ($result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $objPHPExcel->getActiveSheet()->setCellValue('A' . $row, $row['take']);
        $objPHPExcel->getActiveSheet()->setCellValue('B' . $row, $row['have']);
        $objPHPExcel->getActiveSheet()->setCellValue('C' . $row, $row['sell']);
        $row++;
    }
}
      
// Set the appropriate headers for Excel
header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
header('Content-Disposition: attachment;filename="data.xlsx"');

// Save the Excel file
$objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');
$objWriter->save('php://output');

// Close the database connection
$conn->close();
      
?>
