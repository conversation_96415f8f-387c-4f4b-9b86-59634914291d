<?php
include '../Condb.php';
include '../users.inc.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// เมื่อมีการใช้ ตัวแปร Session จะต้องเปิดก่อน
//session_start();  // เปิดตัวแปร session
/*if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
*/

// สำหรับเลือก จังหวัด อำเภอ ตำบล
include("../config.inc.php");
include("../classes/class.database.inc.php");

// สำหรับเลือก จังหวัด อำเภอ ตำบล
$conn2 = get_connection(); // connect to mySQLDB

// สำหรับเลือกสถานีตำรวจทั่วประเทศ
$region = isset($_GET['region']) ? $_GET['region'] : 0; // กองบัญชาการ
$provincial = isset($_GET['provincial']) ? $_GET['provincial'] : 0; // บก.
$station = isset($_GET['station']) ? $_GET['station'] : 0; // สถานีตำรวจ

$pcid = $_GET['pcid'];

//ตาราง Personal
$sql = "SELECT * FROM wm_tb_personal WHERE ps_cl_idcard='$pcid' ";
$result=mysqli_query($conn,$sql);
$row = mysqli_fetch_array($result);

//ตาราง Politicain
$sql2 = "SELECT * FROM wm_politician WHERE po_cl_idcard='$pcid' ";
$resPot = mysqli_query($conn,$sql2);
$rowPot = mysqli_fetch_array($resPot);
$po_cl_aid = $rowPot['po_cl_aid'];
$po_cl_type = $rowPot['po_cl_type'];
$po_cl_type2 = $rowPot['po_cl_type2'];
$po_cl_party = $rowPot[ 'po_cl_party' ];
$po_cl_area = $rowPot[ 'po_cl_area' ];
$po_cl_idcard = $rowPot['po_cl_idcard'];

?>


<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>Edit Politician</title>
<script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/policeinnopolis/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
</style>
    <!--  เลือกเพศอัตโนมัต -->
<script language="javascript" src="/policeinnopolis/jQuery/jquery-3.5.1.min.js"></script>   
<script>
    function doPrefixChange()
    {
        var selected = $("#ps_cl_prefix option:selected").val();
        if(selected == "นาย") {
            $("#ps_cl_sex1").prop("checked", true);
        }
        else if(selected == "นาง"){
            $("#ps_cl_sex2").prop("checked", true);
        }
        else if(selected == "น.ส."){
            $("#ps_cl_sex2").prop("checked", true);
        }
        else if(selected == "ด.ช."){
            $("#ps_cl_sex1").prop("checked", true);
        }
        else if(selected == "ด.ญ."){
            $("#ps_cl_sex2").prop("checked", true);
        }
        else if(selected == "พ.ต.ท."){
            $("#ps_cl_sex1").prop("checked", true);
        }
        else if(selected == "พ.ท."){
            $("#ps_cl_sex1").prop("checked", true);
        }
        else if(selected == "ร.ต.อ."){
            $("#ps_cl_sex1").prop("checked", true);
        }
        else {
            $("#ps_cl_sex1").prop("checked", false);
            $("#ps_cl_sex2").prop("checked", false);
            $("#ps_cl_sex3").prop("checked", false);
        }
    }
</script>

<!-- สำหรับ วันเกิด แบบ พ.ศ. -->
<!--<link rel="stylesheet" href="http://code.jquery.com/ui/1.9.1/themes/base/jquery-ui.css" />
<script src="http://code.jquery.com/jquery-1.8.2.js"></script>
<script src="/policeinnopolis/js/jquery-ui.js"></script>-->
   
<!-- สำหรับวันเกิด แบบใหม่  -->
<link rel="stylesheet" href="../datepicker_th/jquery.datetimepicker.css" type="text/css">
<script src="../datepicker_th/jquery-1.8.3.min.js"></script>  
<script src="../datepicker_th/jquery.datetimepicker.full.js"></script>
<script type="text/javascript"> 
$(function(){
	
	$.datetimepicker.setLocale('th'); // ต้องกำหนดเสมอถ้าใช้ภาษาไทย และ เป็นปี พ.ศ.
	// กรณีใช้แบบ input
    $("#testdate5").datetimepicker({
        timepicker:false,
        format:'Y-m-d',  // กำหนดรูปแบบวันที่ ที่ใช้ เป็น 00-00-0000			
        lang:'th',  // ต้องกำหนดเสมอถ้าใช้ภาษาไทย และ เป็นปี พ.ศ.
		onSelectDate:function(dp,$input){
			var yearT=new Date(dp).getFullYear()-0;  
			var yearTH=yearT+543;
			var fulldate=$input.val();
			var fulldateTH=fulldate.replace(yearT,yearTH);
			$input.val(fulldateTH);
		},
    });       
	// กรณีใช้กับ input ต้องกำหนดส่วนนี้ด้วยเสมอ เพื่อปรับปีให้เป็น ค.ศ. ก่อนแสดงปฏิทิน
	$("#testdate5").on(function(e){
		var dateValue=$(this).val();
		if(dateValue!=""){
				var arr_date=dateValue.split("-"); // ถ้าใช้ตัวแบ่งรูปแบบอื่น ให้เปลี่ยนเป็นตามรูปแบบนั้น
				// ในที่นี้อยู่ในรูปแบบ 00-00-0000 เป็น d-m-Y  แบ่งด่วย - ดังนั้น ตัวแปรที่เป็นปี จะอยู่ใน array
				//  ตัวที่สอง arr_date[2] โดยเริ่มนับจาก 0 
				//if(e.type=="mouseenter"){
//					var yearT=arr_date[2]-543;
//				}		
//				if(e.type=="mouseleave"){
//					var yearT=parseInt(arr_date[2])+543;
//				}	
				dateValue=dateValue.replace(arr_date[2],yearT);
				$(this).val(dateValue);													
		}		
	});
    
    
});
</script>
</head>

<body>
	
	<div class="container">
	<div class="row">
	<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> แก้ไขข้อมูลบุคคลทางการเมือง </div>
	<form action="Save_Politician_Edit.php" method="POST" enctype="multipart/form-data">
    
        
        <input type="hidden" name="po_cl_aid" value="<?= $po_cl_aid ?>" readonly />
        <input type="hidden" name="ps_cl_aid" value="<?= $row['ps_cl_aid'] ?>" readonly />
        
        <label>ประเภทบุคคล 2 ประเภท<span style="color: #F90004">* จำเป็น</span></label>
		<select id="ps_cl_type" name="ps_cl_type" class="form-select form-select-sm" Required>
			<option value="" selected> </option>
			<option value="1">บุคคลทั่วไป</option>
		</select>
        
        <label>เลือกประเภทบุคคลทั่วไป ตามแบบ ศขส. 32 ประเภท<span style="color: #F90004">* จำเป็น</span></label>
                <select id="po_cl_type2" name="po_cl_type2" class="form-select form-select-sm" Required>
                    <option value="" selected>เลือก</option>  <!-- ดึงข้อมูลมาจากตารางใหม่ -->
                    <?php
                        $res_po2 = mysqli_query($conn, "SELECT * FROM wm_tb_gen_detail order by gd_cl_aid ASC");
                        while($row_po2 = mysqli_fetch_array($res_po2))
                        {
                            echo "<option value='{$row_po2['gd_cl_aid']}'>{$row_po2['gd_cl_name']}</option>";
                        }
                    ?>
                </select>
        
        <label>ตำแหน่ง หรือสถานภาพ<span style="color: #F90004">* จำเป็น</span></label>
                <select id="po_cl_type" name="po_cl_type" class="form-select form-select-sm" Required>
                    <option value="" selected>เลือก</option>  <!-- ดึงข้อมูลมาจากตารางใหม่ -->
                    <?php
                        $res_po = mysqli_query($conn, "SELECT * FROM wm_politician_type order by ty_cl_type ASC");
                        while($row_po = mysqli_fetch_array($res_po))
                        {
                            echo "<option value='{$row_po['ty_cl_type']}'>{$row_po['ty_cl_name']}</option>";
                        }
                    ?>
                </select>
        
        <span style="color: #1203F8">
        <label>เลขบัตรประชาชน <span style="color: #F90004">* จำเป็น</span> </label>
        </span>
        <input type = "text" name="po_cl_idcard" class="form-control" value= "<?= $po_cl_idcard ?>" autocomplete="off" placeholder="กรอกเลขบัตรประชาชน 13 หลัก"  Required >

        <label>คำนำหน้า</label>
		<select id="ps_cl_prefix" name="ps_cl_prefix" class="form-select form-select-sm" onChange="doPrefixChange()" >
			<option value="">เลือกคำนำหน้า</option>
			<option value="นาย" <? if($row['ps_cl_prefix'] == 'นาย') echo 'selected'; ?> > นาย</option>
			<option value="นาง" <? if($row['ps_cl_prefix'] == 'นาง') echo 'selected'; ?> >นาง</option>
			<option value="น.ส." <? if($row['ps_cl_prefix'] == 'น.ส.') echo 'selected'; ?> >น.ส.</option>
            <option value="พ.ต.ท." <? if($row['ps_cl_prefix'] == 'พ.ต.ท.') echo 'selected'; ?> >พ.ต.ท.</option>
            <option value="พ.ท." <? if($row['ps_cl_prefix'] == 'พ.ท.') echo 'selected'; ?> >พ.ท.</option>
            <option value="ร.ต.อ." <? if($row['ps_cl_prefix'] == 'ร.ต.อ.') echo 'selected'; ?> >ร.ต.อ.</option>
		</select>

		<label>เพศ</label> : 
		<label><input name="ps_cl_sex" id="ps_cl_sex1" type="radio" value="ชาย" <? if($row['ps_cl_sex'] == "ชาย") echo 'checked="checked" '; ?> />ชาย</label>
		<label><input name="ps_cl_sex" id="ps_cl_sex2" type="radio" value="หญิง" <? if($row['ps_cl_sex'] == "หญิง") echo 'checked="checked" '; ?> />หญิง</label>																										
		<label><input name="ps_cl_sex" id="ps_cl_sex3" type="radio" value="LGBTQ" <? if($row['ps_cl_sex'] == "LGBTQ") echo 'checked="checked" '; ?> /> 
		  LGBTQ 
		</label><br>

        <label>ชื่อ <span style="color: #F90004">* จำเป็น</span> </label>
        <input type = "text" name = "ps_cl_name" class="form-control" value= "<?=$row['ps_cl_name']?>"  placeholder="กรอกชื่อจริง"   Required  >
        <label>นามสกุล <span style="color: #F90004">* จำเป็น</span> </label>
        <input type = "text" name = "ps_cl_surname" class="form-control" value= "<?=$row['ps_cl_surname']?>" placeholder="กรอกนามสกุลจริง"  Required >
        <label>ชื่อเล่น</label>
        <input type = "text" name = "ps_cl_nickname" class="form-control" value= "<?=$row['ps_cl_nickname']?>" placeholder="กรอกชื่อเล่น" >
        
        <label>วันเดือนปีเกิด</label>
        <input type="text" name="ps_cl_birthday2" id="testdate5"  value= "<?=$row['ps_cl_birthday2']?>" class="form-control" style="width:250px;" autocomplete="off">
        
        <label>บิดา</label>
        <input type = "text" name = "ps_cl_father" class="form-control" value= "<?=$row['ps_cl_father']?>"  placeholder="ชื่อบิดา" >
        <label>เลขบัตรประชาชน บิดา</label>
        <input type = "text" name = "ps_cl_father_pid" class="form-control" value= "<?=$row['ps_cl_father_pid']?>" placeholder="เลขบัตรประชาชน บิดา" >
        <label>มารดา</label>
        <input type = "text" name = "ps_cl_mother" class="form-control" value= "<?=$row['ps_cl_mother']?>" placeholder="ชื่อมารดา" >
        <label>เลขบัตรประชาชน มารดา</label>
        <input type = "text" name = "ps_cl_mother_pid" class="form-control" value= "<?=$row['ps_cl_mother_pid']?>"  placeholder="เลขบัตรประชาชน มารดา" >
        
        <label>สถานะสมรส</label>
		<select id="ps_cl_marital_status" name="ps_cl_marital_status" class="form-select form-select-sm" >
			<option value="" selected>เลือก</option>
			<option value="โสด">โสด</option>
			<option value="สมรส">สมรส</option>
			<option value="หย่า">หย่า</option>
			<option value="หม้าย">หม้าย</option>
			<option value="อื่น ๆ">อื่น ๆ</option>
		</select>
        
        <label>สังกัดพรรค/กลุ่ม หรือหมู่บ้าน</label>
        <input type = "text" name = "po_cl_party" class="form-control" value= "<?= $po_cl_party ?>"  placeholder="ระบุพรรคการเมือง หรือกลุ่มที่สังกัด" >
                
        <label>เขตเลือกตั้ง หรือตำบล</label>
        <select id="po_cl_area" name="po_cl_area" placeholder="ระบุเขตพื้นที่" class="form-select form-select-sm" Required>
                    <option value="" selected> </option>  <!-- ดึงข้อมูลมาจากตารางใหม่ -->
                    <?php
                        $res_ar = mysqli_query($conn, "SELECT * FROM wm_politician_area order by code_area ASC");
                        while($row_ar = mysqli_fetch_array($res_ar))
                        {
                            echo "<option value='{$row_ar['code_area']}'>{$row_ar['area']}</option>";
                        }
                    ?>
                </select>
        
        <label>หมายเลขผู้สมัคร</label>
        <input type = "text" name = "po_cl_number" class="form-control" value= "<?= $rowPot['po_cl_number'] ?>" placeholder="ระบุหมายเลขผู้สมัคร (เฉพาะตัวเลข)" >
        
        <label>สถานีตำรวจ <span style="color: #F90004">* จำเป็น</span></label><br>
                <!-- เลือกสถานีตำรวจ -->
        <label> บช. </label><br>
        <select name="region" id="region" onChange="do_region_change()" class="form-control" Required >
                <option value="">&lt;&lt; เลือก บช. &gt;&gt;</option>
                <?php
                $region = $row['region']; // <<ดึงข้อมูลจาก ตารางมา row ซึ่งเป็นคนละตัวกับ row_p
                //------------------------------------
			  	$res_re = $conn2->query("SELECT * FROM `wm_tb_police_region` 
                                            ORDER BY `name_region` ASC");
			     $selected = '';
			     while($row_re = $conn2->fetch_row($res_re)) 
                     {
                      $code_region = $row_re['code_region'];
                      $name_region = $row_re['name_region'];
                      // set default provionce to 64 >> sukhothai
                          if($code_region == $region) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$code_region' $selected> $name_region </option>\n";
                     }
			  ?>
              </select>
        
        
        <label> บก. </label><br>        
        <select name="provincial" id="provincial" onChange="do_provincial_change()" value="<?php echo $provincial; ?>" class="form-control" >
        <option value="0">&lt;&lt; เลือก บก. &gt;&gt;</option>
                <?php
                if($region > 0)
                {
                    $provincial = $row['provincial']; // <<
                    //-------------------------------------
                    $res_prov = $conn2->query("SELECT * FROM `wm_tb_police_provincial` 
                                                WHERE `region_code`='$region' 
                                                ORDER BY `provincial_code` ASC");
                    $selected = '';
                  while($res_prov = $conn2->fetch_row($res_prov)) 
                  {
                      $provincial_code = $res_prov['provincial_code']; // 
                      $provincial = $res_prov['provincial'];
                      // set default provionce to 64 >> sukhothai
                          if($provincial_code == $provincial) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      //
                      echo "<option value='$provincial_code' $selected> $provincial </option>\n";
                  }
                }	
                ?>
              </select>
        
         <label> สน./สภ. </label>
        <br>
                
        <select name="station" id="station" class="form-control" >
                <option value="0">&lt;&lt; เลือก สน./สภ. &gt;&gt;</option>
                <?php
	           if($provincial > 0)
                {
                   $station = $row['station'];
                   //------------------------------------------
                    $res_st = $conn2->query("SELECT * FROM `wm_tb_police_station2` 
                                                WHERE provincial_code='$provincial_code'
                                                ORDER BY `station_code` ASC");
                    $selected = '';
                  while($row_st = $conn2->fetch_row($res_st)) 
                  {
                      $station_code = $row_st['station_code'];
                      $station_name = $row_st['station_name'];
                          if($station_code == $station) {
                              $selected = 'selected';
                          }
                          else {
                              $selected = '';
                          }
                      echo "<option value='$station_code' $selected> $station_name </option>\n";
                  }
                }	
            
                ?>                                                                     
              </select>
		<br>

        <div class="mb-3">
            <label for="formFileMultiple" class="form-label">รูปภาพ</label>
             <input class="form-control" type="file" id="ps_cl_image" name="ps_cl_image" value= "<?=$row['ps_cl_image']?>" multiple>
        </div>
        <p>
            <input type="submit" value="บันทึกข้อมูล" class="btn btn-success" >
            <td> <a href="Show_politician.php?pcid=<?= $row["ps_cl_idcard"] ?>" class="btn btn-warning" >ยกเลิก</a> </td>
        </p>
            <br>
            <br>
        </form>
        </div>
        </div>
        </div>
<script>
    function doPrefixChange()
    {
        var selected = $("#ps_cl_prefix option:selected").val();
        if(selected == "นาย") {
            $("#ps_cl_sex1").prop("checked", true);
        }
        else if(selected == "นาง"){
            $("#ps_cl_sex2").prop("checked", true);
        }
        else if(selected == "น.ส."){
            $("#ps_cl_sex2").prop("checked", true);
        }
        else {
            $("#ps_cl_sex1").prop("checked", false);
            $("#ps_cl_sex2").prop("checked", false);
            $("#ps_cl_sex3").prop("checked", false);
        }
    }
</script>
<script>
// ฟังก์ชั่น เปลี่ยนภาค (บช.) เป็น ภ.จว. (บก.)
function do_region_change(current_value)
{
    current_value = (typeof current_value == 'undefined') ? 0 : current_value;
	var sel_region = document.getElementById("region");		
	var code_region = sel_region.options[sel_region.selectedIndex].value;

	$.get("../ajax/get_provincial.php?region=" + code_region, 
		  function(data, status){
				//alert("Data: " + data + "\nStatus: " + status);
				// jQuery style						
				$('#provincial').find('option').remove().end();
				var data = $.trim(data);
				if(data == '') {
					alert("ไม่พบข้อมูล บก. !");
					return;
				}

					//.append('<option value="whatever">text</option>').val('whatever');
				var datas = data.split(';');
				for(var i=0; i<datas.length; i++) {
					if(datas[i] == '') continue;
					datas[i] = datas[i].split(':');
					var code_region = datas[i][0];
					var name_region = datas[i][1];
					//$('#provincial').append('<option value="'+ code_region+'">' + name_region + '</option>');
                    if(current_value == code_region) {
                        $('#provincial').append('<option value="'+ code_region+'" selected>' + name_region + '</option>'); 
                    }
                    else {
                        $('#provincial').append('<option value="'+ code_region+'">' + name_region + '</option>');
                    }
				}
                 // Trigger the change event on the amphur dropdown so the tambon dropdown will be populated     
                $('#provincial').trigger('change');
		});
}
    //เลือก สถานี จากจังหวัด
function do_provincial_change(current_value)
{
    current_value = (typeof current_value == 'undefined') ? 0 : current_value;
    
	var sel_provincial = document.getElementById("region");		
	var provincial_code = sel_provincial.options[sel_provincial.selectedIndex].value;
    
    var sel_provincial2 = document.getElementById("provincial");		
	var station_code = sel_provincial2.options[sel_provincial2.selectedIndex].value;

	$.get("../ajax/get_station.php?region=" + provincial_code + "&provincial=" + station_code, 
			  function(data, status){
					//alert("Data: " + data + "\nStatus: " + status);
					// jQuery style						
					$('#station').find('option').remove().end();
					var data = $.trim(data);
					if(data == '') {
						alert("ไม่พบข้อมูลสถานี !");
						return;
					}

						//.append('<option value="whatever">text</option>').val('whatever');
					var datas = data.split(';');
					for(var i=0; i<datas.length; i++) {
						if(datas[i] == '') continue;
						datas[i] = datas[i].split(':');
						var station_code = datas[i][0];
						var station_name = datas[i][1];
                        if(current_value == station_code) {
                            $('#station').append('<option value="'+ station_code+'" selected>' + station_name + '</option>'); 
                        }
                        else {
                            $('#station').append('<option value="'+ station_code+'">' + station_name + '</option>');   
                        }						
					}
			});
}
</script>
<!--<script src="/js/datetimepicker2.5.1/jquery.ui.datepicker-th.js" type="text/javascript"></script>-->
<script>
// ชื่อ >> value >> set selct auto (สคลิปตรวจสอบ Selected) จำเป็นต้องก็อปปี้ <script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js" type="text/javascript">
function auto_select(sid, value)
{
    $('#' + sid).val(value);
}

$(document).ready(function() {
<?php
    // po
    echo "auto_select('po_cl_type', '{$po_cl_type}');\n"; 
    echo "auto_select('po_cl_type2', '{$po_cl_type2}');\n";
    echo "auto_select('po_cl_area', '{$po_cl_area}');\n";
                    
    // personal
    echo "auto_select('ps_cl_type', '{$row['ps_cl_type']}');\n";  
    echo "auto_select('ps_cl_prefix', '{$row['ps_cl_prefix']}');\n";
    echo "auto_select('ps_cl_marital_status', '{$row['ps_cl_marital_status']}');\n";
                    
    //echo "auto_select('provincial', '{$row['provincial']}');\n";
    //echo "auto_select('station', '{$row['station']}');\n";
                    
?>    
    setTimeout("do_region_change('<?= $row['provincial'] ?>')", 500);
    setTimeout("do_provincial_change('<?= $row['station'] ?>')", 1000);
});
</script>
</body>
</html>