<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';
include '../Head_image.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

//get month from select or current month
$month = isset($_GET['month']) ? $_GET['month'] : date("m");
$year = isset($_GET['year']) ? $_GET['year'] : date("Y");
//$station = isset($_GET['station']) ? $_GET['station'] : 6707;
//$station = 6707;   // สภ.บ้านสวน

//--------------------
// last modify 2023-01-20
$limite_date = strtotime("$year-09-30"); // วันสุดท้ายของช่วง เป็น 30 ก.ย.ของปีนั้น ๆ
$current_date = time(); // now()   กำหนดวันปัจจุบัน
$check_date = strtotime("$year-10-01");   // ตัวแปร $check_date เป็นวันตั้งแต่ 1 ต.ค.ของปีนั้น ๆ เป็นต้นไป
$select_date = strtotime("$year-$month-01"); // เป็นตัวแปร สำหรับเลือก ของเดือนนั้น ๆ

//ถ้าเวลายังไม่สิ้นสุด 30 กย. ของปี
if($current_date < $check_date)   // ถ้าวันปัจจุบัน มีค่าน้อยกว่า วันที่ 1 ต.ค.
{
    $limite_date = strtotime(($year-1) . "-09-30");  // ตัวแปร $limite_date จะมีค่าเท่ากับ 30 ก.ย.ของปี ที่ลบค่าไป 1
    $check_date = strtotime(($year-1) . "-10-01");   // ตัวแปร $check_date จะมีค่าเท่ากับ 1 ต.ค. ของปี ที่ลบค่าไป 1
}

$m1 = '';
$m2 = '';
$m3 = '';
$m4 = '';
$m5 = '';
$m6 = '';
$m7 = '';
$m8 = '';
$m9 = '';
$m10 = '';
$m11 = '';
$m12 = '';
switch($month)
{
    case "01": $m1 = "selected"; break;
    case "02": $m2 = "selected"; break;
    case "03": $m3 = "selected"; break;
    case "04": $m4 = "selected"; break;
    case "05": $m5 = "selected"; break;
    case "06": $m6 = "selected"; break;
    case "07": $m7 = "selected"; break;
    case "08": $m8 = "selected"; break;
    case "09": $m9 = "selected"; break;
    case "10": $m10 = "selected"; break;
    case "11": $m11 = "selected"; break;
    case "12": $m12 = "selected"; break;
}
$current_month = $global_thai_month[0+$month];
$y2 = $year+543;

// แปลงเวลา เป็นปีไทย
function date_2_Thai( $date )
{
    global $global_thaimonth;
    $strYear = date("Y", $date) + 543;
    $strMonth = date("n",$date) - 1;
    $strDay = date("j", $date);
    $strMonthThai = $global_thaimonth[$strMonth];
    return "$strDay $strMonthThai $strYear";
}

//สร้างฟังก์ชั่น ย้อนหลัง arrest_summary_count มาใช้งาน เป็นยอดรวมการจับกุม ย้อนหลัง 1 เดือน
// dateM - datetime var
function arrest_summary_count($date)
{
    global $pdo, $station;

    $m1 = date("Y-m-1", $date);
    $m2 = date("Y-m-t", $date);

    $sql_s = "SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station` = :station AND `wr_cl_status` IN (2,4,5,6)";

    $stmt = $pdo->prepare($sql_s);
    $stmt->bindParam(':m1', $m1);
    $stmt->bindParam(':m2', $m2);
    $stmt->bindParam(':station', $station);
    $stmt->execute();

    $row_s = $stmt->fetch(PDO::FETCH_NUM);
    return $row_s[0];
}

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<base target="_top">
<title>รายงานการขับเคลื่อนงานสืบสวน</title>
<link rel="stylesheet" href="/bootstrap/css/bootstrap-grid.css">
<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<style type="text/css">
body{
    font-size:12px; 
}
.textAlignVer{
    display:block;
    filter: flipv fliph;
    -webkit-transform: rotate(-90deg); 
    -moz-transform: rotate(-90deg); 
    transform: rotate(-90deg); 
    position:relative;
    width:20px;
    white-space:nowrap;
    font-size:16px;
    margin-bottom:10px;
}
.box !im{
    border: black;
    border-top-color: black !important;
    border-bottom-color: black !important;
    border-left-color: black !important;
    border-right-color: black !important;

}
</style>
    
</head>

<body>
    
<div class="container-fluid" align="left">
<div class=" h3 text-center  alert alert-success mb-4 mt-4 " role="alert" >กิจกรรม 7 หมายจับค้างเก่า <?= $name_station ?></div>
<div>
    <table width="80%" border="0" cellspacing="1" cellpadding="1">
      <tbody>
        <tr>
          <td width="21%">&nbsp;<a href="/WM/index.php?rnd=<?= rand(); ?>&page=report" class="btn btn-warning btn-lg mb-4" >ย้อนกลับ</a><!--&nbsp;&nbsp;<a href="Add_mission.php" class="btn btn-success btn-lg mb-4" >เพิ่มข้อมูล</a>--></td>
          <td width="17%">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<b style="font-size: 25px">เลือกช่วงข้อมูล</b></td>    
          <td width="10%"><label>เลือกปี </label>
            <select id="mi_cl_year" name="mi_cl_year" class="form-select col-8 d-lg-inline" style="background-color: khaki; font-size: 16px" onChange="dates_change()">
              <option value="">เลือก</option>
              <?php
                $cur_y = date("Y") + 2;
                for($y=2020; $y<$cur_y; $y++) {
                    $sel = ($y == $year) ? "selected" : '';
                    echo "<option value='$y' $sel> ". ($y + 543) ." </option>";
                }
            ?>
          </select></td>
          <td width="3%"></td>
          <td width="16%"><label>เดือน</label>
            <select id="mi_cl_month" name="mi_cl_month" class="form-select col-3 d-lg-inline" style="background-color: khaki; font-size: 16px" onChange="dates_change()">
              <option value="">เลือก</option>
              <option <?= $m1 ?> value="01">มกราคม</option>
              <option <?= $m2 ?> value="02">กุมภาพันธ์</option>
              <option <?= $m3 ?> value="03">มีนาคม</option>
              <option <?= $m4 ?> value="04">เมษายน</option>
              <option <?= $m5 ?> value="05">พฤษภาคม</option>
              <option <?= $m6 ?> value="06">มิถุนายน</option>
              <option <?= $m7 ?> value="07">กรกฎาคม</option>
              <option <?= $m8 ?> value="08">สิงหาคม</option>
              <option <?= $m9 ?> value="09">กันยายน</option>
              <option <?= $m10 ?> value="10">ตุลาคม</option>
              <option <?= $m11 ?> value="11">พฤศจิกายน</option>
              <option <?= $m12 ?> value="12">ธันวาคม</option>
          </select></td>
          <td width="29%">&nbsp;</td>
          <td width="4%">&nbsp;</td>
        </tr>
      </tbody>
    </table><br>
</div>
<h3 align="center">ข้อมูลหมายจับค้างเก่า  <?= $name_station ?> </h3>
<h3 align="center">ประจำเดือน&nbsp; <?= $current_month ?> &nbsp;<?= $y2 ?> &nbsp;</h3><br>
<table width="90%" border="1" cellspacing="1" cellpadding="1" class="table table-bordered">
  <tbody>
         <tr  height=28 style='height:21.0pt'>
           <td rowspan="3" bordercolor="#0C0B0B"  style='border:solid #000000;
          height:21.0pt; background-color:aqua '>&nbsp;หน่วย</td>
           <td height="28" colspan="4" bordercolor="#000000"  style='border:solid #000000; background-color:#DDDDDD '>ยอดหมายจับ (ยกมา)</td>
           <td height=28 colspan=12 bordercolor="#0C0B0B"  style='border:solid #000000;
          height:21.0pt; background-color:aqua '><span style="height:21.0pt; background-color:aqua ">หมายจับทั่วไปของ  <?= $name_station ?>  ตั้งแต่ 11 ต.ค.45 - <?= date_2_Thai($limite_date) ?></span></td>
          <td colspan=14  style='border:solid #000000; background-color:#ADFDB6'>หมายจับทั่วไปของ
           <?= $name_station ?>  ตั้งแต่ <?= date_2_Thai($check_date) ?>  - ปัจจุบัน</td>
          <td colspan=3  style='border:solid #000000; background-color: yellow; text-align: center'>รวม (ในเดือนนั้น)</td>
         </tr>
         <tr  height=28 style='height:21.0pt'>
           <td rowspan=2  bordercolor="#000000"  style='border:solid #000000; background-color:#DDDDDD '><p>ยอดหมายจับ (01)</p></td>
           <td rowspan=2  bordercolor="#000000"  style='border:solid #000000; background-color:#DDDDDD '><p>มีคุณภาพ  (02)</p></td>
           <td rowspan=2  bordercolor="#000000"  style='border:solid #000000; background-color:#DDDDDD '>อายัด (03)</td>
          <td rowspan=2  bordercolor="#000000"  style='border:solid #000000; background-color:#51ADFC '><p>ไม่มีคุณภาพ (04)</p></td>
          <td rowspan=2  width=104 style='border:solid #000000; width:78pt; background-color:aqua ' bordercolor="#0C0B0B">หมายคงเหลือทั้งหมด (1)</td>
          <td colspan="2" align=left  style='border:solid #000000; background-color:aqua '>คุณภาพของหมาย</td>
          <td colspan=6  style='border:solid #000000; background-color:#FA757F '><p>การจำหน่ายหมายจับ
            (เพิ่มเติม)
             (<?= $current_month ?>&nbsp;<?= $y2 ?>)</td>
          <td colspan=2  style='border:solid #000000;
          border-left:none; background-color:aqua '>จำนวนหมายคงเหลือ</td>
          <td rowspan=2  width=93 style='border:solid #000000;
          border-top:none;width:70pt; background-color:aqua '>ร้อยละผลการจับกุมจากหมายจับทั้งหมด (12)</td>
          <td rowspan=2  width=87 style='border:solid #000000;
          border-top:none;width:65pt; background-color:#ADFDB6'>หมายจับคงเหลือทั้งหมดจากการประชุมที่ผ่านมา (13)</td>
          <td rowspan=2  width=81 style='border:solid #000000;
          border-top:none;width:61pt; background-color:#ADFDB6'>จำนวนหมายจับใหม่ (เพิ่มเติม) (14)</td>
          <td rowspan=2  width=65 style='border:solid #000000;
          border-top:none;width:49pt; background-color:#ADFDB6'>รวมหมายจับทั้งหมด (15)</td>
          <td colspan="2" align=left class=xl961464 style='border:solid #000000; background-color:#ADFDB6'>คุณภาพของหมาย</td>
          <td colspan=6  style='border:solid #000000; background-color:#FA757F'>การจำหน่ายหมายจับ
            (เพิ่มเติม)<br>
            (<?= $current_month ?>&nbsp;<?= $y2 ?>)</td>
          <td colspan=2  style='border:solid #000000;
          border-left:none; background-color:#ADFDB6'>จำนวนหมายคงเหลือ</td>
          <td rowspan=2  width=93 style='border:solid #000000;
          border-top:none;width:70pt; background-color:#ADFDB6'>ร้อยละผลการจับกุมจากหมายจับทั้งหมด (26)</td>
          <td rowspan=2  width=70 style='border:solid #000000;width:53pt; background-color: #51ADFC'>รวมหมายจับทั้งหมด
          1 ต.ค.45 - ปัจจุบัน (27)</td>
          <td rowspan=2  width=70 style='border:solid #000000;width:53pt; background-color: #FA757F'>รวมจำหน่ายหมายทั้งหมด (28)</td>
          <td rowspan=2  width=70 style='border:solid #000000;width:53pt; background-color: yellow'>ร้อยละผลการจับกุมจากหมายจับทั้งหมด</td>
         </tr>
         <tr  height=133>
          <td style='border:solid #000000; background-color:#7FFA75 ' align="center" valign="bottom"><span class="textAlignVer">มีคุณภาพ (2)</td>
             <td  style='border:solid #000000; background-color:aqua ' align="center" valign="bottom"><span class="textAlignVer">ไม่มีคุณภาพ (3)</td>
          <td  style='border:solid #000000; background-color:aqua '>จับกุม (4)</td>
          <td  style='border:solid #000000; background-color:aqua '>ขาดอายุความ (5)</td>
          <td  style='border:solid #000000; background-color:aqua '>ตาย (6)</td>
          <td  style='border:solid #000000; background-color:aqua '>อายัด (7)</td>
          <td  style='border:solid #000000; background-color:aqua '>อื่น ๆ (8)</td>
          <td  style='border:solid #000000; background-color:#FA757F '>รวม (9)</td>
          <td  style='border:solid #000000; background-color:#51ADFC ' align="center" valign="bottom"><span class="textAlignVer">มีคุณภาพ (10)</td>
          <td  style='border:solid #000000; background-color:aqua ' align="center" valign="bottom"><span class="textAlignVer">ไม่มีคุณภาพ (11)</td>
          <td  style='border:solid #000000; background-color:#7FFA75' align="center" valign="bottom"><span class="textAlignVer">มีคุณภาพ (16)</td>
          <td  style='border:solid #000000; background-color:#ADFDB6' align="center" valign="bottom"><span class="textAlignVer">ไม่มีคุณภาพ (17)</td>
          <td  style='border:solid #000000; background-color:#ADFDB6'>จับกุม (18)</td>
          <td  style='border:solid #000000; background-color:#ADFDB6'>ขาดอายุความ (19)</td>
          <td  style='border:solid #000000; background-color:#ADFDB6'>ตาย (20)</td>
          <td  style='border:solid #000000; background-color:#ADFDB6'>อายัด (21)</td>
          <td  style='border:solid #000000; background-color:#ADFDB6'>อื่น ๆ (22)</td>
          <td  style='border:solid #000000; background-color:#FA757F'>รวม (23)</td>
          <td  style='border:solid #000000; background-color:#51ADFC' align="center" valign="bottom"><span class="textAlignVer">มีคุณภาพ (24)</td>
          <td  style='border:solid #000000; background-color:#ADFDB6' align="center" valign="bottom"><span class="textAlignVer">ไม่มีคุณภาพ (25)</td>
         </tr>
<?php                                    
    
//$date1 = date("Y", $limite_date) . "-$month-01";        //"$year-$month-01";
$day1 = date("t", $limite_date);
$date2 = "$year-$month-$day1"; // <<<
                      
// ยอดหมายจับเก่า ที่ออกระหว่าง ปี 45 - 30 ก.ย.2565 ทั้งหมด
$date_prev = date("Y-m-d", $limite_date);
$date_next = date("Y-m-01", $check_date); // ตั้งแต่ 1 ตค เปนต้นไป
    $m1 = date("Y-m-01", $select_date);       // m1 = เดือนปัจจุบันที่เลือก
    $m2 = date("Y-m-t", $select_date);        // << end date of current month ได้ค่าวันสุดท้ายของเดือนปัจจุบันที่เลือกมา
                      
//ฟังก์ชั่น เลือกเดือน ย้อนหลัง 1 เดือน ได้ตัวแปร $sum2 ไปใช้งาน
$sum = arrest_summary_count( $select_date );
$prev_y = $year;
$prev_m = $month - 1;
if($prev_m < 0) {
    $prev_m = 12;
    $prev_y--;
}
$mp = strtotime("$prev_y-$prev_m-01");
$sum2 = arrest_summary_count( $mp );  // ยอดย้อนหลังไป 1 เดือน
                      
$sql = "SELECT " .
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=:station AND `wr_cl_status` IN (1,3)) AS All_01, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ใส่ช่อง (01)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=:station AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS All_Q_02, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ใส่ช่อง (02)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=:station AND `wr_cl_qty`=1 AND `wr_cl_status`=3) AS All_Hold_03, ". // ยอดอายัดหมายทั้งหมด ใส่ช่อง (03)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `station`=:station AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS All_NQ_04, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ใส่ช่อง (04)
    /////////////////////////////// หมายจับเก่า  ///////////////////////////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=:station AND `wr_cl_status` IN (1,3)) AS Old_All_1, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=:station AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS Old_Q_2, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ใส่ช่อง (2)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `station`=:station AND `wr_cl_qty`=2 AND `wr_cl_status` IN (1,3)) AS Old_NQ_3, ". // หมายจับเก่า ไม่มีคุณภาพ ใส่ช่อง (3)
    //////////////////////////////   ยอดจำหน่ายหมายเก่า   ///////////////////////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=:station AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS Old_C_4, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ใส่ช่อง (4)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=:station AND `wr_cl_status`=4 AND `wr_cl_qty`=1) AS Old_Exp_1, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายมีคุณภาพ สำหรับคำนวน  ใส่ช่อง (5)
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=:station AND `wr_cl_status`=4 AND `wr_cl_qty`=2) AS Old_Exp_2, ".  // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ทั้งหมายไม่มีคุณภาพ สำหรับคำนวน ใส่ช่อง (5)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=:station AND `wr_cl_status`=5 AND `wr_cl_qty`=1) AS Old_Dead_6, ".    // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ใส่ช่อง (6)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=:station AND `wr_cl_status`=3 AND `wr_cl_qty`=1) AS Old_Hold_7 , " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ใส่ช่อง (7)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=:station AND `wr_cl_status`=6 ) AS Old_other_8, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ใส่ช่อง (8)
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=:station AND `wr_cl_status`IN(2,4,5,6)) AS S_9, ". // รวม ยอดจำหน่ายหมายเก่า ทั้งที่มีคุณภาพ + ไม่มีคุณภาพ (ขาดอายุความ)
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=:station AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
	
    ///////////////////////////  หมายจับ ใหม่  ////////////////////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `station`=:station AND `wr_cl_status` IN (1,3)) AS New_BF_13 , ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `station`=:station AND (`wr_cl_status` IN (1,3,2))) AS New_M_14 , ". // หมายจับใหม่ ภายในเดือน ที่เลือก
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=:station AND `wr_cl_qty`=1 AND `wr_cl_status` IN (1,3)) AS New_Q_16 , ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `station`=:station AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS New_NQ_17 , ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
    ///////// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก  ///////////////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=:station AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS New_C_18 , ". // จับกุม เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=:station AND `wr_cl_status`=4) AS New_Exp_19 , ". // ขาดอายุความ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=:station AND `wr_cl_status`=5) AS New_Dead_20 , " . // ตาย เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=:station AND `wr_cl_status`=3) AS New_Hold_21 , " . // อายัด เฉพาะหมายจับใหม่ 
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=:station AND `wr_cl_status`=6) AS New_other_22 , " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=:station AND `wr_cl_status`IN(2,4,5,6)) AS S_23, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `station`=:station AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25 ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
    "";
                      
try {
    $stmt = $pdo->prepare($sql);
    // Bind parameter values
    $stmt->bindParam(':m1', $m1);
    $stmt->bindParam(':m2', $m2);
    $stmt->bindParam(':station', $station);
    $stmt->bindParam(':date_next', $date_next);
    $stmt->bindParam(':date_prev', $date_prev);
    
    // Execute the prepared statement
    $stmt->execute();
    
	
    // Fetch the results if needed
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Process the results as required
    foreach ($results as $row) {
                      
	$numerator = 10;
    $denominator = 0;

    if ($denominator != 0) {
        $result = $numerator / $denominator;
		} else {
		$results = null; // Or any other appropriate value or action.
    }
//echo $sql;
    // ยอดคงเหลือหมายจับเก่า
    
    //$S_10_2 = $row['Old_NQ_3']-$row['S_9_2'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)
$New_All_15 = $row['New_BF_13']+$row['New_M_14'];     //  
$S_27 = $row['Old_All_1']+$New_All_15;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
		
// ยอดหมายจับ ขาดอายุความ ใส่ช่อง (5)
$Old_Exp_5 = $row['Old_Exp_1']+$row['Old_Exp_2'];
		
// ยอดจำหน่ายหมาย ใส่ช่อง (9)
//$s_9_0 = $row['S_9']+$row['S_9_2'];
$Old_clear = $row['Old_C_4'] + $Old_Exp_5 + $row['Old_Dead_6']+ $row['Old_other_8'];		
		
// ยอดจำหน่ายหมายเก่า มีคุณภาพ ใส่ช่อง (10)
//$S_10 = $row['Old_Q_2']-$row['S_9'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)
$Old_Q_clear = $row['Old_C_4'] + $row['Old_Exp_1'] + $row['Old_Dead_6']+ $row['Old_other_8'];
$Old_Q_clear_10 = $row['Old_Q_2'] - $Old_Q_clear;
$Old_NQ_clear_11 = $row['Old_NQ_3'] - $row['Old_Exp_2'];
                      
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['New_BF_13'] >$sum2){
    $New_BF_13 = $row['New_BF_13']-$sum2;
}else{
    $New_BF_13 = $row['New_BF_13'];
}
                   
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['New_Q_16'] > $sum2){
    $New_Q_16 = $row['New_Q_16']-$sum2;
}else{
    $New_Q_16 = $row['New_Q_16'];
}
      
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
     $S_24 = $New_Q_16-$row['S_23'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24 > $sum2){
        $S_24_1 = $S_24-$sum2;
}else{
        $S_24_1 = $S_24;
    }                      

		
// จำหน่ายหมายใหม่ ใส่ช่อง (23)
$New_clear_23 = $row['New_C_18'] + $row['New_Exp_19'] + $row['New_Dead_20']+ $row['New_other_22'];
// หมายใหม่คงเหลือ มีคุณภาพ ใส่ช่อง (24)
$New_Q_bl_24 = $New_Q_16 - $New_clear_23;
$S_28 = $row['S_9']+$New_clear_23; // รวม จำหน่ายหมายจับ ทั้งหมด S_28

$OldPersen = 0;
$NewPersen = 0;
$AllPersen = 0;

if ($row['All_01'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen = ($row['S_9'] / $row['All_01']) * 100;
	$OldPersen = round($OldPersen,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen = ($row['S_23'] / $row['All_01']) * 100;
	$NewPersen = round($NewPersen,2);  // จุดทศนิยม 2 ตำแหน่ง
}

if ($S_27 != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen = ($S_28 / $S_27) * 100;
	$AllPersen = round($AllPersen,2);  // จุดทศนิยม 2 ตำแหน่ง  
}

?>
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua ' nowrap><span style="height:22.8pt; background-color:aqua "><?= $name_station ?></span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_01'] ?> <!-- ยอดหมายจับ ยกมา ใส่ช่อง 01 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['All_Q_02'] ?> <!-- ยอดหมายจับ มีคุณภาพ ยกมา ใส่ช่อง 02 -->
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['All_Hold_03'] ?>&nbsp;</td> <!-- ยอดอายัด ยกมา ใส่ช่อง 03 -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['All_NQ_04'] ?> <!-- ยอดหมายจับ ไม่มีคุณภาพ ยกมา ใส่ช่อง 04 -->
  </td>
	 
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_All_1'] ?>&nbsp;</td> <!-- หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ใส่ช่อง (1)  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['Old_Q_2'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (2) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['Old_NQ_3'] ?></td>  <!-- ยอดไม่มีคุณภาพ ก่อน 1 ต.ค.2565 ใส่ช่อง (3) -->
  <!-- จำหน่าย หมายเก่า    -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['Old_C_4'] > 0) ? $row['Old_C_4'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า ใส่ช่อง (4) -->    
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Exp_5 > 0) ? $Old_Exp_5 : "-") ?>&nbsp;</td> <!-- หมายเก่า ขาดอายุความ ใส่ช่อง (5) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_Dead_6'] > 0) ? $row['Old_Dead_6'] : "-") ?>&nbsp;</td> <!-- หมายเก่า ตาย ใส่ช่อง (6) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['Old_Hold_7'] > 0) ? $row['Old_Hold_7'] : "-") ?></td>  <!-- อายัดตัว หมายเก่า ใส่ช่อง (7)  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_other_8'] > 0) ? $row['Old_other_8'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ ใส่ช่อง (8)  -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_clear > 0) ? $Old_clear : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565 ใส่ช่อง (9)  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($Old_Q_clear_10 > 0) ? ($Old_Q_clear_10)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย  ใส่ช่อง (10) -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['Old_NQ_3'] > 0) ? ($row['Old_NQ_3'])  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ  ใส่ของ (11) -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า ใส่ช่อง (12) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_BF_13 > 0) ? ($New_BF_13)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  ใส่ช่อง (13) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['New_M_14'] > 0) ? $row['New_M_14']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม ใส่ช่อง (14)  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($New_All_15 > 0) ? ($New_All_15)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ ใส่ช่อง (15) -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($New_Q_16 > 0) ? ($New_Q_16)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ ใส่ช่อง (16) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_NQ_17'] > 0) ? $row['New_NQ_17'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ ใส่ช่อง (17) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_C_18'] > 0) ? $row['New_C_18'] : "-") ?></td> <!-- จับกุม หมายใหม่ ใส่ช่อง (18) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Exp_19'] > 0) ? $row['New_Exp_19'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ (19) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Dead_20'] > 0) ? $row['New_Dead_20'] : "-") ?></td> <!-- หมายใหม่ ตาย ใส่ช่อง (20) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_Hold_21'] > 0) ? $row['New_Hold_21'] : "-") ?></td> <!-- อายัด หมายใหม่ ใส่ช่อง (21) -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['New_other_22'] > 0) ? $row['New_other_22'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ ใส่ช่อง (22) -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($New_clear_23 > 0) ? $New_clear_23 : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($New_Q_bl_24 > 0) ? ($New_Q_bl_24)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25'] > 0) ? ($row['S_25'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27 ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28 ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'>
	  <div style="padding: 10px; text-align: center;
    <?php
    if ($AllPersen > 6.5) {
        echo 'background-color: green; color: white;';
    } elseif ($AllPersen < 3) {
        echo 'background-color: red; color: white;';
    } else {
        echo 'background-color: yellow; color: black;';
    }
    ?>">
    <?= $AllPersen ?>
</div>
	 </td> <!--  -->
 </tr>
<?php
    }
} catch (PDOException $e) {
    // Handle errors
    echo 'Query failed: ' . $e->getMessage();
    exit;
}
?>
  </tbody>
</table>

</div>    
&nbsp;&nbsp;<button class="btn btn-primary btn-lg mb-4" onClick="do_print()"> พิมพ์รายงาน </button>
<p style="font-size: 18px">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;เป้าหมาย ตร. ให้จับกุม หมายเก่า 3 % &nbsp;&nbsp;&nbsp;&nbsp; รวมหมายเก่า+หมายใหม่ 6.5 %</p>
    <hr>
<!--<p>&nbsp;&nbsp;<a href="#catch_old_w.php" class="btn btn-secondary btn-lg mb-4" >จับหมายเก่า</a>&nbsp;&nbsp;&nbsp;&nbsp;<a href="#catch_new_w.php" class="btn btn-secondary btn-lg mb-4" >จับหมายใหม่</a>&nbsp;&nbsp;</p>-->
<p>&nbsp;</p>
<script>
function dates_change()  // คำสั่งเปลี่ยนเดือน ปี
{
    var ms = $("#mi_cl_month option:selected").val();
    var ys = $("#mi_cl_year option:selected").val();
    window.location = "/Activity/Show_Activity7.php?&month=" + ms + "&year=" + ys + "&rnd=" + Math.random();
}
  </script>
</body>

</html>
<!-- script สำหรับส่งไปพิมพ์รายงาน  -->
<script>
    
    function do_print()
    {
        var ms = $("#mi_cl_month option:selected").val();
        var ys = $("#mi_cl_year option:selected").val();
        var newW = window.open("/Activity/Show_Activity7_print.php?&month=" + ms + "&year=" + ys + "&rnd=" + Math.random(), "_print", "menubar=no,titlebar=no,toolbar=no,status=no");
        newW.print();
        //window.print();
    }
    
</script>
