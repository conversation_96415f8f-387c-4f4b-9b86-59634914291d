<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';


// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

$id = $_GET['id'];

$sql = "SELECT * FROM wm_tb_Complaints WHERE cp_cl_aid = :id ";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':id', $id);
try{
    $stmt->execute();
}catch (PDOException $e) {
    echo $e->getMessage();
}

$row = $stmt->fetch(PDO::FETCH_ASSOC);

$pcid = $_GET['pcid'];

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title>แก้ไขข้อมูลการรับแจ้งเหตุ</title>
<script src="/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 18px;
    }
input::placeholder {
  color: red;
}
.form-control::placeholder {
  color: sandybrown;
}
</style>

<script src="/jquery-ui-1.12.1/jquery-ui.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/jquery-ui-1.12.1/jquery-ui.css">
<link rel="stylesheet" href="/js/datetimepicker2.5.1/jquery.datetimepicker.min.css">
<script src="/js/datetimepicker2.5.1/jquery.datetimepicker.full.js" type="text/javascript"></script>    
<script src="/js/utils.js?<?= rand() ?>" type="text/javascript"></script>  
</head>

<body>
	
	<div class="container">
	<div class="row">
	<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> การดำเนินการกรณีรับแจ้งเหตุ </div>
    <div>&nbsp;<a href="/Bansuan/show_detail_complaints.php?id=<?= $id ?>&pcid=<?= $pcid ?>" class="btn btn-primary mb-4" >กลับ</a></div>
	<form action="Save_complaints_Edit_for_detail.php" method="POST" enctype="multipart/form-data" class="body">
	<span style="color: #1203F8">
		<label hidden ="hidden">ลำดับ</label>
		<input type = "text" name = "cp_cl_aid" class="form-control" value= "<?= $row['cp_cl_aid']?>" hidden ="hidden" >
		<label>รหัสรับแจ้งเหตุ</label>
		<input type = "text" name = "cp_cl_case_id" class="form-control" value= "<?= $row['cp_cl_case_id']?>" placeholder="รหัสรับแจ้งเหตุ รันนิ่ง/ปี" readonly="readonly" >
		<label>เหตุ <span style="color: #F90004">* จำเป็น</span> </label>
		<input type = "text" name="cp_cl_case" class="form-control" value= "<?= $row['cp_cl_case']?>" placeholder="ระบุเหตุที่รับแจ้ง"  Required >
        
        <div style="display:  none;">
        <label>ที่มา</label>
		<select id="cp_cl_source" name="cp_cl_source" class="form-select form-select-sm"  placeholder="ระบุแหล่งที่มาการรับแจ้งเหตุ">
			<option value="" selected>เลือก</option>
			<option value="พนักงานสอบสวนแจ้งข้อมูล">พนักงานสอบสวนแจ้งข้อมูล</option>
			<option value="ผู้แจ้งเข้ามาพบเอง">ผู้แจ้งเข้ามาพบเอง</option>
			<option value="สืบสวนรับแจ้งทางโทรศัพท์">สืบสวนรับแจ้งทางโทรศัพท์</option>
			<option value="รับแจ้งผ่านสื่อโซเชียล">รับแจ้งผ่านสื่อโซเชียล</option>
			<option value="รับแจ้งจากศูนย์วิทยุ">รับแจ้งจากศูนย์วิทยุ</option>
			<option value="รับแจ้งจากศูนย์วิทยุ 191">รับแจ้งจากศูนย์วิทยุ 191</option>
			<option value="จากสมาชิกแจ้งข่าว">จากสมาชิกแจ้งข่าว</option>
			<option value="จากเรื่องร้องเรียน">จากเรื่องร้องเรียน</option>
            <option value="จาก ศอ.ปส.ภ.จว.">จาก ศอ.ปส.ภ.จว.</option>
            <option value="จาก ศอ.ปส.อำเภอ">จาก ศอ.ปส.อำเภอ</option>
            <option value="จาก ผู้บังคับบัญชา">จาก ผู้บังคับบัญชา</option>
            <option value="จากสถานีตำรวจอื่น">จากสถานีตำรวจอื่น </option>
			<option value="อื่น ๆ">อื่น ๆ</option>
		</select>

        
		<label>วัน ที่รับแจ้งเหตุ <span style="color: #F90004">* จำเป็น</span> </label>
		<input type = "text" name="cp_cl_date_complaints" class="form-control datepicker" value= "<?= $row['cp_cl_date_complaints']?>" placeholder="ระบุวันที่" autocomplete="off" required>   
            
		<label>เวลา รับแจ้งเหตุ</label>
		<input type = "text" name = "cp_cl_time_complaints" class="form-control" value= "<?= $row['cp_cl_time_complaints']?>" placeholder="ระบุเวลา ที่รับแจ้งเหตุ"   >
        
		<label>วันที่เกิดเหตุ</label>
		<input type = "text" name = "cp_cl_date_incident" class="form-control" value= "<?= $row['cp_cl_date_incident']?>" placeholder="ระบุวันที่เกิดเหตุ"   >  
        
		<label>ตั้งแต่เวลา</label>
		<input type = "text" name = "cp_cl_time_start_incident" class="form-control" value= "<?= $row['cp_cl_time_start_incident']?>" placeholder="ระบุเหตุเกิดตั้งแต่เวลาใด"   >
		<label>ถึงเวลา</label>
		<input type = "text" name = "cp_cl_time_end_incident" class="form-control" value= "<?= $row['cp_cl_time_end_incident']?>" placeholder="ระบุเวลาสิ้นสุดเหตุ"   >
		
		<br>
		
		<label>สถานที่เกิดเหตุ</label>
		<input type = "text" name = "cp_cl_place" class="form-control" value= "<?= $row['cp_cl_place']?>" placeholder="ระบุสถานที่เกิดเหตุ"   >
		
		<hr>
		
		<label>จำนวนผู้เสียหาย</label>
		<select id="cp_cl_sufferer_quantity" name="cp_cl_sufferer_quantity" class="form-select form-select-sm"  placeholder="ระบุจำนวนผู้เสียหาย">
			<option value="" selected>เลือก</option>
			<option value="1">1</option>
			<option value="2">2</option>
			<option value="3">3</option>
			<option value="4">4</option>
			<option value="5">5</option>
			<option value="6">6</option>
			<option value="7">7</option>
			<option value="8">8</option>
			<option value="9">9</option>
			<option value="10">10</option>
		</select>
		
		<label>ชื่อผู้เสียหาย 1</label>
		<input type = "text" name = "cp_cl_sufferer_name1" class="form-control" value= "<?= $row['cp_cl_sufferer_name1']?>" placeholder="ระบุชื่อผู้เสียหาย 1"   >
		<label>อายุผู้เสียหาย 1</label>
		<input type = "text" name = "cp_cl_age1" class="form-control" value= "<?= $row['cp_cl_age1']?>" placeholder="ระบุอายุผู้เสียหาย 1"   >
		<label>ที่อยู่ผู้เสียหาย 1</label>
		<input type = "text" name = "cp_cl_address1" class="form-control" value= "<?= $row['cp_cl_address1']?>" placeholder="ระบุที่อยู่ผู้เสียหาย 1"   >
		<label>โทรศัพท์ผู้เสียหาย 1</label>
		<input type = "text" name = "cp_cl_phone1" class="form-control" value= "<?= $row['cp_cl_phone1']?>" placeholder="ระบุโทรศัพท์ผู้เสียหาย 1"   >
		<label>ชื่อผู้เสียหาย 2 (ถ้ามี) </label>
		<input type = "text" name = "cp_cl_sufferer_name2" class="form-control" value= "<?= $row['cp_cl_sufferer_name2']?>" placeholder="ระบุชื่อผู้เสียหาย 2 (ถ้ามี)"   >
		<label>อายุผู้เสียหาย 2</label>
		<input type = "text" name = "cp_cl_age2" class="form-control" value= "<?= $row['cp_cl_age2']?>" placeholder="ระบุอายุผู้เสียหาย 2"   >
		<label>ที่อยู่ผู้เสียหาย 2</label>
		<input type = "text" name = "cp_cl_address2" class="form-control" value= "<?= $row['cp_cl_address2']?>" placeholder="ระบุที่อยู่ผู้เสียหาย 2"   >
		<label>โทรศัพท์ผู้เสียหาย 2</label>
		<input type = "text" name = "cp_cl_phone2" class="form-control" value= "<?= $row['cp_cl_phone2']?>" placeholder="ระบุโทรศัพท์ผู้เสียหาย 2"   >
		<label>ชื่อผู้เสียหาย 3 (ถ้ามี)</label>
		<input type = "text" name = "cp_cl_sufferer_name3" class="form-control" value= "<?= $row['cp_cl_sufferer_name3']?>" placeholder="ระบุชื่อผู้เสียหาย 3 (ถ้ามี)"   >
		<label>อายุผู้เสียหาย 3</label>
		<input type = "text" name = "cp_cl_age3" class="form-control" value= "<?= $row['cp_cl_age3']?>" placeholder="ระบุอายุผู้เสียหาย 3"   >
		<label>ที่อยู่ผู้เสียหาย 3</label>
		<input type = "text" name = "cp_cl_address3" class="form-control" value= "<?= $row['cp_cl_address3']?>" placeholder="ระบุที่อยู่ผู้เสียหาย 3"   >
		<label>โทรศัพท์ผู้เสียหาย 3</label>
		<input type = "text" name = "cp_cl_phone3" class="form-control" value= "<?= $row['cp_cl_phone3']?>" placeholder="ระบุโทรศัพท์ผู้เสียหาย 3"   >
		
		<br>
		<hr color="#F8060A">
		<br>
		
		<label>ผู้ก่อเหตุ/ผู้ต้องสงสัย 1</label>
		<input type = "text" name = "cp_cl_suspect1" class="form-control" value= "<?= $row['cp_cl_suspect1']?>" placeholder="ระบุผู้ต้องสงสัย 1"   >
		<label>ผู้ก่อเหตุ/ผู้ต้องสงสัย 2</label>
		<input type = "text" name = "cp_cl_suspect2" class="form-control" value= "<?= $row['cp_cl_suspect2']?>" placeholder="ระบุผู้ต้องสงสัย 2"   >
		<label>ผู้ก่อเหตุ/ผู้ต้องสงสัย 3</label>
		<input type = "text" name = "cp_cl_suspect3" class="form-control" value= "<?= $row['cp_cl_suspect3']?>" placeholder="ระบุผู้ต้องสงสัย 3"   >
		<label>ตำหนิรูปพรรณ</label>
		<input type = "text" name = "cp_cl_description" class="form-control" value= "<?= $row['cp_cl_description']?>" placeholder="ระบุตำหนิรูปพรรณผู้ต้องสงสัย"   >
		<label>ยานพาหนะที่ใช้ก่อเหตุ</label>
		<input type = "text" name = "cp_cl_vehicle" class="form-control" value= "<?= $row['cp_cl_vehicle']?>" placeholder="ระบุยานพาหนะที่ใช้ก่อเหตุ"   >
		<label>ทรัพย์สินที่ถูกประทุษร้าย</label>
		<input type = "text" name = "cp_cl_property" class="form-control" value= "<?= $row['cp_cl_property']?>" placeholder="ระบุทรัพย์สินที่ถูกประทุษร้าย"   >
		
		<br>
		<hr color="#F8060A">
		<br>
		
		<label>พฤติการณ์</label>
		<input type = "text" name = "cp_cl_case_behavior" class="form-control" value= "<?= $row['cp_cl_case_behavior']?>" placeholder="ระบุพฤติการณ์"   >
		<label>แผนที่สถานที่เกิดเหตุ (ระบุพิกัด)</label>
		<input type = "text" name = "cp_cl_map" class="form-control" value= "<?= $row['cp_cl_map']?>" placeholder="แผนที่สถานที่เกิดเหตุ"   >
		
		<br>
		<hr color="#F8060A">
		<br>
		
		<label>ร้อยเวรสืบสวน</label>
		<?php
		if ($station == 6707) {
			echo '<select id="cp_cl_investigative_sergeant" name="cp_cl_investigative_sergeant"  class="form-select form-select-sm"  placeholder="ระบุร้อยเวรสืบสวน">';
			echo '<option value="" selected> </option>
				<option value="ร.ต.อ.พิทยา อ่วมเหล็ง">ร.ต.อ.พิทยา อ่วมเหล็ง</option>
				<option value="ร.ต.อ.วุฒิไกร สายทอง">ร.ต.อ.วุฒิไกร สายทอง</option>
				<option value="ร.ต.อ.ณัฐพงษ์ ภู่ทอง">ร.ต.อ.ณัฐพงษ์ ภู่ทอง</option>
			';
			echo '</select>';
		} else {
			// User's station is not 6707, display a text input
				$originalValue = htmlspecialchars($row['cp_cl_investigative_sergeant'], ENT_QUOTES, 'UTF-8');
				echo '<input type="text" id="cp_cl_investigative_sergeant" name="cp_cl_investigative_sergeant" value="' . $originalValue . '" class="form-control" placeholder="ระบุร้อยเวรสืบสวน">';
			}
		?>
            
		<label>เจ้าหน้าที่สืบสวน/บันทึก</label>
		<?php
			if ($station == 6707) {
				// User's station is 6707, display the dropdown select
				echo '<select id="cp_cl_investigative_officer" name="cp_cl_investigative_officer" class="form-select form-select-sm" placeholder="ระบุชื่อผู้บันทึก" required>';
				echo '<option value="" selected> </option>';
				$bsdp = $pdo->prepare("SELECT * FROM `police_name_bsdetective` ORDER BY `police_name_bsdetective`.`aid_bsdp` DESC");
				$bsdp->execute();
				while ($row_bsdp = $bsdp->fetch(PDO::FETCH_ASSOC)) {
					$aid_bsdp = htmlspecialchars($row_bsdp['aid_bsdp'], ENT_QUOTES, 'UTF-8');
					$bsdp_name = htmlspecialchars($row_bsdp['bsdp_name'], ENT_QUOTES, 'UTF-8');

					echo "<option value='$bsdp_name'>$bsdp_name</option>";
				}
				echo '</select>';
			} else {
				// User's station is not 6707, display a text input
				$originalValue = htmlspecialchars($row['cp_cl_investigative_officer'], ENT_QUOTES, 'UTF-8');
				echo '<input type="text" id="cp_cl_investigative_officer" name="cp_cl_investigative_officer" value="' . $originalValue . '" class="form-control" placeholder="ระบุ ยศชื่อ-สกุล ผู้บันทึก" required>';
			}
		?>
		</select>

        <hr>

        <div class="input-group">
          <div class="input-group" >
            <span class="input-group-text">การดำเนินการ <span style="color: #F90004"> &nbsp; * หลังดำเนินการแล้ว ให้กรอกข้อมูลการดำเนินการช่องนี้</span></span>
          </div>
            <textarea class="form-control" id="cp_cl_action" name="cp_cl_action"  rows="5" placeholder="กรอกรายละเอียดการดำเนินการ" ><?php echo $row['cp_cl_action'] ?></textarea>
        </div>
        
        
        <div style="display:  none;">
		<br>
        <div class="mb-3">
			<label for="formFileMultiple" class="form-label">ไฟล์ สส.1</label>
			<input class="form-control" type="file" id="cp_cl_complaints_file" name="cp_cl_complaints_file" multiple>
		</div>
		</div>
		<p>
		<br>
			<input type="submit" value="Update" class="btn btn-success" >
			<td> <a href="/Bansuan/show_detail_complaints.php?id=<?= $id ?>&pcid=<?= $pcid ?>" class="btn btn-warning" >ยกเลิก</a> </td>
		</p>
        
		<br>
		<br>
	</form>
	</div>
	</div>
	</div>
        <script src="/js/datetimepicker2.5.1/jquery.ui.datepicker-th.js" type="text/javascript"></script>

<script>
// ชื่อ >> value >> set selct auto (สคลิปตรวจสอบ Selected) จำเป็นต้องก็อปปี้ <script src="/jQuery/jquery-3.5.1.min.js" type="text/javascript">
function auto_select(sid, value)
{
    $('#' + sid).val(value);
}

function auto_thaidate(sid, value)
{
	if(value != '') {
		var dates = value.split('-');
		dates = value.split('-');
		if(dates.length > 2) {
			var ys = dates[0];
			var ms = dates[1];
			var ds = dates[2];
			if(ys.substr(0,2) == '20') {
				ys = parseInt(ys) + 543;
			}
			value = $.digit(ds,2) + '/' + $.digit(ms,2) + '/' +ys;
			$('#' + sid).val(value);
		}
	}
}

$(document).ready(function() {
<?php
    echo "auto_select('cp_cl_sufferer_quantity', '{$row['cp_cl_sufferer_quantity']}');\n";
    echo "auto_select('cp_cl_investigative_sergeant', '{$row['cp_cl_investigative_sergeant']}');\n";
    echo "auto_select('cp_cl_investigative_officer', '{$row['cp_cl_investigative_officer']}');\n"; 
    echo "auto_select('cp_cl_source', '{$row['cp_cl_source']}');\n";
    // convert db date to thai dates
	echo "auto_thaidate('cp_cl_date_complaints', '{$row['cp_cl_date_complaints']}');\n";
    echo "auto_thaidate('cp_cl_date_incident', '{$row['cp_cl_date_incident']}');\n";
?>
    });
</script>
</body>
</html>