<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';
//include '../Head_image.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

//get month from select or current month
$month = isset($_GET['month']) ? $_GET['month'] : date("m");
$year = isset($_GET['year']) ? $_GET['year'] : date("Y");
//$station = isset($_GET['station']) ? $_GET['station'] : 6707;
//$station = 6707;   // สภ.บ้านสวน

//--------------------
// last modify 2023-01-20
$limite_date = strtotime("$year-09-30"); // วันสุดท้ายของช่วง เป็น 30 ก.ย.ของปีนั้น ๆ
$current_date = time(); // now()   กำหนดวันปัจจุบัน
$check_date = strtotime("$year-10-01");   // ตัวแปร $check_date เป็นวันตั้งแต่ 1 ต.ค.ของปีนั้น ๆ เป็นต้นไป
$select_date = strtotime("$year-$month-01"); // เป็นตัวแปร สำหรับเลือก ของเดือนนั้น ๆ

//ถ้าเวลายังไม่สิ้นสุด 30 กย. ของปี
if($current_date < $check_date)   // ถ้าวันปัจจุบัน มีค่าน้อยกว่า วันที่ 1 ต.ค.
{
    $limite_date = strtotime(($year-1) . "-09-30");  // ตัวแปร $limite_date จะมีค่าเท่ากับ 30 ก.ย.ของปี ที่ลบค่าไป 1
    $check_date = strtotime(($year-1) . "-10-01");   // ตัวแปร $check_date จะมีค่าเท่ากับ 1 ต.ค. ของปี ที่ลบค่าไป 1
}

$m1 = '';
$m2 = '';
$m3 = '';
$m4 = '';
$m5 = '';
$m6 = '';
$m7 = '';
$m8 = '';
$m9 = '';
$m10 = '';
$m11 = '';
$m12 = '';
switch($month)
{
    case "01": $m1 = "selected"; break;
    case "02": $m2 = "selected"; break;
    case "03": $m3 = "selected"; break;
    case "04": $m4 = "selected"; break;
    case "05": $m5 = "selected"; break;
    case "06": $m6 = "selected"; break;
    case "07": $m7 = "selected"; break;
    case "08": $m8 = "selected"; break;
    case "09": $m9 = "selected"; break;
    case "10": $m10 = "selected"; break;
    case "11": $m11 = "selected"; break;
    case "12": $m12 = "selected"; break;
}
$current_month = $global_thai_month[0+$month];
$y2 = $year+543;

// แปลงเวลา เป็นปีไทย
function date_2_Thai( $date )
{
    global $global_thaimonth;
    $strYear = date("Y", $date) + 543;
    $strMonth = date("n",$date) - 1;
    $strDay = date("j", $date);
    $strMonthThai = $global_thaimonth[$strMonth];
    return "$strDay $strMonthThai $strYear";
}

//สร้างฟังก์ชั่น ย้อนหลัง arrest_summary_count มาใช้งาน เป็นยอดรวมการจับกุม ย้อนหลัง 1 เดือน
// dateM - datetime var
function arrest_summary_count($date)
{
    global $pdo, $provincial;

    $m1 = date("Y-m-1", $date);
    $m2 = date("Y-m-t", $date);

    $sql_s = "SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial` = :provincial AND `wr_cl_status` IN (2,4,5,6)";

    $stmt = $pdo->prepare($sql_s);
    $stmt->bindParam(':m1', $m1);
    $stmt->bindParam(':m2', $m2);
    $stmt->bindParam(':provincial', $provincial);
    $stmt->execute();

    $row_s = $stmt->fetch(PDO::FETCH_NUM);
    return $row_s[0];
}
// สำหรับ พิมพ์หน้า ข้อมูฃ ประกาศ บรรทัด ที่ต้องการพิมพ์
//-----------------
$MAX_ROW = 18;
//-----------------

// สร้างฟังก์ชั่น เพื่อดึง หัวตาราง ไปแสดง ในหน้าต่อไป
function print_header()
{
    echo '
<table width="90%" border="1" cellspacing="1" cellpadding="1" class="table table-bordered">
  <tbody>
         <tr  height=28 >
           <td rowspan="3">&nbsp;หน่วย</td>
           <td height="28" colspan="4"</td>
           <td height=28 colspan=12><span style="height:21.0pt; background-color:aqua ">หมายจับทั่วไปของ <?= $name_provincial ?> ตั้งแต่ 11 ต.ค.45 - <?= date_2_Thai($limite_date) ?></span></td>
          <td colspan=14>หมายจับทั่วไปของ
            <?= $name_provincial ?> ตั้งแต่ <?= date_2_Thai($check_date) ?>  - ปัจจุบัน</td>
          <td colspan=3>รวม (ในเดือนนั้น)</td>
         </tr>
         <tr  height=28>
           <td rowspan=2><p>ยอดหมายจับ (01)</p></td>
           <td rowspan=2><p>มีคุณภาพ  (02)</p></td>
           <td rowspan=2>อายัด (03)</td>
          <td rowspan=2><p>ไม่มีคุณภาพ (04)</p></td>
          <td rowspan=2>หมายคงเหลือทั้งหมด (1)</td>
          <td colspan="2" align=left>คุณภาพของหมาย</td>
          <td colspan=6><p>การจำหน่ายหมายจับ
            (เพิ่มเติม)
             (<?= $current_month ?>&nbsp;<?= $y2 ?>)</td>
          <td colspan=2>จำนวนหมายคงเหลือ</td>
          <td rowspan=2>ร้อยละผลการจับกุมจากหมายจับทั้งหมด (12)</td>
          <td rowspan=2>หมายจับคงเหลือทั้งหมดจากการประชุมที่ผ่านมา (13)</td>
          <td rowspan=2>จำนวนหมายจับใหม่ (เพิ่มเติม) (14)</td>
          <td rowspan=2>รวมหมายจับทั้งหมด (15)</td>
          <td colspan="2" align=left >คุณภาพของหมาย</td>
          <td colspan=6>การจำหน่ายหมายจับ
            (เพิ่มเติม)<br>
            ()</td>
          <td colspan=2>จำนวนหมายคงเหลือ</td>
          <td rowspan=2>ร้อยละผลการจับกุมจากหมายจับทั้งหมด (26)</td>
          <td rowspan=2>รวมหมายจับทั้งหมด
          1 ต.ค.45 - ปัจจุบัน (27)</td>
          <td rowspan=2>รวมจำหน่ายหมายทั้งหมด (28)</td>
          <td rowspan=2>ร้อยละผลการจับกุมจากหมายจับทั้งหมด</td>
         </tr>
         <tr  height=133>
          <td ><span class="textAlignVer">มีคุณภาพ (2)</td>
             <td><span class="textAlignVer">ไม่มีคุณภาพ (3)</td>
          <td>จับกุม (4)</td>
          <td>ขาดอายุความ (5)</td>
          <td>ตาย (6)</td>
          <td>อายัด (7)</td>
          <td>อื่น ๆ (8)</td>
          <td>รวม (9)</td>
          <td><span class="textAlignVer">มีคุณภาพ (10)</td>
          <td align="center"><span class="textAlignVer">ไม่มีคุณภาพ (11)</td>
          <td><span class="textAlignVer">มีคุณภาพ (16)</td>
          <td><span class="textAlignVer">ไม่มีคุณภาพ (17)</td>
          <td>จับกุม (18)</td>
          <td>ขาดอายุความ (19)</td>
          <td>ตาย (20)</td>
          <td>อายัด (21)</td>
          <td>อื่น ๆ (22)</td>
          <td>รวม (23)</td>
          <td align="center" valign="bottom"><span class="textAlignVer">มีคุณภาพ (24)</td>
          <td align="center" valign="bottom"><span class="textAlignVer">ไม่มีคุณภาพ (25)</td>
         </tr>
    ';
}
?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<base target="_top">
<title>รายงานหมายจับ ภ.จว.สุโขทัย</title>
<link rel="stylesheet" href="/bootstrap/css/bootstrap-grid.css">
<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<style type="text/css">
body{
    font-size:12px; 
}
.textAlignVer{
    display:block;
    filter: flipv fliph;
    -webkit-transform: rotate(-90deg); 
    -moz-transform: rotate(-90deg); 
    transform: rotate(-90deg); 
    position:relative;
    width:20px;
    white-space:nowrap;
    font-size:16px;
    margin-bottom:10px;
}
.box !im{
    border: black;
    border-top-color: black !important;
    border-bottom-color: black !important;
    border-left-color: black !important;
    border-right-color: black !important;

}
</style>
 <!-- สไตล์ สำหรับ สั่งพิมพ์ข้อมูล -->
<style>
@media only print
{
   
}
    
@media print {
    /* Set the page size to A4 landscape */
    @page {
    size: A4 landscape;
  }
    /* Set the margins */
  body {
    margin: 0;
    padding: 0;
  }
td{
    color: black;
}
tr{
    color: black;
}
  .printId{visibility: visible;}
    
.footer { page-break-after: always;}
}
.body_text{
    font-size: 16px;
    color: black;
}
/* Avoid breaking content across pages */
  .content {
    page-break-inside: avoid;
  }
/* Hide elements that shouldn't be printed */
  .no-print {
    display: none;
  }

</style>
</head>

<body>
    <!-- นำสไตล์ สำหรับ สั่งพิมพ์ข้อมูล มาใช้งาน ภายใต้ <dvi></div>-->
<div class="printId">
<div class="container-fluid" align="left">
<h3 align="center">ข้อมูลหมายจับค้างเก่า  <?= $name_provincial ?> </h3>
<h3 align="center">ประจำเดือน&nbsp; <?= $current_month ?> &nbsp;<?= $y2 ?> &nbsp;</h3><br>
<table width="90%" border="1" cellspacing="1" cellpadding="1" class="table table-bordered">
  <tbody>
         <tr  height=28 style='height:21.0pt'>
           <td rowspan="3" bordercolor="#0C0B0B"  style='border:solid #000000;
          height:21.0pt; background-color:aqua '>&nbsp;หน่วย</td>
           <td height="28" colspan="4" bordercolor="#000000"  style='border:solid #000000; background-color:#DDDDDD '>ยอดหมายจับ (ยกมา)</td>
           <td height=28 colspan=12 bordercolor="#0C0B0B"  style='border:solid #000000;
          height:21.0pt; background-color:aqua '><span style="height:21.0pt; background-color:aqua ">หมายจับทั่วไปของ  <?= $name_provincial ?>  ตั้งแต่ 11 ต.ค.45 - <?= date_2_Thai($limite_date) ?></span></td>
          <td colspan=14  style='border:solid #000000; background-color:#ADFDB6'>หมายจับทั่วไปของ
           <?= $name_provincial ?>  ตั้งแต่ <?= date_2_Thai($check_date) ?>  - ปัจจุบัน</td>
          <td colspan=3  style='border:solid #000000; background-color: yellow; text-align: center'>รวม (ในเดือนนั้น)</td>
         </tr>
         <tr  height=28 style='height:21.0pt'>
           <td rowspan=2  bordercolor="#000000"  style='border:solid #000000; background-color:#DDDDDD '><p>ยอดหมายจับ (01)</p></td>
           <td rowspan=2  bordercolor="#000000"  style='border:solid #000000; background-color:#DDDDDD '><p>มีคุณภาพ  (02)</p></td>
           <td rowspan=2  bordercolor="#000000"  style='border:solid #000000; background-color:#DDDDDD '>อายัด (03)</td>
          <td rowspan=2  bordercolor="#000000"  style='border:solid #000000; background-color:#51ADFC '><p>ไม่มีคุณภาพ (04)</p></td>
          <td rowspan=2  width=104 style='border:solid #000000; width:78pt; background-color:aqua ' bordercolor="#0C0B0B">หมายคงเหลือทั้งหมด (1)</td>
          <td colspan="2" align=left  style='border:solid #000000; background-color:aqua '>คุณภาพของหมาย</td>
          <td colspan=6  style='border:solid #000000; background-color:#FA757F '><p>การจำหน่ายหมายจับ
            (เพิ่มเติม)
             (<?= $current_month ?>&nbsp;<?= $y2 ?>)</td>
          <td colspan=2  style='border:solid #000000;
          border-left:none; background-color:aqua '>จำนวนหมายคงเหลือ</td>
          <td rowspan=2  width=93 style='border:solid #000000;
          border-top:none;width:70pt; background-color:aqua '>ร้อยละผลการจับกุมจากหมายจับทั้งหมด (12)</td>
          <td rowspan=2  width=87 style='border:solid #000000;
          border-top:none;width:65pt; background-color:#ADFDB6'>หมายจับคงเหลือทั้งหมดจากการประชุมที่ผ่านมา (13)</td>
          <td rowspan=2  width=81 style='border:solid #000000;
          border-top:none;width:61pt; background-color:#ADFDB6'>จำนวนหมายจับใหม่ (เพิ่มเติม) (14)</td>
          <td rowspan=2  width=65 style='border:solid #000000;
          border-top:none;width:49pt; background-color:#ADFDB6'>รวมหมายจับทั้งหมด (15)</td>
          <td colspan="2" align=left class=xl961464 style='border:solid #000000; background-color:#ADFDB6'>คุณภาพของหมาย</td>
          <td colspan=6  style='border:solid #000000; background-color:#FA757F'>การจำหน่ายหมายจับ
            (เพิ่มเติม)<br>
            (<?= $current_month ?>&nbsp;<?= $y2 ?>)</td>
          <td colspan=2  style='border:solid #000000;
          border-left:none; background-color:#ADFDB6'>จำนวนหมายคงเหลือ</td>
          <td rowspan=2  width=93 style='border:solid #000000;
          border-top:none;width:70pt; background-color:#ADFDB6'>ร้อยละผลการจับกุมจากหมายจับทั้งหมด (26)</td>
          <td rowspan=2  width=70 style='border:solid #000000;width:53pt; background-color: #51ADFC'>รวมหมายจับทั้งหมด
          1 ต.ค.45 - ปัจจุบัน (27)</td>
          <td rowspan=2  width=70 style='border:solid #000000;width:53pt; background-color: #FA757F'>รวมจำหน่ายหมายทั้งหมด (28)</td>
          <td rowspan=2  width=70 style='border:solid #000000;width:53pt; background-color: yellow'>ร้อยละผลการจับกุมจากหมายจับทั้งหมด</td>
         </tr>
         <tr  height=133>
          <td style='border:solid #000000; background-color:#7FFA75 ' align="center" valign="bottom"><span class="textAlignVer">มีคุณภาพ (2)</td>
             <td  style='border:solid #000000; background-color:aqua ' align="center" valign="bottom"><span class="textAlignVer">ไม่มีคุณภาพ (3)</td>
          <td  style='border:solid #000000; background-color:aqua '>จับกุม (4)</td>
          <td  style='border:solid #000000; background-color:aqua '>ขาดอายุความ (5)</td>
          <td  style='border:solid #000000; background-color:aqua '>ตาย (6)</td>
          <td  style='border:solid #000000; background-color:aqua '>อายัด (7)</td>
          <td  style='border:solid #000000; background-color:aqua '>อื่น ๆ (8)</td>
          <td  style='border:solid #000000; background-color:#FA757F '>รวม (9)</td>
          <td  style='border:solid #000000; background-color:#51ADFC ' align="center" valign="bottom"><span class="textAlignVer">มีคุณภาพ (10)</td>
          <td  style='border:solid #000000; background-color:aqua ' align="center" valign="bottom"><span class="textAlignVer">ไม่มีคุณภาพ (11)</td>
          <td  style='border:solid #000000; background-color:#7FFA75' align="center" valign="bottom"><span class="textAlignVer">มีคุณภาพ (16)</td>
          <td  style='border:solid #000000; background-color:#ADFDB6' align="center" valign="bottom"><span class="textAlignVer">ไม่มีคุณภาพ (17)</td>
          <td  style='border:solid #000000; background-color:#ADFDB6'>จับกุม (18)</td>
          <td  style='border:solid #000000; background-color:#ADFDB6'>ขาดอายุความ (19)</td>
          <td  style='border:solid #000000; background-color:#ADFDB6'>ตาย (20)</td>
          <td  style='border:solid #000000; background-color:#ADFDB6'>อายัด (21)</td>
          <td  style='border:solid #000000; background-color:#ADFDB6'>อื่น ๆ (22)</td>
          <td  style='border:solid #000000; background-color:#FA757F'>รวม (23)</td>
          <td  style='border:solid #000000; background-color:#51ADFC' align="center" valign="bottom"><span class="textAlignVer">มีคุณภาพ (24)</td>
          <td  style='border:solid #000000; background-color:#ADFDB6' align="center" valign="bottom"><span class="textAlignVer">ไม่มีคุณภาพ (25)</td>
         </tr>
<?php                                    
    
//$date1 = date("Y", $limite_date) . "-$month-01";        //"$year-$month-01";
$day1 = date("t", $limite_date);
$date2 = "$year-$month-$day1"; // <<<
                      
// ยอดหมายจับเก่า ที่ออกระหว่าง ปี 45 - 30 ก.ย.2565 ทั้งหมด
$date_prev = date("Y-m-d", $limite_date);
$date_next = date("Y-m-01", $check_date); // ตั้งแต่ 1 ตค เปนต้นไป
    $m1 = date("Y-m-01", $select_date);       // m1 = เดือนปัจจุบันที่เลือก
    $m2 = date("Y-m-t", $select_date);        // << end date of current month ได้ค่าวันสุดท้ายของเดือนปัจจุบันที่เลือกมา
                      
//ฟังก์ชั่น เลือกเดือน ย้อนหลัง 1 เดือน ได้ตัวแปร $sum2 ไปใช้งาน
$sum = arrest_summary_count( $select_date );
$prev_y = $year;
$prev_m = $month - 1;
if($prev_m < 0) {
    $prev_m = 12;
    $prev_y--;
}
$mp = strtotime("$prev_y-$prev_m-01");
$sum2 = arrest_summary_count( $mp );  // ยอดย้อนหลังไป 1 เดือน
                      
$sql = "SELECT " .
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `wr_cl_status` IN (1,3)) AS S_01, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6701 AND `wr_cl_status` IN (1,3)) AS S_01_kkl, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก กงไกราลศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6702 AND `wr_cl_status` IN (1,3)) AS S_01_krm, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก คีรีมาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6703 AND `wr_cl_status` IN (1,3)) AS S_01_tsl, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ทุ่งเสลี่ยม
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6704 AND `wr_cl_status` IN (1,3)) AS S_01_bng, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก บ้านแก่ง
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6705 AND `wr_cl_status` IN (1,3)) AS S_01_bdh, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก บ้านด่านลานหอย
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6706 AND `wr_cl_status` IN (1,3)) AS S_01_bnr, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก บ้านไร่
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6707 AND `wr_cl_status` IN (1,3)) AS S_01_bs, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก บ้านสวน
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6708 AND `wr_cl_status` IN (1,3)) AS S_01_mgk, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก เมืองเก่า
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6709 AND `wr_cl_status` IN (1,3)) AS S_01_mst, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก เมืองสุโขทัย
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6710 AND `wr_cl_status` IN (1,3)) AS S_01_snk, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ศรีนคร
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6711 AND `wr_cl_status` IN (1,3)) AS S_01_snl, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ศรีสัชนาลัย
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6712 AND `wr_cl_status` IN (1,3)) AS S_01_ssr, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ศรีสำโรง
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6713 AND `wr_cl_status` IN (1,3)) AS S_01_swl, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก สวรรคโลก
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6714 AND `wr_cl_status` IN (1,3)) AS S_01_tcn, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก ท่าฉนวน
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6715 AND `wr_cl_status` IN (1,3)) AS S_01_mbk, ". // ยอดหมายจับทั้งหมด เก่า + ใหม่ ทั้งมีคุณภาพและไม่มีคุณภาพ แต่สถานะยังไม่ถูกจำหน่ายออก เมืองบางขลัง
	
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `wr_cl_qty`=1 AND (`wr_cl_status` IN (1,3))) AS S_02, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6701 AND `wr_cl_qty`=1 AND (`wr_cl_status` IN (1,3))) AS S_02_kkl, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย กงไกรลาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6702 AND `wr_cl_qty`=1 AND (`wr_cl_status` IN (1,3))) AS S_02_krm, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย คีรีมาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6703 AND `wr_cl_qty`=1 AND (`wr_cl_status` IN (1,3))) AS S_02_tsl, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย tsl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6704 AND `wr_cl_qty`=1 AND (`wr_cl_status` IN (1,3))) AS S_02_bng, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย bng
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6705 AND `wr_cl_qty`=1 AND (`wr_cl_status` IN (1,3))) AS S_02_bdh, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย bdh
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6706 AND `wr_cl_qty`=1 AND (`wr_cl_status` IN (1,3))) AS S_02_bnr, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย bnr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6707 AND `wr_cl_qty`=1 AND (`wr_cl_status` IN (1,3))) AS S_02_bs, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย bs
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6708 AND `wr_cl_qty`=1 AND (`wr_cl_status` IN (1,3))) AS S_02_mgk, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย mgk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6709 AND `wr_cl_qty`=1 AND (`wr_cl_status` IN (1,3))) AS S_02_mst, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย mst
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6710 AND `wr_cl_qty`=1 AND (`wr_cl_status` IN (1,3))) AS S_02_snk, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย snk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6711 AND `wr_cl_qty`=1 AND (`wr_cl_status` IN (1,3))) AS S_02_snl, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย snl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6712 AND `wr_cl_qty`=1 AND (`wr_cl_status` IN (1,3))) AS S_02_ssr, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย ssr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6713 AND `wr_cl_qty`=1 AND (`wr_cl_status` IN (1,3))) AS S_02_swl, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย swl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6714 AND `wr_cl_qty`=1 AND (`wr_cl_status` IN (1,3))) AS S_02_tcn, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย tcn
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6715 AND `wr_cl_qty`=1 AND (`wr_cl_status` IN (1,3))) AS S_02_mbk, ". // ยอดหมายมีคุณภาพทั้งหมด ที่ยังไม่ถูกจำหน่าย mbk
		
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `wr_cl_qty`='1' AND (`wr_cl_status` IN ('3'))) AS S_03, ". // ยอดอายัดหมายทั้งหมด
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6701 AND `wr_cl_qty`='1' AND (`wr_cl_status` IN ('3'))) AS S_03_kkl, ". // ยอดอายัดหมายทั้งหมด กงไกรลาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6702 AND `wr_cl_qty`='1' AND (`wr_cl_status` IN ('3'))) AS S_03_krm, ". // ยอดอายัดหมายทั้งหมด คีรีมาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6703 AND `wr_cl_qty`='1' AND (`wr_cl_status` IN ('3'))) AS S_03_tsl, ". // ยอดอายัดหมายทั้งหมด tsl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6704 AND `wr_cl_qty`='1' AND (`wr_cl_status` IN ('3'))) AS S_03_bng, ". // ยอดอายัดหมายทั้งหมด bng
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6705 AND `wr_cl_qty`='1' AND (`wr_cl_status` IN ('3'))) AS S_03_bdh, ". // ยอดอายัดหมายทั้งหมด bdh
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6706 AND `wr_cl_qty`='1' AND (`wr_cl_status` IN ('3'))) AS S_03_bnr, ". // ยอดอายัดหมายทั้งหมด bnr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6707 AND `wr_cl_qty`='1' AND (`wr_cl_status` IN ('3'))) AS S_03_bs, ". // ยอดอายัดหมายทั้งหมด bs
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6708 AND `wr_cl_qty`='1' AND (`wr_cl_status` IN ('3'))) AS S_03_mgk, ". // ยอดอายัดหมายทั้งหมด mgk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6709 AND `wr_cl_qty`='1' AND (`wr_cl_status` IN ('3'))) AS S_03_mst, ". // ยอดอายัดหมายทั้งหมด mst
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6710 AND `wr_cl_qty`='1' AND (`wr_cl_status` IN ('3'))) AS S_03_snk, ". // ยอดอายัดหมายทั้งหมด snk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6711 AND `wr_cl_qty`='1' AND (`wr_cl_status` IN ('3'))) AS S_03_snl, ". // ยอดอายัดหมายทั้งหมด snl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6712 AND `wr_cl_qty`='1' AND (`wr_cl_status` IN ('3'))) AS S_03_ssr, ". // ยอดอายัดหมายทั้งหมด ssr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6713 AND `wr_cl_qty`='1' AND (`wr_cl_status` IN ('3'))) AS S_03_swl, ". // ยอดอายัดหมายทั้งหมด swl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6714 AND `wr_cl_qty`='1' AND (`wr_cl_status` IN ('3'))) AS S_03_tcn, ". // ยอดอายัดหมายทั้งหมด tcn
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6715 AND `wr_cl_qty`='1' AND (`wr_cl_status` IN ('3'))) AS S_03_mbk, ". // ยอดอายัดหมายทั้งหมด mbk
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS S_04, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6701 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS S_04_kkl, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย กงไกรลาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6702 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS S_04_krm, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย คีรีมาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6703 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS S_04_tsl, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย tsl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6704 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS S_04_bng, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย bng
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6705 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS S_04_bdh, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย bdh
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6706 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS S_04_bnr, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย bnr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6707 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS S_04_bs, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย bs
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6708 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS S_04_mgk, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย mgk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6709 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS S_04_mst, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย mst
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6710 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS S_04_snk, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย snk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6711 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS S_04_snl, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย snl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6712 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS S_04_ssr, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย ssr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6713 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS S_04_swl, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย swl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6714 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS S_04_tcn, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย tcn
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :m2) AND `provincial`=:provincial AND `station`=6715 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS S_04_mbk, ". // หมายไม่มีคุณภาพ ที่ยังไม่จำหน่าย mbk
	
    /////////////////////////////// หมายจับเก่า  ///////////////////////////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `wr_cl_status` IN (1,3)) AS S_1, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6701 AND `wr_cl_status` IN (1,3)) AS S_1_kkl, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด กงไกรลาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6702 AND `wr_cl_status` IN (1,3)) AS S_1_krm, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด คีรีมาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6703 AND `wr_cl_status` IN (1,3)) AS S_1_tsl, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด tsl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6704 AND `wr_cl_status` IN (1,3)) AS S_1_bng, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด bng
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6705 AND `wr_cl_status` IN (1,3)) AS S_1_bdh, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด bdh
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6706 AND `wr_cl_status` IN (1,3)) AS S_1_bnr, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด bnr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6707 AND `wr_cl_status` IN (1,3)) AS S_1_bs, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด bs
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6708 AND `wr_cl_status` IN (1,3)) AS S_1_mgk, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด mgk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6709 AND `wr_cl_status` IN (1,3)) AS S_1_mst, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด mst
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6710 AND `wr_cl_status` IN (1,3)) AS S_1_snk, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด snk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6711 AND `wr_cl_status` IN (1,3)) AS S_1_snl, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด snl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6712 AND `wr_cl_status` IN (1,3)) AS S_1_ssr, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด ssr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6713 AND `wr_cl_status` IN (1,3)) AS S_1_swl, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด swl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6714 AND `wr_cl_status` IN (1,3)) AS S_1_tcn, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด tcn
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6715 AND `wr_cl_status` IN (1,3)) AS S_1_mbk, ".  // หมายจับเก่า ก่อน 1 ต.ค.2565 ทั้งหมด mbk
	
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `wr_cl_qty`IN('1') AND (`wr_cl_status` IN (1,3))) AS S_2, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6701 AND `wr_cl_qty`IN('1') AND (`wr_cl_status` IN (1,3))) AS S_2_kkl, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ กงไกรลาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6702 AND `wr_cl_qty`IN('1') AND (`wr_cl_status` IN (1,3))) AS S_2_krm, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ คีรีมาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6703 AND `wr_cl_qty`IN('1') AND (`wr_cl_status` IN (1,3))) AS S_2_tsl, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ tsl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6704 AND `wr_cl_qty`IN('1') AND (`wr_cl_status` IN (1,3))) AS S_2_bng, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ bng
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6705 AND `wr_cl_qty`IN('1') AND (`wr_cl_status` IN (1,3))) AS S_2_bdh, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ bdh
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6706 AND `wr_cl_qty`IN('1') AND (`wr_cl_status` IN (1,3))) AS S_2_bnr, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ bnr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6707 AND `wr_cl_qty`IN('1') AND (`wr_cl_status` IN (1,3))) AS S_2_bs, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ bs
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6708 AND `wr_cl_qty`IN('1') AND (`wr_cl_status` IN (1,3))) AS S_2_mgk, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ mgk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6709 AND `wr_cl_qty`IN('1') AND (`wr_cl_status` IN (1,3))) AS S_2_mst, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ mst
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6710 AND `wr_cl_qty`IN('1') AND (`wr_cl_status` IN (1,3))) AS S_2_snk, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ snk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6711 AND `wr_cl_qty`IN('1') AND (`wr_cl_status` IN (1,3))) AS S_2_snl, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ snl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6712 AND `wr_cl_qty`IN('1') AND (`wr_cl_status` IN (1,3))) AS S_2_ssr, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ ssr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6713 AND `wr_cl_qty`IN('1') AND (`wr_cl_status` IN (1,3))) AS S_2_swl, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ swl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6714 AND `wr_cl_qty`IN('1') AND (`wr_cl_status` IN (1,3))) AS S_2_tcn, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ tcn
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6715 AND `wr_cl_qty`IN('1') AND (`wr_cl_status` IN (1,3))) AS S_2_mbk, ". // หมายจับเก่า เฉพาะที่มีคุณภาพ mbk
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `wr_cl_qty`IN(2) AND (`wr_cl_status` IN (1,3))) AS S_3, ". // หมายจับเก่า ไม่มีคุณภาพ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6701 AND `wr_cl_qty`IN(2) AND (`wr_cl_status` IN (1,3))) AS S_3_kkl, ". // หมายจับเก่า ไม่มีคุณภาพ กงไกรลาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6702 AND `wr_cl_qty`IN(2) AND (`wr_cl_status` IN (1,3))) AS S_3_krm, ". // หมายจับเก่า ไม่มีคุณภาพ คีรีมาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6703 AND `wr_cl_qty`IN(2) AND (`wr_cl_status` IN (1,3))) AS S_3_tsl, ". // หมายจับเก่า ไม่มีคุณภาพ tsl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6704 AND `wr_cl_qty`IN(2) AND (`wr_cl_status` IN (1,3))) AS S_3_bng, ". // หมายจับเก่า ไม่มีคุณภาพ bng
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6705 AND `wr_cl_qty`IN(2) AND (`wr_cl_status` IN (1,3))) AS S_3_bdh, ". // หมายจับเก่า ไม่มีคุณภาพ bdh
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6706 AND `wr_cl_qty`IN(2) AND (`wr_cl_status` IN (1,3))) AS S_3_bnr, ". // หมายจับเก่า ไม่มีคุณภาพ bnr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6707 AND `wr_cl_qty`IN(2) AND (`wr_cl_status` IN (1,3))) AS S_3_bs, ". // หมายจับเก่า ไม่มีคุณภาพ bs
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6708 AND `wr_cl_qty`IN(2) AND (`wr_cl_status` IN (1,3))) AS S_3_mgk, ". // หมายจับเก่า ไม่มีคุณภาพ mgk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6709 AND `wr_cl_qty`IN(2) AND (`wr_cl_status` IN (1,3))) AS S_3_mst, ". // หมายจับเก่า ไม่มีคุณภาพ mst
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6710 AND `wr_cl_qty`IN(2) AND (`wr_cl_status` IN (1,3))) AS S_3_snk, ". // หมายจับเก่า ไม่มีคุณภาพ snk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6711 AND `wr_cl_qty`IN(2) AND (`wr_cl_status` IN (1,3))) AS S_3_snl, ". // หมายจับเก่า ไม่มีคุณภาพ snl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6712 AND `wr_cl_qty`IN(2) AND (`wr_cl_status` IN (1,3))) AS S_3_ssr, ". // หมายจับเก่า ไม่มีคุณภาพ ssr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6713 AND `wr_cl_qty`IN(2) AND (`wr_cl_status` IN (1,3))) AS S_3_swl, ". // หมายจับเก่า ไม่มีคุณภาพ swl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6714 AND `wr_cl_qty`IN(2) AND (`wr_cl_status` IN (1,3))) AS S_3_tcn, ". // หมายจับเก่า ไม่มีคุณภาพ tcn
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND `provincial`=:provincial AND `station`=6715 AND `wr_cl_qty`IN(2) AND (`wr_cl_status` IN (1,3))) AS S_3_mbk, ". // หมายจับเก่า ไม่มีคุณภาพ mbk
	
    //////////////////////////////   ยอดจำหน่ายหมายเก่า   ///////////////////////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS S_4, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6701 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS S_4_kkl, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ กงไกรลาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6702 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS S_4_krm, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ คีรีมาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6703 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS S_4_tsl, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ tsl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6704 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS S_4_bng, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ bng
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6705 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS S_4_bdh, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ bdh
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6706 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS S_4_bnr, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ bnr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6707 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS S_4_bs, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ bs
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6708 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS S_4_mgk, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ mgk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6709 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS S_4_mst, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ mst
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6710 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS S_4_snk, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ snk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6711 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS S_4_snl, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ snl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6712 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS S_4_ssr, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ ssr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6713 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS S_4_swl, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ swl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6714 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS S_4_tcn, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ tcn
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6715 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS S_4_mbk, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายที่มีคุณภาพ mbk
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`=2 AND `wr_cl_qty`=2) AS S_4_2, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะไม่่มีคุณภาพ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6701 AND `wr_cl_status`=2 AND `wr_cl_qty`=2) AS S_4_2_kkl, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะไม่่มีคุณภาพ กงไกรลาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6702 AND `wr_cl_status`=2 AND `wr_cl_qty`=2) AS S_4_2_krm, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะไม่่มีคุณภาพ คีรีมาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6703 AND `wr_cl_status`=2 AND `wr_cl_qty`=2) AS S_4_2_tsl, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะไม่่มีคุณภาพ tsl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6704 AND `wr_cl_status`=2 AND `wr_cl_qty`=2) AS S_4_2_bng, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะไม่่มีคุณภาพ bng
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6705 AND `wr_cl_status`=2 AND `wr_cl_qty`=2) AS S_4_2_bdh, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะไม่่มีคุณภาพ bdh
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6706 AND `wr_cl_status`=2 AND `wr_cl_qty`=2) AS S_4_2_bnr, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะไม่่มีคุณภาพ bnr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6707 AND `wr_cl_status`=2 AND `wr_cl_qty`=2) AS S_4_2_bs, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะไม่่มีคุณภาพ bs
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6708 AND `wr_cl_status`=2 AND `wr_cl_qty`=2) AS S_4_2_mgk, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะไม่่มีคุณภาพ mgk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6709 AND `wr_cl_status`=2 AND `wr_cl_qty`=2) AS S_4_2_mst, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะไม่่มีคุณภาพ mst
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6710 AND `wr_cl_status`=2 AND `wr_cl_qty`=2) AS S_4_2_snk, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะไม่่มีคุณภาพ snk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6711 AND `wr_cl_status`=2 AND `wr_cl_qty`=2) AS S_4_2_snl, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะไม่่มีคุณภาพ snl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6712 AND `wr_cl_status`=2 AND `wr_cl_qty`=2) AS S_4_2_ssr, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะไม่่มีคุณภาพ ssr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6713 AND `wr_cl_status`=2 AND `wr_cl_qty`=2) AS S_4_2_swl, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะไม่่มีคุณภาพ swl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6714 AND `wr_cl_status`=2 AND `wr_cl_qty`=2) AS S_4_2_tcn, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะไม่่มีคุณภาพ tcn
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6715 AND `wr_cl_status`=2 AND `wr_cl_qty`=2) AS S_4_2_mbk, ".   // ยอดจับกุม (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะไม่่มีคุณภาพ tmbk
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`='5' AND `wr_cl_qty`=1) AS S_5, ".    // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายมีคุณภาพ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6701 AND `wr_cl_status`='5' AND `wr_cl_qty`=1) AS S_5_kkl, ".    // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายมีคุณภาพ กงไกรลาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6702 AND `wr_cl_status`='5' AND `wr_cl_qty`=1) AS S_5_krm, ".    // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายมีคุณภาพ คีรีมาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6703 AND `wr_cl_status`='5' AND `wr_cl_qty`=1) AS S_5_tsl, ".    // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายมีคุณภาพ tsl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6704 AND `wr_cl_status`='5' AND `wr_cl_qty`=1) AS S_5_bng, ".    // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายมีคุณภาพ bng
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6705 AND `wr_cl_status`='5' AND `wr_cl_qty`=1) AS S_5_bdh, ".    // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายมีคุณภาพ bdh
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6706 AND `wr_cl_status`='5' AND `wr_cl_qty`=1) AS S_5_bnr, ".    // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายมีคุณภาพ bnr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6707 AND `wr_cl_status`='5' AND `wr_cl_qty`=1) AS S_5_bs, ".    // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายมีคุณภาพ bs
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6708 AND `wr_cl_status`='5' AND `wr_cl_qty`=1) AS S_5_mgk, ".    // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายมีคุณภาพ mgk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6709 AND `wr_cl_status`='5' AND `wr_cl_qty`=1) AS S_5_mst, ".    // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายมีคุณภาพ mst
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6710 AND `wr_cl_status`='5' AND `wr_cl_qty`=1) AS S_5_snk, ".    // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายมีคุณภาพ snk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6711 AND `wr_cl_status`='5' AND `wr_cl_qty`=1) AS S_5_snl, ".    // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายมีคุณภาพ snl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6712 AND `wr_cl_status`='5' AND `wr_cl_qty`=1) AS S_5_ssr, ".    // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายมีคุณภาพ ssr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6713 AND `wr_cl_status`='5' AND `wr_cl_qty`=1) AS S_5_swl, ".    // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายมีคุณภาพ swl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6714 AND `wr_cl_status`='5' AND `wr_cl_qty`=1) AS S_5_tcn, ".    // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายมีคุณภาพ tcn
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6715 AND `wr_cl_status`='5' AND `wr_cl_qty`=1) AS S_5_mbk, ".    // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะหมายมีคุณภาพ mbk
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND  `wr_cl_status`='5' AND `wr_cl_qty`=2) AS S_5_2, ".    // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6701 AND  `wr_cl_status`='5' AND `wr_cl_qty`=2) AS S_5_2_kkl, ".    // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ กงไกรลาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6702 AND  `wr_cl_status`='5' AND `wr_cl_qty`=2) AS S_5_2_krm, ".    // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ คีรีมาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6703 AND  `wr_cl_status`='5' AND `wr_cl_qty`=2) AS S_5_2_tsl, ".    // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ tsl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6704 AND  `wr_cl_status`='5' AND `wr_cl_qty`=2) AS S_5_2_bng, ".    // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ bng
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6705 AND  `wr_cl_status`='5' AND `wr_cl_qty`=2) AS S_5_2_bdh, ".    // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ bdh
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6706 AND  `wr_cl_status`='5' AND `wr_cl_qty`=2) AS S_5_2_bnr, ".    // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ bnr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6707 AND  `wr_cl_status`='5' AND `wr_cl_qty`=2) AS S_5_2_bs, ".    // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ bs
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6708 AND  `wr_cl_status`='5' AND `wr_cl_qty`=2) AS S_5_2_mgk, ".    // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ mgk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6709 AND  `wr_cl_status`='5' AND `wr_cl_qty`=2) AS S_5_2_mst, ".    // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ mst
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6710 AND  `wr_cl_status`='5' AND `wr_cl_qty`=2) AS S_5_2_snk, ".    // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ snk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6711 AND  `wr_cl_status`='5' AND `wr_cl_qty`=2) AS S_5_2_snl, ".    // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ snk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6712 AND  `wr_cl_status`='5' AND `wr_cl_qty`=2) AS S_5_2_ssr, ".    // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ ssr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6713 AND  `wr_cl_status`='5' AND `wr_cl_qty`=2) AS S_5_2_swl, ".    // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ swl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6714 AND  `wr_cl_status`='5' AND `wr_cl_qty`=2) AS S_5_2_tcn, ".    // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ tcn
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6715 AND  `wr_cl_status`='5' AND `wr_cl_qty`=2) AS S_5_2_mbk, ".    // ยอดขาดอายุความ (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ mbk
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`='6' AND `wr_cl_qty`=1) AS S_6, ".       // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6701 AND `wr_cl_status`='6' AND `wr_cl_qty`=1) AS S_6_kkl, ".       // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ กงไกรลาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6702 AND `wr_cl_status`='6' AND `wr_cl_qty`=1) AS S_6_krm, ".       // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ คีรีมาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6703 AND `wr_cl_status`='6' AND `wr_cl_qty`=1) AS S_6_tsl, ".       // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ tsl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6704 AND `wr_cl_status`='6' AND `wr_cl_qty`=1) AS S_6_bng, ".       // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ bng
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6705 AND `wr_cl_status`='6' AND `wr_cl_qty`=1) AS S_6_bdh, ".       // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ bdh
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6706 AND `wr_cl_status`='6' AND `wr_cl_qty`=1) AS S_6_bnr, ".       // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ bnr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6707 AND `wr_cl_status`='6' AND `wr_cl_qty`=1) AS S_6_bs, ".       // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ bs
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6708 AND `wr_cl_status`='6' AND `wr_cl_qty`=1) AS S_6_mgk, ".       // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ mgk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6709 AND `wr_cl_status`='6' AND `wr_cl_qty`=1) AS S_6_mst, ".       // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ mst
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6710 AND `wr_cl_status`='6' AND `wr_cl_qty`=1) AS S_6_snk, ".       // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ snk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6711 AND `wr_cl_status`='6' AND `wr_cl_qty`=1) AS S_6_snl, ".       // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ snl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6712 AND `wr_cl_status`='6' AND `wr_cl_qty`=1) AS S_6_ssr, ".       // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ ssr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6713 AND `wr_cl_status`='6' AND `wr_cl_qty`=1) AS S_6_swl, ".       // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ swl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6714 AND `wr_cl_status`='6' AND `wr_cl_qty`=1) AS S_6_tcn, ".       // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ tcn
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6715 AND `wr_cl_status`='6' AND `wr_cl_qty`=1) AS S_6_mbk, ".       // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะมีคุณภาพ mbk
	
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`='6' AND `wr_cl_qty`=2) AS S_6_2, ".       // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6701 AND `wr_cl_status`='6' AND `wr_cl_qty`=2) AS S_6_2_kkl, ".       // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ กงไกรลาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6702 AND `wr_cl_status`='6' AND `wr_cl_qty`=2) AS S_6_2_krm, ".       // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ คีรีมาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6703 AND `wr_cl_status`='6' AND `wr_cl_qty`=2) AS S_6_2_tsl, ".       // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ tsl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6704 AND `wr_cl_status`='6' AND `wr_cl_qty`=2) AS S_6_2_bng, ".       // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ bng
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6705 AND `wr_cl_status`='6' AND `wr_cl_qty`=2) AS S_6_2_bdh, ".       // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ bdh
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6706 AND `wr_cl_status`='6' AND `wr_cl_qty`=2) AS S_6_2_bnr, ".       // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ bnr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6707 AND `wr_cl_status`='6' AND `wr_cl_qty`=2) AS S_6_2_bs, ".       // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ bs
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6708 AND `wr_cl_status`='6' AND `wr_cl_qty`=2) AS S_6_2_mgk, ".       // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ mgk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6709 AND `wr_cl_status`='6' AND `wr_cl_qty`=2) AS S_6_2_mst, ".       // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ mst
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6710 AND `wr_cl_status`='6' AND `wr_cl_qty`=2) AS S_6_2_snk, ".       // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ snk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6711 AND `wr_cl_status`='6' AND `wr_cl_qty`=2) AS S_6_2_snl, ".       // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ snl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6712 AND `wr_cl_status`='6' AND `wr_cl_qty`=2) AS S_6_2_ssr, ".       // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ ssr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6713 AND `wr_cl_status`='6' AND `wr_cl_qty`=2) AS S_6_2_swl, ".       // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ swl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6714 AND `wr_cl_status`='6' AND `wr_cl_qty`=2) AS S_6_2_tcn, ".       // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ tcn
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6715 AND `wr_cl_status`='6' AND `wr_cl_qty`=2) AS S_6_2_mbk, ".       // ยอดตาย (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ mbk
	
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`='3' AND `wr_cl_qty`=1) AS S_7 , " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6701 AND `wr_cl_status`='3' AND `wr_cl_qty`=1) AS S_7_kkl, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ กงไกรลาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6702 AND `wr_cl_status`='3' AND `wr_cl_qty`=1) AS S_7_krm, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ คีรีมาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6703 AND `wr_cl_status`='3' AND `wr_cl_qty`=1) AS S_7_tsl, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ tsl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6704 AND `wr_cl_status`='3' AND `wr_cl_qty`=1) AS S_7_bng, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ bng
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6705 AND `wr_cl_status`='3' AND `wr_cl_qty`=1) AS S_7_bdh, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ bdh
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6706 AND `wr_cl_status`='3' AND `wr_cl_qty`=1) AS S_7_bnr, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ bnr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6707 AND `wr_cl_status`='3' AND `wr_cl_qty`=1) AS S_7_bs, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ bs
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6708 AND `wr_cl_status`='3' AND `wr_cl_qty`=1) AS S_7_mgk, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ mgk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6709 AND `wr_cl_status`='3' AND `wr_cl_qty`=1) AS S_7_mst, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ mst
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6710 AND `wr_cl_status`='3' AND `wr_cl_qty`=1) AS S_7_snk, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ snk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6711 AND `wr_cl_status`='3' AND `wr_cl_qty`=1) AS S_7_snl, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ snl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6712 AND `wr_cl_status`='3' AND `wr_cl_qty`=1) AS S_7_ssr, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ ssr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6713 AND `wr_cl_status`='3' AND `wr_cl_qty`=1) AS S_7_swl, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ swl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6714 AND `wr_cl_status`='3' AND `wr_cl_qty`=1) AS S_7_tcn, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ tcn
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6715 AND `wr_cl_status`='3' AND `wr_cl_qty`=1) AS S_7_mbk, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) เฉพาะที่มีคุณภาพ mbk
	
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`='3' AND `wr_cl_qty`=2) AS S_7_2 , " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6701 AND `wr_cl_status`='3' AND `wr_cl_qty`=2) AS S_7_2_kkl, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ กงไกรลาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6702 AND `wr_cl_status`='3' AND `wr_cl_qty`=2) AS S_7_2_krm, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ คีรีมาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6703 AND `wr_cl_status`='3' AND `wr_cl_qty`=2) AS S_7_2_tsl, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ tsl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6704 AND `wr_cl_status`='3' AND `wr_cl_qty`=2) AS S_7_2_bng, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ bng
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6705 AND `wr_cl_status`='3' AND `wr_cl_qty`=2) AS S_7_2_bdh, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ bdh
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6706 AND `wr_cl_status`='3' AND `wr_cl_qty`=2) AS S_7_2_bnr, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ bnr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6707 AND `wr_cl_status`='3' AND `wr_cl_qty`=2) AS S_7_2_bs, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ bs
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6708 AND `wr_cl_status`='3' AND `wr_cl_qty`=2) AS S_7_2_mgk, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ mgk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6709 AND `wr_cl_status`='3' AND `wr_cl_qty`=2) AS S_7_2_mst, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ mst
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6710 AND `wr_cl_status`='3' AND `wr_cl_qty`=2) AS S_7_2_snk, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ snk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6711 AND `wr_cl_status`='3' AND `wr_cl_qty`=2) AS S_7_2_snl, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ snl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6712 AND `wr_cl_status`='3' AND `wr_cl_qty`=2) AS S_7_2_ssr, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ ssr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6713 AND `wr_cl_status`='3' AND `wr_cl_qty`=2) AS S_7_2_swl, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ swl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6714 AND `wr_cl_status`='3' AND `wr_cl_qty`=2) AS S_7_2_tcn, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ tcn
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6715 AND `wr_cl_status`='3' AND `wr_cl_qty`=2) AS S_7_2_mbk, " .     // ยอดอายัด (ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ) ไม่มีคุณภาพ mbk
	
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`='7' AND `wr_cl_qty`=1)  AS S_8, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6701 AND `wr_cl_status`='7' AND `wr_cl_qty`=1)  AS S_8_kkl, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ กงไกรลาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6702 AND `wr_cl_status`='7' AND `wr_cl_qty`=1)  AS S_8_krm, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ คีรีมาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6703 AND `wr_cl_status`='7' AND `wr_cl_qty`=1)  AS S_8_tsl, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ tsl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6704 AND `wr_cl_status`='7' AND `wr_cl_qty`=1)  AS S_8_bng, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ bng
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6705 AND `wr_cl_status`='7' AND `wr_cl_qty`=1)  AS S_8_bdh, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ bdh
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6706 AND `wr_cl_status`='7' AND `wr_cl_qty`=1)  AS S_8_bnr, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ bnr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6707 AND `wr_cl_status`='7' AND `wr_cl_qty`=1)  AS S_8_bs, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ bs
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6708 AND `wr_cl_status`='7' AND `wr_cl_qty`=1)  AS S_8_mgk, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ mgk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6709 AND `wr_cl_status`='7' AND `wr_cl_qty`=1)  AS S_8_mst, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ mst
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6710 AND `wr_cl_status`='7' AND `wr_cl_qty`=1)  AS S_8_snk, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ snk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6711 AND `wr_cl_status`='7' AND `wr_cl_qty`=1)  AS S_8_snl, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ snl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6712 AND `wr_cl_status`='7' AND `wr_cl_qty`=1)  AS S_8_ssr, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ ssr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6713 AND `wr_cl_status`='7' AND `wr_cl_qty`=1)  AS S_8_swl, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ swl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6714 AND `wr_cl_status`='7' AND `wr_cl_qty`=1)  AS S_8_tcn, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ tcn
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6715 AND `wr_cl_status`='7' AND `wr_cl_qty`=1)  AS S_8_mbk, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) เฉพาะที่มีคุณภาพ mbk
	
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`='7' AND `wr_cl_qty`=2)  AS S_8_2, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ไม่มีคุณภาพ
	 "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6701 AND `wr_cl_status`='7' AND `wr_cl_qty`=2)  AS S_8_2_kkl, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ไม่มีคุณภาพ กงไกรลาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6702 AND `wr_cl_status`='7' AND `wr_cl_qty`=2)  AS S_8_2_krm, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ไม่มีคุณภาพ คีรีมาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6703 AND `wr_cl_status`='7' AND `wr_cl_qty`=2)  AS S_8_2_tsl, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ไม่มีคุณภาพ tsl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6704 AND `wr_cl_status`='7' AND `wr_cl_qty`=2)  AS S_8_2_bng, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ไม่มีคุณภาพ bng
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6705 AND `wr_cl_status`='7' AND `wr_cl_qty`=2)  AS S_8_2_bdh, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ไม่มีคุณภาพ bdh
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6706 AND `wr_cl_status`='7' AND `wr_cl_qty`=2)  AS S_8_2_bnr, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ไม่มีคุณภาพ bnr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6707 AND `wr_cl_status`='7' AND `wr_cl_qty`=2)  AS S_8_2_bs, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ไม่มีคุณภาพ bs
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6708 AND `wr_cl_status`='7' AND `wr_cl_qty`=2)  AS S_8_2_mgk, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ไม่มีคุณภาพ mgk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6709 AND `wr_cl_status`='7' AND `wr_cl_qty`=2)  AS S_8_2_mst, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ไม่มีคุณภาพ mst
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6710 AND `wr_cl_status`='7' AND `wr_cl_qty`=2)  AS S_8_2_snk, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ไม่มีคุณภาพ snk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6711 AND `wr_cl_status`='7' AND `wr_cl_qty`=2)  AS S_8_2_snl, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ไม่มีคุณภาพ snl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6712 AND `wr_cl_status`='7' AND `wr_cl_qty`=2)  AS S_8_2_ssr, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ไม่มีคุณภาพ ssr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6713 AND `wr_cl_status`='7' AND `wr_cl_qty`=2)  AS S_8_2_swl, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ไม่มีคุณภาพ swl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6714 AND `wr_cl_status`='7' AND `wr_cl_qty`=2)  AS S_8_2_tcn, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ไม่มีคุณภาพ tcn
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6715 AND `wr_cl_status`='7' AND `wr_cl_qty`=2)  AS S_8_2_mbk, ". // ยอดจำหน่ายอื่น ๆ ของหมายจับเก่า ที่ออกระหว่าง ปี 45 - $date_prev (30 ก.ย.2565) ไม่มีคุณภาพ mbk
	
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=1) AS S_9, ". // รวม ยอดจำหน่ายหมายเก่า เฉพาะที่มีคุณภาพ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6701 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=1) AS S_9_kkl, ". // รวม ยอดจำหน่ายหมายเก่า เฉพาะที่มีคุณภาพ กงไกรลาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6702 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=1) AS S_9_krm, ". // รวม ยอดจำหน่ายหมายเก่า เฉพาะที่มีคุณภาพ คีรีมาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6703 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=1) AS S_9_tsl, ". // รวม ยอดจำหน่ายหมายเก่า เฉพาะที่มีคุณภาพ tsl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6704 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=1) AS S_9_bng, ". // รวม ยอดจำหน่ายหมายเก่า เฉพาะที่มีคุณภาพ bng
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6705 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=1) AS S_9_bdh, ". // รวม ยอดจำหน่ายหมายเก่า เฉพาะที่มีคุณภาพ bdh
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6706 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=1) AS S_9_bnr, ". // รวม ยอดจำหน่ายหมายเก่า เฉพาะที่มีคุณภาพ bnr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6707 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=1) AS S_9_bs, ". // รวม ยอดจำหน่ายหมายเก่า เฉพาะที่มีคุณภาพ bs
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6708 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=1) AS S_9_mgk, ". // รวม ยอดจำหน่ายหมายเก่า เฉพาะที่มีคุณภาพ mgk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6709 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=1) AS S_9_mst, ". // รวม ยอดจำหน่ายหมายเก่า เฉพาะที่มีคุณภาพ mst
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6710 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=1) AS S_9_snk, ". // รวม ยอดจำหน่ายหมายเก่า เฉพาะที่มีคุณภาพ snk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6711 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=1) AS S_9_snl, ". // รวม ยอดจำหน่ายหมายเก่า เฉพาะที่มีคุณภาพ snl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6712 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=1) AS S_9_ssr, ". // รวม ยอดจำหน่ายหมายเก่า เฉพาะที่มีคุณภาพ ssr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6713 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=1) AS S_9_swl, ". // รวม ยอดจำหน่ายหมายเก่า เฉพาะที่มีคุณภาพ swl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6714 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=1) AS S_9_tcn, ". // รวม ยอดจำหน่ายหมายเก่า เฉพาะที่มีคุณภาพ tcn
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6715 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=1) AS S_9_mbk, ". // รวม ยอดจำหน่ายหมายเก่า เฉพาะที่มีคุณภาพ mbk
	
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6701 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_kkl, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ กงไกรลาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6702 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_krm, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ คีรีมาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6703 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_tsl, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ tsl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6704 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_bng, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ bng
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6705 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_bdh, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ bdh
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6706 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_bnr, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ bnr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6707 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_bs, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ bs
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6708 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_mgk, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ mgk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6709 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_mst, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ mst
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6710 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_snk, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ snk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6711 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_snl, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ snl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6712 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_ssr, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ ssr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6713 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_swl, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ swl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6714 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_tcn, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ tcn
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN '2002-01-01' AND :date_prev) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6715 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_9_2_mbk, ". // รวม ยอดจำหน่ายหมายเก่า ไม่มีคุณภาพ mbk
	
	
    ///////////////////////////  หมายจับ ใหม่  ////////////////////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `provincial`=:provincial AND `wr_cl_status` IN (1,3)) AS S_13 , ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `provincial`=:provincial AND `station`=6701 AND `wr_cl_status` IN (1,3)) AS S_13_kkl, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก กงไกรลาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `provincial`=:provincial AND `station`=6702 AND `wr_cl_status` IN (1,3)) AS S_13_krm, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก คีรีมาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `provincial`=:provincial AND `station`=6703 AND `wr_cl_status` IN (1,3)) AS S_13_tsl, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก tsl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `provincial`=:provincial AND `station`=6704 AND `wr_cl_status` IN (1,3)) AS S_13_bng, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก bng
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `provincial`=:provincial AND `station`=6705 AND `wr_cl_status` IN (1,3)) AS S_13_bdh, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก bdh
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `provincial`=:provincial AND `station`=6706 AND `wr_cl_status` IN (1,3)) AS S_13_bnr, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก bnr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `provincial`=:provincial AND `station`=6707 AND `wr_cl_status` IN (1,3)) AS S_13_bs, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก bs
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `provincial`=:provincial AND `station`=6708 AND `wr_cl_status` IN (1,3)) AS S_13_mgk, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก mgk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `provincial`=:provincial AND `station`=6709 AND `wr_cl_status` IN (1,3)) AS S_13_mst, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก mst
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `provincial`=:provincial AND `station`=6710 AND `wr_cl_status` IN (1,3)) AS S_13_snk, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก snk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `provincial`=:provincial AND `station`=6711 AND `wr_cl_status` IN (1,3)) AS S_13_snl, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก snl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `provincial`=:provincial AND `station`=6712 AND `wr_cl_status` IN (1,3)) AS S_13_ssr, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก ssr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `provincial`=:provincial AND `station`=6713 AND `wr_cl_status` IN (1,3)) AS S_13_swl, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก swl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `provincial`=:provincial AND `station`=6714 AND `wr_cl_status` IN (1,3)) AS S_13_tcn, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก tcn
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m1) AND `provincial`=:provincial AND `station`=6715 AND `wr_cl_status` IN (1,3)) AS S_13_mbk, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก mbk
	
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_qty`IN(1,2) AND (`wr_cl_status` IN (1,3,2))) AS S_14 , ". // หมายจับใหม่ ภายในเดือน ที่เลือก
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6701 AND `wr_cl_qty`IN(1,2) AND (`wr_cl_status` IN (1,3,2))) AS S_14_kkl, ". // หมายจับใหม่ ภายในเดือน ที่เลือก กงไกรลาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6702 AND `wr_cl_qty`IN(1,2) AND (`wr_cl_status` IN (1,3,2))) AS S_14_krm, ". // หมายจับใหม่ ภายในเดือน ที่เลือก คีรีมาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6703 AND `wr_cl_qty`IN(1,2) AND (`wr_cl_status` IN (1,3,2))) AS S_14_tsl, ". // หมายจับใหม่ ภายในเดือน ที่เลือก tsl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6704 AND `wr_cl_qty`IN(1,2) AND (`wr_cl_status` IN (1,3,2))) AS S_14_bng, ". // หมายจับใหม่ ภายในเดือน ที่เลือก bng
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6705 AND `wr_cl_qty`IN(1,2) AND (`wr_cl_status` IN (1,3,2))) AS S_14_bdh, ". // หมายจับใหม่ ภายในเดือน ที่เลือก bdh
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6706 AND `wr_cl_qty`IN(1,2) AND (`wr_cl_status` IN (1,3,2))) AS S_14_bnr, ". // หมายจับใหม่ ภายในเดือน ที่เลือก bnr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6707 AND `wr_cl_qty`IN(1,2) AND (`wr_cl_status` IN (1,3,2))) AS S_14_bs, ". // หมายจับใหม่ ภายในเดือน ที่เลือก bs
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6708 AND `wr_cl_qty`IN(1,2) AND (`wr_cl_status` IN (1,3,2))) AS S_14_mgk, ". // หมายจับใหม่ ภายในเดือน ที่เลือก mgk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6709 AND `wr_cl_qty`IN(1,2) AND (`wr_cl_status` IN (1,3,2))) AS S_14_mst, ". // หมายจับใหม่ ภายในเดือน ที่เลือก mst
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6710 AND `wr_cl_qty`IN(1,2) AND (`wr_cl_status` IN (1,3,2))) AS S_14_snk, ". // หมายจับใหม่ ภายในเดือน ที่เลือก snk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6711 AND `wr_cl_qty`IN(1,2) AND (`wr_cl_status` IN (1,3,2))) AS S_14_snl, ". // หมายจับใหม่ ภายในเดือน ที่เลือก snl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6712 AND `wr_cl_qty`IN(1,2) AND (`wr_cl_status` IN (1,3,2))) AS S_14_ssr, ". // หมายจับใหม่ ภายในเดือน ที่เลือก ssr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6713 AND `wr_cl_qty`IN(1,2) AND (`wr_cl_status` IN (1,3,2))) AS S_14_swl, ". // หมายจับใหม่ ภายในเดือน ที่เลือก swl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6714 AND `wr_cl_qty`IN(1,2) AND (`wr_cl_status` IN (1,3,2))) AS S_14_tcn, ". // หมายจับใหม่ ภายในเดือน ที่เลือก tcn
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6715 AND `wr_cl_qty`IN(1,2) AND (`wr_cl_status` IN (1,3,2))) AS S_14_mbk, ". // หมายจับใหม่ ภายในเดือน ที่เลือก mbk
	
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `provincial`=:provincial AND `wr_cl_qty`='1' AND (`wr_cl_status` IN (1,3))) AS S_16 , ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `provincial`=:provincial AND `station`=6701 AND `wr_cl_qty`='1' AND (`wr_cl_status` IN (1,3))) AS S_16_kkl, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ กงไกรลาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `provincial`=:provincial AND `station`=6702 AND `wr_cl_qty`='1' AND (`wr_cl_status` IN (1,3))) AS S_16_krm, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ คีรีมาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `provincial`=:provincial AND `station`=6703 AND `wr_cl_qty`='1' AND (`wr_cl_status` IN (1,3))) AS S_16_tsl, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ tsl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `provincial`=:provincial AND `station`=6704 AND `wr_cl_qty`='1' AND (`wr_cl_status` IN (1,3))) AS S_16_bng, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ bng
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `provincial`=:provincial AND `station`=6705 AND `wr_cl_qty`='1' AND (`wr_cl_status` IN (1,3))) AS S_16_bdh, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ bdh
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `provincial`=:provincial AND `station`=6706 AND `wr_cl_qty`='1' AND (`wr_cl_status` IN (1,3))) AS S_16_bnr, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ bnr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `provincial`=:provincial AND `station`=6707 AND `wr_cl_qty`='1' AND (`wr_cl_status` IN (1,3))) AS S_16_bs, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ bs
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `provincial`=:provincial AND `station`=6708 AND `wr_cl_qty`='1' AND (`wr_cl_status` IN (1,3))) AS S_16_mgk, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ mgk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `provincial`=:provincial AND `station`=6709 AND `wr_cl_qty`='1' AND (`wr_cl_status` IN (1,3))) AS S_16_mst, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ mst
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `provincial`=:provincial AND `station`=6710 AND `wr_cl_qty`='1' AND (`wr_cl_status` IN (1,3))) AS S_16_snk, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ snk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `provincial`=:provincial AND `station`=6711 AND `wr_cl_qty`='1' AND (`wr_cl_status` IN (1,3))) AS S_16_snl, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ snl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `provincial`=:provincial AND `station`=6712 AND `wr_cl_qty`='1' AND (`wr_cl_status` IN (1,3))) AS S_16_ssr, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ ssr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `provincial`=:provincial AND `station`=6713 AND `wr_cl_qty`='1' AND (`wr_cl_status` IN (1,3))) AS S_16_swl, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ swl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `provincial`=:provincial AND `station`=6714 AND `wr_cl_qty`='1' AND (`wr_cl_status` IN (1,3))) AS S_16_tcn, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ tcn
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `provincial`=:provincial AND `station`=6715 AND `wr_cl_qty`='1' AND (`wr_cl_status` IN (1,3))) AS S_16_mbk, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก เฉพาะที่มีคุณภาพ mbk
	
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `provincial`=:provincial AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS S_17 , ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ 
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `provincial`=:provincial AND `station`=6701 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS S_17_kkl, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ กงไกรลาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `provincial`=:provincial AND `station`=6702 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS S_17_krm, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ คีรีมาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `provincial`=:provincial AND `station`=6703 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS S_17_tsl, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ tsl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `provincial`=:provincial AND `station`=6704 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS S_17_bng, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ bng
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `provincial`=:provincial AND `station`=6705 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS S_17_bdh, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ bdh
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `provincial`=:provincial AND `station`=6706 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS S_17_bnr, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ bnr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `provincial`=:provincial AND `station`=6707 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS S_17_bs, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ bs
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `provincial`=:provincial AND `station`=6708 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS S_17_mgk, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ mgk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `provincial`=:provincial AND `station`=6709 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS S_17_mst, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ mst
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `provincial`=:provincial AND `station`=6710 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS S_17_snk, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ snk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `provincial`=:provincial AND `station`=6711 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS S_17_snl, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ snl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `provincial`=:provincial AND `station`=6712 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS S_17_ssr, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ ssr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `provincial`=:provincial AND `station`=6713 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS S_17_swl, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ swl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `provincial`=:provincial AND `station`=6714 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS S_17_tcn, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ tcn
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` >= :date_next AND `wr_cl_date` < :m2) AND `provincial`=:provincial AND `station`=6715 AND `wr_cl_qty`=2 AND (`wr_cl_status` IN (1,3))) AS S_17_mbk, ". // หมายจับใหม่ ทั้งหมด ตั้งแต่ 1 ต.ค.2565 มาถึง ก่อนเดือนปัจจุบันที่เลือก // หมายจับใหม่ ที่ไม่มีคุณภาพ mbk
	
	
    ///////// จำหน่าย หมายจับใหม่ ตั้งแต่ 1 ต.ค.2565 ถึง ก่อนเดือนปัจจุบันที่เลือก  ///////////////
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS S_18 , ". // จับกุม เฉพาะหมายจับใหม่
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6701 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS S_18_kkl, ". // จับกุม เฉพาะหมายจับใหม่ กงไกรลาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6702 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS S_18_krm, ". // จับกุม เฉพาะหมายจับใหม่ คีรีมาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6703 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS S_18_tsl, ". // จับกุม เฉพาะหมายจับใหม่ tsl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6704 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS S_18_bng, ". // จับกุม เฉพาะหมายจับใหม่ bng
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6705 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS S_18_bdh, ". // จับกุม เฉพาะหมายจับใหม่ bdh
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6706 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS S_18_bnr, ". // จับกุม เฉพาะหมายจับใหม่ bnr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6707 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS S_18_bs, ". // จับกุม เฉพาะหมายจับใหม่ bs
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6708 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS S_18_mgk, ". // จับกุม เฉพาะหมายจับใหม่ mgk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6709 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS S_18_mst, ". // จับกุม เฉพาะหมายจับใหม่ mst
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6710 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS S_18_snk, ". // จับกุม เฉพาะหมายจับใหม่ snk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6711 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS S_18_snl, ". // จับกุม เฉพาะหมายจับใหม่ snl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6712 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS S_18_ssr, ". // จับกุม เฉพาะหมายจับใหม่ ssr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6713 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS S_18_swl, ". // จับกุม เฉพาะหมายจับใหม่ swl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6714 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS S_18_tcn, ". // จับกุม เฉพาะหมายจับใหม่ tcn
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6715 AND `wr_cl_status`=2 AND `wr_cl_qty`=1) AS S_18_mbk, ". // จับกุม เฉพาะหมายจับใหม่ mbk
	
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`='5') AS S_19 , ". // ขาดอายุความ เฉพาะหมายจับใหม่
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6701 AND `wr_cl_status`='5') AS S_19_kkl, ". // ขาดอายุความ เฉพาะหมายจับใหม่ กงไกรลาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6702 AND `wr_cl_status`='5') AS S_19_krm, ". // ขาดอายุความ เฉพาะหมายจับใหม่ คีรีมาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6703 AND `wr_cl_status`='5') AS S_19_tsl, ". // ขาดอายุความ เฉพาะหมายจับใหม่ tsl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6704 AND `wr_cl_status`='5') AS S_19_bng, ". // ขาดอายุความ เฉพาะหมายจับใหม่ bng
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6705 AND `wr_cl_status`='5') AS S_19_bdh, ". // ขาดอายุความ เฉพาะหมายจับใหม่ bdh
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6706 AND `wr_cl_status`='5') AS S_19_bnr, ". // ขาดอายุความ เฉพาะหมายจับใหม่ bnr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6707 AND `wr_cl_status`='5') AS S_19_bs, ". // ขาดอายุความ เฉพาะหมายจับใหม่ bs
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6708 AND `wr_cl_status`='5') AS S_19_mgk, ". // ขาดอายุความ เฉพาะหมายจับใหม่ mgk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6709 AND `wr_cl_status`='5') AS S_19_mst, ". // ขาดอายุความ เฉพาะหมายจับใหม่ mst
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6710 AND `wr_cl_status`='5') AS S_19_snk, ". // ขาดอายุความ เฉพาะหมายจับใหม่ snk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6711 AND `wr_cl_status`='5') AS S_19_snl, ". // ขาดอายุความ เฉพาะหมายจับใหม่ snl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6712 AND `wr_cl_status`='5') AS S_19_ssr, ". // ขาดอายุความ เฉพาะหมายจับใหม่ ssr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6713 AND `wr_cl_status`='5') AS S_19_swl, ". // ขาดอายุความ เฉพาะหมายจับใหม่ swl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6714 AND `wr_cl_status`='5') AS S_19_tcn, ". // ขาดอายุความ เฉพาะหมายจับใหม่ tcn
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6715 AND `wr_cl_status`='5') AS S_19_mbk, ". // ขาดอายุความ เฉพาะหมายจับใหม่ mbk
	
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`='6') AS S_20 , " . // ตาย เฉพาะหมายจับใหม่
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6701 AND `wr_cl_status`='6') AS S_20_kkl, " . // ตาย เฉพาะหมายจับใหม่ กงไกรลาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6702 AND `wr_cl_status`='6') AS S_20_krm, " . // ตาย เฉพาะหมายจับใหม่ คีรีมาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6703 AND `wr_cl_status`='6') AS S_20_tsl, " . // ตาย เฉพาะหมายจับใหม่ tsl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6704 AND `wr_cl_status`='6') AS S_20_bng, " . // ตาย เฉพาะหมายจับใหม่ bng
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6705 AND `wr_cl_status`='6') AS S_20_bdh, " . // ตาย เฉพาะหมายจับใหม่ bdh
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6706 AND `wr_cl_status`='6') AS S_20_bnr, " . // ตาย เฉพาะหมายจับใหม่ bnr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6707 AND `wr_cl_status`='6') AS S_20_bs, " . // ตาย เฉพาะหมายจับใหม่ bs
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6708 AND `wr_cl_status`='6') AS S_20_mgk, " . // ตาย เฉพาะหมายจับใหม่ mgk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6709 AND `wr_cl_status`='6') AS S_20_mst, " . // ตาย เฉพาะหมายจับใหม่ mst
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6710 AND `wr_cl_status`='6') AS S_20_snk, " . // ตาย เฉพาะหมายจับใหม่ snk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6711 AND `wr_cl_status`='6') AS S_20_snl, " . // ตาย เฉพาะหมายจับใหม่ snl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6712 AND `wr_cl_status`='6') AS S_20_ssr, " . // ตาย เฉพาะหมายจับใหม่ ssr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6713 AND `wr_cl_status`='6') AS S_20_swl, " . // ตาย เฉพาะหมายจับใหม่ swl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6714 AND `wr_cl_status`='6') AS S_20_tcn, " . // ตาย เฉพาะหมายจับใหม่ tcn
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6715 AND `wr_cl_status`='6') AS S_20_mbk, " . // ตาย เฉพาะหมายจับใหม่ mbk
	
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`='3') AS S_21 , " . // อายัด เฉพาะหมายจับใหม่ 
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6701 AND `wr_cl_status`='3') AS S_21_kkl, " . // อายัด เฉพาะหมายจับใหม่ กงไกรลาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6702 AND `wr_cl_status`='3') AS S_21_krm, " . // อายัด เฉพาะหมายจับใหม่ krm
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6703 AND `wr_cl_status`='3') AS S_21_tsl, " . // อายัด เฉพาะหมายจับใหม่ tsl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6704 AND `wr_cl_status`='3') AS S_21_bng, " . // อายัด เฉพาะหมายจับใหม่ bng
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6705 AND `wr_cl_status`='3') AS S_21_bdh, " . // อายัด เฉพาะหมายจับใหม่ bdh
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6706 AND `wr_cl_status`='3') AS S_21_bnr, " . // อายัด เฉพาะหมายจับใหม่ bnr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6707 AND `wr_cl_status`='3') AS S_21_bs, " . // อายัด เฉพาะหมายจับใหม่ bs
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6708 AND `wr_cl_status`='3') AS S_21_mgk, " . // อายัด เฉพาะหมายจับใหม่ mgk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6709 AND `wr_cl_status`='3') AS S_21_mst, " . // อายัด เฉพาะหมายจับใหม่ mst
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6710 AND `wr_cl_status`='3') AS S_21_snk, " . // อายัด เฉพาะหมายจับใหม่ snk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6711 AND `wr_cl_status`='3') AS S_21_snl, " . // อายัด เฉพาะหมายจับใหม่ snl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6712 AND `wr_cl_status`='3') AS S_21_ssr, " . // อายัด เฉพาะหมายจับใหม่ ssr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6713 AND `wr_cl_status`='3') AS S_21_swl, " . // อายัด เฉพาะหมายจับใหม่ swl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6714 AND `wr_cl_status`='3') AS S_21_tcn, " . // อายัด เฉพาะหมายจับใหม่ tcn
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6715 AND `wr_cl_status`='3') AS S_21_mbk, " . // อายัด เฉพาะหมายจับใหม่ mbk
	
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`='7') AS S_22 , " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6701 AND `wr_cl_status`='7') AS S_22_kkl, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่ กงไกรลาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6702 AND `wr_cl_status`='7') AS S_22_krm, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่ krm
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6703 AND `wr_cl_status`='7') AS S_22_tsl, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่ tsl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6704 AND `wr_cl_status`='7') AS S_22_bng, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่ bng
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6705 AND `wr_cl_status`='7') AS S_22_bdh, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่ bdh
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6706 AND `wr_cl_status`='7') AS S_22_bnr, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่ bnr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6707 AND `wr_cl_status`='7') AS S_22_bs, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่ bs
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6708 AND `wr_cl_status`='7') AS S_22_mgk, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่ mgk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6709 AND `wr_cl_status`='7') AS S_22_mst, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่ mst
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6710 AND `wr_cl_status`='7') AS S_22_snk, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่ snk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6711 AND `wr_cl_status`='7') AS S_22_snl, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่ snl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6712 AND `wr_cl_status`='7') AS S_22_ssr, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่ ssr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6713 AND `wr_cl_status`='7') AS S_22_swl, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่ swl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6714 AND `wr_cl_status`='7') AS S_22_tcn, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่ tcn
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6715 AND `wr_cl_status`='7') AS S_22_mbk, " . // จำหน่ายอื่น ๆ เฉพาะหมายจับใหม่ mbk
	
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=1) AS S_23, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6701 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=1) AS S_23_kkl, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ กงไกรลาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6702 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=1) AS S_23_krm, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  krm
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6703 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=1) AS S_23_tsl, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ tsl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6704 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=1) AS S_23_bng, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  bng
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6705 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=1) AS S_23_bdh, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  bdh
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6706 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=1) AS S_23_bnr, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  bnr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6707 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=1) AS S_23_bs, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  bs
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6708 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=1) AS S_23_mgk, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  mgk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6709 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=1) AS S_23_mst, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  mst
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6710 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=1) AS S_23_snk, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  snk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6711 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=1) AS S_23_snl, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  snl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6712 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=1) AS S_23_ssr, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  ssr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6713 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=1) AS S_23_swl, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ swl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6714 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=1) AS S_23_tcn, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ tcn
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6715 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=1) AS S_23_mbk, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  mbk
	
	
    "(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25,". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6701 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_kkl, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ กงไกรลาศ
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6702 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_krm, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ krm
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6703 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_tsl, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ tsl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6704 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_bng, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ bng
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6705 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_bdh, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ bdh
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6706 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_bnr, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ bnr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6707 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_bs, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ bs
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6708 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_mgk, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ mgk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6709 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_mst, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ mst
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6710 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_snk, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ snk
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6711 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_snl, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  snl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6712 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_ssr, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  ssr
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6713 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_swl, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  swl
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6714 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_tcn, ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ  tcn
	"(SELECT COUNT(*) FROM wm_tb_warrant WHERE (`wr_cl_date` BETWEEN :date_next AND :m2) AND (`wr_cl_date_finish` BETWEEN :m1 AND :m2) AND `provincial`=:provincial AND `station`=6715 AND `wr_cl_status`IN(2,4,5,6) AND `wr_cl_qty`=2) AS S_25_mbk ". // รวม ยอดจำหน่ายหมายใหม่ เฉพาะที่มีคุณภาพ mbk
	
    "";
                      
try {
    $stmt = $pdo->prepare($sql);
    // Bind parameter values
    $stmt->bindParam(':m1', $m1);
    $stmt->bindParam(':m2', $m2);
    $stmt->bindParam(':provincial', $provincial);
    $stmt->bindParam(':date_next', $date_next);
    $stmt->bindParam(':date_prev', $date_prev);
    
    // Execute the prepared statement
    $stmt->execute();
    
	
    // Fetch the results if needed
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Process the results as required
    foreach ($results as $row) {
                      
	$numerator = 10;
    $denominator = 0;

    if ($denominator != 0) {
        $result = $numerator / $denominator;
		} else {
		$results = null; // Or any other appropriate value or action.
    }
//echo $sql;
    // ยอดคงเหลือหมายจับเก่า
    $S_10 = $row['S_2']-$row['S_9'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)
    $S_10_2 = $row['S_3']-$row['S_9_2'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)
    $S_15 = $row['S_13']+$row['S_14'];     //  
    $S_27 = $row['S_1']+$S_15;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
    $S_28 = $row['S_9']+$row['S_23']; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
	// ยอดคงเหลือหมายจับเก่า กงไกรลาศ
    $S_10_kkl = $row['S_2_kkl']-$row['S_9_kkl'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)
    $S_10_2_kkl = $row['S_3_kkl']-$row['S_9_2_kkl'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)
    $S_15_kkl = $row['S_13_kkl']+$row['S_14_kkl'];     //  
    $S_27_kkl = $row['S_1_kkl']+$S_15_kkl;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
    $S_28_kkl = $row['S_9_kkl']+$row['S_23_kkl']; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
	// ยอดคงเหลือหมายจับเก่า คีรีมาศ
    $S_10_krm = $row['S_2_krm']-$row['S_9_krm'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)
    $S_10_2_krm = $row['S_3_krm']-$row['S_9_2_krm'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)
    $S_15_krm = $row['S_13_krm']+$row['S_14_krm'];     //  
    $S_27_krm = $row['S_1_krm']+$S_15_krm;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
    $S_28_krm = $row['S_9_krm']+$row['S_23_krm']; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
	// ยอดคงเหลือหมายจับเก่า ทุ่งเสลี่ยม
    $S_10_tsl = $row['S_2_tsl']-$row['S_9_tsl'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)
    $S_10_2_tsl = $row['S_3_tsl']-$row['S_9_2_tsl'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)
    $S_15_tsl = $row['S_13_tsl']+$row['S_14_tsl'];     //  
    $S_27_tsl = $row['S_1_tsl']+$S_15_tsl;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
    $S_28_tsl = $row['S_9_tsl']+$row['S_23_tsl']; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
	// ยอดคงเหลือหมายจับเก่า บ้านแก่ง
    $S_10_bng = $row['S_2_bng']-$row['S_9_bng'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)
    $S_10_2_bng = $row['S_3_bng']-$row['S_9_2_bng'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)
    $S_15_bng = $row['S_13_bng']+$row['S_14_bng'];     //  
    $S_27_bng = $row['S_1_bng']+$S_15_bng;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
    $S_28_bng = $row['S_9_bng']+$row['S_23_bng']; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
	// ยอดคงเหลือหมายจับเก่า บ้านด่านลานหอย
    $S_10_bdh = $row['S_2_bdh']-$row['S_9_bdh'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)
    $S_10_2_bdh = $row['S_3_bdh']-$row['S_9_2_bdh'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)
    $S_15_bdh = $row['S_13_bdh']+$row['S_14_bdh'];     //  
    $S_27_bdh = $row['S_1_bdh']+$S_15_bdh;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
    $S_28_bdh = $row['S_9_bdh']+$row['S_23_bdh']; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
	// ยอดคงเหลือหมายจับเก่า บ้านไร่
    $S_10_bnr = $row['S_2_bnr']-$row['S_9_bnr'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)
    $S_10_2_bnr = $row['S_3_bnr']-$row['S_9_2_bnr'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)
    $S_15_bnr = $row['S_13_bnr']+$row['S_14_bnr'];     //  
    $S_27_bnr = $row['S_1_bnr']+$S_15_bnr;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
    $S_28_bnr = $row['S_9_bnr']+$row['S_23_bnr']; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
	// ยอดคงเหลือหมายจับเก่า บ้านสวน
    $S_10_bs = $row['S_2_bs']-$row['S_9_bs'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)
    $S_10_2_bs = $row['S_3_bs']-$row['S_9_2_bs'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)
    $S_15_bs = $row['S_13_bs']+$row['S_14_bs'];     //  
    $S_27_bs = $row['S_1_bs']+$S_15_bs;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
    $S_28_bs = $row['S_9_bs']+$row['S_23_bs']; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
	// ยอดคงเหลือหมายจับเก่า เมืองเก่า
    $S_10_mgk = $row['S_2_mgk']-$row['S_9_mgk'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)
    $S_10_2_mgk = $row['S_3_mgk']-$row['S_9_2_mgk'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)
    $S_15_mgk = $row['S_13_mgk']+$row['S_14_mgk'];     //  
    $S_27_mgk = $row['S_1_mgk']+$S_15_mgk;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
    $S_28_mgk = $row['S_9_mgk']+$row['S_23_mgk']; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
	// ยอดคงเหลือหมายจับเก่า เมืองสุโขทัย
    $S_10_mst = $row['S_2_mst']-$row['S_9_mst'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)
    $S_10_2_mst = $row['S_3_mst']-$row['S_9_2_mst'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)
    $S_15_mst = $row['S_13_mst']+$row['S_14_mst'];     //  
    $S_27_mst = $row['S_1_mst']+$S_15_mst;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
    $S_28_mst = $row['S_9_mst']+$row['S_23_mst']; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
	// ยอดคงเหลือหมายจับเก่า ศรีนคร
    $S_10_snk = $row['S_2_snk']-$row['S_9_snk'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)
    $S_10_2_snk = $row['S_3_snk']-$row['S_9_2_snk'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)
    $S_15_snk = $row['S_13_snk']+$row['S_14_snk'];     //  
    $S_27_snk = $row['S_1_snk']+$S_15_snk;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
    $S_28_snk = $row['S_9_snk']+$row['S_23_snk']; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
	// ยอดคงเหลือหมายจับเก่า ศรีสัชนาลัย
    $S_10_snl = $row['S_2_snl']-$row['S_9_snl'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)
    $S_10_2_snl = $row['S_3_snl']-$row['S_9_2_snl'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)
    $S_15_snl = $row['S_13_snl']+$row['S_14_snl'];     //  
    $S_27_snl = $row['S_1_snl']+$S_15_snl;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
    $S_28_snl = $row['S_9_snl']+$row['S_23_snl']; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
	// ยอดคงเหลือหมายจับเก่า ศรีสำโรง
    $S_10_ssr = $row['S_2_ssr']-$row['S_9_ssr'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)
    $S_10_2_ssr = $row['S_3_ssr']-$row['S_9_2_ssr'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)
    $S_15_ssr = $row['S_13_ssr']+$row['S_14_ssr'];     //  
    $S_27_ssr = $row['S_1_ssr']+$S_15_ssr;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
    $S_28_ssr = $row['S_9_ssr']+$row['S_23_ssr']; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
	// ยอดคงเหลือหมายจับเก่า สวรรคโลก
    $S_10_swl = $row['S_2_swl']-$row['S_9_swl'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)
    $S_10_2_swl = $row['S_3_swl']-$row['S_9_2_swl'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)
    $S_15_swl = $row['S_13_swl']+$row['S_14_swl'];     //  
    $S_27_swl = $row['S_1_swl']+$S_15_swl;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
    $S_28_swl = $row['S_9_swl']+$row['S_23_swl']; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
	// ยอดคงเหลือหมายจับเก่า ท่าฉนวน
    $S_10_tcn = $row['S_2_tcn']-$row['S_9_tcn'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)
    $S_10_2_tcn = $row['S_3_tcn']-$row['S_9_2_tcn'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)
    $S_15_tcn = $row['S_13_tcn']+$row['S_14_tcn'];     //  
    $S_27_tcn = $row['S_1_tcn']+$S_15_tcn;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
    $S_28_tcn = $row['S_9_tcn']+$row['S_23_tcn']; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
	// ยอดคงเหลือหมายจับเก่า เมืองบางขลัง
    $S_10_mbk = $row['S_2_mbk']-$row['S_9_mbk'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)
    $S_10_2_mbk = $row['S_3_mbk']-$row['S_9_2_mbk'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)
    $S_15_mbk = $row['S_13_mbk']+$row['S_14_mbk'];     //  
    $S_27_mbk = $row['S_1_mbk']+$S_15_mbk;     //  รวมหมายจับ ทั้งหมด ภายในเดือน S_27
    $S_28_mbk = $row['S_9_mbk']+$row['S_23_mbk']; // รวม จำหน่ายหมายจับ ทั้งหมด S_28
                      
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา
if($row['S_13'] >$sum2){
    $S_13 = $row['S_13']-$sum2;
}else{
    $S_13 = $row['S_13'];
}
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา กงไกรลาศ
if($row['S_13_kkl'] >$sum2){
    $S_13_kkl = $row['S_13_kkl']-$sum2;
}else{
    $S_13_kkl = $row['S_13_kkl'];
}
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา คีรีมาศ
if($row['S_13_krm'] >$sum2){
    $S_13_krm = $row['S_13_krm']-$sum2;
}else{
    $S_13_krm = $row['S_13_krm'];
}
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา ทุ่งเสลี่ยม
if($row['S_13_tsl'] >$sum2){
    $S_13_tsl = $row['S_13_tsl']-$sum2;
}else{
    $S_13_tsl = $row['S_13_tsl'];
}
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา บ้านแก่ง
if($row['S_13_bng'] >$sum2){
    $S_13_bng = $row['S_13_bng']-$sum2;
}else{
    $S_13_bng = $row['S_13_bng'];
}
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา บ้านด่านลานหอย
if($row['S_13_bdh'] >$sum2){
    $S_13_bdh = $row['S_13_bdh']-$sum2;
}else{
    $S_13_bdh = $row['S_13_bdh'];
}
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา บ้านไร่
if($row['S_13_bnr'] >$sum2){
    $S_13_bnr = $row['S_13_bnr']-$sum2;
}else{
    $S_13_bnr = $row['S_13_bnr'];
}
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา บ้านสวน
if($row['S_13_bs'] >$sum2){
    $S_13_bs = $row['S_13_bs']-$sum2;
}else{
    $S_13_bs = $row['S_13_bs'];
}
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา เมืองเก่า
if($row['S_13_mgk'] >$sum2){
    $S_13_mgk = $row['S_13_mgk']-$sum2;
}else{
    $S_13_mgk = $row['S_13_mgk'];
}
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา เมืองสุโขทัย
if($row['S_13_mst'] >$sum2){
    $S_13_mst = $row['S_13_mst']-$sum2;
}else{
    $S_13_mst = $row['S_13_mst'];
}
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา ศรีนคร
if($row['S_13_snk'] >$sum2){
    $S_13_snk = $row['S_13_snk']-$sum2;
}else{
    $S_13_snk = $row['S_13_snk'];
}
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา ศรีสัชนาลัย
if($row['S_13_snl'] >$sum2){
    $S_13_snl = $row['S_13_snl']-$sum2;
}else{
    $S_13_snl = $row['S_13_snl'];
}
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา ศรีสำโรง
if($row['S_13_ssr'] >$sum2){
    $S_13_ssr = $row['S_13_ssr']-$sum2;
}else{
    $S_13_ssr = $row['S_13_ssr'];
}
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา สวรรคโลก
if($row['S_13_swl'] >$sum2){
    $S_13_swl = $row['S_13_swl']-$sum2;
}else{
    $S_13_swl = $row['S_13_swl'];
}
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา ท่าฉนวน
if($row['S_13_tcn'] >$sum2){
    $S_13_tcn = $row['S_13_tcn']-$sum2;
}else{
    $S_13_tcn = $row['S_13_tcn'];
}
// ยอดหมายจับใหม่ - ยอดเดือนที่ผ่านมา เมืองบางขลัง
if($row['S_13_mbk'] >$sum2){
    $S_13_mbk = $row['S_13_mbk']-$sum2;
}else{
    $S_13_mbk = $row['S_13_mbk'];
}
		
                   
// ยอดหมายจับใหม่ มีคุณภาพ
if($row['S_16'] > $sum2){
    $S_16 = $row['S_16']-$sum2;
}else{
    $S_16 = $row['S_16'];
}
// ยอดหมายจับใหม่ มีคุณภาพ กงไกรลาศ
if($row['S_16_kkl'] > $sum2){
    $S_16_kkl = $row['S_16_kkl']-$sum2;
}else{
    $S_16_kkl = $row['S_16_kkl'];
}
// ยอดหมายจับใหม่ มีคุณภาพ คีรีมาศ
if($row['S_16_krm'] > $sum2){
    $S_16_krm = $row['S_16_krm']-$sum2;
}else{
    $S_16_krm = $row['S_16_krm'];
}
// ยอดหมายจับใหม่ มีคุณภาพ ทุ่งเสลี่ยม
if($row['S_16_tsl'] > $sum2){
    $S_16_tsl = $row['S_16_tsl']-$sum2;
}else{
    $S_16_tsl = $row['S_16_tsl'];
}
// ยอดหมายจับใหม่ มีคุณภาพ บ้านแก่ง
if($row['S_16_bng'] > $sum2){
    $S_16_bng = $row['S_16_bng']-$sum2;
}else{
    $S_16_bng = $row['S_16_bng'];
}
// ยอดหมายจับใหม่ มีคุณภาพ บ้านด่านลานหอย
if($row['S_16_bdh'] > $sum2){
    $S_16_bdh = $row['S_16_bdh']-$sum2;
}else{
    $S_16_bdh = $row['S_16_bdh'];
}
// ยอดหมายจับใหม่ มีคุณภาพ บ้านไร่
if($row['S_16_bnr'] > $sum2){
    $S_16_bnr = $row['S_16_bnr']-$sum2;
}else{
    $S_16_bnr = $row['S_16_bnr'];
}
// ยอดหมายจับใหม่ มีคุณภาพ บ้านสวน
if($row['S_16_bs'] > $sum2){
    $S_16_bs = $row['S_16_bs']-$sum2;
}else{
    $S_16_bs = $row['S_16_bs'];
}
// ยอดหมายจับใหม่ มีคุณภาพ เมืองเก่า
if($row['S_16_mgk'] > $sum2){
    $S_16_mgk = $row['S_16_mgk']-$sum2;
}else{
    $S_16_mgk = $row['S_16_mgk'];
}
// ยอดหมายจับใหม่ มีคุณภาพ เมืองสุโขทัย
if($row['S_16_mst'] > $sum2){
    $S_16_mst = $row['S_16_mst']-$sum2;
}else{
    $S_16_mst = $row['S_16_mst'];
}
// ยอดหมายจับใหม่ มีคุณภาพ ศรีนคร
if($row['S_16_snk'] > $sum2){
    $S_16_snk = $row['S_16_snk']-$sum2;
}else{
    $S_16_snk = $row['S_16_snk'];
}
// ยอดหมายจับใหม่ มีคุณภาพ ศรีสัชนาลัย
if($row['S_16_snl'] > $sum2){
    $S_16_snl = $row['S_16_snl']-$sum2;
}else{
    $S_16_snl = $row['S_16_snl'];
}
// ยอดหมายจับใหม่ มีคุณภาพ ศรีสำโรง
if($row['S_16_ssr'] > $sum2){
    $S_16_ssr = $row['S_16_ssr']-$sum2;
}else{
    $S_16_ssr = $row['S_16_ssr'];
}
// ยอดหมายจับใหม่ มีคุณภาพ สวรรคโลก
if($row['S_16_swl'] > $sum2){
    $S_16_swl = $row['S_16_swl']-$sum2;
}else{
    $S_16_swl = $row['S_16_swl'];
}
// ยอดหมายจับใหม่ มีคุณภาพ ท่าฉนวน
if($row['S_16_tcn'] > $sum2){
    $S_16_tcn = $row['S_16_tcn']-$sum2;
}else{
    $S_16_tcn = $row['S_16_tcn'];
}
// ยอดหมายจับใหม่ มีคุณภาพ เมืองบางขลัง
if($row['S_16_mbk'] > $sum2){
    $S_16_mbk = $row['S_16_mbk']-$sum2;
}else{
    $S_16_mbk = $row['S_16_mbk'];
}

      
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ
     $S_24 = $S_16-$row['S_23'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24 > $sum2){
        $S_24_1 = $S_24-$sum2;
}else{
        $S_24_1 = $S_24;
    }   
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ กงไกรลาศ
     $S_24_kkl = $S_16-$row['S_23_kkl'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_kkl > $sum2){
        $S_24_1_kkl = $S_24_kkl-$sum2;
}else{
        $S_24_1_kkl = $S_24_kkl;
    }
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ กงไกรลาศ
     $S_24_kkl = $S_16_kkl-$row['S_23_kkl'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_kkl > $sum2){
        $S_24_1_kkl = $S_24_kkl-$sum2;
}else{
        $S_24_1_kkl = $S_24_kkl;
    }
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ คีรีมาศ
     $S_24_krm = $S_16_krm-$row['S_23_krm'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_krm > $sum2){
        $S_24_1_krm = $S_24_krm-$sum2;
}else{
        $S_24_1_krm = $S_24_krm;
    }
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ ทุ่งเสลี่ยม
     $S_24_tsl = $S_16_tsl-$row['S_23_tsl'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_tsl > $sum2){
        $S_24_1_tsl = $S_24_tsl-$sum2;
}else{
        $S_24_1_tsl = $S_24_tsl;
    }
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ บ้านแก่ง
     $S_24_bng = $S_16_bng-$row['S_23_bng'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_bng > $sum2){
        $S_24_1_bng = $S_24_bng-$sum2;
}else{
        $S_24_1_bng = $S_24_bng;
    }
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ บ้านด่านลานหอย
     $S_24_bdh = $S_16_bdh-$row['S_23_bdh'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_bdh > $sum2){
        $S_24_1_bdh = $S_24_bdh-$sum2;
}else{
        $S_24_1_bdh = $S_24_bdh;
    }
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ บ้านไร่
     $S_24_bnr = $S_16_bnr-$row['S_23_bnr'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_bnr > $sum2){
        $S_24_1_bnr = $S_24_bnr-$sum2;
}else{
        $S_24_1_bnr = $S_24_bnr;
    }
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ บ้านสวน
     $S_24_bs = $S_16_bs-$row['S_23_bs'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_bs > $sum2){
        $S_24_1_bs = $S_24_bs-$sum2;
}else{
        $S_24_1_bs = $S_24_bs;
    }
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ เมืองเก่า
     $S_24_mgk = $S_16_mgk-$row['S_23_mgk'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_mgk > $sum2){
        $S_24_1_mgk = $S_24_mgk-$sum2;
}else{
        $S_24_1_mgk = $S_24_mgk;
    }
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ เมืองสุโขทัย
     $S_24_mst = $S_16_mst-$row['S_23_mst'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_mst > $sum2){
        $S_24_1_mst = $S_24_mst-$sum2;
}else{
        $S_24_1_mst = $S_24_mst;
    }
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ ศรีนคร
     $S_24_snk = $S_16_snk-$row['S_23_snk'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_snk > $sum2){
        $S_24_1_snk = $S_24_snk-$sum2;
}else{
        $S_24_1_snk = $S_24_snk;
    }
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ ศรีสัชนาลัย
     $S_24_snl = $S_16_snl-$row['S_23_snl'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_snl > $sum2){
        $S_24_1_snl = $S_24_snl-$sum2;
}else{
        $S_24_1_snl = $S_24_snl;
    }
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ ศรีสำโรง
     $S_24_ssr = $S_16_ssr-$row['S_23_ssr'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_ssr > $sum2){
        $S_24_1_ssr = $S_24_ssr-$sum2;
}else{
        $S_24_1_ssr = $S_24_ssr;
    }
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ สวรรคโลก
     $S_24_swl = $S_16_swl-$row['S_23_swl'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_swl > $sum2){
        $S_24_1_swl = $S_24_swl-$sum2;
}else{
        $S_24_1_swl = $S_24_swl;
    }
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ ท่าฉนวน
     $S_24_tcn = $S_16_tcn-$row['S_23_tcn'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_tcn > $sum2){
        $S_24_1_tcn = $S_24_tcn-$sum2;
}else{
        $S_24_1_tcn = $S_24_tcn;
    }
// ยอดหมายจับใหม่ มีคุณภาพ คงเหลือ เมืองบางขลัง
     $S_24_mbk = $S_16_mbk-$row['S_23_mbk'];     // นำยอดหมายจับ - ยอดจำหน่าย (มีุคุณภาพ)                 
if($S_24_mbk > $sum2){
        $S_24_1_mbk = $S_24_mbk-$sum2;
}else{
        $S_24_1_mbk = $S_24_mbk;
    }
		

// ยอดรวม
$OldPersen = 0;
$NewPersen = 0;
$AllPersen = 0;

if ($row['S_01'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen = ($row['S_9'] / $row['S_01']) * 100;
	$OldPersen = round($OldPersen,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen = ($row['S_23'] / $row['S_01']) * 100;
	$NewPersen = round($NewPersen,2);  // จุดทศนิยม 2 ตำแหน่ง
}

if ($S_27 != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen = ($S_28 / $S_27) * 100;
	$AllPersen = round($AllPersen,2);  // จุดทศนิยม 2 ตำแหน่ง  
}		
// ยอดรวม กงไกรลาศ
$OldPersen_kkl = 0;
$NewPersen_kkl = 0;
$AllPersen_kkl = 0;

if ($row['S_01_kkl'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_kkl = ($row['S_9_kkl'] / $row['S_01_kkl']) * 100;
	$OldPersen_kkl = round($OldPersen_kkl,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_kkl = ($row['S_23_kkl'] / $row['S_01_kkl']) * 100;
	$NewPersen_kkl = round($NewPersen_kkl,2);  // จุดทศนิยม 2 ตำแหน่ง
}

if ($S_27_kkl != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_kkl = ($S_28_kkl / $S_27_kkl) * 100;
	$AllPersen_kkl = round($AllPersen_kkl,2);  // จุดทศนิยม 2 ตำแหน่ง  
}

// ยอดรวม คีรีมาศ
$OldPersen_krm = 0;
$NewPersen_krm = 0;
$AllPersen_krm = 0;

if ($row['S_01_krm'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_krm = ($row['S_9_krm'] / $row['S_01_krm']) * 100;
	$OldPersen_krm = round($OldPersen_krm,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_krm = ($row['S_23_krm'] / $row['S_01_krm']) * 100;
	$NewPersen_krm = round($NewPersen_krm,2);  // จุดทศนิยม 2 ตำแหน่ง
}

if ($S_27_krm != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_krm = ($S_28_krm / $S_27_krm) * 100;
	$AllPersen_krm = round($AllPersen_krm,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
// ยอดรวม ทุ่งเสลี่ยม
$OldPersen_tsl = 0;
$NewPersen_tsl = 0;
$AllPersen_tsl = 0;

if ($row['S_01_tsl'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_tsl = ($row['S_9_tsl'] / $row['S_01_tsl']) * 100;
	$OldPersen_tsl = round($OldPersen_tsl,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_tsl = ($row['S_23_tsl'] / $row['S_01_tsl']) * 100;
	$NewPersen_tsl = round($NewPersen_tsl,2);  // จุดทศนิยม 2 ตำแหน่ง
}

if ($S_27_tsl != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_tsl = ($S_28_tsl / $S_27_tsl) * 100;
	$AllPersen_tsl = round($AllPersen_tsl,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
// ยอดรวม บ้านแก่ง
$OldPersen_bng = 0;
$NewPersen_bng = 0;
$AllPersen_bng = 0;

if ($row['S_01_bng'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_bng = ($row['S_9_bng'] / $row['S_01_bng']) * 100;
	$OldPersen_bng = round($OldPersen_bng,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_bng = ($row['S_23_bng'] / $row['S_01_bng']) * 100;
	$NewPersen_bng = round($NewPersen_bng,2);  // จุดทศนิยม 2 ตำแหน่ง
}

if ($S_27_bng != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_bng = ($S_28_bng / $S_27_bng) * 100;
	$AllPersen_bng = round($AllPersen_bng,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
// ยอดรวม บ้านด่านลานหอย
$OldPersen_bdh = 0;
$NewPersen_bdh = 0;
$AllPersen_bdh = 0;

if ($row['S_01_bdh'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_bdh = ($row['S_9_bdh'] / $row['S_01_bdh']) * 100;
	$OldPersen_bdh = round($OldPersen_bdh,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_bdh = ($row['S_23_bdh'] / $row['S_01_bdh']) * 100;
	$NewPersen_bdh = round($NewPersen_bdh,2);  // จุดทศนิยม 2 ตำแหน่ง
}

if ($S_27_bdh != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_bdh = ($S_28_bdh / $S_27_bdh) * 100;
	$AllPersen_bdh = round($AllPersen_bdh,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
// ยอดรวม บ้านไร่
$OldPersen_bnr = 0;
$NewPersen_bnr = 0;
$AllPersen_bnr = 0;

if ($row['S_01_bnr'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_bnr = ($row['S_9_bnr'] / $row['S_01_bnr']) * 100;
	$OldPersen_bnr = round($OldPersen_bnr,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_bnr = ($row['S_23_bnr'] / $row['S_01_bnr']) * 100;
	$NewPersen_bnr = round($NewPersen_bnr,2);  // จุดทศนิยม 2 ตำแหน่ง
}

if ($S_27_bnr != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_bnr = ($S_28_bnr / $S_27_bnr) * 100;
	$AllPersen_bnr = round($AllPersen_bnr,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
// ยอดรวม บ้านสวน
$OldPersen_bs = 0;
$NewPersen_bs = 0;
$AllPersen_bs = 0;

if ($row['S_01_bs'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_bs = ($row['S_9_bs'] / $row['S_01_bs']) * 100;
	$OldPersen_bs = round($OldPersen_bs,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_bs = ($row['S_23_bs'] / $row['S_01_bs']) * 100;
	$NewPersen_bs = round($NewPersen_bs,2);  // จุดทศนิยม 2 ตำแหน่ง
}

if ($S_27_bs != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_bs = ($S_28_bs / $S_27_bs) * 100;
	$AllPersen_bs = round($AllPersen_bs,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
// ยอดรวม เมืองเก่า
$OldPersen_mgk = 0;
$NewPersen_mgk = 0;
$AllPersen_mgk = 0;

if ($row['S_01_mgk'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_mgk = ($row['S_9_mgk'] / $row['S_01_mgk']) * 100;
	$OldPersen_mgk = round($OldPersen_mgk,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_mgk = ($row['S_23_mgk'] / $row['S_01_mgk']) * 100;
	$NewPersen_mgk = round($NewPersen_mgk,2);  // จุดทศนิยม 2 ตำแหน่ง
}

if ($S_27_mgk != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_mgk = ($S_28_mgk / $S_27_mgk) * 100;
	$AllPersen_mgk = round($AllPersen_mgk,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
// ยอดรวม เมืองสุโขทัย
$OldPersen_mst = 0;
$NewPersen_mst = 0;
$AllPersen_mst = 0;

if ($row['S_01_mst'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_mst = ($row['S_9_mst'] / $row['S_01_mst']) * 100;
	$OldPersen_mst = round($OldPersen_mst,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_mst = ($row['S_23_mst'] / $row['S_01_mst']) * 100;
	$NewPersen_mst = round($NewPersen_mst,2);  // จุดทศนิยม 2 ตำแหน่ง
}

if ($S_27_mst != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_mst = ($S_28_mst / $S_27_mst) * 100;
	$AllPersen_mst = round($AllPersen_mst,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
// ยอดรวม ศรีนคร
$OldPersen_snk = 0;
$NewPersen_snk = 0;
$AllPersen_snk = 0;

if ($row['S_01_snk'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_snk = ($row['S_9_snk'] / $row['S_01_snk']) * 100;
	$OldPersen_snk = round($OldPersen_snk,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_snk = ($row['S_23_snk'] / $row['S_01_snk']) * 100;
	$NewPersen_snk = round($NewPersen_snk,2);  // จุดทศนิยม 2 ตำแหน่ง
}

if ($S_27_snk != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_snk = ($S_28_snk / $S_27_snk) * 100;
	$AllPersen_snk = round($AllPersen_snk,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
// ยอดรวม ศรีสัชนาลัย
$OldPersen_snl = 0;
$NewPersen_snl = 0;
$AllPersen_snl = 0;

if ($row['S_01_snl'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_snl = ($row['S_9_snl'] / $row['S_01_snl']) * 100;
	$OldPersen_snl = round($OldPersen_snl,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_snl = ($row['S_23_snl'] / $row['S_01_snl']) * 100;
	$NewPersen_snl = round($NewPersen_snl,2);  // จุดทศนิยม 2 ตำแหน่ง
}

if ($S_27_snl != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_snl = ($S_28_snl / $S_27_snl) * 100;
	$AllPersen_snl = round($AllPersen_snl,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
// ยอดรวม ศรีสำโรง
$OldPersen_ssr = 0;
$NewPersen_ssr = 0;
$AllPersen_ssr = 0;

if ($row['S_01_ssr'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_ssr = ($row['S_9_ssr'] / $row['S_01_ssr']) * 100;
	$OldPersen_ssr = round($OldPersen_ssr,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_ssr = ($row['S_23_ssr'] / $row['S_01_ssr']) * 100;
	$NewPersen_ssr = round($NewPersen_ssr,2);  // จุดทศนิยม 2 ตำแหน่ง
}

if ($S_27_ssr != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_ssr = ($S_28_ssr / $S_27_ssr) * 100;
	$AllPersen_ssr = round($AllPersen_ssr,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
// ยอดรวม สวรรคโลก
$OldPersen_swl = 0;
$NewPersen_swl = 0;
$AllPersen_swl = 0;

if ($row['S_01_swl'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_swl = ($row['S_9_swl'] / $row['S_01_swl']) * 100;
	$OldPersen_swl = round($OldPersen_swl,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_swl = ($row['S_23_swl'] / $row['S_01_swl']) * 100;
	$NewPersen_swl = round($NewPersen_swl,2);  // จุดทศนิยม 2 ตำแหน่ง
}

if ($S_27_swl != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_swl = ($S_28_swl / $S_27_swl) * 100;
	$AllPersen_swl = round($AllPersen_swl,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
// ยอดรวม ท่าฉนวน
$OldPersen_tcn = 0;
$NewPersen_tcn = 0;
$AllPersen_tcn = 0;

if ($row['S_01_tcn'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_tcn = ($row['S_9_tcn'] / $row['S_01_tcn']) * 100;
	$OldPersen_tcn = round($OldPersen_tcn,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_tcn = ($row['S_23_tcn'] / $row['S_01_tcn']) * 100;
	$NewPersen_tcn = round($NewPersen_tcn,2);  // จุดทศนิยม 2 ตำแหน่ง
}

if ($S_27_tcn != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_tcn = ($S_28_tcn / $S_27_tcn) * 100;
	$AllPersen_tcn = round($AllPersen_tcn,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
// ยอดรวม เมืองบางขลัง
$OldPersen_mbk = 0;
$NewPersen_mbk = 0;
$AllPersen_mbk = 0;

if ($row['S_01_mbk'] != 0) {
	// เปอร์เซ็นการจับ หมายเก่า
    $OldPersen_mbk = ($row['S_9_mbk'] / $row['S_01_mbk']) * 100;
	$OldPersen_mbk = round($OldPersen_mbk,2);  // จุดทศนิยม 2 ตำแหน่ง
	// เปอร์เซ็นการจับ หมายใหม่
    $NewPersen_mbk = ($row['S_23_mbk'] / $row['S_01_mbk']) * 100;
	$NewPersen_mbk = round($NewPersen_mbk,2);  // จุดทศนิยม 2 ตำแหน่ง
}

if ($S_27_mbk != 0) {
	// เปอร์เซ็นการจับ หมายใหม่
    $AllPersen_mbk = ($S_28_mbk / $S_27_mbk) * 100;
	$AllPersen_mbk = round($AllPersen_mbk,2);  // จุดทศนิยม 2 ตำแหน่ง  
}
		
?>
 <tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left ' nowrap><span style="height:22.8pt; background-color:aqua" ><?= $name_provincial ?></span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['S_01'] ?>
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['S_02'] ?>
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_03'] ?>&nbsp;</td>
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['S_04'] ?>
  </td>
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_1'] ?>&nbsp;</td> <!-- ยอดรวม =23  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_2'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['S_3'] ?></td>  <!-- ยอดไม่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['S_4'] > 0) ? $row['S_4'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า -->    
  <!-- จับกุม     -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_5'] > 0) ? $row['S_5'] : "-") ?>&nbsp;</td> <!-- ขาดอายุความ -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_6'] > 0) ? $row['S_6'] : "-") ?>&nbsp;</td> <!-- ตาย -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['S_7'] > 0) ? $row['S_7'] : "-") ?></td>  <!-- อายัดตัว   -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_8'] > 0) ? $row['S_8'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_9'] > 0) ? $row['S_9'] : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($S_10 > 0) ? ($S_10)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($S_10_2 > 0) ? ($S_10_2)  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($S_13 > 0) ? ($S_13)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['S_14'] > 0) ? $row['S_14']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($S_15 > 0) ? ($S_15)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($S_16 > 0) ? ($S_16)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_17'] > 0) ? $row['S_17'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_18'] > 0) ? $row['S_18'] : "-") ?></td> <!-- จับกุม หมายใหม่ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_19'] > 0) ? $row['S_19'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_20'] > 0) ? $row['S_20'] : "-") ?></td> <!-- หมายใหม่ ตาย -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_21'] > 0) ? $row['S_21'] : "-") ?></td> <!-- อายัด หมายใหม่ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_22'] > 0) ? $row['S_22'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($row['S_23'] > 0) ? $row['S_23'] : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($S_24_1 > 0) ? ($S_24_1)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25'] > 0) ? ($row['S_25'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27 ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28 ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'><?=$AllPersen?> %</td> <!--  -->
 </tr>
<!-- กงไกรลาศ -->
<tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left ' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.กงไกรลาศ</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['S_01_kkl'] ?>
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['S_02_kkl'] ?>
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_03_kkl'] ?>&nbsp;</td>
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['S_04_kkl'] ?>
  </td>
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_1_kkl'] ?>&nbsp;</td> <!-- ยอดรวม =23  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_2_kkl'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['S_3_kkl'] ?></td>  <!-- ยอดไม่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['S_4_kkl'] > 0) ? $row['S_4_kkl'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า -->    
  <!-- จับกุม     -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_5_kkl'] > 0) ? $row['S_5_kkl'] : "-") ?>&nbsp;</td> <!-- ขาดอายุความ -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_6_kkl'] > 0) ? $row['S_6_kkl'] : "-") ?>&nbsp;</td> <!-- ตาย -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['S_7_kkl'] > 0) ? $row['S_7_kkl'] : "-") ?></td>  <!-- อายัดตัว   -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_8_kkl'] > 0) ? $row['S_8_kkl'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_9_kkl'] > 0) ? $row['S_9_kkl'] : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($S_10_kkl > 0) ? ($S_10_kkl)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($S_10_2_kkl > 0) ? ($S_10_2_kkl)  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_kkl ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($S_13_kkl > 0) ? ($S_13_kkl)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['S_14_kkl'] > 0) ? $row['S_14_kkl']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($S_15_kkl > 0) ? ($S_15_kkl)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($S_16_kkl > 0) ? ($S_16_kkl)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_17_kkl'] > 0) ? $row['S_17_kkl'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_18_kkl'] > 0) ? $row['S_18_kkl'] : "-") ?></td> <!-- จับกุม หมายใหม่ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_19_kkl'] > 0) ? $row['S_19_kkl'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_20_kkl'] > 0) ? $row['S_20_kkl'] : "-") ?></td> <!-- หมายใหม่ ตาย -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_21_kkl'] > 0) ? $row['S_21_kkl'] : "-") ?></td> <!-- อายัด หมายใหม่ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_22_kkl'] > 0) ? $row['S_22_kkl'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($row['S_23_kkl'] > 0) ? $row['S_23_kkl'] : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($S_24_1_kkl > 0) ? ($S_24_1_kkl)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_kkl'] > 0) ? ($row['S_25_kkl'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_kkl ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_kkl ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_kkl ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'><?= $AllPersen_kkl ?> %</td> <!--  -->
 </tr>
<!-- คีรีมาศ -->
<tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left ' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.คีรีมาศ</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['S_01_krm'] ?>
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['S_02_krm'] ?>
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_03_krm'] ?>&nbsp;</td>
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['S_04_krm'] ?>
  </td>
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_1_krm'] ?>&nbsp;</td> <!-- ยอดรวม =23  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_2_krm'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['S_3_krm'] ?></td>  <!-- ยอดไม่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['S_4_krm'] > 0) ? $row['S_4_krm'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า -->    
  <!-- จับกุม     -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_5_krm'] > 0) ? $row['S_5_krm'] : "-") ?>&nbsp;</td> <!-- ขาดอายุความ -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_6_krm'] > 0) ? $row['S_6_krm'] : "-") ?>&nbsp;</td> <!-- ตาย -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['S_7_krm'] > 0) ? $row['S_7_krm'] : "-") ?></td>  <!-- อายัดตัว   -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_8_krm'] > 0) ? $row['S_8_krm'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_9_krm'] > 0) ? $row['S_9_krm'] : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($S_10_krm > 0) ? ($S_10_krm)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($S_10_2_krm > 0) ? ($S_10_2_krm)  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_krm ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($S_13_krm > 0) ? ($S_13_krm)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['S_14_krm'] > 0) ? $row['S_14_krm']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($S_15_krm > 0) ? ($S_15_krm)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($S_16_krm > 0) ? ($S_16_krm)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_17_krm'] > 0) ? $row['S_17_krm'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_18_krm'] > 0) ? $row['S_18_krm'] : "-") ?></td> <!-- จับกุม หมายใหม่ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_19_krm'] > 0) ? $row['S_19_krm'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_20_krm'] > 0) ? $row['S_20_krm'] : "-") ?></td> <!-- หมายใหม่ ตาย -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_21_krm'] > 0) ? $row['S_21_krm'] : "-") ?></td> <!-- อายัด หมายใหม่ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_22_krm'] > 0) ? $row['S_22_krm'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($row['S_23_krm'] > 0) ? $row['S_23_krm'] : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($S_24_1_krm > 0) ? ($S_24_1_krm)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_krm'] > 0) ? ($row['S_25_krm'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_krm ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_krm ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_krm ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'><?= $AllPersen_krm ?> %</td> <!--  -->
 </tr>
<!-- ทุ่งเสลี่ยม -->
<tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left ' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.ทุ่งเสลี่ยม</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['S_01_tsl'] ?>
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['S_02_tsl'] ?>
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_03_tsl'] ?>&nbsp;</td>
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['S_04_tsl'] ?>
  </td>
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_1_tsl'] ?>&nbsp;</td> <!-- ยอดรวม =23  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_2_tsl'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['S_3_tsl'] ?></td>  <!-- ยอดไม่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['S_4_tsl'] > 0) ? $row['S_4_tsl'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า -->    
  <!-- จับกุม     -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_5_tsl'] > 0) ? $row['S_5_tsl'] : "-") ?>&nbsp;</td> <!-- ขาดอายุความ -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_6_tsl'] > 0) ? $row['S_6_tsl'] : "-") ?>&nbsp;</td> <!-- ตาย -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['S_7_tsl'] > 0) ? $row['S_7_tsl'] : "-") ?></td>  <!-- อายัดตัว   -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_8_tsl'] > 0) ? $row['S_8_tsl'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_9_tsl'] > 0) ? $row['S_9_tsl'] : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($S_10_tsl > 0) ? ($S_10_tsl)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($S_10_2_tsl > 0) ? ($S_10_2_tsl)  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_tsl ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($S_13_tsl > 0) ? ($S_13_tsl)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['S_14_tsl'] > 0) ? $row['S_14_tsl']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($S_15_tsl > 0) ? ($S_15_tsl)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($S_16_tsl > 0) ? ($S_16_tsl)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_17_tsl'] > 0) ? $row['S_17_tsl'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_18_tsl'] > 0) ? $row['S_18_tsl'] : "-") ?></td> <!-- จับกุม หมายใหม่ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_19_tsl'] > 0) ? $row['S_19_tsl'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_20_tsl'] > 0) ? $row['S_20_tsl'] : "-") ?></td> <!-- หมายใหม่ ตาย -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_21_tsl'] > 0) ? $row['S_21_tsl'] : "-") ?></td> <!-- อายัด หมายใหม่ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_22_tsl'] > 0) ? $row['S_22_tsl'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($row['S_23_tsl'] > 0) ? $row['S_23_tsl'] : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($S_24_1_tsl > 0) ? ($S_24_1_tsl)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_tsl'] > 0) ? ($row['S_25_tsl'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_tsl ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_tsl ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_tsl ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'><?= $AllPersen_tsl ?> %</td> <!--  -->
 </tr>
<!-- บ้านแก่ง -->
<tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left ' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.บ้านแก่ง</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['S_01_bng'] ?>
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['S_02_bng'] ?>
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_03_bng'] ?>&nbsp;</td>
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['S_04_bng'] ?>
  </td>
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_1_bng'] ?>&nbsp;</td> <!-- ยอดรวม =23  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_2_bng'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['S_3_bng'] ?></td>  <!-- ยอดไม่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['S_4_bng'] > 0) ? $row['S_4_bng'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า -->    
  <!-- จับกุม     -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_5_bng'] > 0) ? $row['S_5_bng'] : "-") ?>&nbsp;</td> <!-- ขาดอายุความ -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_6_bng'] > 0) ? $row['S_6_bng'] : "-") ?>&nbsp;</td> <!-- ตาย -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['S_7_bng'] > 0) ? $row['S_7_bng'] : "-") ?></td>  <!-- อายัดตัว   -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_8_bng'] > 0) ? $row['S_8_bng'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_9_bng'] > 0) ? $row['S_9_bng'] : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($S_10_bng > 0) ? ($S_10_bng)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($S_10_2_bng > 0) ? ($S_10_2_bng)  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_bng ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($S_13_bng > 0) ? ($S_13_bng)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['S_14_bng'] > 0) ? $row['S_14_bng']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($S_15_bng > 0) ? ($S_15_bng)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($S_16_bng > 0) ? ($S_16_bng)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_17_bng'] > 0) ? $row['S_17_bng'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_18_bng'] > 0) ? $row['S_18_bng'] : "-") ?></td> <!-- จับกุม หมายใหม่ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_19_bng'] > 0) ? $row['S_19_bng'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_20_bng'] > 0) ? $row['S_20_bng'] : "-") ?></td> <!-- หมายใหม่ ตาย -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_21_bng'] > 0) ? $row['S_21_bng'] : "-") ?></td> <!-- อายัด หมายใหม่ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_22_bng'] > 0) ? $row['S_22_bng'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($row['S_23_bng'] > 0) ? $row['S_23_bng'] : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($S_24_1_bng > 0) ? ($S_24_1_bng)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_bng'] > 0) ? ($row['S_25_bng'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_bng ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_bng ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_bng ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'><?= $AllPersen_bng ?> %</td> <!--  -->
 </tr>
<!-- บ้านด่านลานหอย -->
<tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left ' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.บ้านด่านลานหอย</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['S_01_bdh'] ?>
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['S_02_bdh'] ?>
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_03_bdh'] ?>&nbsp;</td>
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['S_04_bdh'] ?>
  </td>
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_1_bdh'] ?>&nbsp;</td> <!-- ยอดรวม =23  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_2_bdh'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['S_3_bdh'] ?></td>  <!-- ยอดไม่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['S_4_bdh'] > 0) ? $row['S_4_bdh'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า -->    
  <!-- จับกุม     -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_5_bdh'] > 0) ? $row['S_5_bdh'] : "-") ?>&nbsp;</td> <!-- ขาดอายุความ -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_6_bdh'] > 0) ? $row['S_6_bdh'] : "-") ?>&nbsp;</td> <!-- ตาย -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['S_7_bdh'] > 0) ? $row['S_7_bdh'] : "-") ?></td>  <!-- อายัดตัว   -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_8_bdh'] > 0) ? $row['S_8_bdh'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_9_bdh'] > 0) ? $row['S_9_bdh'] : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($S_10_bdh > 0) ? ($S_10_bdh)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($S_10_2_bdh > 0) ? ($S_10_2_bdh)  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_bdh ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($S_13_bdh > 0) ? ($S_13_bdh)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['S_14_bdh'] > 0) ? $row['S_14_bdh']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($S_15_bdh > 0) ? ($S_15_bdh)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($S_16_bdh > 0) ? ($S_16_bdh)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_17_bdh'] > 0) ? $row['S_17_bdh'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_18_bdh'] > 0) ? $row['S_18_bdh'] : "-") ?></td> <!-- จับกุม หมายใหม่ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_19_bdh'] > 0) ? $row['S_19_bdh'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_20_bdh'] > 0) ? $row['S_20_bdh'] : "-") ?></td> <!-- หมายใหม่ ตาย -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_21_bdh'] > 0) ? $row['S_21_bdh'] : "-") ?></td> <!-- อายัด หมายใหม่ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_22_bdh'] > 0) ? $row['S_22_bdh'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($row['S_23_bdh'] > 0) ? $row['S_23_bdh'] : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($S_24_1_bdh > 0) ? ($S_24_1_bdh)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_bdh'] > 0) ? ($row['S_25_bdh'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_bdh ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_bdh ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_bdh ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'><?= $AllPersen_bdh ?> %</td> <!--  -->
 </tr>
<!-- บ้านไร่ -->
<tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left ' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.บ้านไร่</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['S_01_bnr'] ?>
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['S_02_bnr'] ?>
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_03_bnr'] ?>&nbsp;</td>
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['S_04_bnr'] ?>
  </td>
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_1_bnr'] ?>&nbsp;</td> <!-- ยอดรวม =23  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_2_bnr'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['S_3_bnr'] ?></td>  <!-- ยอดไม่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['S_4_bnr'] > 0) ? $row['S_4_bnr'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า -->    
  <!-- จับกุม     -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_5_bnr'] > 0) ? $row['S_5_bnr'] : "-") ?>&nbsp;</td> <!-- ขาดอายุความ -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_6_bnr'] > 0) ? $row['S_6_bnr'] : "-") ?>&nbsp;</td> <!-- ตาย -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['S_7_bnr'] > 0) ? $row['S_7_bnr'] : "-") ?></td>  <!-- อายัดตัว   -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_8_bnr'] > 0) ? $row['S_8_bnr'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_9_bnr'] > 0) ? $row['S_9_bnr'] : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($S_10_bnr > 0) ? ($S_10_bnr)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($S_10_2_bnr > 0) ? ($S_10_2_bnr)  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_bnr ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($S_13_bnr > 0) ? ($S_13_bnr)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['S_14_bnr'] > 0) ? $row['S_14_bnr']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($S_15_bnr > 0) ? ($S_15_bnr)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($S_16_bnr > 0) ? ($S_16_bnr)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_17_bnr'] > 0) ? $row['S_17_bnr'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_18_bnr'] > 0) ? $row['S_18_bnr'] : "-") ?></td> <!-- จับกุม หมายใหม่ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_19_bnr'] > 0) ? $row['S_19_bnr'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_20_bnr'] > 0) ? $row['S_20_bnr'] : "-") ?></td> <!-- หมายใหม่ ตาย -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_21_bnr'] > 0) ? $row['S_21_bnr'] : "-") ?></td> <!-- อายัด หมายใหม่ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_22_bnr'] > 0) ? $row['S_22_bnr'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($row['S_23_bnr'] > 0) ? $row['S_23_bnr'] : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($S_24_1_bnr > 0) ? ($S_24_1_bnr)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_bnr'] > 0) ? ($row['S_25_bnr'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_bnr ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_bnr ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_bnr ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'><?= $AllPersen_bnr ?> %</td> <!--  -->
 </tr>
<!-- บ้านสวน -->
<tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left ' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.บ้านสวน</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['S_01_bs'] ?>
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['S_02_bs'] ?>
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_03_bs'] ?>&nbsp;</td>
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['S_04_bs'] ?>
  </td>
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_1_bs'] ?>&nbsp;</td> <!-- ยอดรวม =23  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_2_bs'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['S_3_bs'] ?></td>  <!-- ยอดไม่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['S_4_bs'] > 0) ? $row['S_4_bs'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า -->    
  <!-- จับกุม     -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_5_bs'] > 0) ? $row['S_5_bs'] : "-") ?>&nbsp;</td> <!-- ขาดอายุความ -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_6_bs'] > 0) ? $row['S_6_bs'] : "-") ?>&nbsp;</td> <!-- ตาย -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['S_7_bs'] > 0) ? $row['S_7_bs'] : "-") ?></td>  <!-- อายัดตัว   -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_8_bs'] > 0) ? $row['S_8_bs'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_9_bs'] > 0) ? $row['S_9_bs'] : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($S_10_bs > 0) ? ($S_10_bs)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($S_10_2_bs > 0) ? ($S_10_2_bs)  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_bs ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($S_13_bs > 0) ? ($S_13_bs)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['S_14_bs'] > 0) ? $row['S_14_bs']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($S_15_bs > 0) ? ($S_15_bs)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($S_16_bs > 0) ? ($S_16_bs)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_17_bs'] > 0) ? $row['S_17_bs'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_18_bs'] > 0) ? $row['S_18_bs'] : "-") ?></td> <!-- จับกุม หมายใหม่ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_19_bs'] > 0) ? $row['S_19_bs'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_20_bs'] > 0) ? $row['S_20_bs'] : "-") ?></td> <!-- หมายใหม่ ตาย -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_21_bs'] > 0) ? $row['S_21_bs'] : "-") ?></td> <!-- อายัด หมายใหม่ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_22_bs'] > 0) ? $row['S_22_bs'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($row['S_23_bs'] > 0) ? $row['S_23_bs'] : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($S_24_1_bs > 0) ? ($S_24_1_bs)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_bs'] > 0) ? ($row['S_25_bs'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_bs ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_bs ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_bs ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'><?= $AllPersen_bs ?> %</td> <!--  -->
 </tr>
<!-- เมืองเก่า -->
<tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left ' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.เมืองเก่า</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['S_01_mgk'] ?>
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['S_02_mgk'] ?>
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_03_mgk'] ?>&nbsp;</td>
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['S_04_mgk'] ?>
  </td>
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_1_mgk'] ?>&nbsp;</td> <!-- ยอดรวม =23  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_2_mgk'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['S_3_mgk'] ?></td>  <!-- ยอดไม่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['S_4_mgk'] > 0) ? $row['S_4_mgk'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า -->    
  <!-- จับกุม     -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_5_mgk'] > 0) ? $row['S_5_mgk'] : "-") ?>&nbsp;</td> <!-- ขาดอายุความ -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_6_mgk'] > 0) ? $row['S_6_mgk'] : "-") ?>&nbsp;</td> <!-- ตาย -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['S_7_mgk'] > 0) ? $row['S_7_mgk'] : "-") ?></td>  <!-- อายัดตัว   -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_8_mgk'] > 0) ? $row['S_8_mgk'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_9_mgk'] > 0) ? $row['S_9_mgk'] : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($S_10_mgk > 0) ? ($S_10_mgk)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($S_10_2_mgk > 0) ? ($S_10_2_mgk)  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_mgk ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($S_13_mgk > 0) ? ($S_13_mgk)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['S_14_mgk'] > 0) ? $row['S_14_mgk']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($S_15_mgk > 0) ? ($S_15_mgk)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($S_16_mgk > 0) ? ($S_16_mgk)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_17_mgk'] > 0) ? $row['S_17_mgk'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_18_mgk'] > 0) ? $row['S_18_mgk'] : "-") ?></td> <!-- จับกุม หมายใหม่ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_19_mgk'] > 0) ? $row['S_19_mgk'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_20_mgk'] > 0) ? $row['S_20_mgk'] : "-") ?></td> <!-- หมายใหม่ ตาย -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_21_mgk'] > 0) ? $row['S_21_mgk'] : "-") ?></td> <!-- อายัด หมายใหม่ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_22_mgk'] > 0) ? $row['S_22_mgk'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($row['S_23_mgk'] > 0) ? $row['S_23_mgk'] : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($S_24_1_mgk > 0) ? ($S_24_1_mgk)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_mgk'] > 0) ? ($row['S_25_mgk'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_mgk ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_mgk ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_mgk ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'><?= $AllPersen_mgk ?> %</td> <!--  -->
 </tr>
<!-- เมืองสุโขทัย -->
<tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left ' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.เมืองสุโขทัย</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['S_01_mst'] ?>
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['S_02_mst'] ?>
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_03_mst'] ?>&nbsp;</td>
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['S_04_mst'] ?>
  </td>
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_1_mst'] ?>&nbsp;</td> <!-- ยอดรวม =23  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_2_mst'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['S_3_mst'] ?></td>  <!-- ยอดไม่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['S_4_mst'] > 0) ? $row['S_4_mst'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า -->    
  <!-- จับกุม     -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_5_mst'] > 0) ? $row['S_5_mst'] : "-") ?>&nbsp;</td> <!-- ขาดอายุความ -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_6_mst'] > 0) ? $row['S_6_mst'] : "-") ?>&nbsp;</td> <!-- ตาย -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['S_7_mst'] > 0) ? $row['S_7_mst'] : "-") ?></td>  <!-- อายัดตัว   -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_8_mst'] > 0) ? $row['S_8_mst'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_9_mst'] > 0) ? $row['S_9_mst'] : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($S_10_mst > 0) ? ($S_10_mst)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($S_10_2_mst > 0) ? ($S_10_2_mst)  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_mst ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($S_13_mst > 0) ? ($S_13_mst)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['S_14_mst'] > 0) ? $row['S_14_mst']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($S_15_mst > 0) ? ($S_15_mst)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($S_16_mst > 0) ? ($S_16_mst)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_17_mst'] > 0) ? $row['S_17_mst'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_18_mst'] > 0) ? $row['S_18_mst'] : "-") ?></td> <!-- จับกุม หมายใหม่ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_19_mst'] > 0) ? $row['S_19_mst'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_20_mst'] > 0) ? $row['S_20_mst'] : "-") ?></td> <!-- หมายใหม่ ตาย -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_21_mst'] > 0) ? $row['S_21_mst'] : "-") ?></td> <!-- อายัด หมายใหม่ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_22_mst'] > 0) ? $row['S_22_mst'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($row['S_23_mst'] > 0) ? $row['S_23_mst'] : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($S_24_1_mst > 0) ? ($S_24_1_mst)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_mst'] > 0) ? ($row['S_25_mst'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_mst ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_mst ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_mst ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'><?= $AllPersen_mst ?> %</td> <!--  -->
 </tr>
<!-- ศรีนคร -->
<tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left ' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.ศรีนคร</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['S_01_snk'] ?>
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['S_02_snk'] ?>
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_03_snk'] ?>&nbsp;</td>
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['S_04_snk'] ?>
  </td>
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_1_snk'] ?>&nbsp;</td> <!-- ยอดรวม =23  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_2_snk'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['S_3_snk'] ?></td>  <!-- ยอดไม่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['S_4_snk'] > 0) ? $row['S_4_snk'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า -->    
  <!-- จับกุม     -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_5_snk'] > 0) ? $row['S_5_snk'] : "-") ?>&nbsp;</td> <!-- ขาดอายุความ -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_6_snk'] > 0) ? $row['S_6_snk'] : "-") ?>&nbsp;</td> <!-- ตาย -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['S_7_snk'] > 0) ? $row['S_7_snk'] : "-") ?></td>  <!-- อายัดตัว   -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_8_snk'] > 0) ? $row['S_8_snk'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_9_snk'] > 0) ? $row['S_9_snk'] : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($S_10_snk > 0) ? ($S_10_snk)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($S_10_2_snk > 0) ? ($S_10_2_snk)  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_snk ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($S_13_snk > 0) ? ($S_13_snk)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['S_14_snk'] > 0) ? $row['S_14_snk']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($S_15_snk > 0) ? ($S_15_snk)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($S_16_snk > 0) ? ($S_16_snk)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_17_snk'] > 0) ? $row['S_17_snk'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_18_snk'] > 0) ? $row['S_18_snk'] : "-") ?></td> <!-- จับกุม หมายใหม่ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_19_snk'] > 0) ? $row['S_19_snk'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_20_snk'] > 0) ? $row['S_20_snk'] : "-") ?></td> <!-- หมายใหม่ ตาย -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_21_snk'] > 0) ? $row['S_21_snk'] : "-") ?></td> <!-- อายัด หมายใหม่ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_22_snk'] > 0) ? $row['S_22_snk'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($row['S_23_snk'] > 0) ? $row['S_23_snk'] : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($S_24_1_snk > 0) ? ($S_24_1_snk)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_snk'] > 0) ? ($row['S_25_snk'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_snk ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_snk ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_snk ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'><?= $AllPersen_snk ?> %</td> <!--  -->
 </tr>
<!-- ศรีสัชนาลัย -->
<tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left ' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.ศรีสัชนาลัย</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['S_01_snl'] ?>
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['S_02_snl'] ?>
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_03_snl'] ?>&nbsp;</td>
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['S_04_snl'] ?>
  </td>
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_1_snl'] ?>&nbsp;</td> <!-- ยอดรวม =23  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_2_snl'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['S_3_snl'] ?></td>  <!-- ยอดไม่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['S_4_snl'] > 0) ? $row['S_4_snl'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า -->    
  <!-- จับกุม     -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_5_snl'] > 0) ? $row['S_5_snl'] : "-") ?>&nbsp;</td> <!-- ขาดอายุความ -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_6_snl'] > 0) ? $row['S_6_snl'] : "-") ?>&nbsp;</td> <!-- ตาย -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['S_7_snl'] > 0) ? $row['S_7_snl'] : "-") ?></td>  <!-- อายัดตัว   -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_8_snl'] > 0) ? $row['S_8_snl'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_9_snl'] > 0) ? $row['S_9_snl'] : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($S_10_snl > 0) ? ($S_10_snl)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($S_10_2_snl > 0) ? ($S_10_2_snl)  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_snl ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($S_13_snl > 0) ? ($S_13_snl)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['S_14_snl'] > 0) ? $row['S_14_snl']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($S_15_snl > 0) ? ($S_15_snl)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($S_16_snl > 0) ? ($S_16_snl)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_17_snl'] > 0) ? $row['S_17_snl'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_18_snl'] > 0) ? $row['S_18_snl'] : "-") ?></td> <!-- จับกุม หมายใหม่ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_19_snl'] > 0) ? $row['S_19_snl'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_20_snl'] > 0) ? $row['S_20_snl'] : "-") ?></td> <!-- หมายใหม่ ตาย -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_21_snl'] > 0) ? $row['S_21_snl'] : "-") ?></td> <!-- อายัด หมายใหม่ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_22_snl'] > 0) ? $row['S_22_snl'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($row['S_23_snl'] > 0) ? $row['S_23_snl'] : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($S_24_1_snl > 0) ? ($S_24_1_snl)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_snl'] > 0) ? ($row['S_25_snl'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_snl ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_snl ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_snl ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'><?= $AllPersen_snl ?> %</td> <!--  -->
 </tr>
<!-- ศรีสำโรง -->
<tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left ' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.ศรีสำโรง</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['S_01_ssr'] ?>
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['S_02_ssr'] ?>
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_03_ssr'] ?>&nbsp;</td>
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['S_04_ssr'] ?>
  </td>
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_1_ssr'] ?>&nbsp;</td> <!-- ยอดรวม =23  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_2_ssr'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['S_3_ssr'] ?></td>  <!-- ยอดไม่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['S_4_ssr'] > 0) ? $row['S_4_ssr'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า -->    
  <!-- จับกุม     -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_5_ssr'] > 0) ? $row['S_5_ssr'] : "-") ?>&nbsp;</td> <!-- ขาดอายุความ -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_6_ssr'] > 0) ? $row['S_6_ssr'] : "-") ?>&nbsp;</td> <!-- ตาย -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['S_7_ssr'] > 0) ? $row['S_7_ssr'] : "-") ?></td>  <!-- อายัดตัว   -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_8_ssr'] > 0) ? $row['S_8_ssr'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_9_ssr'] > 0) ? $row['S_9_ssr'] : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($S_10_ssr > 0) ? ($S_10_ssr)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($S_10_2_ssr > 0) ? ($S_10_2_ssr)  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_ssr ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($S_13_ssr > 0) ? ($S_13_ssr)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['S_14_ssr'] > 0) ? $row['S_14_ssr']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($S_15_ssr > 0) ? ($S_15_ssr)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($S_16_ssr > 0) ? ($S_16_ssr)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_17_ssr'] > 0) ? $row['S_17_ssr'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_18_ssr'] > 0) ? $row['S_18_ssr'] : "-") ?></td> <!-- จับกุม หมายใหม่ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_19_ssr'] > 0) ? $row['S_19_ssr'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_20_ssr'] > 0) ? $row['S_20_ssr'] : "-") ?></td> <!-- หมายใหม่ ตาย -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_21_ssr'] > 0) ? $row['S_21_ssr'] : "-") ?></td> <!-- อายัด หมายใหม่ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_22_ssr'] > 0) ? $row['S_22_ssr'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($row['S_23_ssr'] > 0) ? $row['S_23_ssr'] : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($S_24_1_ssr > 0) ? ($S_24_1_ssr)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_ssr'] > 0) ? ($row['S_25_ssr'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_ssr ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_ssr ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_ssr ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'><?= $AllPersen_ssr ?> %</td> <!--  -->
 </tr>
<!-- สวรรคโลก -->
<tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left ' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.สวรรคโลก</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['S_01_swl'] ?>
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['S_02_swl'] ?>
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_03_swl'] ?>&nbsp;</td>
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['S_04_swl'] ?>
  </td>
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_1_swl'] ?>&nbsp;</td> <!-- ยอดรวม =23  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_2_swl'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['S_3_swl'] ?></td>  <!-- ยอดไม่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['S_4_swl'] > 0) ? $row['S_4_swl'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า -->    
  <!-- จับกุม     -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_5_swl'] > 0) ? $row['S_5_swl'] : "-") ?>&nbsp;</td> <!-- ขาดอายุความ -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_6_swl'] > 0) ? $row['S_6_swl'] : "-") ?>&nbsp;</td> <!-- ตาย -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['S_7_swl'] > 0) ? $row['S_7_swl'] : "-") ?></td>  <!-- อายัดตัว   -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_8_swl'] > 0) ? $row['S_8_swl'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_9_swl'] > 0) ? $row['S_9_swl'] : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($S_10_swl > 0) ? ($S_10_swl)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($S_10_2_swl > 0) ? ($S_10_2_swl)  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_swl ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($S_13_swl > 0) ? ($S_13_swl)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['S_14_swl'] > 0) ? $row['S_14_swl']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($S_15_swl > 0) ? ($S_15_swl)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($S_16_swl > 0) ? ($S_16_swl)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_17_swl'] > 0) ? $row['S_17_swl'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_18_swl'] > 0) ? $row['S_18_swl'] : "-") ?></td> <!-- จับกุม หมายใหม่ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_19_swl'] > 0) ? $row['S_19_swl'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_20_swl'] > 0) ? $row['S_20_swl'] : "-") ?></td> <!-- หมายใหม่ ตาย -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_21_swl'] > 0) ? $row['S_21_swl'] : "-") ?></td> <!-- อายัด หมายใหม่ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_22_swl'] > 0) ? $row['S_22_swl'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($row['S_23_swl'] > 0) ? $row['S_23_swl'] : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($S_24_1_swl > 0) ? ($S_24_1_swl)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_swl'] > 0) ? ($row['S_25_swl'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_swl ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_swl ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_swl ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'><?= $AllPersen_swl ?> %</td> <!--  -->
 </tr>
<!-- ท่าฉนวน -->
<tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left ' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.ท่าฉนวน</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['S_01_tcn'] ?>
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['S_02_tcn'] ?>
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_03_tcn'] ?>&nbsp;</td>
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['S_04_tcn'] ?>
  </td>
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_1_tcn'] ?>&nbsp;</td> <!-- ยอดรวม =23  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_2_tcn'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['S_3_tcn'] ?></td>  <!-- ยอดไม่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['S_4_tcn'] > 0) ? $row['S_4_tcn'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า -->    
  <!-- จับกุม     -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_5_tcn'] > 0) ? $row['S_5_tcn'] : "-") ?>&nbsp;</td> <!-- ขาดอายุความ -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_6_tcn'] > 0) ? $row['S_6_tcn'] : "-") ?>&nbsp;</td> <!-- ตาย -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['S_7_tcn'] > 0) ? $row['S_7_tcn'] : "-") ?></td>  <!-- อายัดตัว   -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_8_tcn'] > 0) ? $row['S_8_tcn'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_9_tcn'] > 0) ? $row['S_9_tcn'] : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($S_10_tcn > 0) ? ($S_10_tcn)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($S_10_2_tcn > 0) ? ($S_10_2_tcn)  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_tcn ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($S_13_tcn > 0) ? ($S_13_tcn)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['S_14_tcn'] > 0) ? $row['S_14_tcn']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($S_15_tcn > 0) ? ($S_15_tcn)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($S_16_tcn > 0) ? ($S_16_tcn)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_17_tcn'] > 0) ? $row['S_17_tcn'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_18_tcn'] > 0) ? $row['S_18_tcn'] : "-") ?></td> <!-- จับกุม หมายใหม่ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_19_tcn'] > 0) ? $row['S_19_tcn'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_20_tcn'] > 0) ? $row['S_20_tcn'] : "-") ?></td> <!-- หมายใหม่ ตาย -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_21_tcn'] > 0) ? $row['S_21_tcn'] : "-") ?></td> <!-- อายัด หมายใหม่ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_22_tcn'] > 0) ? $row['S_22_tcn'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($row['S_23_tcn'] > 0) ? $row['S_23_tcn'] : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($S_24_1_tcn > 0) ? ($S_24_1_tcn)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_tcn'] > 0) ? ($row['S_25_tcn'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_tcn ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_tcn ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_tcn ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'><?= $AllPersen_tcn ?> %</td> <!--  -->
 </tr>
<!-- เมืองบางขลัง -->
<tr height=30 style='height:22.8pt'>
   <td style='border:solid #000000; background-color:aqua;text-align: left ' nowrap><span style="height:22.8pt; background-color:aqua ">สภ.เมืองบางขลัง</span></td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['S_01_mbk'] ?>
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>
     <?= $row['S_02_mbk'] ?>
   </td>
   <td style='border:solid #000000; background-color:#DDDDDD; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_03_mbk'] ?>&nbsp;</td>
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>
    <?= $row['S_04_mbk'] ?>
  </td>
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_1_mbk'] ?>&nbsp;</td> <!-- ยอดรวม =23  -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300 '>&nbsp;<?= $row['S_2_mbk'] ?>&nbsp;</td>  <!-- ยอดมีคุณภาพ  -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?= $row['S_3_mbk'] ?></td>  <!-- ยอดไม่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (( $row['S_4_mbk'] > 0) ? $row['S_4_mbk'] : "-")?>&nbsp;</td>  <!-- จับกุม หมายเก่า -->    
  <!-- จับกุม     -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_5_mbk'] > 0) ? $row['S_5_mbk'] : "-") ?>&nbsp;</td> <!-- ขาดอายุความ -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_6_mbk'] > 0) ? $row['S_6_mbk'] : "-") ?>&nbsp;</td> <!-- ตาย -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '><?php echo (($row['S_7_mbk'] > 0) ? $row['S_7_mbk'] : "-") ?></td>  <!-- อายัดตัว   -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_8_mbk'] > 0) ? $row['S_8_mbk'] : "-") ?>&nbsp;</td> <!-- จำหน่ายอื่น ๆ -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($row['S_9_mbk'] > 0) ? $row['S_9_mbk'] : "-") ?>&nbsp;</td> <!-- รวมยอดจำหน่ายหมายเก่า ก่อน 30 ก.ย.2565  -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($S_10_mbk > 0) ? ($S_10_mbk)  : "-") ?>&nbsp;</td>  <!-- หมายมีคุณภาพ ลบด้วย ยอดจำหน่าย -->
  <td style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?php echo (($S_10_2_mbk > 0) ? ($S_10_2_mbk)  : "-") ?>&nbsp;</td> <!-- รวมยอดไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:aqua; font-size: 25px; font-weight: 300 '>&nbsp;<?= $OldPersen_mbk ?> %&nbsp;</td>  <!-- รวมจำหน่ายหมายเก่า -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($S_13_mbk > 0) ? ($S_13_mbk)  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่  -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($row['S_14_mbk'] > 0) ? $row['S_14_mbk']  : "-") ?>&nbsp;</td> <!-- หมายจับใหม่เพิ่มเติม -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'>&nbsp;<?php echo (($S_15_mbk > 0) ? ($S_15_mbk)  : "-") ?>&nbsp;</td> <!-- รวมหมายจับใหม่ -->
  <td style='border:solid #000000; background-color:#7FFA75; font-size: 25px; font-weight: 300'><?php echo (($S_16_mbk > 0) ? ($S_16_mbk)  : "-") ?></td> <!-- หมายใหม่ มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_17_mbk'] > 0) ? $row['S_17_mbk'] : "-") ?></td> <!-- หมายใหม่ ไม่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_18_mbk'] > 0) ? $row['S_18_mbk'] : "-") ?></td> <!-- จับกุม หมายใหม่ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_19_mbk'] > 0) ? $row['S_19_mbk'] : "-") ?></td> <!-- หมายใหม่ ขาดอายุความ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_20_mbk'] > 0) ? $row['S_20_mbk'] : "-") ?></td> <!-- หมายใหม่ ตาย -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_21_mbk'] > 0) ? $row['S_21_mbk'] : "-") ?></td> <!-- อายัด หมายใหม่ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_22_mbk'] > 0) ? $row['S_22_mbk'] : "-") ?></td> <!-- จำหน่ายหมายใหม่ อื่น ๆ -->
  <td style='border:solid #000000; background-color:#FA757F; font-size: 25px; font-weight: 300'><?php echo (($row['S_23_mbk'] > 0) ? $row['S_23_mbk'] : "0") ?></td> <!-- รวมจำหน่าย หมายใหม่ -->
  <td style='border:solid #000000; background-color:#51ADFC; font-size: 25px; font-weight: 300'><?php echo (($S_24_1_mbk > 0) ? ($S_24_1_mbk)  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ที่มีคุณภาพ -->
  <td style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?php echo (($row['S_25_mbk'] > 0) ? ($row['S_25_mbk'])  : "-") ?></td> <!-- คงเหลือ หมายใหม่ ไม่มีคุณภาพ -->
  <td nowrap style='border:solid #000000; background-color:#ADFDB6; font-size: 25px; font-weight: 300'><?= $NewPersen_mbk ?> %</td>
  <td style='border:solid #000000; background-color: #51ADFC; font-size: 25px; font-weight: 300'><?= $S_27_mbk ?></td> <!-- ยอดรวม หมายจับทั้งหมด ทั้งเก่า และใหม่-->
  <td style='border:solid #000000; background-color: #FA757F; font-size: 25px; font-weight: 300'><?= $S_28_mbk ?></td> <!-- ยอดรวมจำหน่ายหมาย ทั้งหมด -->
  <td nowrap style='border:solid #000000; background-color: yellow; font-size: 25px; font-weight: 300'><?= $AllPersen_mbk ?> %</td> <!--  -->
 </tr>
	  
<?php
    }
} catch (PDOException $e) {
    // Handle errors
    echo 'Query failed: ' . $e->getMessage();
    exit;
}
?>
  </tbody>
</table>
</div>
</div>
</body>

</html>
