<?php
include "../Condb.php"; //PDO
include '../users.inc.php';
include '../right_user.php';


// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link rel="icon" type="image/x-icon" href="/favicon.ico">
<title>เพิ่มข้อมูล ATM and CDM</title>
<script src="/policeinnopolis/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>    
<link rel="stylesheet" href="/policeinnopolis/bootstrap/css/bootstrap.rtl.min.css">
<style type="text/css">
body,td,th {
    color: #1203F8;
}
	.callcenter {
		width: 20px;
		height: 20px;
	}
</style>
<!-- สำหรับ วันที่ แบบ พ.ศ. -->
<link rel="stylesheet" href="http://code.jquery.com/ui/1.9.1/themes/base/jquery-ui.css" />
<script src="http://code.jquery.com/jquery-1.8.2.js"></script>
<script src="/policeinnopolis/js/jquery-ui.js"></script>
<script>
window.addEventListener('DOMContentLoaded', function() {
  // Get references to the checkboxes
  var checkbox1 = document.getElementById('callcenter1');
  var checkbox2 = document.getElementById('callcenter2');
  
  // Replace this with your logic to determine the default value (1 or 2)
  var defaultValue = 1; // Change this value based on your data
  
  if (defaultValue === 1) {
    checkbox1.checked = true;
    checkbox2.checked = false;
  } else if (defaultValue === 2) {
    checkbox1.checked = false;
    checkbox2.checked = true;
  }
  
  // Add event listeners to checkboxes to manage their state
  checkbox1.addEventListener('change', function() {
    if (checkbox1.checked) {
      checkbox2.checked = false;
    }
  });
  
  checkbox2.addEventListener('change', function() {
    if (checkbox2.checked) {
      checkbox1.checked = false;
    }
  });
});
</script>
</head>

<body>
	<div class="container">
	<div class="row">
		<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> เพิ่มข้อมูล ATM and CDM </div>
	<form action="Save_ATM_CDM.php" method="POST" enctype="multipart/form-data">
        
        <label hidden ="hidden">ลำดับ</label>
		<input type = "text" name = "atm_id" value= "" class="form-control"  hidden ="hidden" >
        
		<label>รหัสตู้ ATM หรือ CDM</label>
		<input type = "text" name="ATMcode" value= "" class="form-control" >
		<label>ธนาคาร</label>
		<select name="Bank" id="Bank" onChange="do_bank_change()" class="form-control" >
            <option value="">เลือกธนาคาร</option>
                <?php
                    $res_ty = $pdo->prepare("SELECT * FROM bank_name ORDER BY bk_code ASC");
                    $res_ty->execute();
                
                    while($row_ty = $res_ty->fetch(PDO::FETCH_ASSOC))
                    {
                        if($ps_type == $row_ty['bk_code']) {
                            $selected = "selected";
                        }
                        else {
                            $selected = "";
                        }

                        $bk_code = htmlspecialchars($row_ty['bk_code'], ENT_QUOTES, 'UTF-8');
                        $bk_name = htmlspecialchars($row_ty['bk_name'], ENT_QUOTES, 'UTF-8');
                        
                        echo "<option value='$bk_code' {$selected}> $bk_name </option>";
                    }
                ?>
            </select>

        <label>รหัสธนาคาร</label>
		<input type="text" name="Branch" id="Branch" value="" class="form-control" readonly>

		<script>
		function do_bank_change() {
			// Get the selected bank's value (bk_code)
			var selectedBank = document.getElementById("Bank").value;

			// Update the "Branch" input with the selected bank's value
			document.getElementById("Branch").value = selectedBank;
		}
		</script>
		
		<label>จุดติดตั้งตู้</label>
		<input type = "text" name = "location" value= "" class="form-control" placeholder="สถานที่ และที่อยู่ของจุดติดตั้งตู้"  >
		<label>จังหวัดที่ติดตั้งตู้</label>
		<input type = "text" name = "Province" value= "" class="form-control" placeholder="จังหวัดที่ติดตั้งตู้"  >
		<label>รหัสไปรษณีย์</label>
		<input type = "text" name = "Postcode" value= "" class="form-control" placeholder="ระบุรหัสไปรษณีย์จุดที่ติดตั้ง"  >
		<label>ละติจูด</label>
		<input type = "text" name = "atm_lat" value= "" class="form-control" placeholder="ระบุละติจูด"  >
		<label>ลองจิจูด</label>
		<input type = "text" name = "atm_long" value= "" class="form-control" placeholder="ระบุลองจิจูด"  >

		<br>
        <label>วันอัพเดตข้อมูล </label>
        <p><input type="text" name="data_Update" id="datepicker" value= "" class="form-control" placeholder="เลือกวันที่อัพเดตข้อมูล" autocomplete="off" ></p>
        <script>
            function set_cal(ele)//function สร้างตัวเลือกปฎิทิน
            {
              $( ele ).datepicker({
                  onSelect:(date_text)=>
                  {
                    let arr=date_text.split("/");
                    let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])+543).toString();
                    $(ele).val(new_date);
                    $(ele).css("color","");
                  },
                  beforeShow:()=>{

                    if($(ele).val()!="")
                    {
                      let arr=$(ele).val().split("/");
                      let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])-543).toString();
                      $(ele).val(new_date);

                    }

                    $(ele).css("color","white");
                  },
                  onClose:()=>{

                      $(ele).css("color","");

                      if($(ele).val()!="")
                      {
                          let arr=$(ele).val().split("-");
                          if(parseInt(arr[2])<2500)
                          {
                              let new_date=arr[0]+"-"+arr[1]+"-"+(parseInt(arr[2])).toString();
                              $(ele).val(new_date);
                          }
                      }
                  },
                  dateFormat:"yy-mm-dd", //กำหนดรูปแบบวันที่เป็น วัน/เดือน/ปี
                  changeMonth:true,//กำหนดให้เลือกเดือนได้
                  changeYear:true,//กำหนดให้เลือกปีได้
                  showOtherMonths:true,//กำหนดให้แสดงวันของเดือนก่อนหน้าได้
              });
            }
          $(document).ready(function(){
            //เรียก function set_cal เมื่อเปิดหน้าเว็บ โดยส่ง object element ที่มี id เป็น datepicker เป็นพารามิเตอร์
            set_cal( $("#datepicker") );
          })
          </script>
        <!-- สำหรับ วันที่ แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ -->
        
		<label>หมายเหตุ</label>
        <textarea class="form-control" id="Remark" name="Remark" rows="5" placeholder="หมายเหตุ (ถ้ามี)" > </textarea>
	 	<br>
			<p>
			<input type="submit" value="บันทึกข้อมูล" class="btn btn-success" >
			<td> <a href="/WatchmanData/form_ATM_CDM_search.php?pcid=<?= rand() ?>" class="btn btn-warning" >ยกเลิก</a> </td>
			</p>
		<br>
		<br>
	</form>
	</div>
	</div>
	</div>
</body>
</html>