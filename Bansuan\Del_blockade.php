<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../Alert.php';


$id = $_GET['id'];
$num = $_GET['num'];

if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
// Store $pcid in a session variable
$_SESSION['num'] = $num;

$acc = $user['account'];
$ip_address = $_SERVER['REMOTE_ADDR'];
$action = 'Delete blockade'; // Update the action to reflect a delete action
if (isset($_SESSION['num'])) {
    $deletedItemId = $_SESSION['num'];
    $data = ['deleted_item_id' => $deletedItemId]; // Create a data array with the deleted item's ID
	$dataString = json_encode($data); // Encode the array into a JSON string
    log_activity($acc, $action, $dataString, $ip_address);

    // Clear the session variable after successful deletion if needed
    unset($_SESSION['num']);
}


if($user['ua_delete_data'] != 100)
{
    echo '<script>alert("เฉพาะสิทธิ์ Admin เท่านั้น")</script>';
}

try{
// Perform the deletion query
$sql = "DELETE FROM wm_tb_blockade WHERE bl_cl_aid=:id ";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':id', $id);
    $result = $stmt->execute();

if ($result) {
    $_SESSION['success'] = "Data has been deleted successfully";
    showSweetAlert("Delete successfully", "ลบข้อมูลเรียบร้อย");
    unset($_SESSION['success']); // Clear the session variable
    header("refresh:2; url=/Bansuan/index.php?rnd=' + Math.random() + '&page=blockade");
} else {
    $_SESSION['error'] = "ลบข้อมูลไม่สำเร็จ";
    showSweetAlert("Failed to delete", "ลบข้อมูลไม่สำเร็จ");
    unset($_SESSION['success']); // Clear the session variable
    header("refresh:2; url=/Bansuan/index.php?rnd=' + Math.random() + '&page=blockade");
}

}catch(Exception $e){
    echo 'Query Failed : '. $e->getMessage();
}

$pdo = null;
?>