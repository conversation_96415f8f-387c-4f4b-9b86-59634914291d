<?php
// Database connection parameters
include '../Condb.php';
include '../users.inc.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// User's latitude and longitude
//$userLat = $_GET['lat'];
//$userLong = $_GET['long'];
$userLat = 17.***************;
$userLong = 99.**************;

// Radius in kilometers
$radius = 2;

// Query nearby places
$query = "SELECT gc_cl_aid, gc_cl_name, gc_cl_place_lat, gc_cl_place_lon,
          (6371 * ACOS(COS(RADIANS(:lat)) * COS(RADIANS(gc_cl_place_lat)) * COS(RADIANS(gc_cl_place_lon) - RADIANS(:long)) + SIN(RADIANS(:lat)) * SIN(RADIANS(gc_cl_place_lat)))) AS distance
          FROM wm_tb_place_data
          HAVING distance <= :radius
          ORDER BY distance";

$stmt = $pdo->prepare($query);
$stmt->bindParam(':lat', $userLat);
$stmt->bindParam(':long', $userLong);
$stmt->bindParam(':radius', $radius);
$stmt->execute();
$places = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>

<!DOCTYPE html>
<html>
<head>
    <title>Nearby Places</title>
    <script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCIN1hO0EaE0HOhnhxk5i0tC1COym6fWSw&callback=initMap" async defer></script>
	<!--<script src='https://maps.googleapis.com/maps/api/distancematrix/json?origins=Washington%2C%20DC&destinations=New%20York%20City%2C%20NY&units=imperial&key=YOUR_API_KEY'
	</script>-->
    <script>
        var map;

        function initMap() {
            map = new google.maps.Map(document.getElementById('map'), {
                center: {lat: <?php echo $userLat; ?>, lng: <?php echo $userLong; ?>},
                zoom: 12
            });

            <?php foreach ($places as $place) { ?>
                var marker = new google.maps.Marker({
                    position: {lat: <?php echo $place['gc_cl_place_lat']; ?>, lng: <?php echo $place['gc_cl_place_lon']; ?>},
                    map: map,
                    title: '<?php echo $place['gc_cl_name']; ?>'
                });
            <?php } ?>
        }
    </script>
</head>
<body>
    <h1>Nearby Places</h1>
    <div id="map" style="height: 400px;"></div>
</body>
</html>