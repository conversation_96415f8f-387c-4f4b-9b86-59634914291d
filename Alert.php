<?php
function showSweetAlert($title, $text, $type = 'success') {
    echo "<script src='/jQuery/jquery-3.5.1.min.js' type='text/javascript'></script>";
    echo "<script src='../SweetAlert2/dist/sweetalert2.all.min.js'></script>";
    echo "<script>
        $(document).ready(function() {
            Swal.fire({
                title: '$title',
                text: '$text',
                icon: '$type',
                timer: 15000,
				timerProgressBar: true,
                showConfirmButton: true
            });
        })
    </script>";
}

?>

<?php
function showSweetAlert2($title, $text, $type = 'info', $redirectUrl = null) {
    echo "<script src='/jQuery/jquery-3.5.1.min.js' type='text/javascript'></script>";
    echo "<script src='../SweetAlert2/dist/sweetalert2.all.min.js'></script>";
	echo "<style> .left-align {text-align: left !important;}</style>";
	echo "<script>
        $(document).ready(function() {
        Swal.fire({
                title: '$title',
                html: `<div class='left-align'> (1) ข้อมูลที่ใช้งานนี้ถือเป็นความลับของทางราชการ <br>
                        (2) โปรดใช้ข้อมูล เพื่อป้องกันปราบปรามอาชญากรรมหรือรักษาความปลอดภัยในชีวิตและทรัพย์ของประชาชนเท่านั้น <br>
                        (3) ห้ามนำข้อมูลไปใช้ในส่วนที่ไม่เกี่ยวกับการปฏิบัติหน้าที่ราชการเด็ดขาด <br>
						(4) หากนำข้อมูลออกไปใช้แล้วเกิดความเสียหายหรือถูกร้องเรียน จะต้องรับผิดชอบต่อการดำเนินการนั้น <br>
                        (5) และยินยอมให้โปรแกรมจัดการข้อมูลการใช้งานของผู้ใช้ทุกรายการ สำหรับใช้ในการตรวจสอบย้อนหลัง</div>`,
                icon: '$type',
                showConfirmButton: true,
                showCancelButton: false,
                confirmButtonText: 'ยอมรับเงื่อนไข',
                cancelButtonText: 'cancel',
				customClass: {
				content: 'left-align'
			  }
            }).then((result) => {
                if (result.isConfirmed && '$redirectUrl') {
                    window.location.href = '$redirectUrl';
                } else if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
                    window.location.href = '/WatchmanData/index.php';
                }
            });
        })
    </script>";
}
?>

<?php
function showSweetAlert3($title, $text, $type = 'success', $redirectUrl = null) {
    echo "<script src='/jQuery/jquery-3.5.1.min.js' type='text/javascript'></script>";
    echo "<script src='../SweetAlert2/dist/sweetalert2.all.min.js'></script>";
    echo "<script>
        $(document).ready(function() {
            Swal.fire({
                title: '$title',
                html: ` บันทึกข้อมูลเรียบร้อย <br><br>
                        โปรดบันทึกข้อมูล <br>
						ที่อยู่ และ โทรศัพท์ <br>
                        ของบุคคลนี้ ด้วย`,
                icon: '$type',
				timer: 30000,
                showConfirmButton: true,
                showCancelButton: false,
                confirmButtonText: 'OK',
                cancelButtonText: 'cancel'
            }).then((result) => {
                if (result.isConfirmed && '$redirectUrl') {
                    window.location.href = '$redirectUrl';
                } else if (result.isDismissed && result.dismiss === Swal.DismissReason.cancel) {
                    window.location.href = '/WatchmanData/index.php';
                }
            });
        })
    </script>";
}

?>

<?php
function DuplicateData($title, $text, $type = 'info', $redirectUrl = null) {
    echo '<script src="/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>';
    echo '<script src="../SweetAlert2/dist/sweetalert2.all.min.js"></script>';
    
    if (!empty($redirectUrl)) {
        echo '<script>
            $(document).ready(function() {
                Swal.fire({
                    title: \'' . $title . '\',
                    html: \'' . $text . '\',
                    icon: \'' . $type . '\',
                    showConfirmButton: true,
                    showCancelButton: true,
                    confirmButtonText: \'แก้ไข\',
                    cancelButtonText: \'cancel\'
                }).then((result) => {
                    if (result.isConfirmed) {
                        window.location.href = \'' . $redirectUrl . '\';
                    } else {
                        window.location.href = \'/WatchmanData/index.php\';
                    }
                });
            });
        </script>';
    }
}
?>

<?php
function AdrDuplicateData($title, $text, $type = 'info', $redirectUrl = null) {
    echo '<script src="/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>';
    echo '<script src="../SweetAlert2/dist/sweetalert2.all.min.js"></script>';
    
    if (!empty($redirectUrl)) {
        echo '<script>
            $(document).ready(function() {
                Swal.fire({
                    title: \'' . $title . '\',
                    html: \'' . $text . '\',
                    icon: \'' . $type . '\',
                    showConfirmButton: true,
                    showCancelButton: true,
                    confirmButtonText: \'แก้ไข\',
                    cancelButtonText: \'cancel\'
                }).then((result) => {
                    if (result.isConfirmed) {
                        window.location.href = \'' . $redirectUrl . '\';
                    } else {
                        window.location.href = \'/WatchmanData/Crime_Prevention_and_Suppression.php\';
                    }
                });
            });
        </script>';
    }
}
?>

<?php
function WrongData($title, $text, $type = 'warning', $redirectUrl = null) {
    echo '<script src="/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>';
    echo '<script src="../SweetAlert2/dist/sweetalert2.all.min.js"></script>';
    
    if (!empty($redirectUrl)) {
        echo '<script>
            $(document).ready(function() {
                Swal.fire({
                    title: \'' . $title . '\',
                    html: \'' . $text . '\',
                    icon: \'' . $type . '\',
                    showConfirmButton: true,
                    showCancelButton: true,
                    confirmButtonText: \'บันทึกข้อมูลใหม่\',
                    cancelButtonText: \'cancel\'
                }).then((result) => {
                    if (result.isConfirmed) {
                        window.location.href = \'' . $redirectUrl . '\';
                    } else {
                        window.location.href = \'/WatchmanData/Add_Personal.php\';
                    }
                });
            });
        </script>';
    }
}
?>


<script>
    // Check if the session variable is set to show the SweetAlert2
    if (<?php echo isset($_SESSION['show_sweet_alert']) ? 'true' : 'false'; ?>) {
        // Function to display the SweetAlert2
        function showSweetAlert() {
            Swal.fire({
                title: 'Successfully',
                text: 'ใช้ข้อมูล เพื่อการป้องกันปราบปรามอาชญากรรมเท่านั้น',
                icon: 'success',
                timer: 15000, // Set the duration (in milliseconds) for the alert to automatically close
            	timerProgressBar: true,
                showConfirmButton: false // Hide the "Confirm" button
            });
        }

        // Call the function to show the SweetAlert2
        showSweetAlert();

        // Clear the session variable after showing the alert
        <?php unset($_SESSION['show_sweet_alert']); ?>
    }
</script>
<?php
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
?>
<script>
    // Check if the SweetAlert message exists in the session
    <?php
    if (isset($_SESSION['message'])) {
        echo 'document.addEventListener("DOMContentLoaded", function() {';
        echo 'Swal.fire({
            title: "' . $_SESSION['success'] . '",
            text: "' . $_SESSION['message'] . '",
            icon: "success",
            confirmButtonText: "OK",
            timer: 15000, // 5 seconds delay before automatically redirecting
            timerProgressBar: true,
            allowOutsideClick: false
        }).then((result) => {
            if (result.dismiss === Swal.DismissReason.timer) {
                window.location.href = "/WatchmanData/main.php";
            }
        });';
        echo '});';
        unset($_SESSION['message']);
        unset($_SESSION['success']);
    }
    ?>
</script>