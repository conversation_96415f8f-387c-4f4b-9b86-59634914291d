<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../Alert.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);
$ip_address = $_SERVER['REMOTE_ADDR'];
$action = 'Save complaints'; // You can set the action to a suitable value
$data = str_replace("'", "", compact_array($_POST));
log_activity($acc, $action, $data, $ip_address); // ใช้ function ในไฟล์ Condb.php

/*echo '<pre>';
print_r($_POST);
echo '</pre>';

echo '<hr';

print_r($_FILES);
exit();*/

$mi_cl_aid = $_POST[ 'mi_cl_aid' ];
$mi_cl_date = $_POST[ 'mi_cl_date' ];
$mi_cl_time = $_POST[ 'mi_cl_time' ];
$mi_cl_mission = $_POST[ 'mi_cl_mission' ];
$mi_cl_mission_details = $_POST[ 'mi_cl_mission_details' ];
$mi_cl_worker = $_POST[ 'mi_cl_worker' ];		
$mi_cl_record = $_POST[ 'mi_cl_record' ];
$region = $_POST[ 'region' ];
$provincial = $_POST[ 'provincial' ];
$station = $_POST[ 'station' ];

// save file สส.1
$file1 = $_FILES[ 'mi_cl_file' ][ 'tmp_name' ];
if ( is_uploaded_file( $file1 ) ) {
  $mi_cl_file = "uploaded/Doc/" . $_FILES[ 'mi_cl_file' ][ 'name' ];	
  move_uploaded_file( $file1, "../" . $mi_cl_file );  
} else {
  $mi_cl_file = '';
}

try{
$sql ="SELECT * FROM wm_tb_mission WHERE mi_cl_aid = :mi_cl_aid ";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':mi_cl_aid', $mi_cl_aid);
    $stmt->execute();
    $row = $stmt->fetch(PDO::FETCH_ASSOC);

if($row) {
    $mi_cl_aid = $row['mi_cl_aid'];
    $sql = "UPDATE wm_tb_mission SET
            mi_cl_date = :mi_cl_date,
            mi_cl_time = :mi_cl_time,
            mi_cl_mission = :mi_cl_mission,
            mi_cl_mission_details = :mi_cl_mission_details,
            mi_cl_worker = :mi_cl_worker,
            mi_cl_record = :mi_cl_record,
			region = :region,
			provincial = :provincial,
			station = :station
			" ;
    
            $params = [
            'mi_cl_date' => $mi_cl_date,
            'mi_cl_time' => $mi_cl_time,
            'mi_cl_mission' => $mi_cl_mission,
            'mi_cl_mission_details' => $mi_cl_mission_details,
            'mi_cl_worker' => $mi_cl_worker,
            'mi_cl_record' => $mi_cl_record,
			'region' => $region,
			'provincial' => $provincial,
			'station' => $station
            ];
    
            if ($mi_cl_file !== '') {
            $sql .= ", mi_cl_file = :mi_cl_file";
            $params['mi_cl_file'] = $mi_cl_file;
            }

            $sql .= " WHERE mi_cl_aid = :updated_mi_cl_aid";
            $params['updated_mi_cl_aid'] = $mi_cl_aid; 

}else{
        $sql = "INSERT INTO wm_tb_mission (mi_cl_date, mi_cl_time, mi_cl_mission, mi_cl_mission_details, mi_cl_worker, mi_cl_record, region, provincial, station, mi_cl_file) 
        VALUES(:mi_cl_date, :mi_cl_time, :mi_cl_mission, :mi_cl_mission_details, :mi_cl_worker, :mi_cl_record, :region, :provincial, :station, :mi_cl_file) ";
            $params = [
            'mi_cl_date' => $mi_cl_date,
            'mi_cl_time' => $mi_cl_time,
            'mi_cl_mission' => $mi_cl_mission,
            'mi_cl_mission_details' => $mi_cl_mission_details,
            'mi_cl_worker' => $mi_cl_worker,
            'mi_cl_record' => $mi_cl_record,
			'region' => $region,
			'provincial' => $provincial,
			'station' => $station,
            'mi_cl_file' => $mi_cl_file
            ];
    }
    
$stmt = $pdo->prepare($sql);

try{
    $result = $stmt->execute($params);
}catch(PDOException $e){
    echo 'Query $result Failed: '. $e->getMessage();
}

    if ($result) {
        $_SESSION['success'] = "Data has been inserted succesfully";
        showSweetAlert("Saved successfully", "บันทึกข้อมูลเรียบร้อย");
        unset($_SESSION['success']); // Clear the session variable
        header("refresh:2; url=/Bansuan/index.php?rnd=<?= rand(); ?>&page=mission");
    } else {
        $_SESSION['error'] = "Error";
        showSweetAlert('Error', "บันทึกข้อมูล ไม่สำเร็จ", 'error');
        unset($_SESSION['error']); // Clear the session variable
        header("refresh:2; url=/Bansuan/index.php?rnd=<?= rand(); ?>&page=mission");
    }
}catch(PDOException $e){
    echo 'Query Failed: ' .$e->getMessage();
}

$pdo = null;

?>