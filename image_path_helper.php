<?php
/**
 * Helper function to fix image paths for proper display
 * แก้ไข path ของรูปภาพให้แสดงได้ถูกต้อง
 */

/**
 * แก้ไข path ของรูปภาพให้ถูกต้องสำหรับการแสดงผล
 * @param string $imagePath - path ของรูปภาพจากฐานข้อมูล
 * @return string - path ที่แก้ไขแล้ว
 */
function fixImagePath($imagePath) {
    if (empty($imagePath)) {
        return '';
    }
    
    // แก้ไข path ที่มี /policeinnopolis/ ให้เป็น path ที่ถูกต้อง
    $fixedPath = str_replace('/policeinnopolis/', '/', $imagePath);
    
    // แก้ไข path ที่มี ../policeinnopolis/ ให้เป็น path ที่ถูกต้อง
    $fixedPath = str_replace('../policeinnopolis/', '/', $fixedPath);
    
    return $fixedPath;
}

/**
 * สร้าง HTML img tag พร้อม path ที่ถูกต้อง
 * @param string $imagePath - path ของรูปภาพจากฐานข้อมูล
 * @param int $height - ความสูงของรูป (default: 50)
 * @param string $alt - alt text (default: 'Image')
 * @param string $class - CSS class (default: '')
 * @return string - HTML img tag
 */
function createImageTag($imagePath, $height = 50, $alt = 'Image', $class = '') {
    if (empty($imagePath)) {
        return '';
    }
    
    $fixedPath = fixImagePath($imagePath);
    $classAttr = !empty($class) ? " class=\"{$class}\"" : '';
    
    return "<img src=\"{$fixedPath}\" height=\"{$height}\" alt=\"{$alt}\"{$classAttr}>";
}

/**
 * ตรวจสอบว่าไฟล์รูปภาพมีอยู่จริงหรือไม่
 * @param string $imagePath - path ของรูปภาพ
 * @return bool - true ถ้าไฟล์มีอยู่
 */
function imageExists($imagePath) {
    if (empty($imagePath)) {
        return false;
    }
    
    // แปลง path สำหรับตรวจสอบไฟล์ในระบบ
    $systemPath = str_replace('/', DIRECTORY_SEPARATOR, $imagePath);
    
    // ถ้า path เริ่มต้นด้วย / ให้เอาออก
    if (substr($systemPath, 0, 1) === DIRECTORY_SEPARATOR) {
        $systemPath = substr($systemPath, 1);
    }
    
    return file_exists($systemPath);
}
?>
