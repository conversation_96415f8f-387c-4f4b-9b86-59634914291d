<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../right_user.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

$pcid = $_GET['pcid'];
//$pcid = *************;

?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link rel="stylesheet" href="/bootstrap/css/bootstrap-grid.css">
<title>อัยการ22</title>
<style>
    @media print {
          @page {
            size: A4;
            margin: 20mm;
			/*a[href]:after { content: none !important; }
  			img[src]:after { content: none !important; }*/
          }

		  body {
			margin: 0;
			padding: 0;
		  }

			body::after {
				content: none !important;
				display: none !important;
			}
		footer {
				content: none !important;
		}
		
          .header {
            text-align: center;
            margin-bottom: 1cm;
          }

           .footer {
            text-align: left;
            margin-bottom: 1cm;
          }
        
         .page-break {
            page-break-after: always;
          }
         .no-print {
                display: none;
              }
            }

            @media screen {
              .no-screen {
                display: none;
              }
    }
    
    h2{
        font-family : "TH SarabunIT๙","TH Sarabun New", "TH SarabunPSK" ;
        font-size: 24.0pt;
    }
    h3{
        font-family : "TH SarabunIT๙","TH Sarabun New", "TH SarabunPSK" ;
        font-size: 22.0pt;
    }
    h4{
        font-family : "TH SarabunIT๙","TH Sarabun New", "TH SarabunPSK" ;
        font-size: 20.0pt;
    }
    h5{
        font-family : "TH SarabunIT๙","TH Sarabun New", "TH SarabunPSK" ;
        font-size: 18.0pt;
    }
    p {
        font-family : "TH SarabunIT๙","TH Sarabun New", "TH SarabunPSK" ;
        font-size: 16.0pt;
        line-height: normal;
		margin-top: 10px;
    }
.image-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 10px;
  
  /*display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh; /* Adjust this value if needed */*/
}    
.row {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
}

.column {
  position: relative;
  overflow: hidden;
  padding-bottom: 80%; /* Adjust this percentage to control the visible portion of the image */
  transition: 0.3s;
}

    .column img:hover{
        border: 2px solid red;
    }
.column img {
  position: absolute;
  top: 0;
  left: 0;
  width: 80%;
  object-fit: cover;
  padding: 30px;
  transition: 0.3s;
}

.u-underline {
      text-decoration: underline;
      text-decoration-color: gray;
      text-decoration-style: dotted;
      text-decoration-thickness: 1px;
    }
    
.lbox3 {
        font-family: "TH SarabunIT๙","TH Sarabun New", "TH SarabunPSK";
        font-size: 15.0pt;
        padding-left: .5rem;
		margin-top: 10px;
		margin-bottom: 0px;
    }
    
	#img3 {
		padding-top: 10px;
		margin-top: 10px;
	}
</style>
</head>

<body>
<?php
$sql = "SELECT *,
            PS.ps_cl_prefix, PS.ps_cl_name, PS.ps_cl_surname, PS.station, PS.ps_cl_image,
            Adr.ad_cl_num, Adr.ad_cl_moo, Adr.ad_cl_tumbon, Adr.ad_cl_amphur, Adr.ad_cl_province,
            Tb.tb_name_th, 
            Ap.ap_name_th, 
            Pv.pv_name_th,
            St.station_name,
            Ch.ch_cl_crimes_detail, Ch.ch_cl_pol_remark, Ch.ch_cl_Moo, Ch.ch_cl_Tumbon, Ch.ch_cl_Amphur, Ch.ch_cl_Province, Ch.station ST, 
            St2.station_name ST2, St2.tumbon_st stTb, St2.ampher_st stAp, St2.prov_st stPv,
            Tb2.tb_name_th tb2, 
            Ap2.ap_name_th ap2, 
            Pv2.pv_name_th pv2
        FROM DirectoryDetention22 Dir
            LEFT JOIN wm_tb_personal PS ON PS.ps_cl_idcard = Dir.idcard
            LEFT JOIN wm_tb_address Adr ON Adr.ad_cl_idcard = Dir.idcard
            LEFT JOIN provinces AS Pv ON Pv.pv_code = Adr.ad_cl_province
            LEFT JOIN amphures AS Ap ON Ap.ap_code = Adr.ad_cl_amphur
            LEFT JOIN districts AS Tb ON Tb.tb_id = Adr.ad_cl_tumbon
            LEFT JOIN wm_tb_police_station2 AS St ON St.station_code = PS.station
            LEFT JOIN wm_tb_crimes_history AS Ch ON Ch.ch_cl_idcard = Dir.idcard
            LEFT JOIN provinces AS Pv2 ON Pv2.pv_code = Ch.ch_cl_Province
            LEFT JOIN amphures AS Ap2 ON Ap2.ap_code = Ch.ch_cl_Amphur
            LEFT JOIN districts AS Tb2 ON Tb2.tb_id = Ch.ch_cl_Tumbon
            LEFT JOIN wm_tb_police_station2 AS St2 ON St2.station_code = Ch.station
        WHERE Dir.station=:station AND Dir.idcard=:pcid 
        ORDER BY Ch.ch_cl_aid DESC
        LIMIT 1;
        ";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':pcid', $pcid);
	$stmt->bindParam(':station', $station);
try{
    $stmt->execute();
}catch (PDOException $e) {
    echo '$sql Failed: '. $e->getMessage();
}
$no = 1;
while($row = $stmt->fetch(PDO::FETCH_ASSOC))	//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
{
 
//วันแจ้งฝ่ายปกครอง
if(!empty($row["gov_date"])){
    $govDate = DateThai( $row["gov_date"] );
}else{
    $govDate = '';
}
    
// วันแจ้งอัยการ
if(!empty($row["att_date"])){
    $attDate = DateThai( $row["att_date"] );
}else{
    $attDate = '';
}
    
// วันควบคุมตัว
if(!empty($row["ch_cl_date"])){
    $ChDate = DateThai( $row["ch_cl_date"] );
}else{
    $ChDate = '';
}
// อายุ
if($row["ps_cl_birthday2"] > 0){
        $age = get_personal_age_2( $row["ps_cl_birthday2"] );
        $age_display = $age . " ปี";
    }else{
        $age_display = '-';
    }
//ตรวจสอบไฟล์ สส.1 ก่อนว่า มีไฟล์ในตารางไหม หากมี ให้ส่งเป็นลิงค์
	$govPdf = $row["gov_pdf"];
	if($govPdf !== '')
	{
		$links_gov = '<a href="/' . $row["gov_pdf"] .'" target="_blank">Download</a>';
	}
	else {
		$links_gov = 'ไม่มีไฟล์';
	}
    
//ตรวจสอบไฟล์ สส.1 ก่อนว่า มีไฟล์ในตารางไหม หากมี ให้ส่งเป็นลิงค์
	$attPdf = $row["att_pdf"];
	if($attPdf !== '')
	{
		$links_att = '<a href="/' . $row["att_pdf"] .'" target="_blank">Download</a>';
	}
	else {
		$links_att = 'ไม่มีไฟล์';
	}
 
//ที่อยู่
    $adr = $row["ad_cl_num"] . ' หมู่ '.  $row["ad_cl_moo"]. ' ต.'. $row["tb_name_th"]. ' อ.'. $row["ap_name_th"]. ' จ.'. $row["pv_name_th"];
    
//ที่อยู่ จับกุม
    $adr_catch = $row["ch_cl_number"]. ' หมู่ '.  $row["ch_cl_Moo"];
    
// เปลี่ยนรูปแบบ เลขบัตรประชาชน
$id_number = $row['ps_cl_idcard'];
$formatted_id = $id_number[0] . "-" . substr($id_number, 1, 4) . "-" . substr($id_number, 5, 5) . "-" . substr($id_number, 10, 2) . "-" . $id_number[12];
    
// ภาพด้านหน้า
$image1 = $row["image1"];
if($image1 != '') {
	$image1 = "<img src='{$image1}' width='90%'> ";
}
// ภาพด้านหลัง
$image2 = $row["image2"];
if($image2 != '') {
	$image2 = "<img src='{$image2}' width='90%'> ";
}
// ภาพด้านหน้า
$image3 = $row["image3"];
if($image3 != '') {
	$image3 = "<img src='{$image3}' width='90%'> ";
}
// ภาพด้านหน้า
$image4 = $row["image4"];
if($image4 != '') {
	$image4 = "<img src='{$image4}' width='90%'> ";
}
    
?>
    
<h2 align="center">แบบแจ้งเรื่องการควบคุมตามมาตรา ๒๒ วรรคสอง<br>******************************************************</h2>
    
    <div class="lbox3">
            <p>
                รายการข้อมูลในการแจ้งการจับและควบคุมไปยังพนักงานอัยการ และนายอำเภอในท้องที่ ที่มีการควบคุมตัว และสำหรับในกรุงเทพมหานครให้แจ้งพนักงานอัยการ และผู้อำนวยการสำนักการสอบสวนและนิติการ กรมการปกครอง ผ่านทางศูนย์รับแจ้งโดยทันทีที่มีข้อมูลครบถ้วน ดังต่อไปนี้
            </p>
            <p>(๑) ข้อมูลของผู้ถูกจับและควบคุม (หากไม่ทราบให้ระบุว่าไม่ทราบ)</p>
            <p><dd>ชื่อ-นามสกุล :&nbsp;&nbsp;&nbsp; <u class="u-underline"> <b>&nbsp;&nbsp;&nbsp;<?= $row['ps_cl_prefix'] ?><?= $row['ps_cl_name'] ?>&nbsp;<?= $row['ps_cl_surname'] ?></b> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </u> </dd></p>
            <p><dd>หมายเลขประจำตัวประชาชน :&nbsp;&nbsp;&nbsp; <u class="u-underline"> <b>&nbsp;&nbsp;&nbsp;<?= $formatted_id ?> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </u></b></p>
            <p><dd>ที่อยู่ :&nbsp;&nbsp;&nbsp; <u class="u-underline"> <b>&nbsp;&nbsp;&nbsp;<?= $adr ?> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </u></b></dd></p>
            <p>(๒) วัน เวลา และสถานที่ที่ทำการจับและควบคุม</p>
            <p><dd>วันที่ :&nbsp;&nbsp;&nbsp; <u class="u-underline"> <b>&nbsp;&nbsp;&nbsp;<?= $ChDate ?> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </u></b> &nbsp;&nbsp;&nbsp; เวลา :&nbsp;&nbsp;&nbsp; <u class="u-underline"> <b>&nbsp;&nbsp;&nbsp;<?= $row["ch_cl_time"] ?> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </u></b></dd></p>
            <p><dd>สถานที่จับและควบคุม :&nbsp;&nbsp;&nbsp; <u class="u-underline"> <b>&nbsp;&nbsp;&nbsp; <?= $adr_catch ?> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </u></b></dd></p>
            <p><dd>ตำบล&nbsp;&nbsp;&nbsp; <u class="u-underline"> <b>&nbsp;&nbsp;&nbsp; <?= $row["tb2"] ?> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </u></b> &nbsp;&nbsp; อำเภอ &nbsp;&nbsp;&nbsp; <u class="u-underline"> <b>&nbsp;&nbsp;&nbsp; <?= $row["ap2"] ?>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </u></b> &nbsp;&nbsp; จังหวัด &nbsp;&nbsp;&nbsp; <u class="u-underline"> <b>&nbsp;&nbsp;&nbsp; <?= $row["pv2"] ?> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </u></b></dd></p>
            <p>(๓) พฤติการณ์ในการจับและควบคุมบุคคลดังกล่าวโดยย่อ :&nbsp;&nbsp; <u class="u-underline"> <b><?= $row['ch_cl_crimes_detail']?> &nbsp;&nbsp;</u></b></p>
            <p>(๔) สถานที่ควบคุมตัวบุคคลดังกล่าวไว้</p>
            <p><dd>สถานที่ควบคุมตัว(สน./สภ./กก.) :&nbsp;&nbsp;&nbsp; <u class="u-underline"> <b>&nbsp;&nbsp;&nbsp; <?= $row["ST2"] ?> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </u></b></dd></p>
            <p><dd>ตำบล&nbsp;&nbsp;&nbsp; <u class="u-underline"> <b>&nbsp;&nbsp;&nbsp; <?= $row["stTb"] ?> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </u></b> &nbsp;&nbsp; อำเภอ&nbsp;&nbsp;&nbsp; <u class="u-underline"> <b>&nbsp;&nbsp;&nbsp; <?= $row["stAp"] ?> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </u></b> &nbsp;&nbsp; จังหวัด&nbsp;&nbsp;&nbsp; <u class="u-underline"> <b>&nbsp;&nbsp;&nbsp; <?= $row["stPv"] ?> &nbsp;&nbsp;</u></b></dd></p>
		</div>
		
	<div class="page-break"></div>
		<div class="lbox3">
			
            <p>(๕) ภาพถ่ายผู้ถูกจับและควบคุม ตามข้อ (๑) (เป็นภาพแนบท้าย)</p>		
            <p>(๖) ชื่อเจ้าหน้าที่รัฐผู้รับผิดชอบที่ทำการจับและควบคุมตามมาตรา ๒๒ (ชื่อ ตำแหน่ง และหมายเลขโทรศัพท์ที่สามารถติดต่อได้)</p>
            <p><dd>ชื่อ-นามสกุล :&nbsp;&nbsp;&nbsp; <u class="u-underline"> <b>&nbsp;&nbsp;&nbsp; <?= $row['pol_arrest'] ?> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </u></b></dd></p>
            <p><dd>ตำแหน่ง :&nbsp;&nbsp;&nbsp; <u class="u-underline"> <b>&nbsp;&nbsp;&nbsp; <?= $row['position_arrest'] ?> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </u></b></dd></p>
            <p><dd>หมายเลขโทรศัพท์ที่สามารถติดต่อได้ :&nbsp;&nbsp;&nbsp; <u class="u-underline"> <b>&nbsp;&nbsp;&nbsp; <?= $row['phone_arrest'] ?> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </u></b> </dd></p>
            <p>(๗) เหตุสุดวิสัยในกรณีที่ไม่สามารถบันทึกภาพและเสียงได้ในขณะจับและควบคุม (ถ้ามี) :&nbsp;&nbsp;&nbsp; <u class="u-underline"> <b>&nbsp;&nbsp;&nbsp; <?= $row['remark']?> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </u></b></p>
            <p>(๘) ชื่อเจ้าหน้าที่ของรัฐผู้แจ้ง (ชื่อ ตำแหน่ง และหมายเลขโทรศัพท์ที่สามารถติดต่อได้)</p>
            <p><dd>ชื่อ-นามสกุล :&nbsp;&nbsp;&nbsp; <u class="u-underline"> <b>&nbsp;&nbsp;&nbsp; <?= $row['pol_arrest'] ?> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </u></b></dd></p>
            <p><dd>ตำแหน่ง  :&nbsp;&nbsp;&nbsp; <u class="u-underline"> <b>&nbsp;&nbsp;&nbsp; <?= $row['position_arrest'] ?> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </u></b> </dd></p>
            <p><dd>หมายเลขโทรศัพท์ที่สามารถติดต่อได้ :&nbsp;&nbsp;&nbsp; <u class="u-underline"> <b>&nbsp;&nbsp;&nbsp; <?= $row['phone_arrest'] ?> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </u></b> </dd></p>
        </div>

    <div class="page-break"></div>
    
    

<div class="container">
    <h2 align="center">ภาพถ่ายผู้ถูกจับกุม/ควบคุมตัว</h2>
        <div class="image-grid">
          <div class="row">
            <div class="column">
                <div><?= $image1 ?> </div>
                <div align="center">ภาพด้านหน้า</div>
                <div></div>
            </div>
            <div class="column" id="img3">
              <div><?= $image3 ?> </div>
              <div align="center">ภาพด้านขวา</div>
                <div></div>
            </div>
          </div>
          <div class="row">
            <div class="column">
              <div><?= $image2 ?> </div>
              <div align="center">ภาพด้านหลัง</div>
                <div></div>
            </div>
            <div class="column" id="img3">
              <div><?= $image4 ?> </div>
              <div align="center">ภาพด้านซ้าย</div>
              <div></div>
            </div>
          </div>
        </div>
	<!--&nbsp;&nbsp;<button class="btn btn-primary btn-lg mb-4" onClick="do_print()"> พิมพ์รายงาน </button> -->
</div>
<?php
    $no++;
}
?>
</body>
</html>
<!-- script สำหรับส่งไปพิมพ์รายงาน  -->
<!--<script>
    
    function do_print()
    {
        var ys = $("#changeyear option:selected").val();
        var newW = window.open("/Bansuan/ago22_pdf.php?&rnd=" + Math.random() , "_print", "menubar=no,titlebar=no,toolbar=no,status=no");
        newW.print();
        //window.print();
    }
    
</script>-->