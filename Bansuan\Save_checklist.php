<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../Alert.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);


/*$pcid = $_GET['pcid'];
echo '<pre>';
print_r($_POST);
echo '</pre>';

echo '<hr>';
exit();*/

$ck_aid = $_POST['ck_aid'];
$ck_idcard = $_POST['ck_idcard'];
$ck_checkbox = $_POST['ck_checkbox'];
$ck_detective = $_POST['ck_detective'];
$ck_date = $_POST['ck_date'];

// Convert the checkbox array into a comma-separated string before saving to the database
$ck_checklist = implode(",", $_POST['ck_checkbox']);

// Check if the ID card already exists in the database
$sql = "SELECT * FROM wm_tb_warrant_checklist WHERE ck_idcard = :ck_idcard";
$stmt = $pdo->prepare($sql);
$stmt->bindParam(':ck_idcard', $ck_idcard);

try {
    $stmt->execute();
} catch (PDOException $e) {
    echo $e->getMessage();
}

$row1 = $stmt->fetch(PDO::FETCH_ASSOC);

if ($row1) {
    // If the ID card exists, perform an update
    $ck_aid = $row1['ck_aid'];
    $sql = "UPDATE wm_tb_warrant_checklist SET ".
            "ck_idcard = :ck_idcard, " .
            "ck_checkbox = :ck_checklist, " .
            "ck_detective = :ck_detective, " .
            "ck_date = :ck_date " .
            "WHERE ck_aid = :ck_aid";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':ck_aid', $ck_aid);
    $stmt->bindParam(':ck_idcard', $ck_idcard);
    $stmt->bindParam(':ck_checklist', $ck_checklist); // Updated parameter name
    $stmt->bindParam(':ck_detective', $ck_detective);
    $stmt->bindParam(':ck_date', $ck_date);
} else {
    // If the ID card does not exist, perform an insert
    $sql = "INSERT INTO wm_tb_warrant_checklist (ck_idcard, ck_checkbox, ck_detective, ck_date) 
            VALUES(:ck_idcard, :ck_checklist, :ck_detective, :ck_date)";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':ck_idcard', $ck_idcard);
    $stmt->bindParam(':ck_checklist', $ck_checklist); // Updated parameter name
    $stmt->bindParam(':ck_detective', $ck_detective);
    $stmt->bindParam(':ck_date', $ck_date);
}

try {
    $result = $stmt->execute();
} catch (PDOException $e) {
    echo '$result Failed: ' . $e->getMessage();
}

$ip_address = $_SERVER['REMOTE_ADDR'];
$action = "Save checklist wanted : $ck_idcard"; // You can set the action to a suitable value
$data = str_replace("'", "", compact_array($_POST));
log_activity($acc, $action, $data, $ip_address);


if ($result) {
    $_SESSION['success'] = "Data has been inserted succesfully";
    showSweetAlert("Saved successfully", "บันทึกข้อมูลเรียบร้อย");
    unset($_SESSION['success']); // Clear the session variable
    header("refresh:2; url=/Bansuan/Checklist_wanted.php?pcid={$ck_idcard}&page=warrant");
} else {
    $_SESSION['error'] = "Error";
    showSweetAlert('Error', "บันทึกข้อมูล ไม่สำเร็จ", 'error');
    unset($_SESSION['error']); // Clear the session variable
    header("refresh:2; url=/Bansuan/Checklist_wanted.php?pcid={$ck_idcard}&page=warrant");
}

$pdo = null;

?>