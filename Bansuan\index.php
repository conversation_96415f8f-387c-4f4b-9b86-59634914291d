<?php
// ตรวจสอบเวลาการใช้งาน หากไม่มีการใช้งาน เกิน 15 นาที ให้ตัดออกจากระบบที่ login
/*$_SESSION['last_active'] = time();

if(isset($_SESSION['user'])) {
    if (time() - $_SESSION['last_active'] > 300) { // 900 seconds = 15 minutes
        session_destroy();
        header("Location: /WatchmanData/index.php");
        exit;
    }
    else {
        $_SESSION['last_active'] = time();
    }
}
*/

include "../Condb.php"; //PDO
require_once('../users.inc.php');
include '../right_user.php';
include '../Head_image.php';
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

if(empty($acc))
{
	header("Location: /WatchmanData/index.php");
}

$page = isset($_GET['page']) ? $_GET['page'] : '';

?>

<!DOCTYPE html>
<html>
    <head>
        <title>ระบบ WatchmanDB</title>
		<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<!-- Add the following meta tag to disable zooming on mobile devices -->
		<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <base target="_top">
		<link rel="stylesheet" href="/bootstrap/css/bootstrap.rtl.min.css">
		<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/css/bootstrap.min.css">
  		<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
 		<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.4.1/js/bootstrap.min.js"></script>
		
		<script src="../SweetAlert2/dist/sweetalert2.min.js"></script>
		<link rel="stylesheet" href="../SweetAlert2/dist/sweetalert2.min.css">
		
        <style>
            body {
              font-family: 'Courier New', Courier, monospace;
              font-size: 16px;
            }
			.dropdown {
				display: none;
			}
			.dropdown-menu1 {
				display: none;
			}
			/* Media query for smaller screen sizes (mobile devices) */
    @media (max-width: 768px) {
        /* Style the menu items as a dropdown */
        .nav-tabs {
            display: none;
        }
		.nav {
			display: none;
		}

		.dropdown {
            display: block;
            margin-bottom: 15px;
        }
		
        .dropdown-menu1 {
            display: block;
            margin-bottom: 15px;
        }
			.dropdown-menu2 {
			display: none;
		}

		ul li ul {
        min-width: 100%; /* Set width of the dropdown */
        background: #f2f2f2;
        display: none;
        position: absolute;
        z-index: 999;
        left: 0;
		}
		ul li:hover ul {
			display: block; /* Display the dropdown */
		}
		ul li ul li {
			display: block;
		}
			
        </style>
    </head>
    <body class="container-fluid">
		<div>
            <!--<img src="../Image/Head2.jpg" alt="Head สภ.บ้านสวน" width="100%" >-->
			<!--<img src="../Image/WatchmanDB3.jpg" alt="Head สภ.บ้านสวน" width="100%" >-->
		</div>
		<div>
            <?php
			include('../users_info.php');
			?>	
		</div>
		  <div class="container-fluid">
			<div class="nav nav-tabs navbar-left">
			<!--  <li><a href="#"><span class="glyphicon glyphicon-user"></span> Sign Up</a></li>
			  <li><a href="#"><span class="glyphicon glyphicon-log-in"></span> Login</a></li>   -->
			</div>
            
        <nav>
			<div class="nav nav-tabs" >
            <?php
                $active1 = '';
                $active2 = '';
                $active3 = '';
                $active4 = '';
                $active5 = '';
                $active6 = '';
                $active7 = '';
                $active8 = '';
                $active9 = '';
                $active10 = '';
                $active11 = '';
                $active12 = '';
                $active13 = '';
                $active14 = '';
                $active15 = '';
                $active16 = '';
                $active17 = '';
                $active18 = '';
                $active19 = '';
                $active20 = '';
                $active21 = '';
                $active22 = '';
                $active23 = '';
				$active24 = '';
                
            if (($page == '') || ($page == 'home') || ($page == 'สืบบ้านสวน')) {
                  $active1 = " class='active btn-primary' ";
              }
              elseif($page == 'wanted') {
                  $active2 = " class='btn btn-primary btn-lg active' ";
              }
              elseif($page == 'freed') {
                  $active3 = " class='btn btn-primary btn-lg active' ";
              }
              elseif($page == 'complain') {
                  $active4 = " class='btn btn-primary btn-lg active' ";
              }
              elseif($page == 'investigating') {
                  $active5 = " class='btn btn-primary btn-lg active' ";
              }
              elseif($page == 'law_order') {
                  $active6 = " class='btn btn-primary btn-lg active' ";
              }
              elseif($page == 'suspect') {
                  $active7 = " class='btn btn-primary btn-lg active' ";
              }
              elseif($page == 'foreigner') {
                  $active8 = " class='btn btn-primary btn-lg active' ";
              }
              elseif($page == 'criminalfund') {
                  $active9 = " class='btn btn-primary btn-lg active' ";
              }
              elseif($page == 'information') {
                  $active10 = " class='btn btn-primary btn-lg active' ";
              }
              elseif($page == 'crimesmaping') {
                  $active11 = " class='btn btn-primary btn-lg active' ";
              }
              elseif($page == 'performance') {
                  $active12 = " class='btn btn-primary btn-lg active' ";
              }
              elseif($page == 'mission') {
                  $active13 = " class='btn btn-primary btn-lg active' ";
              }
              elseif($page == 'complaints') {
                  $active14 = " class='btn btn-primary btn-lg active' ";
              }
              elseif($page == 'online') {
                  $active15 = " class='btn btn-primary btn-lg active' ";
              }
              elseif($page == 'blockade') {
                  $active16 = " class='btn btn-primary btn-lg active' ";
              }
              elseif($page == 'doc') {
                  $active17 = " class='btn btn-primary btn-lg active' ";
              }
              elseif($page == 'program') {
                  $active18 = " class='btn btn-primary btn-lg active' ";
              }
              elseif($page == 'study') {
                  $active19 = " class='btn btn-primary btn-lg active' ";
              }
              elseif($page == 'knowledge') {
                  $active20 = " class='btn btn-primary btn-lg active' ";
              }
              elseif($page == 'report') {
                  $active21 = " class='btn btn-primary btn-lg active' ";
              }
              elseif($page == 'tool') {
                  $active22 = " class='btn btn-primary btn-lg active' ";
              }
              elseif($page == 'Directory') {
                  $active23 = " class='btn btn-primary btn-lg active' ";
              }
			  elseif($page == 'ST_report') {
                  $active24 = " class='btn btn-primary btn-lg active' ";
              }
                ?>
				
	<div class="dropdown">
		<button class="btn btn-light btn-lg mb-4 dropdown-toggle" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" onclick="toggleMenu()"> เมนู </button>
			<div class="dropdown-menu1" aria-labelledby="dropdownMenuButton">
				<ul>
					<li <?= $active1 ?>><a href="index.php?rnd=<?= rand(); ?>&page=home" class="fw-bold" >Home</a></li>
					<li <?= $active2 ?>><a href="index.php?rnd=<?= rand(); ?>&page=wanted" class="fw-bold" style="color: black">แฟ้มหมายจับ</a></li>
					<li <?= $active3 ?>><a href="index.php?rnd=<?= rand(); ?>&page=freed" class="fw-bold" style="color: black">แฟ้มบุคคลพ้นโทษ</a></li>
					<li <?= $active4 ?>><a href="index.php?rnd=<?= rand(); ?>&page=complain" class="fw-bold" style="color: black">แฟ้มเรื่องร้องเรียน</a></li>
					<li <?= $active5 ?>><a href="index.php?rnd=<?= rand(); ?>&page=investigating" class="fw-bold" style="color: black">แฟ้มสืบสวนคดี</a></li>
					<li <?= $active6 ?>><a href="index.php?rnd=<?= rand(); ?>&page=law_order" class="fw-bold" style="color: black">แฟ้มระเบียบคำสั่ง</a></li> 
					<li <?= $active7 ?>><a href="index.php?rnd=<?= rand(); ?>&page=suspect" class="fw-bold" style="color: black">แฟ้มยึดรถ</a></li>
					<li <?= $active8 ?>><a href="index.php?rnd=<?= rand(); ?>&page=foreigner" class="fw-bold" style="color: black">แฟ้มต่างด้าว</a></li>
					<li <?= $active9 ?>><a href="index.php?rnd=<?= rand(); ?>&page=criminalfund" class="fw-bold" style="color: black">แฟ้มกองทุนสืบสวน</a></li>
					<li <?= $active10 ?>><a href="index.php?rnd=<?= rand(); ?>&page=information" class="fw-bold"><b>ฐานข้อมูลท้องถิ่น</b></a></li>
					<li <?= $active11 ?>><a href="index.php?rnd=<?= rand(); ?>&page=crimesmaping" class="fw-bold"><b>ฐานข้อมูลคดีอาญา</b></a></li>
					<li <?= $active12 ?>><a href="index.php?rnd=<?= rand(); ?>&page=performance" class="fw-bold">ผลงาน</a></li>
					<li <?= $active13 ?>><a href="index.php?rnd=<?= rand(); ?>&page=mission" class="fw-bold">ภารกิจ</a></li>
					<li <?= $active14 ?>><a href="index.php?rnd=<?= rand(); ?>&page=complaints" class="fw-bold">รับแจ้งเหตุ</a></li>
					<li <?= $active15 ?>><a href="index.php?rnd=<?= rand(); ?>&page=online" class="fw-bold">คดีออนไลน์</a></li>
					<li <?= $active16 ?>><a href="index.php?rnd=<?= rand(); ?>&page=blockade" class="fw-bold">ปิดล้อมตรวจค้น</a></li>
					<li <?= $active17 ?>><a href="index.php?rnd=<?= rand(); ?>&page=doc" class="fw-bold">เอกสารใช้งาน</a></li>
					<li <?= $active18 ?>><a href="index.php?rnd=<?= rand(); ?>&page=program" class="fw-bold">Program</a></li>
					<li <?= $active19 ?>><a href="index.php?rnd=<?= rand(); ?>&page=study" class="fw-bold">StudyCase</a></li>
					<li <?= $active20 ?>><a href="index.php?rnd=<?= rand(); ?>&page=knowledge" class="fw-bold">Knowledge</a></li> 
					<li <?= $active22 ?>><a href="index.php?rnd=<?= rand(); ?>&page=tool" class="fw-bold">เครื่องมือนักสืบ</a></li>
					<li <?= $active21 ?>><a href="index.php?rnd=<?= rand(); ?>&page=report" class="fw-bold">รายงานประจำเดือน</a></li>
					<li <?= $active23 ?>><a href="index.php?rnd=<?= rand(); ?>&page=Directory" class="fw-bold">รายงานปกครอง+อัยการ</a></li>
					<?php if($provincial == 67) { 
					echo '<li <?= $active24 ?>><a href="index.php?rnd=<?= rand(); ?>&page=ST_report" class="fw-bold">สำหรับ ภ.จว.</a></li>';
					}?>
					
				</ul>
            </div>
	</div>
				
		<!--	<ul class="nav navbar-nav"> -->
			  <div class="row nav-bar"><a href="../WatchmanData/main.php" class="btn btn-primary btn-lg mb-4" >หน้าหลัก</a></div>
				<li <?= $active1 ?>><a href="index.php?rnd=<?= rand(); ?>&page=home" class="fw-bold" >Home</a></li>
                <li <?= $active2 ?>><a href="index.php?rnd=<?= rand(); ?>&page=wanted" class="fw-bold" style="color: black">แฟ้มหมายจับ</a></li>
				<li <?= $active3 ?>><a href="index.php?rnd=<?= rand(); ?>&page=freed" class="fw-bold" style="color: black">แฟ้มบุคคลพ้นโทษ</a></li>
                <li <?= $active4 ?>><a href="index.php?rnd=<?= rand(); ?>&page=complain" class="fw-bold" style="color: black">แฟ้มเรื่องร้องเรียน</a></li>
                <li <?= $active5 ?>><a href="index.php?rnd=<?= rand(); ?>&page=investigating" class="fw-bold" style="color: black">แฟ้มสืบสวนคดี</a></li>
			 	<li <?= $active6 ?>><a href="index.php?rnd=<?= rand(); ?>&page=law_order" class="fw-bold" style="color: black">แฟ้มระเบียบคำสั่ง</a></li> 
                <li <?= $active7 ?>><a href="index.php?rnd=<?= rand(); ?>&page=suspect" class="fw-bold" style="color: black">แฟ้มยึดรถ</a></li>
                <li <?= $active8 ?>><a href="index.php?rnd=<?= rand(); ?>&page=foreigner" class="fw-bold" style="color: black">แฟ้มต่างด้าว</a></li>
                <li <?= $active9 ?>><a href="index.php?rnd=<?= rand(); ?>&page=criminalfund" class="fw-bold" style="color: black">แฟ้มกองทุนสืบสวน</a></li>
				<li <?= $active10 ?>><a href="index.php?rnd=<?= rand(); ?>&page=information" class="fw-bold"><b>ฐานข้อมูลท้องถิ่น</b></a></li>
                <li <?= $active11 ?>><a href="index.php?rnd=<?= rand(); ?>&page=crimesmaping" class="fw-bold"><b>ฐานข้อมูลคดีอาญา</b></a></li>
				<li <?= $active12 ?>><a href="index.php?rnd=<?= rand(); ?>&page=performance" class="fw-bold">ผลงาน</a></li>
                <li <?= $active13 ?>><a href="index.php?rnd=<?= rand(); ?>&page=mission" class="fw-bold">ภารกิจ</a></li>
				<li <?= $active14 ?>><a href="index.php?rnd=<?= rand(); ?>&page=complaints" class="fw-bold">รับแจ้งเหตุ</a></li>
                <li <?= $active15 ?>><a href="index.php?rnd=<?= rand(); ?>&page=online" class="fw-bold">คดีออนไลน์</a></li>
                <li <?= $active16 ?>><a href="index.php?rnd=<?= rand(); ?>&page=blockade" class="fw-bold">ปิดล้อมตรวจค้น</a></li>
                <li <?= $active17 ?>><a href="index.php?rnd=<?= rand(); ?>&page=doc" class="fw-bold">เอกสารใช้งาน</a></li>
                <li <?= $active18 ?>><a href="index.php?rnd=<?= rand(); ?>&page=program" class="fw-bold">Program</a></li>
                <li <?= $active19 ?>><a href="index.php?rnd=<?= rand(); ?>&page=study" class="fw-bold">StudyCase</a></li>
				<li <?= $active20 ?>><a href="index.php?rnd=<?= rand(); ?>&page=knowledge" class="fw-bold">Knowledge</a></li> 
                <li <?= $active22 ?>><a href="index.php?rnd=<?= rand(); ?>&page=tool" class="fw-bold">เครื่องมือนักสืบ</a></li>
                <li <?= $active21 ?>><a href="index.php?rnd=<?= rand(); ?>&page=report" class="fw-bold">รายงานประจำเดือน</a></li>
                <li <?= $active23 ?>><a href="index.php?rnd=<?= rand(); ?>&page=Directory" class="fw-bold">รายงานปกครอง+อัยการ</a></li>
				<?php if($provincial == 67) { 
					echo '<li <?= $active24 ?><a href="Show_ST_report.php" class="fw-bold">สำหรับ-ภ.จว.</a></li>';
					}?>
            </div>
		</nav>
		<div class="container-fluid">
    <?php
	  
		
	  if (($page == '') || ($page == 'home') || ($page == 'สืบบ้านสวน'))
	  {
		  include("home.php");
          $active1 = " class='active' ";
	  }
	  elseif($page == 'wanted') {
		  include("wanted.php");
          $active2 = " class='active' ";
	  }
      elseif($page == 'freed') {
		  include("freed.php");
          $active3 = " class='active' ";
	  }
      elseif($page == 'complain') {
		  include("Show_complain.php");
          $active4 = " class='active' ";
	  }
      elseif($page == 'investigating') {
		  include("investigating_case.php");
          $active5 = " class='active' ";
	  } 
      elseif($page == 'law_order') {
		  include("Show_law_order.php");
          $active6 = " class='active' ";
	  }
      elseif($page == 'suspect') {
		  include("suspect_vehicle.php");
          $active7 = " class='active' ";
	  }
      elseif($page == 'foreigner') {
		  include("Show_foreigner.php");
          $active8 = " class='active' ";
	  }
      elseif($page == 'criminalfund') {
		  include("Show_criminal_fund.php");
          $active9 = " class='active' ";
	  }
	  elseif($page == 'information') {
		  include("information.php");
          $active10 = " class='active' ";
	  }
      elseif($page == 'crimesmaping') {
		  include("Show_crimes_map.php");
          $active11 = " class='active' ";
	  }
      elseif($page == 'performance') {
		  include("performance.php");
          $active12 = " class='active' ";
	  }
      elseif($page == 'mission') {
		  include("mission.php");
          $active13 = " class='active' ";
	  }
      elseif($page == 'complaints') {
		  include("complaints.php");
          $active14 = " class='active' ";
	  }
      elseif($page == 'online') {
		  include("online_case.php");
          $active15 = " class='active' ";
	  }
      elseif($page == 'blockade') {
		  include("Show_blockade.php");
          $active16 = " class='active' ";
	  }
	  elseif($page == 'doc') {
		  include("doc.php");
          $active17 = " class='active' ";
	  }
	  elseif($page == 'program') {
		  include("program.php");
          $active18 = " class='active' ";
	  }
      elseif($page == 'study') {
		  include("study_case.php");
          $active19 = " class='active' ";
	  }
      elseif($page == 'knowledge') {
		  include("Show_knowledge.php");
          $active20 = " class='active' ";
	  }
      elseif($page == 'report') {
		  include("Show_report.php");
          $active21 = " class='active' ";
	  }
      elseif($page == 'tool') {
		  include("Show_tool.php");
          $active22 = " class='active' ";
	  }
      elseif($page == 'Directory') {
		  include("Show_Directory.php");
          $active23 = " class='active' ";
	  }
	  elseif($page == 'ST_report') {
		  include("Show_ST_report.php");
          $active24 = " class='active' ";
	  }
	else {
		echo "ยังไม่ระบุ หน้า... '$page' ";
	}
	?>
    </div>

          
    </body><br>
    <hr width="100%" size="8" color="blue">
    <footer>
		<h4 style="text-align: right;">Created by ว่าที่ พ.ต.อ.ณัทรภณ ทรงไทย ผกก.สภ.วังกะพี้ อ.เมือง จ.อุตรดิตถ์ 0804651999 27.08.2022</h5>
    </footer>
</html>
	
<script>
const { value: accept } = await Swal.fire({
  title: 'Terms and conditions',
  input: 'checkbox',
  inputValue: 1,
  inputPlaceholder:
    'I agree with the terms and conditions',
  confirmButtonText:
    'Continue <i class="fa fa-arrow-right"></i>',
  inputValidator: (result) => {
    return !result && 'You need to agree with T&C'
  }
})

if (accept) {
  Swal.fire('You agreed with T&C :)')
}	
</script>