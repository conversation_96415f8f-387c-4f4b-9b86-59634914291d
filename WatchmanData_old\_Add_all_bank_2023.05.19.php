<?php
include '../Condb.php';
include '../users.inc.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// เมื่อมีการใช้ ตัวแปร Session จะต้องเปิดก่อน
//session_start();  // เปิดตัวแปร session
/*if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
*/

$pcid = $_GET['pcid']; // url parameter url ? [v1=222] & [v2=xxx]
$people_name = '';
if($pcid ) 
{
	$query1 = "SELECT ps_cl_prefix,ps_cl_name,ps_cl_surname, ps_cl_image FROM wm_tb_personal WHERE ps_cl_idcard ='$pcid' ";
	$result1 = mysqli_query($conn, $query1);
	$row1 = mysqli_fetch_array($result1);		
	$people_name = $row1['ps_cl_prefix'] . $row1['ps_cl_name'] . ' ' . $row1['ps_cl_surname'];
	$people_image = $row1["ps_cl_image"];
	if($people_image != '') {
		$people_image = "<img src='{$people_image}' height='80'> ";
	}
}

?>


<!doctype html>
<html>
<head>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link rel="icon" type="image/x-icon" href="/favicon.ico">
<title>เพิ่มข้อมูลธนาคาร</title>
<link rel="stylesheet" href="/policeinnopolis/bootstrap/css/bootstrap.rtl.min.css">
<!-- สำหรับ วันที่ แบบ พ.ศ. -->
<link rel="stylesheet" href="http://code.jquery.com/ui/1.9.1/themes/base/jquery-ui.css" />
<script src="http://code.jquery.com/jquery-1.8.2.js"></script>
<script src="/policeinnopolis/js/jquery-ui.js"></script>
<style type="text/css">
body,td,th {
    color: #1203F8;
}
    .body{
        font-size: 18px;
    }
input::placeholder {
  color: red;
}
.form-control::placeholder {
  color: sandybrown;
}
</style>
</head>

<body>
	<div class="container">
	<div class="row">
		<div class="col-sm-8">
						  
	<div class=" h4 text-center  alert alert-success mb-4 mt-4 " role="alert"> เพิ่มข้อมูลบัญชีธนาคาร ของ <?= $people_image ?> <?= $people_name ?> <?= $pcid ?> </div>
	<form action="Save_all_bank.php" method="POST" enctype="multipart/form-data" class="body">
            <label hidden ="hidden">ลำดับ</label>
            <input name="bk_cl_aid" type = "text" class="form-control" hidden ="hidden"  >
			<label>เลขบัตรประชาชน</label>
			<input type = "text" name="bk_cl_idcard" class="form-control" placeholder="กรอกเลขบัตรประชาชน 13 หลัก"  value="<?= $pcid ?>"  readonly="readonly" >
			<label>รหัสธนาคาร </label>
			<input type = "text" name = "bk_cl_code" class="form-control" placeholder="กรอกรหัสธนาคาร"   >
			<label>ชื่อธนาคาร</label>
			<input type = "text" name = "bk_cl_bankname" class="form-control" placeholder="ระบุชื่อธนาคาร"  >
			<label>สาขา</label>
			<input type = "text" name = "bk_cl_branch" class="form-control" placeholder="ระบุสาขา"  >
			<label>ที่อยู่</label>
			<input type = "text" name = "bk_cl_address" class="form-control" placeholder="กรอกข้อมูลที่อยู่ธนาคาร"  >
			<label>เลขบัญชี <span style="color: #F90004">* จำเป็น</span> </label>
			<input type = "text" name = "bk_cl_ac_no" class="form-control" placeholder="ระบุเลขที่บัญชีธนาคาร"  Required  >
			<label>ยอดเงิน</label>
			<input type = "text" name = "bk_cl_balance" class="form-control" placeholder="ระบุยอดเงินในบัญชี"  >
			<label>วันตรวจสอบ</label>
            <p><input type="text" name="bk_cl_inspect" id="datepicker" class="form-control" autocomplete="off" ></p>
            <script>
                function set_cal(ele)//function สร้างตัวเลือกปฎิทิน
                {
                  $( ele ).datepicker({
                      onSelect:(date_text)=>
                      {
                        let arr=date_text.split("/");
                        let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])+543).toString();
                        $(ele).val(new_date);
                        $(ele).css("color","");
                      },
                      beforeShow:()=>{

                        if($(ele).val()!="")
                        {
                          let arr=$(ele).val().split("/");
                          let new_date=arr[0]+"/"+arr[1]+"/"+(parseInt(arr[2])-543).toString();
                          $(ele).val(new_date);

                        }

                        $(ele).css("color","white");
                      },
                      onClose:()=>{

                          $(ele).css("color","");

                          if($(ele).val()!="")
                          {
                              let arr=$(ele).val().split("-");
                              if(parseInt(arr[2])<2500)
                              {
                                  let new_date=arr[0]+"-"+arr[1]+"-"+(parseInt(arr[2])).toString();
                                  $(ele).val(new_date);
                              }
                          }
                      },
                      dateFormat:"yy-mm-dd", //กำหนดรูปแบบวันที่เป็น วัน/เดือน/ปี
                      changeMonth:true,//กำหนดให้เลือกเดือนได้
                      changeYear:true,//กำหนดให้เลือกปีได้
                      showOtherMonths:true,//กำหนดให้แสดงวันของเดือนก่อนหน้าได้
                  });
                }
              $(document).ready(function(){
                //เรียก function set_cal เมื่อเปิดหน้าเว็บ โดยส่ง object element ที่มี id เป็น datepicker เป็นพารามิเตอร์
                set_cal( $("#datepicker") );
              })
              </script>
            <!-- สำหรับ วันที่ แบบ พ.ศ. จากข้างบน มาถึงตรงนี้ -->
			<label>หมายเหตุ</label>
			<input type = "text" name = "bk_cl_remark" class="form-control" placeholder="หมายเหตุ"  >
 <br>
		<p>
    	<input type="submit" value="บันทึกข้อมูล" class="btn btn-success" >
		<td> <a href="Show_All.php?pcid=<?= $pcid ?>&page=bank" class="btn btn-warning" >ยกเลิก</a> </td>
 	</p>
		<br>
		<br>
	</form>
	</div>
	</div>
	</div>
</body>
</html>