<?php
include '../Condb.php';
include '../users.inc.php';
// Get total number of records
$query_count = "SELECT COUNT(*) as total_rows FROM wm_tb_crimes_history";
$result_count = mysqli_query($conn, $query_count);
$row_count = mysqli_fetch_assoc($result_count);
$total_rows = $row_count['total_rows'];

// Number of records to display per page
$records_per_page = 50;

// Calculate the number of pages
$total_pages = ceil($total_rows / $records_per_page);

// Current page number
$current_page = isset($_GET['page']) ? $_GET['page'] : 1;

// Calculate the starting record for the current page
$start = ($current_page - 1) * $records_per_page;

// Retrieve records for the current page
$query = "SELECT * FROM wm_tb_crimes_history AS CH  
          LEFT JOIN wm_tb_personal AS PS ON CH.ch_cl_idcard = PS.ps_cl_idcard
          WHERE
                PS.`ps_cl_type`=2 AND CH.`ch_cl_Tumbon` IN ('640102','640107','640108')
          LIMIT $start, $records_per_page";
$result = mysqli_query($conn, $query);


// Display records
echo "<table>";
?>
<div class="container">
<table class="table table-striped table-hover mb-4 mt-4" >
  <tbody>
    <tr class="container-fluid">
		<th> คำนำหน้า </th>
		<th> ชื่อ </th>
		<th> นามสกุล </th>
		<th> ชื่อเล่น </th>
		<th> อายุ </th>
        <th> รูปภาพ</th>
    </tr>
<?php
while ($row = mysqli_fetch_assoc($result)) {
    $age = get_personal_age_2( $row["ps_cl_birthday2"] );
    $image = $row["ps_cl_image"];
    echo "<tr>";
    echo "<td>" . $row['ps_cl_prefix'] . "</td>";
    echo "<td>" . $row['ps_cl_name'] . "</td>";
    echo "<td>" . $row['ps_cl_surname'] . "</td>";
    echo "<td>" . $row['ps_cl_nickname'] . "</td>";
    echo "<td>" . $age . "</td>";
    echo "<td><img src='" . $image . "' alt='" . $row['Picture'] . "'></td>";

    // Add more columns as needed
    echo "</tr>";
}
echo "</table>";
?>
 </tbody>
</table>
</div>
<?php
// Create links or buttons to navigate between pages
echo "<div>";
for ($i = 1; $i <= $total_pages; $i++) {
    echo "<button><a href='?page=$i'>$i</a></button> ";
}
echo "</div>";
?>