<?php //PDO
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

$id = isset($_GET['cp_cl_aid']) ? $_GET['cp_cl_aid'] : '';

//get month from select or current month
$month = isset($_GET['month']) ? $_GET['month'] : date("m");
$year = isset($_GET['year']) ? $_GET['year'] : date("Y");

$m1 = '';
$m2 = '';
$m3 = '';
$m4 = '';
$m5 = '';
$m6 = '';
$m7 = '';
$m8 = '';
$m9 = '';
$m10 = '';
$m11 = '';
$m12 = '';
switch($month)
{
    case "01": $m1 = "selected"; break;
    case "02": $m2 = "selected"; break;
    case "03": $m3 = "selected"; break;
    case "04": $m4 = "selected"; break;
    case "05": $m5 = "selected"; break;
    case "06": $m6 = "selected"; break;
    case "07": $m7 = "selected"; break;
    case "08": $m8 = "selected"; break;
    case "09": $m9 = "selected"; break;
    case "10": $m10 = "selected"; break;
    case "11": $m11 = "selected"; break;
    case "12": $m12 = "selected"; break;
}

 // เลขคดี
$sql_no = "SELECT * FROM wm_tb_Complaints WHERE station='$station' ORDER BY cp_cl_aid DESC LIMIT 1";
    $stmt_no = $pdo->prepare($sql_no);
    $stmt_no->execute();
    $row_no = $stmt_no->fetch(PDO::FETCH_ASSOC);

if ($row_no !== false) {
    $running_no = $row_no['cp_cl_case_id'];
} else {
  // Handle the case when no information is found
  $running_no = null; // or any default value you prefer
}

//print_r($running_no);
?>

<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link rel="stylesheet" href="/bootstrap/css/bootstrap-grid.css">
<style>
    .swal2-popup {
    width: 500px !important;
    height: auto !important;
    font-size: 14px !important;
}
</style>
<div class="container-fluid" align="left">
<div class=" h3 text-center  alert alert-success mb-4 mt-4 " role="alert" >บันทึกรับแจ้งเหตุ ฝ่ายสืบสวน <?= $name_station ?> </div>
<div align="left">
<table width="80%" border="0" cellspacing="1" cellpadding="1">
      <tbody>
          
        <tr>
          <td width="11%"><a href="Add_complaints.php" class="btn btn-success btn-lg mb-4 <?= $add_btn ?>" style="flex:initial">เพิ่มข้อมูล</a></td>
          <!--<td width="19%"><a href="sum_complaints.php" class="btn btn-primary btn-lg mb-4" style="flex:initial">บัญชีเฉพาะเรื่องร้องเรียน</a></td>-->
          <td width="31%"><a href="sum_complaints2.php" class="btn btn-primary btn-lg mb-4" style="flex:initial">บัญชีรับแจ้งเหตุ ทั้งหมด</a></td>
          <td width="20%"><label>เลือกปี </label>
            <select id="cp_cl_year" name="cp_cl_year" class="form-select col-6 d-lg-inline" style="background-color: khaki; font-size: 16px" onChange="dates_change()">
              <option value="">เลือก</option>
              <?php
                $cur_y = date("Y") + 3;
                for($y=2020; $y<$cur_y; $y++) {
                    $sel = ($y == $year) ? "selected" : '';
                    echo "<option value='$y' $sel> ". ($y + 543) ." </option>";
                }
              ?>
          </select></td>
          <td width="19%"><label>เดือน</label>
            <select id="cp_cl_month" name="cp_cl_month" class="form-select col-8 d-lg-inline" style="background-color: khaki; font-size: 16px" onChange="dates_change()">
              <option value="">เลือก</option>
              <option <?= $m1 ?> value="01">มกราคม</option>
              <option <?= $m2 ?> value="02">กุมภาพันธ์</option>
              <option <?= $m3 ?> value="03">มีนาคม</option>
              <option <?= $m4 ?> value="04">เมษายน</option>
              <option <?= $m5 ?> value="05">พฤษภาคม</option>
              <option <?= $m6 ?> value="06">มิถุนายน</option>
              <option <?= $m7 ?> value="07">กรกฎาคม</option>
              <option <?= $m8 ?> value="08">สิงหาคม</option>
              <option <?= $m9 ?> value="09">กันยายน</option>
              <option <?= $m10 ?> value="10">ตุลาคม</option>
              <option <?= $m11 ?> value="11">พฤศจิกายน</option>
              <option <?= $m12 ?> value="12">ธันวาคม</option>
          </select></td>
            <div align="right"> <a>รหัสรับแจ้งเหตุ (ล่าสุด) </a>&nbsp;<b style="color: crimson; font-size: 20px ; background-color: yellow ;padding: 10px"><?= $running_no ?></b></div>
        </tr>
      </tbody>
    </table>
</div>


<div class="table-responsive">
<table width="100%" height="94" border="1" cellpadding="1" cellspacing="1" class="table-bordered table-hover mb-4 mt-4">
  <tbody>
    <tr>
	<!--  <td height="44" bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">ลำดับ</td>		-->
	  <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">รหัสรับแจ้งเหตุ</td>   
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">เหตุ</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">ที่มา</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">วันเวลา รับแจ้งเหตุ</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">วันที่เกิดเหตุ</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">เวลา</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">สถานที่เกิดเหตุ</td>      
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">ร้อยเวรสืบสวน</td>
	  <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">เจ้าหน้าที่สืบสวน</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">สถานะ</td>
      <td bgcolor="#995D5D" style="color: #F7EFEF; text-align: center;">ไฟล์ สส.1</td>
      <td colspan="3" bgcolor="#995D5D"></td>
      <td colspan="8" bgcolor="#995D5D"></td>
    </tr>
	  
<?php
$sql = "SELECT
            T1.*,
            T2.`sco_cl_data` AS data,
            T3.`cs_cl_status` AS status,
            T4.`bsdp_name` AS BSDP
        FROM
            `wm_tb_Complaints` AS T1 " .
            "LEFT JOIN `wm_tb_source_complaints` AS T2 ON T1.`cp_cl_source` = T2.`sco_cl_aid` " .
            "LEFT JOIN `wm_tb_Complaints_status` AS T3 ON T1.`cp_cl_status` = T3.`cs_cl_aid` ".
            "LEFT JOIN `police_name_bsdetective` AS T4 ON T1.`cp_cl_investigative_officer` = T4.`aid_bsdp` 
        WHERE
            `cp_cl_source` IN ('1','2','3','4','5','6','7','12','13') AND  MONTH(cp_cl_date_complaints)=:month AND YEAR(cp_cl_date_complaints)=:year AND T1.station='$station'
        ORDER BY
            cp_cl_date_complaints ASC ";  //ตั้งตัวแปร sql เลือกฐานข้อมูลจากตารางที่ชื่อ wm_tb_personal

    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':month', $month);
    $stmt->bindParam(':year', $year);
    $stmt->execute();

// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no = 1;
while($rowCp = $stmt->fetch(PDO::FETCH_ASSOC))
      //เพิ่มเข้ามาใหม่ 12/09/2022 จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
{
    //ฟังก์ชั่น วันที่ ดึงจาก condb
    $strDate = DateThai( $rowCp["cp_cl_date_complaints"] );
    $strDate2 = DateThai( $rowCp["cp_cl_date_incident"] );
    
     $pcid = $rowCp['cp_cl_case_id'];  
    //ตรวจสอบไฟล์ สส.1 ก่อนว่า มีไฟล์ในตารางไหม หากมี ให้ส่งเป็นลิงค์
	$pdf = $rowCp["cp_cl_complaints_file"];
	if($pdf !== '')
	{
		$links = '<a href="/' . $rowCp["cp_cl_complaints_file"] .'" target="_blank">Download</a>';
	}
	else {
		$links = 'ไม่มีไฟล์';
	}
    
?>	

    <tr>
      <td><?= $rowCp["cp_cl_case_id"]?></td>
      <td><?= $rowCp["cp_cl_case"]?></td>
      <td><?= $rowCp["data"]?></td>
      <td nowrap="nowrap"><?= $strDate ?> / <?= $rowCp["cp_cl_time_complaints"]?></td>
      <td nowrap="nowrap"><?= $strDate2 ?></td>
      <td><?= $rowCp["cp_cl_time_start_incident"]?> - <?= $rowCp["cp_cl_time_end_incident"]?></td>
   	  <td><?= $rowCp["cp_cl_place"]?></td>
	  <td><?= $rowCp["cp_cl_investigative_sergeant"]?></td>
	  <td><?= $rowCp["BSDP"]?></td>
      <td><?= $rowCp["status"]?></td>
      <td><?= $links ?></td>

        <td>&nbsp;<a href="show_detail_complaints.php?id=<?= $rowCp["cp_cl_aid"] ?>&pcid=<?= $pcid ?>" class="btn btn-success mb-4" >รายละเอียด</a>&nbsp;</td>
        <td>&nbsp;<a href="Edit_complaints.php?id=<?= $rowCp["cp_cl_aid"] ?>" class="btn btn-warning mb-4 <?= $edit_btn ?>" >แก้ไข</a>&nbsp;</td>
        <td>&nbsp;<button href="#" class="btn btn-danger mb-4" onClick="deleteItem(<?= $rowCp["cp_cl_aid"] ?>, '<?= $rowCp["cp_cl_case_id"] ?>')"<?=$del_btn?>>ลบ</button>&nbsp;</td>
        
	  </tr>
  </tbody>
<?php
	$no++;
}
?>
</table>
</div>
    
<script src="/jQuery/jquery-3.5.1.min.js" type="text/javascript"></script>
<script src="../SweetAlert2/dist/sweetalert2.all.min.js"></script>
<script>
function deleteItem(id, caseid) {
    Swal.fire({
        title: 'คุณต้องการลบข้อมูลนี้ใช่ไหม?',
        text: "หลังจากลบแล้ว จะไม่สามารถกู้คืนได้",
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'ใช่, ยืนยันลบ',
        cancelButtonText: 'ยกเลิก'
    }).then((result) => {
        if (result.isConfirmed) {
            window.location.href = '/Bansuan/Del_complaints.php?id=' + id + '&caseid=' + caseid;
        }
    });
}
</script>
    
<script>
function dates_change()  // คำสั่งเปลี่ยนเดือน ปี
{
    var ms = $("#cp_cl_month option:selected").val();
    var ys = $("#cp_cl_year option:selected").val();
    window.location = "/Bansuan/index.php?page=complaints&month=" + ms + "&year=" + ys + "&rnd=" + Math.random();
}
</script>
