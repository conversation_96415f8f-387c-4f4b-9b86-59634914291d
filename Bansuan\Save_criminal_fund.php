<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../Alert.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

$ip_address = $_SERVER['REMOTE_ADDR'];
$action = 'Save criminal fund'; // You can set the action to a suitable value
$data = str_replace("'", "", compact_array($_POST));
log_activity($acc, $action, $data, $ip_address); // ใช้ function ในไฟล์ Condb.php

$fun_cl_aid = $_POST[ 'fun_cl_aid' ];
$fun_cl_date = $_POST[ 'fun_cl_date' ];	
$fun_cl_detail = $_POST[ 'fun_cl_detail' ];
$fun_cl_record = $_POST[ 'fun_cl_record' ];
$region = $_POST[ 'region' ];
$provincial = $_POST[ 'provincial' ];
$station = $_POST[ 'station' ];

// save file สส.1
$file1 = $_FILES[ 'fun_cl_file' ][ 'tmp_name' ];
if ( is_uploaded_file( $file1 ) ) {
  $fun_cl_file = "uploaded/Doc/" . $_FILES[ 'fun_cl_file' ][ 'name' ];	
  move_uploaded_file( $file1, "../" . $fun_cl_file );  
} else {
  $fun_cl_file = '';
}

try{
$sql = "SELECT * FROM wm_tb_criminalfund WHERE fun_cl_aid = :fun_cl_aid ";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':fun_cl_aid', $fun_cl_aid);
    $stmt->execute();
    $row = $stmt->fetch(PDO::FETCH_ASSOC);

if($row){
	// Update existing record
    $sql = "UPDATE wm_tb_criminalfund SET fun_cl_date=:fun_cl_date, fun_cl_detail=:fun_cl_detail, fun_cl_record=:fun_cl_record, region=:region, provincial=:provincial, station=:station, fun_cl_file=:fun_cl_file WHERE fun_cl_aid=:fun_cl_aid";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':fun_cl_date', $fun_cl_date);
    $stmt->bindParam(':fun_cl_detail', $fun_cl_detail);
    $stmt->bindParam(':fun_cl_record', $fun_cl_record);
    $stmt->bindParam(':region', $region);
    $stmt->bindParam(':provincial', $provincial);
	$stmt->bindParam(':station', $station);
    $stmt->bindParam(':fun_cl_file', $fun_cl_file);
    $stmt->bindParam(':fun_cl_aid', $fun_cl_aid);
    $stmt->execute();
} else {
    // Insert new record
    $sql = "INSERT INTO wm_tb_criminalfund (fun_cl_aid, fun_cl_date, fun_cl_detail, fun_cl_record, region, provincial, station, fun_cl_file) VALUES (:fun_cl_aid, :fun_cl_date, :fun_cl_detail, :fun_cl_record, :region, :provincial, :station, :fun_cl_file)";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':fun_cl_aid', $fun_cl_aid);
    $stmt->bindParam(':fun_cl_date', $fun_cl_date);
    $stmt->bindParam(':fun_cl_detail', $fun_cl_detail);
    $stmt->bindParam(':fun_cl_record', $fun_cl_record);
    $stmt->bindParam(':region', $region);
    $stmt->bindParam(':provincial', $provincial);
	$stmt->bindParam(':station', $station);
    $stmt->bindParam(':fun_cl_file', $fun_cl_file);
    $stmt->execute();
}

if ($stmt->rowCount() > 0) {
        $_SESSION['success'] = "Data has been inserted succesfully";
        showSweetAlert("Saved successfully", "บันทึกข้อมูลเรียบร้อย");
        unset($_SESSION['success']); // Clear the session variable
        header("refresh:2; url=/Bansuan/index.php?rnd=' + Math.random() + '&page=criminalfund");
    } else {
        $_SESSION['error'] = "Error";
        showSweetAlert('Error', "บันทึกข้อมูล ไม่สำเร็จ", 'error');
        unset($_SESSION['error']); // Clear the session variable
        header("refresh:2; url=/Bansuan/index.php?rnd=' + Math.random() + '&page=criminalfund");
    }
    
}catch(Exception $e){
    echo 'Query Failed: ' . $e->getMessage();
}

?>