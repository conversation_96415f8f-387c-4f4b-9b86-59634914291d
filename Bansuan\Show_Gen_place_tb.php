<?php
include '../Condb.php';
include '../users.inc.php';
include '../right_user.php';
include '../Head_image.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);


$sql_all = "SELECT " .
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='1' AND station=:station ) AS T1, ".   //สถานที่ราชการ
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='2' AND station=:station ) AS T2, " .  //ทูตานุทูต
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='3' AND station=:station ) AS T3, " .  //กงสุล
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='4' AND station=:station ) AS T4, " .  //หัวหน้าองค์กรต่างประเทศ
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='5' AND station=:station ) AS T5, " .  //มหาวิทยาลัย
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='6' AND station=:station ) AS T6, " .  //วิทยาลัย
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='7' AND station=:station ) AS T7, " .  //วิทยาเขต
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='8' AND station=:station ) AS T8, ".   //สถาบัน
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='9' AND station=:station ) AS T9, " .  //โรงเรียน
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='10' AND station=:station ) AS T10, " .  //สถานฝึกอบรม
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='11' AND station=:station ) AS T11, " .  //สถานพยาบาลรัฐบาล
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='12' AND station=:station ) AS T12, " .  //สถานพยาบาลเอกชน
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='13' AND station=:station ) AS T13, " .  //ที่ทำการรัฐวิสาหกิจ
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='14' AND station=:station ) AS T14, " .  //สถานที่ท่องเที่ยว
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='15' AND station=:station ) AS T15, " .  //ธนาคาร
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='16' AND station=:station ) AS T16, " .  //บริษัทเงินทุนหลักทรัพย์
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='17' AND station=:station ) AS T17, " .  //สถานที่ค้าขายหุ้น
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='18' AND station=:station ) AS T18, " .  //ตู้บริการเงินด่วน (เอ.ที.เอ็ม.)
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='19' AND station=:station ) AS T19, " .  //สถานที่รับแลกเปลี่ยนเงินตรา
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='20' AND station=:station ) AS T20, " .  //โรงรับจำนำ/สถานธนานุบาล
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='21' AND station=:station ) AS T21, " .  //ศูนย์การค้า, ห้างสรรพสินค้า
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='22' AND station=:station ) AS T22, " .  //ตลาดนัด
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='23' AND station=:station ) AS T23, " .  //ตลาดสด
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='24' AND station=:station ) AS T24, " .  //ตลาดโต้รุ่ง
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='25' AND station=:station ) AS T25, " .  //มินิมาร์ท/ซุปเปอร์มาเก็ต
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='26' AND station=:station ) AS T26, " .  //อุทยานประวัติศาสตร์
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='27' AND station=:station ) AS T27, " .  //อนุสาวรีย์
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='28' AND station=:station ) AS T28, " .  //อนุสรณ์สถาน
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='29' AND station=:station ) AS T29, " .  //ฯลฯ
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='30' AND station=:station ) AS T30, " .  //สถานประกอบพิธีกรรมทางศาสนา
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='31' AND station=:station ) AS T31, " .  //สถานีโทรทัศน์
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='32' AND station=:station ) AS T32, " .  //สถานีวิทยุกระจายเสียง
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='33' AND station=:station ) AS T33, " .  //สถานที่บันทึกเสียง
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='34' AND station=:station ) AS T34, " .  //สถานีดาวเทียม
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='35' AND station=:station ) AS T35, " .  //สำนักพิมพ์
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='36' AND station=:station ) AS T36, " .  //โรงพิมพ์
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='37' AND station=:station ) AS T37, " .  //สถานที่ให้เช่าพักอาศัย
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='38' AND station=:station ) AS T38, " .  //หมู่บ้านจัดสรร
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='39' AND station=:station ) AS T39, " .  //โรงแรม, บังกะโล
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='40' AND station=:station ) AS T40, " .  //มูลนิธิ, สมาคม, องค์กรสาธารณกุศล
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='41' AND station=:station ) AS T41, " .  //ชุมชน
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='42' AND station=:station ) AS T42, " .  //สถานรับเลี้ยงเด็ก
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='43' AND station=:station ) AS T43, " .  //ร้านค้าทองและอัญมณี
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='44' AND station=:station ) AS T44, " .  //โรงภาพยนตร์
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='45' AND station=:station ) AS T45, " .  //โรงถ่ายทำภาพยนตร์
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='46' AND station=:station ) AS T46, " .  //โรงละคร
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='47' AND station=:station ) AS T47, " .  //สถานที่จัดแสดงดนตรี เช่น ห้องจัดแสดง, ลานจัดแสดง
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='48' AND station=:station ) AS T48, " .  //ฯลฯ
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='49' AND station=:station ) AS T49, " .  //ร้านค้าอาวุธปืน และเครื่องกระสุนปืน
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='50' AND station=:station ) AS T50, " .  //บริษัทประกันภัย
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='51' AND station=:station ) AS T51, " .  //บริษัทประมูลสินทรัพย์
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='52' AND station=:station ) AS T52, " .  //โรงงานอุตสาหกรรม
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='53' AND station=:station ) AS T53, " .  //โรงสีข้าว
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='54' AND station=:station ) AS T54, " .  //โกดังหรือคลังสินค้า
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='55' AND station=:station ) AS T55, " .  //โรงฆ่าสัตว์
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='56' AND station=:station ) AS T56, " .  //ฟาร์มเลี้ยงสัตว์
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='57' AND station=:station ) AS T57, " .  //สถานที่ประกอบธุรกิจเกี่ยวกับไม้
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='58' AND station=:station ) AS T58, " .  //สถานที่จำหน่ายน้ำมัน, ก๊าซ(ปั๊ม) , ก๊าซหุงต้ม
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='59' AND station=:station ) AS T59, " .  //คลัง, โรงกลั่นน้ำมัน, โรงถ่ายก๊าซ
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='60' AND station=:station ) AS T60, " .  //สถานที่จ่าย, สำรวจ, ขุดเจาะน้ำมัน,ก๊าซ
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='61' AND station=:station ) AS T61, " .  //แหล่งกำเนิดพลังงานไฟฟ้า หรือพลังงานอื่น ๆ
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='62' AND station=:station ) AS T62, " .  //โรงกรองน้ำ, โรงสูบน้ำ
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='63' AND station=:station ) AS T63, " .  //สถานที่จำหน่ายรถ, อะไหล่, เครื่องยนต์ใหม่
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='64' AND station=:station ) AS T64, " .  //ศูนย์บริการ/อู่
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='65' AND station=:station ) AS T65, " .  //โรงงานผลิต, สถานที่เก็บ, โกดัง
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='66' AND station=:station ) AS T66, " .  //สถานที่จำหน่าย รับแลกเปลี่ยนหรือให้เช่ารถยนต์ (เต็นท์, คาร์เร้นท์)
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='67' AND station=:station ) AS T67, " .  //อู่เคาะ พ่น สี
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='68' AND station=:station ) AS T68, " .  //สถานที่ซื้อ-ขาย, แลกเปลี่ยน, บริการ
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='69' AND station=:station ) AS T69, " .  //โรงงานผลิต, สถานที่เก็บ, โกดัง
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='70' AND station=:station ) AS T70, " .  //
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='71' AND station=:station ) AS T71, " .  //
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='72' AND station=:station ) AS T72, " .  //
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='73' AND station=:station ) AS T73, " .  //
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='74' AND station=:station ) AS T74, " .  //
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='75' AND station=:station ) AS T75, " .  //
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='76' AND station=:station ) AS T76, " .  //
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='77' AND station=:station ) AS T77, " .  //
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='78' AND station=:station ) AS T78, " .  //
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='79' AND station=:station ) AS T79, " .  //
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='80' AND station=:station ) AS T80, " .  //
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='81' AND station=:station ) AS T81, " .  //
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='82' AND station=:station ) AS T82, " .  //
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='83' AND station=:station ) AS T83, " .  //
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='84' AND station=:station ) AS T84, " .  //
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='85' AND station=:station ) AS T85, " .  //
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='86' AND station=:station ) AS T86, " .  //
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='87' AND station=:station ) AS T87, " .  //
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='88' AND station=:station ) AS T88, " .  //
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='89' AND station=:station ) AS T89, " .  //
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='90' AND station=:station ) AS T90, " .  //
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='91' AND station=:station ) AS T91, " .  //
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='92' AND station=:station ) AS T92, " .  //
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='93' AND station=:station ) AS T93, " .  //
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='94' AND station=:station ) AS T94, " .  //
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='95' AND station=:station ) AS T95, " .  //
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='96' AND station=:station ) AS T96, " .  //
            "(SELECT COUNT(*) FROM wm_tb_place_data WHERE gc_cl_gen_place_type='97' AND station=:station ) AS T97 " .
            "";

    // Prepare the statement
    $stmt = $pdo->prepare($sql_all);

    // Bind the station value to the parameter
    $stmt->bindParam(':station', $station);

try {
    // Execute the query
    $stmt->execute();

    // Fetch the result as an associative array
    $row_all = $stmt->fetch(PDO::FETCH_ASSOC);

    // You can access the results using the column aliases (T1, T2, T31, T32)
/*    $T1 = $row_all['T1'];
    $T32 = $row_all['T32'];*/

} catch (PDOException $e) {
    // Handle any errors that may occur during the database operation
    echo "Error: " . $e->getMessage();
}


?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
        <base target="_top">
        <title>ระบบ WatchmanDB</title>
		<link rel="stylesheet" href="../bootstrap/css/bootstrap.rtl.min.css">
		<link rel="stylesheet" href="../bootstrap-3.4.1-dist/css/bootstrap.min.css">
  		<script src="../jQuery/jquery-3.6.1.min.js"></script>
 		<script src="../bootstrap-3.4.1-dist/js/bootstrap.min.js"></script>
        <style>
            body {
              font-family: 'Courier New', Courier, monospace;
              font-size: 16px;
            }
        </style>
</head>

<body>

<div>
  <?php
    include('../users_info.php');
    ?>	
</div>

<div class=" h3 text-center  alert alert-success mb-2 mt-2 " role="alert" >บัญชีข้อมูลสถานทั่วไป (แบบ ศขส.) 57 ประเภท <?= $name_station ?></div>
    <div style="flex: auto">
        &nbsp;&nbsp;&nbsp;<a href="/Bansuan/Show_Gen_place.php?rnd=<?= rand(); ?>" class="btn btn-primary btn-lg mb-4" >กลับ</a>&nbsp;
    </div>
    
<div class="container">
<table width="95%" border="1" cellspacing="1" cellpadding="1" class="table-striped table-hover table-bordered">
  <tbody>
    <tr>
      <td height="40" width="54%" bgcolor="#1D04F3" style="text-align: center"><strong>รายการ</strong></td>
      <td height="40" width="19%" bgcolor="#1D04F3" style="text-align: center" ><strong>จำนวน</strong></td>
      <td height="40" width="27%" bgcolor="#1D04F3" style="text-align: center"><strong>หมายเหตุ</strong></td>
      
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;1. สถานที่ราชการ</td>
      <td style="text-align: center"><b>
          <?php
		  $T1Value = $row_all['T1'];
			if ($T1Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T1Value . '</b>';
			} else {
				echo $T1Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" colspan="3" style="font-size: 18px; text-align: left">&nbsp;2. สถานที่ของผู้แทนจากต่างประเทศ (ที่ทำการ, ที่พักอาศัย</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;2.1 ทูตานุทูต</td>
      <td style="text-align: center"><b>
          <?php
		  $T2Value = $row_all['T2'];
			if ($T2Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T2Value . '</b>';
			} else {
				echo $T2Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;2.2 กงสุล</td>
      <td style="text-align: center"><b>
          <?php
		  $T3Value = $row_all['T3'];
			if ($T3Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T3Value . '</b>';
			} else {
				echo $T3Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;2.3 หัวหน้าองค์กรของต่างประเทศ</td>
      <td style="text-align: center"><b>
          <?php
		  $T4Value = $row_all['T4'];
			if ($T4Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T4Value . '</b>';
			} else {
				echo $T4Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" colspan="3" style="font-size: 18px; text-align: left">&nbsp;3. สถานศึกษา</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;3.1 มหาวิทยาลัย</td>
      <td style="text-align: center"><b>
          <?php
		  $T5Value = $row_all['T5'];
			if ($T5Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T5Value . '</b>';
			} else {
				echo $T5Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;3.2 วิทยาลัย</td>
      <td style="text-align: center"><b>
          <?php
		  $T6Value = $row_all['T6'];
			if ($T6Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T6Value . '</b>';
			} else {
				echo $T6Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;3.3 วิทยาเขต</td>
      <td style="text-align: center"><b>
          <?php
		  $T7Value = $row_all['T7'];
			if ($T7Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T7Value . '</b>';
			} else {
				echo $T7Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;3.4 สถาบัน</td>
      <td style="text-align: center"><b>
          <?php
		  $T8Value = $row_all['T8'];
			if ($T8Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T8Value . '</b>';
			} else {
				echo $T8Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;3.5 โรงเรียน (มัธยม, ประถม, อนุบาล)</td>
      <td style="text-align: center"><b>
          <?php
		  $T9Value = $row_all['T9'];
			if ($T9Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T9Value . '</b>';
			} else {
				echo $T9Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;3.6 สถานฝึกอบรม พัฒนา ฝึกสอนวิชาชีพต่าง ๆ เช่น สารพัดช่าง, สอนขับรถ สอนตัดผม, สอนคอมพิวเตอร์, สอนตัดเสื้อผ้า</td>
      <td style="text-align: center"><b>
          <?php
		  $T10Value = $row_all['T10'];
			if ($T10Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T10Value . '</b>';
			} else {
				echo $T10Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" colspan="3" style="font-size: 18px; text-align: left">&nbsp;4. สถานพยาบาล</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;4.1 ของรัฐบาล</td>
      <td style="text-align: center"><b>
          <?php
		  $T11Value = $row_all['T11'];
			if ($T11Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T11Value . '</b>';
			} else {
				echo $T11Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;4.2 ของเอกชน</td>
      <td style="text-align: center"><b>
          <?php
		  $T12Value = $row_all['T12'];
			if ($T12Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T12Value . '</b>';
			} else {
				echo $T12Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;5. ที่ทำการรัฐวิสาหกิจ</td>
      <td style="text-align: center"><b>
          <?php
		  $T13Value = $row_all['T13'];
			if ($T13Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T13Value . '</b>';
			} else {
				echo $T13Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;6. สถานที่ท่องเที่ยว (สวนสาธารณะ, พิพิธภัณฑ์, สวนสัตว์, สวนสนุก, หาด, ทะเล, น้ำตก, เกาะ, แก่ง, ดอย, เนิน, ถ้ำ, ภูเขา, เขื่อน, อ่างเก็บน้ำ, ฝาย, วนอุทยานแห่งชาติ, ที่พักตากอากาศ, รีสอร์ท, ฯลฯ)</td>
      <td style="text-align: center"><b>
          <?php
		  $T14Value = $row_all['T14'];
			if ($T14Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T14Value . '</b>';
			} else {
				echo $T14Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" colspan="3" style="font-size: 18px; text-align: left">&nbsp;7. สถานที่เกี่ยวข้องกับธุรกิจด้านการเงิน</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;7.1 ธนาคาร</td>
      <td style="text-align: center"><b>
          <?php
		  $T15Value = $row_all['T15'];
			if ($T15Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T15Value . '</b>';
			} else {
				echo $T15Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;7.2 บริษัทเงินทุนหลักทรัพย์</td>
      <td style="text-align: center"><b>
          <?php
		  $T16Value = $row_all['T16'];
			if ($T16Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T16Value . '</b>';
			} else {
				echo $T16Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;7.3 สถานที่ค้าขายหุ้น</td>
      <td style="text-align: center"><b>
          <?php
		  $T17Value = $row_all['T17'];
			if ($T17Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T17Value . '</b>';
			} else {
				echo $T17Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;7.4 ตู้บริการเงินด่วน (เอ.ที.เอ็ม.)</td>
      <td style="text-align: center"><b>
          <?php
		  $T18Value = $row_all['T18'];
			if ($T18Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T18Value . '</b>';
			} else {
				echo $T18Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;7.5 สถานที่รับแลกเปลี่ยนเงินตรา</td>
      <td style="text-align: center"><b>
          <?php
		  $T19Value = $row_all['T19'];
			if ($T19Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T19Value . '</b>';
			} else {
				echo $T19Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;8. โรงรับจำนำ/สถานธนานุบาล</td>
      <td style="text-align: center"><b>
          <?php
		  $T20Value = $row_all['T20'];
			if ($T20Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T20Value . '</b>';
			} else {
				echo $T20Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" colspan="3" style="font-size: 18px; text-align: left">&nbsp;9. ย่านการค้า</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;9.1 ศูนย์การค้า, ห้างสรรพสินค้า</td>
      <td style="text-align: center"><b>
          <?php
		  $T21Value = $row_all['T21'];
			if ($T21Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T21Value . '</b>';
			} else {
				echo $T21Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;9.2 ตลาดนัด</td>
      <td style="text-align: center"><b>
          <?php
		  $T22Value = $row_all['T22'];
			if ($T22Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T22Value . '</b>';
			} else {
				echo $T22Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;9.3 ตลาดสด</td>
      <td style="text-align: center"><b>
          <?php
		  $T23Value = $row_all['T23'];
			if ($T23Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T23Value . '</b>';
			} else {
				echo $T23Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;9.4 ตลาดโต้รุ่ง</td>
      <td style="text-align: center"><b>
          <?php
		  $T24Value = $row_all['T24'];
			if ($T24Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T24Value . '</b>';
			} else {
				echo $T24Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;9.5 มินิมาร์ท/ซุปเปอร์มาเก็ต</td>
      <td style="text-align: center"><b>
          <?php
		  $T25Value = $row_all['T25'];
			if ($T25Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T25Value . '</b>';
			} else {
				echo $T25Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" colspan="3" style="font-size: 18px; text-align: left">&nbsp;10. สถานที่สำคัญทางประวัติศาสตร์/โบราณสถาน</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;10.1 อุทยานประวัติศาสตร์</td>
      <td style="text-align: center"><b>
          <?php
		  $T26Value = $row_all['T26'];
			if ($T26Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T26Value . '</b>';
			} else {
				echo $T26Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;10.2 อนุสาวรีย์</td>
      <td style="text-align: center"><b>
          <?php
		  $T27Value = $row_all['T27'];
			if ($T27Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T27Value . '</b>';
			} else {
				echo $T27Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;10.3 อนุสรณ์สถาน</td>
      <td style="text-align: center"><b>
          <?php
		  $T28Value = $row_all['T28'];
			if ($T28Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T28Value . '</b>';
			} else {
				echo $T28Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;10.4 ฯลฯ</td>
      <td style="text-align: center"><b>
          <?php
		  $T29Value = $row_all['T29'];
			if ($T29Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T29Value . '</b>';
			} else {
				echo $T29Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;11. สถานประกอบพิธีกรรมทางศาสนา (วัด, โบสถ์, สุเหร่า, สำนักสงฆ์, สุสาน, ศาลเจ้า, ฮวงซุ้ย, ป่าช้า)</td>
      <td style="text-align: center"><b>
          <?php
		  $T30Value = $row_all['T30'];
			if ($T30Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T30Value . '</b>';
			} else {
				echo $T30Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" colspan="3" style="font-size: 18px; text-align: left">&nbsp;12. สถานที่ผลิตแลเผยแพร่ด้านสื่อสารมวลชน (วิทยุ, ดาวเทียม, โทรทัศน์, สิ่งพิมพ์)</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;12.1 สถานีโทรทัศน์</td>
      <td style="text-align: center"><b>
          <?php
		  $T31Value = $row_all['T31'];
			if ($T31Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T31Value . '</b>';
			} else {
				echo $T31Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;12.2 สถานีวิทยุกระจายเสียง</td>
      <td style="text-align: center"><b>
          <?php
		  $T32Value = $row_all['T32'];
			if ($T32Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T32Value . '</b>';
			} else {
				echo $T32Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;12.3 สถานที่บันทึกเสียง</td>
      <td style="text-align: center"><b>
          <?php
		  $T33Value = $row_all['T33'];
			if ($T33Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T33Value . '</b>';
			} else {
				echo $T33Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;12.4 สถานีดาวเทียม</td>
      <td style="text-align: center"><b>
          <?php
		  $T34Value = $row_all['T34'];
			if ($T34Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T34Value . '</b>';
			} else {
				echo $T34Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;12.5 สำนักพิมพ์</td>
      <td style="text-align: center"><b>
          <?php
		  $T35Value = $row_all['T35'];
			if ($T35Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T35Value . '</b>';
			} else {
				echo $T35Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;12.6 โรงพิมพ์</td>
      <td style="text-align: center"><b>
          <?php
		  $T36Value = $row_all['T36'];
			if ($T36Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T36Value . '</b>';
			} else {
				echo $T36Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;13. สถานที่ให้เช่าพักอาศัย (บ้านเช่า, หอพัก, แฟลต, คอนโดมิเนียม, อพาร์ทเมนต์, คอร์ท)</td>
      <td style="text-align: center"><b>
          <?php
		  $T37Value = $row_all['T37'];
			if ($T37Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T37Value . '</b>';
			} else {
				echo $T37Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;14. หมู่บ้านจัดสรร</td>
      <td style="text-align: center"><b>
          <?php
		  $T38Value = $row_all['T38'];
			if ($T38Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T38Value . '</b>';
			} else {
				echo $T38Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;15. โรงแรม, บังกะโล</td>
      <td style="text-align: center"><b>
          <?php
		  $T39Value = $row_all['T39'];
			if ($T39Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T39Value . '</b>';
			} else {
				echo $T39Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;16. มูลนิธิ, สมาคม, องค์กรสาธารณกุศล</td>
      <td style="text-align: center"><b>
          <?php
		  $T40Value = $row_all['T40'];
			if ($T40Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T40Value . '</b>';
			} else {
				echo $T40Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;17. ชุมชน</td>
      <td style="text-align: center"><b>
          <?php
		  $T41Value = $row_all['T41'];
			if ($T41Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T41Value . '</b>';
			} else {
				echo $T41Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;18. สถานรับเลี้ยงเด็ก</td>
      <td style="text-align: center"><b>
          <?php
		  $T42Value = $row_all['T42'];
			if ($T42Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T42Value . '</b>';
			} else {
				echo $T42Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;19. ร้านค้าทองและอัญมณี (ร้านเจียระไน/ผลิต/จำหน่าย เครื่องประดับ)</td>
      <td style="text-align: center"><b>
          <?php
		  $T43Value = $row_all['T43'];
			if ($T43Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T43Value . '</b>';
			} else {
				echo $T43Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" colspan="3" style="font-size: 18px; text-align: left">&nbsp;20. สถานแสดงมหรสพ</td>
      </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;20.1 โรงภาพยนตร์</td>
      <td style="text-align: center"><b>
          <?php
		  $T44Value = $row_all['T44'];
			if ($T44Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T44Value . '</b>';
			} else {
				echo $T44Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;20.2 โรงถ่ายทำภาพยนตร์</td>
      <td style="text-align: center"><b>
          <?php
		  $T45Value = $row_all['T45'];
			if ($T45Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T45Value . '</b>';
			} else {
				echo $T45Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;20.3 โรงละคร</td>
      <td style="text-align: center"><b>
          <?php
		  $T46Value = $row_all['T46'];
			if ($T46Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T46Value . '</b>';
			} else {
				echo $T46Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;20.4 สถานที่จัดแสดงดนตรี เช่น ห้องจัดแสดง, ลานจัดแสดง</td>
      <td style="text-align: center"><b>
          <?php
		  $T47Value = $row_all['T47'];
			if ($T47Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T47Value . '</b>';
			} else {
				echo $T47Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;20.5 ฯลฯ</td>
      <td style="text-align: center"><b>
          <?php
		  $T48Value = $row_all['T48'];
			if ($T48Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T48Value . '</b>';
			} else {
				echo $T48Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;21. ร้านค้าอาวุธปืน และเครื่องกระสุนปืน</td>
      <td style="text-align: center"><b>
          <?php
		  $T49Value = $row_all['T49'];
			if ($T49Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T49Value . '</b>';
			} else {
				echo $T49Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;22. บริษัทประกันภัย</td>
      <td style="text-align: center"><b>
          <?php
		  $T50Value = $row_all['T50'];
			if ($T50Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T50Value . '</b>';
			} else {
				echo $T50Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;23. บริษัทประมูลสินทรัพย์</td>
      <td style="text-align: center"><b>
          <?php
		  $T51Value = $row_all['T51'];
			if ($T51Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T51Value . '</b>';
			} else {
				echo $T51Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;24. โรงงานอุตสาหกรรม</td>
      <td style="text-align: center"><b>
          <?php
		  $T52Value = $row_all['T52'];
			if ($T52Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T52Value . '</b>';
			} else {
				echo $T52Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;25. โรงสีข้าว</td>
      <td style="text-align: center"><b>
          <?php
		  $T53Value = $row_all['T53'];
			if ($T53Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T53Value . '</b>';
			} else {
				echo $T53Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;26. โกดังหรือคลังสินค้า</td>
      <td style="text-align: center"><b>
          <?php
		  $T54Value = $row_all['T54'];
			if ($T54Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T54Value . '</b>';
			} else {
				echo $T54Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;27. โรงฆ่าสัตว์</td>
      <td style="text-align: center"><b>
          <?php
		  $T55Value = $row_all['T55'];
			if ($T55Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T55Value . '</b>';
			} else {
				echo $T55Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;28. ฟาร์มเลี้ยงสัตว์</td>
      <td style="text-align: center"><b>
          <?php
		  $T56Value = $row_all['T56'];
			if ($T56Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T56Value . '</b>';
			} else {
				echo $T56Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;29. สถานที่ประกอบธุรกิจเกี่ยวกับไม้ (โรงเลื่อย, โรงไม้, ร้านค้าไม้, ร้านค้าเฟอร์นิเจอร์ ฯลฯ)</td>
      <td style="text-align: center"><b>
          <?php
		  $T57Value = $row_all['T57'];
			if ($T57Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T57Value . '</b>';
			} else {
				echo $T57Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" colspan="3" style="font-size: 18px; text-align: left">&nbsp;30. สถานที่ดำเนินการเกี่ยวกับเชื้อเพลิง</td>
      </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;30.1 สถานที่จำหน่ายน้ำมัน, ก๊าซ(ปั๊ม) , ก๊าซหุงต้ม</td>
      <td style="text-align: center"><b>
          <?php
		  $T58Value = $row_all['T58'];
			if ($T58Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T58Value . '</b>';
			} else {
				echo $T58Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;30.2 คลัง, โรงกลั่นน้ำมัน, โรงถ่ายก๊าซ</td>
      <td style="text-align: center"><b>
          <?php
		  $T59Value = $row_all['T59'];
			if ($T59Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T59Value . '</b>';
			} else {
				echo $T59Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;30.3 สถานที่จ่าย, สำรวจ, ขุดเจาะน้ำมัน,ก๊าซ</td>
      <td style="text-align: center"><b>
          <?php
		  $T60Value = $row_all['T60'];
			if ($T60Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T60Value . '</b>';
			} else {
				echo $T60Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;31. แห่งกำเนิดพลังงานไฟฟ้า หรือพลังงานอื่น ๆ</td>
      <td style="text-align: center"><b>
          <?php
		  $T61Value = $row_all['T61'];
			if ($T61Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T61Value . '</b>';
			} else {
				echo $T61Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;32. โรงกรองน้ำ, โรงสูบน้ำ</td>
      <td style="text-align: center"><b>
          <?php
		  $T62Value = $row_all['T62'];
			if ($T62Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T62Value . '</b>';
			} else {
				echo $T62Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" colspan="3" style="font-size: 18px; text-align: left">&nbsp;63. สถานที่ประกอบธุรกิจรถยนต์</td>
      </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;33.1 สถานที่จำหน่ายรถ, อะไหล่, เครื่องยนต์ใหม่</td>
      <td style="text-align: center"><b>
          <?php
		  $T63Value = $row_all['T63'];
			if ($T63Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T63Value . '</b>';
			} else {
				echo $T63Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;33.2 ศูนย์บริการ/อู่</td>
      <td style="text-align: center"><b>
          <?php
		  $T64Value = $row_all['T64'];
			if ($T64Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T64Value . '</b>';
			} else {
				echo $T64Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;33.3 โรงงานผลิต, สถานที่เก็บ, โกดัง</td>
      <td style="text-align: center"><b>
          <?php
		  $T65Value = $row_all['T65'];
			if ($T65Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T65Value . '</b>';
			} else {
				echo $T65Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;33.4 สถานที่จำหน่าย รับแลกเปลี่ยนหรือให้เช่ารถยนต์ (เต็นท์, คาร์เร้นท์)</td>
      <td style="text-align: center"><b>
          <?php
		  $T66Value = $row_all['T66'];
			if ($T66Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T66Value . '</b>';
			} else {
				echo $T66Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;33.5 อู่เคาะ พ่น สี</td>
      <td style="text-align: center"><b>
          <?php
		  $T67Value = $row_all['T67'];
			if ($T67Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T67Value . '</b>';
			} else {
				echo $T67Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" colspan="3" style="font-size: 18px; text-align: left">&nbsp;34. สถานที่ประกอบธุรกิจรถจักรยานยนต์</td>
      </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;34.1 สถานที่ซื้อ-ขาย, แลกเปลี่ยน, บริการ</td>
      <td style="text-align: center"><b>
          <?php
		  $T68Value = $row_all['T68'];
			if ($T68Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T68Value . '</b>';
			} else {
				echo $T68Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;34.2 โรงงานผลิต, สถานที่เก็บ, โกดัง</td>
      <td style="text-align: center"><b>
          <?php
		  $T69Value = $row_all['T69'];
			if ($T69Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T69Value . '</b>';
			} else {
				echo $T69Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;34.3 ร้านซ่อม</td>
      <td style="text-align: center"><b>
          <?php
		  $T70Value = $row_all['T70'];
			if ($T70Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T70Value . '</b>';
			} else {
				echo $T70Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;34.5 ร้านค้าอะไหล่</td>
      <td style="text-align: center"><b>
          <?php
		  $T71Value = $row_all['T71'];
			if ($T71Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T71Value . '</b>';
			} else {
				echo $T71Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;35. สถานที่จำหน่ายยารักษาโรค</td>
      <td style="text-align: center"><b>
          <?php
		  $T72Value = $row_all['T72'];
			if ($T72Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T72Value . '</b>';
			} else {
				echo $T72Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;36. สถานที่ให้บริการอินเตอร์เน็ต</td>
      <td style="text-align: center"><b>
          <?php
		  $T73Value = $row_all['T73'];
			if ($T73Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T73Value . '</b>';
			} else {
				echo $T73Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;37. สถานที่จำหน่ายและติดตั้งเครื่องคอมพิวเตอร์</td>
      <td style="text-align: center"><b>
          <?php
		  $T74Value = $row_all['T74'];
			if ($T74Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T74Value . '</b>';
			} else {
				echo $T74Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;38. สถานที่จำหน่ายหรือซ่อมอุปกรณ์เครื่องใช้ไฟฟ้า, เครื่องปรับอากาศ</td>
      <td style="text-align: center"><b>
          <?php
		  $T75Value = $row_all['T75'];
			if ($T75Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T75Value . '</b>';
			} else {
				echo $T75Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;39. สถานที่จำหน่ายและบริการถ่ายภาพ ถ่ายเอกสาร</td>
      <td style="text-align: center"><b>
          <?php
		  $T76Value = $row_all['T76'];
			if ($T76Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T76Value . '</b>';
			} else {
				echo $T76Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;40. สถานที่จำหน่ายเครื่องสุขภัณฑ์และวัสดุก่อสร้าง</td>
      <td style="text-align: center"><b>
          <?php
		  $T77Value = $row_all['T77'];
			if ($T77Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T77Value . '</b>';
			} else {
				echo $T77Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;41. สถานที่จำหน่ายเครื่องมือแพทย์</td>
      <td style="text-align: center"><b>
          <?php
		  $T78Value = $row_all['T78'];
			if ($T78Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T78Value . '</b>';
			} else {
				echo $T78Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;42. สถานที่จำหน่ายหนังสือ</td>
      <td style="text-align: center"><b>
          <?php
		  $T79Value = $row_all['T79'];
			if ($T79Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T79Value . '</b>';
			} else {
				echo $T79Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;43. สถานที่ประกอบธุรกิจการท่องเที่ยวภายในประเทศและต่างประเทศ</td>
      <td style="text-align: center"><b>
          <?php
		  $T80Value = $row_all['T80'];
			if ($T80Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T80Value . '</b>';
			} else {
				echo $T80Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;44. สถานีรถ (ท่ารถ) , รถตู้, รถประจำทาง, บขส., สถานีขนส่ง</td>
      <td style="text-align: center"><b>
          <?php
		  $T81Value = $row_all['T81'];
			if ($T81Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T81Value . '</b>';
			} else {
				echo $T81Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;45. ท่าเรือ</td>
      <td style="text-align: center"><b>
          <?php
		  $T82Value = $row_all['T82'];
			if ($T82Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T82Value . '</b>';
			} else {
				echo $T82Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;46. สถานีรถไฟ</td>
      <td style="text-align: center"><b>
          <?php
		  $T83Value = $row_all['T83'];
			if ($T83Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T83Value . '</b>';
			} else {
				echo $T83Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;47. ท่าอากาศยาน</td>
      <td style="text-align: center"><b>
          <?php
		  $T84Value = $row_all['T84'];
			if ($T84Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T84Value . '</b>';
			} else {
				echo $T84Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;48. สนามกีฬา, สถานที่ออกกำลังกาย</td>
      <td style="text-align: center"><b>
          <?php
		  $T85Value = $row_all['T85'];
			if ($T85Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T85Value . '</b>';
			} else {
				echo $T85Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" colspan="3" style="font-size: 18px; text-align: left">&nbsp;49. สถานที่ทำการค้าเกี่ยวกับของเก่า</td>
      </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;49.1 วัตถุโบราณ</td>
      <td style="text-align: center"><b>
          <?php
		  $T86Value = $row_all['T86'];
			if ($T86Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T86Value . '</b>';
			} else {
				echo $T86Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;49.2 เครื่องยนต์</td>
      <td style="text-align: center"><b>
          <?php
		  $T87Value = $row_all['T87'];
			if ($T87Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T87Value . '</b>';
			} else {
				echo $T87Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;49.3 อะไหล่รถยนต์, อะไหล่รถจักรยานยนต์</td>
      <td style="text-align: center"><b>
          <?php
		  $T88Value = $row_all['T88'];
			if ($T88Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T88Value . '</b>';
			} else {
				echo $T88Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;&nbsp;49.4 เบ็ดเตล็ด</td>
      <td style="text-align: center"><b>
          <?php
		  $T89Value = $row_all['T89'];
			if ($T89Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T89Value . '</b>';
			} else {
				echo $T89Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;50. อู่, บริษัทเจ้าของรถแท็กซี่</td>
      <td style="text-align: center"><b>
          <?php
		  $T90Value = $row_all['T90'];
			if ($T90Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T90Value . '</b>';
			} else {
				echo $T90Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;51. อู่, บริษัทเจ้าของรถสามล้อ</td>
      <td style="text-align: center"><b>
          <?php
		  $T91Value = $row_all['T91'];
			if ($T91Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T91Value . '</b>';
			} else {
				echo $T91Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;52. บริษัท, ห้างหุ้นส่วนจำกัด รับเหมาก่อสร้างต่อเติมอาคาร</td>
      <td style="text-align: center"><b>
          <?php
		  $T92Value = $row_all['T92'];
			if ($T92Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T92Value . '</b>';
			} else {
				echo $T92Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;53. สถานที่ผลิต, จำหน่าย, ให้เช่าเทปคาสเซ็ท, ซีดี. ดีวีดี และวีดีโอ</td>
      <td style="text-align: center"><b>
          <?php
		  $T93Value = $row_all['T93'];
			if ($T93Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T93Value . '</b>';
			} else {
				echo $T93Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;54. ตู้โทรศัพท์สาธารณะ</td>
      <td style="text-align: center"><b>
          <?php
		  $T94Value = $row_all['T94'];
			if ($T94Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T94Value . '</b>';
			} else {
				echo $T94Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;55. สถานที่บริการโทรศัพท์หรือให้เช่าโทรศัพท์</td>
      <td style="text-align: center"><b>
          <?php
		  $T95Value = $row_all['T95'];
			if ($T95Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T95Value . '</b>';
			} else {
				echo $T95Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;56. สถานที่จำหน่ายอาหาร เครื่องดื่ม (ร้านอาหาร, สวนอาหาร)</td>
      <td style="text-align: center"><b>
          <?php
		  $T96Value = $row_all['T96'];
			if ($T96Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T96Value . '</b>';
			} else {
				echo $T96Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
      <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;57. อื่น ๆ</td>
      <td style="text-align: center"><b>
          <?php
		  $T97Value = $row_all['T97'];
			if ($T97Value > 0) {
				echo '<b style="color: blue; font-size: 25px;">' . $T97Value . '</b>';
			} else {
				echo $T97Value;
			}
          ?></b></td>
      <td>&nbsp;</td>
    </tr>
  </tbody>
</table>
    <br>
    <div style="flex: auto" align="right">
        &nbsp;&nbsp;&nbsp;<a href="/Bansuan/Show_Gen_place.php?rnd=<?= rand(); ?>" class="btn btn-primary btn-lg mb-4" >กลับ</a>&nbsp;&nbsp;
    </div>
    <hr>
</div>
    
</body>
</html>