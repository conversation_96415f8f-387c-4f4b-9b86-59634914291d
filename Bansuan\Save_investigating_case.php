<?php
include '../Condb.php'; //PDO
include '../users.inc.php';
include '../Alert.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

/*echo '<pre>';
print_r($_POST);
echo '</pre>';

echo '<hr>';
exit();*/

$ivc_cl_aid = $_POST[ 'ivc_cl_aid' ];
$ivc_cl_no = $_POST[ 'ivc_cl_no' ];
$ivc_cl_type = $_POST[ 'ivc_cl_type' ];
$ivc_cl_know = $_POST[ 'ivc_cl_know' ];
$ivc_cl_case = $_POST[ 'ivc_cl_case' ];
$ivc_cl_crimes_no = $_POST[ 'ivc_cl_crimes_no' ];
$ivc_cl_name_sufferer = $_POST[ 'ivc_cl_name_sufferer' ];
$ivc_cl_idcard = $_POST[ 'ivc_cl_idcard' ];
$ivc_cl_name = $_POST[ 'ivc_cl_name' ];
$ivc_cl_detail = $_POST[ 'ivc_cl_detail' ];
$ivc_cl_damages = $_POST[ 'ivc_cl_damages' ];
$region = $_POST[ 'region' ];
$provincial = $_POST[ 'provincial' ];
$station = $_POST[ 'station' ];
$ivc_cl_inquiry_official = $_POST[ 'ivc_cl_inquiry_official' ];
$ivc_cl_date = $_POST[ 'ivc_cl_date' ];
$ivc_cl_status_case = $_POST[ 'ivc_cl_status_case' ];
$ivc_cl_catch = $_POST[ 'ivc_cl_catch' ];
$ivc_cl_case_out = $_POST[ 'ivc_cl_case_out' ];
$ivc_cl_wanted = $_POST[ 'ivc_cl_wanted' ];
$ivc_cl_detective = $_POST[ 'ivc_cl_detective' ];
$ivc_cl_remark = $_POST[ 'ivc_cl_remark' ];

// save file รายงานสืบสวน
$file1 = $_FILES[ 'ivc_cl_file' ][ 'tmp_name' ];
if ( is_uploaded_file( $file1 ) ) {
  $ivc_cl_file = "uploaded/Doc/" . $_FILES[ 'ivc_cl_file' ][ 'name' ];	
  move_uploaded_file( $file1, "../" . $ivc_cl_file );  
} else {
  $ivc_cl_file = '';
}

try{
$sql ="SELECT * FROM `wm_tb_investigating_case` WHERE ivc_cl_aid = :ivc_cl_aid ";
    $stmt = $pdo->prepare($sql);
    $stmt->bindParam(':ivc_cl_aid', $ivc_cl_aid);
    $stmt->execute();
    $row = $stmt->fetch(PDO::FETCH_ASSOC);

if($row) {
    $ivc_cl_aid = $row['ivc_cl_aid'];

    if($ivc_cl_file !== ''){
        $sql = "UPDATE wm_tb_investigating_case SET " .
            "ivc_cl_no = :ivc_cl_no," .
            "ivc_cl_type = :ivc_cl_type," .
            "ivc_cl_know = :ivc_cl_know," .
            "ivc_cl_case = :ivc_cl_case," .
            "ivc_cl_crimes_no = :ivc_cl_crimes_no," .
            "ivc_cl_name_sufferer = :ivc_cl_name_sufferer," .
            "ivc_cl_idcard = :ivc_cl_idcard," .
            "ivc_cl_name = :ivc_cl_name," .
            "ivc_cl_detail = :ivc_cl_detail," .
            "ivc_cl_damages = :ivc_cl_damages," .
            "region = :region," .
            "provincial = :provincial," .
            "station = :station," .
            "ivc_cl_inquiry_official = :ivc_cl_inquiry_official," .
            "ivc_cl_date = :ivc_cl_date," .
            "ivc_cl_status_case = :ivc_cl_status_case," .
            "ivc_cl_catch = :ivc_cl_catch," .
            "ivc_cl_case_out = :ivc_cl_case_out," .
            "ivc_cl_wanted = :ivc_cl_wanted," .
            "ivc_cl_detective = :ivc_cl_detective, " .
            "ivc_cl_remark = :ivc_cl_remark, " .
            "ivc_cl_file = :ivc_cl_file " .
            "WHERE ivc_cl_aid = :ivc_cl_aid ";
            $stmt = $pdo->prepare($sql);
            $stmt->bindParam(':ivc_cl_aid', $ivc_cl_aid);
            $stmt->bindParam(':ivc_cl_no', $ivc_cl_no);
            $stmt->bindParam(':ivc_cl_file', $ivc_cl_file); 
    }else{
        $sql = "UPDATE wm_tb_investigating_case SET " .
            "ivc_cl_no = :ivc_cl_no," .
            "ivc_cl_type = :ivc_cl_type," .
            "ivc_cl_know = :ivc_cl_know," .
            "ivc_cl_case = :ivc_cl_case," .
            "ivc_cl_crimes_no = :ivc_cl_crimes_no," .
            "ivc_cl_name_sufferer = :ivc_cl_name_sufferer," .
            "ivc_cl_idcard = :ivc_cl_idcard," .
            "ivc_cl_name = :ivc_cl_name," .
            "ivc_cl_detail = :ivc_cl_detail," .
            "ivc_cl_damages = :ivc_cl_damages," .
            "region = :region," .
            "provincial = :provincial," .
            "station = :station," .
            "ivc_cl_inquiry_official = :ivc_cl_inquiry_official," .
            "ivc_cl_date = :ivc_cl_date," .
            "ivc_cl_status_case = :ivc_cl_status_case," .
            "ivc_cl_catch = :ivc_cl_catch," .
            "ivc_cl_case_out = :ivc_cl_case_out," .
            "ivc_cl_wanted = :ivc_cl_wanted," .
            "ivc_cl_detective = :ivc_cl_detective, " .
            "ivc_cl_remark = :ivc_cl_remark " .
            "WHERE ivc_cl_aid = :ivc_cl_aid ";
            $stmt = $pdo->prepare($sql);
            $stmt->bindParam(':ivc_cl_aid', $ivc_cl_aid);
            $stmt->bindParam(':ivc_cl_no', $ivc_cl_no);
        }
}else{
    $sql = "INSERT INTO wm_tb_investigating_case (ivc_cl_no, ivc_cl_type, ivc_cl_know, ivc_cl_case, ivc_cl_crimes_no, ivc_cl_name_sufferer, ivc_cl_idcard, ivc_cl_name, ivc_cl_detail, ivc_cl_damages, region, provincial, station, ivc_cl_inquiry_official, ivc_cl_date, ivc_cl_status_case, ivc_cl_catch, ivc_cl_case_out, ivc_cl_wanted, ivc_cl_detective, ivc_cl_remark, ivc_cl_file) VALUES(:ivc_cl_no, :ivc_cl_type, :ivc_cl_know, :ivc_cl_case, :ivc_cl_crimes_no, :ivc_cl_name_sufferer, :ivc_cl_idcard, :ivc_cl_name, :ivc_cl_detail, :ivc_cl_damages, :region, :provincial, :station, :ivc_cl_inquiry_official, :ivc_cl_date, :ivc_cl_status_case, :ivc_cl_catch, :ivc_cl_case_out, :ivc_cl_wanted, :ivc_cl_detective, :ivc_cl_remark, :ivc_cl_file) ";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':ivc_cl_file', $ivc_cl_file);
        $stmt->bindParam(':ivc_cl_no', $ivc_cl_no);    
}
        $stmt->bindParam(':ivc_cl_type', $ivc_cl_type);
        $stmt->bindParam(':ivc_cl_know', $ivc_cl_know);
        $stmt->bindParam(':ivc_cl_case', $ivc_cl_case);
        $stmt->bindParam(':ivc_cl_crimes_no', $ivc_cl_crimes_no);
        $stmt->bindParam(':ivc_cl_name_sufferer', $ivc_cl_name_sufferer);
        $stmt->bindParam(':ivc_cl_idcard', $ivc_cl_idcard);
        $stmt->bindParam(':ivc_cl_name', $ivc_cl_name);
        $stmt->bindParam(':ivc_cl_detail', $ivc_cl_detail);
        $stmt->bindParam(':ivc_cl_damages', $ivc_cl_damages);
        $stmt->bindParam(':region', $region);
        $stmt->bindParam(':provincial', $provincial);
        $stmt->bindParam(':station', $station);
        $stmt->bindParam(':ivc_cl_inquiry_official', $ivc_cl_inquiry_official);
        $stmt->bindParam(':ivc_cl_date', $ivc_cl_date);
        $stmt->bindParam(':ivc_cl_status_case', $ivc_cl_status_case);
        $stmt->bindParam(':ivc_cl_catch', $ivc_cl_catch);
        $stmt->bindParam(':ivc_cl_case_out', $ivc_cl_case_out);
        $stmt->bindParam(':ivc_cl_wanted', $ivc_cl_wanted);
        $stmt->bindParam(':ivc_cl_detective', $ivc_cl_detective);
        $stmt->bindParam(':ivc_cl_remark', $ivc_cl_remark);
  
$result = $stmt->execute();

$ip_address = $_SERVER['REMOTE_ADDR'];
$action = "Save Investigating case : $ivc_cl_idcard"; // You can set the action to a suitable value
$data = str_replace("'", "", compact_array($_POST));
log_activity($acc, $action, $data, $ip_address);
	
    if ($result) {
        $_SESSION['success'] = "Data has been inserted succesfully";
        showSweetAlert("Saved successfully", "บันทึกข้อมูลเรียบร้อย");
        unset($_SESSION['success']); // Clear the session variable
        header("refresh:2; url=/Bansuan/index.php?pcid={$ivc_cl_aid}&page=investigating");
    } else {
        $_SESSION['error'] = "Error";
        showSweetAlert('Error', "บันทึกข้อมูล ไม่สำเร็จ", 'error');
        unset($_SESSION['error']); // Clear the session variable
        header("refresh:2; url=/Bansuan/index.php?pcid={$ivc_cl_aid}&page=investigating");
    }
} catch (PDOException $e) {
    echo 'Query Failed : '. $e->getMessage();
}
$pdo = null;

?>