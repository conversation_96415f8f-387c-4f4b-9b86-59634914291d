<?php
include '../Condb.php';  //PDO
include '../users.inc.php';
include '../Alert.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);
$ip_address = $_SERVER['REMOTE_ADDR'];
$action = 'Save Edit Gen place'; // You can set the action to a suitable value
$data = str_replace("'", "", compact_array($_POST));
log_activity($acc, $action, $data, $ip_address); // ใช้ function ในไฟล์ Condb.php


$gc_cl_aid = $_POST[ 'gc_cl_aid' ];	
$gc_cl_place = $_POST[ 'gc_cl_place' ];									
$gc_cl_gen_place_type = $_POST[ 'gc_cl_gen_place_type' ];
$gc_cl_name = $_POST[ 'gc_cl_name' ];
$gc_cl_place_address = $_POST[ 'gc_cl_place_address' ];	
$gc_cl_place_phone = $_POST[ 'gc_cl_place_phone' ];		
$gc_cl_owner_name = $_POST[ 'gc_cl_owner_name' ];		
$gc_cl_owner_idcard = $_POST[ 'gc_cl_owner_idcard' ];	
$gc_cl_mgr_name = $_POST[ 'gc_cl_mgr_name' ];			
$gc_cl_mgr_idcard = $_POST[ 'gc_cl_mgr_idcard' ];		
$gc_cl_general_charac = $_POST[ 'gc_cl_general_charac' ];
$gc_cl_activity = $_POST[ 'gc_cl_activity' ];			
$gc_cl_important_info = $_POST[ 'gc_cl_important_info' ];
$gc_cl_recorder = $_POST[ 'gc_cl_recorder' ];			
$gc_cl_date_record = $_POST[ 'gc_cl_date_record' ];		
$region = $_POST[ 'region' ];
$provincial = $_POST[ 'provincial' ];
$station = $_POST[ 'station' ];
$gc_cl_place_lat = $_POST[ 'gc_cl_place_lat' ];			
$gc_cl_place_lon = $_POST[ 'gc_cl_place_lon' ];			


// save Image ใหม่แทน
$file1 = $_FILES[ 'gc_cl_place_image' ][ 'tmp_name' ];

if ( is_uploaded_file( $file1 ) ) {
    //ลบรูปเก่าในเซิร์ฟเวอร์ก่อน
    $sql = "SELECT * FROM wm_tb_place_data WHERE gc_cl_aid = :gc_cl_aid ";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':gc_cl_aid', $gc_cl_aid);
        $stmt->execute();

    if($row = $stmt->fetch(PDO::FETCH_ASSOC))
    {
        if(($row['gc_cl_place_image'] != '') && file_exists($row['gc_cl_place_image'])) {
            unlink( $row['gc_cl_place_image'] );
        }    
    }

    //เปลี่ยนชื่อไฟล์ใหม่
    $gc_cl_place_image = "/policeinnopolis/uploaded/Image1/" . basename( $_FILES[ 'gc_cl_place_image' ][ 'name' ] );	    
    // file + ext    
    $ext = strrchr($gc_cl_place_image, ".");    
    $gc_cl_place_image = "../policeinnopolis/uploaded/Image1/File_" . time() . $ext; // file+time+ext        
    move_uploaded_file( $file1, $gc_cl_place_image );  
} 
else {
  $gc_cl_place_image = '';
}

//print_r($_FILES[ 'gc_cl_place_image' ]);
//echo '<br>';
//echo $file1 . ' >> ';
//echo 'F >> ' . $gc_cl_place_image;

try{
if($gc_cl_place_image !== ''){
    $sql = "UPDATE wm_tb_place_data SET " .
		"gc_cl_place = :gc_cl_place," .
		"gc_cl_gen_place_type = :gc_cl_gen_place_type," .
		"gc_cl_name = :gc_cl_name, " . 
		"gc_cl_place_address = :gc_cl_place_address," .
		"gc_cl_place_phone = :gc_cl_place_phone,".
		"gc_cl_owner_name = :gc_cl_owner_name, ".
		"gc_cl_owner_idcard = :gc_cl_owner_idcard, " .
		"gc_cl_mgr_name = :gc_cl_mgr_name, " .
		"gc_cl_mgr_idcard = :gc_cl_mgr_idcard, " .
		"gc_cl_general_charac = :gc_cl_general_charac, " .
	  	"gc_cl_activity = :gc_cl_activity, " .
		"gc_cl_important_info = :gc_cl_important_info, " .
		"gc_cl_recorder = :gc_cl_recorder, " .
		"gc_cl_date_record = :gc_cl_date_record, " .
        "region = :region, " .
        "provincial = :provincial, " .
        "station = :station, " .
        "gc_cl_place_lat = :gc_cl_place_lat, " .
		"gc_cl_place_lon = :gc_cl_place_lon, " .
        "gc_cl_place_image = :gc_cl_place_image " .
	    "WHERE gc_cl_aid = :gc_cl_aid ";
        $stmt = $pdo->prepare($sql);
        $stmt->bindParam(':gc_cl_place_image', $gc_cl_place_image);
}else{
    $sql = "UPDATE wm_tb_place_data SET " .
		"gc_cl_place = :gc_cl_place," .
		"gc_cl_gen_place_type = :gc_cl_gen_place_type," .
		"gc_cl_name = :gc_cl_name, " . 
		"gc_cl_place_address = :gc_cl_place_address," .
		"gc_cl_place_phone = :gc_cl_place_phone,".
		"gc_cl_owner_name = :gc_cl_owner_name, ".
		"gc_cl_owner_idcard = :gc_cl_owner_idcard, " .
		"gc_cl_mgr_name = :gc_cl_mgr_name, " .
		"gc_cl_mgr_idcard = :gc_cl_mgr_idcard, " .
		"gc_cl_general_charac = :gc_cl_general_charac, " .
	  	"gc_cl_activity = :gc_cl_activity, " .
		"gc_cl_important_info = :gc_cl_important_info, " .
		"gc_cl_recorder = :gc_cl_recorder, " .
		"gc_cl_date_record = :gc_cl_date_record, " .
        "region = :region, " .
        "provincial = :provincial, " .
        "station = :station, " .
        "gc_cl_place_lat = :gc_cl_place_lat, " .
		"gc_cl_place_lon = :gc_cl_place_lon " .
	    "WHERE gc_cl_aid = :gc_cl_aid ";
    $stmt = $pdo->prepare($sql);
}

    $stmt->bindParam(':gc_cl_aid', $gc_cl_aid);
    $stmt->bindParam(':gc_cl_place', $gc_cl_place);
    $stmt->bindParam(':gc_cl_gen_place_type', $gc_cl_gen_place_type);
    $stmt->bindParam(':gc_cl_name', $gc_cl_name);
    $stmt->bindParam(':gc_cl_place_address', $gc_cl_place_address);
    $stmt->bindParam(':gc_cl_place_phone', $gc_cl_place_phone);
    $stmt->bindParam(':gc_cl_owner_name', $gc_cl_owner_name);
    $stmt->bindParam(':gc_cl_owner_idcard', $gc_cl_owner_idcard);
    $stmt->bindParam(':gc_cl_mgr_name', $gc_cl_mgr_name);
    $stmt->bindParam(':gc_cl_mgr_idcard', $gc_cl_mgr_idcard);
    $stmt->bindParam(':gc_cl_general_charac', $gc_cl_general_charac);
    $stmt->bindParam(':gc_cl_activity', $gc_cl_activity);
    $stmt->bindParam(':gc_cl_important_info', $gc_cl_important_info);
    $stmt->bindParam(':gc_cl_recorder', $gc_cl_recorder);
    $stmt->bindParam(':gc_cl_date_record', $gc_cl_date_record);
    $stmt->bindParam(':region', $region);
    $stmt->bindParam(':provincial', $provincial);
    $stmt->bindParam(':station', $station);
    $stmt->bindParam(':gc_cl_place_lat', $gc_cl_place_lat);
    $stmt->bindParam(':gc_cl_place_lon', $gc_cl_place_lon);
    
$result = $stmt->execute();

    if ($result) {
        $_SESSION['success'] = "Data has been inserted succesfully";
        showSweetAlert("Saved successfully", "บันทึกข้อมูลเรียบร้อย");
        unset($_SESSION['success']); // Clear the session variable
        header("refresh:2; url=/Bansuan/Show_Gen_place.php?pcid={$gc_cl_aid}");
    } else {
        $_SESSION['error'] = "Error";
        showSweetAlert('Error', "บันทึกข้อมูล ไม่สำเร็จ", 'error');
        unset($_SESSION['error']); // Clear the session variable
        header("refresh:2; url=/Bansuan/Show_Gen_place.php?pcid={$gc_cl_aid}");
    }
}catch(Exception $e){
    echo 'Query Failed : ' . $e->getMessage();
}

?>
