<?php
include '../Condb.php';
include '../users.inc.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// เมื่อมีการใช้ ตัวแปร Session จะต้องเปิดก่อน
//session_start();  // เปิดตัวแปร session
/*if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
*/

//echo '<pre>';
//print_r($_POST);
//echo '</pre>';
//
//echo '<hr>';
//
//exit();

$aid_crimes_record = $_POST[ 'aid_crimes_record' ];
$region = $_POST[ 'region' ];
$provincial = $_POST[ 'provincial' ];
$station = $_POST[ 'station' ];
$person_gen_report = $_POST[ 'person_gen_report' ];
$person_crimes_report = $_POST[ 'person_crimes_report' ];
$person_gen_record = $_POST[ 'person_gen_record' ];
$person_crimes_record = $_POST[ 'person_crimes_record' ];
$record_complete = $_POST[ 'record_complete' ];
$date_record = $_POST[ 'date_record' ];			

// convert Thai dates to eng
if($date_record != '') {
	if(strpos($date_record, '/') > 0) {
    	$dates = explode('/', $date_record);	// d/m/y
	}
	elseif(strpos($date_record, '-') > 0) {
		$date = explode('-', $date_record);	// y-m-d
		$dates = array($date[2], $date[1], $date[0]);	// d/m/Y
	}
	// thai dates
	if(substr(''.$dates[2],0,2) ==='25') {
		$date_record = ($dates[2]-543) . '-' . $dates[1] . '-' . $dates[0];
	}
	// eng dates
	else {
    	$date_record = $dates[2] . '-' . $dates[1] . '-' . $dates[0];
	}
}

    // save Image ใหม่แทน
$file1 = $_FILES[ 'image1' ][ 'tmp_name' ];
if ( is_uploaded_file( $file1 ) ) {
    //ลบรูปเก่าในเซิร์ฟเวอร์ก่อน
    $sql_img = "SELECT * FROM wm_tb_crimes_record WHERE aid_crimes_record='aid_crimes_record' ";
    $res_img = mysqli_query($conn,$sql_img);
    if($row_img = mysqli_fetch_array($res_img))
    {
        if(($row_img['image1'] != '') && file_exists($row_img['image1'])) {
            unlink( $row_img['image1'] );
        }    
    }
    //เปลี่ยนชื่อไฟล์ใหม่
    $image1 = "/policeinnopolis/uploaded/Image1/" . basename( $_FILES[ 'image1' ][ 'name' ] );	    
    // file + ext    
    $ext = strrchr($image1, ".");    
    $image1 = "../policeinnopolis/uploaded/Image1/File_" . time() . $ext; // file+time+ext        
    move_uploaded_file( $file1, $image1 );  
} 
else {
  $image1 = '';
}

$sql ="SELECT * FROM wm_tb_crimes_record WHERE aid_crimes_record='$aid_crimes_record' ";
$res = mysqli_query($conn,$sql);
$row1 = mysqli_fetch_array($res);
if($row1) {
    // ถ้ามีแล้ว ให้ update
    $aid_crimes_record = $row1['aid_crimes_record'];
    $sql = "UPDATE wm_tb_crimes_record SET ".
            "region = '$region', " .
            "provincial = '$provincial', " .
            "station = '$station', " .
            "person_gen_report = '$person_gen_report', " .
            "person_crimes_report = '$person_crimes_report', " .
            "person_gen_record = '$person_gen_record', " .
            "person_crimes_record = '$person_crimes_record', " .
            "record_complete = '$record_complete', " .
            "date_record = '$date_record', " .
            "image1 = '$image1' " .
            "WHERE aid_crimes_record = '$aid_crimes_record' ";
}
else {
    // ถ้ายังไม่มี ให้ insert
    $sql = "INSERT INTO wm_tb_crimes_record (region, provincial, station, person_gen_report, person_crimes_report, person_gen_record, person_crimes_record, record_complete, date_record, image1) VALUES('$region', '$provincial', '$station', '$person_gen_report', '$person_crimes_report', '$person_gen_record', '$person_crimes_record', '$record_complete', '$date_record', '$image1') ";
}

$result=mysqli_query($conn,$sql);
if($result){
	echo "<script>alert('บันทึกข้อมูลเรียบร้อย');</script>";	
}else{
	echo "<script>alert(\"บันทึกข้อมูลไม่สำเร็จ\\n" . print_r(mysqli_error($conn), true) . "\");</script>";
}
mysqli_close($conn);

echo "<script>window.location='/Activity/Show_Activity1.php?pcid={$aid_crimes_record}';</script>";

?>