<?php
//PDO
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// นับข้อมูลในตาราง
	$sql2 = "SELECT ".
            "(SELECT COUNT(*) FROM wm_tb_freed AS T1 WHERE station='$station' AND fr_cl_tracking='1') AS Total , ".
            "(SELECT COUNT(*) FROM wm_tb_freed AS T1 WHERE station='$station' AND fr_cl_tracking='2') AS total_fi , ".
            "(SELECT COUNT(*) FROM wm_tb_freed AS T1 WHERE station='$station' AND fr_cl_tracking='3') AS total_out , ".
            "(SELECT COUNT(*) FROM wm_tb_freed AS T1 WHERE station='$station' AND fr_cl_tracking='4') AS Prison ".
            "";
	$stmt = $pdo->prepare($sql2);
    $stmt->execute();
    $row2 = $stmt->fetch(PDO::FETCH_ASSOC);

	$total = $row2['Total']; //
    $Prison = $row2['Prison']; //
    $total_fi = $row2['total_fi']; //
    $total_out = $row2['total_out']; //$total_in_prison
    $all_total = $total+$Prison+$total_out;

?>
<meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<link rel="stylesheet" href="/bootstrap/css/bootstrap-grid.css">
<div class="container-fluid">
		<div class=" h3 text-center  alert alert-dark mb-4 mt-4 " role="alert" >ข้อมูลบุคคลพ้นโทษ/พักการลงโทษ  <?= $name_station ?> (อยู่ในพื้นที่)</div>
		<div align="left"> &nbsp;<a href="Add_Personal_for_bansuan.php" class="btn btn-success btn-lg mb-4 <?= $add_btn ?>" >เพิ่มข้อมูล</a>&nbsp; 
        <a href="freed_out_area.php" class="btn btn-dark btn-lg mb-4" style="flex:initial">อยู่นอกพื้นที่</a>
        <a href="freed_in_prison.php" class="btn btn-dark btn-lg mb-4" style="flex:initial">อยู่ในเรือนจำ(ทำผิดซ้ำ)</a>
        <a href="freed_finish.php" class="btn btn-dark btn-lg mb-4" style="flex:initial">สิ้นสุดการติดตามแล้ว</a>
        <!--<a href="https://datastudio.google.com/reporting/64da3070-6418-45f8-bb8f-5825dd724b77/page/WPviC" class="btn btn-warning btn-lg mb-4" target="_blank">Link ฐานข้อมูล</a>&nbsp;&nbsp;-->
		<a href="https://vv19-bgs.in.th/" class="btn btn-primary btn-lg mb-4" target="_blank">Police 4.0</a>&nbsp;&nbsp;
		<?php
			if ($station == 6707) {
            echo 'bansuan : 7sylvccfx0';
        } else{
				
			}
		?>
			
		</div>
		<div align="left">
		<a style="background-color: yellow ;padding: 10px">อยู่ระหว่างติดตาม มี : <span style="color: #FC0408"><b style="color: crimson; font-size: 24px">
            <?= $all_total ?> </b> ราย </a>
        <a style="background-color: yellow ;padding: 10px">แบ่งเป็น อยู่ในพื้นที่ : <span style="color: #FC0408"><b style="color: crimson; font-size: 24px">
            <?= $total ?> </b> ราย </a>
        <a style="background-color: yellow ;padding: 10px">อยู่นอกพื้นที่ : <span style="color: #FC0408"><b style="color: crimson; font-size: 24px">
            <?= $total_out ?> </b> ราย </a>
        <a style="background-color: yellow ;padding: 10px">และ อยู่ในเรือนจำ : <span style="color: #FC0408"><b style="color: crimson; font-size: 24px">
            <?= $Prison ?> </b> ราย </a>
		<a style="background-color: yellow ;padding: 10px">สำหรับที่สิ้นสุดการติดตามแล้ว มี : <span style="color: #FC0408"><b style="color: crimson; font-size: 24px">
            <?= $total_fi ?> </b> ราย </a>
		</div>

<div class="table-responsive">   
<table class="table table-striped table-hover table-bordered table-sm mb-4 mt-4 " >
      <tbody>
            <tr>
              <th nowrap="nowrap">ลำดับ</th>
              <th>ชื่อ</th>
              <th>เลขบัตร</th>
              <th>ประเภท</th>
              <th>ต้องโทษฐาน</th>
              <th>กำหนดโทษ</th> 
              <th>นับตั้งแต่</th>
              <th>พ้นโทษ<br>/พักโทษ</th>
              <th>ที่อยู่ตามบัตร</th>
              <th>ที่อยู่ที่พบตัว</th>
              <th>ผู้รับผิดชอบ</th> 
              <th>หมายเหตุ</th>
              <th></th>
            </tr>


<?php
$sql = "SELECT
            T1.*,
            T2.ps_cl_name, T2.ps_cl_surname
        FROM
            wm_tb_freed AS T1 " .
            "LEFT JOIN wm_tb_personal AS T2 ON T1.fr_cl_idcard = T2.ps_cl_idcard
        WHERE
            T1.station='$station' AND fr_cl_tracking='1'
        ORDER BY
            T1.fr_cl_freed DESC";

    $stmt = $pdo->prepare($sql);
    $stmt->execute();

//echo $sql;
// ใช้คำสั่งวนลูป เก็บข้อมูลในรูปแบบ array
$no = 1;
while($rowFr = $stmt->fetch(PDO::FETCH_ASSOC))	{//จะได้ข้อมูลมาเป็นก้อนในรูป array ซึ่งจะนำไปใส่ในคอลัมภ์ต่อไป
    $pcid = $rowFr['fr_cl_idcard']; //เลขบัตร ผู้พ้นโทษ

    //ฟังก์ชั่น วันที่ ดึงจาก condb
    if($rowFr['fr_cl_freed'] != ""){
        $strDate = DateThai( $rowFr['fr_cl_freed'] );
    }else{
        $strDate = "";
    }

    if($rowFr['fr_cl_check_date'] != ""){
        $strDate2 = DateThai( $rowFr['fr_cl_check_date'] );
    }else{
        $strDate2 = "";
    }


    if($rowFr['fr_cl_start'] != ""){
        $strDate3 = DateThai( $rowFr['fr_cl_start'] );
    }else{
        $strDate3 = "";
    }

?>                                                                  

            <tr>
              <td>&nbsp; <?= $no ?></td>
              <td nowrap style="text-align: left"><?= $rowFr['ps_cl_name'] .' ' . $rowFr['ps_cl_surname'] ?></td>
              <td nowrap><?= $pcid ?></td>
              <td><?= $rowFr['fr_cl_status'] ?></td>
              <td><?= $rowFr['fr_cl_offense'] ?></td>
              <td><?= $rowFr['fr_cl_in_prison'] ?></td> 
              <td nowrap><?= $strDate3 ?></td>
              <td nowrap><?= $strDate ?></td>
              <td style="text-align: left"><?= $rowFr['fr_cl_adrress1'] ?></td>
              <td style="text-align: left"><?= $rowFr['fr_cl_adrress2'] ?></td>
              <td><?= $rowFr['fr_cl_police'] ?></td>
              <td><?= $rowFr['fr_cl_remark'] ?></td>	
              <td><button onClick="go_detail('<?= $pcid ?>')" class="btn btn-success">รายละเอียด</button> <br>
                  &nbsp; <a href="home_visit.php?id=<?= $rowFr["fr_cl_aid"] ?>&pcid=<?= $pcid ?>" class="btn btn-warning mb-4" >ประวัติการเยี่ยม</a> </td>
            </tr>
                <?php
                    $no++;
                }
                ?>
      </tbody>
</table>
</div>
</div>
    <hr>
    

<script>
function go_detail(pcid)	
{
	window.location = "/WatchmanData/Show_All.php?pcid=" +pcid + "&rnd=" + "&page=freed";
}
</script>

