<?php
include '../Condb.php';
include '../users.inc.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// เมื่อมีการใช้ ตัวแปร Session จะต้องเปิดก่อน
//session_start();  // เปิดตัวแปร session
/*if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
*/

//echo '<pre>';
//print_r($_POST);
//echo '</pre>';
//
//echo '<hr';
//
//echo '<pre>';
//var_dump($_POST);
//echo '</pre>';
//
//exit();
// สำหรับตาราง politicial
$po_cl_type2 = $_POST[ 'po_cl_type2' ];
$po_cl_type = $_POST[ 'po_cl_type' ];
$po_cl_idcard = $_POST[ 'po_cl_idcard' ];
$po_cl_party = $_POST[ 'po_cl_party' ];
$po_cl_area = $_POST[ 'po_cl_area' ];
// สำหรับ ตาราง Personal
$ps_cl_type = $_POST[ 'ps_cl_type' ];
$ps_cl_prefix = $_POST[ 'ps_cl_prefix' ];
$ps_cl_sex = $_POST[ 'ps_cl_sex' ];
$ps_cl_name = $_POST[ 'ps_cl_name' ];               // >> tb_personal
$ps_cl_surname = $_POST[ 'ps_cl_surname' ];         // >> tb_personal
$ps_cl_nickname = $_POST[ 'ps_cl_nickname' ];       // >> tb_personal
$ps_cl_birthday2 = $_POST[ 'ps_cl_birthday2' ];       // >> tb_personal
$ps_cl_father = $_POST[ 'ps_cl_father' ];
$ps_cl_father_pid = $_POST[ 'ps_cl_father_pid' ];
$ps_cl_mother = $_POST[ 'ps_cl_mother' ];
$ps_cl_mother_pid = $_POST[ 'ps_cl_mother_pid' ];
$ps_cl_marital_status = $_POST[ 'ps_cl_marital_status' ];
$region = $_POST[ 'region' ];
$provincial = $_POST[ 'provincial' ];
$station = $_POST[ 'station' ];

// แปลงปีในเซฟลงฐานข้อมูลให้ถูกต้อง
// convert Thai dates to eng
if($ps_cl_birthday2 != '') {
	if(strpos($ps_cl_birthday2, '-') > 0) {
    	$dates = explode('-', $ps_cl_birthday2);	// d/m/y  => y-m-d
	}
	elseif(strpos($ps_cl_birthday2, '-') > 0) {
		$date = explode('-', $ps_cl_birthday2);	// y-m-d
		$dates = array($date[0], $date[1], $date[2]);	// d/m/Y  = y-m-d 0 1 2
	}
	// thai dates
	if(substr(''.$dates[0],0,2) ==='25') {
		$ps_cl_birthday2 = ($dates[0]-543) . '-' . $dates[1] . '-' . $dates[2];
	}
	// eng dates
	else {
    	$ps_cl_birthday2 = $dates[0] . '-' . $dates[1] . '-' . $dates[2];
	}
}

// save Image
$file1 = $_FILES[ 'ps_cl_image' ][ 'tmp_name' ];
if ( is_uploaded_file( $file1 ) ) {
 /* $ps_cl_image = "/policeinnopolis/uploaded/Image1/" . basename( $_FILES[ 'ps_cl_image' ][ 'name' ] );
	
  move_uploaded_file( $file1, "../" . $ps_cl_image );  */
    
    // เปลี่ยนชื่อไฟล์ก่อนบันทึก แก้ปัญหา ชื่อซ้ำกัน
    $ps_cl_image = "/policeinnopolis/uploaded/Image1/" . basename( $_FILES[ 'ps_cl_image' ][ 'name' ] );	    
    // file + ext    
    $ext = strrchr($ps_cl_image, ".");    
    $ps_cl_image = "../policeinnopolis/uploaded/Image1/File_" . time() . $ext; // file+time+ext        
    move_uploaded_file( $file1, $ps_cl_image );
    
} else {
  $ps_cl_image = '';
}

// 1. ข้อมูลนักการเมือง
// check exists data in personal เช็คว่ามีข้อมูลในตาราง Personal หรือยัง ถ้ามีแล้ว ไม่ต้องทำอะไร
$sql1 = "SELECT * FROM wm_tb_personal WHERE ps_cl_idcard='$po_cl_idcard' ";
//echo $sql3;
$res1 = mysqli_query($conn, $sql1);
$row1 = mysqli_fetch_array($res1);
// มีข้อมูลแล้ว
//if($row1 = mysqli_fetch_array($res1)) {
//   
//}
//// เปนข้อมูลใหม่ >>
//else {
    if($ps_cl_birthday2 == "") 
    {
        $sql1 = "INSERT INTO wm_tb_personal (ps_cl_type, ps_cl_idcard, ps_cl_prefix, ps_cl_sex, ps_cl_name, ps_cl_surname, ps_cl_nickname, ps_cl_father, ps_cl_father_pid, ps_cl_mother, ps_cl_mother_pid, ps_cl_marital_status, region, provincial, station, ps_cl_image)
        VALUES('$ps_cl_type','$po_cl_idcard', '$ps_cl_prefix', '$ps_cl_sex', '$ps_cl_name', '$ps_cl_surname', '$ps_cl_nickname', '$ps_cl_father', '$ps_cl_father_pid', '$ps_cl_mother', '$ps_cl_mother_pid', '$ps_cl_marital_status', '$region', '$provincial', '$station', '$ps_cl_image') ";
    }
    else {
        $sql1 = "INSERT INTO wm_tb_personal (ps_cl_type, ps_cl_idcard, ps_cl_prefix, ps_cl_sex, ps_cl_name, ps_cl_surname, ps_cl_nickname, ps_cl_birthday2, ps_cl_father, ps_cl_father_pid, ps_cl_mother, ps_cl_mother_pid, ps_cl_marital_status, region, provincial, station, ps_cl_image)
        VALUES('$ps_cl_type','$po_cl_idcard', '$ps_cl_prefix', '$ps_cl_sex', '$ps_cl_name', '$ps_cl_surname', '$ps_cl_nickname', '$ps_cl_birthday2', '$ps_cl_father', '$ps_cl_father_pid', '$ps_cl_mother', '$ps_cl_mother_pid', '$ps_cl_marital_status', '$region', '$provincial', '$station', '$ps_cl_image') ";
    }
    mysqli_query($conn, $sql1);
    
    //echo( $sql1 );
    echo mysqli_error($conn);
//}
//
// $sql3 >> insert >> personal2

$sql = "INSERT INTO wm_politician (po_cl_type, po_cl_type2, po_cl_idcard, po_cl_party, po_cl_area) VALUES('$po_cl_type', '$po_cl_type2', '$po_cl_idcard', '$po_cl_party', '$po_cl_area' ) ";
$result=mysqli_query($conn, $sql);


if($result){
	echo "<script>alert('บันทึกข้อมูลเรียบร้อย');</script>";	
}else{
	echo "<script>alert(\"บันทึกข้อมูลไม่สำเร็จ\\n" . print_r(mysqli_error($conn), true) . "\");</script>";
}




// เก็บข้อมูล Action
$inputs = str_replace("'", "", compact_array($_POST));
$prev = "";//str_replace("'", "", implode(',', $row_0));

$acc = $user['account'];
$sql3 = "INSERT wm_tb_user_action (at_cl_user,at_cl_date,at_cl_action,at_cl_input,at_cl_prev) VALUES('$acc',CURRENT_TIMESTAMP,'Add new Politician {$po_cl_idcard}', '$inputs', '{$prev}')";
mysqli_query($conn, $sql3);
//

mysqli_close($conn);

echo "<script>window.location='/Bansuan/Show_Gen_person.php?pcid=" . $po_cl_idcard . " ';</script>";
?>

