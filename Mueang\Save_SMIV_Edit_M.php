<?php
include '../Condb.php';
include '../users.inc.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// เมื่อมีการใช้ ตัวแปร Session จะต้องเปิดก่อน
//session_start();  // เปิดตัวแปร session
/*if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
*/

//print_r($_POST);

$smi_cl_aid = $_POST['smi_cl_aid'];
$smi_cl_idcard = $_POST[ 'smi_cl_idcard' ];
$smi_cl_type = $_POST[ 'smi_cl_type' ];
$smi_cl_level = $_POST[ 'smi_cl_level' ];
$smi_cl_assessment = $_POST[ 'smi_cl_assessment' ];
$smi_cl_color = $_POST[ 'smi_cl_color' ];
$smi_cl_treatment = $_POST[ 'smi_cl_treatment' ];
$smi_cl_province = $_POST[ 'smi_cl_province' ];
$smi_cl_amphur = $_POST[ 'smi_cl_amphur' ];
$smi_cl_tumbon = $_POST[ 'smi_cl_tumbon' ];
$smi_cl_moo = $_POST[ 'smi_cl_moo' ];
$smi_cl_remark = $_POST[ 'smi_cl_remark' ];

$ps_cl_aid = $_POST['ps_cl_aid'];
$ps_cl_idcard = $_POST[ 'smi_cl_idcard' ];
$ps_cl_prefix = $_POST[ 'ps_cl_prefix' ];
$ps_cl_sex = $_POST[ 'ps_cl_sex' ];
$ps_cl_name = $_POST[ 'ps_cl_name' ];               // >> tb_personal
$ps_cl_surname = $_POST[ 'ps_cl_surname' ];         // >> tb_personal
$ps_cl_nickname = $_POST[ 'ps_cl_nickname' ];       // >> tb_personal
$ps_cl_birthday2 = $_POST[ 'ps_cl_birthday2' ];       // >> tb_personal
$ps_cl_father = $_POST[ 'ps_cl_father' ];
$ps_cl_father_pid = $_POST[ 'ps_cl_father_pid' ];
$ps_cl_mother = $_POST[ 'ps_cl_mother' ];
$ps_cl_mother_pid = $_POST[ 'ps_cl_mother_pid' ];
$ps_cl_marital_status = $_POST[ 'ps_cl_marital_status' ];
$region = $_POST[ 'region' ];
$provincial = $_POST[ 'provincial' ];
$station = $_POST[ 'station' ];

//$ps_cl_birthday2 = thai_date_2_eng($ps_cl_birthday);


// save Image ใหม่แทน
$file1 = $_FILES[ 'ps_cl_image' ][ 'tmp_name' ];

if ( is_uploaded_file( $file1 ) ) {


    //ลบรูปเก่าในเซิร์ฟเวอร์ก่อน
    $sql = "SELECT * FROM wm_tb_personal WHERE ps_cl_aid='ps_cl_aid' ";
    $res = mysqli_query($conn,$sql);
    if($row = mysqli_fetch_array($res))
    {
        if(($row['ps_cl_image'] != '') && file_exists($row['ps_cl_image'])) {
            unlink( $row['ps_cl_image'] );
        }    
    }

    //เปลี่ยนชื่อไฟล์ใหม่
    $ps_cl_image = "/policeinnopolis/uploaded/Image1/" . basename( $_FILES[ 'ps_cl_image' ][ 'name' ] );	    
    // file + ext    
    $ext = strrchr($ps_cl_image, ".");    
    $ps_cl_image = "../policeinnopolis/uploaded/Image1/File_" . time() . $ext; // file+time+ext        
    move_uploaded_file( $file1, $ps_cl_image );  
} 
else {
  $ps_cl_image = '';
}

// 1. ข้อมูลส่วนบุคคล
$sql1 = "UPDATE wm_tb_personal SET " .
        "ps_cl_idcard = '$smi_cl_idcard', " .
        "ps_cl_prefix = '$ps_cl_prefix', " .
        "ps_cl_sex = '$ps_cl_sex', " .
        "ps_cl_name = '$ps_cl_name', " .    
        "ps_cl_surname = '$ps_cl_surname', " .
        "ps_cl_nickname = '$ps_cl_nickname', " .
        "ps_cl_father = '$ps_cl_father', " .
        "ps_cl_father_pid = '$ps_cl_father_pid', " .
        "ps_cl_mother = '$ps_cl_mother', " .
        "ps_cl_mother_pid = '$ps_cl_mother_pid', " .
        "ps_cl_marital_status = '$ps_cl_marital_status', " .
        "region = '$region', " .
        "provincial = '$provincial', " .
        "station = '$station' " .
        (($ps_cl_image == '') ? '' : (",ps_cl_image = '$ps_cl_image' ")) .
        "WHERE ps_cl_aid='$ps_cl_aid' ";    
$result1 = mysqli_query($conn, $sql1);


// $sql2 บันทึกตารางผู้ป่วย
$sql2 = "UPDATE wm_tb_SMIV SET " .
        "smi_cl_idcard = '$smi_cl_idcard', " .
        "smi_cl_type = '$smi_cl_type', " .
        "smi_cl_level = '$smi_cl_level', " .
        "smi_cl_assessment = '$smi_cl_assessment', " .
        "smi_cl_color = '$smi_cl_color', " .
        "smi_cl_treatment = '$smi_cl_treatment', " .
        "smi_cl_moo = '$smi_cl_moo', " .
        "smi_cl_tumbon = '$smi_cl_tumbon', " .
        "smi_cl_amphur = '$smi_cl_amphur', " .
        "smi_cl_province = '$smi_cl_province', " .
        "region = '$region', " .
        "provincial = '$provincial', " .
        "station = '$station', " .
        "smi_cl_remark = '$smi_cl_remark' " .    
        "WHERE smi_cl_aid = '$smi_cl_aid' ";    

$result2=mysqli_query($conn, $sql2);


    //echo( $sql1 );
    echo mysqli_error($conn);

if($result2){
	echo "<script>alert('บันทึกข้อมูลเรียบร้อย');</script>";	
}else{
	echo "<script>alert(\"บันทึกข้อมูลไม่สำเร็จ\\n" . print_r(mysqli_error($conn), true) . "\");</script>";
}


// เก็บข้อมูล Action
$inputs = str_replace("'", "", compact_array($_POST));
$prev = "";//str_replace("'", "", implode(',', $row_0));

$acc = $user['account'];
$sql3 = "INSERT wm_tb_user_action (at_cl_user,at_cl_date,at_cl_action,at_cl_input,at_cl_prev) VALUES('$acc',CURRENT_TIMESTAMP,'Save Edit SMI-V_Mueang {$smi_cl_idcard}', '$inputs', '{$prev}')";
mysqli_query($conn, $sql3);
//

mysqli_close($conn);

echo "<script>window.location='Show_SMI-V_Mueang.php?pcid=" . $smi_cl_idcard . " ';</script>";
?>

