<?php
include '../Condb.php';
include '../users.inc.php';

$acc = $user['account'];
// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// เมื่อมีการใช้ ตัวแปร Session จะต้องเปิดก่อน
//session_start();  // เปิดตัวแปร session
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}


// $_GET[''] >> url
// $_POST[] >> form
// $_FILES[] >> form >> type file

// ADD ?
// $cd_cl_aid = $_POST[ 'cd_cl_aid' ];

$cd_cl_idcard = $_POST[ 'cd_cl_idcard' ];
$cd_cl_idcard_child = $_POST[ 'cd_cl_idcard_child' ];
$cd_cl_prefix = $_POST[ 'cd_cl_prefix' ];
$cd_cl_sex = $_POST[ 'cd_cl_sex' ];
$cd_cl_name = $_POST[ 'cd_cl_name' ];               // >> tb_personal
$cd_cl_surname = $_POST[ 'cd_cl_surname' ];         // >> tb_personal
$cd_cl_nickname = $_POST[ 'cd_cl_nickname' ];       // >> tb_personal
$cd_cl_birthday2 = $_POST[ 'cd_cl_birthday2' ];       // >> tb_personal
$cd_cl_father = $_POST[ 'cd_cl_father' ];
$cd_cl_father_pid = $_POST[ 'cd_cl_father_pid' ];
$cd_cl_mother = $_POST[ 'cd_cl_mother' ];
$cd_cl_mother_pid = $_POST[ 'cd_cl_mother_pid' ];
$cd_cl_spouse = $_POST[ 'cd_cl_spouse' ];

//แปลงวันที่ พ.ศ. ให้เป็น ค.ศ.
//$cd_cl_birthday2 = thai_date_2_eng($cd_cl_birthday);

// save Image
$file1 = $_FILES[ 'cd_cl_image' ][ 'tmp_name' ];
if ( is_uploaded_file( $file1 ) ) {
    /*
  $cd_cl_image = "/policeinnopolis/uploaded/Image1/" . basename( $_FILES[ 'cd_cl_image' ][ 'name' ] );	
  move_uploaded_file( $file1, "../" . $cd_cl_image );  
    */
    // เปลี่ยนชื่อไฟล์รูปภาพก่อน
    $cd_cl_image = "/policeinnopolis/uploaded/Image1/" . basename( $_FILES[ 'cd_cl_image' ][ 'name' ] );	    
    // file + ext    
    $ext = strrchr($cd_cl_image, ".");    
    $cd_cl_image = "../policeinnopolis/uploaded/Image1/File_" . time() . $ext; // file+time+ext        
    move_uploaded_file( $file1, $cd_cl_image ); 
    
} else {
  $cd_cl_image = '';
}
//php >> $[AA-z]


// $sql2 >> insert ไปลงตาราง >> personal
// check exists data in personal เช็คว่ามีข้อมูลในตาราง Personal หรือยัง ถ้ามีแล้ว ไม่ต้องทำอะไร
$sql3 = "SELECT * FROM wm_tb_personal WHERE ps_cl_idcard='$cd_cl_idcard_child' ";
//echo $sql3;
$res3 = mysqli_query($conn, $sql3);
// มีข้อมูลแล้ว
if($row3 = mysqli_fetch_array($res3)) {
   
}
// เปนข้อมูลใหม่ >>
else {
    $sql3 = "INSERT INTO wm_tb_personal (ps_cl_idcard, ps_cl_prefix, ps_cl_sex, ps_cl_name, ps_cl_surname, ps_cl_nickname, ps_cl_birthday2, ps_cl_father, ps_cl_father_pid, ps_cl_mother, ps_cl_mother_pid,  ps_cl_image)
    VALUES('$cd_cl_idcard_child', '$cd_cl_prefix', '$cd_cl_sex', '$cd_cl_name', '$cd_cl_surname', '$cd_cl_nickname', '$cd_cl_birthday2', '$cd_cl_father', '$cd_cl_father_pid', '$cd_cl_mother', '$cd_cl_mother_pid', '$cd_cl_image') ";
    mysqli_query($conn, $sql3);
    
   // echo( $sql3 );
   // echo mysqli_error($conn);
}
//
// $sql3 >> insert >> personal2


$sql = "INSERT INTO wm_tb_child(cd_cl_idcard, cd_cl_idcard_child, cd_cl_prefix, cd_cl_sex, cd_cl_name, cd_cl_surname, cd_cl_nickname, cd_cl_birthday2, cd_cl_father, cd_cl_father_pid, cd_cl_mother, cd_cl_mother_pid, cd_cl_spouse, cd_cl_image) VALUES('$cd_cl_idcard', '$cd_cl_idcard_child', '$cd_cl_prefix', '$cd_cl_sex', '$cd_cl_name', '$cd_cl_surname', '$cd_cl_nickname', '$cd_cl_birthday2', '$cd_cl_father', '$cd_cl_father_pid','$cd_cl_mother', '$cd_cl_mother_pid', '$cd_cl_spouse', '$cd_cl_image') ";

$result=mysqli_query($conn, $sql);
if($result){
	//echo "<script>alert('บันทึกข้อมูลเรียบร้อย');</script>";	
    echo "<script> window.location='Add_Personal.php?pcid=$ps_cl_idcard'; </script>";
    $_SESSION['Success']="บันทึกสำเร็จ"; // ตัวแปร session ตั้งชื่อว่า Success
}else{
	//echo "<script>alert(\"บันทึกข้อมูลไม่สำเร็จ\\n" . print_r(mysqli_error($conn), true) . "\");</script>";
    echo "<script> window.location='Add_Personal.php?pcid=$cps_cl_idcard'; </script>"; 
    $_SESSION['error']="บันทึกไม่สำเร็จ"; // ตัวแปร session ตั้งชื่อว่า error หากบันทึกไม่สำเร็จ
}

// เก็บข้อมูล Action สำหรับ SAVE
$inputs = str_replace("'", "", compact_array($_POST));
$prev = "";//str_replace("'", "", implode(',', $row_0));

$acc = $user['account'];
$sql3 = "INSERT wm_tb_user_action (at_cl_user,at_cl_date,at_cl_action,at_cl_input,at_cl_prev) VALUES('$acc',CURRENT_TIMESTAMP,'Add new child {$cd_cl_idcard}', '$inputs', '{$prev}')";
mysqli_query($conn, $sql3);
//

mysqli_close($conn);

//echo "<script>window.location='Show_All.php?pcid=" . $cd_cl_idcard . "&page=child';</script>";
?>

