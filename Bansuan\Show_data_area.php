<?php
include '../Condb.php';
include '../users.inc.php';
include '../right_user.php';
include '../Head_image.php';

// Call this function at the top of each page to log the page view
logPageView($user['account'], $_SERVER['REQUEST_URI']);

// เมื่อมีการใช้ ตัวแปร Session จะต้องเปิดก่อน
//session_start();  // เปิดตัวแปร session
/*if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
*/

$sql_smiv = "SELECT " .
            "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='1' AND `station`='$station' ) AS TotalG, ". //จิตเวช เขียว
            "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='2' AND `station`='$station' ) AS TotalY, ". //จิตเวช เหลือง
            "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='3' AND `station`='$station' ) AS TotalR, ". //จิตเวช แดง
    
            "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='1' AND `station`='$station' AND `smi_cl_tumbon`='640102' ) AS BSG1, ". //จิตเวช เขียว ตำบลบ้านสวน
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='1' AND `station`='$station' AND `smi_cl_tumbon`='640102'AND `smi_cl_moo`='1' ) AS BSG11, ". //จิตเวช เขียว ตำบลบ้านสวน หมู่ 1
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='1' AND `station`='$station' AND `smi_cl_tumbon`='640102'AND `smi_cl_moo`='2' ) AS BSG12, ". //จิตเวช เขียว ตำบลบ้านสวน หมู่ 2
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='1' AND `station`='$station' AND `smi_cl_tumbon`='640102'AND `smi_cl_moo`='3' ) AS BSG13, ". //จิตเวช เขียว ตำบลบ้านสวน หมู่ 3
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='1' AND `station`='$station' AND `smi_cl_tumbon`='640102'AND `smi_cl_moo`='4' ) AS BSG14, ". //จิตเวช เขียว ตำบลบ้านสวน หมู่ 4
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='1' AND `station`='$station' AND `smi_cl_tumbon`='640102'AND `smi_cl_moo`='5' ) AS BSG15, ". //จิตเวช เขียว ตำบลบ้านสวน หมู่ 5
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='1' AND `station`='$station' AND `smi_cl_tumbon`='640102'AND `smi_cl_moo`='6' ) AS BSG16, ". //จิตเวช เขียว ตำบลบ้านสวน หมู่ 6
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='1' AND `station`='$station' AND `smi_cl_tumbon`='640102'AND `smi_cl_moo`='7' ) AS BSG17, ". //จิตเวช เขียว ตำบลบ้านสวน หมู่ 7
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='1' AND `station`='$station' AND `smi_cl_tumbon`='640102'AND `smi_cl_moo`='8' ) AS BSG18, ". //จิตเวช เขียว ตำบลบ้านสวน หมู่ 8
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='1' AND `station`='$station' AND `smi_cl_tumbon`='640102'AND `smi_cl_moo`='9' ) AS BSG19, ". //จิตเวช เขียว ตำบลบ้านสวน หมู่ 9
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='1' AND `station`='$station' AND `smi_cl_tumbon`='640102'AND `smi_cl_moo`='10' ) AS BSG110, ". //จิตเวช เขียว ตำบลบ้านสวน หมู่ 10
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='1' AND `station`='$station' AND `smi_cl_tumbon`='640102'AND `smi_cl_moo`='11' ) AS BSG111, ". //จิตเวช เขียว ตำบลบ้านสวน หมู่ 11
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='1' AND `station`='$station' AND `smi_cl_tumbon`='640102'AND `smi_cl_moo`='12' ) AS BSG112, ". //จิตเวช เขียว ตำบลบ้านสวน หมู่ 12
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='1' AND `station`='$station' AND `smi_cl_tumbon`='640102'AND `smi_cl_moo`='13' ) AS BSG113, ". //จิตเวช เขียว ตำบลบ้านสวน หมู่ 13
    
            "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='2' AND `station`='$station' AND `smi_cl_tumbon`='640102' ) AS BSY1, ". //จิตเวช เหลือง ตำบลบ้านสวน
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='2' AND `station`='$station' AND `smi_cl_tumbon`='640102'AND `smi_cl_moo`='1' ) AS BSY11, ". //จิตเวช เหลือง ตำบลบ้านสวน หมู่ 1
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='2' AND `station`='$station' AND `smi_cl_tumbon`='640102'AND `smi_cl_moo`='2' ) AS BSY12, ". //จิตเวช เหลือง ตำบลบ้านสวน หมู่ 2
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='2' AND `station`='$station' AND `smi_cl_tumbon`='640102'AND `smi_cl_moo`='3' ) AS BSY13, ". //จิตเวช เหลือง ตำบลบ้านสวน หมู่ 3
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='2' AND `station`='$station' AND `smi_cl_tumbon`='640102'AND `smi_cl_moo`='4' ) AS BSY14, ". //จิตเวช เหลือง ตำบลบ้านสวน หมู่ 4
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='2' AND `station`='$station' AND `smi_cl_tumbon`='640102'AND `smi_cl_moo`='5' ) AS BSY15, ". //จิตเวช เหลือง ตำบลบ้านสวน หมู่ 5
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='2' AND `station`='$station' AND `smi_cl_tumbon`='640102'AND `smi_cl_moo`='6' ) AS BSY16, ". //จิตเวช เหลือง ตำบลบ้านสวน หมู่ 6
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='2' AND `station`='$station' AND `smi_cl_tumbon`='640102'AND `smi_cl_moo`='7' ) AS BSY17, ". //จิตเวช เหลือง ตำบลบ้านสวน หมู่ 7
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='2' AND `station`='$station' AND `smi_cl_tumbon`='640102'AND `smi_cl_moo`='8' ) AS BSY18, ". //จิตเวช เหลือง ตำบลบ้านสวน หมู่ 8
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='2' AND `station`='$station' AND `smi_cl_tumbon`='640102'AND `smi_cl_moo`='9' ) AS BSY19, ". //จิตเวช เหลือง ตำบลบ้านสวน หมู่ 9
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='2' AND `station`='$station' AND `smi_cl_tumbon`='640102'AND `smi_cl_moo`='10' ) AS BSY110, ". //จิตเวช เหลือง ตำบลบ้านสวน หมู่ 10
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='2' AND `station`='$station' AND `smi_cl_tumbon`='640102'AND `smi_cl_moo`='11' ) AS BSY111, ". //จิตเวช เหลือง ตำบลบ้านสวน หมู่ 11
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='2' AND `station`='$station' AND `smi_cl_tumbon`='640102'AND `smi_cl_moo`='12' ) AS BSY112, ". //จิตเวช เหลือง ตำบลบ้านสวน หมู่ 12
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='2' AND `station`='$station' AND `smi_cl_tumbon`='640102'AND `smi_cl_moo`='13' ) AS BSY113, ". //จิตเวช เหลือง ตำบลบ้านสวน หมู่ 13    
    
            "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='3' AND `station`='$station' AND `smi_cl_tumbon`='640102' ) AS BSR1, ". //จิตเวช แดง ตำบลบ้านสวน
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='3' AND `station`='$station' AND `smi_cl_tumbon`='640102'AND `smi_cl_moo`='1' ) AS BSR11, ". //จิตเวช แดง ตำบลบ้านสวน หมู่ 1
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='3' AND `station`='$station' AND `smi_cl_tumbon`='640102'AND `smi_cl_moo`='2' ) AS BSR12, ". //จิตเวช แดง ตำบลบ้านสวน หมู่ 2
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='3' AND `station`='$station' AND `smi_cl_tumbon`='640102'AND `smi_cl_moo`='3' ) AS BSR13, ". //จิตเวช แดง ตำบลบ้านสวน หมู่ 3
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='3' AND `station`='$station' AND `smi_cl_tumbon`='640102'AND `smi_cl_moo`='4' ) AS BSR14, ". //จิตเวช แดง ตำบลบ้านสวน หมู่ 4
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='3' AND `station`='$station' AND `smi_cl_tumbon`='640102'AND `smi_cl_moo`='5' ) AS BSR15, ". //จิตเวช แดง ตำบลบ้านสวน หมู่ 5
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='3' AND `station`='$station' AND `smi_cl_tumbon`='640102'AND `smi_cl_moo`='6' ) AS BSR16, ". //จิตเวช แดง ตำบลบ้านสวน หมู่ 6
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='3' AND `station`='$station' AND `smi_cl_tumbon`='640102'AND `smi_cl_moo`='7' ) AS BSR17, ". //จิตเวช แดง ตำบลบ้านสวน หมู่ 7
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='3' AND `station`='$station' AND `smi_cl_tumbon`='640102'AND `smi_cl_moo`='8' ) AS BSR18, ". //จิตเวช แดง ตำบลบ้านสวน หมู่ 8
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='3' AND `station`='$station' AND `smi_cl_tumbon`='640102'AND `smi_cl_moo`='9' ) AS BSR19, ". //จิตเวช แดง ตำบลบ้านสวน หมู่ 9
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='3' AND `station`='$station' AND `smi_cl_tumbon`='640102'AND `smi_cl_moo`='10' ) AS BSR110, ". //จิตเวช แดง ตำบลบ้านสวน หมู่ 10
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='3' AND `station`='$station' AND `smi_cl_tumbon`='640102'AND `smi_cl_moo`='11' ) AS BSR111, ". //จิตเวช แดง ตำบลบ้านสวน หมู่ 11
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='3' AND `station`='$station' AND `smi_cl_tumbon`='640102'AND `smi_cl_moo`='12' ) AS BSR112, ". //จิตเวช แดง ตำบลบ้านสวน หมู่ 12
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='3' AND `station`='$station' AND `smi_cl_tumbon`='640102'AND `smi_cl_moo`='13' ) AS BSR113, ". //จิตเวช แดง ตำบลบ้านสวน หมู่ 13 

            "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='1' AND `station`='$station' AND `smi_cl_tumbon`='640107' ) AS BLG2, ". //จิตเวช เขียว ตำบลบ้านหลุม
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='1' AND `station`='$station' AND `smi_cl_tumbon`='640107'AND `smi_cl_moo`='1' ) AS BLG21, ". //จิตเวช เขียว ตำบลบ้านสวน หมู่ 1
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='1' AND `station`='$station' AND `smi_cl_tumbon`='640107'AND `smi_cl_moo`='2' ) AS BLG22, ". //จิตเวช เขียว ตำบลบ้านสวน หมู่ 2
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='1' AND `station`='$station' AND `smi_cl_tumbon`='640107'AND `smi_cl_moo`='3' ) AS BLG23, ". //จิตเวช เขียว ตำบลบ้านสวน หมู่ 3
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='1' AND `station`='$station' AND `smi_cl_tumbon`='640107'AND `smi_cl_moo`='4' ) AS BLG24, ". //จิตเวช เขียว ตำบลบ้านสวน หมู่ 4
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='1' AND `station`='$station' AND `smi_cl_tumbon`='640107'AND `smi_cl_moo`='5' ) AS BLG25, ". //จิตเวช เขียว ตำบลบ้านสวน หมู่ 5
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='1' AND `station`='$station' AND `smi_cl_tumbon`='640107'AND `smi_cl_moo`='6' ) AS BLG26, ". //จิตเวช เขียว ตำบลบ้านสวน หมู่ 6
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='1' AND `station`='$station' AND `smi_cl_tumbon`='640107'AND `smi_cl_moo`='7' ) AS BLG27, ". //จิตเวช เขียว ตำบลบ้านสวน หมู่ 7
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='1' AND `station`='$station' AND `smi_cl_tumbon`='640107'AND `smi_cl_moo`='8' ) AS BLG28, ". //จิตเวช เขียว ตำบลบ้านสวน หมู่ 8
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='1' AND `station`='$station' AND `smi_cl_tumbon`='640107'AND `smi_cl_moo`='9' ) AS BLG29, ". //จิตเวช เขียว ตำบลบ้านสวน หมู่ 9

            "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='2' AND `station`='$station' AND `smi_cl_tumbon`='640107' ) AS BLY2, ". //จิตเวช เหลือง ตำบลบ้านหลุม
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='2' AND `station`='$station' AND `smi_cl_tumbon`='640107'AND `smi_cl_moo`='1' ) AS BLY21, ". //จิตเวช เหลือง ตำบลบ้านหลุม หมู่ 1
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='2' AND `station`='$station' AND `smi_cl_tumbon`='640107'AND `smi_cl_moo`='2' ) AS BLY22, ". //จิตเวช เหลือง ตำบลบ้านหลุม หมู่ 2
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='2' AND `station`='$station' AND `smi_cl_tumbon`='640107'AND `smi_cl_moo`='3' ) AS BLY23, ". //จิตเวช เหลือง ตำบลบ้านหลุม หมู่ 3
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='2' AND `station`='$station' AND `smi_cl_tumbon`='640107'AND `smi_cl_moo`='4' ) AS BLY24, ". //จิตเวช เหลือง ตำบลบ้านหลุม หมู่ 4
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='2' AND `station`='$station' AND `smi_cl_tumbon`='640107'AND `smi_cl_moo`='5' ) AS BLY25, ". //จิตเวช เหลือง ตำบลบ้านหลุม หมู่ 5
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='2' AND `station`='$station' AND `smi_cl_tumbon`='640107'AND `smi_cl_moo`='6' ) AS BLY26, ". //จิตเวช เหลือง ตำบลบ้านหลุม หมู่ 6
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='2' AND `station`='$station' AND `smi_cl_tumbon`='640107'AND `smi_cl_moo`='7' ) AS BLY27, ". //จิตเวช เหลือง ตำบลบ้านหลุม หมู่ 7
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='2' AND `station`='$station' AND `smi_cl_tumbon`='640107'AND `smi_cl_moo`='8' ) AS BLY28, ". //จิตเวช เหลือง ตำบลบ้านหลุม หมู่ 8
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='2' AND `station`='$station' AND `smi_cl_tumbon`='640107'AND `smi_cl_moo`='9' ) AS BLY29, ". //จิตเวช เหลือง ตำบลบ้านหลุม หมู่ 9
    
            "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='3' AND `station`='$station' AND `smi_cl_tumbon`='640107' ) AS BLR2, ". //จิตเวช แดง ตำบลบ้านหลุม 
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='3' AND `station`='$station' AND `smi_cl_tumbon`='640107'AND `smi_cl_moo`='1' ) AS BLR21, ". //จิตเวช แดง ตำบลบ้านหลุม หมู่ 1
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='3' AND `station`='$station' AND `smi_cl_tumbon`='640107'AND `smi_cl_moo`='2' ) AS BLR22, ". //จิตเวช แดง ตำบลบ้านหลุม หมู่ 2
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='3' AND `station`='$station' AND `smi_cl_tumbon`='640107'AND `smi_cl_moo`='3' ) AS BLR23, ". //จิตเวช แดง ตำบลบ้านหลุม หมู่ 3
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='3' AND `station`='$station' AND `smi_cl_tumbon`='640107'AND `smi_cl_moo`='4' ) AS BLR24, ". //จิตเวช แดง ตำบลบ้านหลุม หมู่ 4
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='3' AND `station`='$station' AND `smi_cl_tumbon`='640107'AND `smi_cl_moo`='5' ) AS BLR25, ". //จิตเวช แดง ตำบลบ้านหลุม หมู่ 5
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='3' AND `station`='$station' AND `smi_cl_tumbon`='640107'AND `smi_cl_moo`='6' ) AS BLR26, ". //จิตเวช แดง ตำบลบ้านหลุม หมู่ 6
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='3' AND `station`='$station' AND `smi_cl_tumbon`='640107'AND `smi_cl_moo`='7' ) AS BLR27, ". //จิตเวช แดง ตำบลบ้านหลุม หมู่ 7
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='3' AND `station`='$station' AND `smi_cl_tumbon`='640107'AND `smi_cl_moo`='8' ) AS BLR28, ". //จิตเวช แดง ตำบลบ้านหลุม หมู่ 8
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='3' AND `station`='$station' AND `smi_cl_tumbon`='640107'AND `smi_cl_moo`='9' ) AS BLR29, ". //จิตเวช แดง ตำบลบ้านหลุม หมู่ 9

            "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='1' AND `station`='$station' AND `smi_cl_tumbon`='640108' ) AS TTG3, ". //จิตเวช เขียว ตำบลตาลเตี้ย
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='1' AND `station`='$station' AND `smi_cl_tumbon`='640108'AND `smi_cl_moo`='1' ) AS TTG31, ". //จิตเวช เขียว ตำบลตาลเตี้ย หมู่ 1
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='1' AND `station`='$station' AND `smi_cl_tumbon`='640108'AND `smi_cl_moo`='2' ) AS TTG32, ". //จิตเวช เขียว ตำบลตาลเตี้ย หมู่ 2
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='1' AND `station`='$station' AND `smi_cl_tumbon`='640108'AND `smi_cl_moo`='3' ) AS TTG33, ". //จิตเวช เขียว ตำบลตาลเตี้ย หมู่ 3
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='1' AND `station`='$station' AND `smi_cl_tumbon`='640108'AND `smi_cl_moo`='4' ) AS TTG34, ". //จิตเวช เขียว ตำบลตาลเตี้ย หมู่ 4
    
            "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='2' AND `station`='$station' AND `smi_cl_tumbon`='640108' ) AS TY3, ". //จิตเวช เหลือง ตำบลตาลเตี้ย
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='2' AND `station`='$station' AND `smi_cl_tumbon`='640108'AND `smi_cl_moo`='1' ) AS TTY31, ". //จิตเวช เหลือง ตำบลตาลเตี้ย หมู่ 1
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='2' AND `station`='$station' AND `smi_cl_tumbon`='640108'AND `smi_cl_moo`='2' ) AS TTY32, ". //จิตเวช เหลือง ตำบลตาลเตี้ย หมู่ 2
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='2' AND `station`='$station' AND `smi_cl_tumbon`='640108'AND `smi_cl_moo`='3' ) AS TTY33, ". //จิตเวช เหลือง ตำบลตาลเตี้ย หมู่ 3
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='2' AND `station`='$station' AND `smi_cl_tumbon`='640108'AND `smi_cl_moo`='4' ) AS TTY34, ". //จิตเวช เหลือง ตำบลตาลเตี้ย หมู่ 4
    
            "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='3' AND `station`='$station' AND `smi_cl_tumbon`='640108' ) AS TR3, ". //จิตเวช แดง ตำบลตาลเตี้ย
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='3' AND `station`='$station' AND `smi_cl_tumbon`='640108'AND `smi_cl_moo`='1' ) AS TTR31, ". //จิตเวช แดง ตำบลตาลเตี้ย หมู่ 1
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='3' AND `station`='$station' AND `smi_cl_tumbon`='640108'AND `smi_cl_moo`='2' ) AS TTR32, ". //จิตเวช แดง ตำบลตาลเตี้ย หมู่ 2
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='3' AND `station`='$station' AND `smi_cl_tumbon`='640108'AND `smi_cl_moo`='3' ) AS TTR33, ". //จิตเวช แดง ตำบลตาลเตี้ย หมู่ 3
                    "(SELECT COUNT(*) FROM wm_tb_SMIV WHERE `smi_cl_color`='3' AND `station`='$station' AND `smi_cl_tumbon`='640108'AND `smi_cl_moo`='4' ) AS TTR34 ". //จิตเวช แดง ตำบลตาลเตี้ย หมู่ 4    
            "";
$res_smiv = mysqli_query($conn, $sql_smiv);
$row_smiv = mysqli_fetch_array($res_smiv);

//////// ข้อมูล เกี่ยวกับยาเสพติด คิวรี่ ทีละรายการ เพื่อที่จะ Group By เลขบัตร//////////////////////
// ยอดรวมทั้งหมด ของ ผลิต(6) จำหน่าย(7) ครอบครอง(8) เสพ(9) ในตำบลบ้านสวน(640102) บ้านหลุม(640107) และตาลเตี้ย(640108)
$sql_total = "SELECT ch_cl_crimes_type2, COUNT(DISTINCT ch_cl_idcard) as count FROM wm_tb_crimes_history WHERE station='$station' AND ch_cl_Tumbon IN('640102','640107','640108') GROUP BY ch_cl_crimes_type2";
$res_total = mysqli_query($conn, $sql_total);

$Total6 = $Total7 = $Total8 = $Total9 = 0;
while ($row_total = mysqli_fetch_assoc($res_total)) {
    switch ($row_total['ch_cl_crimes_type2']) {
        case '6':
            $Total6 = $row_total['count'];
            break;
        case '7':
            $Total7 = $row_total['count'];
            break;
        case '8':
            $Total8 = $row_total['count'];
            break;
        case '9':
            $Total9 = $row_total['count'];
            break;
    }
}
///////////////////////////////////////////////////////////////////////

// Chat GPT บ้านสวน //
// ผลิต บ้านสวน
$sql_bs6 = "SELECT ch_cl_Moo, COUNT(DISTINCT ch_cl_idcard) as count FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='6' AND station='$station' AND ch_cl_Tumbon='640102' GROUP BY ch_cl_Moo";
$res_bs6 = mysqli_query($conn, $sql_bs6);

$BS1 = $BS2 = $BS3 = $BS4 = $BS5 = $BS6 = $BS7 = $BS8 = $BS9 = $BS10 = $BS11 = $BS12 = $BS13 = 0;
while ($row_bs6 = mysqli_fetch_assoc($res_bs6)) {
    switch ($row_bs6['ch_cl_Moo']) {
        case '1':
            $BS1 = $row_bs6['count'];
            break;
        case '2':
            $BS2 = $row_bs6['count'];
            break;
        case '3':
            $BS3 = $row_bs6['count'];
            break;
        case '4':
            $BS4 = $row_bs6['count'];
            break;
        case '5':
            $BS5 = $row_bs6['count'];
            break;
        case '6':
            $BS6 = $row_bs6['count'];
            break;
        case '7':
            $BS7 = $row_bs6['count'];
            break;
        case '8':
            $BS8 = $row_bs6['count'];
            break;
        case '9':
            $BS9 = $row_bs6['count'];
            break;
        case '10':
            $BS10 = $row_bs6['count'];
            break;
        case '11':
            $BS11 = $row_bs6['count'];
            break;
        case '12':
            $BS12 = $row_bs6['count'];
            break;
        case '13':
            $BS13 = $row_bs6['count'];
            break;
    }
}

$T1 = $BS1 + $BS2 + $BS3 + $BS4 + $BS5 + $BS6 + $BS7 + $BS8 + $BS9 + $BS10 + $BS11 + $BS12 + $BS13;

// จำหน่าย บ้านสวน
$sql_bs7 = "SELECT ch_cl_Moo, COUNT(DISTINCT ch_cl_idcard) as count FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='7' AND station='$station' AND ch_cl_Tumbon='640102' GROUP BY ch_cl_Moo";
$res_bs7 = mysqli_query($conn, $sql_bs7);

$BS21 = $BS22 = $BS23 = $BS24 = $BS25 = $BS26 = $BS27 = $BS28 = $BS29 = $BS210 = $BS211 = $BS212 = $BS213 = 0;
while ($row_bs7 = mysqli_fetch_assoc($res_bs7)) {
    switch ($row_bs7['ch_cl_Moo']) {
        case '1':
            $BS21 = $row_bs7['count'];
            break;
        case '2':
            $BS22 = $row_bs7['count'];
            break;
        case '3':
            $BS23 = $row_bs7['count'];
            break;
        case '4':
            $BS24 = $row_bs7['count'];
            break;
        case '5':
            $BS25 = $row_bs7['count'];
            break;
        case '6':
            $BS26 = $row_bs7['count'];
            break;
        case '7':
            $BS27 = $row_bs7['count'];
            break;
        case '8':
            $BS28 = $row_bs7['count'];
            break;
        case '9':
            $BS29 = $row_bs7['count'];
            break;
        case '10':
            $BS210 = $row_bs7['count'];
            break;
        case '11':
            $BS211 = $row_bs7['count'];
            break;
        case '12':
            $BS212 = $row_bs7['count'];
            break;
        case '13':
            $BS213 = $row_bs7['count'];
            break;
    }
}

$T2 = $BS21 + $BS22 + $BS23 + $BS24 + $BS25 + $BS26 + $BS27 + $BS28 + $BS29 + $BS210 + $BS211 + $BS212 + $BS213;

// ครอบครอง บ้านสวน
$sql_bs8 = "SELECT ch_cl_Moo, COUNT(DISTINCT ch_cl_idcard) as count FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='8' AND station='$station' AND ch_cl_Tumbon='640102' GROUP BY ch_cl_Moo";
$res_bs8 = mysqli_query($conn, $sql_bs8);

$BS31 = $BS32 = $BS33 = $BS34 = $BS35 = $BS36 = $BS37 = $BS38 = $BS39 = $BS310 = $BS311 = $BS312 = $BS313 = 0;
while ($row_bs8 = mysqli_fetch_assoc($res_bs8)) {
    switch ($row_bs8['ch_cl_Moo']) {
        case '1':
            $BS31 = $row_bs8['count'];
            break;
        case '2':
            $BS32 = $row_bs8['count'];
            break;
        case '3':
            $BS33 = $row_bs8['count'];
            break;
        case '4':
            $BS34 = $row_bs8['count'];
            break;
        case '5':
            $BS35 = $row_bs8['count'];
            break;
        case '6':
            $BS36 = $row_bs8['count'];
            break;
        case '7':
            $BS37 = $row_bs8['count'];
            break;
        case '8':
            $BS38 = $row_bs8['count'];
            break;
        case '9':
            $BS39 = $row_bs8['count'];
            break;
        case '10':
            $BS310 = $row_bs8['count'];
            break;
        case '11':
            $BS311 = $row_bs8['count'];
            break;
        case '12':
            $BS312 = $row_bs8['count'];
            break;
        case '13':
            $BS313 = $row_bs8['count'];
            break;
    }
}

$T3 = $BS31 + $BS32 + $BS33 + $BS34 + $BS35 + $BS36 + $BS37 + $BS38 + $BS39 + $BS310 + $BS311 + $BS312 + $BS313;

// เสพ บ้านสวน
$sql_bs9 = "SELECT ch_cl_Moo, COUNT(DISTINCT ch_cl_idcard) as count FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='9' AND station='$station' AND ch_cl_Tumbon='640102' GROUP BY ch_cl_Moo";
$res_bs9 = mysqli_query($conn, $sql_bs9);

$BS41 = $BS42 = $BS43 = $BS44 = $BS45 = $BS46 = $BS47 = $BS48 = $BS49 = $BS410 = $BS411 = $BS412 = $BS413 = 0;
while ($row_bs9 = mysqli_fetch_assoc($res_bs9)) {
    switch ($row_bs9['ch_cl_Moo']) {
        case '1':
            $BS41 = $row_bs9['count'];
            break;
        case '2':
            $BS42 = $row_bs9['count'];
            break;
        case '3':
            $BS43 = $row_bs9['count'];
            break;
        case '4':
            $BS44 = $row_bs9['count'];
            break;
        case '5':
            $BS45 = $row_bs9['count'];
            break;
        case '6':
            $BS46 = $row_bs9['count'];
            break;
        case '7':
            $BS47 = $row_bs9['count'];
            break;
        case '8':
            $BS48 = $row_bs9['count'];
            break;
        case '9':
            $BS49 = $row_bs9['count'];
            break;
        case '10':
            $BS410 = $row_bs9['count'];
            break;
        case '11':
            $BS411 = $row_bs9['count'];
            break;
        case '12':
            $BS412 = $row_bs9['count'];
            break;
        case '13':
            $BS413 = $row_bs9['count'];
            break;
    }
}

$T4 = $BS41 + $BS42 + $BS43 + $BS44 + $BS45 + $BS46 + $BS47 + $BS48 + $BS49 + $BS410 + $BS411 + $BS412 + $BS413;

// Chat GPT บ้านหลุม //
// ผลิต บ้านหลุม
$sql_bl6 = "SELECT ch_cl_Moo, COUNT(DISTINCT ch_cl_idcard) as count FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='6' AND station='$station' AND ch_cl_Tumbon='640107' GROUP BY ch_cl_Moo";
$res_bl6 = mysqli_query($conn, $sql_bl6);

$BL1 = $BL2 = $BL3 = $BL4 = $BL5 = $BL6 = $BL7 = $BL8 = $BL9 = 0;
while ($row_bl6 = mysqli_fetch_assoc($res_bl6)) {
    switch ($row_bl6['ch_cl_Moo']) {
        case '1':
            $BL1 = $row_bl6['count'];
            break;
        case '2':
            $BL2 = $row_bl6['count'];
            break;
        case '3':
            $BL3 = $row_bl6['count'];
            break;
        case '4':
            $BL4 = $row_bl6['count'];
            break;
        case '5':
            $BL5 = $row_bl6['count'];
            break;
        case '6':
            $BL6 = $row_bl6['count'];
            break;
        case '7':
            $BL7 = $row_bl6['count'];
            break;
        case '8':
            $BL8 = $row_bl6['count'];
            break;
        case '9':
            $BL9 = $row_bl6['count'];
            break;
    }
}

$T5 = $BL1 + $BL2 + $BL3 + $BL4 + $BL5 + $BL6 + $BL7 + $BL8 + $BL9;

// จำหน่าย บ้านหลุม
$sql_bl7 = "SELECT ch_cl_Moo, COUNT(DISTINCT ch_cl_idcard) as count FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='7' AND station='$station' AND ch_cl_Tumbon='640107' GROUP BY ch_cl_Moo";
$res_bl7 = mysqli_query($conn, $sql_bl7);

$BL21 = $BL22 = $BL23 = $BL24 = $BL25 = $BL26 = $BL27 = $BL28 = $BL29 = 0;
while ($row_bl7 = mysqli_fetch_assoc($res_bl7)) {
    switch ($row_bl7['ch_cl_Moo']) {
        case '1':
            $BL21 = $row_bl7['count'];
            break;
        case '2':
            $BL22 = $row_bl7['count'];
            break;
        case '3':
            $BL23 = $row_bl7['count'];
            break;
        case '4':
            $BL24 = $row_bl7['count'];
            break;
        case '5':
            $BL25 = $row_bl7['count'];
            break;
        case '6':
            $BL26 = $row_bl7['count'];
            break;
        case '7':
            $BL27 = $row_bl7['count'];
            break;
        case '8':
            $BL28 = $row_bl7['count'];
            break;
        case '9':
            $BL29 = $row_bl7['count'];
            break;
    }
}

$T6 = $BL21 + $BL22 + $BL23 + $BL24 + $BL25 + $BL26 + $BL27 + $BL28 + $BL29;

// ครอบครอง บ้านหลุม
$sql_bl8 = "SELECT ch_cl_Moo, COUNT(DISTINCT ch_cl_idcard) as count FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='8' AND station='$station' AND ch_cl_Tumbon='640107' GROUP BY ch_cl_Moo";
$res_bl8 = mysqli_query($conn, $sql_bl8);

$BL31 = $BL32 = $BL33 = $BL34 = $BL35 = $BL36 = $BL37 = $BL38 = $BL39 = 0;
while ($row_bl8 = mysqli_fetch_assoc($res_bl8)) {
    switch ($row_bl8['ch_cl_Moo']) {
        case '1':
            $BL31 = $row_bl8['count'];
            break;
        case '2':
            $BL32 = $row_bl8['count'];
            break;
        case '3':
            $BL33 = $row_bl8['count'];
            break;
        case '4':
            $BL34 = $row_bl8['count'];
            break;
        case '5':
            $BL35 = $row_bl8['count'];
            break;
        case '6':
            $BL36 = $row_bl8['count'];
            break;
        case '7':
            $BL37 = $row_bl8['count'];
            break;
        case '8':
            $BL38 = $row_bl8['count'];
            break;
        case '9':
            $BL39 = $row_bl8['count'];
            break;
    }
}

$T7 = $BL31 + $BL32 + $BL33 + $BL34 + $BL35 + $BL36 + $BL37 + $BL38 + $BL39;

// เสพ บ้านหลุม
$sql_bl9 = "SELECT ch_cl_Moo, COUNT(DISTINCT ch_cl_idcard) as count FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='9' AND station='$station' AND ch_cl_Tumbon='640107' GROUP BY ch_cl_Moo";
$res_bl9 = mysqli_query($conn, $sql_bl9);

$BL41 = $BL42 = $BL43 = $BL44 = $BL45 = $BL46 = $BL47 = $BL48 = $BL49 = 0;
while ($row_bl9 = mysqli_fetch_assoc($res_bl9)) {
    switch ($row_bl9['ch_cl_Moo']) {
        case '1':
            $BL41 = $row_bl9['count'];
            break;
        case '2':
            $BL42 = $row_bl9['count'];
            break;
        case '3':
            $BL43 = $row_bl9['count'];
            break;
        case '4':
            $BL44 = $row_bl9['count'];
            break;
        case '5':
            $BL45 = $row_bl9['count'];
            break;
        case '6':
            $BL46 = $row_bl9['count'];
            break;
        case '7':
            $BL47 = $row_bl9['count'];
            break;
        case '8':
            $BL48 = $row_bl9['count'];
            break;
        case '9':
            $BL49 = $row_bl9['count'];
            break;
    }
}

$T8 = $BL41 + $BL42 + $BL43 + $BL44 + $BL45 + $BL46 + $BL47 + $BL48 + $BL49;


// Chat GPT ตาลเตี้ย //
// ผลิต ตาลเตี้ย
$sql_tt6 = "SELECT ch_cl_Moo, COUNT(DISTINCT ch_cl_idcard) as count FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='6' AND station='$station' AND ch_cl_Tumbon='640108' GROUP BY ch_cl_Moo";
$res_tt6 = mysqli_query($conn, $sql_tt6);

$TT1 = $TT2 = $TT3 = $TT4 = 0;
while ($row_tt6 = mysqli_fetch_assoc($res_tt6)) {
    switch ($row_tt6['ch_cl_Moo']) {
        case '1':
            $TT1 = $row_tt6['count'];
            break;
        case '2':
            $TT2 = $row_tt6['count'];
            break;
        case '3':
            $TT3 = $row_tt6['count'];
            break;
        case '4':
            $TT4 = $row_tt6['count'];
            break;
    }
}

$T9 = $TT1 + $TT2 + $TT3 + $TT4;

// จำหน่าย ตาลเตี้ย
$sql_tt7 = "SELECT ch_cl_Moo, COUNT(DISTINCT ch_cl_idcard) as count FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='7' AND station='$station' AND ch_cl_Tumbon='640108' GROUP BY ch_cl_Moo";
$res_tt7 = mysqli_query($conn, $sql_tt7);

$TT21 = $TT22 = $TT23 = $TT24 = 0;
while ($row_tt7 = mysqli_fetch_assoc($res_tt7)) {
    switch ($row_tt7['ch_cl_Moo']) {
        case '1':
            $TT21 = $row_tt7['count'];
            break;
        case '2':
            $TT22 = $row_tt7['count'];
            break;
        case '3':
            $TT23 = $row_tt7['count'];
            break;
        case '4':
            $TT24 = $row_tt7['count'];
            break;
    }
}

$T10 = $TT21 + $TT22 + $TT23 + $TT24;

// ครอบครอง ตาลเตี้ย
$sql_tt8 = "SELECT ch_cl_Moo, COUNT(DISTINCT ch_cl_idcard) as count FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='8' AND station='$station' AND ch_cl_Tumbon='640108' GROUP BY ch_cl_Moo";
$res_tt8 = mysqli_query($conn, $sql_tt8);

$TT31 = $TT32 = $TT33 = $TT34 = 0;
while ($row_tt8 = mysqli_fetch_assoc($res_tt8)) {
    switch ($row_tt8['ch_cl_Moo']) {
        case '1':
            $TT31 = $row_tt8['count'];
            break;
        case '2':
            $TT32 = $row_tt8['count'];
            break;
        case '3':
            $TT33 = $row_tt8['count'];
            break;
        case '4':
            $TT34 = $row_tt8['count'];
            break;
    }
}

$T11 = $TT31 + $TT32 + $TT33 + $TT34;

// เสพ ตาลเตี้ย
$sql_tt9 = "SELECT ch_cl_Moo, COUNT(DISTINCT ch_cl_idcard) as count FROM wm_tb_crimes_history WHERE ch_cl_crimes_type2='9' AND station='$station' AND ch_cl_Tumbon='640108' GROUP BY ch_cl_Moo";
$res_tt9 = mysqli_query($conn, $sql_tt9);

//$tt_moo = $row_tt9['ch_cl_Moo'];
$TT41 = $TT42 = $TT43 = $TT44 = 0;
while ($row_tt9 = mysqli_fetch_assoc($res_tt9)) {
    switch ($row_tt9['ch_cl_Moo']) {
        case '1':
            $TT41 = $row_tt9['count'];
            break;
        case '2':
            $TT42 = $row_tt9['count'];
            break;
        case '3':
            $TT43 = $row_tt9['count'];
            break;
        case '4':
            $TT44 = $row_tt9['count'];
            break;
    }
}

$T12 = $TT41 + $TT42 + $TT43 + $TT44;


?>

<!doctype html>
<html>
<head>
<meta charset="utf-8">
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
        <base target="_top">
        <title>ระบบ WatchmanDB</title>
		<link rel="stylesheet" href="../bootstrap/css/bootstrap.rtl.min.css">
		<link rel="stylesheet" href="../bootstrap-3.4.1-dist/css/bootstrap.min.css">
  		<script src="../jQuery/jquery-3.5.1.min.js"></script>
 		<script src="../bootstrap/js/bootstrap.min.js"></script>
        <style>
            body {
              font-family: 'Courier New', Courier, monospace;
              font-size: 16px;
            }
        </style>
</head>

<body>

<div>
  <?php
    include('../users_info.php');
    ?>	
</div>

<div class="h3 text-center alert alert-danger mb-4 mt-4 " role="alert" >บัญชีข้อมูลยาเสพติดและผู้ป่วยจิตเวชจากการใช้ยาเสพติด ในเขต <?= $name_station ?> (แยกตามรายหมู่บ้าน)</div>
    <div style="flex: auto">
        &nbsp;<a href="/Bansuan/index.php?rnd=<?= rand(); ?>&page=information" class="btn btn-primary btn-lg mb-4" >ย้อนกลับ</a>&nbsp;
        <a href="Show_crimes_person_drug6.php" class="btn btn-danger btn-lg mb-4 rounded-pill"  >บัญชี ผลิต,นำเข้า</a>
    &nbsp;
        <a href="Show_crimes_person_drug7.php" class="btn btn-danger btn-lg mb-4 rounded-pill"  >บัญชี จำหน่าย,ครอบครองเพื่อจำหน่าย</a>
    &nbsp;
        <a href="Show_crimes_person_drug8.php" class="btn btn-danger btn-lg mb-4 rounded-pill"  >บัญชี ครอบครองเพื่อเสพ</a>
    &nbsp;
        <a href="Show_crimes_person_drug.php" class="btn btn-danger btn-lg mb-4 rounded-pill"  >บัญชี ผู้เสพ</a>
    &nbsp;
        <a href="../WatchmanData/Show_SMI-V.php" class="btn btn-warning btn-lg mb-4 rounded-pill"  >ผู้ป่วยจิตเวช</a>
    &nbsp;
        
    </div>
    
<div class="container" align="left" style="margin-left:auto">
<table width="99%" border="1" cellspacing="1" cellpadding="1" class="table-striped table-hover table-bordered">
  <tbody>
    <tr>
      <td width="35%" rowspan="2" bgcolor="#4DF913" style="text-align: center"><b style="color: black">หมู่บ้าน/ชุมชน</b></td>
      <td height="40" colspan="4" bgcolor="#4DF913" style="text-align: center" ><b style="color: black">ยาเสพติด</b></td>
      <td colspan="3" bgcolor="#4DF913" style="text-align: center"><b style="color: black">ผู้ป่วยจิตเวชจากยาเสพติด</b></td>
      <td colspan="3" bgcolor="#4DF913" style="text-align: center"><b style="color: black">ผู้ป่วยจิตเวช ไม่ใช่จากการใช้ยาเสพติด</b></td>
      <td width="5%" rowspan="2" bgcolor="#4DF913" style="text-align: center"><b style="color: black">หมายเหตุ</b></td>
    </tr>
    <tr>
      <td width="6%" height="40" bgcolor="#4DF913" style="text-align: center" ><b style="color: black">ผลิต, นำเข้า</b></td>
      <td width="6%" height="40" bgcolor="#4DF913" style="text-align: center" ><b style="color: black">จำหน่าย, คค.เพื่อจำหน่าย</b></td>
      <td width="6%" height="40" bgcolor="#4DF913" style="text-align: center" ><b style="color: black">ครอบครองเพื่อเสพ</b></td>
      <td width="6%" height="40" bgcolor="#4DF913" style="text-align: center" ><b style="color: black">เสพ</b></td>
      <td width="6%" height="40" bgcolor="#069B2A" style="text-align: center; color: aliceblue">เขียว</td>
      <td width="6%" height="40" bgcolor="#FCF80D" style="text-align: center">เหลือง</td>
      <td width="6%" height="40" bgcolor="#FC0400" style="text-align: center; font-size: 18px; color: aliceblue">แดง</td>
      <td width="6%" height="40" bgcolor="#069B2A" style="text-align: center; color: aliceblue">เขียว</td>
      <td width="6%" height="40" bgcolor="#FCF80D" style="text-align: center">เหลือง</td>
      <td width="6%" height="40" bgcolor="#FC0400" style="text-align: center; font-size: 18px; color: aliceblue">แดง</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left" bgcolor="#FDA251">&nbsp;ตำบลบ้านสวน</td>
      <td style="text-align: center" bgcolor="#FDA251">
          <b style="text-align: center; color:black">
          <?php echo (($T1 > 0) ? $T1  : "") ?>
          </b>
      </td>
        
      <td style="text-align: center" bgcolor="#FDA251">
          <b style="text-align: center; color:black">
          <?php echo (($T2 > 0) ? $T2  : "") ?>
          </b>
      </td>
      <td style="text-align: center" bgcolor="#FDA251">
          <b style="text-align: center; color:black">
          <?php echo (($T3 > 0) ? $T3  : "") ?>
        </b></td>
      <td style="text-align: center" bgcolor="#FDA251">
          <b style="text-align: center; color:black">
          <?php echo (($T4 > 0) ? $T4  : "") ?>
          </b>
      </td>
      <td style="text-align: center" bgcolor="#FDA251">
          <b style="font-size: 18px; color:black">
          <?php echo (($row_smiv['BSG1'] > 0) ? $row_smiv['BSG1']  : "") ?>
          </b>
      </td>
      <td style="text-align: center; border:medium" bgcolor="#FDA251"><b style="text-align: center; color:black">
          <?php echo (($row_smiv['BSY1'] > 0) ? $row_smiv['BSY1']  : "") ?></b>
        </td>
      <td style="text-align: center" bgcolor="#FDA251">
          <b  style="font-size: 18px; color:black">
          <?php echo (($row_smiv['BSR1'] > 0) ? $row_smiv['BSR1']  : "") ?>
          </b>
        </td>
      <td style="text-align: center" bgcolor="#FDA251">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FDA251">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FDA251">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FDA251">&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;1. หมู่ 1 บ้านป่า</td> 
      <td style="text-align: center">
          <b>
          <?php echo (($BS1 > 0) ? $BS1  : "") ?>
          </b>
        </td>
      <td style="text-align: center">
          <b>
          <?php echo (($BS21 > 0) ? $BS21  : "") ?>
          </b>
        </td>
      <td style="text-align: center">
          <b>
          <?php echo (($BS31 > 0) ? $BS31  : "") ?>
          </b>
        </td>
      <td style="text-align: center">
          <b>
          <?php echo (($BS41 > 0) ? $BS41  : "") ?>
          </b>
        </td>
      <td style="text-align: center" bgcolor="#069B2A">
          <b style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BSG11'] > 0) ? $row_smiv['BSG11']  : "") ?>
          </b>
        </td>
      <td style="text-align: center"  bgcolor="#FCF80D"><b style="text-align: center">
          <?php echo (($row_smiv['BSY11'] > 0) ? $row_smiv['BSY11']  : "") ?>
          </b>
        </td>
      <td style="text-align: center" bgcolor="#FC0400">
          <b  style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BSR11'] > 0) ? $row_smiv['BSR11']  : "") ?>
          </b>
        </td>
      <td style="text-align: center" bgcolor="#069B2A">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FCF80D">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FC0400">&nbsp;</td>
      <td style="text-align: center">&nbsp;</td>
    </tr>
    <tr>

      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;2. หมู่ 2 บ้านสวนใต้</td>
      <td style="text-align: center">
          <b>
          <?php echo (($BS2 > 0) ? $BS2  : "") ?>
          </b>
        </td>
      <td style="text-align: center">
          <b>
          <?php echo (($BS22 > 0) ? $BS22  : "") ?>
          </b>
        </td>
      <td style="text-align: center">
          <b>
          <?php echo (($BS32 > 0) ? $BS32  : "") ?>
          </b>
        </td>
      <td style="text-align: center">
          <b>
          <?php echo (($BS42 > 0) ? $BS42  : "") ?>
          </b>
        </td>
      <td style="text-align: center" bgcolor="#069B2A">
          <b style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BSG12'] > 0) ? $row_smiv['BSG12']  : "") ?>
          </b>
        </td>
      <td style="text-align: center" bgcolor="#FCF80D"><b style="text-align: center">
          <?php echo (($row_smiv['BSY12'] > 0) ? $row_smiv['BSY12']  : "") ?>
          </b>
        </td>
      <td style="text-align: center" bgcolor="#FC0400">
          <b  style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BSR12'] > 0) ? $row_smiv['BSR12']  : "") ?>
          </b>
        </td>
      <td style="text-align: center" bgcolor="#069B2A">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FCF80D">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FC0400">&nbsp;</td>
      <td style="text-align: center">&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;3. หมู่ 3 ตลาดบ้านสวน-วัดคุ้งยางใหญ่</td>
      <td style="text-align: center">
          <b>
          <?php echo (($BS3 > 0) ? $BS3  : "") ?>
          </b>
        </td>
      <td style="text-align: center">
          <b>
          <?php echo (($BS23 > 0) ? $BS23  : "") ?>
          </b>
        </td>
      <td style="text-align: center">
          <b>
          <?php echo (($BS33 > 0) ? $BS33  : "") ?>
          </b>
        </td>
      <td style="text-align: center">
          <b>
          <?php echo (($BS43 > 0) ? $BS43  : "") ?>
          </b>
        </td>
      <td style="text-align: center" bgcolor="#069B2A">
          <b style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BSG13'] > 0) ? $row_smiv['BSG13']  : "") ?>
          </b>
        </td>
      <td style="text-align: center" bgcolor="#FCF80D"><b style="text-align: center">
          <?php echo (($row_smiv['BSY13'] > 0) ? $row_smiv['BSY13']  : "") ?>
          </b>
        </td>
      <td style="text-align: center" bgcolor="#FC0400">
          <b  style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BSR13'] > 0) ? $row_smiv['BSR13']  : "") ?>
          </b>
        </td>
      <td style="text-align: center" bgcolor="#069B2A">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FCF80D">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FC0400">&nbsp;</td>
      <td style="text-align: center">&nbsp;</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;4. หมู่ 4 บ้านไร่-วัดบึง-วัดฤทธิ์-บ้านบอน</td>
      <td style="text-align: center">
          <b>
          <?php echo (($BS4 > 0) ? $BS4  : "") ?>
          </b>
        </td>
      <td style="text-align: center">
          <b>
          <?php echo (($BS24 > 0) ? $BS24  : "") ?>
          </b>
        </td>
      <td style="text-align: center">
          <b>
          <?php echo (($BS34 > 0) ? $BS34  : "") ?>
          </b>
        </td>
      <td style="text-align: center">
          <b>
          <?php echo (($BS44 > 0) ? $BS44  : "") ?>
          </b>
        </td>
      <td style="text-align: center" bgcolor="#069B2A">
          <b style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BSG14'] > 0) ? $row_smiv['BSG14']  : "") ?>
          </b>
        </td>
      <td style="text-align: center" bgcolor="#FCF80D"><b style="text-align: center">
          <?php echo (($row_smiv['BSY14'] > 0) ? $row_smiv['BSY14']  : "") ?>
          </b>
        </td>
      <td style="text-align: center" bgcolor="#FC0400">
          <b  style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BSR14'] > 0) ? $row_smiv['BSR14']  : "") ?>
          </b>
        </td>
      <td style="text-align: center" bgcolor="#069B2A">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FCF80D">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FC0400">&nbsp;</td>
      <td style="text-align: center">&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;5. หมู่ 5 บ้านคลองด่าน</td>
      <td style="text-align: center">
          <b>
          <?php echo (($BS5 > 0) ? $BS5  : "") ?>
          </b>
        </td>
      <td style="text-align: center">
          <b>
          <?php echo (($BS25 > 0) ? $BS25  : "") ?>
          </b>
        </td>
      <td style="text-align: center">
          <b>
          <?php echo (($BS35 > 0) ? $BS35  : "") ?>
          </b>
        </td>
      <td style="text-align: center">
          <b>
          <?php echo (($BS45 > 0) ? $BS45  : "") ?>
          </b>
        </td>
      <td style="text-align: center" bgcolor="#069B2A">
          <b style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BSG15'] > 0) ? $row_smiv['BSG15']  : "") ?>
          </b>
        </td>
      <td style="text-align: center" bgcolor="#FCF80D"><b style="text-align: center">
          <?php echo (($row_smiv['BSY15'] > 0) ? $row_smiv['BSY15']  : "") ?>
          </b>
        </td>
      <td style="text-align: center" bgcolor="#FC0400">
          <b  style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BSR15'] > 0) ? $row_smiv['BSR15']  : "") ?>
          </b>
        </td>
      <td style="text-align: center" bgcolor="#069B2A">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FCF80D">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FC0400">&nbsp;</td>
      <td style="text-align: center">&nbsp;</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;6. หมู่ 6 คลองตะเคียน</td>
      <td style="text-align: center">
          <b>
          <?php echo (($BS6 > 0) ? $BS6  : "") ?>
          </b>
        </td>
      <td style="text-align: center">
          <b>
          <?php echo (($BS26 > 0) ? $BS26  : "") ?>
          </b>
        </td>
      <td style="text-align: center">
          <b>
          <?php echo (($BS36 > 0) ? $BS36  : "") ?>
          </b>
        </td>
      <td style="text-align: center">
          <b>
          <?php echo (($BS46 > 0) ? $BS46  : "") ?>
          </b>
        </td>
      <td style="text-align: center" bgcolor="#069B2A">
          <b style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BSG16'] > 0) ? $row_smiv['BSG16']  : "") ?>
          </b>
        </td>
      <td style="text-align: center" bgcolor="#FCF80D"><b style="text-align: center">
          <?php echo (($row_smiv['BSY16'] > 0) ? $row_smiv['BSY16']  : "") ?>
          </b>
        </td>
      <td style="text-align: center" bgcolor="#FC0400">
          <b  style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BSR16'] > 0) ? $row_smiv['BSR16']  : "") ?>
          </b>
        </td>
      <td style="text-align: center" bgcolor="#069B2A">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FCF80D">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FC0400">&nbsp;</td>
      <td style="text-align: center">&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;7. หมู่ 7 หนองโครง</td>
      <td style="text-align: center">
          <b>
          <?php echo (($BS7 > 0) ? $BS7  : "") ?>
          </b>
        </td>
      <td style="text-align: center">
          <b>
          <?php echo (($BS27 > 0) ? $BS27  : "") ?>
          </b>
        </td>
      <td style="text-align: center">
          <b>
          <?php echo (($BS37 > 0) ? $BS37  : "") ?>
          </b>
        </td>
      <td style="text-align: center">
          <b>
          <?php echo (($BS47 > 0) ? $BS47  : "") ?>
          </b>
        </td>
      <td style="text-align: center" bgcolor="#069B2A">
          <b style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BSG17'] > 0) ? $row_smiv['BSG17']  : "") ?>
          </b>
        </td>
      <td style="text-align: center" bgcolor="#FCF80D"><b style="text-align: center">
          <?php echo (($row_smiv['BSY17'] > 0) ? $row_smiv['BSY17']  : "") ?>
          </b>
        </td>
      <td style="text-align: center" bgcolor="#FC0400">
          <b  style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BSR17'] > 0) ? $row_smiv['BSR17']  : "") ?>
          </b>
        </td>
      <td style="text-align: center" bgcolor="#069B2A">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FCF80D">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FC0400">&nbsp;</td>
      <td style="text-align: center">&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;8. หมู่ 8 บ้านไผ่ขวาง</td>
      <td style="text-align: center">
          <b>
          <?php echo (($BS8 > 0) ? $BS8  : "") ?>
          </b>
        </td>
      <td style="text-align: center">
          <b>
          <?php echo (($BS28 > 0) ? $BS28  : "") ?>
          </b>
        </td>
      <td style="text-align: center">
          <b>
          <?php echo (($BS38 > 0) ? $BS38  : "") ?>
          </b>
        </td>
      <td style="text-align: center">
          <b>
          <?php echo (($BS48 > 0) ? $BS48  : "") ?>
          </b>
        </td>
      <td style="text-align: center" bgcolor="#069B2A">
          <b style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BSG18'] > 0) ? $row_smiv['BSG18']  : "") ?>
          </b>
        </td>
      <td style="text-align: center" bgcolor="#FCF80D"><b style="text-align: center">
          <?php echo (($row_smiv['BSY18'] > 0) ? $row_smiv['BSY18']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#FC0400">
          <b  style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BSR18'] > 0) ? $row_smiv['BSR18']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#069B2A">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FCF80D">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FC0400">&nbsp;</td>
      <td style="text-align: center">&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;9. หมู่ 9 บ้านสวนเหนือ/เหมืองใหญ่</td>
      <td style="text-align: center"><b>
          <?php echo (($BS9 > 0) ? $BS9  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($BS29 > 0) ? $BS29  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($BS39 > 0) ? $BS39  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($BS49 > 0) ? $BS49  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#069B2A">
          <b style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BSG19'] > 0) ? $row_smiv['BSG19']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#FCF80D"><b style="text-align: center">
          <?php echo (($row_smiv['BSY19'] > 0) ? $row_smiv['BSY19']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#FC0400">
          <b  style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BSR19'] > 0) ? $row_smiv['BSR19']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#069B2A">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FCF80D">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FC0400">&nbsp;</td>
      <td style="text-align: center">&nbsp;</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;10. หมู่ 10 คลองปลายนา</td>
      <td style="text-align: center"><b>
          <?php echo (($BS10 > 0) ? $BS10  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($BS210 > 0) ? $BS210  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($BS310 > 0) ? $BS310  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($BS410 > 0) ? $BS410  : "") ?>
          </b></td>
      <td style="text-align: center"bgcolor="#069B2A">
          <b style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BSG110'] > 0) ? $row_smiv['BSG110']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#FCF80D"><b style="text-align: center">
          <?php echo (($row_smiv['BSY110'] > 0) ? $row_smiv['BSY110']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#FC0400">
          <b  style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BSR110'] > 0) ? $row_smiv['BSR110']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#069B2A">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FCF80D">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FC0400">&nbsp;</td>
      <td style="text-align: center">&nbsp;</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;11. หมู่ 11 หนองทอง/วัดหนองทอง</td>
      <td style="text-align: center"><b>
          <?php echo (($BS11 > 0) ? $BS11  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($BS211 > 0) ? $BS211  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($BS311 > 0) ? $BS311  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($BS411 > 0) ? $BS411  : "") ?>
          </b></td>
      <td style="text-align: center"bgcolor="#069B2A">
          <b style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BSG111'] > 0) ? $row_smiv['BSG111']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#FCF80D"><b style="text-align: center">
          <?php echo (($row_smiv['BSY111'] > 0) ? $row_smiv['BSY111']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#FC0400">
          <b  style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BSR111'] > 0) ? $row_smiv['BSR111']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#069B2A">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FCF80D">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FC0400">&nbsp;</td>
      <td style="text-align: center">&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;12. หมู่ 12 บ้านวัดจันทร์</td>
      <td style="text-align: center"><b>
          <?php echo (($BS12 > 0) ? $BS12  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($BS212 > 0) ? $BS212  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($BS312 > 0) ? $BS312  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($BS412 > 0) ? $BS412  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#069B2A">
          <b style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BSG112'] > 0) ? $row_smiv['BSG112']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#FCF80D"><b style="text-align: center">
          <?php echo (($row_smiv['BSY112'] > 0) ? $row_smiv['BSY112']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#FC0400">
          <b  style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BSR112'] > 0) ? $row_smiv['BSR112']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#069B2A">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FCF80D">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FC0400">&nbsp;</td>
      <td style="text-align: center">&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;13. หมู่ บ้านโปร่ง</td>
      <td style="text-align: center"><b>
          <?php echo (($BS13 > 0) ? $BS13  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($BS213 > 0) ? $BS213  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($BS313 > 0) ? $BS313  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($BS413 > 0) ? $BS413  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#069B2A">
          <b style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BSG113'] > 0) ? $row_smiv['BSG113']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#FCF80D"><b style="text-align: center">
          <?php echo (($row_smiv['BSY113'] > 0) ? $row_smiv['BSY113']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#FC0400">
          <b  style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BSR113'] > 0) ? $row_smiv['BSR113']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#069B2A">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FCF80D">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FC0400">&nbsp;</td>
      <td style="text-align: center">&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left" bgcolor="#0DFCF8">&nbsp;ตำบลบ้านหลุม</td>
      <td style="text-align: center" bgcolor="#0DFCF8"><b style="text-align: center; color:black">
          <?php echo (($T5 > 0) ? $T5  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#0DFCF8"><b style="text-align: center; color:black">
          <?php echo (($T6 > 0) ? $T6  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#0DFCF8"><b style="text-align: center; color:black">
          <?php echo (($T7 > 0) ? $T7  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#0DFCF8"><b style="text-align: center; color:black">
          <?php echo (($T8 > 0) ? $T8  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#0DFCF8">
          <b style="font-size: 18px; color:black">
          <?php echo (($row_smiv['BLG2'] > 0) ? $row_smiv['BLG2']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#0DFCF8"><b style="text-align: center; color:black">
          <?php echo (($row_smiv['BLY2'] > 0) ? $row_smiv['BLY2']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#0DFCF8">
          <b  style="font-size: 18px; color:black">
          <?php echo (($row_smiv['BLR2'] > 0) ? $row_smiv['BLR2']  : "") ?>
          </b></td>
      <td bgcolor="#0DFCF8">&nbsp;</td>
      <td bgcolor="#0DFCF8">&nbsp;</td>
      <td bgcolor="#0DFCF8">&nbsp;</td>
      <td bgcolor="#0DFCF8">&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;1. หมู่ 1 บ้านหลุม</td>
      <td style="text-align: center"><b>
          <?php echo (($BL1 > 0) ? $BL1  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($BL21 > 0) ? $BL21  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($BL31 > 0) ? $BL31  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($BL41 > 0) ? $BL41  : "") ?>
          </b></td>
      <td style="text-align: center"  bgcolor="#069B2A">
          <b style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BLG21'] > 0) ? $row_smiv['BLG21']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#FCF80D"><b style="text-align: center">
          <?php echo (($row_smiv['BLY21'] > 0) ? $row_smiv['BLY21']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#FC0400">
          <b  style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BLR21'] > 0) ? $row_smiv['BLY21']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#069B2A">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FCF80D">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FC0400">&nbsp;</td>
      <td style="text-align: center">&nbsp;</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;2. หมู่ 2 บ้านหลุม</td>
      <td style="text-align: center"><b>
          <?php echo (($BL2 > 0) ? $BL2  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($BL22 > 0) ? $BL22  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($BL32 > 0) ? $BL32  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($BL42 > 0) ? $BL42  : "") ?>
          </b></td>
      <td style="text-align: center"  bgcolor="#069B2A">
          <b style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BLG22'] > 0) ? $row_smiv['BLG22']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#FCF80D"><b style="text-align: center">
          <?php echo (($row_smiv['BLY22'] > 0) ? $row_smiv['BLY22']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#FC0400">
          <b  style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BLR22'] > 0) ? $row_smiv['BLR22']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#069B2A">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FCF80D">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FC0400">&nbsp;</td>
      <td style="text-align: center">&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;3. หมู่ 3 วังครก-วังแดง</td>
      <td style="text-align: center"><b>
          <?php echo (($BL3 > 0) ? $BL3  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($BL23 > 0) ? $BL23  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($BL33 > 0) ? $BL33  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($BL43 > 0) ? $BL43  : "") ?>
          </b></td>
      <td style="text-align: center"  bgcolor="#069B2A">
          <b style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BLG23'] > 0) ? $row_smiv['BLG23']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#FCF80D"><b style="text-align: center">
          <?php echo (($row_smiv['BLY23'] > 0) ? $row_smiv['BLY23']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#FC0400">
          <b  style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BLR23'] > 0) ? $row_smiv['BLR23']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#069B2A">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FCF80D">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FC0400">&nbsp;</td>
      <td style="text-align: center">&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;4. หมู่ 4 กระชงค์</td>
      <td style="text-align: center"><b>
          <?php echo (($BL4 > 0) ? $BL4  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($BL24 > 0) ? $BL24  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($BL34 > 0) ? $BL34  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($BL44 > 0) ? $BL44  : "") ?>
          </b></td>
      <td style="text-align: center"  bgcolor="#069B2A">
          <b style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BLG24'] > 0) ? $row_smiv['BLG24']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#FCF80D"><b style="text-align: center">
          <?php echo (($row_smiv['BLY24'] > 0) ? $row_smiv['BLY24']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#FC0400">
          <b  style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BLR24'] > 0) ? $row_smiv['BLR24']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#069B2A">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FCF80D">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FC0400">&nbsp;</td>
      <td style="text-align: center">&nbsp;</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;5. หมู่ 5 คลองระหัน</td>
      <td style="text-align: center"><b>
          <?php echo (($BL5 > 0) ? $BL5  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($BL25 > 0) ? $BL25  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($BL35 > 0) ? $BL35  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($BL45 > 0) ? $BL45  : "") ?>
          </b></td>
      <td style="text-align: center"  bgcolor="#069B2A">
          <b style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BLG25'] > 0) ? $row_smiv['BLG25']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#FCF80D"><b style="text-align: center">
          <?php echo (($row_smiv['BLY25'] > 0) ? $row_smiv['BLY25']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#FC0400">
          <b  style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BLR25'] > 0) ? $row_smiv['BLR25']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#069B2A">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FCF80D">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FC0400">&nbsp;</td>
      <td style="text-align: center">&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;6. หมู่ 6 คลองกง</td>
      <td style="text-align: center"><b>
          <?php echo (($BL6 > 0) ? $BL6  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($BL26 > 0) ? $BL26  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($BL36 > 0) ? $BL36  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($BL46 > 0) ? $BL46  : "") ?>
          </b></td>
      <td style="text-align: center"  bgcolor="#069B2A">
          <b style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BLG26'] > 0) ? $row_smiv['BLG26']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#FCF80D"><b style="text-align: center">
          <?php echo (($row_smiv['BLY26'] > 0) ? $row_smiv['BLY26']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#FC0400">
          <b  style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BLR26'] > 0) ? $row_smiv['BLR26']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#069B2A">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FCF80D">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FC0400">&nbsp;</td>
      <td style="text-align: center">&nbsp;</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;7. หมู่ 7 หางคลอง</td>
      <td style="text-align: center"><b>
          <?php echo (($BL7 > 0) ? $BL7  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($BL27 > 0) ? $BL27  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($BL37 > 0) ? $BL37  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($BL47 > 0) ? $BL47  : "") ?>
          </b></td>
      <td style="text-align: center"  bgcolor="#069B2A">
          <b style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BLG27'] > 0) ? $row_smiv['BLG27']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#FCF80D"><b style="text-align: center">
          <?php echo (($row_smiv['BLY27'] > 0) ? $row_smiv['BLY27']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#FC0400">
          <b  style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BLR27'] > 0) ? $row_smiv['BLR27']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#069B2A">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FCF80D">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FC0400">&nbsp;</td>
      <td style="text-align: center">&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;8. หมู่ 8 เหนือ</td>
      <td style="text-align: center"><b>
          <?php echo (($BL8 > 0) ? $BL8  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($BL28 > 0) ? $BL28  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($BL38 > 0) ? $BL38  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($BL48 > 0) ? $BL48  : "") ?>
          </b></td>
      <td style="text-align: center"  bgcolor="#069B2A">
          <b style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BLG28'] > 0) ? $row_smiv['BLG28']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#FCF80D"><b style="text-align: center">
          <?php echo (($row_smiv['BLY28'] > 0) ? $row_smiv['BLY28']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#FC0400">
          <b  style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BLR28'] > 0) ? $row_smiv['BLR28']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#069B2A">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FCF80D">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FC0400">&nbsp;</td>
      <td style="text-align: center">&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;9. หมู่ 9 เนินยาง</td>
      <td style="text-align: center"><b>
          <?php echo (($BL9 > 0) ? $BL9  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($BL29 > 0) ? $BL29  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($BL39 > 0) ? $BL39  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($BL49 > 0) ? $BL49  : "") ?>
          </b></td>
      <td style="text-align: center"  bgcolor="#069B2A">
          <b style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BLG29'] > 0) ? $row_smiv['BLG29']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#FCF80D"><b style="text-align: center">
          <?php echo (($row_smiv['BLY29'] > 0) ? $row_smiv['BLY29']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#FC0400">
          <b  style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['BLR29'] > 0) ? $row_smiv['BLR29']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#069B2A">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FCF80D">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FC0400">&nbsp;</td>
      <td style="text-align: center">&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left" bgcolor="#EB6BF7">&nbsp;ตำบลตาลเตี้ย</td>
      <td style="font-size: 18px; text-align: center" bgcolor="#EB6BF7"><b style="text-align: center; color:black">
          <?php echo (($T9 > 0) ? $T9  : "") ?>
          </b></td>
      <td style="font-size: 18px ;text-align: center" bgcolor="#EB6BF7"><b style="text-align: center; color:black">
          <?php echo (($T10 > 0) ? $T10  : "") ?>
          </b></td>
      <td style="font-size: 18px ;text-align: center" bgcolor="#EB6BF7"><b style="text-align: center; color:black">
          <?php echo (($T11 > 0) ? $T11  : "") ?>
          </b></td>
      <td height="40" style="font-size: 18px ;text-align: center" bgcolor="#EB6BF7"><b style="text-align: center; color:black">
          <?php echo (($T12 > 0) ? $T12  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#EB6BF7">
          <b style="font-size: 18px; color:black">
          <?php echo (($row_smiv['TTG3'] > 0) ? $row_smiv['TTG3']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#EB6BF7"><b style="text-align: center; color:black">
          <?php echo (($row_smiv['TY3'] > 0) ? $row_smiv['TY3']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#EB6BF7">
          <b  style="font-size: 18px; color:black">
          <?php echo (($row_smiv['TR3'] > 0) ? $row_smiv['TR3']  : "") ?>
          </b></td>
      <td style="text-align: center"  bgcolor="#EB6BF7">&nbsp;</td>
      <td style="text-align: center"  bgcolor="#EB6BF7">&nbsp;</td>
      <td style="text-align: center"  bgcolor="#EB6BF7">&nbsp;</td>
      <td style="text-align: center"  bgcolor="#EB6BF7">&nbsp;</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;1. หมู่ 1 ตาลเตี้ย</td>
      <td style="text-align: center"><b>
          <?php echo (($TT1 > 0) ? $TT1  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($TT21 > 0) ? $TT21  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($TT31 > 0) ? $TT31  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($TT41 > 0) ? $TT41  : "") ?>
          </b></td>
      <td style="text-align: center"  bgcolor="#069B2A">
          <b style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['TTG31'] > 0) ? $row_smiv['TTG31']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#FCF80D"><b style="text-align: center">
          <?php echo (($row_smiv['TTY31'] > 0) ? $row_smiv['TTY31']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#FC0400">
          <b  style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['TTR31'] > 0) ? $row_smiv['TTR31']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#069B2A">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FCF80D">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FC0400">&nbsp;</td>
      <td style="text-align: center">&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;2. หมู่ 2 ตาลเตี้ย</td>
      <td style="text-align: center"><b>
          <?php echo (($TT2 > 0) ? $TT2  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($TT22 > 0) ? $TT22  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($TT32 > 0) ? $TT32  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($TT42 > 0) ? $TT42  : "") ?>
          </b></td>
      <td style="text-align: center"  bgcolor="#069B2A">
          <b style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['TTG32'] > 0) ? $row_smiv['TTG32']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#FCF80D"><b style="text-align: center">
          <?php echo (($row_smiv['TTY32'] > 0) ? $row_smiv['TTY32']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#FC0400">
          <b  style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['TTR32'] > 0) ? $row_smiv['TTR32']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#069B2A">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FCF80D">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FC0400">&nbsp;</td>
      <td style="text-align: center">&nbsp;</td>
    </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;3. หมู่ 3 ยางเอน</td>
      <td style="text-align: center"><b>
          <?php echo (($TT3 > 0) ? $TT3  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($TT23 > 0) ? $TT23  : "") ?>
          </b></td>
      <td style="text-align: center"><b>
          <?php echo (($TT33 > 0) ? $TT33  : "") ?>
          </b></td>
      <td height="40" style="text-align: center"><b>
          <?php echo (($TT43 > 0) ? $TT43  : "") ?>
          </b></td>
      <td style="text-align: center"  bgcolor="#069B2A">
          <b style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['TTG33'] > 0) ? $row_smiv['TTG33']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#FCF80D"><b style="text-align: center">
          <?php echo (($row_smiv['TTY33'] > 0) ? $row_smiv['TTY33']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#FC0400">
          <b  style="font-size: 18px; color: aliceblue">
          <?php echo (($row_smiv['TTR33'] > 0) ? $row_smiv['TTR33']  : "") ?>
          </b></td>
      <td style="text-align: center" bgcolor="#069B2A">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FCF80D">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FC0400">&nbsp;</td>
      <td style="text-align: center">&nbsp;</td>
      </tr>
    <tr>
      <td height="40" style="font-size: 18px; text-align: left">&nbsp;&nbsp;&nbsp;4. หมู่ 4 คลองโบสถ์</td>
      <td style="text-align: center"><b><?php echo (($TT4 > 0) ? $TT4  : "") ?> </b></td>
      <td style="text-align: center"><b><?php echo (($TT24 > 0) ? $TT24  : "") ?> </b></td>
      <td style="text-align: center"><b><?php echo (($TT34 > 0) ? $TT34  : "") ?> </b></td>
      <td style="text-align: center"><b><?php echo (($TT44 > 0) ? $TT44  : "") ?> </b></td>
      <td style="text-align: center" bgcolor="#069B2A">
          <b style="font-size: 18px; color: aliceblue"><?php echo (($row_smiv['TTG34'] > 0) ? $row_smiv['TTG34']  : "") ?> </b></td>
      <td style="text-align: center" bgcolor="#FCF80D"><b style="text-align: center"><?php echo (($row_smiv['TTY34'] > 0) ? $row_smiv['TTY34']  : "") ?> </b></td>
      <td style="text-align: center" bgcolor="#FC0400">
          <b style="font-size: 18px; color: aliceblue"><?php echo (($row_smiv['TTR34'] > 0) ? $row_smiv['TTR34']  : "") ?> </b></td>
      <td style="text-align: center" bgcolor="#069B2A">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FCF80D">&nbsp;</td>
      <td style="text-align: center" bgcolor="#FC0400">&nbsp;</td>
      <td style="text-align: center">&nbsp;</td>
    </tr>
    <tr>
      <td height="40" bgcolor="#4DF913"><b style="font-size: 22px; color: black; text-align: center" >&nbsp;&nbsp;ยอดรวมทั้งหมด</b></td>
      <td style="text-align: center" bgcolor="#4DF913"><b style="text-align: center; color:black"><?php echo (($Total6 > 0) ? $Total6  : "") ?></b></td>
      <td style="text-align: center" bgcolor="#4DF913"><b style="text-align: center; color:black"><?php echo (($Total7 > 0) ? $Total7  : "") ?></b></td>
      <td style="text-align: center" bgcolor="#4DF913"><b style="text-align: center; color:black"><?php echo (($Total8 > 0) ? $Total8  : "") ?></b></td>
      <td style="text-align: center" bgcolor="#4DF913"><b style="text-align: center; color:black"><?php echo (($Total9 > 0) ? $Total9  : "") ?></b></td>
      <td style="text-align: center; color: aliceblue" bgcolor="#4DF913"><b style="font-size: 18px; color:black"><?php echo (($row_smiv['TotalG'] > 0) ? $row_smiv['TotalG']  : "") ?></b></td>
      <td style="text-align: center" bgcolor="#4DF913"><b style="text-align: center; color:black"><?php echo (($row_smiv['TotalY'] > 0) ? $row_smiv['TotalY']  : "") ?></b></td>
      <td style="text-align: center" bgcolor="#4DF913">
          <b  style="font-size: 18px; color:black"><?php echo (($row_smiv['TotalR'] > 0) ? $row_smiv['TotalR']  : "") ?></b></td>
      <td style="text-align: center" bgcolor="#4DF913">&nbsp;</td>
      <td style="text-align: center" bgcolor="#4DF913">&nbsp;</td>
      <td style="text-align: center" bgcolor="#4DF913">&nbsp;</td>
      <td style="text-align: center" bgcolor="#4DF913">&nbsp;</td>
    </tr>
    </tbody>
</table>
    <br>
    <div style="flex: auto" align="right">
        &nbsp;&nbsp;&nbsp;<a href="/Bansuan/index.php?rnd=<?= rand(); ?>&page=information" class="btn btn-primary btn-lg mb-4" >กลับ</a>&nbsp;
    </div>
    <hr>
</div>
    
</body>
</html>